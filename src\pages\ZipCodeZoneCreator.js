import React, { useState, useCallback } from 'react';
import { 
  collection, 
  addDoc, 
  getDocs,
  query,
  where,
  serverTimestamp,
  getFirestore 
} from 'firebase/firestore';

function ZipCodeZoneCreator({ 
  teamId, 
  currentUser, 
  onClose,
  onZoneCreated 
}) {
  const [zipCode, setZipCode] = useState('');
  const [zoneName, setZoneName] = useState('');
  const [zoneColor, setZoneColor] = useState('#3b82f6'); // Default blue color
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  
  const db = getFirestore();
  
  // Color palette options for zone creation
  const colorPalette = [
    '#3b82f6', // Blue
    '#8b5cf6', // Purple
    '#ef4444', // Red
    '#10b981', // Green
    '#f59e0b', // Amber
    '#ec4899', // Pink
    '#6366f1', // Indigo
    '#14b8a6', // Teal
  ];
  
  // Function to check if a zip code zone already exists
  const checkZoneExists = async (zipCode) => {
    const zonesRef = collection(db, 'teams', teamId, 'zones');
    const q = query(zonesRef, where("zipCode", "==", zipCode));
    const snapshot = await getDocs(q);
    return !snapshot.empty;
  };
  
  // Create a zone using the zip code
  const createZoneFromZipCode = useCallback(async () => {
    if (!zipCode || zipCode.length !== 5 || !/^\d+$/.test(zipCode)) {
      setError('Please enter a valid 5-digit zip code');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Check if zone already exists
      const exists = await checkZoneExists(zipCode);
      if (exists) {
        setError(`A zone for zip code ${zipCode} already exists`);
        setIsLoading(false);
        return;
      }
      
      // Use Nominatim (free, no API key required) to get zip code data
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?postalcode=${zipCode}&country=USA&format=json&limit=1`
      );
      
      if (!response.ok) {
        throw new Error('Failed to get information for this zip code');
      }
      
      const data = await response.json();
      
      if (!data || data.length === 0) {
        throw new Error(`Zip code ${zipCode} not found`);
      }
      
      const result = data[0];
      
      if (!result.lat || !result.lon || !result.boundingbox) {
        throw new Error('Incomplete location data for this zip code');
      }
      
      // Extract location details
      const lat = parseFloat(result.lat);
      const lng = parseFloat(result.lon);
      
      // Create bounds from the bounding box data
      const bounds = {
        southWest: {
          lat: parseFloat(result.boundingbox[0]),
          lng: parseFloat(result.boundingbox[2])
        },
        northEast: {
          lat: parseFloat(result.boundingbox[1]),
          lng: parseFloat(result.boundingbox[3])
        }
      };
      
      // Extract city and state from display name
      const addressParts = result.display_name.split(', ');
      const cityName = addressParts[0] || '';
      const stateName = addressParts[addressParts.length - 2] || '';
      
      // Default zone name if none provided
      const defaultZoneName = cityName 
        ? `${cityName}, ${stateName} (${zipCode})`
        : `Zip Code: ${zipCode}`;
      
      // Create zone document
      const newZone = {
        name: zoneName || defaultZoneName,
        color: zoneColor,
        description: `Zone for zip code ${zipCode} ${cityName ? `in ${cityName}, ${stateName}` : ''}`,
        bounds,
        zipCode,
        center: { lat, lng },
        formattedAddress: result.display_name,
        assignedUsers: [],
        createdAt: serverTimestamp(),
        createdBy: currentUser.uid,
        teamId,
        source: 'zipcode-generator'
      };
      
      // Add to Firestore
      const zoneRef = await addDoc(collection(db, 'teams', teamId, 'zones'), newZone);
      
      // Show success message
      setSuccess(true);
      
      // Notify parent component if callback provided
      if (onZoneCreated) {
        onZoneCreated({
          id: zoneRef.id,
          ...newZone
        });
      }
      
      // Reset form after a delay
      setTimeout(() => {
        setZipCode('');
        setZoneName('');
        setSuccess(false);
        
        // Close the modal if onClose callback provided
        if (typeof onClose === 'function') {
          onClose();
        }
      }, 2000);
    } catch (error) {
      console.error('Error creating zone from zip code:', error);
      setError(`Failed to create zone: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [zipCode, zoneName, zoneColor, currentUser, teamId, db, onZoneCreated, onClose]);
  
  return (
    <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium text-white">Create Zone from Zip Code</h2>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>
      
      {error && (
        <div className="bg-red-900 bg-opacity-25 border border-red-800 text-red-100 px-4 py-3 rounded-md mb-4">
          <p className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {error}
          </p>
        </div>
      )}
      
      {success && (
        <div className="bg-green-900 bg-opacity-25 border border-green-800 text-green-100 px-4 py-3 rounded-md mb-4">
          <p className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Zone created successfully!
          </p>
        </div>
      )}
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Zip Code*
          </label>
          <input
            type="text"
            value={zipCode}
            onChange={(e) => setZipCode(e.target.value.trim())}
            className="w-full px-3 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter zip code (e.g. 90210)"
            pattern="[0-9]{5}"
            maxLength={5}
          />
          <p className="text-xs text-gray-400 mt-1">
            Enter a 5-digit US zip code
          </p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Zone Name (Optional)
          </label>
          <input
            type="text"
            value={zoneName}
            onChange={(e) => setZoneName(e.target.value)}
            className="w-full px-3 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Leave blank for automatic naming"
          />
          <p className="text-xs text-gray-400 mt-1">
            If left blank, zone will be named based on city and zip code
          </p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Zone Color
          </label>
          <div className="flex flex-wrap gap-2 mt-1">
            {colorPalette.map(color => (
              <div
                key={color}
                onClick={() => setZoneColor(color)}
                className={`w-8 h-8 rounded-full cursor-pointer border-2 ${
                  color === zoneColor ? 'border-white' : 'border-transparent'
                }`}
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
        </div>
        
        <button
          onClick={createZoneFromZipCode}
          disabled={isLoading || !zipCode || zipCode.length !== 5 || !/^\d+$/.test(zipCode)}
          className={`w-full py-2 px-4 rounded-md font-medium ${
            isLoading || !zipCode || zipCode.length !== 5 || !/^\d+$/.test(zipCode)
              ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isLoading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating Zone...
            </span>
          ) : (
            'Create Zone from Zip Code'
          )}
        </button>
      </div>
    </div>
  );
}

export default ZipCodeZoneCreator;