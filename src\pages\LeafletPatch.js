import { useEffect } from 'react';

// LeafletPatch component to ensure Leaflet is patched
const LeafletPatch = () => {
  useEffect(() => {
    // Function to patch Leaflet MarkerCluster to prevent _unspiderfy errors
    const patchLeafletMarkerCluster = () => {
      // Only patch in browser environment
      if (typeof window === 'undefined' || !window.L) {
        console.log("Leaflet not loaded yet, waiting...");
        setTimeout(patchLeafletMarkerCluster, 500);
        return;
      }
      
      const L = window.L;
      
      // Check if MarkerClusterGroup exists
      if (!L.MarkerClusterGroup) {
        console.log("MarkerClusterGroup not loaded yet, waiting...");
        setTimeout(patchLeafletMarkerCluster, 500);
        return;
      }
      
      // Only patch once
      if (L.MarkerClusterGroup._patched) {
        console.log("MarkerClusterGroup already patched");
        return;
      }
      
      console.log("Patching Leaflet MarkerClusterGroup...");
      
      // Store original methods to maintain functionality
      const originalUnspiderfyLayer = L.MarkerClusterGroup.prototype._unspiderfyLayer;
      const originalSpiderfy = L.MarkerClusterGroup.prototype._spiderfy;
      const originalUnspiderfy = L.MarkerClusterGroup.prototype.unspiderfy;
      
      // Patch _unspiderfyLayer method
      L.MarkerClusterGroup.prototype._unspiderfyLayer = function() {
        try {
          if (!this._spiderfied) {
            console.log("Prevented _unspiderfyLayer error: _spiderfied is undefined");
            return;
          }
          return originalUnspiderfyLayer.apply(this, arguments);
        } catch (e) {
          console.warn("Prevented _unspiderfyLayer error:", e);
        }
      };
      
      // Patch _spiderfy method
      L.MarkerClusterGroup.prototype._spiderfy = function(cluster) {
        try {
          if (!cluster || !cluster._icon) {
            console.log("Prevented _spiderfy error: invalid cluster");
            return;
          }
          return originalSpiderfy.apply(this, arguments);
        } catch (e) {
          console.warn("Prevented _spiderfy error:", e);
        }
      };
      
      // Patch unspiderfy method
      L.MarkerClusterGroup.prototype.unspiderfy = function() {
        try {
          if (!this._spiderfied) {
            console.log("Prevented unspiderfy error: _spiderfied is undefined");
            return this;
          }
          return originalUnspiderfy.apply(this, arguments);
        } catch (e) {
          console.warn("Prevented unspiderfy error:", e);
          return this;
        }
      };
      
      // Add a safety method for clean removal
      L.MarkerClusterGroup.prototype.safeRemove = function() {
        try {
          // Try to clean up spiderfied markers first
          if (this._spiderfied) {
            this.unspiderfy();
          }
          
          // Now remove from map
          if (this._map) {
            this._map.removeLayer(this);
          }
        } catch (e) {
          console.warn("Error in safeRemove:", e);
        }
      };
      
      // Mark as patched
      L.MarkerClusterGroup._patched = true;
      
      console.log("Leaflet MarkerClusterGroup successfully patched");
    };
    
    // Run the patch
    patchLeafletMarkerCluster();
  }, []);
  
  // This component doesn't render anything
  return null;
};

export default LeafletPatch;