import React, { useState, useEffect } from 'react';
import { getFirestore, collection, getDocs, doc, getDoc } from 'firebase/firestore';
import { getApps } from 'firebase/app';
import { useAuth } from '../contexts/AuthContext.js';
function VehicleTrackerURLGenerator() {
  const { currentUser, isAdmin } = useAuth();
  const [teams, setTeams] = useState([]);
  const [selectedTeam, setSelectedTeam] = useState(null);
  const [teamMembers, setTeamMembers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [copiedUrls, setCopiedUrls] = useState(new Set());
  
  // UPDATED: Production URL configuration
  const getProductionUrl = () => {
    // Option 1: Set via environment variable (recommended)
    if (process.env.REACT_APP_PRODUCTION_URL) {
      return process.env.REACT_APP_PRODUCTION_URL;
    }
    
    // Option 2: Auto-detect production domain
    if (window.location.hostname !== 'localhost' && 
        window.location.hostname !== '127.0.0.1' && 
        !window.location.hostname.includes('192.168.')) {
      return window.location.origin;
    }
    
    // Option 3: Default to current live URL (UPDATED)
    return 'https://recoveriqs.net';
  };
  
  const [baseUrl, setBaseUrl] = useState(getProductionUrl());
  const [urlMode, setUrlMode] = useState('production');
  
  // UPDATED: Use existing Firebase instance
  const getFirestoreInstance = () => {
    const existingApps = getApps();
    if (existingApps.length > 0) {
      return getFirestore(existingApps[0]);
    }
    return getFirestore();
  };
  
  const db = getFirestoreInstance();

  // Load teams
  useEffect(() => {
    if (!isAdmin) return;

    const fetchTeams = async () => {
      try {
        console.log('🔍 Loading teams...');
        const teamsSnapshot = await getDocs(collection(db, 'teams'));
        const teamsData = teamsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        console.log('✅ Found teams:', teamsData.length);
        setTeams(teamsData);
        
        if (teamsData.length > 0) {
          setSelectedTeam(teamsData[0]);
        }
      } catch (err) {
        console.error("❌ Error loading teams:", err);
        setError("Failed to load teams");
      }
    };

    fetchTeams();
  }, [isAdmin, db]);

  // Load team members when team changes
  useEffect(() => {
    if (!selectedTeam) return;

    const fetchTeamMembers = async () => {
      setLoading(true);
      try {
        console.log('👥 Loading team members for:', selectedTeam.name);
        
        const teamMembersSnapshot = await getDocs(
          collection(db, `teams/${selectedTeam.id}/teamMembers`)
        );
        
        const memberIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);
        console.log('📋 Found member IDs:', memberIds);
        
        const membersData = await Promise.all(memberIds.map(async userId => {
          try {
            const userDoc = await getDoc(doc(db, "users", userId));
            
            if (!userDoc.exists()) {
              console.warn(`⚠️ User ${userId} not found in users collection`);
              return null;
            }
            
            const userData = userDoc.data();
            const profileDoc = await getDoc(doc(db, "userProfiles", userId));
            const profileData = profileDoc.exists() ? profileDoc.data() : {};
            
            return {
              id: userId,
              ...userData,
              displayName: profileData.displayName || userData.email?.split('@')[0] || 'Unknown User',
              avatar: profileData.avatar || getUserAvatar(userData.role),
              jobTitle: profileData.jobTitle || userData.role
            };
          } catch (error) {
            console.error(`❌ Error fetching data for user ${userId}:`, error);
            return null;
          }
        }));
        
        const validMembers = membersData.filter(Boolean);
        console.log('✅ Valid team members loaded:', validMembers.length);
        setTeamMembers(validMembers);
        
        if (validMembers.length === 0) {
          setError(`No valid users found in team "${selectedTeam.name}". Check that team members exist in the users collection.`);
        } else {
          setError(null);
        }
        
      } catch (err) {
        console.error("❌ Error loading team members:", err);
        setError("Failed to load team members");
      } finally {
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, [selectedTeam, db]);

  const getUserAvatar = (role) => {
    switch(role) {
      case 'admin': return '👨‍💼';
      case 'manager': return '👩‍💼';
      case 'tow': return '🚚';
      case 'technician': return '👨‍🔧';
      case 'support': return '👩‍💻';
      case 'driver': return '🚗';
      case 'operator': return '📱';
      case 'supervisor': return '👨‍🏫';
      default: return '👤';
    }
  };

  const generateUrl = (userId) => {
    return `${baseUrl}/vehicles/${userId}`;
  };

  const generateQueryUrl = (userId) => {
    return `${baseUrl}/vehicles?user=${userId}`;
  };

  // UPDATED: Better clipboard handling
  const copyToClipboard = async (text, userId) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedUrls(prev => new Set([...prev, userId]));
      
      console.log('📋 Copied to clipboard:', text);
      
      // Remove the copied indicator after 3 seconds
      setTimeout(() => {
        setCopiedUrls(prev => {
          const newSet = new Set(prev);
          newSet.delete(userId);
          return newSet;
        });
      }, 3000);
    } catch (err) {
      console.error('❌ Failed to copy to clipboard:', err);
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      
      // Still show the copied indicator
      setCopiedUrls(prev => new Set([...prev, userId]));
      setTimeout(() => {
        setCopiedUrls(prev => {
          const newSet = new Set(prev);
          newSet.delete(userId);
          return newSet;
        });
      }, 3000);
    }
  };

  const copyAllUrls = () => {
    const allUrls = teamMembers.map(member => 
      `${member.displayName}: ${generateUrl(member.id)}`
    ).join('\n');
    
    copyToClipboard(allUrls, 'all');
  };

  const handleUrlModeChange = (mode) => {
    setUrlMode(mode);
    if (mode === 'production') {
      setBaseUrl(getProductionUrl());
    }
  };

  // UPDATED: Test URL functionality
  const testUrl = async (userId) => {
    const testUrl = generateQueryUrl(userId);
    console.log('🧪 Testing URL:', testUrl);
    
    try {
      // Open in new tab
      window.open(testUrl, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('❌ Error opening test URL:', error);
      // Fallback: copy URL and show message
      await copyToClipboard(testUrl, `test-${userId}`);
      alert(`Test URL copied to clipboard: ${testUrl}`);
    }
  };

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-gray-300">
            Only administrators can access the URL generator.
          </p>
        </div>
      </div>
    );
  }

  const isValidUrl = () => {
    try {
      new URL(baseUrl);
      return !baseUrl.includes('localhost') && 
             !baseUrl.includes('127.0.0.1') && 
             !baseUrl.includes('192.168.');
    } catch {
      return false;
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 shadow-md border-b border-gray-700">
        <div className="mx-auto px-4 py-4">
          <h1 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">
            Vehicle Tracker URL Generator
          </h1>
          <p className="text-sm text-gray-400 mt-1">
            Generate live access links for team members to quickly add vehicles
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* URL Validation Warning */}
        {!isValidUrl() && (
          <div className="bg-yellow-900 bg-opacity-50 border border-yellow-700 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <div className="text-yellow-500 text-xl mr-3">⚠️</div>
              <div>
                <h3 className="text-yellow-300 font-semibold">Production URL Required</h3>
                <p className="text-yellow-200 text-sm mt-1">
                  Current URL appears to be localhost/development. Team members won't be able to access these URLs from their devices.
                  Please set your live production domain below.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-4 mb-6">
          <h2 className="text-lg font-semibold text-blue-300 mb-2">
            How to Use
          </h2>
          <div className="text-sm text-gray-300 space-y-2">
            <p>• Set your live production domain below (currently: {window.location.origin})</p>
            <p>• Generate direct access URLs for each team member</p>
            <p>• Team members can bookmark their URL for quick vehicle entry</p>
            <p>• No sign-in required - direct access to their vehicle tracker</p>
            <p>• All entries automatically sync with the team and carry over between weeks</p>
          </div>
        </div>

        {/* Controls */}
        <div className="bg-gray-800 rounded-lg p-4 mb-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Select Team
              </label>
              <select
                value={selectedTeam?.id || ''}
                onChange={(e) => {
                  const team = teams.find(t => t.id === e.target.value);
                  setSelectedTeam(team);
                }}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white"
                disabled={teams.length === 0}
              >
                {teams.length === 0 ? (
                  <option value="">No teams found</option>
                ) : (
                  teams.map(team => (
                    <option key={team.id} value={team.id}>{team.name}</option>
                  ))
                )}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                URL Mode
              </label>
              <select
                value={urlMode}
                onChange={(e) => handleUrlModeChange(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white"
              >
                <option value="production">Production (Auto)</option>
                <option value="custom">Custom URL</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Production Domain
                <span className="text-red-400 ml-1">*</span>
              </label>
              <input
                type="text"
                value={baseUrl}
                onChange={(e) => {
                  setBaseUrl(e.target.value);
                  setUrlMode('custom');
                }}
                className={`w-full border rounded-md px-3 py-2 text-white ${
                  isValidUrl() 
                    ? 'bg-gray-700 border-gray-600' 
                    : 'bg-red-900 bg-opacity-30 border-red-600'
                }`}
                placeholder="https://recoveriqs.net"
              />
              {!isValidUrl() && (
                <p className="text-red-400 text-xs mt-1">
                  Enter your live production URL (not localhost)
                </p>
              )}
            </div>
          </div>

          {teamMembers.length > 0 && isValidUrl() && (
            <div className="flex justify-between items-center">
              <p className="text-sm text-gray-400">
                {teamMembers.length} team member(s) found
              </p>
              <button
                onClick={copyAllUrls}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                {copiedUrls.has('all') ? 'Copied!' : 'Copy All URLs'}
              </button>
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-900 bg-opacity-50 border border-red-700 rounded-lg p-4 mb-6">
            <p className="text-red-200">{error}</p>
          </div>
        )}

        {/* Team Members URLs */}
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <span className="ml-3 text-gray-300">Loading team members...</span>
          </div>
        ) : (
          <div className="space-y-3">
            {teamMembers.map(member => (
              <div key={member.id} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="text-2xl mr-3">{member.avatar}</div>
                    <div>
                      <h3 className="font-medium text-white">{member.displayName}</h3>
                      <p className="text-sm text-gray-400">{member.email}</p>
                      <p className="text-xs text-blue-300">{member.jobTitle}</p>
                      <p className="text-xs text-gray-500 font-mono">ID: {member.id}</p>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => copyToClipboard(generateQueryUrl(member.id), member.id)}
                      disabled={!isValidUrl()}
                      className={`px-3 py-2 rounded-md text-sm transition-colors ${
                        !isValidUrl() 
                          ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                          : copiedUrls.has(member.id) 
                            ? 'bg-green-600 text-white' 
                            : 'bg-blue-600 hover:bg-blue-700 text-white'
                      }`}
                    >
                      {!isValidUrl() ? 'Set URL First' : copiedUrls.has(member.id) ? '✓ Copied' : 'Copy URL'}
                    </button>
                    
                    {isValidUrl() && (
                      <button
                        onClick={() => testUrl(member.id)}
                        className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-md text-sm"
                      >
                        Test
                      </button>
                    )}
                  </div>
                </div>
                
                <div className="mt-3 p-3 bg-gray-700 rounded border">
                  <div className="text-xs text-gray-400 mb-1">Recommended URL (Query Parameter):</div>
                  <div className={`font-mono text-sm break-all ${isValidUrl() ? 'text-green-400' : 'text-gray-500'}`}>
                    {generateQueryUrl(member.id)}
                  </div>
                </div>
                
                <div className="mt-2 p-3 bg-gray-700 rounded border">
                  <div className="text-xs text-gray-400 mb-1">Alternative (Path Parameter):</div>
                  <div className={`font-mono text-sm break-all ${isValidUrl() ? 'text-blue-400' : 'text-gray-500'}`}>
                    {generateUrl(member.id)}
                  </div>
                </div>
              </div>
            ))}
            
            {teamMembers.length === 0 && !loading && selectedTeam && (
              <div className="text-center py-8 text-gray-400">
                <div className="text-6xl mb-4">👥</div>
                <p className="text-lg mb-2">No team members found</p>
                <p className="text-sm">Make sure team members are added to the "{selectedTeam.name}" team and exist in the users collection.</p>
              </div>
            )}
          </div>
        )}

        {/* Usage Instructions */}
        <div className="mt-8 bg-gray-800 rounded-lg p-4 border border-gray-600">
          <h3 className="text-lg font-semibold text-gray-200 mb-3">📱 How Team Members Use URLs</h3>
          <div className="text-sm text-gray-300 space-y-2">
            <p><strong>1.</strong> Copy the generated URL for each team member</p>
            <p><strong>2.</strong> Share via text message, email, or chat</p>
            <p><strong>3.</strong> Team members bookmark the URL on their phones</p>
            <p><strong>4.</strong> They can add vehicles instantly without logging in</p>
            <p><strong>5.</strong> All data syncs automatically with the team</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default VehicleTrackerURLGenerator;