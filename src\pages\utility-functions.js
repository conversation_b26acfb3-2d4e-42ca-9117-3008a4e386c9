// Utility functions for formatting, geocoding, and car image generation

import { serverTimestamp } from 'firebase/firestore';

// Enhanced color mapping for car visualization
export const COLORS_MAP = {
  'Black': 'black',
  'White': 'white',
  'Silver': 'silver',
  'Gray': 'gray',
  'Red': 'red',
  'Blue': 'blue',
  'Green': 'green',
  'Yellow': 'yellow',
  'Brown': 'brown',
  'Orange': 'orange',
  'Purple': 'purple',
  'Gold': 'gold',
  'Beige': 'beige',
  'Burgundy': 'burgundy'
};

// Enhanced status options with styling
export const STATUS_OPTIONS = {
  'open': { 
    label: 'Open', 
    bgColor: 'bg-blue-500', 
    textColor: 'text-white'
  },
  'pending-pickup': { 
    label: 'Pending Pickup', 
    bgColor: 'bg-yellow-500', 
    textColor: 'text-gray-900'
  },
  'secure': { 
    label: 'Secure', 
    bgColor: 'bg-green-500', 
    textColor: 'text-white'
  },
  'on-hold': { 
    label: 'On-Hold', 
    bgColor: 'bg-purple-500', 
    textColor: 'text-white'
  },
  'claim': { 
    label: 'Claim', 
    bgColor: 'bg-pink-500', 
    textColor: 'text-white'
  },
  'closed': { 
    label: 'Closed', 
    bgColor: 'bg-gray-500', 
    textColor: 'text-white'
  },
  'restricted': { 
    label: 'Restricted', 
    bgColor: 'bg-red-500', 
    textColor: 'text-white'
  }
};

// Status mapping between orders and locations collections
export const STATUS_MAPPING = {
  'open': 'pending',
  'pending-pickup': 'pending',
  'on-hold': 'pending',
  'claim': 'pending',
  'restricted': 'pending',
  'secure': 'picked-up',
  'closed': 'completed'
};

// Calculate days past due
export const calculateDaysPastDue = (dueDate) => {
  if (!dueDate) return 0;
  
  const dueDateObj = dueDate.toDate ? dueDate.toDate() : new Date(dueDate);
  const today = new Date();
  
  // Reset time parts to compare just the dates
  dueDateObj.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);
  
  const diffTime = today - dueDateObj;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays > 0 ? diffDays : 0;
};

// Calculate days since last check-in
export const calculateDaysSinceLastCheckIn = (lastCheckIn) => {
  if (!lastCheckIn) return null;
  
  const checkInDate = lastCheckIn.toDate ? lastCheckIn.toDate() : new Date(lastCheckIn);
  const today = new Date();
  
  // Reset time parts to compare just the dates
  checkInDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);
  
  const diffTime = today - checkInDate;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
};

// Format date for display
export const formatDate = (timestamp) => {
  if (!timestamp) return 'N/A';
  
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  }).format(date);
};

// Format address for display
export const formatAddress = (address) => {
  if (!address) return '';
  
  if (typeof address === 'string') {
    return address;
  }
  
  let parts = [];
  if (address.street) parts.push(address.street);
  
  let cityStateZip = '';
  if (address.city) cityStateZip += address.city;
  if (address.state) {
    if (cityStateZip) cityStateZip += ', ';
    cityStateZip += address.state;
  }
  if (address.zip) {
    if (cityStateZip) cityStateZip += ' ';
    cityStateZip += address.zip;
  }
  
  if (cityStateZip) parts.push(cityStateZip);
  
  return parts.join(', ');
};

// Format user display name
export const formatUserDisplayName = (user) => {
  if (!user) return 'Unknown User';
  if (user.displayName) return user.displayName;
  if (user.email) {
    const emailParts = user.email.split('@');
    if (emailParts.length > 0) return emailParts[0];
    return user.email;
  }
  return 'User';
};

// Process addresses to ensure proper format
export const processAddresses = (addresses) => {
  if (!addresses) return [];
  
  return addresses.map(addr => {
    if (typeof addr === 'string') {
      // Legacy string address format
      return {
        street: addr,
        city: '',
        state: '',
        zip: '',
        checkIns: []
      };
    }
    
    // Ensure all fields exist
    return {
      street: addr.street || '',
      city: addr.city || '',
      state: addr.state || '',
      zip: addr.zip || '',
      position: addr.position || null,
      checkIns: addr.checkIns || []
    };
  });
};

// Determine vehicle drive type based on make and model
export const determineDriveType = (make, model, year) => {
  if (!make || !model) return '';
  
  make = make.toLowerCase().trim();
  model = model.toLowerCase().trim();
  const yearNum = parseInt(year) || 2023;
  
  // Specific brand patterns
  if (make === 'subaru' && model !== 'brz') {
    return 'AWD'; // Nearly all Subarus are AWD except BRZ
  }
  
  if (make === 'audi' && yearNum >= 2000) {
    return 'AWD'; // Most modern Audis have Quattro
  }
  
  // Check for AWD naming patterns in luxury brands
  if (make === 'bmw' && model.includes('xdrive')) {
    return 'AWD';
  }
  
  if ((make === 'mercedes' || make === 'mercedes-benz') && 
      (model.includes('4matic') || model.includes('4-matic'))) {
    return 'AWD';
  }
  
  // Default for most ordinary cars
  return 'FWD'; 
};

// IMPROVED: Geocoding function to convert address to coordinates using Nominatim API
export const geocodeAddress = async (address) => {
  // Skip if address already has coordinates
  if (address?.position?.lat && address?.position?.lng) {
    return { ...address };
  }
  
  // Create a properly formatted search string from the address components
  const parts = [];
  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.zip) parts.push(address.zip);
  
  const searchString = parts.filter(Boolean).join(', ');
  
  if (!searchString) {
    console.warn("Cannot geocode empty address");
    return address;
  }
  
  try {
    console.log(`Geocoding address: ${searchString}`);
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Use OpenStreetMap Nominatim API for geocoding with additional parameters
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchString)}&addressdetails=1&limit=1`,
      {
        headers: {
          // Add a user-agent to comply with Nominatim's usage policy
          'User-Agent': 'VehicleTrackingApp'
        }
      }
    );
    
    if (!response.ok) {
      throw new Error(`Geocoding API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data && data.length > 0) {
      // Add position data to the address
      const geocodedAddress = { 
        ...address,
        position: {
          lat: parseFloat(data[0].lat),
          lng: parseFloat(data[0].lon)
        }
      };
      console.log(`Successfully geocoded address to: ${geocodedAddress.position.lat}, ${geocodedAddress.position.lng}`);
      return geocodedAddress;
    } else {
      console.warn(`No geocoding results found for: ${searchString}`);
      return address;
    }
  } catch (error) {
    console.error("Error geocoding address:", error);
    return address;
  }
};

// UPDATED: Function to fetch car data from API Ninjas and then generate accurate 3D renders
export const fetchCarDataAndGenerateImage = async (make, model, year, color) => {
  if (!make || !model) return null;
  
  try {
    console.log(`Fetching car data for ${make} ${model} ${year}...`);
    
    // First, try to get accurate car details from the Cars API
    const response = await fetch(
      `https://cars-by-api-ninjas.p.rapidapi.com/v1/cars?make=${encodeURIComponent(make.toLowerCase())}&model=${encodeURIComponent(model.toLowerCase())}${year ? `&year=${year}` : ''}`,
      {
        headers: {
          'x-rapidapi-host': 'cars-by-api-ninjas.p.rapidapi.com',
          'x-rapidapi-key': '**************************************************'
        }
      }
    );
    
    if (!response.ok) {
      throw new Error(`Car API error: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('Car API response:', data);
    
    // If we got valid car data
    if (data && data.length > 0) {
      // Use the most accurate data (first result)
      const carData = data[0];
      
      // Format parameters for 3D rendering with validated data
      const validatedMake = carData.make || make;
      const validatedModel = carData.model || model;
      const validatedYear = carData.year || year;
      
      // Use color parameter if available (default to white if not specified)
      const colorCode = color ? COLORS_MAP[color] || color.toLowerCase() : 'white';
      
      // Generate the URL with validated car details - use a reliable customer key
      const imageUrl = `https://cdn.imagin.studio/getimage?customer=hrjavascript-mastery&make=${encodeURIComponent(validatedMake)}&modelFamily=${encodeURIComponent(validatedModel)}${validatedYear ? `&modelYear=${validatedYear}` : ''}&angle=1&paintId=${encodeURIComponent(colorCode)}`;
      
      console.log('Generated image URL with validated data:', imageUrl);
      return {
        url: imageUrl,
        angle: '1',
        colorName: color || 'White',
        colorCode: colorCode,
        // Store the validated car data for reference
        carData: carData
      };
    } else {
      // Fallback to direct rendering if no API data found
      console.log('No car data found, using fallback rendering');
      return generateFallbackCarImage(make, model, year, color);
    }
  } catch (error) {
    console.error("Error fetching car data:", error);
    // Use fallback render method if API fails
    return generateFallbackCarImage(make, model, year, color);
  }
};

// Fallback function for when API data isn't available
export const generateFallbackCarImage = (make, model, year, color) => {
  // Try multiple known working format combinations
  const formattedMake = make.toLowerCase().trim();
  let formattedModel = model.toLowerCase().trim();
  
  // Remove common problematic parts from model names
  formattedModel = formattedModel
    .replace(/\s(series|class|model)(\s+\w+)?$/i, '') // Remove series/class designations
    .replace(/\s+(sedan|coupe|convertible|hatchback|wagon|suv|truck)$/i, '') // Remove body types
    .replace(/\s+\d+(\.\d+)?.*$/, ''); // Remove numbers and anything after
  
  // Use color parameter if available
  const colorCode = color ? COLORS_MAP[color] || color.toLowerCase() : 'white';
  
  // Try a more generic approach with fewer parameters - use a reliable customer ID
  const imageUrl = `https://cdn.imagin.studio/getimage?customer=hrjavascript-mastery&make=${encodeURIComponent(formattedMake)}&modelFamily=${encodeURIComponent(formattedModel)}&zoomType=fullscreen&paintId=${encodeURIComponent(colorCode)}`;
  
  return {
    url: imageUrl,
    angle: '1',
    colorName: color || 'White',
    colorCode: colorCode,
    isFallback: true
  };
};

// Function to generate multiple angles for a car
export const generateMultipleCarViews = async (make, model, year, color) => {
  if (!make || !model) return [];
  
  try {
    // Get the base car data and first angle
    const baseView = await fetchCarDataAndGenerateImage(make, model, year, color);
    if (!baseView) return [];
    
    // Define angles for additional views
    const angles = ['1', '5', '9', '13', '29'];
    const views = [];
    
    // For cars with validated data, generate all angles
    if (baseView.carData) {
      const validatedMake = baseView.carData.make || make;
      const validatedModel = baseView.carData.model || model;
      const validatedYear = baseView.carData.year || year;
      const colorCode = color ? COLORS_MAP[color] || color.toLowerCase() : 'white';
      
      for (const angle of angles) {
        const imageUrl = `https://cdn.imagin.studio/getimage?customer=hrjavascript-mastery&make=${encodeURIComponent(validatedMake)}&modelFamily=${encodeURIComponent(validatedModel)}${validatedYear ? `&modelYear=${validatedYear}` : ''}&angle=${angle}&paintId=${encodeURIComponent(colorCode)}`;
        
        views.push({
          url: imageUrl,
          angle: angle,
          colorName: color || 'White',
          colorCode: colorCode,
          carData: baseView.carData
        });
      }
    }
    // For fallback images, try a more limited set of angles
    else if (baseView.isFallback) {
      const formattedMake = make.toLowerCase().trim();
      let formattedModel = model.toLowerCase().trim().replace(/\s+(sedan|coupe|convertible|hatchback|wagon|suv|truck)$/i, '');
      const colorCode = color ? COLORS_MAP[color] || color.toLowerCase() : 'white';
      
      // Only use a few angles for fallback to reduce chance of errors
      const limitedAngles = ['1', '5', '29'];
      
      for (const angle of limitedAngles) {
        const imageUrl = `https://cdn.imagin.studio/getimage?customer=hrjavascript-mastery&make=${encodeURIComponent(formattedMake)}&modelFamily=${encodeURIComponent(formattedModel)}&angle=${angle}&paintId=${encodeURIComponent(colorCode)}`;
        
        views.push({
          url: imageUrl,
          angle: angle,
          colorName: color || 'White',
          colorCode: colorCode,
          isFallback: true
        });
      }
    }
    
    return views.length > 0 ? views : [baseView];
  } catch (error) {
    console.error("Error generating multiple car views:", error);
    return [];
  }
};

// Generate a fallback image URL if needed
export const getFallbackImageUrl = (order) => {
  if (order.make && order.model && order.year) {
    const formattedMake = encodeURIComponent(order.make.toLowerCase());
    const formattedModel = encodeURIComponent(order.model.toLowerCase().replace(/\s(series|class)$/i, '').replace(/\s+\d+$/,''));
    const colorCode = order.color ? COLORS_MAP[order.color] || order.color.toLowerCase() : 'white';
    
    return `https://cdn.imagin.studio/getimage?customer=hrjavascript-mastery&make=${formattedMake}&modelFamily=${formattedModel}&zoomType=fullscreen&paintId=${encodeURIComponent(colorCode)}`;
  }
  return null;
};

// Function to prepare order data for Firestore
export const prepareOrderData = (orderData, currentUser) => {
  // Get current timestamp
  const timestamp = new Date();
  
  // Create the order data
  const preparedData = {
    ...orderData,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
    createdBy: currentUser?.uid,
    createdByName: formatUserDisplayName(currentUser),
  };
  
  // For locations panel integration, also provide a formatted name
  preparedData.name = `${orderData.year} ${orderData.make} ${orderData.model} - ${orderData.licensePlate}`;
  
  return preparedData;
};