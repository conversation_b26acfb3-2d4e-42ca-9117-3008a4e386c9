// tailwind.config.js
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      height: {
        'screen-header': 'calc(100vh - 64px)',
        'screen-header-sm': 'calc(100vh - 128px)',
        'map-area': 'calc(100vh - 220px)',
        'map-area-sm': 'calc(100vh - 280px)',
        'map-area-lg': 'calc(100vh - 180px)',
        // Add orientation-specific map heights
        'map-portrait': 'calc(100vh - 350px)',
        'map-landscape': 'calc(100vh - 250px)',
        // iPad specific heights
        'ipad-portrait': 'calc(100vh - 300px)',
        'ipad-landscape': 'calc(100vh - 200px)',
        // Add specific chat heights for all screen sizes
        'chat-xs': '220px',
        'chat-sm': '260px',
        'chat-md': '280px',
        'chat-lg': '300px',
        'chat-xl': '300px',
      },
      maxHeight: {
        'panel-mobile': '120px',
        'panel-tablet': '140px',
        'panel-desktop': '160px',
        // Updated chat heights to match our CSS
        'chat-mobile': '30vh',    // Updated from 25vh
        'chat-tablet': '30vh',    // Updated from 28vh
        'chat-desktop': '30vh',   // Same as before
        // Orientation-specific chat heights
        'chat-portrait': '30vh',  // Same as before
        'chat-landscape': '30vh', // Updated from 25vh to match new value
        // Add specific chat heights for computer screens
        'chat-1080p': '300px',    // For 1080p screens
        'chat-1440p': '350px',    // For 1440p screens
        'chat-4k': '400px',       // For 4K screens
      },
      minHeight: {
        'map-mobile': '300px',
        'map-tablet': '400px',
        'map-desktop': '500px',
        'map-portrait': '350px',
        'map-landscape': '400px',
        // Add minimum heights for chat on different devices
        'chat-mobile': '220px',
        'chat-tablet': '260px',
        'chat-desktop': '300px',
      },
      width: {
        // Panel widths for different devices/orientations
        'panel-mobile': '85%',
        'panel-tablet': '40%',
        'panel-tablet-landscape': '25%',
        'panel-desktop': '22%',
        'panel-widescreen': '18%',
      },
      zIndex: {
        'map-control': 100,
        'modal': 1000,
      },
      fontSize: {
        'xxs': '0.65rem',
      },
      padding: {
        'safe-area-top': 'env(safe-area-inset-top)',
        'safe-area-bottom': 'env(safe-area-inset-bottom)',
        'safe-area-left': 'env(safe-area-inset-left)',
        'safe-area-right': 'env(safe-area-inset-right)',
      },
      animation: {
        'title-letter': 'titleLetterAnimation 2s ease-in-out forwards',
        'fade-in': 'fadeIn 1.5s ease-in-out forwards',
        'fade-out': 'fadeOut 1.5s ease-in-out forwards',
        'float': 'float 3s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        titleLetterAnimation: {
          '0%': {
            opacity: '0',
            transform: 'translateY(20px)'
          },
          '20%': {
            opacity: '1',
            transform: 'translateY(0)'
          },
          '80%': {
            opacity: '1',
            transform: 'translateY(0)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        },
        fadeIn: {
          '0%': {
            opacity: '0'
          },
          '100%': {
            opacity: '1'
          }
        },
        fadeOut: {
          '0%': {
            opacity: '1'
          },
          '100%': {
            opacity: '0'
          }
        },
        float: {
          '0%, 100%': {
            transform: 'translateY(0)'
          },
          '50%': {
            transform: 'translateY(-10px)'
          }
        }
      },
      transitionDuration: {
        '1500': '1500ms',
        '2000': '2000ms',
      }
    },
    screens: {
      'xs': '320px',
      'sm': '640px',
      'md': '768px', // iPad portrait min-width
      'lg': '1024px', // iPad landscape min-width
      'xl': '1280px',
      '2xl': '1536px',
      // Add orientation-specific breakpoints
      'portrait': {'raw': '(orientation: portrait)'},
      'landscape': {'raw': '(orientation: landscape)'},
      // iPad specific breakpoints
      'ipad': {'raw': '(min-device-width: 768px) and (max-device-width: 1024px)'},
      'ipad-portrait': {'raw': '(min-device-width: 768px) and (max-device-width: 1024px) and (orientation: portrait)'},
      'ipad-landscape': {'raw': '(min-device-width: 768px) and (max-device-width: 1024px) and (orientation: landscape)'},
      // iPhone specific breakpoints (if needed)
      'iphone': {'raw': '(max-device-width: 767px)'},
      // Desktop/computer screen breakpoints
      'desktop': {'raw': '(min-width: 1025px)'},
      '1080p': {'raw': '(min-width: 1920px)'},
      '1440p': {'raw': '(min-width: 2560px)'},
      '4k': {'raw': '(min-width: 3840px)'},
    },
    
  },
  variants: {
    extend: {
      opacity: ['disabled'],
      cursor: ['disabled'],
      backgroundColor: ['active', 'disabled'],
      textColor: ['active', 'disabled'],
      display: ['group-hover', 'portrait', 'landscape'],
      width: ['portrait', 'landscape'],
      height: ['portrait', 'landscape'],
    },
  },
  
  plugins: [],
}