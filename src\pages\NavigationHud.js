import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import './NavigationHUD.css';

/**
 * NavigationHUD - A dedicated component for displaying navigation information
 * 
 * This component appears when navigation is active and displays:
 * - Direction indicators with next turn information
 * - Distance to destination
 * - Estimated time of arrival
 * - Arrival clock time
 * - Destination address
 * - Cancel navigation button
 */
const NavigationHUD = ({
  isNavigating,
  navigationDirection,
  distanceToDestination,
  estimatedTime,
  arrivalTime,
  destinationAddress,
  stopNavigation,
  formatDistance,
  formatTime,
  formatClockTime,
  // Add new props for enhanced navigation
  nextInstruction = null,
  destinationLocation = null // New prop to track the destination location object
}) => {
  // Log when navigation state changes to help with debugging
  useEffect(() => {
    if (isNavigating) {
      console.log("NavigationHUD state:", {
        isNavigating,
        navigationDirection,
        nextInstruction,
        destinationLocation
      });
    }
  }, [isNavigating, navigationDirection, nextInstruction, destinationLocation]);

  // UPDATED: Only check for required function props, not isNavigating
  const hasRequiredFunctions = typeof stopNavigation === 'function' && 
                              typeof formatDistance === 'function' && 
                              typeof formatTime === 'function' && 
                              typeof formatClockTime === 'function';
                              
  if (!hasRequiredFunctions) {
    console.warn("NavigationHUD missing required function props");
    return null; // Don't render if essential function props are missing
  }
  
  // Don't render anything if not navigating (without warning)
  if (!isNavigating) return null;
  
  // Helper to get direction icon
  const getDirectionIcon = (direction) => {
    switch(direction) {
      case 'left':
      case 'west':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
        );
      case 'right':
      case 'east':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        );
      case 'straight':
      case 'north':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l4 4a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L6.707 8.707a1 1 0 01-1.414-1.414l4-4A1 1 0 0110 3z" clipRule="evenodd" />
          </svg>
        );
      case 'uturn':
      case 'south':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M16.293 9.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 011.414-1.414L9 13.586V3a1 1 0 012 0v10.586l4.293-4.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l4 4a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L6.707 8.707a1 1 0 01-1.414-1.414l4-4A1 1 0 0110 3z" clipRule="evenodd" />
          </svg>
        );
    }
  };
  
  // Helper to format instruction text
  const formatInstructionText = (instruction) => {
    if (!instruction) return "Proceed to destination";
    
    // If instruction is a string, just return it
    if (typeof instruction === 'string') return instruction;
    
    // If instruction has a text property, use that
    if (instruction.text) {
      // Convert HTML instructions to plain text if needed
      let text = instruction.text;
      if (text.includes('<')) {
        // Create a temporary div to parse HTML
        const temp = document.createElement('div');
        temp.innerHTML = text;
        text = temp.textContent || temp.innerText || '';
      }
      return text;
    }
    
    // Default fallback
    return "Proceed to destination";
  };
  
  // Determine what to display for the next instruction
  const instructionToShow = nextInstruction || {
    text: "Proceed to destination",
    distance: distanceToDestination ? distanceToDestination * 1609.34 : 0 // Convert miles to meters
  };
  
  // Determine display address
  const displayAddress = destinationAddress || 
                         (destinationLocation?.name) ||
                         (destinationLocation?.address) ||
                         'Destination';
  
  return (
    <div className="navigation-hud-container">
      <div className="bg-gradient-to-r from-gray-800 to-gray-900 border-2 border-blue-800 rounded-lg shadow-lg p-3 mx-auto max-w-4xl">
        {/* Prominent Next Turn Direction - Added at the top */}
        <div className="mb-3 text-center">
          <div className="text-lg font-bold text-white bg-blue-900 rounded-lg p-2 inline-flex items-center justify-center">
            <div className="mr-3 w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              {getDirectionIcon(navigationDirection)}
            </div>
            <div className="text-left">
              <div className="text-base md:text-lg">{formatInstructionText(instructionToShow)}</div>
              {instructionToShow.distance && (
                <div className="text-sm text-blue-200">
                  In {(instructionToShow.distance / 1609.34).toFixed(1)} miles
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row items-center justify-between">
          {/* Left section - Destination info */}
          <div className="flex items-center mb-2 md:mb-0">
            <div className="text-center md:text-left">
              <div className="text-sm font-bold text-blue-400 mb-1 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                <span>Navigation Active</span>
              </div>
              <div className="text-xs text-gray-300 truncate max-w-xs" title={displayAddress}>
                To: <span className="text-blue-300 font-medium">{displayAddress}</span>
              </div>
            </div>
          </div>
          
          {/* Middle section - Metrics */}
          <div className="flex space-x-4 items-center mb-2 md:mb-0">
            <div className="text-center bg-gray-700 bg-opacity-50 rounded-lg px-3 py-1">
              <div className="text-xs text-gray-400">Distance</div>
              <div className="text-sm font-bold text-white">{distanceToDestination ? formatDistance(distanceToDestination) : 'Calculating...'}</div>
            </div>
            
            <div className="text-center bg-gray-700 bg-opacity-50 rounded-lg px-3 py-1">
              <div className="text-xs text-gray-400">ETA</div>
              <div className="text-sm font-bold text-white">{estimatedTime ? formatTime(estimatedTime) : 'Calculating...'}</div>
            </div>
            
            <div className="text-center bg-gray-700 bg-opacity-50 rounded-lg px-3 py-1">
              <div className="text-xs text-gray-400">Arrival</div>
              <div className="text-sm font-bold text-green-400">{arrivalTime ? formatClockTime(arrivalTime) : 'Calculating...'}</div>
            </div>
          </div>
          
          {/* Right section - Direction indicators and cancel button */}
          <div className="flex items-center space-x-3">
            {/* Direction indicators */}
            <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${navigationDirection === 'left' || navigationDirection === 'west' || navigationDirection === 'south' ? 'bg-blue-500 text-white' : 'bg-gray-700 text-gray-400'}`}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
              </div>
              
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${navigationDirection === 'straight' || navigationDirection === 'north' ? 'bg-blue-500 text-white' : 'bg-gray-700 text-gray-400'}`}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l4 4a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L6.707 8.707a1 1 0 01-1.414-1.414l4-4A1 1 0 0110 3z" clipRule="evenodd" />
                </svg>
              </div>
              
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${navigationDirection === 'right' || navigationDirection === 'east' ? 'bg-blue-500 text-white' : 'bg-gray-700 text-gray-400'}`}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {/* Added debug button for development environment */}
              {process.env.NODE_ENV === 'development' && (
                <button 
                  onClick={() => {
                    console.log("Navigation Debug Info:", {
                      isNavigating,
                      distanceToDestination,
                      estimatedTime,
                      navigationDirection,
                      destinationAddress,
                      nextInstruction,
                      destinationLocation
                    });
                    
                    // Log global navigation values if they exist
                    if (typeof window !== 'undefined') {
                      console.log("Window navigation values:", {
                        currentLocation: window.currentLocation,
                        destination: window.destination
                      });
                    }
                  }}
                  className="bg-gray-600 hover:bg-gray-500 text-white text-xs px-2 py-1 rounded-md"
                  title="Debug navigation info"
                >
                  Debug
                </button>
              )}
              
              <button 
                onClick={stopNavigation}
                className="bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1.5 rounded-md flex items-center transition-colors duration-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                Cancel
              </button>
            </div>
          </div>
        </div>

        {/* Regular style tag instead of style jsx */}
        <style>{`
          .navigation-hud-container {
            animation: slide-in-top 0.3s ease-out;
            z-index: 100;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            margin: 0 auto;
            padding: 0.5rem;
          }
          
          @keyframes slide-in-top {
            0% {
              transform: translateY(-20px);
              opacity: 0;
            }
            100% {
              transform: translateY(0);
              opacity: 1;
            }
          }
          
          @keyframes pulse-direction {
            0% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.1);
            }
            100% {
              transform: scale(1);
            }
          }
          
          .bg-blue-600 {
            animation: pulse-direction 2s infinite;
          }
        `}</style>
      </div>
    </div>
  );
};

// Define PropTypes for proper type checking
NavigationHUD.propTypes = {
  isNavigating: PropTypes.bool.isRequired,
  navigationDirection: PropTypes.string,
  distanceToDestination: PropTypes.number,
  estimatedTime: PropTypes.number,
  arrivalTime: PropTypes.instanceOf(Date),
  destinationAddress: PropTypes.string,
  stopNavigation: PropTypes.func.isRequired,
  formatDistance: PropTypes.func.isRequired,
  formatTime: PropTypes.func.isRequired,
  formatClockTime: PropTypes.func.isRequired,
  nextInstruction: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.string
  ]),
  destinationLocation: PropTypes.object
};

// Set default values for optional props
NavigationHUD.defaultProps = {
  navigationDirection: 'straight',
  distanceToDestination: null,
  estimatedTime: null,
  arrivalTime: null,
  destinationAddress: '',
  nextInstruction: null,
  destinationLocation: null
};

export default NavigationHUD;