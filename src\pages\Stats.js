import React, { useState, useEffect } from 'react';
import { getFirestore, doc, getDoc, collection, getDocs, updateDoc, setDoc, serverTimestamp, query, orderBy, limit } from 'firebase/firestore';

const Stats = ({ 
  teamName = "Your Team", 
  teamStats, 
  isNavigating, 
  navigationDirection, 
  distanceToDestination, 
  estimatedTime, 
  arrivalTime, 
  destinationAddress, 
  stopNavigation,
  formatDistance,
  formatTime,
  formatClockTime,
  // Pay calculation props
  currentUser,
  hourlyRate = 15, 
  bonusPerCarRecovered = 80, 
  scanBonusPerTeamMember = 150, 
  scanBonusTier1ThresholdPerPerson = 65000,  
  scanBonusTier2ThresholdPerPerson = 80000,  
  scanBonusTier3ThresholdPerPerson = 100000, 
  scanBonusTier4ThresholdPerPerson = 150000, 
  scanBonusTier5ThresholdPerPerson = 250000, 
  hoursWorkedToday = 0, 
  isAdmin = false, 
  userRole = '', 
  userTags = [], 
  teamSize = { spotters: 0, towDrivers: 0 }, 
  resetAllStats = () => {},
  onStatsSizeChange = () => {}, // New callback to notify parent components
  initialMinimized = true // New prop to control initial minimized state
}) => {
  // State for modals and popups
  const [showResetConfirmation, setShowResetConfirmation] = useState(false);
  const [showStatDetail, setShowStatDetail] = useState(false);
  const [showTeamRanking, setShowTeamRanking] = useState(false);
  const [selectedStat, setSelectedStat] = useState(null);
  const [isUserAdmin, setIsUserAdmin] = useState(false);
  const [allTeams, setAllTeams] = useState([]);
  const [db, setDb] = useState(null);
  const [userRoleFromTags, setUserRoleFromTags] = useState('');
  
  // Use initialMinimized prop for initial state
  const [isMinimized, setIsMinimized] = useState(initialMinimized);
  
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [currentHeight, setCurrentHeight] = useState(null);
  const [isNewUser, setIsNewUser] = useState(false);
  const [currentTeamRank, setCurrentTeamRank] = useState(0);
  const [totalTeamsCount, setTotalTeamsCount] = useState(0);
  
  // New state for pay settings - updated with new toggle options
  const [paySettings, setPaySettings] = useState({
    enablePayCalculations: true,
    enableBasicPayCalculations: true, // New toggle for basic pay features
    enableScanBonusCalculations: true, // New toggle for scan bonus features
    showTaxEstimates: true,
    hourlyRate: 15,
    bonusPerCarRecovered: 80,
    scanBonusPerTeamMember: 150,
    scanBonusTier1ThresholdPerPerson: 65000,
    scanBonusTier2ThresholdPerPerson: 80000,
    scanBonusTier3ThresholdPerPerson: 100000,
    scanBonusTier4ThresholdPerPerson: 150000,
    scanBonusTier5ThresholdPerPerson: 250000,
    stateTaxRate: 4.95,
    socialSecurityTaxRate: 6.2,
    medicareTaxRate: 1.45,
    exampleCarsPerWeek: 20,
    exampleHoursPerWeek: 40,
    exampleTeamSize: 4,
    exampleScansPerWeek: 70000
  });
  const [showExamplePay, setShowExamplePay] = useState(false);

  // Helper function to safely format numbers with toLocaleString
  const safelyFormatNumber = (value) => {
    // Check if value is undefined or null and default to 0
    return (value || 0).toLocaleString();
  };

  // Ensure teamStats exists and has default values of zero to prevent errors
  // Moved this up before any references to stats
  const stats = teamStats || {
    userOpenOrdersToday: 0, userOpenOrdersWeek: 0, userOpenOrdersMonth: 0, userOpenOrdersYTD: 0,
    teamOpenOrdersToday: 0, teamOpenOrdersWeek: 0, teamOpenOrdersMonth: 0, teamOpenOrdersYTD: 0,
    userHoursWorkedToday: 0, userHoursWorkedWeek: 0, userHoursWorkedMonth: 0, userHoursWorkedYTD: 0,
    teamHoursWorkedToday: 0, teamHoursWorkedWeek: 0, teamHoursWorkedMonth: 0, teamHoursWorkedYTD: 0,
    carsFoundToday: 0, carsFoundWeek: 0, carsFoundMonth: 0, carsFoundYTD: 0,
    carsRecoveredToday: 0, carsRecoveredWeek: 0, carsRecoveredMonth: 0, carsRecoveredYTD: 0,
    userScansToday: 0, userScansWeek: 0, userScansMonth: 0, userScansYTD: 0,
    teamScansToday: 0, teamScansWeek: 0, teamScansMonth: 0, teamScansYTD: 0,
    userMarkersCreatedToday: 0, userMarkersCreatedWeek: 0, userMarkersCreatedMonth: 0, userMarkersCreatedYTD: 0,
    teamMarkersCreatedToday: 0, teamMarkersCreatedWeek: 0, teamMarkersCreatedMonth: 0, teamMarkersCreatedYTD: 0,
    userMilesTraveledToday: 0, userMilesTraveledWeek: 0, userMilesTraveledMonth: 0, userMilesTraveledYTD: 0,
    teamMilesTraveledToday: 0, teamMilesTraveledWeek: 0, teamMilesTraveledMonth: 0, teamMilesTraveledYTD: 0,
    teamRank: currentTeamRank, totalTeams: totalTeamsCount
  };

  // Initialize Firestore
  useEffect(() => {
    const firestore = getFirestore();
    setDb(firestore);
  }, []);
  
  // Load pay settings from Firestore
  useEffect(() => {
    const loadPaySettings = async () => {
      try {
        if (!db) return;
        
        const settingsDoc = await getDoc(doc(db, 'settings', 'payCalculation'));
        
        if (settingsDoc.exists()) {
          const savedSettings = settingsDoc.data();
          // Merge saved settings with defaults
          setPaySettings(prevSettings => ({
            ...prevSettings,
            ...savedSettings
          }));
        }
      } catch (error) {
        console.error("Error loading pay calculation settings:", error);
      }
    };
    
    loadPaySettings();
  }, [db]);

  // Check if this is a first-time user
  useEffect(() => {
    const hasSeenDragHandle = localStorage.getItem('hasSeenStatsDragHandle');
    
    if (!hasSeenDragHandle) {
      setIsNewUser(true);
      
      // Show tooltip animation for 5 seconds
      const timer = setTimeout(() => {
        setIsNewUser(false);
        localStorage.setItem('hasSeenStatsDragHandle', 'true');
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, []);

  // Notify parent when minimized state changes
  useEffect(() => {
    onStatsSizeChange(isMinimized);
  }, [isMinimized, onStatsSizeChange]);

  // Determine user role based on tags
  useEffect(() => {
    const determineUserRole = () => {
      // First check if we have tags
      if (userTags && userTags.length > 0) {
        // Check for specific tag names that indicate roles
        const adminTag = userTags.find(tag => 
          tag.name.toLowerCase() === 'admin' || 
          tag.name.toLowerCase().includes('admin')
        );
        
        const towTag = userTags.find(tag => 
          tag.name.toLowerCase() === 'tow' || 
          tag.name.toLowerCase().includes('tow')
        );
        
        if (adminTag) {
          setUserRoleFromTags('admin');
          return;
        } else if (towTag) {
          setUserRoleFromTags('tow');
          return;
        } else {
          // Default to regular user if no specific role tag found
          setUserRoleFromTags('user');
          return;
        }
      }
      
      // Fallback to prop if no tags or no matching tags
      setUserRoleFromTags(userRole || 'user');
    };
    
    determineUserRole();
  }, [userTags, userRole]);

  // Check admin status from Firestore or tags
  useEffect(() => {
    const checkAdminStatus = async () => {
      // First check if we determined admin from tags
      if (userRoleFromTags === 'admin') {
        console.log("Admin status found from tags");
        setIsUserAdmin(true);
        return;
      }
      
      // Then check props
      if (isAdmin === true || userRole === 'admin') {
        console.log("Admin status found from props");
        setIsUserAdmin(true);
        return;
      }

      // Then check Firestore if currentUser exists
      try {
        if (currentUser?.uid) {
          const db = getFirestore();
          const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
          
          if (userDoc.exists()) {
            const userData = userDoc.data();
            
            // Check for admin in multiple possible places
            if (
              userData.isAdmin === true || 
              userData.role === 'admin' || 
              userData.userRole === 'admin' || 
              userData.admin === true
            ) {
              console.log("Admin status found in Firestore");
              setIsUserAdmin(true);
              return;
            }
          }
        }
        
        setIsUserAdmin(false);
      } catch (error) {
        console.error("Error checking admin status:", error);
        setIsUserAdmin(false);
      }
    };

    // Load teams data from Firestore
    const loadTeamsData = async () => {
      try {
        if (!db) return;
        
        // Query teams collection - use real Firestore data
        const teamsRef = collection(db, 'teams');
        const teamsQuery = query(teamsRef, orderBy('score', 'desc'));
        const teamsSnapshot = await getDocs(teamsQuery);
        
        if (!teamsSnapshot.empty) {
          // Map the data and add rank based on index
          const teamsData = teamsSnapshot.docs.map((doc, index) => {
            return {
              id: doc.id,
              rank: index + 1,
              ...doc.data()
            };
          });
          
          // Store total team count
          setTotalTeamsCount(teamsData.length);
          
          // Find current team's rank
          const currentTeam = teamsData.find(team => team.name === teamName);
          if (currentTeam) {
            setCurrentTeamRank(currentTeam.rank);
          }
          
          setAllTeams(teamsData);
          console.log("Loaded real team data:", teamsData.length, "teams");
        } else {
          console.log("No teams found in Firestore, showing stats with current team only");
          // If we don't find teams in Firestore but we have a team name,
          // create a minimal team entry for display
          const singleTeam = [
            { 
              id: 'team1', 
              name: teamName, 
              rank: 1, 
              score: 0, 
              members: teamSize.spotters + teamSize.towDrivers,
              carsFound: stats.carsFoundToday || 0, 
              scans: stats.teamScansToday || 0 
            }
          ];
          setTotalTeamsCount(1);
          setCurrentTeamRank(1);
          setAllTeams(singleTeam);
        }
      } catch (error) {
        console.error("Error loading teams data:", error);
        // If error, show at least current team
        const fallbackTeam = [
          { 
            id: 'currentTeam', 
            name: teamName, 
            rank: 1, 
            score: 0, 
            members: teamSize.spotters + teamSize.towDrivers, 
            carsFound: 0, 
            scans: 0 
          }
        ];
        
        setTotalTeamsCount(1);
        setCurrentTeamRank(1);
        setAllTeams(fallbackTeam);
      }
    };

    checkAdminStatus();
    loadTeamsData();
  }, [db, isAdmin, userRole, currentUser, userRoleFromTags, teamName, teamSize, teamStats]); // Changed stats to teamStats

  // Auto-Reset Stats Feature
  useEffect(() => {
    // Function to check if stats should be reset
    const checkAndResetStats = async () => {
      try {
        if (!db || !currentUser) return;
        
        const statsConfigRef = doc(db, 'statsConfig', 'resetTimes');
        const statsConfig = await getDoc(statsConfigRef);
        
        if (statsConfig.exists()) {
          const config = statsConfig.data();
          const now = new Date();
          
          // Check daily reset (store in UTC to avoid timezone issues)
          if (!config.lastDailyReset || isNewDay(new Date(config.lastDailyReset.toDate()), now)) {
            console.log("Resetting daily stats...");
            resetDailyStats();
            
            // Update reset timestamp
            await updateDoc(statsConfigRef, {
              lastDailyReset: serverTimestamp()
            });
          }
          
          // Check weekly reset (generally Sunday or Monday)
          if (!config.lastWeeklyReset || isNewWeek(new Date(config.lastWeeklyReset.toDate()), now)) {
            console.log("Resetting weekly stats...");
            resetWeeklyStats();
            
            await updateDoc(statsConfigRef, {
              lastWeeklyReset: serverTimestamp()
            });
          }
          
          // Check monthly reset
          if (!config.lastMonthlyReset || isNewMonth(new Date(config.lastMonthlyReset.toDate()), now)) {
            console.log("Resetting monthly stats...");
            resetMonthlyStats();
            
            await updateDoc(statsConfigRef, {
              lastMonthlyReset: serverTimestamp()
            });
          }
        } else {
          // Create initial config
          await setDoc(statsConfigRef, {
            lastDailyReset: serverTimestamp(),
            lastWeeklyReset: serverTimestamp(),
            lastMonthlyReset: serverTimestamp()
          });
        }
      } catch (error) {
        console.error("Error checking stats reset times:", error);
      }
    };
    
    // Helper functions to check time periods
    const isNewDay = (lastReset, now) => {
      return lastReset.getDate() !== now.getDate() || 
             lastReset.getMonth() !== now.getMonth() || 
             lastReset.getFullYear() !== now.getFullYear();
    };
    
    const isNewWeek = (lastReset, now) => {
      // Consider Sunday or Monday as start of week
      const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday
      const isWeekStart = dayOfWeek === 1; // Monday
      return isWeekStart && lastReset.getDay() !== dayOfWeek;
    };
    
    const isNewMonth = (lastReset, now) => {
      return lastReset.getMonth() !== now.getMonth() || 
             lastReset.getFullYear() !== now.getFullYear();
    };
    
    checkAndResetStats();
    
    // Set up interval to check for resets (every hour)
    const interval = setInterval(checkAndResetStats, 3600000);
    return () => clearInterval(interval);
  }, [db, currentUser]);

  // Reset functions
  const resetDailyStats = async () => {
    if (!db || !currentUser?.uid) return;
    
    try {
      // Get team stats document
      const teamStatsRef = doc(db, 'teamStats', 'current');
      const teamStatsDoc = await getDoc(teamStatsRef);
      
      if (teamStatsDoc.exists()) {
        // Reset only daily values
        await updateDoc(teamStatsRef, {
          userOpenOrdersToday: 0,
          teamOpenOrdersToday: 0,
          userHoursWorkedToday: 0,
          teamHoursWorkedToday: 0,
          carsFoundToday: 0,
          carsRecoveredToday: 0,
          userScansToday: 0,
          teamScansToday: 0,
          userMarkersCreatedToday: 0,
          teamMarkersCreatedToday: 0,
          userMilesTraveledToday: 0,
          teamMilesTraveledToday: 0
        });
        
        console.log("Daily stats reset successful");
      }
    } catch (error) {
      console.error("Error resetting daily stats:", error);
    }
  };

  const resetWeeklyStats = async () => {
    if (!db || !currentUser?.uid) return;
    
    try {
      // Get team stats document
      const teamStatsRef = doc(db, 'teamStats', 'current');
      const teamStatsDoc = await getDoc(teamStatsRef);
      
      if (teamStatsDoc.exists()) {
        // Reset only weekly values
        await updateDoc(teamStatsRef, {
          userOpenOrdersWeek: 0,
          teamOpenOrdersWeek: 0,
          userHoursWorkedWeek: 0,
          teamHoursWorkedWeek: 0,
          carsFoundWeek: 0,
          carsRecoveredWeek: 0,
          userScansWeek: 0,
          teamScansWeek: 0,
          userMarkersCreatedWeek: 0,
          teamMarkersCreatedWeek: 0,
          userMilesTraveledWeek: 0,
          teamMilesTraveledWeek: 0
        });
        
        console.log("Weekly stats reset successful");
      }
    } catch (error) {
      console.error("Error resetting weekly stats:", error);
    }
  };

  const resetMonthlyStats = async () => {
    if (!db || !currentUser?.uid) return;
    
    try {
      // Get team stats document
      const teamStatsRef = doc(db, 'teamStats', 'current');
      const teamStatsDoc = await getDoc(teamStatsRef);
      
      if (teamStatsDoc.exists()) {
        // Reset only monthly values
        await updateDoc(teamStatsRef, {
          userOpenOrdersMonth: 0,
          teamOpenOrdersMonth: 0,
          userHoursWorkedMonth: 0,
          teamHoursWorkedMonth: 0,
          carsFoundMonth: 0,
          carsRecoveredMonth: 0,
          userScansMonth: 0,
          teamScansMonth: 0,
          userMarkersCreatedMonth: 0,
          teamMarkersCreatedMonth: 0,
          userMilesTraveledMonth: 0,
          teamMilesTraveledMonth: 0
        });
        
        console.log("Monthly stats reset successful");
      }
    } catch (error) {
      console.error("Error resetting monthly stats:", error);
    }
  };

  // Calculate proper team composition based on user roles from tags
  const calculateTeamComposition = () => {
    // Use teamSize prop if it appears to be set correctly (not both zero when there are team members)
    if ((teamSize.spotters > 0 || teamSize.towDrivers > 0) && teamSize.spotters + teamSize.towDrivers > 0) {
      return teamSize;
    }
    
    // Default fallback - at minimum one team member of the user's role type 
    if (userRoleFromTags === 'tow') {
      return { spotters: 0, towDrivers: 1 };
    } else {
      return { spotters: 1, towDrivers: 0 };
    }
  };

  // Get team composition numbers
  const teamComposition = calculateTeamComposition();
  const totalSpotters = teamComposition.spotters;
  const totalTowDrivers = teamComposition.towDrivers;
  const totalTeamMembers = totalSpotters + totalTowDrivers;

  // Use a minimum team size of 1 for calculations to avoid division by zero
  const effectiveTeamSize = Math.max(1, totalTeamMembers);
  
  // Check all required settings for pay features
  const shouldShowBasicPay = paySettings.enablePayCalculations && paySettings.enableBasicPayCalculations;
  const shouldShowScanBonus = paySettings.enablePayCalculations && paySettings.enableScanBonusCalculations;
  
  // Use pay settings if available and enabled
  const effectiveHourlyRate = shouldShowBasicPay ? paySettings.hourlyRate : hourlyRate;
  const effectiveBonusPerCarRecovered = shouldShowBasicPay ? paySettings.bonusPerCarRecovered : bonusPerCarRecovered;
  const effectiveScanBonusPerTeamMember = shouldShowScanBonus ? paySettings.scanBonusPerTeamMember : scanBonusPerTeamMember;
  const effectiveScanBonusTier1ThresholdPerPerson = shouldShowScanBonus ? paySettings.scanBonusTier1ThresholdPerPerson : scanBonusTier1ThresholdPerPerson;
  const effectiveScanBonusTier2ThresholdPerPerson = shouldShowScanBonus ? paySettings.scanBonusTier2ThresholdPerPerson : scanBonusTier2ThresholdPerPerson;
  const effectiveScanBonusTier3ThresholdPerPerson = shouldShowScanBonus ? paySettings.scanBonusTier3ThresholdPerPerson : scanBonusTier3ThresholdPerPerson;
  const effectiveScanBonusTier4ThresholdPerPerson = shouldShowScanBonus ? paySettings.scanBonusTier4ThresholdPerPerson : scanBonusTier4ThresholdPerPerson;
  const effectiveScanBonusTier5ThresholdPerPerson = shouldShowScanBonus ? paySettings.scanBonusTier5ThresholdPerPerson : scanBonusTier5ThresholdPerPerson;
  
  // Calculate scan bonus tiers based on team size - pure multiplication of per member amount
  const scanBonusTier1 = effectiveScanBonusPerTeamMember * effectiveTeamSize;
  const scanBonusTier2 = effectiveScanBonusPerTeamMember * 2 * effectiveTeamSize;
  const scanBonusTier3 = effectiveScanBonusPerTeamMember * 3 * effectiveTeamSize;
  const scanBonusTier4 = effectiveScanBonusPerTeamMember * 4 * effectiveTeamSize;
  const scanBonusTier5 = effectiveScanBonusPerTeamMember * 5 * effectiveTeamSize;

  // Calculate actual scan thresholds based on team size
  const calculateTeamScanThreshold = (baseThreshold) => {
    return baseThreshold * effectiveTeamSize;
  };

  // Calculate the actual scan thresholds for the current team size
  const scanBonusTier1Threshold = calculateTeamScanThreshold(effectiveScanBonusTier1ThresholdPerPerson);
  const scanBonusTier2Threshold = calculateTeamScanThreshold(effectiveScanBonusTier2ThresholdPerPerson);
  const scanBonusTier3Threshold = calculateTeamScanThreshold(effectiveScanBonusTier3ThresholdPerPerson);
  const scanBonusTier4Threshold = calculateTeamScanThreshold(effectiveScanBonusTier4ThresholdPerPerson);
  const scanBonusTier5Threshold = calculateTeamScanThreshold(effectiveScanBonusTier5ThresholdPerPerson);

  // Safe division to prevent division by zero errors
  const safeDivide = (numerator, denominator, defaultValue = 0) => {
    if (!denominator || denominator === 0) return defaultValue;
    return numerator / denominator;
  };

  // Safe percentage calculation
  const calculatePercentage = (part, total, defaultValue = 0) => {
    return safeDivide(part, total, defaultValue) * 100;
  };

  // Calculate scan bonus with 5 tiers - only if scan bonus is enabled
  const calculateScanBonus = (scans) => {
    if (!shouldShowScanBonus) return 0;
    
    const safeScans = scans || 0;
    if (safeScans >= scanBonusTier5Threshold) {
      return scanBonusTier5;
    } else if (safeScans >= scanBonusTier4Threshold) {
      return scanBonusTier4;
    } else if (safeScans >= scanBonusTier3Threshold) {
      return scanBonusTier3;
    } else if (safeScans >= scanBonusTier2Threshold) {
      return scanBonusTier2;
    } else if (safeScans >= scanBonusTier1Threshold) {
      return scanBonusTier1;
    }
    return 0;
  };

  // UPDATED PAY CALCULATION STRUCTURE - respects feature toggles
  const calculatePay = (carsFound, carsRecovered, hoursWorked, teamScans, isDaily = true) => {
    // Return zero values if pay calculations are disabled completely
    if (!paySettings.enablePayCalculations) return {
      hourlyPay: 0,
      teamBonusPool: 0,
      scanBonus: 0,
      totalPay: 0
    };
    
    // Handle undefined inputs
    const safeHoursWorked = hoursWorked || 0;
    const safeCarsRecovered = carsRecovered || 0; 
    const safeTeamScans = teamScans || 0;
    
    // Base hourly pay - only if basic pay is enabled
    const hourlyPay = shouldShowBasicPay ? effectiveHourlyRate * safeHoursWorked : 0;
    
    // Team bonus pool from recovered cars - only if basic pay is enabled
    const teamBonusPool = shouldShowBasicPay ? safeCarsRecovered * effectiveBonusPerCarRecovered : 0;
    
    // Scan bonus (only applies to weekly calculations) - only if scan bonus is enabled
    const scanBonus = (!isDaily && shouldShowScanBonus) ? calculateScanBonus(safeTeamScans) : 0;
    
    // Total pay
    const totalPay = hourlyPay + teamBonusPool + scanBonus;
    
    return {
      hourlyPay,
      teamBonusPool,
      scanBonus,
      totalPay
    };
  };
  
  // Calculate pay for different time periods (now using team scans)
  const userDailyPayDetails = calculatePay(stats.carsFoundToday, stats.carsRecoveredToday, hoursWorkedToday, stats.teamScansToday, true);
  const userWeeklyPayDetails = calculatePay(stats.carsFoundWeek, stats.carsRecoveredWeek, stats.userHoursWorkedWeek, stats.teamScansWeek, false);
  const userMonthlyPayDetails = calculatePay(stats.carsFoundMonth, stats.carsRecoveredMonth, stats.userHoursWorkedMonth, stats.teamScansMonth, false);
  const userYearlyPayDetails = calculatePay(stats.carsFoundYTD, stats.carsRecoveredYTD, stats.userHoursWorkedYTD, stats.teamScansYTD, false);

  // Extract totals for simpler access
  const userDailyPay = userDailyPayDetails.totalPay;
  const userWeeklyPay = userWeeklyPayDetails.totalPay;
  const userMonthlyPay = userMonthlyPayDetails.totalPay;
  const userYearlyPay = userYearlyPayDetails.totalPay;

  // Calculate team pay metrics - respecting feature toggles
  // For basic pay (hourly + recovery)
  const teamHourlyPay = shouldShowBasicPay ? (stats.teamHoursWorkedToday * effectiveHourlyRate) : 0;
  const teamRecoveryBonus = shouldShowBasicPay ? (stats.carsRecoveredToday * effectiveBonusPerCarRecovered) : 0;
  
  // Calculate total pay values with the appropriate components
  const teamDailyPay = teamRecoveryBonus + teamHourlyPay;
  const teamWeeklyHourlyPay = shouldShowBasicPay ? (stats.teamHoursWorkedWeek * effectiveHourlyRate) : 0;
  const teamWeeklyRecoveryBonus = shouldShowBasicPay ? (stats.carsRecoveredWeek * effectiveBonusPerCarRecovered) : 0;
  const teamWeeklyScanBonus = shouldShowScanBonus ? calculateScanBonus(stats.teamScansWeek) : 0;
  const teamWeeklyPay = teamWeeklyRecoveryBonus + teamWeeklyHourlyPay + teamWeeklyScanBonus;
  
  const teamMonthlyPay = 
    (shouldShowBasicPay ? stats.carsRecoveredMonth * effectiveBonusPerCarRecovered : 0) + 
    (shouldShowBasicPay ? stats.teamHoursWorkedMonth * effectiveHourlyRate : 0);
  
  const teamYearlyPay = 
    (shouldShowBasicPay ? stats.carsRecoveredYTD * effectiveBonusPerCarRecovered : 0) + 
    (shouldShowBasicPay ? stats.teamHoursWorkedYTD * effectiveHourlyRate : 0);

  // Calculate user's contribution percentage to the team (safe calculation)
  const userContribution = (stats.teamOpenOrdersToday === 0) ? 
    "0.0" : 
    calculatePercentage(stats.userOpenOrdersToday, stats.teamOpenOrdersToday).toFixed(1);

  // Calculate scan bonus for user based on team scans
  const teamScanBonus = shouldShowScanBonus ? calculateScanBonus(stats.teamScansWeek) : 0;
  
  // Calculate example pay with taxes
  const calculateExamplePayWithTaxes = () => {
    if (!paySettings.enablePayCalculations || !paySettings.showTaxEstimates) {
      return null;
    }
    
    // Ensure at least one pay component is enabled
    if (!shouldShowBasicPay && !shouldShowScanBonus) {
      return null;
    }
    
    const { 
      exampleCarsPerWeek,
      exampleHoursPerWeek,
      exampleTeamSize,
      exampleScansPerWeek,
      stateTaxRate,
      socialSecurityTaxRate,
      medicareTaxRate,
      federalTaxBrackets = [
        { rate: 10, threshold: 0 },
        { rate: 12, threshold: 11000 },
        { rate: 22, threshold: 44725 },
        { rate: 24, threshold: 95375 },
        { rate: 32, threshold: 182100 },
        { rate: 35, threshold: 231250 },
        { rate: 37, threshold: 578125 }
      ]
    } = paySettings;
    
    // Calculate scan bonus - only if enabled
    let scanBonus = 0;
    if (shouldShowScanBonus) {
      // Calculate scan bonus thresholds for the example team
      const tier1Threshold = effectiveScanBonusTier1ThresholdPerPerson * exampleTeamSize;
      const tier2Threshold = effectiveScanBonusTier2ThresholdPerPerson * exampleTeamSize;
      const tier3Threshold = effectiveScanBonusTier3ThresholdPerPerson * exampleTeamSize;
      const tier4Threshold = effectiveScanBonusTier4ThresholdPerPerson * exampleTeamSize;
      const tier5Threshold = effectiveScanBonusTier5ThresholdPerPerson * exampleTeamSize;
      
      if (exampleScansPerWeek >= tier5Threshold) {
        scanBonus = effectiveScanBonusPerTeamMember * 5 * exampleTeamSize;
      } else if (exampleScansPerWeek >= tier4Threshold) {
        scanBonus = effectiveScanBonusPerTeamMember * 4 * exampleTeamSize;
      } else if (exampleScansPerWeek >= tier3Threshold) {
        scanBonus = effectiveScanBonusPerTeamMember * 3 * exampleTeamSize;
      } else if (exampleScansPerWeek >= tier2Threshold) {
        scanBonus = effectiveScanBonusPerTeamMember * 2 * exampleTeamSize;
      } else if (exampleScansPerWeek >= tier1Threshold) {
        scanBonus = effectiveScanBonusPerTeamMember * exampleTeamSize;
      }
    }
    
    // Per person calculations - respecting feature toggles
    const scanBonusPerPerson = shouldShowScanBonus ? (scanBonus / exampleTeamSize) : 0;
    const recoveryBonusPool = shouldShowBasicPay ? (exampleCarsPerWeek * effectiveBonusPerCarRecovered) : 0;
    const recoveryBonusPerPerson = shouldShowBasicPay ? (recoveryBonusPool / exampleTeamSize) : 0;
    const hourlyPay = shouldShowBasicPay ? (effectiveHourlyRate * exampleHoursPerWeek) : 0;
    
    // Calculate gross pay
    const grossWeeklyPay = hourlyPay + recoveryBonusPerPerson + scanBonusPerPerson;
    
    // Annualize for tax calculations
    let annualizedPay = grossWeeklyPay * 52;
    
    // Calculate taxes
    // 1. Federal income tax (simplified progressive calculation)
    let federalTax = 0;
    for (let i = federalTaxBrackets.length - 1; i >= 0; i--) {
      const bracket = federalTaxBrackets[i];
      const nextLowerThreshold = i > 0 ? federalTaxBrackets[i-1].threshold : 0;
      
      if (annualizedPay > bracket.threshold) {
        const taxableAmountInBracket = i === federalTaxBrackets.length - 1 ? 
          annualizedPay - bracket.threshold :
          Math.min(annualizedPay - bracket.threshold, bracket.threshold - nextLowerThreshold);
          
        federalTax += taxableAmountInBracket * (bracket.rate / 100);
      }
    }
    
    const weeklyFederalTax = federalTax / 52;
    
    // 2. State tax (flat rate in Illinois)
    const weeklyStateTax = grossWeeklyPay * (stateTaxRate / 100);
    
    // 3. FICA taxes
    const weeklySocialSecurityTax = Math.min(grossWeeklyPay * (socialSecurityTaxRate / 100), 
                                            (147000 / 52) * (socialSecurityTaxRate / 100)); // 2023 SS wage cap
    const weeklyMedicareTax = grossWeeklyPay * (medicareTaxRate / 100);
    
    // Total tax and net pay
    const totalWeeklyTax = weeklyFederalTax + weeklyStateTax + weeklySocialSecurityTax + weeklyMedicareTax;
    const netWeeklyPay = grossWeeklyPay - totalWeeklyTax;
    
    return {
      grossWeeklyPay,
      hourlyPay,
      recoveryBonusPerPerson,
      scanBonusPerPerson,
      weeklyFederalTax,
      weeklyStateTax,
      weeklySocialSecurityTax,
      weeklyMedicareTax,
      totalWeeklyTax,
      netWeeklyPay
    };
  };
  
  // Calculate the example pay
  const examplePay = calculateExamplePayWithTaxes();
  
  // Toggle the example pay display
  const toggleExamplePay = () => {
    setShowExamplePay(!showExamplePay);
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  // Calculate trend percentages safely
  const calculateTrend = (weekly, monthly) => {
    const weeklyAvg = safeDivide(monthly, 4);
    if (weeklyAvg === 0) return 0;
    return ((weekly / weeklyAvg) - 1) * 100;
  };

  // Handle stat card click to show detailed popup
  const handleStatClick = (statType) => {
    setSelectedStat(statType);
    setShowStatDetail(true);
  };

  // Show team rankings
  const handleShowTeamRanking = () => {
    setShowTeamRanking(true);
  };

  // Handle reset stats confirmation
  const handleResetConfirm = () => {
    resetAllStats();
    setShowResetConfirmation(false);
  };

  // Returns style classes for team rank (gold, silver, bronze)
  const getRankStyle = (rank) => {
    switch (rank) {
      case 1:
        return "bg-gradient-to-r from-yellow-600 to-yellow-300 text-yellow-900 animate-pulse";
      case 2:
        return "bg-gradient-to-r from-gray-500 to-gray-300 text-gray-800 animate-pulse";
      case 3:
        return "bg-gradient-to-r from-orange-600 to-orange-300 text-orange-900 animate-pulse";
      default:
        return "bg-gradient-to-r from-blue-600 to-blue-400 text-blue-900";
    }
  };

  // Helper function to determine the current scan bonus tier with progress
  const getScanBonusTierInfo = (scans) => {
    // Return zero values if scan bonus is disabled
    if (!shouldShowScanBonus) {
      return { tier: 0, amount: 0, progress: 0 };
    }
    
    const safeScans = scans || 0;
    const tier5Progress = Math.min(100, (safeScans / scanBonusTier5Threshold) * 100);
    const tier4Progress = Math.min(100, (safeScans / scanBonusTier4Threshold) * 100);
    const tier3Progress = Math.min(100, (safeScans / scanBonusTier3Threshold) * 100);
    const tier2Progress = Math.min(100, (safeScans / scanBonusTier2Threshold) * 100);
    const tier1Progress = Math.min(100, (safeScans / scanBonusTier1Threshold) * 100);
    
    if (safeScans >= scanBonusTier5Threshold) {
      return { tier: 5, amount: scanBonusTier5, progress: 100 };
    } else if (safeScans >= scanBonusTier4Threshold) {
      return { tier: 4, amount: scanBonusTier4, progress: tier5Progress };
    } else if (safeScans >= scanBonusTier3Threshold) {
      return { tier: 3, amount: scanBonusTier3, progress: tier4Progress };
    } else if (safeScans >= scanBonusTier2Threshold) {
      return { tier: 2, amount: scanBonusTier2, progress: tier3Progress };
    } else if (safeScans >= scanBonusTier1Threshold) {
      return { tier: 1, amount: scanBonusTier1, progress: tier2Progress };
    } else {
      return { tier: 0, amount: 0, progress: tier1Progress };
    }
  };

  // Get the current scan tier info
  const scanTierInfo = getScanBonusTierInfo(stats.teamScansWeek);

  // Helper function to get role display text
  const getRoleDisplayText = () => {
    if (userRoleFromTags === 'tow') {
      return 'Tow Driver';
    } else if (userRoleFromTags === 'admin') {
      return 'Administrator';
    } else {
      return 'Spotter';
    }
  };
  
  // Toggle minimized state
  const toggleMinimized = () => {
    setIsMinimized(!isMinimized);
  };

  // Handle touch start for drag functionality
  const handleTouchStart = (e) => {
    setIsDragging(true);
    setStartY(e.touches[0].clientY);
  };

  // Handle mouse down for drag functionality
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartY(e.clientY);
  };

  // Handle touch move for drag functionality
  const handleTouchMove = (e) => {
    if (!isDragging) return;
    const currentY = e.touches[0].clientY;
    const deltaY = currentY - startY;
    
    // If dragged up more than 50px, minimize
    if (deltaY < -50 && !isMinimized) {
      setIsMinimized(true);
      setIsDragging(false);
    }
    // If dragged down more than 50px, maximize
    else if (deltaY > 50 && isMinimized) {
      setIsMinimized(false);
      setIsDragging(false);
    }
  };

  // Handle mouse move for drag functionality
  const handleMouseMove = (e) => {
    if (!isDragging) return;
    const currentY = e.clientY;
    const deltaY = currentY - startY;
    
    // If dragged up more than 50px, minimize
    if (deltaY < -50 && !isMinimized) {
      setIsMinimized(true);
      setIsDragging(false);
    }
    // If dragged down more than 50px, maximize
    else if (deltaY > 50 && isMinimized) {
      setIsMinimized(false);
      setIsDragging(false);
    }
  };

  // Handle touch/mouse end
  const handleDragEnd = () => {
    setIsDragging(false);
  };

  // Add event listeners for drag functionality
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleDragEnd);
      document.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('touchend', handleDragEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleDragEnd);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleDragEnd);
    };
  }, [isDragging]);

  // Add CSS styles for animations
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      /* Base stats container styles */
      .stats-container {
        transition: height 0.3s ease-in-out, max-height 0.3s ease-in-out;
        overflow: hidden;
      }
      
      .stats-container.minimized {
        max-height: 60px;
      }
      
      .stats-container.expanded {
        max-height: 1000px;
      }
      
      .stats-content {
        transition: opacity 0.3s ease-in-out;
      }
      
      .stats-content.hidden {
        opacity: 0;
      }
      
      /* Enhanced drag handle styles */
      .stats-drag-handle {
        position: relative;
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);
      }
      
      /* Pulsing effect on the handle to attract attention */
      @keyframes handlePulse {
        0% { background-color: #1f2937; }
        50% { background-color: #2d3748; }
        100% { background-color: #1f2937; }
      }
      
      /* Apply pulsing animation to new users */
      .stats-drag-handle.new-user {
        animation: handlePulse 2s infinite;
      }
      
      /* Enhanced visual feedback on hover */
      .stats-drag-handle:hover .drag-pill {
        width: 40px;
        background-color: #3b82f6;
      }
      
      .stats-drag-handle:active .drag-pill {
        width: 50px;
        background-color: #2563eb;
      }
      
      /* Drag pill transition */
      .drag-pill {
        transition: width 0.2s ease, background-color 0.2s ease;
      }
      
      /* Drag arrows indicator */
      .stats-drag-handle:before,
      .stats-drag-handle:after {
        content: '';
        position: absolute;
        width: 30px;
        height: 2px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 1px;
      }
      
      .stats-drag-handle:before {
        top: 6px;
        left: 10px;
        transform: rotate(45deg);
      }
      
      .stats-drag-handle:after {
        top: 6px;
        right: 10px;
        transform: rotate(-45deg);
      }
      
      /* When minimized, flip the arrows */
      .stats-container.minimized .stats-drag-handle:before {
        transform: rotate(-45deg);
      }
      
      .stats-container.minimized .stats-drag-handle:after {
        transform: rotate(45deg);
      }
      
      /* Tooltip indicator for new users */
      .stats-drag-handle .tooltip {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background-color: #2563eb;
        color: white;
        padding: 4px 10px;
        border-radius: 4px;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        white-space: nowrap;
      }
      
      .stats-drag-handle .tooltip:after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border-width: 5px;
        border-style: solid;
        border-color: #2563eb transparent transparent transparent;
      }
      
      .stats-drag-handle:hover .tooltip {
        opacity: 1;
      }
      
      /* Stats minimized view enhanced */
      .stats-minimized-view {
        transition: opacity 0.3s ease-in-out;
      }
      
      /* First-time user instruction */
      .first-time-instruction {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #2563eb;
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        z-index: 9999;
        font-size: 14px;
        display: flex;
        align-items: center;
        animation: bounce 2s infinite;
      }
      
      @keyframes bounce {
        0%, 100% { transform: translateY(0) translateX(-50%); }
        50% { transform: translateY(-10px) translateX(-50%); }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div className={`flex-none bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 text-white border-t border-b border-gray-700 shadow-lg stats-container ${isMinimized ? 'minimized' : 'expanded'}`}>
      {/* Minimized view - only shown when minimized */}
      {isMinimized && (
        <div className="flex justify-between items-center p-2 stats-minimized-view">
          <div className="flex items-center">
            <h2 className="text-sm font-semibold text-gray-300 mr-3">Stats</h2>
            <div className="text-sm bg-gradient-to-r from-purple-800 to-purple-600 text-white px-2 py-1 rounded-lg shadow-md">
              Team: <span className="font-bold">{teamName}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-sm bg-gradient-to-r from-blue-800 to-blue-600 text-white px-2 py-1 rounded-lg">
              <span className="text-xs">Cars Today:</span> <span className="font-bold">{stats.carsFoundToday}</span>
            </div>
            {paySettings.enablePayCalculations && shouldShowBasicPay ? (
              <div className="text-sm bg-gradient-to-r from-green-800 to-green-600 text-white px-2 py-1 rounded-lg">
                <span className="text-xs">Pay Today:</span> <span className="font-bold">{formatCurrency(userDailyPay)}</span>
              </div>
            ) : (
              <div className="text-sm bg-gradient-to-r from-blue-800 to-blue-600 text-white px-2 py-1 rounded-lg">
                <span className="text-xs">Scans Today:</span> <span className="font-bold">{safelyFormatNumber(stats.userScansToday)}</span>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Full content - hidden when minimized */}
      <div className={`stats-content ${isMinimized ? 'hidden' : ''}`}>
        <div className="flex flex-col space-y-3 p-3">
          {/* Stats header with team rank and admin reset button */}
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <h2 className="text-sm font-semibold text-gray-300 mr-3">Stats</h2>
              
              {/* Clickable Team Rank */}
              <div 
                onClick={handleShowTeamRanking}
                className="text-sm bg-gradient-to-r from-purple-800 to-purple-600 text-white px-3 py-1 rounded-lg shadow-md cursor-pointer hover:from-purple-700 hover:to-purple-500 transition-all duration-200"
              >
                Team Rank: <span className="font-bold">{currentTeamRank}</span> of {totalTeamsCount}
                <span className="text-xs ml-1 text-purple-300">(Click for details)</span>
              </div>
            </div>
            
            {/* Admin Reset Button - Only visible to admins */}
            {isUserAdmin && (
              <button
                onClick={() => setShowResetConfirmation(true)}
                className="bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 text-white px-3 py-1 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center text-sm"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
                Reset Stats
              </button>
            )}
          </div>
          
          {/* Enhanced Team Box - Combines team name, size, and role information */}
          <div className="bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-3 border border-blue-700 shadow-md">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                </svg>
                <div>
                  <div className="text-xs text-blue-300 uppercase font-semibold">Current Team</div>
                  <div className="text-xl font-bold text-white">{teamName}</div>
                  
                  {/* User Role - Added here */}
                  <div className="flex items-center mt-1">
                    {userRoleFromTags === 'tow' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                        <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-5h6a1 1 0 001-1v-2a1 1 0 00-.55-.9L14.2 4.5A4 4 0 0010.4 2H4a1 1 0 00-1 1v1zm1 7a1 1 0 011-1h11a1 1 0 110 2H5a1 1 0 01-1-1z" />
                      </svg>
                    ) : userRoleFromTags === 'admin' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                      </svg>
                    )}
                    <span className="text-xs text-blue-300">Your Role:</span>
                    <span className="ml-1 text-xs font-medium text-white">{getRoleDisplayText()}</span>
                    
                    {/* User Tags */}
                    <div className="flex ml-2 flex-wrap gap-1">
                      {userRoleFromTags === 'admin' && (
                        <span className="bg-purple-700 text-purple-100 text-xs px-1.5 py-0.5 rounded-full">
                          Admin
                        </span>
                      )}
                      {userRoleFromTags === 'tow' && (
                        <span className="bg-yellow-700 text-yellow-100 text-xs px-1.5 py-0.5 rounded-full">
                          Tow
                        </span>
                      )}
                      {userRoleFromTags === 'user' && (
                        <span className="bg-blue-700 text-blue-100 text-xs px-1.5 py-0.5 rounded-full">
                          Spotter
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Team Composition - Right side */}
              <div className="bg-blue-800 bg-opacity-50 px-3 py-1 rounded-lg">
                <div className="text-xs text-blue-300">TEAM COMPOSITION</div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-xs text-blue-200">{totalSpotters}</span>
                  </div>
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                      <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-5h6a1 1 0 001-1v-2a1 1 0 00-.55-.9L14.2 4.5A4 4 0 0010.4 2H4a1 1 0 00-1 1v1zm1 7a1 1 0 011-1h11a1 1 0 110 2H5a1 1 0 01-1-1z" />
                    </svg>
                    <span className="text-xs text-yellow-200">{totalTowDrivers}</span>
                  </div>
                </div>
                <div className="text-sm font-bold text-white text-center">{totalTeamMembers} Members</div>
              </div>
            </div>
          </div>
          
          {/* Pay stats section - Conditionally rendered based on enablePayCalculations setting */}
          {paySettings.enablePayCalculations ? (
            <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg p-3 shadow-inner border border-gray-600">
              <h3 className="text-sm font-semibold text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-green-400 mb-2">
                Pay Stats
              </h3>
              
              <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {/* Current Pay - Only show if basic pay is enabled */}
                {shouldShowBasicPay && (
                  <div 
                    className="bg-gray-800 bg-opacity-50 rounded-lg p-2 border border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer hover:bg-gray-700"
                    onClick={() => handleStatClick('userPay')}
                  >
                    <div className="text-xs text-gray-400 uppercase">Your Pay</div>
                    <div className="font-bold text-green-400 text-sm">
                      {formatCurrency(userDailyPay)} / {formatCurrency(userWeeklyPay)} / {formatCurrency(userMonthlyPay)}
                    </div>
                    <div className="text-xs text-gray-500">Today / Week / Month</div>
                    <div className="text-xs text-gray-400 mt-1">YTD: <span className="text-green-300">{formatCurrency(userYearlyPay)}</span></div>
                    {shouldShowScanBonus && teamScanBonus > 0 && (
                      <div className="text-xs text-yellow-300 mt-1">+ {formatCurrency(teamScanBonus)} Team Scan Bonus!</div>
                    )}
                    <div className="text-xs text-blue-300 mt-1 text-right">Click for details</div>
                  </div>
                )}
                
                {/* Team Pay - Only show if basic pay is enabled */}
                {shouldShowBasicPay && (
                  <div 
                    className="bg-gray-800 bg-opacity-50 rounded-lg p-2 border border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer hover:bg-gray-700"
                    onClick={() => handleStatClick('teamPay')}
                  >
                    <div className="text-xs text-gray-400 uppercase">Team Pay</div>
                    <div className="font-bold text-green-400 text-sm">
                      {formatCurrency(teamDailyPay)} / {formatCurrency(teamWeeklyPay)} / {formatCurrency(teamMonthlyPay)}
                    </div>
                    <div className="text-xs text-gray-500">Today / Week / Month</div>
                    <div className="text-xs text-gray-400 mt-1">YTD: <span className="text-green-300">{formatCurrency(teamYearlyPay)}</span></div>
                    <div className="text-xs text-blue-300 mt-1 text-right">Click for details</div>
                  </div>
                )}
                
                {/* Pay Structure - Only show if basic pay is enabled */}
                {shouldShowBasicPay && (
                  <div 
                    className="bg-gray-800 bg-opacity-50 rounded-lg p-2 border border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer hover:bg-gray-700"
                    onClick={() => handleStatClick('payStructure')}
                  >
                    <div className="text-xs text-gray-400 uppercase">Pay Structure</div>
                    <div className="grid grid-cols-2 gap-1 mt-1">
                      <div className="text-xs text-gray-400">Hourly Rate:</div>
                      <div className="text-xs text-green-300 font-medium">{formatCurrency(effectiveHourlyRate)}/hr</div>
                      
                      <div className="text-xs text-gray-400">Recovery Bonus:</div>
                      <div className="text-xs text-green-300 font-medium">{formatCurrency(effectiveBonusPerCarRecovered)}/car</div>
                      
                      <div className="text-xs text-gray-400">Position:</div>
                      <div className="text-xs text-green-300 font-medium">{getRoleDisplayText()}</div>
                    </div>
                    <div className="text-xs text-blue-300 mt-1 text-right">Click for details</div>
                  </div>
                )}
                
                {/* Contribution */}
                <div 
                  className="bg-gray-800 bg-opacity-50 rounded-lg p-2 border border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer hover:bg-gray-700"
                  onClick={() => handleStatClick('contribution')}
                >
                  <div className="text-xs text-gray-400 uppercase">Your Contribution</div>
                  <div className="mt-2 relative pt-1">
                    <div className="flex mb-2 items-center justify-between">
                      <div>
                        <span className="text-xs font-semibold inline-block text-green-300">
                          {userContribution}% of Team
                        </span>
                      </div>
                      <div className="text-right">
                        <span className="text-xs font-semibold inline-block text-green-300">
                          {stats.userOpenOrdersToday}/{stats.teamOpenOrdersToday} orders
                        </span>
                      </div>
                    </div>
                    <div className="overflow-hidden h-2 mb-1 text-xs flex rounded bg-gray-700">
                      <div 
                        style={{ width: `${userContribution}%` }} 
                        className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-green-500 to-green-600"
                      ></div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-400 mt-1">Hours Today: <span className="text-green-300">{hoursWorkedToday}hrs</span></div>
                  <div className="text-xs text-blue-300 mt-1 text-right">Click for details</div>
                </div>
                
                {/* Show scan bonus card if scan bonus is enabled but basic pay is disabled */}
                {!shouldShowBasicPay && shouldShowScanBonus && (
                  <div 
                    className="bg-gray-800 bg-opacity-50 rounded-lg p-2 border border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer hover:bg-gray-700"
                    onClick={() => handleStatClick('scanBonus')}
                  >
                    <div className="text-xs text-gray-400 uppercase">Scan Bonus</div>
                    <div className="font-bold text-amber-400 text-sm">
                      {formatCurrency(teamScanBonus)}
                    </div>
                    <div className="text-xs text-gray-500">Weekly Bonus - Tier {scanTierInfo.tier}</div>
                    <div className="mt-1 relative pt-1">
                      <div className="overflow-hidden h-2 mb-1 text-xs flex rounded bg-gray-700">
                        <div 
                          style={{ width: `${scanTierInfo.progress}%` }} 
                          className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-amber-500 to-amber-600"
                        ></div>
                      </div>
                    </div>
                    <div className="text-xs text-blue-300 mt-1 text-right">Click for details</div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg p-3 shadow-inner border border-gray-600">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-semibold text-transparent bg-clip-text bg-gradient-to-r from-gray-300 to-gray-400 mb-2">
                  Stats Summary
                </h3>
                {isUserAdmin && (
                  <div className="text-xs text-blue-400 bg-blue-900 bg-opacity-50 px-2 py-1 rounded-lg">
                    <a href="/settings" className="hover:underline flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                      Settings
                    </a>
                  </div>
                )}
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <div className="bg-gray-800 bg-opacity-50 rounded-lg p-2 border border-gray-700 shadow-md">
                  <div className="text-xs text-gray-400 uppercase">Cars Found Today</div>
                  <div className="font-bold text-blue-400 text-xl">{stats.carsFoundToday}</div>
                  <div className="text-xs text-gray-400 mt-1">Weekly: {stats.carsFoundWeek}</div>
                </div>
                
                <div className="bg-gray-800 bg-opacity-50 rounded-lg p-2 border border-gray-700 shadow-md">
                  <div className="text-xs text-gray-400 uppercase">Cars Recovered</div>
                  <div className="font-bold text-green-400 text-xl">{stats.carsRecoveredToday}</div>
                  <div className="text-xs text-gray-400 mt-1">Weekly: {stats.carsRecoveredWeek}</div>
                </div>
                
                <div className="bg-gray-800 bg-opacity-50 rounded-lg p-2 border border-gray-700 shadow-md">
                  <div className="text-xs text-gray-400 uppercase">Your Scans</div>
                  <div className="font-bold text-amber-400 text-xl">{safelyFormatNumber(stats.userScansToday)}</div>
                  <div className="text-xs text-gray-400 mt-1">Weekly: {safelyFormatNumber(stats.userScansWeek)}</div>
                </div>
                
                <div className="bg-gray-800 bg-opacity-50 rounded-lg p-2 border border-gray-700 shadow-md">
                  <div className="text-xs text-gray-400 uppercase">Hours Worked</div>
                  <div className="font-bold text-yellow-400 text-xl">{hoursWorkedToday}hrs</div>
                  <div className="text-xs text-gray-400 mt-1">Weekly: {stats.userHoursWorkedWeek}hrs</div>
                </div>
              </div>
            </div>
          )}
          
          {/* Grid layout for stats with miles moved to the end and navigation box in the middle */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 lg:grid-cols-12 gap-4 p-2">
            {/* Left section stats */}
            {/* Area Orders */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('areaOrders')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
              </svg>
              <div className="text-xs text-gray-400">Area Orders</div>
              <div className="font-bold text-orange-400 text-sm">0 / 0 / 0</div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: 0</div>
            </div>
            
            {/* Regional Orders */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('regionalOrders')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5z" />
                <path d="M11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
              <div className="text-xs text-gray-400">Regional Orders</div>
              <div className="font-bold text-orange-400 text-sm">0 / 0 / 0</div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: 0</div>
            </div>
            
            {/* Your Hours */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('userHours')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              <div className="text-xs text-gray-400">Your Hours</div>
              <div className="font-bold text-yellow-400 text-sm">0 / 0 / 0</div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: 0</div>
            </div>
            
            {/* Team Hours */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('teamHours')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              <div className="text-xs text-gray-400">Team Hours</div>
              <div className="font-bold text-yellow-400 text-sm">0 / 0 / 0</div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: 0</div>
            </div>
            
            {/* Center navigation guidance box - spans 4 columns on larger screens */}
            <div className={`text-center p-2 col-span-1 md:col-span-4 lg:col-span-4 bg-gray-800 bg-opacity-75 rounded-lg shadow-lg border border-blue-900 ${isNavigating ? 'block' : 'hidden'}`}>
              <div className="flex flex-col h-full">
                <div className="text-xs text-blue-300 uppercase font-semibold mb-1">Navigation</div>
                
                {/* Direction indicators */}
                <div className="flex justify-center items-center mb-2">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center mx-1 ${navigationDirection === 'left' ? 'bg-blue-500 text-white' : 'bg-gray-700 text-gray-400'}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                  
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center mx-1 ${navigationDirection === 'straight' ? 'bg-blue-500 text-white' : 'bg-gray-700 text-gray-400'}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l4 4a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L6.707 8.707a1 1 0 01-1.414-1.414l4-4A1 1 0 0110 3z" clipRule="evenodd" />
                    </svg>
                  </div>
                  
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center mx-1 ${navigationDirection === 'right' ? 'bg-blue-500 text-white' : 'bg-gray-700 text-gray-400'}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                
                {/* Distance and time */}
                <div className="flex justify-between items-center mb-1">
                  <div className="flex items-center">
                    <span className="text-xs text-gray-400 mr-1">Distance:</span>
                    <span className="text-sm font-bold text-white">{distanceToDestination ? formatDistance(distanceToDestination) : 'N/A'}</span>
                  </div>
                  
                  <div className="flex items-center">
                    <span className="text-xs text-gray-400 mr-1">ETA:</span>
                    <span className="text-sm font-bold text-white">{estimatedTime ? formatTime(estimatedTime) : 'N/A'}</span>
                  </div>
                </div>
                
                {/* Arrival time */}
                <div className="text-xs text-gray-400 mb-1">
                  Arrival: <span className="text-green-400">{arrivalTime ? formatClockTime(arrivalTime) : 'N/A'}</span>
                </div>
                
                {/* Destination */}
                <div className="text-xs text-blue-300 truncate mb-1" title={destinationAddress}>
                  To: {destinationAddress || 'Unknown destination'}
                </div>
                
                {/* Cancel button */}
                <button 
                  onClick={stopNavigation}
                  className="mt-1 bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1 rounded self-center"
                >
                  Cancel Navigation
                </button>
              </div>
            </div>

            {/* Right section stats */}
            {/* Cars Found */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('carsFound')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-5h6a1 1 0 001-1v-2a1 1 0 00-.55-.9L14.2 4.5A4 4 0 0010.4 2H4a1 1 0 00-1 1v1zm1 7a1 1 0 011-1h11a1 1 0 110 2H5a1 1 0 01-1-1z" />
              </svg>
              <div className="text-xs text-gray-400">Cars Found</div>
              <div className="font-bold text-blue-400 text-sm">0 / 0 / 0</div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: 0</div>
            </div>
            
            {/* Cars Recovered - This affects pay calculation */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('carsRecovered')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <div className="text-xs text-gray-400">Cars Recovered</div>
              <div className="font-bold text-green-400 text-sm">0 / 0 / 0</div>
<div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: 0</div>
            </div>
            
            {/* Your Scans */}
<div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('userScans')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zm2 2v-1h1v1H5zM13 3a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1V4a1 1 0 00-1-1h-3zm1 2v1h1V5h-1zM13 12a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1v-3a1 1 0 00-1-1h-3zm1 2v1h1v-1h-1z" clipRule="evenodd" />
              </svg>
              <div className="text-xs text-gray-400">Your Scans</div>
              <div className="font-bold text-amber-400 text-sm">
                {safelyFormatNumber(stats.userScansToday)} / {safelyFormatNumber(stats.userScansWeek)} / {safelyFormatNumber(stats.userScansMonth)}
              </div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: {safelyFormatNumber(stats.userScansYTD)}</div>
            </div>
            
            {/* Team Scans - Enhanced to show bonus if scan bonus is enabled */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer hover:bg-gray-700"
              onClick={() => handleStatClick('teamScans')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zm2 2v-1h1v1H5zM13 3a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1V4a1 1 0 00-1-1h-3zm1 2v1h1V5h-1zM13 12a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1v-3a1 1 0 00-1-1h-3zm1 2v1h1v-1h-1z" clipRule="evenodd" />
              </svg>
              <div className="text-xs text-gray-400">Team Scans</div>
              <div className="font-bold text-amber-400 text-sm">
                {safelyFormatNumber(stats.teamScansToday)} / {safelyFormatNumber(stats.teamScansWeek)} / {safelyFormatNumber(stats.teamScansMonth)}
              </div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: {safelyFormatNumber(stats.teamScansYTD)}</div>
              {shouldShowScanBonus && teamScanBonus > 0 && (
                <div className="text-xs text-yellow-300 animate-pulse">+{formatCurrency(teamScanBonus)} Tier {scanTierInfo.tier} Bonus!</div>
              )}
            </div>
            
            {/* Your Markers */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('userMarkers')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-pink-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
              <div className="text-xs text-gray-400">Your Markers</div>
              <div className="font-bold text-pink-400 text-sm">0 / 0 / 0</div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: 0</div>
            </div>
            
            {/* Team Markers */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('teamMarkers')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-pink-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
              <div className="text-xs text-gray-400">Team Markers</div>
              <div className="font-bold text-pink-400 text-sm">0 / 0 / 0</div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: 0</div>
            </div>
            
            {/* Your Miles - Moved to the end */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('userMiles')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-indigo-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              <div className="text-xs text-gray-400">Your Miles</div>
              <div className="font-bold text-indigo-400 text-sm">0 / 0 / 0</div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: 0</div>
            </div>
            
            {/* Team Miles - Moved to the end */}
            <div 
              className="text-center p-2 bg-gray-800 bg-opacity-40 rounded-lg transform hover:scale-105 transition-transform duration-200 shadow-md cursor-pointer"
              onClick={() => handleStatClick('teamMiles')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto mb-1 text-indigo-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              <div className="text-xs text-gray-400">Team Miles</div>
              <div className="font-bold text-indigo-400 text-sm">0 / 0 / 0</div>
              <div className="text-xs text-gray-500">Today / Week / Month</div>
              <div className="text-xs text-indigo-400">YTD: 0</div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Drag handle at the bottom - with better visual cues and descriptive text */}
      <div 
        className={`w-full bg-gray-800 hover:bg-gray-700 py-2 cursor-grab active:cursor-grabbing border-t border-gray-600 rounded-b-lg transition-colors duration-200 stats-drag-handle ${isDragging ? 'bg-gray-700' : ''} ${isNewUser ? 'new-user' : ''}`}
        onClick={toggleMinimized}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        onMouseUp={handleDragEnd}
        onTouchEnd={handleDragEnd}
      >
        {/* First-time user tooltip - shown conditionally */}
        {isNewUser && <div className="tooltip">Drag to resize stats panel</div>}
        
        {/* Visual drag indicators */}
        <div className="flex flex-col items-center">
          {/* Drag pill - becomes blue on hover */}
          <div className="w-16 h-2 bg-gray-500 rounded-full mb-1.5 drag-pill"></div>
          
          {/* Status text */}
          <div className="flex items-center text-xs text-gray-400 hover:text-blue-300 transition-colors duration-200">
            <span>{isMinimized ? "Expand Stats Panel" : "Collapse Stats Panel"}</span>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-4 w-4 ml-1 transition-transform duration-300 ${isMinimized ? 'rotate-180' : ''}`} 
              viewBox="0 0 20 20" 
              fill="currentColor"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </div>

      {/* First-time user instruction - shown conditionally */}
      {isNewUser && (
        <div className="first-time-instruction">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          <span>Drag the gray bar to resize the stats panel</span>
        </div>
      )}

      {/* Reset Stats Confirmation Modal */}
      {showResetConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[9999]">
          <div className="bg-gray-800 p-6 rounded-lg shadow-xl border border-gray-700 max-w-md w-full mx-4">
            <h3 className="text-xl font-bold text-red-400 mb-4">Reset All Stats</h3>
            <p className="text-white mb-6">
              Are you sure you want to reset all stats to zero? This action cannot be undone and will affect all team members.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-end">
              <button
                onClick={() => setShowResetConfirmation(false)}
                className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg shadow-md"
              >
                Cancel
              </button>
              <button
                onClick={handleResetConfirm}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg shadow-md"
              >
                Yes, Reset All Stats
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Team Rankings Popup */}
      {showTeamRanking && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[9999]">
          <div className="bg-gray-800 p-6 rounded-lg shadow-xl border border-gray-700 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-blue-400">Team Rankings</h3>
              <button
                onClick={() => setShowTeamRanking(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="mb-6">
              {allTeams.length > 0 ? (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    {/* Team ranks 1-3 with special styling */}
                    {allTeams.slice(0, Math.min(3, allTeams.length)).map((team) => (
                      <div 
                        key={team.id}
                        className={`${getRankStyle(team.rank)} p-4 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-300`}
                      >
                        <div className="flex items-center mb-2">
                          {team.rank === 1 && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-2 text-yellow-900" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                            </svg>
                          )}
                          {team.rank === 2 && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-2 text-gray-800" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                            </svg>
                          )}
                          {team.rank === 3 && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-2 text-orange-900" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                            </svg>
                          )}
                          <h4 className="text-lg font-bold">
                            #{team.rank}: {team.name}
                          </h4>
                        </div>
                        <div className="grid grid-cols-2 gap-2 mt-2">
                          <div className="text-sm">
                            <span className="block font-semibold">Score:</span>
                            <span className="text-lg font-bold">{team.score || 0}</span>
                          </div>
                          <div className="text-sm">
                            <span className="block font-semibold">Members:</span>
                            <span>{team.members || 0}</span>
                          </div>
                          <div className="text-sm">
                            <span className="block font-semibold">Cars Found:</span>
                            <span>{team.carsFound || 0}</span>
                          </div>
                          <div className="text-sm">
                            <span className="block font-semibold">Scans:</span>
                            <span>{safelyFormatNumber(team.scans || 0)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Table for the rest of the teams */}
                  {allTeams.length > 3 && (
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-gray-900 rounded-lg overflow-hidden">
                        <thead className="bg-gray-700">
                          <tr>
                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Rank</th>
                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Team Name</th>
                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Score</th>
                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Members</th>
                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Cars Found</th>
                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Scans</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-800">
                          {allTeams.slice(3).map((team) => (
                            <tr key={team.id} className="hover:bg-gray-800">
                              <td className="px-3 py-2 whitespace-nowrap text-sm text-white">#{team.rank}</td>
                              <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-white">{team.name}</td>
                              <td className="px-3 py-2 whitespace-nowrap text-sm text-white">{team.score || 0}</td>
                              <td className="px-3 py-2 whitespace-nowrap text-sm text-white">{team.members || 0}</td>
                              <td className="px-3 py-2 whitespace-nowrap text-sm text-white">{team.carsFound || 0}</td>
                              <td className="px-3 py-2 whitespace-nowrap text-sm text-white">{safelyFormatNumber(team.scans || 0)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-gray-700 p-6 rounded-lg text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h4 className="text-lg font-semibold text-white mb-2">No Teams Found</h4>
                  <p className="text-gray-300">No teams have been created yet. Teams will appear here once they are set up.</p>
                </div>
              )}
            </div>
            
            {/* Team metrics & explanation */}
            <div className="mt-4 bg-gray-700 p-4 rounded-lg">
              <h4 className="text-md font-semibold text-blue-300 mb-2">How Teams Are Ranked</h4>
              <p className="text-sm text-gray-200 mb-3">
                Team rankings are calculated based on a combination of factors including cars found, recovery rate, scan volume, and total markers created by team members.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h5 className="text-sm font-semibold text-blue-300 mb-1">Cars Found</h5>
                  <p className="text-xs text-gray-300">Each found car adds 10 points to team score</p>
                </div>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h5 className="text-sm font-semibold text-blue-300 mb-1">Recovery Rate</h5>
                  <p className="text-xs text-gray-300">Teams with higher recovery rates get bonus points</p>
                </div>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h5 className="text-sm font-semibold text-blue-300 mb-1">Scan Volume</h5>
                  <p className="text-xs text-gray-300">Every 1,000 scans adds 1 point to team score</p>
                </div>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h5 className="text-sm font-semibold text-blue-300 mb-1">Team Efficiency</h5>
                  <p className="text-xs text-gray-300">Teams with higher cars per hour rate get bonus points</p>
                </div>
              </div>
            </div>
                
            <div className="flex justify-end mt-6">
              <button
                onClick={() => setShowTeamRanking(false)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-md"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Stat Detail Popup */}
      {showStatDetail && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[9999]">
          <div className="bg-gray-800 p-6 rounded-lg shadow-xl border border-gray-700 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-blue-400">
                {selectedStat === 'userPay' && 'Your Pay Details'}
                {selectedStat === 'teamPay' && 'Team Pay Details'}
                {selectedStat === 'payStructure' && 'Pay Structure Details'}
                {selectedStat === 'scanBonus' && 'Scan Bonus Details'}
                {selectedStat === 'contribution' && 'Your Contribution Details'}
                {selectedStat === 'areaOrders' && 'Area Orders Details'}
                {selectedStat === 'regionalOrders' && 'Regional Orders Details'}
                {selectedStat === 'userHours' && 'Your Hours Details'}
                {selectedStat === 'teamHours' && 'Team Hours Details'}
                {selectedStat === 'carsFound' && 'Cars Found Details'}
                {selectedStat === 'carsRecovered' && 'Cars Recovered Details'}
                {selectedStat === 'userScans' && 'Your Scans Details'}
                {selectedStat === 'teamScans' && 'Team Scans Details'}
                {selectedStat === 'userMarkers' && 'Your Markers Details'}
                {selectedStat === 'teamMarkers' && 'Team Markers Details'}
                {selectedStat === 'userMiles' && 'Your Miles Details'}
                {selectedStat === 'teamMiles' && 'Team Miles Details'}
              </h3>
              <button
                onClick={() => setShowStatDetail(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="mt-4">
              {/* Pay Details - Respect pay calculation toggles */}
              {selectedStat === 'userPay' && shouldShowBasicPay && (
                <div className="space-y-4">
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h4 className="text-lg font-semibold text-green-300 mb-2">Pay Calculation Breakdown</h4>
                    <div className="grid grid-cols-2 gap-y-2">
                      <div className="text-gray-300">Base Hourly Pay:</div>
                      <div className="text-white font-medium">{formatCurrency(effectiveHourlyRate)} × {hoursWorkedToday} hrs = {formatCurrency(effectiveHourlyRate * hoursWorkedToday)}</div>
                      
                      <div className="text-gray-300">Team Bonus Pool Share:</div>
                      <div className="text-white font-medium">{formatCurrency(effectiveBonusPerCarRecovered)} × {stats.carsRecoveredToday} cars = {formatCurrency(effectiveBonusPerCarRecovered * stats.carsRecoveredToday)}</div>
                      
                      {shouldShowScanBonus && (
                        <>
                          <div className="text-gray-300">Team Scan Bonus (Weekly):</div>
                          <div className="text-white font-medium">{formatCurrency(teamScanBonus)}</div>
                        </>
                      )}
                      
                      <div className="text-gray-300 font-semibold">Total Daily Pay:</div>
                      <div className="text-green-300 font-bold">{formatCurrency(userDailyPay)}</div>
                      
                      <div className="text-gray-300 font-semibold">Total Weekly Pay:</div>
                      <div className="text-green-300 font-bold">{formatCurrency(userWeeklyPay)}</div>
                    </div>
                  </div>
                  
                  {/* Updated Pay Structure Explanation */}
                  <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg border border-blue-700">
                    <h4 className="text-lg font-semibold text-blue-300 mb-2">Pay Structure</h4>
                    <p className="text-white text-sm mb-3">
                      Our pay structure provides fair compensation to all team members:
                    </p>
                    <ul className="list-disc pl-5 text-sm text-white space-y-2">
                      <li><span className="font-bold text-yellow-300">Base Hourly Rate:</span> All team members receive {formatCurrency(effectiveHourlyRate)}/hour base pay.</li>
                      <li><span className="font-bold text-yellow-300">Team Bonus Pool:</span> Every car fully recovered adds {formatCurrency(effectiveBonusPerCarRecovered)} to the team bonus pool, which is distributed among team members.</li>
                      {shouldShowScanBonus && (
                        <li><span className="font-bold text-yellow-300">Team Scan Bonuses:</span> Additional weekly bonuses are available based on team scan volume thresholds scaled to team size.</li>
                      )}
                    </ul>
                  </div>
                  
                  {/* Important Pay Note */}
                  <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg border border-blue-700">
                    <h4 className="text-lg font-semibold text-blue-300 mb-2">Important Payment Notes</h4>
                    <p className="text-white text-sm">
                      <span className="font-bold text-yellow-300">Note:</span> Pay bonuses for vehicles are only granted when a vehicle is marked as "Recovered," not when initially found. Finding a vehicle marks it in the system, but recovery status is what triggers the bonus payment.
                    </p>
                  </div>
                  
                  {/* Scan Bonus Explanation - Only shown if scan bonus is enabled */}
                  {shouldShowScanBonus && (
                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h4 className="text-lg font-semibold text-blue-300 mb-2">Team Scan Bonus Program</h4>
                      <div className="grid grid-cols-1 gap-y-2">
                        <p className="text-white text-sm">Your team can earn additional bonuses by reaching scan targets. These targets scale with team size (<span className="text-blue-300">Currently {totalTeamMembers} members</span>):</p>
                        
                        <div className="flex items-center mt-2">
                          <div className={`h-4 w-4 rounded-full ${scanTierInfo.tier >= 1 ? 'bg-yellow-500' : 'bg-gray-600'} mr-2`}></div>
                          <div className="text-sm">
                            <span className="text-gray-300">Tier 1: </span>
                            <span className="text-white font-medium">{formatCurrency(scanBonusTier1)}</span>
                            <span className="text-gray-300"> bonus for {safelyFormatNumber(scanBonusTier1Threshold)} scans</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center">
                          <div className={`h-4 w-4 rounded-full ${scanTierInfo.tier >= 2 ? 'bg-yellow-500' : 'bg-gray-600'} mr-2`}></div>
                          <div className="text-sm">
                            <span className="text-gray-300">Tier 2: </span>
                            <span className="text-white font-medium">{formatCurrency(scanBonusTier2)}</span>
                            <span className="text-gray-300"> bonus for {safelyFormatNumber(scanBonusTier2Threshold)} scans</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center">
                          <div className={`h-4 w-4 rounded-full ${scanTierInfo.tier >= 3 ? 'bg-yellow-500' : 'bg-gray-600'} mr-2`}></div>
                          <div className="text-sm">
                            <span className="text-gray-300">Tier 3: </span>
                            <span className="text-white font-medium">{formatCurrency(scanBonusTier3)}</span>
                            <span className="text-gray-300"> bonus for {safelyFormatNumber(scanBonusTier3Threshold)} scans</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center">
                          <div className={`h-4 w-4 rounded-full ${scanTierInfo.tier >= 4 ? 'bg-yellow-500' : 'bg-gray-600'} mr-2`}></div>
                          <div className="text-sm">
                            <span className="text-gray-300">Tier 4: </span>
                            <span className="text-white font-medium">{formatCurrency(scanBonusTier4)}</span>
                            <span className="text-gray-300"> bonus for {safelyFormatNumber(scanBonusTier4Threshold)} scans</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center">
                          <div className={`h-4 w-4 rounded-full ${scanTierInfo.tier >= 5 ? 'bg-yellow-500' : 'bg-gray-600'} mr-2`}></div>
                          <div className="text-sm">
                            <span className="text-gray-300">Tier 5: </span>
                            <span className="text-white font-medium">{formatCurrency(scanBonusTier5)}</span>
                            <span className="text-gray-300"> bonus for {safelyFormatNumber(scanBonusTier5Threshold)} scans</span>
                          </div>
                        </div>
                        
                        <div className="mt-3 relative pt-1">
                          <div className="text-xs text-gray-400 mb-1">Team progress toward scan bonus:</div>
                          <div className="flex mb-2 items-center justify-between">
                            <div>
                              <span className="text-xs font-semibold inline-block text-blue-300">
                                {safelyFormatNumber(stats.teamScansWeek)} scans
                              </span>
                            </div>
                            <div className="text-right">
                              <span className="text-xs font-semibold inline-block text-blue-300">
                                {scanTierInfo.progress.toFixed(1)}%
                              </span>
                            </div>
                          </div>
                          <div className="overflow-hidden h-3 mb-1 text-xs flex rounded bg-gray-700 border border-gray-600">
                            <div 
                              style={{ width: `${scanTierInfo.progress}%` }} 
                              className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-blue-500 to-blue-600"
                            ></div>
                          </div>
                          <div className="flex justify-between text-xs text-gray-400 mt-1">
                            <span>0</span>
                            <span>T1: {safelyFormatNumber(scanBonusTier1Threshold)}</span>
                            <span>T5: {safelyFormatNumber(scanBonusTier5Threshold)}</span>
                          </div>
                        </div>
                        
                        <div className="mt-3 p-3 bg-blue-900 bg-opacity-30 rounded-lg">
                          <span className="text-xs text-blue-300">
                            <b>Note on Scan Bonus Structure:</b> Each tier adds ${effectiveScanBonusPerTeamMember} per team member to the bonus pool. With {effectiveTeamSize} team members:
                            <ul className="mt-1 ml-4 list-disc">
                              <li>Tier 1: ${effectiveScanBonusPerTeamMember} × {effectiveTeamSize} = ${scanBonusTier1}</li>
                              <li>Tier 2: ${effectiveScanBonusPerTeamMember} × 2 × {effectiveTeamSize} = ${scanBonusTier2}</li>
                              <li>Tier 3: ${effectiveScanBonusPerTeamMember} × 3 × {effectiveTeamSize} = ${scanBonusTier3}</li>
                              <li>Tier 4: ${effectiveScanBonusPerTeamMember} × 4 × {effectiveTeamSize} = ${scanBonusTier4}</li>
                              <li>Tier 5: ${effectiveScanBonusPerTeamMember} × 5 × {effectiveTeamSize} = ${scanBonusTier5}</li>
                            </ul>
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h4 className="text-md font-semibold text-blue-300 mb-2">Daily Pay</h4>
                      <div className="text-2xl font-bold text-green-400">{formatCurrency(userDailyPay)}</div>
                      <div className="text-xs text-gray-400 mt-1">Based on today's activity</div>
                    </div>
                    
                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h4 className="text-md font-semibold text-blue-300 mb-2">Weekly Pay</h4>
                      <div className="text-2xl font-bold text-green-400">{formatCurrency(userWeeklyPay)}</div>
                      <div className="text-xs text-gray-400 mt-1">Based on this week's activity</div>
                    </div>
                    
                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h4 className="text-md font-semibold text-blue-300 mb-2">Monthly Pay</h4>
                      <div className="text-2xl font-bold text-green-400">{formatCurrency(userMonthlyPay)}</div>
                      <div className="text-xs text-gray-400 mt-1">Based on this month's activity</div>
                    </div>
                    
                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h4 className="text-md font-semibold text-blue-300 mb-2">YTD Pay</h4>
                      <div className="text-2xl font-bold text-green-400">{formatCurrency(userYearlyPay)}</div>
                      <div className="text-xs text-gray-400 mt-1">Based on YTD activity</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Scan Bonus Details (if only scan bonus is enabled but basic pay is disabled) */}
              {selectedStat === 'scanBonus' && shouldShowScanBonus && (
                <div className="space-y-4">
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h4 className="text-lg font-semibold text-blue-300 mb-2">Team Scan Bonus Program</h4>
                    <p className="text-white text-sm mb-4">Your team can earn weekly bonuses by reaching scan thresholds. These bonuses are shared equally among all team members.</p>
                    
                    <div className="grid grid-cols-1 gap-y-2">
                      <div className="flex items-center mt-2">
                        <div className={`h-4 w-4 rounded-full ${scanTierInfo.tier >= 1 ? 'bg-yellow-500' : 'bg-gray-600'} mr-2`}></div>
                        <div className="text-sm">
                          <span className="text-gray-300">Tier 1: </span>
                          <span className="text-white font-medium">{formatCurrency(scanBonusTier1)}</span>
                          <span className="text-gray-300"> bonus for {safelyFormatNumber(scanBonusTier1Threshold)} scans</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center">
                        <div className={`h-4 w-4 rounded-full ${scanTierInfo.tier >= 2 ? 'bg-yellow-500' : 'bg-gray-600'} mr-2`}></div>
                        <div className="text-sm">
                          <span className="text-gray-300">Tier 2: </span>
                          <span className="text-white font-medium">{formatCurrency(scanBonusTier2)}</span>
                          <span className="text-gray-300"> bonus for {safelyFormatNumber(scanBonusTier2Threshold)} scans</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center">
                        <div className={`h-4 w-4 rounded-full ${scanTierInfo.tier >= 3 ? 'bg-yellow-500' : 'bg-gray-600'} mr-2`}></div>
                        <div className="text-sm">
                          <span className="text-gray-300">Tier 3: </span>
                          <span className="text-white font-medium">{formatCurrency(scanBonusTier3)}</span>
                          <span className="text-gray-300"> bonus for {safelyFormatNumber(scanBonusTier3Threshold)} scans</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center">
                        <div className={`h-4 w-4 rounded-full ${scanTierInfo.tier >= 4 ? 'bg-yellow-500' : 'bg-gray-600'} mr-2`}></div>
                        <div className="text-sm">
                          <span className="text-gray-300">Tier 4: </span>
                          <span className="text-white font-medium">{formatCurrency(scanBonusTier4)}</span>
                          <span className="text-gray-300"> bonus for {safelyFormatNumber(scanBonusTier4Threshold)} scans</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center">
                        <div className={`h-4 w-4 rounded-full ${scanTierInfo.tier >= 5 ? 'bg-yellow-500' : 'bg-gray-600'} mr-2`}></div>
                        <div className="text-sm">
                          <span className="text-gray-300">Tier 5: </span>
                          <span className="text-white font-medium">{formatCurrency(scanBonusTier5)}</span>
                          <span className="text-gray-300"> bonus for {safelyFormatNumber(scanBonusTier5Threshold)} scans</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-6 p-4 bg-blue-900 bg-opacity-20 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Current Progress</h5>
                      <div className="flex justify-between mb-2">
                        <div className="text-gray-300">Current Tier:</div>
                        <div className="text-white font-medium">Tier {scanTierInfo.tier}</div>
                      </div>
                      <div className="flex justify-between mb-2">
                        <div className="text-gray-300">Current Bonus:</div>
                        <div className="text-yellow-300 font-bold">{formatCurrency(scanTierInfo.amount)}</div>
                      </div>
                      <div className="flex justify-between mb-2">
                        <div className="text-gray-300">Team Scans This Week:</div>
                        <div className="text-white font-medium">{safelyFormatNumber(stats.teamScansWeek)}</div>
                      </div>
                      
                      <div className="mt-3 relative pt-1">
                        <div className="text-xs text-gray-400 mb-1">Progress to next tier:</div>
                        <div className="flex mb-2 items-center justify-between">
                          <div>
                            <span className="text-xs text-blue-300">
                              Current: {safelyFormatNumber(stats.teamScansWeek)}
                            </span>
                          </div>
                          <div className="text-right">
                            <span className="text-xs text-blue-300">
                              {scanTierInfo.tier < 5 ? 
                                `Next: ${scanTierInfo.tier === 0 ? safelyFormatNumber(scanBonusTier1Threshold) : 
                                  scanTierInfo.tier === 1 ? safelyFormatNumber(scanBonusTier2Threshold) : 
                                  scanTierInfo.tier === 2 ? safelyFormatNumber(scanBonusTier3Threshold) : 
                                  scanTierInfo.tier === 3 ? safelyFormatNumber(scanBonusTier4Threshold) : 
                                  safelyFormatNumber(scanBonusTier5Threshold)}` : 
                                'Maximum Tier Reached!'}
                            </span>
                          </div>
                        </div>
                        <div className="overflow-hidden h-3 mb-1 text-xs flex rounded bg-gray-700 border border-gray-600">
                          <div 
                            style={{ width: `${scanTierInfo.progress}%` }} 
                            className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-amber-500 to-amber-600"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Team Pay Details - Only show if basic pay is enabled */}
              {selectedStat === 'teamPay' && shouldShowBasicPay && (
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h4 className="text-lg font-semibold text-blue-300 mb-4">Team Pay Breakdown</h4>
                  
                  <div className="space-y-4">
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Daily Pay Calculation</h5>
                      <div className="grid grid-cols-2 gap-y-2">
                        <div className="text-gray-300">Team Hours:</div>
                        <div className="text-white font-medium">{stats.teamHoursWorkedToday} hrs × {formatCurrency(effectiveHourlyRate)} = {formatCurrency(stats.teamHoursWorkedToday * effectiveHourlyRate)}</div>
                        
                        <div className="text-gray-300">Recovery Bonus:</div>
                        <div className="text-white font-medium">{stats.carsRecoveredToday} cars × {formatCurrency(effectiveBonusPerCarRecovered)} = {formatCurrency(stats.carsRecoveredToday * effectiveBonusPerCarRecovered)}</div>
                        
                        <div className="text-gray-300 font-semibold">Total Team Pay Today:</div>
                        <div className="text-green-300 font-bold">{formatCurrency(teamDailyPay)}</div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Weekly Pay Summary</h5>
                      <div className="grid grid-cols-2 gap-y-2">
                        <div className="text-gray-300">Team Hours:</div>
                        <div className="text-white font-medium">{stats.teamHoursWorkedWeek} hrs × {formatCurrency(effectiveHourlyRate)} = {formatCurrency(stats.teamHoursWorkedWeek * effectiveHourlyRate)}</div>
                        
                        <div className="text-gray-300">Recovery Bonus:</div>
                        <div className="text-white font-medium">{stats.carsRecoveredWeek} cars × {formatCurrency(effectiveBonusPerCarRecovered)} = {formatCurrency(stats.carsRecoveredWeek * effectiveBonusPerCarRecovered)}</div>
                        
                        {shouldShowScanBonus && (
                          <>
                            <div className="text-gray-300">Team Scan Bonus:</div>
                            <div className="text-white font-medium">{formatCurrency(teamScanBonus)}</div>
                          </>
                        )}
                        
                        <div className="text-gray-300 font-semibold">Total Team Pay This Week:</div>
                        <div className="text-green-300 font-bold">{formatCurrency(teamWeeklyPay)}</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mt-6">
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Daily</h5>
                      <div className="text-3xl font-bold text-green-400">{formatCurrency(teamDailyPay)}</div>
                      <div className="mt-2 text-xs text-gray-400">Today's team earning</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Weekly</h5>
                      <div className="text-3xl font-bold text-green-400">{formatCurrency(teamWeeklyPay)}</div>
                      <div className="mt-2 text-xs text-gray-400">Week's team earning</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Monthly</h5>
                      <div className="text-3xl font-bold text-green-400">{formatCurrency(teamMonthlyPay)}</div>
                      <div className="mt-2 text-xs text-gray-400">Month's team earning</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Year to Date</h5>
                      <div className="text-3xl font-bold text-green-400">{formatCurrency(teamYearlyPay)}</div>
                      <div className="mt-2 text-xs text-gray-400">YTD team earning</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Pay Structure Details - Only show if basic pay is enabled */}
              {selectedStat === 'payStructure' && shouldShowBasicPay && (
                <div className="space-y-4">
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h4 className="text-lg font-semibold text-green-300 mb-2">Pay Components</h4>
                    <div className="grid grid-cols-2 gap-y-3">
                      <div className="text-gray-300 font-semibold">Hourly Base Rate:</div>
                      <div className="text-white font-medium">{formatCurrency(effectiveHourlyRate)}/hour</div>
                      
                      <div className="text-gray-300 font-semibold">Vehicle Recovery Bonus:</div>
                      <div className="text-white font-medium">{formatCurrency(effectiveBonusPerCarRecovered)} per recovered vehicle</div>
                      
                      {shouldShowScanBonus && (
                        <>
                          <div className="text-gray-300 font-semibold">Team Scan Bonus:</div>
                          <div className="text-white font-medium">Up to {formatCurrency(scanBonusTier5)} based on team scanning volume</div>
                        </>
                      )}
                      
                      <div className="text-gray-300 font-semibold">Your Position:</div>
                      <div className="text-white font-medium">{getRoleDisplayText()}</div>
                      
                      <div className="text-gray-300 font-semibold">Team Composition:</div>
                      <div className="text-white font-medium">{totalSpotters} Spotters, {totalTowDrivers} Tow Drivers</div>
                    </div>
                  </div>
                  
                  {/* Example Weekly Paycheck (after Illinois taxes) */}
                  {paySettings.showTaxEstimates && examplePay && (
                    <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg border border-blue-700 mt-4">
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="text-lg font-semibold text-blue-300">Sample Weekly Paycheck</h4>
                        <button 
                          onClick={toggleExamplePay}
                          className="text-xs bg-blue-700 hover:bg-blue-600 text-white px-2 py-1 rounded"
                        >
                          {showExamplePay ? 'Hide Details' : 'Show Details'}
                        </button>
                      </div>
                      
                      <p className="text-white text-sm">
                        If your team found <span className="font-bold text-yellow-300">{paySettings.exampleCarsPerWeek} cars</span> in a week, 
                        with <span className="font-bold text-yellow-300">{safelyFormatNumber(paySettings.exampleScansPerWeek)} scans</span> and 
                        <span className="font-bold text-yellow-300"> {paySettings.exampleHoursPerWeek} hours</span> worked:
                      </p>
                      
                      <div className="mt-3">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-gray-300">Gross Weekly Pay:</span>
                          <span className="text-white font-medium">{formatCurrency(examplePay.grossWeeklyPay)}</span>
                        </div>
                        <div className="flex justify-between items-center mb-1 text-red-300">
                          <span>Estimated Taxes (IL):</span>
                          <span>-{formatCurrency(examplePay.totalWeeklyTax)}</span>
                        </div>
                        <div className="flex justify-between items-center pt-1 border-t border-blue-700">
                          <span className="text-lg font-semibold text-blue-300">Take-Home Pay:</span>
                          <span className="text-xl font-bold text-green-300">{formatCurrency(examplePay.netWeeklyPay)}</span>
                        </div>
                      </div>
                      
                      {showExamplePay && (
                        <div className="mt-3 pt-3 border-t border-blue-700">
                          <h5 className="text-sm font-semibold text-blue-300 mb-2">Pay Breakdown</h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <h6 className="text-xs text-blue-300 mb-1">Income</h6>
                              <ul className="space-y-1">
                                {shouldShowBasicPay && (
                                  <>
                                    <li className="flex justify-between text-xs">
                                      <span className="text-gray-400">Hourly Pay:</span>
                                      <span className="text-white">{formatCurrency(examplePay.hourlyPay)}</span>
                                    </li>
                                    <li className="flex justify-between text-xs">
                                      <span className="text-gray-400">Recovery Bonus:</span>
                                      <span className="text-white">{formatCurrency(examplePay.recoveryBonusPerPerson)}</span>
                                    </li>
                                  </>
                                )}
                                {shouldShowScanBonus && (
                                  <li className="flex justify-between text-xs">
                                    <span className="text-gray-400">Scan Bonus:</span>
                                    <span className="text-white">{formatCurrency(examplePay.scanBonusPerPerson)}</span>
                                  </li>
                                )}
                              </ul>
                            </div>
                            <div>
                              <h6 className="text-xs text-blue-300 mb-1">Tax Deductions</h6>
                              <ul className="space-y-1">
                                <li className="flex justify-between text-xs">
                                  <span className="text-gray-400">Federal:</span>
                                  <span className="text-red-300">-{formatCurrency(examplePay.weeklyFederalTax)}</span>
                                </li>
                                <li className="flex justify-between text-xs">
                                  <span className="text-gray-400">Illinois:</span>
                                  <span className="text-red-300">-{formatCurrency(examplePay.weeklyStateTax)}</span>
                                </li>
                                <li className="flex justify-between text-xs">
                                  <span className="text-gray-400">Social Security:</span>
                                  <span className="text-red-300">-{formatCurrency(examplePay.weeklySocialSecurityTax)}</span>
                                </li>
                                <li className="flex justify-between text-xs">
                                  <span className="text-gray-400">Medicare:</span>
                                  <span className="text-red-300">-{formatCurrency(examplePay.weeklyMedicareTax)}</span>
                                </li>
                              </ul>
                            </div>
                          </div>
                          <p className="mt-2 text-xs text-gray-400">
                            Tax calculations are based on 2023 tax tables for a single filer in Illinois. Your actual taxes may vary 
                            based on your specific tax situation, filing status, and other income sources.
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                  
                  <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg border border-blue-700">
                    <h4 className="text-lg font-semibold text-blue-300 mb-2">Pay Structure Overview</h4>
                    <div className="text-white text-sm space-y-3">
                      <p>Our compensation model is designed to reward both individual effort and team performance:</p>
                      
                      <div>
                        <h5 className="font-semibold text-yellow-300">Base Hourly Pay</h5>
                        <p>Every team member earns a base hourly rate of {formatCurrency(effectiveHourlyRate)}/hour, regardless of position.</p>
                      </div>
                      
                      <div>
                        <h5 className="font-semibold text-yellow-300">Vehicle Recovery Bonuses</h5>
                        <p>When a vehicle is successfully recovered (not just found), the team earns an additional {formatCurrency(effectiveBonusPerCarRecovered)} per vehicle. This reward encourages not just finding vehicles but ensuring they are properly processed.</p>
                      </div>
                      
                      {shouldShowScanBonus && (
                        <div>
                          <h5 className="font-semibold text-yellow-300">Team Scan Volume Bonuses</h5>
                          <p>To incentivize thorough area coverage, weekly bonuses are awarded based on the team's total scanning volume. These bonuses scale with team size to ensure fairness across different team configurations.</p>
                          <p className="mt-1">Current scan bonus tiers for your team ({totalTeamMembers} members):</p>
                          <ul className="list-disc pl-5 mt-1">
                            <li>Tier 1: {formatCurrency(scanBonusTier1)} at {safelyFormatNumber(scanBonusTier1Threshold)} scans</li>
                            <li>Tier 2: {formatCurrency(scanBonusTier2)} at {safelyFormatNumber(scanBonusTier2Threshold)} scans</li>
                            <li>Tier 3: {formatCurrency(scanBonusTier3)} at {safelyFormatNumber(scanBonusTier3Threshold)} scans</li>
                            <li>Tier 4: {formatCurrency(scanBonusTier4)} at {safelyFormatNumber(scanBonusTier4Threshold)} scans</li>
                            <li>Tier 5: {formatCurrency(scanBonusTier5)} at {safelyFormatNumber(scanBonusTier5Threshold)} scans</li>
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              
              {/* Contribution Details */}
              {selectedStat === 'contribution' && (
                <div className="space-y-4">
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h4 className="text-lg font-semibold text-blue-300 mb-2">Your Contribution to Team Performance</h4>
                    
                    <div className="mt-3 relative pt-1">
                      <div className="text-xs text-gray-400 mb-1">Orders Processed Today:</div>
                      <div className="flex mb-2 items-center justify-between">
                        <div>
                          <span className="text-xs font-semibold inline-block text-blue-300">
                            You: {stats.userOpenOrdersToday} orders
                          </span>
                        </div>
                        <div className="text-right">
                          <span className="text-xs font-semibold inline-block text-blue-300">
                            Team: {stats.teamOpenOrdersToday} orders
                          </span>
                        </div>
                      </div>
                      <div className="overflow-hidden h-3 mb-1 text-xs flex rounded bg-gray-800 border border-gray-700">
                        <div 
                          style={{ width: `${userContribution}%` }} 
                          className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-green-500 to-green-600"
                        >
                          <span className="text-xs text-white px-1">{userContribution}%</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-4 grid grid-cols-2 gap-4">
                      <div className="bg-gray-800 p-3 rounded-lg">
                        <div className="text-sm text-gray-300">Hours Worked Today</div>
                        <div className="text-xl font-bold text-yellow-400">{hoursWorkedToday} hrs</div>
                        <div className="mt-1 text-xs text-gray-400">Team: {stats.teamHoursWorkedToday} hrs</div>
                      </div>
                      
                      <div className="bg-gray-800 p-3 rounded-lg">
                        <div className="text-sm text-gray-300">Your Hours This Week</div>
                        <div className="text-xl font-bold text-yellow-400">{stats.userHoursWorkedWeek} hrs</div>
                        <div className="mt-1 text-xs text-gray-400">Team: {stats.teamHoursWorkedWeek} hrs</div>
                      </div>
                    </div>
                    
                    <div className="mt-4 grid grid-cols-2 gap-4">
                      <div className="bg-gray-800 p-3 rounded-lg">
                        <div className="text-sm text-gray-300">Your Scans Today</div>
                        <div className="text-xl font-bold text-amber-400">{safelyFormatNumber(stats.userScansToday)}</div>
                        <div className="mt-1 text-xs text-gray-400">Team: {safelyFormatNumber(stats.teamScansToday)}</div>
                      </div>
                      
                      <div className="bg-gray-800 p-3 rounded-lg">
                        <div className="text-sm text-gray-300">Your Markers Today</div>
                        <div className="text-xl font-bold text-pink-400">{stats.userMarkersCreatedToday}</div>
                        <div className="mt-1 text-xs text-gray-400">Team: {stats.teamMarkersCreatedToday}</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg border border-blue-700">
                    <h4 className="text-lg font-semibold text-blue-300 mb-2">Efficiency Metrics</h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <div className="text-sm text-gray-300 mb-1">Scans Per Hour</div>
                        <div className="text-xl font-bold text-white">
                          {hoursWorkedToday > 0 ? Math.round((stats.userScansToday || 0) / hoursWorkedToday).toLocaleString() : 0}
                        </div>
                        <div className="mt-1 text-xs text-gray-400">Team Avg: {stats.teamHoursWorkedToday > 0 ? Math.round((stats.teamScansToday || 0) / stats.teamHoursWorkedToday).toLocaleString() : 0}</div>
                      </div>
                      
                      <div>
                        <div className="text-sm text-gray-300 mb-1">Orders Per Hour</div>
                        <div className="text-xl font-bold text-white">
                          {hoursWorkedToday > 0 ? (stats.userOpenOrdersToday / hoursWorkedToday).toFixed(1) : 0}
                        </div>
                        <div className="mt-1 text-xs text-gray-400">Team Avg: {stats.teamHoursWorkedToday > 0 ? (stats.teamOpenOrdersToday / stats.teamHoursWorkedToday).toFixed(1) : 0}</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Team Scans Detail */}
              {selectedStat === 'teamScans' && (
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h4 className="text-lg font-semibold text-blue-300 mb-2">Team Scans Details</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Today</h5>
                      <div className="text-2xl font-bold text-amber-400">{safelyFormatNumber(stats.teamScansToday)}</div>
                      <div className="mt-2 text-xs text-gray-400">Today's total team scans</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">This Week</h5>
                      <div className="text-2xl font-bold text-amber-400">{safelyFormatNumber(stats.teamScansWeek)}</div>
                      <div className="mt-2 text-xs text-gray-400">Week's total team scans</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">This Month</h5>
                      <div className="text-2xl font-bold text-amber-400">{safelyFormatNumber(stats.teamScansMonth)}</div>
                      <div className="mt-2 text-xs text-gray-400">Month's total team scans</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Year to Date</h5>
                      <div className="text-2xl font-bold text-amber-400">{safelyFormatNumber(stats.teamScansYTD)}</div>
                      <div className="mt-2 text-xs text-gray-400">YTD total team scans</div>
                    </div>
                  </div>
                  
                  {shouldShowScanBonus && (
                    <div className="mt-6 bg-blue-800 bg-opacity-50 p-4 rounded-lg border border-blue-700">
                      <h4 className="text-md font-semibold text-blue-300 mb-3">Scan Bonus Progress</h4>
                      
                      <div className="text-sm mb-4">
                        Current Weekly Scan Bonus: <span className="text-xl font-bold text-yellow-300">{formatCurrency(teamScanBonus)}</span>
                        {scanTierInfo.tier > 0 ? (
                          <span className="ml-2 px-2 py-1 bg-yellow-800 text-yellow-200 rounded-full text-xs">
                            Tier {scanTierInfo.tier} Achieved!
                          </span>
                        ) : (
                          <span className="ml-2 px-2 py-1 bg-gray-700 text-gray-400 rounded-full text-xs">
                            No Tier Reached Yet
                          </span>
                        )}
                      </div>
                      
                      <div className="mt-3 relative pt-1">
                        <div className="text-xs text-gray-400 mb-1">Progress toward next tier:</div>
                        <div className="flex mb-2 items-center justify-between">
                          <div>
                            <span className="text-xs font-semibold inline-block text-blue-300">
                              {safelyFormatNumber(stats.teamScansWeek)} scans
                            </span>
                          </div>
                          <div className="text-right">
                            <span className="text-xs font-semibold inline-block text-blue-300">
                              {scanTierInfo.progress.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                        <div className="overflow-hidden h-4 mb-1 text-xs flex rounded bg-gray-700 border border-gray-600">
                          <div 
                            style={{ width: `${scanTierInfo.progress}%` }} 
                            className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-blue-500 to-blue-600"
                          ></div>
                        </div>
                        <div className="flex justify-between text-xs text-gray-400 mt-1">
                          <span>0</span>
                          <span>T1: {safelyFormatNumber(scanBonusTier1Threshold)}</span>
                          <span>T5: {safelyFormatNumber(scanBonusTier5Threshold)}</span>
                        </div>
                      </div>
                      
                      <div className="mt-4 grid grid-cols-1 sm:grid-cols-5 gap-2">
                        {[1, 2, 3, 4, 5].map(tier => (
                          <div key={tier} className={`p-2 rounded-lg ${scanTierInfo.tier >= tier ? 'bg-blue-700' : 'bg-gray-700'}`}>
                            <div className="text-xs text-gray-300">Tier {tier}</div>
                            <div className={`text-sm font-bold ${scanTierInfo.tier >= tier ? 'text-yellow-300' : 'text-gray-500'}`}>
                              {formatCurrency(tier === 1 ? scanBonusTier1 : 
                                tier === 2 ? scanBonusTier2 : 
                                tier === 3 ? scanBonusTier3 : 
                                tier === 4 ? scanBonusTier4 : scanBonusTier5)}
                            </div>
                            <div className="text-xs text-gray-400">
                              {safelyFormatNumber(tier === 1 ? scanBonusTier1Threshold : 
                                tier === 2 ? scanBonusTier2Threshold : 
                                tier === 3 ? scanBonusTier3Threshold : 
                                tier === 4 ? scanBonusTier4Threshold : scanBonusTier5Threshold)} scans
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* User Scans Detail */}
              {selectedStat === 'userScans' && (
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h4 className="text-lg font-semibold text-blue-300 mb-2">Your Scans Details</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Today</h5>
                      <div className="text-2xl font-bold text-amber-400">{safelyFormatNumber(stats.userScansToday)}</div>
                      <div className="mt-2 text-xs text-gray-400">Today's total personal scans</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">This Week</h5>
                      <div className="text-2xl font-bold text-amber-400">{safelyFormatNumber(stats.userScansWeek)}</div>
                      <div className="mt-2 text-xs text-gray-400">Week's total personal scans</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">This Month</h5>
                      <div className="text-2xl font-bold text-amber-400">{safelyFormatNumber(stats.userScansMonth)}</div>
                      <div className="mt-2 text-xs text-gray-400">Month's total personal scans</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Year to Date</h5>
                      <div className="text-2xl font-bold text-amber-400">{safelyFormatNumber(stats.userScansYTD)}</div>
                      <div className="mt-2 text-xs text-gray-400">YTD total personal scans</div>
                    </div>
                  </div>
                  
                  <div className="mt-6 bg-gray-800 p-4 rounded-lg">
                    <h4 className="text-md font-semibold text-blue-300 mb-3">Performance Insights</h4>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                      <div>
                        <div className="text-sm text-gray-300 mb-1">Your Contribution to Team Scans</div>
                        <div className="mt-2 relative pt-1">
                          <div className="flex mb-2 items-center justify-between">
                            <div>
                              <span className="text-xs font-semibold inline-block text-blue-300">
                                You: {safelyFormatNumber(stats.userScansToday)}
                              </span>
                            </div>
                            <div className="text-right">
                              <span className="text-xs font-semibold inline-block text-blue-300">
                                Team: {safelyFormatNumber(stats.teamScansToday)}
                              </span>
                            </div>
                          </div>
                          <div className="overflow-hidden h-3 mb-1 text-xs flex rounded bg-gray-700">
                            <div 
                              style={{ width: `${stats.teamScansToday ? (stats.userScansToday / stats.teamScansToday * 100) : 0}%` }} 
                              className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-amber-500 to-amber-600"
                            ></div>
                          </div>
                          <div className="text-xs text-gray-400 mt-1 text-right">
                            {stats.teamScansToday ? ((stats.userScansToday / stats.teamScansToday) * 100).toFixed(1) : 0}% of team scans
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="text-sm text-gray-300 mb-1">Scans Per Hour Today</div>
                        <div className="text-3xl font-bold text-amber-400">
                          {hoursWorkedToday > 0 ? Math.round(stats.userScansToday / hoursWorkedToday).toLocaleString() : 0}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          Based on {hoursWorkedToday} hours worked today
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-sm text-gray-300 mb-1">Weekly Trend</div>
                    <div className="flex items-center">
                      <div className="flex-1 bg-gray-700 h-8 rounded-lg overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-amber-600 to-amber-400" style={{ width: '65%' }}></div>
                      </div>
                      <div className="ml-4 text-amber-300 font-bold">+15%</div>
                    </div>
                    <div className="text-xs text-gray-400 mt-1">Compared to your average weekly scans</div>
                  </div>
                </div>
              )}

              {/* Generic template for all other stats - with YTD column added */}
              {selectedStat !== 'userPay' && selectedStat !== 'teamPay' && selectedStat !== 'payStructure' && 
                selectedStat !== 'contribution' && selectedStat !== 'scanBonus' && 
                selectedStat !== 'teamScans' && selectedStat !== 'userScans' && (
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h4 className="text-lg font-semibold text-blue-300 mb-4">Detailed Metrics</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Today</h5>
                      <div className="text-3xl font-bold text-white">0</div>
                      <div className="mt-2 text-xs text-gray-400">Today's total</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">This Week</h5>
                      <div className="text-3xl font-bold text-white">0</div>
                      <div className="mt-2 text-xs text-gray-400">Week's total</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">This Month</h5>
                      <div className="text-3xl font-bold text-white">0</div>
                      <div className="mt-2 text-xs text-gray-400">Month's total</div>
                    </div>
                    
                    <div className="bg-gray-800 p-3 rounded-lg">
                      <h5 className="text-md font-semibold text-blue-300 mb-2">Year to Date</h5>
                      <div className="text-3xl font-bold text-white">0</div>
                      <div className="mt-2 text-xs text-gray-400">YTD total</div>
                    </div>
                  </div>
                  
                  <div className="mt-6 bg-gray-800 p-4 rounded-lg">
                    <h5 className="text-md font-semibold text-blue-300 mb-3">Performance Insights</h5>
                    
                    {/* Daily Average */}
                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-1">
                        <div className="text-gray-300 text-sm">Daily Average:</div>
                        <div className="text-white font-medium">0</div>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div className="bg-blue-500 h-2 rounded-full" style={{ width: `0%` }}></div>
                      </div>
                      <div className="text-xs text-gray-400 mt-1">Today vs. Daily Average</div>
                    </div>
                    
                    {/* Weekly Trend */}
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <div className="text-gray-300 text-sm">Weekly Trend:</div>
                        <div className="text-white font-medium">0% vs. average</div>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div className="bg-gray-500 h-2 rounded-full" style={{ width: `0%` }}></div>
                      </div>
                      <div className="text-xs text-gray-400 mt-1">This week vs. average week</div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Messages for disabled features */}
              {selectedStat === 'userPay' && !paySettings.enablePayCalculations && (
                <div className="bg-gray-700 p-4 rounded-lg text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                  <h4 className="text-lg font-semibold text-white mb-2">Pay Calculations Disabled</h4>
                  <p className="text-gray-300 mb-4">
                    The pay calculation feature is currently disabled in system settings.
                  </p>
                  {isUserAdmin && (
                    <a 
                      href="/settings" 
                      className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                      Go to Settings
                    </a>
                  )}
                </div>
              )}
              
              {selectedStat === 'teamPay' && !paySettings.enablePayCalculations && (
                <div className="bg-gray-700 p-4 rounded-lg text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                  <h4 className="text-lg font-semibold text-white mb-2">Team Pay Calculations Disabled</h4>
                  <p className="text-gray-300 mb-4">
                    The team pay calculation feature is currently disabled in system settings.
                  </p>
                  {isUserAdmin && (
                    <a 
                      href="/settings" 
                      className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
<path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                      Go to Settings
                    </a>
                  )}
                </div>
              )}
              
              {selectedStat === 'payStructure' && !paySettings.enablePayCalculations && (
                <div className="bg-gray-700 p-4 rounded-lg text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                  <h4 className="text-lg font-semibold text-white mb-2">Pay Structure Disabled</h4>
                  <p className="text-gray-300 mb-4">
                    The pay structure feature is currently disabled in system settings.
                  </p>
                  {isUserAdmin && (
                    <a 
                      href="/settings" 
                      className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                      Go to Settings
                    </a>
                  )}
                </div>
              )}
              
              {/* Disabled scan bonus message */}
              {selectedStat === 'scanBonus' && (!paySettings.enablePayCalculations || !paySettings.enableScanBonusCalculations) && (
                <div className="bg-gray-700 p-4 rounded-lg text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                  <h4 className="text-lg font-semibold text-white mb-2">Scan Bonus Feature Disabled</h4>
                  <p className="text-gray-300 mb-4">
                    The scan bonus feature is currently disabled in system settings.
                  </p>
                  {isUserAdmin && (
                    <a 
                      href="/settings" 
                      className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                      Go to Settings
                    </a>
                  )}
                </div>
              )}
              
              {/* Pay enabled but specific feature disabled */}
              {selectedStat === 'userPay' && paySettings.enablePayCalculations && !paySettings.enableBasicPayCalculations && (
                <div className="bg-gray-700 p-4 rounded-lg text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                  <h4 className="text-lg font-semibold text-white mb-2">Basic Pay Calculations Disabled</h4>
                  <p className="text-gray-300 mb-4">
                    The hourly and recovery pay components are currently disabled in system settings.
                  </p>
                  {isUserAdmin && (
                    <a 
                      href="/settings" 
                      className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                      Go to Settings
                    </a>
                  )}
                </div>
              )}
              
              <div className="flex justify-end mt-6">
                <button
                  onClick={() => setShowStatDetail(false)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-md"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Stats;