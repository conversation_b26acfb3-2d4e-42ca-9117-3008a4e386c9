/**
 * Utility functions for navigation, routing, and direction guidance
 */
import L from 'leaflet';
import 'leaflet-routing-machine';

/**
 * Calculate and display a route between two points
 * 
 * @param {Object} mapRef - Reference to Leaflet map
 * @param {Object} origin - Origin position with lat/lng
 * @param {Object} destination - Destination position with lat/lng
 * @param {Function} onRouteCalculated - Callback when route is calculated
 * @param {Function} onRouteFailed - Callback when route calculation fails
 * @returns {Object} Routing control object
 */
export const calculateAndDisplayRoute = (
  mapRef, 
  origin, 
  destination, 
  onRouteCalculated = null, 
  onRouteFailed = null
) => {
  if (!mapRef || !mapRef.current || !origin || !destination) {
    console.error("Valid map reference and coordinates are required");
    if (onRouteFailed) onRouteFailed(new Error("Invalid parameters"));
    return null;
  }
  
  try {
    // Clean up existing routing control if any
    if (mapRef._routingControl) {
      try {
        mapRef.current.removeControl(mapRef._routingControl);
      } catch (err) {
        console.warn("Error removing existing routing control:", err);
      }
    }
    
    // Create routing control
    const routingControl = L.Routing.control({
      waypoints: [
        L.latLng(origin.lat, origin.lng),
        L.latLng(destination.lat, destination.lng)
      ],
      routeWhileDragging: false,
      router: L.Routing.osrmv1({
        serviceUrl: 'https://router.project-osrm.org/route/v1',
        profile: 'driving'
      }),
      lineOptions: {
        styles: [
          {color: '#3B82F6', opacity: 0.8, weight: 5}
        ],
        addWaypoints: false,
        extendToWaypoints: true,
        missingRouteTolerance: 0
      },
      createMarker: function() { return null; }, // Suppress default markers
      fitSelectedRoutes: false,
      showAlternatives: false,
      altLineOptions: {
        styles: [
          {color: '#60A5FA', opacity: 0.6, weight: 4}
        ]
      },
      plan: new L.Routing.Plan([
        L.latLng(origin.lat, origin.lng),
        L.latLng(destination.lat, destination.lng)
      ], {
        createMarker: function() { return null; },
        draggableWaypoints: false,
        routeWhileDragging: false
      }),
      collapsible: true,
      show: false // Don't show the instructions panel
    });
    
    // Save reference for cleanup
    mapRef._routingControl = routingControl;
    
    // Add to map
    routingControl.addTo(mapRef.current);
    
    // Listen for route calculation completion
    routingControl.on('routesfound', function(e) {
      try {
        const routes = e.routes;
        const summary = routes[0].summary;
        const steps = routes[0].instructions || [];
        
        // Extract useful information
        const distance = summary.totalDistance / 1609.34; // convert meters to miles
        const duration = summary.totalTime; // in seconds
        
        // Call callback with route information
        if (onRouteCalculated) {
          onRouteCalculated({
            distance,
            duration,
            steps,
            route: routes[0]
          });
        }
      } catch (err) {
        console.error("Error processing route:", err);
        if (onRouteFailed) onRouteFailed(err);
      }
    });
    
    // Handle route calculation error
    routingControl.on('routingerror', function(e) {
      console.error("Routing error:", e);
      if (onRouteFailed) onRouteFailed(e);
    });
    
    return routingControl;
  } catch (err) {
    console.error("Error calculating route:", err);
    if (onRouteFailed) onRouteFailed(err);
    return null;
  }
};

/**
 * Create a fallback direct route when routing service fails
 * 
 * @param {Object} mapRef - Reference to Leaflet map
 * @param {Object} origin - Origin position with lat/lng
 * @param {Object} destination - Destination position with lat/lng
 * @param {Object} options - Route options
 * @returns {Object} Route information and Polyline
 */
export const createFallbackRoute = (mapRef, origin, destination, options = {}) => {
  if (!mapRef || !mapRef.current || !origin || !destination) {
    console.error("Valid map reference and coordinates are required");
    return null;
  }
  
  try {
    const {
      color = '#3B82F6',
      dashArray = '10, 10',
      fitBounds = true
    } = options;
    
    // Calculate approximate distance and time
    const distance = calculateDistance(origin, destination);
    const estimatedTimeSeconds = (distance / 40) * 3600; // 40 mph average speed
    
    // Calculate bearing for direction
    const bearing = calculateBearing(origin, destination);
    const direction = getDirectionText(bearing);
    
    // Draw a straight line
    const fallbackRoute = L.polyline([
      [origin.lat, origin.lng],
      [destination.lat, destination.lng]
    ], {
      color: color,
      weight: 5,
      opacity: 0.8,
      dashArray: dashArray,
      lineCap: 'round'
    }).addTo(mapRef.current);
    
    // Fit bounds if requested
    if (fitBounds) {
      mapRef.current.fitBounds([
        [origin.lat, origin.lng],
        [destination.lat, destination.lng]
      ], { padding: [50, 50] });
    }
    
    // Return route information
    return {
      route: fallbackRoute,
      distance: distance,
      duration: estimatedTimeSeconds,
      steps: [{ 
        text: `Head ${direction} toward destination`, 
        distance: distance * 1609.34, // Convert to meters
        time: estimatedTimeSeconds // In seconds
      }]
    };
  } catch (err) {
    console.error("Error creating fallback route:", err);
    return null;
  }
};

/**
 * Generate navigation panel content
 * 
 * @param {Object} location - Location data
 * @param {Object} routeInfo - Route information
 * @returns {string} HTML content for navigation panel
 */
export const generateNavigationPanelContent = (location, routeInfo = {}) => {
  if (!location) return '';
  
  try {
    // Determine image to show
    let imageUrl = '';
    if (location.images && location.images.length > 0) {
      imageUrl = location.images[0];
    }
    
    // Determine if there's parking side info
    let parkingSideHtml = '';
    if (location.parkingSide) {
      const side = location.parkingSide.charAt(0).toUpperCase() + location.parkingSide.slice(1);
      const arrow = location.parkingSide === 'left' ? '←' : '→';
      parkingSideHtml = `
        <div class="parking-side-indicator">
          Vehicle Parked on ${side} Side ${arrow}
        </div>
      `;
    }
    
    // Status indicator
    let statusHtml = '';
    if (location.status === 'picked-up') {
      statusHtml = `<div class="parking-side-indicator" style="background-color: #10B981;">
        Picked Up
      </div>`;
    }
    
    // Vehicle info section
    let vehicleInfoHtml = '';
    if (location.plateNumber || location.vin || location.make || location.model || location.driveType) {
      vehicleInfoHtml = `
        <div class="vehicle-info" style="font-size: 10px; margin-top: 3px; margin-bottom: 3px;">
          ${location.make && location.model ? `${location.make} ${location.model}` : ''}
          ${location.plateNumber ? `• Plate: ${location.plateNumber}` : ''}
        </div>
      `;
    }
    
    // Get navigation info
    const { distance, duration, nextDirection, directionArrow } = routeInfo;
    
    // Format distance and time
    const distanceText = distance ? formatDistance(distance) : 'Calculating...';
    const durationText = duration ? formatTime(duration) : 'Calculating...';
    
    // Direction arrow
    const directionArrowHtml = directionArrow || '⏳';
    
    // Next direction step
    const directionText = nextDirection || 'Calculating route...';
    
    // Generate HTML
    return `
      <div class="navigation-image">
        ${imageUrl ? `<img src="${imageUrl}" alt="Destination" style="width:100%; height:100%; object-fit:cover;">` : 
        `<div style="color: #9CA3AF; text-align: center;">No image available</div>`}
      </div>
      <div class="navigation-details">
        ${parkingSideHtml}
        ${statusHtml}
        <div class="navigation-destination">${location.name || 'Destination'}</div>
        ${vehicleInfoHtml}
        <div class="navigation-distance-time">
          <div>${distanceText}</div>
          <div>${durationText}</div>
        </div>
        <div class="navigation-direction">
          <div class="navigation-direction-arrow">${directionArrowHtml}</div>
          <div>${directionText}</div>
        </div>
      </div>
    `;
  } catch (err) {
    console.error("Error generating navigation panel content:", err);
    return '';
  }
};

/**
 * Get the next navigation instruction from route steps
 * 
 * @param {Array} steps - Route instruction steps
 * @param {number} currentIndex - Current step index
 * @returns {Object} Next instruction and direction arrow
 */
export const getNextNavigationInstruction = (steps, currentIndex = 0) => {
  if (!steps || steps.length === 0 || currentIndex >= steps.length) {
    return { text: "Proceed to destination", arrow: '⬆️' };
  }
  
  try {
    const step = steps[currentIndex];
    let arrow = '⬆️'; // Default direction
    
    // Determine arrow based on direction keywords in the instruction
    if (step.text.includes('left')) {
      arrow = '↖️';
    } else if (step.text.includes('right')) {
      arrow = '↗️';
    } else if (step.text.includes('continue')) {
      arrow = '⬆️';
    } else if (step.text.includes('arrive')) {
      arrow = '🏁';
    }
    
    return {
      text: step.text,
      arrow: arrow,
      distance: step.distance,
      time: step.time
    };
  } catch (err) {
    console.error("Error getting next navigation instruction:", err);
    return { text: "Proceed to destination", arrow: '⬆️' };
  }
};

/**
 * Optimize a route through multiple locations
 * 
 * @param {Object} startPosition - Starting position with lat/lng
 * @param {Array} locationsList - List of locations to visit
 * @param {Object} options - Optimization options
 * @returns {Array} Optimized route with distances
 */
export const optimizeRoute = (startPosition, locationsList, options = {}) => {
  if (!locationsList || locationsList.length === 0) return [];
  
  try {
    const {
      strategy = 'nearest', // 'nearest' or 'priority'
      filterFunc = null // Optional function to filter locations
    } = options;
    
    // Filter locations if filter function provided
    const filteredLocations = filterFunc ? locationsList.filter(filterFunc) : [...locationsList];
    
    // For priority strategy, sort by priority first
    if (strategy === 'priority') {
      // Group by priority
      const priorityLocations = filteredLocations.filter(loc => loc.isPriority);
      const normalLocations = filteredLocations.filter(loc => !loc.isPriority);
      
      // Find closest priority location first, then closest normal location
      const optimizedRoute = [];
      let currentPosition = startPosition;
      
      // Process priority locations first
      while (priorityLocations.length > 0) {
        const { location: closestLocation, distance } = findClosestLocation(currentPosition, priorityLocations);
        
        if (closestLocation) {
          optimizedRoute.push({
            location: closestLocation,
            distanceFromPrevious: distance
          });
          
          const index = priorityLocations.findIndex(loc => loc.id === closestLocation.id);
          if (index !== -1) {
            priorityLocations.splice(index, 1);
          }
          currentPosition = closestLocation.position;
        }
      }
      
      // Then process normal locations
      while (normalLocations.length > 0) {
        const { location: closestLocation, distance } = findClosestLocation(currentPosition, normalLocations);
        
        if (closestLocation) {
          optimizedRoute.push({
            location: closestLocation,
            distanceFromPrevious: distance
          });
          
          const index = normalLocations.findIndex(loc => loc.id === closestLocation.id);
          if (index !== -1) {
            normalLocations.splice(index, 1);
          }
          currentPosition = closestLocation.position;
        }
      }
      
      return optimizedRoute;
    } else {
      // Default nearest neighbor strategy
      const unvisitedLocations = [...filteredLocations];
      const optimizedRoute = [];
      let currentPosition = startPosition;
      
      while (unvisitedLocations.length > 0) {
        const { location: closestLocation, distance } = findClosestLocation(currentPosition, unvisitedLocations);
        
        if (closestLocation) {
          optimizedRoute.push({
            location: closestLocation,
            distanceFromPrevious: distance
          });
          
          const index = unvisitedLocations.findIndex(loc => loc.id === closestLocation.id);
          if (index !== -1) {
            unvisitedLocations.splice(index, 1);
          }
          currentPosition = closestLocation.position;
        }
      }
      
      return optimizedRoute;
    }
  } catch (err) {
    console.error("Error optimizing route:", err);
    return [];
  }
};

/**
 * Find the closest location to a given position
 * 
 * @param {Object} fromPosition - Starting position with lat/lng
 * @param {Array} locationsList - List of locations
 * @returns {Object} Closest location and distance
 */
export const findClosestLocation = (fromPosition, locationsList) => {
  if (!locationsList || locationsList.length === 0) return null;
  
  let closestLocation = null;
  let shortestDistance = Infinity;
  
  for (const location of locationsList) {
    const distance = calculateDistance(fromPosition, location.position);
    if (distance < shortestDistance) {
      shortestDistance = distance;
      closestLocation = location;
    }
  }
  
  return { location: closestLocation, distance: shortestDistance };
};

/**
 * Calculate distance between two points (in MILES)
 * 
 * @param {Object} point1 - First point with lat/lng properties
 * @param {Object} point2 - Second point with lat/lng properties
 * @returns {number} Distance in miles
 */
export const calculateDistance = (point1, point2) => {
  const R = 3958.8; // Earth's radius in MILES
  const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
  const φ2 = point2.lat * Math.PI/180;
  const Δφ = (point2.lat-point1.lat) * Math.PI/180;
  const Δλ = (point2.lng-point1.lng) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  const d = R * c; // in MILES
  return d;
};

/**
 * Calculate bearing between two points (in degrees)
 * 
 * @param {Object} start - Starting point with lat/lng
 * @param {Object} end - Ending point with lat/lng
 * @returns {number} Bearing in degrees (0-360)
 */
export const calculateBearing = (start, end) => {
  const startLat = start.lat * Math.PI / 180;
  const startLng = start.lng * Math.PI / 180;
  const endLat = end.lat * Math.PI / 180;
  const endLng = end.lng * Math.PI / 180;

  const y = Math.sin(endLng - startLng) * Math.cos(endLat);
  const x = Math.cos(startLat) * Math.sin(endLat) -
            Math.sin(startLat) * Math.cos(endLat) * Math.cos(endLng - startLng);
  
  const bearing = Math.atan2(y, x) * 180 / Math.PI;
  return (bearing + 360) % 360; // Normalize to 0-360
};

/**
 * Get direction text from bearing
 * 
 * @param {number} bearing - Bearing in degrees (0-360)
 * @returns {string} Direction text
 */
export const getDirectionText = (bearing) => {
  const directions = ['North', 'Northeast', 'East', 'Southeast', 'South', 'Southwest', 'West', 'Northwest'];
  const index = Math.round(bearing / 45) % 8;
  return directions[index];
};

/**
 * Get direction arrow from bearing
 * 
 * @param {number} bearing - Bearing in degrees (0-360)
 * @returns {string} Arrow emoji
 */
export const getDirectionArrow = (bearing) => {
  // Map bearing to one of 8 cardinal directions with corresponding arrows
  const directions = [
    { min: 337.5, max: 360, arrow: '⬆️' }, // North
    { min: 0, max: 22.5, arrow: '⬆️' },    // North
    { min: 22.5, max: 67.5, arrow: '↗️' },  // Northeast
    { min: 67.5, max: 112.5, arrow: '➡️' }, // East
    { min: 112.5, max: 157.5, arrow: '↘️' }, // Southeast
    { min: 157.5, max: 202.5, arrow: '⬇️' }, // South
    { min: 202.5, max: 247.5, arrow: '↙️' }, // Southwest
    { min: 247.5, max: 292.5, arrow: '⬅️' }, // West
    { min: 292.5, max: 337.5, arrow: '↖️' }  // Northwest
  ];
  
  // Find the direction that matches the bearing
  for (const dir of directions) {
    if ((bearing >= dir.min && bearing < dir.max) || 
        (dir.min > dir.max && (bearing >= dir.min || bearing < dir.max))) {
      return dir.arrow;
    }
  }
  
  return '⬆️'; // Default to North
};

/**
 * Format distance in human-readable form
 * 
 * @param {number} miles - Distance in miles
 * @returns {string} Formatted distance
 */
export const formatDistance = (miles) => {
  if (miles < 0.1) {
    return `${Math.round(miles * 5280)} feet`;
  } else {
    return `${miles.toFixed(1)} miles`;
  }
};

/**
 * Format time in human-readable form
 * 
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time
 */
export const formatTime = (seconds) => {
  if (seconds < 60) {
    return `${Math.round(seconds)} seconds`;
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)} minutes`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
};

/**
 * Check if the user is close to a destination
 * 
 * @param {Object} userPosition - User's position with lat/lng
 * @param {Object} destinationPosition - Destination position with lat/lng
 * @param {number} thresholdMiles - Distance threshold in miles
 * @returns {boolean} Whether user is close to destination
 */
export const isNearDestination = (userPosition, destinationPosition, thresholdMiles = 0.03) => {
  if (!userPosition || !destinationPosition) return false;
  
  const distance = calculateDistance(userPosition, destinationPosition);
  return distance <= thresholdMiles;
};

/**
 * Check if the user is at a navigation junction
 * 
 * @param {Object} userPosition - User's position with lat/lng
 * @param {Array} steps - Navigation steps
 * @param {number} currentStepIndex - Current step index
 * @param {number} thresholdMiles - Distance threshold in miles
 * @returns {Object} Junction information if at junction
 */
export const checkNavigationJunction = (userPosition, steps, currentStepIndex, thresholdMiles = 0.01) => {
  if (!userPosition || !steps || currentStepIndex >= steps.length) return null;
  
  try {
    const currentStep = steps[currentStepIndex];
    
    // Check if we're near the maneuver point of this step
    if (currentStep.maneuver && currentStep.maneuver.location) {
      const maneuverPos = {
        lat: currentStep.maneuver.location[0],
        lng: currentStep.maneuver.location[1]
      };
      
      const distance = calculateDistance(userPosition, maneuverPos);
      
      if (distance <= thresholdMiles) {
        return {
          isAtJunction: true,
          nextStepIndex: currentStepIndex + 1,
          nextStep: currentStepIndex + 1 < steps.length ? steps[currentStepIndex + 1] : null
        };
      }
    }
    
    return { isAtJunction: false };
  } catch (err) {
    console.error("Error checking navigation junction:", err);
    return { isAtJunction: false };
  }
};