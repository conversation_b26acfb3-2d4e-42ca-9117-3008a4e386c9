// VehicleTrackerFirebase.js - Complete Firebase Operations and Data Management

// Firebase imports
import { 
  getFirestore, 
  collection, 
  getDocs, 
  doc, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  setDoc, 
  updateDoc, 
  writeBatch, 
  serverTimestamp, 
  addDoc, 
  deleteDoc, 
  onSnapshot 
} from 'firebase/firestore';
import { 
  getStorage, 
  ref, 
  uploadBytes, 
  getDownloadURL 
} from 'firebase/storage';
import { 
  initializeApp, 
  getApps 
} from 'firebase/app';

// Import utilities from VehicleTrackerUtils.js
import { 
  firebaseConfig, 
  formatDateRange, 
  compressImage, 
  getGeolocation,
  getUserAvatar 
} from './VehicleTrackerUtils';

// Initialize Firebase
export const initializeFirebase = async (targetUserId) => {
  try {
    console.log('🔥 Initializing Firebase for user:', targetUserId);

    let app;
    const existingApps = getApps();
    if (existingApps.length === 0) {
      console.log('🔥 Creating new Firebase app instance');
      app = initializeApp(firebaseConfig);
    } else {
      console.log('🔥 Using existing Firebase app instance');
      app = existingApps[0];
    }

    const firestore = getFirestore(app);
    const storageInstance = getStorage(app);

    return { firestore, storageInstance };
  } catch (error) {
    console.error('❌ Error initializing Firebase:', error);
    throw error;
  }
};

// Load user data
export const loadUserData = async (firestore, targetUserId) => {
  try {
    console.log('👤 Loading user data...');
    const userDoc = await getDoc(doc(firestore, 'users', targetUserId));

    if (!userDoc.exists()) {
      throw new Error(`User not found with ID: ${targetUserId}`);
    }
    
    const userData = userDoc.data();
    console.log('✅ User data loaded:', userData.email);

    // Load user profile for tags
    const profileDoc = await getDoc(doc(firestore, 'userProfiles', targetUserId));
    const profileData = profileDoc.exists() ? profileDoc.data() : {};

    const userInfo = {
      id: targetUserId,
      ...userData,
      displayName: profileData.displayName || userData.email?.split('@')[0] || 'Unknown User',
      avatar: profileData.avatar || getUserAvatar(userData.role),
      jobTitle: profileData.jobTitle || userData.role
    };

    return { userInfo, profileData };
  } catch (error) {
    console.error('❌ Error loading user data:', error);
    throw error;
  }
};

// Find user's team
export const findUserTeam = async (firestore, targetUserId) => {
  try {
    console.log('👥 Looking for user team...');
    const teamsSnapshot = await getDocs(collection(firestore, 'teams'));
    
    for (const teamDoc of teamsSnapshot.docs) {
      const teamMembersSnapshot = await getDocs(
        collection(firestore, `teams/${teamDoc.id}/teamMembers`)
      );

      const isMember = teamMembersSnapshot.docs.some(
        memberDoc => memberDoc.data().userId === targetUserId
      );

      if (isMember) {
        const userTeam = {
          id: teamDoc.id,
          ...teamDoc.data()
        };
        console.log('✅ Found user team:', userTeam.name);
        return userTeam;
      }
    }

    console.log('ℹ️ User is not part of any team');
    return null;
  } catch (error) {
    console.error('❌ Error finding user team:', error);
    return null;
  }
};

// Create week if needed
export const createWeekIfNeeded = async (db, userId, weekId) => {
  if (!db || !userId || !weekId) return;

  try {
    const weekRef = doc(db, 'users', userId, 'vehicleWeeks', weekId);
    const weekDoc = await getDoc(weekRef);

    if (!weekDoc.exists()) {
      const startOfWeek = new Date(parseInt(weekId.replace('week_', '')));
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(endOfWeek.getDate() + 6);

      const formattedRange = formatDateRange(startOfWeek, endOfWeek);

      await setDoc(weekRef, {
        startDate: startOfWeek,
        endDate: endOfWeek,
        displayRange: formattedRange,
        totalScans: 0,
        totalFound: 0,
        totalSecured: 0,
        recoveryRate: 0,
        carryOverInitialized: false,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      console.log('✅ Created new week:', weekId, 'with display range:', formattedRange);
    }
  } catch (error) {
    console.error('❌ Error creating week:', error);
  }
};

// Initialize week with carryovers
export const initializeWeekWithCarryOvers = async (db, userId, weekId) => {
  if (!db) return;

  try {
    // Check if this week already has carryovers initialized
    const weekRef = doc(db, 'users', userId, 'vehicleWeeks', weekId);
    const weekDoc = await getDoc(weekRef);

    if (weekDoc.exists() && weekDoc.data().carryOverInitialized) {
      return; // Already initialized
    }

    // Get week dates
    const weekData = weekDoc.data();
    if (!weekData || !weekData.startDate) return;

    const startDate = weekData.startDate.toDate();

    // Find previous weeks
    const weeksQuery = query(
      collection(db, 'users', userId, 'vehicleWeeks'),
      orderBy('endDate', 'desc')
    );

    const weeksSnapshot = await getDocs(weeksQuery);
    const weeks = weeksSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      startDate: doc.data().startDate?.toDate(),
      endDate: doc.data().endDate?.toDate()
    }));

    // Find closest previous week
    const previousWeeks = weeks.filter(week =>
      week.endDate < startDate && week.id !== weekId
    ).sort((a, b) => b.endDate - a.endDate);

    if (previousWeeks.length > 0) {
      const previousWeek = previousWeeks[0];

      // Get unsecured vehicles from previous week
      const previousVehiclesQuery = query(
        collection(db, 'users', userId, 'vehicleWeeks', previousWeek.id, 'vehicles')
      );

      const previousVehiclesSnapshot = await getDocs(previousVehiclesQuery);
      const unsecuredVehicles = previousVehiclesSnapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter(vehicle => {
          // Only carry over:
          // 1. Vehicles that are not secured
          // 2. User's own vehicles (not team vehicles that were incorrectly added)
          const isOwnVehicle = !vehicle.teamMemberId || vehicle.teamMemberId === userId || vehicle.securedFromTeammate === true;
          return vehicle.status !== 'SECURED' && isOwnVehicle && !vehicle.copiedFromTeammate;
        });

      // Add unsecured vehicles as carryovers to this week
      const batch = writeBatch(db);

      for (const vehicle of unsecuredVehicles) {
        const newVehicleRef = doc(collection(db, 'users', userId, 'vehicleWeeks', weekId, 'vehicles'));
        
        // Remove the id field to avoid Firestore error
        const { id, ...vehicleWithoutId } = vehicle;

        batch.set(newVehicleRef, {
          ...vehicleWithoutId,
          carriedOver: true,
          originalWeekId: previousWeek.id,
          carriedOverTimestamp: new Date(),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      }

      // Mark week as having carryovers initialized
      batch.update(weekRef, {
        carryOverInitialized: true,
        updatedAt: serverTimestamp()
      });

      await batch.commit();

      console.log(`✅ Carried over ${unsecuredVehicles.length} vehicles from previous week`);
    } else {
      // No previous week found, just mark as initialized
      await updateDoc(weekRef, {
        carryOverInitialized: true,
        updatedAt: serverTimestamp()
      });
    }

  } catch (error) {
    console.error('❌ Error initializing carryovers:', error);
  }
};

// Update vehicle week stats
export const updateVehicleWeekStats = async (db, userId, weekId) => {
  try {
    const vehiclesQuery = query(
      collection(db, 'users', userId, 'vehicleWeeks', weekId, 'vehicles')
    );

    const vehiclesSnapshot = await getDocs(vehiclesQuery);
    const vehiclesData = vehiclesSnapshot.docs.map(doc => doc.data());

    // Corrected logic: Only count non-carryover vehicles as "found"
    const totalFound = vehiclesData.filter(v =>
      !v.carriedOver && (v.status === 'FOUND' || v.status === 'SECURED' || v.status === 'PENDING PICKUP')
    ).length;

    // Total secured includes both new and carryover vehicles that are secured
    const totalSecured = vehiclesData.filter(v => v.status === 'SECURED').length;

    // Calculate recovery rate based on weekly found only
    const recoveryRate = totalFound > 0 ? (totalSecured / totalFound) * 100 : 0;

    const weekDoc = await getDoc(doc(db, 'users', userId, 'vehicleWeeks', weekId));
    const currentScans = weekDoc.exists() ? (weekDoc.data().totalScans || 0) : 0;

    await updateDoc(doc(db, 'users', userId, 'vehicleWeeks', weekId), {
      totalFound,
      totalSecured,
      recoveryRate,
      updatedAt: serverTimestamp()
    });

    return {
      totalScans: currentScans,
      totalFound,
      totalSecured,
      recoveryRate,
      weekData: weekDoc.exists() ? weekDoc.data() : null
    };

  } catch (error) {
    console.error("Error updating vehicle week stats:", error);
    return null;
  }
};

// Team sync - mark VIN as secured across team
export const markVINAsSecuredAcrossTeam = async (db, teamId, securedByUserId, vin, securedDate, securedByUserName) => {
  if (!db || !teamId || !vin) return;

  try {
    console.log(`🔄 Marking VIN ${vin} as secured across team ${teamId} by user ${securedByUserId}`);

    const teamMembersCollection = collection(db, `teams/${teamId}/teamMembers`);
    const teamMembersSnapshot = await getDocs(teamMembersCollection);
    const memberIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);

    const batch = writeBatch(db);
    let updateCount = 0;

    for (const userId of memberIds) {
      if (userId === securedByUserId) continue;

      try {
        const weeksQuery = query(
          collection(db, 'users', userId, 'vehicleWeeks'),
          orderBy('startDate', 'desc')
        );

        const weeksSnapshot = await getDocs(weeksQuery);

        for (const weekDoc of weeksSnapshot.docs) {
          const weekId = weekDoc.id;

          const vehiclesQuery = query(
            collection(db, 'users', userId, 'vehicleWeeks', weekId, 'vehicles')
          );

          const vehiclesSnapshot = await getDocs(vehiclesQuery);

          vehiclesSnapshot.forEach(vehicleDoc => {
            const vehicleData = vehicleDoc.data();
            const vehicleVin = vehicleData.vin || vehicleData.VIN;

            if (vehicleVin === vin && vehicleData.status !== 'SECURED') {
              console.log(`🔄 Updating vehicle ${vehicleData.vehicle} for user ${userId} in week ${weekId}`);

              batch.update(vehicleDoc.ref, {
                status: 'SECURED',
                securedDate: securedDate,
                securedTimestamp: new Date(),
                securedByTeammate: true,
                securedByUserId: securedByUserId,
                securedByUserName: securedByUserName,
                autoSecuredFromTeam: true,
                teamSyncTimestamp: new Date(),
                updatedAt: serverTimestamp()
              });

              updateCount++;
            }
          });
        }
      } catch (error) {
        console.error(`❌ Error processing user ${userId}:`, error);
      }
    }

    if (updateCount > 0) {
      await batch.commit();
      console.log(`✅ Successfully updated ${updateCount} vehicles across team for VIN ${vin}`);
      return { success: true, updateCount };
    }

    return { success: false, updateCount: 0 };

  } catch (error) {
    console.error("❌ Error marking VIN as secured across team:", error);
    return { success: false, error: error.message };
  }
};

// Upload image with metadata
export const handleImageUpload = async (storage, user, files) => {
  if (!storage || !user) return [];

  const uploadedImages = [];
  const totalFiles = files.length;

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    try {
      // Compress image
      const compressedBlob = await compressImage(file);

      // Get geolocation
      const location = await getGeolocation();

      // Create metadata
      const metadata = {
        customMetadata: {
          uploadedBy: user.displayName || user.email,
          uploadedByUserId: user.id,
          uploadTime: new Date().toISOString(),
          originalName: file.name,
          latitude: location ? location.latitude.toString() : 'unknown',
          longitude: location ? location.longitude.toString() : 'unknown',
          accuracy: location ? location.accuracy.toString() : 'unknown'
        }
      };

      // Create unique filename
      const timestamp = Date.now();
      const fileName = `found_car_pics/${user.id}/${timestamp}_${file.name}`;

      // Upload to Firebase Storage
      const storageRef = ref(storage, fileName);
      const snapshot = await uploadBytes(storageRef, compressedBlob, metadata);

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      uploadedImages.push({
        url: downloadURL,
        name: file.name,
        uploadTime: new Date().toISOString(),
        location: location,
        path: fileName
      });

    } catch (error) {
      console.error("Error uploading image:", error);
      throw error;
    }
  }

  return uploadedImages;
};

// Load never secured vehicles
export const loadNeverSecuredVehicles = async (db, teamId) => {
  if (!db || !teamId) return [];

  try {
    // Use the team-specific collection
    const neverSecuredRef = collection(db, 'teams', teamId, 'neverSecuredVehicles');
    const snapshot = await getDocs(neverSecuredRef);
    
    const vehicles = [];
    snapshot.forEach(doc => {
      vehicles.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    // Sort by date, newest first
    vehicles.sort((a, b) => {
      const dateA = new Date(a.lastAttemptDate || a.createdAt);
      const dateB = new Date(b.lastAttemptDate || b.createdAt);
      return dateB - dateA;
    });
    
    return vehicles;
  } catch (error) {
    console.error("Error loading never secured vehicles:", error);
    return [];
  }
};

// FIXED: Mark team vehicle with bottom status - DO NOT ADD TO DRIVER'S LIST
export const markTeamVehicleBottomStatus = async (db, user, team, selectedWeek, teamVehicle, bottomStatus) => {
  if (!db || !user || !selectedWeek) throw new Error('Missing required parameters');

  try {
    // Get current bottom status count
    const currentCount = teamVehicle.bottomStatusCount || 0;
    const newCount = currentCount + 1;

    // REMOVED: The section that was adding the vehicle to driver's list
    // Now we only update the original vehicle with bottom status

    // If this is the third attempt, add to never secured list
    if (newCount >= 3) {
      // Add to team's never secured collection
      const neverSecuredData = {
        ...teamVehicle,
        bottomStatus: bottomStatus,
        bottomStatusCount: newCount,
        attemptCount: newCount,
        lastAttemptDate: new Date().toISOString(),
        markedByUserId: user.id,
        markedByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
        autoMovedAfter3Attempts: true,
        createdAt: serverTimestamp()
      };
      
      // Remove fields that shouldn't be in the never secured collection
      delete neverSecuredData.id;
      delete neverSecuredData.uniqueKey;

      await addDoc(collection(db, 'teams', team.id, 'neverSecuredVehicles'), neverSecuredData);

      // Update the original vehicle to mark as never secured
      await updateDoc(
        doc(db, 'users', teamVehicle.teamMemberId, 'vehicleWeeks', teamVehicle.weekId, 'vehicles', teamVehicle.id),
        {
          bottomStatus: bottomStatus,
          bottomStatusCount: newCount,
          bottomStatusDate: new Date().toISOString(),
          bottomStatusByUserId: user.id,
          bottomStatusByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
          isNeverSecured: true,
          neverSecuredDate: new Date().toISOString(),
          updatedAt: serverTimestamp()
        }
      );

      return { success: true, isNeverSecured: true, newCount };
    } else {
      // Just update the original vehicle with bottom status
      await updateDoc(
        doc(db, 'users', teamVehicle.teamMemberId, 'vehicleWeeks', teamVehicle.weekId, 'vehicles', teamVehicle.id),
        {
          bottomStatus: bottomStatus,
          bottomStatusCount: newCount,
          bottomStatusDate: new Date().toISOString(),
          bottomStatusByUserId: user.id,
          bottomStatusByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
          updatedAt: serverTimestamp()
        }
      );

      return { success: true, isNeverSecured: false, newCount };
    }
  } catch (error) {
    console.error("Error marking bottom status:", error);
    throw error;
  }
};

// Reset team vehicle status
export const recheckTeamVehicle = async (db, user, teamVehicle) => {
  if (!db || !user) throw new Error('Missing required parameters');

  try {
    await updateDoc(
      doc(db, 'users', teamVehicle.teamMemberId, 'vehicleWeeks', teamVehicle.weekId, 'vehicles', teamVehicle.id),
      {
        bottomStatus: null,
        bottomStatusCount: 0,
        bottomStatusDate: null,
        bottomStatusByUserId: null,
        bottomStatusByUserName: null,
        updatedAt: serverTimestamp()
      }
    );

    return { success: true };
  } catch (error) {
    console.error("Error resetting vehicle status:", error);
    throw error;
  }
};