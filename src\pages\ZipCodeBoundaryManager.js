import L from 'leaflet';

/**
 * Adds CSS styles for ZIP code boundaries to the document
 */
export const addZipCodeStyles = () => {
  if (!document.getElementById('zipcode-boundary-styles')) {
    const styleEl = document.createElement('style');
    styleEl.id = 'zipcode-boundary-styles';
    styleEl.textContent = `
      .zip-boundary {
        transition: all 0.2s ease;
      }
      
      .zip-boundary:hover {
        fill-opacity: 0.3 !important;
        weight: 2 !important;
        cursor: pointer;
      }
      
      .zip-tooltip {
        background: rgba(0, 0, 0, 0.7) !important;
        border: none !important;
        color: white !important;
        font-weight: bold;
        padding: 2px 6px !important;
      }
      
      /* State-specific styles */
      .zip-boundary.il:hover {
        fill-opacity: 0.4 !important;
      }
      
      .zip-boundary.in:hover {
        fill-opacity: 0.4 !important;
      }
      
      .zip-boundary.wi:hover {
        fill-opacity: 0.4 !important;
      }
      
      /* ZIP code popup styles */
      .zip-popup {
        padding: 6px 10px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        border-radius: 6px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.15);
      }
      
      .zip-popup h4 {
        margin: 0 0 8px;
        font-size: 16px;
        font-weight: 600;
      }
      
      .zip-popup-btn {
        display: inline-block;
        margin-top: 8px;
        padding: 4px 10px;
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: background 0.2s;
      }
      
      .zip-popup-btn:hover {
        background: #2563eb;
      }
      
      .zip-popup-btn.remove {
        background: #ef4444;
      }
      
      .zip-popup-btn.remove:hover {
        background: #dc2626;
      }
    `;
    document.head.appendChild(styleEl);
  }
};

/**
 * ZIP Code Boundaries Manager with improved error handling and fallbacks
 */
const ZipCodeBoundaryManager = {
  // Basic properties
  isLoaded: false,
  isLoading: false,
  boundariesVisible: false,
  stateGeoJsonPaths: {
    'il': '/data/il-zip-codes.geojson',    // Illinois
    'in': '/data/in-zip-codes.geojson',    // Indiana
    'wi': '/data/wi-zip-codes.geojson'     // Wisconsin
  },
  // Add fallback paths for each state
  fallbackPaths: {
    'il': 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/il_illinois_zip_codes_geo.min.json',
    'in': 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/in_indiana_zip_codes_geo.min.json',
    'wi': 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/wi_wisconsin_zip_codes_geo.min.json'
  },
  // CDN fallback paths (more reliable than GitHub raw)
  cdnFallbackPaths: {
    'il': 'https://cdn.jsdelivr.net/gh/OpenDataDE/State-zip-code-GeoJSON@master/il_illinois_zip_codes_geo.min.json',
    'in': 'https://cdn.jsdelivr.net/gh/OpenDataDE/State-zip-code-GeoJSON@master/in_indiana_zip_codes_geo.min.json',
    'wi': 'https://cdn.jsdelivr.net/gh/OpenDataDE/State-zip-code-GeoJSON@master/wi_wisconsin_zip_codes_geo.min.json'
  },
  stateLabels: {
    'il': 'Illinois',
    'in': 'Indiana',
    'wi': 'Wisconsin'
  },
  stateFeatureCollections: {
    'il': null,
    'in': null,
    'wi': null
  },
  layerGroups: { zoomedOut: null, zoomedIn: null },
  selectedZipCodes: [],
  loadAttempts: { 'il': 0, 'in': 0, 'wi': 0 },
  maxAttempts: 3, // Maximum number of attempts per state

  /**
   * Load the GeoJSON files for all states - IMPROVED IMPLEMENTATION
   * @param {Object} mapRefs - References to map objects
   * @returns {Promise<boolean>} - True if successful, false otherwise
   */
  loadGeoJson: async function (mapRefs) {
    if (this.isLoading) {
      console.log("ZIP boundary loading already in progress");
      return false;
    }
    
    this.isLoading = true;

    try {
      console.log("Loading ZIP code boundaries for multiple states");
      
      // Validation check - ensure we have valid file paths
      if (!this.stateGeoJsonPaths || Object.keys(this.stateGeoJsonPaths).length === 0) {
        console.error("No valid file paths configured for ZIP code boundaries");
        this.isLoading = false;
        return false;
      }
      
      // Load all state files separately and track success
      let loadedStates = [];
      const statePromises = [];
      
      // Process each state - combine in Promise.allSettled for better performance
      for (const stateCode of ['il', 'in', 'wi']) {
        // Skip already loaded states
        if (this.stateFeatureCollections[stateCode] && this.stateFeatureCollections[stateCode].features) {
          loadedStates.push(stateCode);
          console.log(`State ${stateCode.toUpperCase()} already loaded with ${this.stateFeatureCollections[stateCode].features.length} features, skipping`);
          continue;
        }
        
        // Increment attempt counter for this state
        this.loadAttempts[stateCode] = (this.loadAttempts[stateCode] || 0) + 1;
        
        console.log(`Attempting to load state ${stateCode.toUpperCase()}, attempt #${this.loadAttempts[stateCode]}`);
        
        // Create a promise for this state's loading
        const statePromise = this.loadStateGeoJson(stateCode)
          .then(data => {
            if (data && data.features && data.features.length > 0) {
              console.log(`Successfully loaded ${data.features.length} ZIP boundaries for ${this.stateLabels[stateCode]}`);
              this.stateFeatureCollections[stateCode] = data;
              loadedStates.push(stateCode);
              return true;
            } else {
              console.warn(`Failed to load ${stateCode.toUpperCase()} ZIP boundaries - no data returned`);
              return false;
            }
          })
          .catch(error => {
            console.error(`Error loading ${stateCode.toUpperCase()} ZIP boundaries:`, error);
            return false;
          });
          
        statePromises.push(statePromise);
      }
      
      // Wait for all state loading attempts to complete
      const promiseResults = await Promise.allSettled(statePromises);
      
      // Log detailed results of loading attempts
      console.log('ZIP code boundary loading results:', promiseResults.map((result, index) => {
        const stateCode = ['il', 'in', 'wi'][index];
        return {
          state: stateCode.toUpperCase(),
          status: result.status,
          value: result.value
        };
      }));

      // If we failed to load any real data, generate dummy data
      if (loadedStates.length === 0) {
        console.log("No state data loaded successfully, generating dummy data");
        this.generateDummyZipData();
        loadedStates.push('dummy');
      }

      // Update loaded status - we're successful if at least one state loaded
      this.isLoaded = loadedStates.length > 0;
      
      console.log(`ZIP boundaries loaded: ${loadedStates.join(', ').toUpperCase()}`);
      
      // If maps are ready, display boundaries
      if (this.boundariesVisible && mapRefs.zoomedOutMapRef?.current && mapRefs.zoomedInMapRef?.current) {
        this.displayBoundaries(mapRefs.zoomedOutMapRef.current, mapRefs.zoomedInMapRef.current);
      }

      this.isLoading = false;
      return this.isLoaded;
    } catch (error) {
      console.error("Error loading ZIP code boundaries:", error);
      
      // Try to generate dummy data as a last resort
      if (!this.isLoaded) {
        console.log("Generating dummy ZIP data as fallback");
        this.generateDummyZipData();
        this.isLoaded = true;
      }
      
      this.isLoading = false;
      return this.isLoaded;
    }
  },

  /**
   * Helper to load a single state's GeoJSON with better error handling and fallbacks
   * @param {string} stateCode - The state code (e.g., 'il', 'in', 'wi')
   * @returns {Promise<Object|null>} - GeoJSON object or null on failure
   */
  loadStateGeoJson: async function(stateCode) {
    if (!stateCode || !this.stateGeoJsonPaths[stateCode]) {
      console.error(`Invalid state code or missing path: ${stateCode}`);
      return null;
    }
    
    // Determine paths to try
    const primaryPath = this.stateGeoJsonPaths[stateCode];
    const fallbackPath = this.fallbackPaths[stateCode];
    const cdnPath = this.cdnFallbackPaths[stateCode];
    const stateName = this.stateLabels[stateCode] || stateCode.toUpperCase();
    
    // Try up to 3 different paths with proper error handling
    try {
      // Strategy 1: Try primary path first
      try {
        console.log(`Loading ${stateName} ZIP boundaries from primary path: ${primaryPath}`);
        
        const response = await fetch(primaryPath, {
          method: 'GET',
          headers: {
            'Accept': 'application/json, application/geo+json',
            'Cache-Control': 'no-cache'
          },
          timeout: 5000 // 5 second timeout
        });
        
        if (!response.ok) {
          console.warn(`Server returned ${response.status} for ${primaryPath}, trying fallback...`);
          throw new Error(`HTTP error ${response.status}`);
        }
        
        // Get text and verify it looks like JSON before parsing
        const text = await response.text();
        
        // Basic check - must start with { or [
        if (!text.trim().startsWith('{') && !text.trim().startsWith('[')) {
          console.warn(`Response from ${primaryPath} is not valid JSON`);
          throw new Error('Response is not valid JSON');
        }
        
        // Parse JSON with error handling
        const data = JSON.parse(text);
        
        // Validate it's a GeoJSON
        if (!data.type || data.type !== 'FeatureCollection' || !Array.isArray(data.features)) {
          console.warn(`Response from ${primaryPath} is not a valid GeoJSON FeatureCollection`);
          throw new Error('Not a valid GeoJSON FeatureCollection');
        }
        
        console.log(`Successfully loaded ${stateName} GeoJSON from primary path with ${data.features.length} features`);
        return data;
      } catch (primaryError) {
        console.warn(`Primary path failed for ${stateName}:`, primaryError);
        
        // Strategy 2: Try GitHub fallback
        try {
          console.log(`Trying GitHub fallback for ${stateName}: ${fallbackPath}`);
          
          const fallbackResponse = await fetch(fallbackPath, {
            method: 'GET',
            headers: {
              'Accept': 'application/json, application/geo+json',
              'Cache-Control': 'no-cache'
            },
            timeout: 10000 // 10 second timeout for GitHub (can be slower)
          });
          
          if (!fallbackResponse.ok) {
            console.warn(`GitHub fallback returned ${fallbackResponse.status}, trying CDN...`);
            throw new Error(`Fallback HTTP error ${fallbackResponse.status}`);
          }
          
          const fallbackText = await fallbackResponse.text();
          
          // Basic validation
          if (!fallbackText.trim().startsWith('{') && !fallbackText.trim().startsWith('[')) {
            console.warn(`GitHub fallback response is not valid JSON`);
            throw new Error('GitHub fallback response is not valid JSON');
          }
          
          const fallbackData = JSON.parse(fallbackText);
          
          // Validate it's a GeoJSON
          if (!fallbackData.type || fallbackData.type !== 'FeatureCollection' || !Array.isArray(fallbackData.features)) {
            console.warn('GitHub fallback is not a valid GeoJSON FeatureCollection');
            throw new Error('GitHub fallback is not a valid GeoJSON FeatureCollection');
          }
          
          console.log(`Successfully loaded ${stateName} GeoJSON from GitHub with ${fallbackData.features.length} features`);
          return fallbackData;
        } catch (githubError) {
          console.warn(`GitHub fallback failed for ${stateName}:`, githubError);
          
          // Strategy 3: Try CDN fallback (most reliable)
          try {
            console.log(`Trying CDN fallback for ${stateName}: ${cdnPath}`);
            
            const cdnResponse = await fetch(cdnPath, {
              method: 'GET',
              headers: {
                'Accept': 'application/json, application/geo+json',
                'Cache-Control': 'no-cache'
              },
              timeout: 8000 // 8 second timeout for CDN
            });
            
            if (!cdnResponse.ok) {
              console.error(`CDN fallback returned ${cdnResponse.status}, all paths failed`);
              throw new Error(`CDN HTTP error ${cdnResponse.status}`);
            }
            
            const cdnText = await cdnResponse.text();
            
            // Basic validation
            if (!cdnText.trim().startsWith('{') && !cdnText.trim().startsWith('[')) {
              console.error(`CDN fallback response is not valid JSON`);
              throw new Error('CDN fallback response is not valid JSON');
            }
            
            const cdnData = JSON.parse(cdnText);
            
            // Validate it's a GeoJSON
            if (!cdnData.type || cdnData.type !== 'FeatureCollection' || !Array.isArray(cdnData.features)) {
              console.error('CDN fallback is not a valid GeoJSON FeatureCollection');
              throw new Error('CDN fallback is not a valid GeoJSON FeatureCollection');
            }
            
            console.log(`Successfully loaded ${stateName} GeoJSON from CDN with ${cdnData.features.length} features`);
            return cdnData;
          } catch (cdnError) {
            console.error(`All paths failed for ${stateName}:`, cdnError);
            return null;
          }
        }
      }
    } catch (error) {
      console.error(`Unexpected error loading ${stateName}:`, error);
      return null;
    }
  },

  /**
   * Generate dummy ZIP code data for testing
   * @returns {Object} GeoJSON object with dummy features
   */
  generateDummyZipData: function() {
    console.log("Generating dummy ZIP code data");
    
    // Create a dummy GeoJSON feature collection centered on Chicago
    const chicagoLat = 41.8781;
    const chicagoLng = -87.6298;
    
    // Create dummy features
    const features = [];
    const zipCodes = ["60601", "60602", "60603", "60604", "60605", 
                     "60606", "60607", "60608", "60609", "60610"];
    
    // Helper to create a hexagon polygon
    const createHexagon = (centerLat, centerLng, radius, zipCode) => {
      const points = [];
      for (let i = 0; i < 6; i++) {
        const angle = (Math.PI / 180) * (60 * i);
        const lat = centerLat + radius * Math.cos(angle);
        const lng = centerLng + radius * Math.sin(angle);
        points.push([lng, lat]);
      }
      // Close the polygon
      points.push(points[0]);
      
      return {
        type: "Feature",
        properties: {
          ZCTA5CE10: zipCode,
          STATE: "IL",
          COUNTY: "Cook",
          stateCode: "il"
        },
        geometry: {
          type: "Polygon",
          coordinates: [points]
        }
      };
    };
    
    // Generate ZIP code polygons in a circle around Chicago
    zipCodes.forEach((zipCode, index) => {
      const angle = (Math.PI / 180) * (36 * index);
      const distance = 0.05; // ~5km
      const lat = chicagoLat + distance * Math.cos(angle);
      const lng = chicagoLng + distance * Math.sin(angle);
      
      // Create hexagonal area
      const feature = createHexagon(lat, lng, 0.015, zipCode);
      features.push(feature);
    });
    
    // Create the GeoJSON object
    const dummyData = {
      type: "FeatureCollection",
      features: features
    };
    
    // Store in state collections
    this.stateFeatureCollections.il = dummyData;
    
    console.log(`Generated ${features.length} dummy ZIP boundaries`);
    return dummyData;
  },

  /**
   * Get features for all states - IMPROVED TO HANDLE DEBUGGING
   * @returns {Array} - Array of GeoJSON features
   */
  getFeatures: function() {
    // Combine all states' features
    const allFeatures = [];
    let featureCounts = {};
    
    Object.entries(this.stateFeatureCollections).forEach(([stateCode, collection]) => {
      if (collection && collection.features && Array.isArray(collection.features)) {
        // Log the number of features found
        featureCounts[stateCode] = collection.features.length;
        
        // Tag features with their state for easier identification
        const stateFeatures = collection.features.map(feature => ({
          ...feature,
          properties: {
            ...feature.properties,
            stateCode: stateCode
          }
        }));
        allFeatures.push(...stateFeatures);
      } else {
        featureCounts[stateCode] = 0;
      }
    });
    
    // Debug log
    console.log(`ZIP boundary features by state:`, featureCounts);
    console.log(`Total ZIP boundaries to display: ${allFeatures.length}`);
    
    return allFeatures;
  },

  /**
   * Display boundaries with performance optimizations
   * @param {L.Map} zoomedOutMap - The zoomed out map instance
   * @param {L.Map} zoomedInMap - The zoomed in map instance
   * @returns {boolean} - True if boundaries displayed, false otherwise
   */
  displayBoundaries: function (zoomedOutMap, zoomedInMap) {
    if (!zoomedOutMap || !zoomedInMap) {
      console.warn("Cannot display ZIP boundaries: Maps not available");
      return false;
    }
    
    if (!this.isLoaded) {
      console.log("No GeoJSON data loaded");
      return false;
    }

    // Get features for all states
    const features = this.getFeatures();
    if (!features || features.length === 0) {
      console.log("No features to display");
      return false;
    }

    // Remove existing layers
    this.clearBoundaries(zoomedOutMap, zoomedInMap);

    // Create new layer groups
    this.layerGroups.zoomedOut = L.layerGroup().addTo(zoomedOutMap);
    this.layerGroups.zoomedIn = L.layerGroup().addTo(zoomedInMap);

    // Get current view parameters
    const bounds = zoomedOutMap.getBounds();
    const zoom = zoomedOutMap.getZoom();

    // Optimize rendering based on zoom level
    let displayLimit = 300; // Default max features to display
    let skipFactor = 1; // Display every feature by default

    if (zoom < 10) {
      displayLimit = 100;
      skipFactor = 5; // Display only 1 out of 5 features
    } else if (zoom < 12) {
      displayLimit = 200;
      skipFactor = 2; // Display 1 out of 2 features
    }

    // Only process features in the visible area
    let featuresInView = features
      .filter((feature, index) => {
        // Apply skip factor for performance
        if (index % skipFactor !== 0) return false;

        // Skip features without valid properties
        if (!feature.properties || !feature.geometry) return false;

        // Get zip code
        const zipCode = feature.properties.ZCTA5CE10 ||
          feature.properties.ZIP ||
          feature.properties.zipCode ||
          feature.properties.zip ||
          feature.properties.postalCode;

        if (!zipCode) return false;

        // Always include selected ZIP codes
        if (this.selectedZipCodes.includes(zipCode)) return true;

        // Basic check if feature might be in view (approximation)
        return true;
      })
      .slice(0, displayLimit); // Limit number of features

    console.log(`Displaying ${featuresInView.length} ZIP boundaries`);

    // Process features in batches to prevent UI freezing
    const processBatch = (features, startIndex) => {
      const batchSize = 20; // Increased for faster display
      const endIndex = Math.min(startIndex + batchSize, features.length);

      for (let i = startIndex; i < endIndex; i++) {
        const feature = features[i];

        try {
          // Get zip code
          const zipCode = feature.properties.ZCTA5CE10 ||
            feature.properties.ZIP ||
            feature.properties.zipCode ||
            feature.properties.zip ||
            feature.properties.postalCode;

          // Skip invalid features
          if (!feature.geometry || !zipCode) continue;

          // Check if selected
          const isSelected = this.selectedZipCodes.includes(zipCode);
          
          // Determine state for color differentiation
          const stateCode = feature.properties.stateCode || 'unknown';
          
          // Assign different colors by state
          let stateColor;
          switch(stateCode) {
            case 'il': stateColor = '#3b82f6'; break; // Blue for Illinois
            case 'in': stateColor = '#8b5cf6'; break; // Purple for Indiana
            case 'wi': stateColor = '#f97316'; break; // Orange for Wisconsin
            default: stateColor = '#3b82f6';
          }

          // Create polygon
          const polygonOptions = {
            color: isSelected ? '#10b981' : stateColor,
            weight: isSelected ? 2 : 1,
            opacity: 0.8,
            fillColor: isSelected ? '#10b981' : stateColor,
            fillOpacity: isSelected ? 0.4 : 0.2,
            className: `zip-boundary ${stateCode}`,
            interactive: true
          };

          // Create GeoJSON layer
          const layer = L.geoJSON(feature, {
            style: polygonOptions
          });

          // Add tooltip with state abbreviation for multi-state view
          const tooltipContent = `${zipCode} (${stateCode.toUpperCase()})`;
            
          layer.bindTooltip(tooltipContent, {
            permanent: false,
            direction: 'center',
            className: 'zip-tooltip'
          });

          // Add popup for more information and selection
          const popupContent = this.createZipPopup(zipCode, stateCode);
          layer.bindPopup(popupContent, {
            closeButton: true,
            className: 'zip-popup'
          });

          // Add click handler
          layer.on('click', (e) => {
            // Stop propagation
            if (e && e.originalEvent) {
              L.DomEvent.stopPropagation(e);
            }

            // Open popup
            layer.openPopup();
          });

          // Add to both maps
          if (this.layerGroups.zoomedOut) {
            this.layerGroups.zoomedOut.addLayer(layer);
          }

          // Add to zoomed in map with same properties
          if (this.layerGroups.zoomedIn) {
            const layerClone = L.geoJSON(feature, {
              style: polygonOptions
            });

            layerClone.bindTooltip(tooltipContent, {
              permanent: false,
              direction: 'center',
              className: 'zip-tooltip'
            });

            layerClone.bindPopup(popupContent, {
              closeButton: true,
              className: 'zip-popup'
            });

            layerClone.on('click', (e) => {
              if (e && e.originalEvent) {
                L.DomEvent.stopPropagation(e);
              }
              layerClone.openPopup();
            });

            this.layerGroups.zoomedIn.addLayer(layerClone);
          }
        } catch (error) {
          console.warn("Error processing boundary:", error);
        }
      }

      // Process next batch if not done
      if (endIndex < features.length) {
        setTimeout(() => {
          processBatch(features, endIndex);
        }, 0);
      }
    };

    // Start processing
    processBatch(featuresInView, 0);

    this.boundariesVisible = true;
    return true;
  },

  /**
   * Create a popup for a ZIP code
   * @param {string} zipCode - The ZIP code
   * @param {string} stateCode - The state code
   * @returns {HTMLElement} - The popup content
   */
  createZipPopup: function(zipCode, stateCode) {
    const container = document.createElement('div');
    
    // Add title
    const title = document.createElement('h4');
    title.textContent = `ZIP Code: ${zipCode}`;
    container.appendChild(title);
    
    // Add information
    const info = document.createElement('div');
    info.innerHTML = `<p>State: ${this.stateLabels[stateCode] || stateCode.toUpperCase()}</p>`;
    container.appendChild(info);
    
    // Add selection button
    const isSelected = this.selectedZipCodes.includes(zipCode);
    const button = document.createElement('button');
    
    if (isSelected) {
      button.textContent = 'Remove Selection';
      button.className = 'zip-popup-btn remove';
    } else {
      button.textContent = 'Select This ZIP';
      button.className = 'zip-popup-btn';
    }
    
    // Add click handler to button
    button.onclick = (e) => {
      e.stopPropagation();
      
      if (isSelected) {
        // Remove from selection
        this.selectedZipCodes = this.selectedZipCodes.filter(code => code !== zipCode);
        console.log(`Removed ZIP code ${zipCode} from selection`);
        
        // Refresh both maps to update styles
        this.refreshZipBoundaryStyles(zipCode, false);
      } else {
        // Add to selection
        this.selectedZipCodes.push(zipCode);
        console.log(`Added ZIP code ${zipCode} to selection`);
        
        // Refresh both maps to update styles
        this.refreshZipBoundaryStyles(zipCode, true);
      }
      
      // Close any open popups
      if (L.DomUtil.get('leaflet-popup-close-button')) {
        L.DomUtil.get('leaflet-popup-close-button').click();
      }
    };
    
    container.appendChild(button);
    return container;
  },

  /**
   * Update styles for a ZIP code boundary
   * @param {string} zipCode - The ZIP code to update
   * @param {boolean} isSelected - Whether the ZIP code is selected
   */
  refreshZipBoundaryStyles: function(zipCode, isSelected) {
    // Update style for zoomed out map
    if (this.layerGroups.zoomedOut) {
      this.layerGroups.zoomedOut.eachLayer(layer => {
        if (layer instanceof L.GeoJSON) {
          layer.eachLayer(subLayer => {
            try {
              const properties = subLayer.feature.properties;
              const layerZipCode = properties.ZCTA5CE10 || 
                properties.ZIP || 
                properties.zipCode || 
                properties.zip || 
                properties.postalCode;
              
              if (layerZipCode === zipCode) {
                const stateCode = properties.stateCode || 'unknown';
                let stateColor;
                
                switch(stateCode) {
                  case 'il': stateColor = '#3b82f6'; break;
                  case 'in': stateColor = '#8b5cf6'; break;
                  case 'wi': stateColor = '#f97316'; break;
                  default: stateColor = '#3b82f6';
                }
                
                subLayer.setStyle({
                  color: isSelected ? '#10b981' : stateColor,
                  weight: isSelected ? 2 : 1,
                  fillColor: isSelected ? '#10b981' : stateColor,
                  fillOpacity: isSelected ? 0.4 : 0.2
                });
                
                // Update popup content
                if (subLayer.getPopup()) {
                  subLayer.setPopupContent(this.createZipPopup(zipCode, stateCode));
                }
              }
            } catch (e) {
              // Ignore errors for individual layers
            }
          });
        }
      });
    }
    
    // Update style for zoomed in map
    if (this.layerGroups.zoomedIn) {
      this.layerGroups.zoomedIn.eachLayer(layer => {
        if (layer instanceof L.GeoJSON) {
          layer.eachLayer(subLayer => {
            try {
              const properties = subLayer.feature.properties;
              const layerZipCode = properties.ZCTA5CE10 || 
                properties.ZIP || 
                properties.zipCode || 
                properties.zip || 
                properties.postalCode;
              
              if (layerZipCode === zipCode) {
                const stateCode = properties.stateCode || 'unknown';
                let stateColor;
                
                switch(stateCode) {
                  case 'il': stateColor = '#3b82f6'; break;
                  case 'in': stateColor = '#8b5cf6'; break;
                  case 'wi': stateColor = '#f97316'; break;
                  default: stateColor = '#3b82f6';
                }
                
                subLayer.setStyle({
                  color: isSelected ? '#10b981' : stateColor,
                  weight: isSelected ? 2 : 1,
                  fillColor: isSelected ? '#10b981' : stateColor,
                  fillOpacity: isSelected ? 0.4 : 0.2
                });
                
                // Update popup content
                if (subLayer.getPopup()) {
                  subLayer.setPopupContent(this.createZipPopup(zipCode, stateCode));
                }
              }
            } catch (e) {
              // Ignore errors for individual layers
            }
          });
        }
      });
    }
  },

  /**
   * Clear boundaries from maps
   * @param {L.Map} zoomedOutMap - The zoomed out map instance
   * @param {L.Map} zoomedInMap - The zoomed in map instance
   */
  clearBoundaries: function (zoomedOutMap, zoomedInMap) {
    try {
      if (this.layerGroups.zoomedOut && zoomedOutMap) {
        zoomedOutMap.removeLayer(this.layerGroups.zoomedOut);
        this.layerGroups.zoomedOut = null;
      }

      if (this.layerGroups.zoomedIn && zoomedInMap) {
        zoomedInMap.removeLayer(this.layerGroups.zoomedIn);
        this.layerGroups.zoomedIn = null;
      }

      this.boundariesVisible = false;
    } catch (error) {
      console.warn("Error clearing ZIP boundaries:", error);
    }
  },

  /**
   * Toggle boundaries visibility
   * @param {L.Map} zoomedOutMap - The zoomed out map instance
   * @param {L.Map} zoomedInMap - The zoomed in map instance
   * @returns {boolean} - New visibility state
   */
  toggleBoundaries: function (zoomedOutMap, zoomedInMap) {
    if (this.boundariesVisible) {
      this.clearBoundaries(zoomedOutMap, zoomedInMap);
      return false;
    } else {
      // If data is loaded, display it
      if (this.isLoaded) {
        this.displayBoundaries(zoomedOutMap, zoomedInMap);
      } else if (!this.isLoading) {
        // Otherwise load it first
        this.loadGeoJson({
          zoomedOutMapRef: { current: zoomedOutMap },
          zoomedInMapRef: { current: zoomedInMap }
        }).then(success => {
          if (success) {
            this.displayBoundaries(zoomedOutMap, zoomedInMap);
          } else {
            console.error("Failed to load ZIP boundaries");
          }
        });
      }
      return true;
    }
  }
};

export default ZipCodeBoundaryManager;