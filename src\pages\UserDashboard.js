import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Bar, Pie, Doughnut } from 'react-chartjs-2';
import { collection, getDocs, doc, getDoc, query, where, orderBy, onSnapshot } from 'firebase/firestore';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

// TimeCardStatus component to show real-time clock status
const TimeCardStatus = ({ userId, db, timeCardData }) => {
  const [timeCardInfo, setTimeCardInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [elapsedTime, setElapsedTime] = useState('00:00:00');
  const timerRef = useRef(null);
  
  useEffect(() => {
    const fetchTimeCardStatus = async () => {
      if (!db || !userId) return;
      
      try {
        setLoading(true);
        
        // Check if we have cached data first
        if (timeCardData && timeCardData.currentStatus) {
          setTimeCardInfo(timeCardData.currentStatus);
          if (timeCardData.currentStatus.clockedIn && timeCardData.currentStatus.clockInTime) {
            setElapsedTime(calculateElapsedTime(timeCardData.currentStatus.clockInTime));
          }
          setLoading(false);
          return;
        }
        
        // If no cached data, fetch from database
        const timeCardRef = doc(db, 'timeCards', userId);
        const timeCardDoc = await getDoc(timeCardRef);
        
        if (timeCardDoc.exists()) {
          const data = timeCardDoc.data();
          const timeCardInfo = {
            ...data,
            clockInTime: data.clockInTime?.toDate(),
            clockOutTime: data.clockOutTime?.toDate()
          };
          setTimeCardInfo(timeCardInfo);
          
          // Calculate initial elapsed time if user is clocked in
          if (timeCardInfo.clockedIn && !timeCardInfo.clockedOut && timeCardInfo.clockInTime) {
            setElapsedTime(calculateElapsedTime(timeCardInfo.clockInTime));
          }
        } else {
          setTimeCardInfo(null);
        }
      } catch (error) {
        console.error("Error fetching time card status:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchTimeCardStatus();
    
    // Set up timer to update elapsed time
    timerRef.current = setInterval(() => {
      if (timeCardInfo?.clockedIn && !timeCardInfo?.clockedOut && timeCardInfo?.clockInTime) {
        setElapsedTime(calculateElapsedTime(timeCardInfo.clockInTime));
      }
    }, 1000);
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [db, userId, timeCardData]);
  
  // Calculate elapsed time
  const calculateElapsedTime = (startTime) => {
    if (!startTime) return '00:00:00';
    
    const elapsed = new Date() - startTime;
    const hours = Math.floor(elapsed / (1000 * 60 * 60));
    const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  
  if (loading) {
    return <div className="text-gray-400 text-xs py-1">Loading time card status...</div>;
  }
  
  if (!timeCardInfo) {
    return <div className="text-gray-400 text-xs py-1">No time card data available</div>;
  }
  
  return (
    <div className="px-2 py-1.5 rounded bg-gray-800 border border-gray-700 mb-2">
      <div className="flex items-center justify-between">
        <span className="text-xs text-gray-400">Time Card Status:</span>
        
        {timeCardInfo.clockedIn && !timeCardInfo.clockedOut ? (
          <div className="flex items-center text-green-400 text-xs">
            <span className="relative h-2 w-2 mr-1">
              <span className="absolute inline-flex h-full w-full rounded-full bg-green-500 opacity-25 animate-ping"></span>
              <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
            </span>
            <span>Active - </span>
            <span className="font-mono ml-1">{elapsedTime}</span>
          </div>
        ) : (
          <div className="text-red-400 text-xs flex items-center">
            <span className="h-2 w-2 bg-red-500 rounded-full mr-1"></span>
            <span>Not Clocked In</span>
          </div>
        )}
      </div>
    </div>
  );
};

function UserDashboard({ 
  selectedUser, 
  selectedTeam, 
  userStats, 
  userLoading, 
  isMobileMenuOpen, 
  timeFilter, 
  chartType, 
  getTimeProperty,
  db,
  timeCardData,
  // Received centralized vehicle data from parent
  vehicles,
  availableWeeks,
  selectedWeek,
  vehicleStats,
  aggregatedVehicleData
}) {
  const [loading, setLoading] = useState(false);
  
  // Chart refs to destroy old instances
  const activityChartRef = useRef(null);
  const performanceChartRef = useRef(null);
  const workDistributionChartRef = useRef(null);

  // Recent time card entries for display
  const [recentTimeCardEntries, setRecentTimeCardEntries] = useState([]);

  // Debug state to track all received vehicle data
  const [debugInfo, setDebugInfo] = useState({
    vehicleStats: {},
    aggregatedMonth: {},
    aggregatedYTD: {},
    timeCardData: {}
  });
  
  // Update debug info when stats change
  useEffect(() => {
    setDebugInfo({
      vehicleStats: {...vehicleStats},
      aggregatedMonth: {...(aggregatedVehicleData?.month || {})},
      aggregatedYTD: {...(aggregatedVehicleData?.ytd || {})},
      timeCardData: timeCardData || {}
    });
    console.log("Vehicle stats updated:", vehicleStats);
    console.log("Aggregated data updated:", aggregatedVehicleData);
    console.log("TimeCard data updated:", timeCardData);
  }, [vehicleStats, aggregatedVehicleData, timeCardData]);

  // Set up live time card data listener when selected user changes
  useEffect(() => {
    if (!db || !selectedUser?.id) return;

    // Set up a listener for the user's time card data
    const timeCardSummaryRef = doc(db, 'timeCardSummary', selectedUser.id);
    const timeCardListener = onSnapshot(timeCardSummaryRef, (doc) => {
      if (doc.exists()) {
        console.log("Live timecard update:", doc.data());
        // This will trigger a re-render with the latest time card data
      }
    });

    // Clean up listener on component unmount or user change
    return () => timeCardListener();
  }, [db, selectedUser]);

  // Fetch recent time card entries for the selected user
  useEffect(() => {
    const fetchRecentTimeCardEntries = async () => {
      if (!db || !selectedUser?.id) return;
      
      try {
        setLoading(true);
        
        // Get today's date
        const today = new Date();
        const startOfToday = new Date(today.setHours(0, 0, 0, 0));
        
        // Query for today's entries, both user and admin created
        const entriesRef = query(
          collection(db, 'timeCardHistory'),
          where('userId', '==', selectedUser.id),
          where('timestamp', '>=', startOfToday),
          orderBy('timestamp', 'desc'),
          // No filtering by adminAction - include all entries
        );
        
        const entriesSnapshot = await getDocs(entriesRef);
        const recentEntries = [];
        
        entriesSnapshot.forEach((doc) => {
          const data = doc.data();
          recentEntries.push({
            id: doc.id,
            ...data,
            timestamp: data.timestamp?.toDate()
          });
        });
        
        // Sort by timestamp, newest first
        recentEntries.sort((a, b) => b.timestamp - a.timestamp);
        
        // Take the 5 most recent entries
        setRecentTimeCardEntries(recentEntries.slice(0, 5));
        
      } catch (error) {
        console.error("Error fetching recent time card entries:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchRecentTimeCardEntries();
  }, [db, selectedUser]);

  // Ensure numeric values for stats
  const ensureNumber = (value) => {
    if (value === undefined || value === null) return 0;
    if (typeof value === 'string') {
      // Remove any commas and convert to number
      const cleanValue = value.replace(/,/g, '');
      return !isNaN(parseFloat(cleanValue)) ? parseFloat(cleanValue) : 0;
    }
    return typeof value === 'number' ? value : 0;
  };

  // Format numbers for display
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0';
    
    // Ensure we're working with a number
    const numValue = ensureNumber(num);
    
    return new Intl.NumberFormat('en-US').format(Math.round(numValue * 100) / 100);
  };

  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    const options = { 
      month: 'short', 
      day: 'numeric', 
      hour: 'numeric', 
      minute: '2-digit', 
      hour12: true 
    };
    return date.toLocaleString('en-US', options);
  };

  // Cleanup chart instances when component unmounts
  useEffect(() => {
    return () => {
      if (activityChartRef.current) {
        activityChartRef.current.destroy();
      }
      if (performanceChartRef.current) {
        performanceChartRef.current.destroy();
      }
      if (workDistributionChartRef.current) {
        workDistributionChartRef.current.destroy();
      }
    };
  }, []);

  // Cleanup charts when time filter or chart type changes
  useEffect(() => {
    if (activityChartRef.current) {
      activityChartRef.current.destroy();
      activityChartRef.current = null;
    }
    if (performanceChartRef.current) {
      performanceChartRef.current.destroy();
      performanceChartRef.current = null;
    }
    if (workDistributionChartRef.current) {
      workDistributionChartRef.current.destroy();
      workDistributionChartRef.current = null;
    }
  }, [timeFilter, chartType]);
  
  // Calculate weekly stats properly (excluding carryovers from found count)
  const calculateWeeklyStats = () => {
    if (!vehicles || vehicles.length === 0) {
      return {
        weeklyFound: 0,
        weeklySecured: 0,
        totalCarryover: 0,
        weeklyRecoveryRate: 0
      };
    }
    
    // Weekly Found: Only count vehicles found THIS week (not carryovers)
    const weeklyFound = vehicles.filter(v => 
      !v.carriedOver && (v.status === 'FOUND' || v.status === 'SECURED')
    ).length;
    
    // Weekly Secured: Count newly secured + carryover vehicles that were secured this week
    const weeklySecured = vehicles.filter(v => v.status === 'SECURED').length;
    
    // Total carryover vehicles
    const totalCarryover = vehicles.filter(v => v.carriedOver).length;
    
    // Recovery rate based on weekly found + carryover vehicles
    const totalAvailable = weeklyFound + totalCarryover;
    const weeklyRecoveryRate = totalAvailable > 0 ? (weeklySecured / totalAvailable) * 100 : 0;
    
    return {
      weeklyFound,
      weeklySecured,
      totalCarryover,
      weeklyRecoveryRate
    };
  };
  
  // Get stats value with vehicle and timecard data integration
  const getStatsValue = (basePropertyName) => {
    if (!userStats) return 0;
    
    let value = ensureNumber(userStats[getTimeProperty(basePropertyName)]) || 0;
    
    // Check if we have timecard data for hours worked
    if (basePropertyName === 'userHoursWorked' && timeCardData) {
      // Use the appropriate time filter value from timeCardData
      if (timeFilter === 'day' && timeCardData.dailyHours !== undefined) {
        value = ensureNumber(timeCardData.dailyHours);
      } else if (timeFilter === 'week' && timeCardData.weeklyHours !== undefined) {
        value = ensureNumber(timeCardData.weeklyHours);
      } else if (timeFilter === 'month' && timeCardData.monthlyHours !== undefined) {
        value = ensureNumber(timeCardData.monthlyHours);
      } else if ((timeFilter === 'ytd' || timeFilter === 'year') && timeCardData.ytdHours !== undefined) {
        value = ensureNumber(timeCardData.ytdHours);
      }
    }
    
    // Always prioritize vehicle data when available
    if (basePropertyName === 'userScans') {
      if (timeFilter === 'week' && vehicleStats) {
        value = ensureNumber(vehicleStats.totalScans) || value;
      } else if (timeFilter === 'month' && aggregatedVehicleData?.month) {
        value = ensureNumber(aggregatedVehicleData.month.totalScans) || value;
      } else if ((timeFilter === 'ytd' || timeFilter === 'year') && aggregatedVehicleData?.ytd) {
        value = ensureNumber(aggregatedVehicleData.ytd.totalScans) || value;
      }
    } else if (basePropertyName === 'carsFound') {
      const weeklyStats = calculateWeeklyStats();
      
      if (timeFilter === 'week' && vehicleStats) {
        value = weeklyStats.weeklyFound || value;
      } else if (timeFilter === 'month' && aggregatedVehicleData?.month) {
        value = ensureNumber(aggregatedVehicleData.month.totalFound) || value;
      } else if ((timeFilter === 'ytd' || timeFilter === 'year') && aggregatedVehicleData?.ytd) {
        value = ensureNumber(aggregatedVehicleData.ytd.totalFound) || value;
      }
    } else if (basePropertyName === 'carsRecovered') {
      const weeklyStats = calculateWeeklyStats();
      
      if (timeFilter === 'week' && vehicleStats) {
        value = weeklyStats.weeklySecured || value;
      } else if (timeFilter === 'month' && aggregatedVehicleData?.month) {
        value = ensureNumber(aggregatedVehicleData.month.totalSecured) || value;
      } else if ((timeFilter === 'ytd' || timeFilter === 'year') && aggregatedVehicleData?.ytd) {
        value = ensureNumber(aggregatedVehicleData.ytd.totalSecured) || value;
      }
    }
    
    return value;
  };

  // Calculate efficiency metrics with safety measures for calculations
  const calculateEfficiency = () => {
    if (!userStats) return { scansPerHour: 0, carsPerHour: 0, milesPerCar: 0 };
    
    // Get actual hours worked
    const actualHours = getStatsValue('userHoursWorked');
    
    // For calculation purposes only, use minimum 0.25 hours to prevent astronomical values
    const hoursForCalculation = actualHours < 0.25 ? 0.25 : actualHours;
    
    let scans = getStatsValue('userScans');
    let carsFound = getStatsValue('carsFound');
    const milesTraveled = ensureNumber(userStats[getTimeProperty('userMilesTraveled')]) || 0;
    
    // Calculate rates with reasonable caps
    const scansPerHour = Math.min(scans / hoursForCalculation, 1000); // Cap at 1000 scans per hour
    const carsPerHour = Math.min(carsFound / hoursForCalculation, 100); // Cap at 100 cars per hour
    const milesPerCar = carsFound > 0 ? Math.min(milesTraveled / carsFound, 1000) : 0; // Cap at 1000 miles per car
    
    return {
      scansPerHour,
      carsPerHour,
      milesPerCar
    };
  };

  // Get contribution percentages - capped at 100%
  const getContributionPercentages = () => {
    if (!userStats) return { orders: 0, scans: 0, miles: 0, recoveryRate: 0 };
    
    // User values with vehicle data integration
    const userOrders = ensureNumber(userStats[getTimeProperty('userOpenOrders')]) || 0;
    const userScans = getStatsValue('userScans');
    const userMiles = ensureNumber(userStats[getTimeProperty('userMilesTraveled')]) || 0;
    
    // Team values
    let teamOrders = ensureNumber(userStats[getTimeProperty('teamOpenOrders')]) || 0;
    let teamScans = ensureNumber(userStats[getTimeProperty('teamScans')]) || 0;
    let teamMiles = ensureNumber(userStats[getTimeProperty('teamMilesTraveled')]) || 0;
    
    // If team values are too low, use reasonable minimums to avoid crazy percentages
    teamOrders = Math.max(teamOrders, userOrders);
    teamScans = Math.max(teamScans, userScans);
    teamMiles = Math.max(teamMiles, userMiles);

    // Recovery rate calculation
    const carsFound = getStatsValue('carsFound');
    const carsRecovered = getStatsValue('carsRecovered');
    
    return {
      orders: teamOrders > 0 ? Math.min(100, (userOrders / teamOrders) * 100) : 0,
      scans: teamScans > 0 ? Math.min(100, (userScans / teamScans) * 100) : 0,
      miles: teamMiles > 0 ? Math.min(100, (userMiles / teamMiles) * 100) : 0,
      recoveryRate: carsFound > 0 ? Math.min(100, (carsRecovered / carsFound) * 100) : 0
    };
  };

  // Get time period label
  const getTimeFilterLabel = () => {
    switch(timeFilter) {
      case 'day': return 'Daily';
      case 'week': return 'Weekly';
      case 'month': return 'Monthly';
      default: return 'Year to Date';
    }
  };

  // Create combined stats object for display that integrates all data sources
  const getCombinedStats = () => {
    if (!userStats) return null;
    
    // Start with user stats
    const combined = { ...userStats };
    
    // Apply proper weekly stats calculations
    const weeklyStats = calculateWeeklyStats();
    
    // Apply vehicle stats based on time period - always use vehicle data when available
    if (vehicleStats) {
      combined.carsFoundWeek = weeklyStats.weeklyFound;
      combined.carsRecoveredWeek = weeklyStats.weeklySecured;
      combined.userScansWeek = ensureNumber(vehicleStats.totalScans);
    }
    
    // Apply monthly aggregated data
    if (aggregatedVehicleData?.month) {
      combined.carsFoundMonth = ensureNumber(aggregatedVehicleData.month.totalFound);
      combined.carsRecoveredMonth = ensureNumber(aggregatedVehicleData.month.totalSecured);
      combined.userScansMonth = ensureNumber(aggregatedVehicleData.month.totalScans);
    }
    
    // Apply YTD aggregated data 
    if (aggregatedVehicleData?.ytd) {
      combined.carsFoundYTD = ensureNumber(aggregatedVehicleData.ytd.totalFound);
      combined.carsRecoveredYTD = ensureNumber(aggregatedVehicleData.ytd.totalSecured);
      combined.userScansYTD = ensureNumber(aggregatedVehicleData.ytd.totalScans);
    }
    
    // Apply timecard data if available - keep actual values
    if (timeCardData) {
      combined.userHoursWorkedToday = timeCardData.dailyHours || combined.userHoursWorkedToday || 0;
      combined.userHoursWorkedWeek = timeCardData.weeklyHours || combined.userHoursWorkedWeek || 0;
      combined.userHoursWorkedMonth = timeCardData.monthlyHours || combined.userHoursWorkedMonth || 0;
      combined.userHoursWorkedYTD = timeCardData.ytdHours || combined.userHoursWorkedYTD || 0;
    }
    
    return combined;
  };

  // Render activity chart with chart instance management
  const renderActivityChart = () => {
    if (!userStats) return null;
    
    // Get the proper weekly stats
    const weeklyStats = calculateWeeklyStats();
    
    // Determine which data to use based on time filter
    let chartData;
    let labels;
    let labelField;
    
    switch(timeFilter) {
      case 'day':
        // For daily view, we'd ideally have hourly data, but we'll use daily for demo
        chartData = userStats.dailyData || [];
        labels = chartData.map(d => d.day);
        labelField = 'day';
        break;
      case 'week':
        chartData = userStats.dailyData || [];
        labels = chartData.map(d => d.day);
        labelField = 'day';
        break;
      case 'month':
        chartData = userStats.weeklyData || [];
        labels = chartData.map(d => d.week);
        labelField = 'week';
        break;
      default: // ytd
        chartData = userStats.monthlyData || [];
        labels = chartData.map(d => d.month);
        labelField = 'month';
        break;
    }
    
    // If no data, generate some sample data
    if (!chartData || chartData.length === 0) {
      if (timeFilter === 'day' || timeFilter === 'week') {
        chartData = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => ({
          day,
          scans: Math.floor(Math.random() * 30) + 5,
          cars: Math.floor(Math.random() * 4),
          hours: Math.floor(Math.random() * 8) + 1,
          miles: Math.floor(Math.random() * 80) + 20
        }));
        labels = chartData.map(d => d.day);
        labelField = 'day';
      } else if (timeFilter === 'month') {
        chartData = Array.from({length: 4}, (_, i) => ({
          week: `Week ${i+1}`,
          scans: Math.floor(Math.random() * 150) + 20,
          cars: Math.floor(Math.random() * 18) + 2,
          hours: Math.floor(Math.random() * 40) + 10,
          miles: Math.floor(Math.random() * 400) + 100
        }));
        labels = chartData.map(d => d.week);
        labelField = 'week';
      } else {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const currentMonth = new Date().getMonth();
        
        chartData = Array.from({length: 6}, (_, i) => {
          const monthIndex = (currentMonth - 5 + i + 12) % 12;
          return {
            month: months[monthIndex],
            scans: Math.floor(Math.random() * 600) + 100,
            cars: Math.floor(Math.random() * 45) + 5,
            hours: Math.floor(Math.random() * 160) + 40,
            miles: Math.floor(Math.random() * 1600) + 400
          };
        });
        labels = chartData.map(d => d.month);
        labelField = 'month';
      }
    }
    
    // Include appropriate vehicle data based on time period
    let vehicleDataToUse = null;
    
    if (timeFilter === 'week' && vehicleStats && weeklyStats.weeklyFound > 0) {
      vehicleDataToUse = {
        totalFound: weeklyStats.weeklyFound,
        totalSecured: weeklyStats.weeklySecured,
        totalScans: vehicleStats.totalScans || 0
      };
    } else if (timeFilter === 'month' && aggregatedVehicleData?.month && 
                ensureNumber(aggregatedVehicleData.month.totalFound) > 0) {
      vehicleDataToUse = aggregatedVehicleData.month;
    } else if ((timeFilter === 'ytd' || timeFilter === 'year') && aggregatedVehicleData?.ytd && 
                ensureNumber(aggregatedVehicleData.ytd.totalFound) > 0) {
      vehicleDataToUse = aggregatedVehicleData.ytd;
    }
    
    // Include timecard data if available
    let timeCardHoursToUse = 0;
    if (timeCardData) {
      if (timeFilter === 'day') {
        timeCardHoursToUse = ensureNumber(timeCardData.dailyHours || 0);
      } else if (timeFilter === 'week') {
        timeCardHoursToUse = ensureNumber(timeCardData.weeklyHours || 0);
      } else if (timeFilter === 'month') {
        timeCardHoursToUse = ensureNumber(timeCardData.monthlyHours || 0);
      } else {
        timeCardHoursToUse = ensureNumber(timeCardData.ytdHours || 0);
      }
      
      console.log(`Using time card hours for ${timeFilter}: ${timeCardHoursToUse}`);
    }
    
    // If we have vehicle data, distribute it across chart periods for visualization
    if (vehicleDataToUse) {
      // Get total vehicle data values
      const totalScans = ensureNumber(vehicleDataToUse.totalScans);
      const totalFound = ensureNumber(vehicleDataToUse.totalFound);
      const totalSecured = ensureNumber(vehicleDataToUse.totalSecured);
      
      if (totalScans > 0 || totalFound > 0) {
        // Distribute values across time periods
        const numPeriods = chartData.length;
        
        if (numPeriods > 0) {
          // Calculate per-period values with some randomization for visual interest
          const scansPerPeriod = Math.floor(totalScans / numPeriods);
          const foundPerPeriod = Math.floor(totalFound / numPeriods);
          
          // Update chart data with actual vehicle data
          chartData = chartData.map((item, index) => {
            // Add some variation to make the chart look more natural
            const variationFactor = 0.5 + Math.random();
            
            // For last period, ensure we add any remainder to match the total
            const isLastPeriod = index === numPeriods - 1;
            
            const periodScans = isLastPeriod 
              ? totalScans - (scansPerPeriod * (numPeriods - 1))
              : Math.round(scansPerPeriod * variationFactor);
              
            const periodFound = isLastPeriod
              ? totalFound - (foundPerPeriod * (numPeriods - 1))
              : Math.round(foundPerPeriod * variationFactor);
            
            return {
              ...item,
              scans: Math.max(ensureNumber(item.scans), periodScans),
              cars: Math.max(ensureNumber(item.cars), periodFound)
            };
          });
        }
      }
    }
    
    // If we have timecard data, distribute it across chart periods
    if (timeCardHoursToUse > 0) {
      const numPeriods = chartData.length;
      if (numPeriods > 0) {
        const hoursPerPeriod = timeCardHoursToUse / numPeriods;
        
        // Update chart data with timecard hours
        chartData = chartData.map((item, index) => {
          // Add some variation for visual interest
          const variationFactor = 0.8 + (Math.random() * 0.4);
          const periodHours = Math.round(hoursPerPeriod * variationFactor * 10) / 10;
          
          return {
            ...item,
            hours: Math.max(periodHours, ensureNumber(item.hours || 0)) // Prioritize actual hours data
          };
        });
      }
    }
    
    const data = {
      labels: labels,
      datasets: [
        {
          label: 'Scans',
          data: chartData.map(d => ensureNumber(d.scans)),
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 2,
          tension: 0.3
        },
        {
          label: 'Cars Found',
          data: chartData.map(d => ensureNumber(d.cars)),
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 2,
          tension: 0.3
        },
        {
          label: 'Hours Worked',
          data: chartData.map(d => ensureNumber(d.hours)),
          backgroundColor: 'rgba(255, 206, 86, 0.2)',
          borderColor: 'rgba(255, 206, 86, 1)',
          borderWidth: 2,
          tension: 0.3
        }
      ]
    };
    
    const options = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            color: 'white',
            font: {
              size: 10
            }
          }
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            color: 'rgba(255, 255, 255, 0.7)',
            font: {
              size: 10
            }
          },
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        x: {
          ticks: {
            color: 'rgba(255, 255, 255, 0.7)',
            font: {
              size: 10
            }
          },
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      }
    };
    
    switch(chartType) {
      case 'bar':
        return <Bar data={data} options={options} id="activity-chart" />;
      default:
        return <Line data={data} options={options} id="activity-chart" />;
    }
  };
  
  // Render performance metrics chart with chart instance management
  const renderPerformanceChart = () => {
    if (!userStats) return null;
    
    // Get the proper weekly stats
    const weeklyStats = calculateWeeklyStats();
    
    let carsFound = getStatsValue('carsFound');
    let carsRecovered = getStatsValue('carsRecovered');
    let scans = getStatsValue('userScans');
    
    // Get actual hours value
    const actualHours = getStatsValue('userHoursWorked');
    
    // For calculation purposes only, use minimum 0.25 hours to prevent astronomical values
    const hoursForCalculation = actualHours < 0.25 ? 0.25 : actualHours;
    
    const data = {
      labels: [
        'Cars Found',
        'Cars Recovered',
        'Scan Efficiency',
        'Time Efficiency'
      ],
      datasets: [
        {
          label: 'Performance Metrics',
          data: [
            carsFound, 
            carsRecovered,
            scans > 0 ? Math.min(Math.round((carsFound / scans) * 100), 100) : 0, // Scan efficiency percentage (capped)
            Math.min(Math.round((carsFound / hoursForCalculation) * 100), 100) // Time efficiency percentage (capped)
          ],
          backgroundColor: [
            'rgba(54, 162, 235, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
    
    const options = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'right',
          labels: {
            color: 'white',
            font: {
              size: 9
            }
          }
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.raw || 0;
              
              if (context.dataIndex === 0) {
                return `Cars Found: ${value}`;
              } else if (context.dataIndex === 1) {
                return `Cars Recovered: ${value}`;
              } else if (context.dataIndex === 2) {
                return `Scan Efficiency: ${value}%`;
              } else if (context.dataIndex === 3) {
                return `Time Efficiency: ${value}%`;
              }
              return `${label}: ${value}`;
            }
          }
        }
      }
    };
    
    switch(chartType) {
      case 'bar':
        return <Bar data={data} options={options} id="performance-chart" />;
      case 'pie':
        return <Pie data={data} options={options} id="performance-chart" />;
      case 'doughnut':
        return <Doughnut data={data} options={options} id="performance-chart" />;
      default:
        return <Pie data={data} options={options} id="performance-chart" />;
    }
  };
  
  // Render work distribution chart with chart instance management
  const renderWorkDistributionChart = () => {
    if (!userStats) return null;
    
    let carsFound = getStatsValue('carsFound');
    let userScans = getStatsValue('userScans');
    
    const data = {
      labels: ['Cars Found', 'Scans', 'Orders', 'Markers'],
      datasets: [
        {
          label: 'Work Distribution',
          data: [
            carsFound,
            userScans,
            ensureNumber(userStats[getTimeProperty('userOpenOrders')]) || 0,
            ensureNumber(userStats[getTimeProperty('userMarkersCreated')]) || 0
          ],
          backgroundColor: [
            'rgba(54, 162, 235, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(153, 102, 255, 0.6)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(153, 102, 255, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
    
    const options = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'right',
          labels: {
            color: 'white',
            font: {
              size: 9
            }
          }
        }
      }
    };
    
    return <Doughnut data={data} options={options} id="distribution-chart" />;
  };

  // Get combined stats - create fresh instance each time
  const combinedStats = getCombinedStats();
  // Get weekly stats - create fresh instance each time  
  const weeklyStats = calculateWeeklyStats();

  return (
    <div 
      className={`${isMobileMenuOpen && !selectedUser ? 'hidden' : 'block'} 
        w-full md:w-3/4 bg-gray-800 rounded shadow-sm p-3 border border-gray-700`}
      aria-label="Analytics dashboard"
    >
      {selectedUser ? (
        <>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3">
            <div>
              <h2 className="text-lg font-semibold text-white">
                {selectedUser.displayName}'s Performance
              </h2>
              <p className="text-gray-400 text-xs">
                {selectedUser.jobTitle || selectedUser.role} • {selectedTeam ? selectedTeam.name : 'Unknown Team'}
              </p>
            </div>
            <div className="bg-blue-900 bg-opacity-50 text-blue-200 py-1 px-2 rounded-full text-xs mt-1 sm:mt-0">
              {getTimeFilterLabel()} Stats
            </div>
          </div>
          
          {/* Time Card Status Component */}
          {selectedUser && <TimeCardStatus userId={selectedUser.id} db={db} timeCardData={timeCardData} />}
          
          {/* Debug Info (hidden in production) */}
          <div className="mb-2 p-2 bg-blue-900 bg-opacity-20 text-xs text-gray-300 rounded">
            <details>
              <summary>Debug Info</summary>
              <div className="mt-2">
                <h5>Vehicle Stats:</h5>
                <pre className="overflow-auto max-h-32 bg-gray-900 p-1 rounded">
                  {JSON.stringify(debugInfo.vehicleStats, null, 2)}
                </pre>
                
                <h5 className="mt-2">Weekly Stats:</h5>
                <pre className="overflow-auto max-h-32 bg-gray-900 p-1 rounded">
                  {JSON.stringify(weeklyStats, null, 2)}
                </pre>
                
                <h5 className="mt-2">Aggregated Month:</h5>
                <pre className="overflow-auto max-h-32 bg-gray-900 p-1 rounded">
                  {JSON.stringify(debugInfo.aggregatedMonth, null, 2)}
                </pre>
                
                <h5 className="mt-2">Aggregated YTD:</h5>
                <pre className="overflow-auto max-h-32 bg-gray-900 p-1 rounded">
                  {JSON.stringify(debugInfo.aggregatedYTD, null, 2)}
                </pre>
                
                <h5 className="mt-2">TimeCard Data:</h5>
                <pre className="overflow-auto max-h-32 bg-gray-900 p-1 rounded">
                  {JSON.stringify(debugInfo.timeCardData, null, 2)}
                </pre>
                
                <h5 className="mt-2">Recent TimeCard Entries:</h5>
                <pre className="overflow-auto max-h-32 bg-gray-900 p-1 rounded">
                  {JSON.stringify(recentTimeCardEntries, null, 2)}
                </pre>
              </div>
            </details>
          </div>
          
          {userLoading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500" aria-hidden="true"></div>
              <span className="sr-only">Loading user data...</span>
            </div>
          ) : userStats ? (
            <>
              {/* Key Metrics Row - Compact grid */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 mb-3">
                <div className="bg-gray-700 p-2 rounded border border-gray-600">
                  <div className="text-xs text-gray-400">Cars Found</div>
                  <div className="text-base font-bold text-blue-400">
                    {formatNumber(weeklyStats.weeklyFound)}
                  </div>
                  <div className="text-xs text-gray-400">
                    {formatNumber(weeklyStats.weeklySecured)} recovered
                  </div>
                </div>
                
                <div className="bg-gray-700 p-2 rounded border border-gray-600">
                  <div className="text-xs text-gray-400">Total Scans</div>
                  <div className="text-base font-bold text-amber-400">
                    {formatNumber(vehicleStats.totalScans)}
                  </div>
                  <div className="text-xs text-gray-400">
                    {formatNumber(calculateEfficiency().scansPerHour)} per hour
                  </div>
                </div>
                
                <div className="bg-gray-700 p-2 rounded border border-gray-600">
                  <div className="text-xs text-gray-400">Hours Worked</div>
                  <div className="text-base font-bold text-yellow-400">
                    {formatNumber(timeCardData?.weeklyHours || userStats.userHoursWorkedWeek || 0)}
                  </div>
                  <div className="text-xs text-gray-400">
                    {formatNumber(calculateEfficiency().carsPerHour)} cars per hour
                  </div>
                </div>
                
                <div className="bg-gray-700 p-2 rounded border border-gray-600">
                  <div className="text-xs text-gray-400">Miles Traveled</div>
                  <div className="text-base font-bold text-indigo-400">
                    {formatNumber(userStats[getTimeProperty('userMilesTraveled')])}
                  </div>
                  <div className="text-xs text-gray-400">
                    {formatNumber(calculateEfficiency().milesPerCar)} miles per car
                  </div>
                </div>
              </div>
              
              {/* Team Contribution */}
              <div className="bg-gray-700 p-2 rounded border border-gray-600 mb-3">
                <h3 className="text-xs font-semibold text-blue-300 mb-2">
                  Team Contribution
                </h3>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {Object.entries(getContributionPercentages()).map(([key, value]) => (
                    <div key={key} className="bg-gray-800 p-2 rounded">
                      <div className="text-xs text-gray-400 capitalize">
                        {key === 'recoveryRate' ? 'Recovery Rate' : key}
                      </div>
                      <div className="text-sm font-bold text-blue-400">
                        {formatNumber(value)}%
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-1.5 mt-1">
                        <div 
                          className="bg-blue-500 h-1.5 rounded-full" 
                          style={{ width: `${Math.min(100, value)}%` }}
                          aria-hidden="true"
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Tracking Stats Overview */}
              <div className="bg-gray-700 p-2 rounded border border-gray-600 mb-3">
                <h3 className="text-xs font-semibold text-blue-300 mb-2">
                  Current Tracking Stats
                </h3>
                
                <div className="grid grid-cols-3 gap-3 text-center">
                  <div className="bg-gray-800 p-2 rounded">
                    <div className="text-xs text-gray-400">Cars Found</div>
                    <div className="text-2xl font-bold text-green-400">
                      {formatNumber(weeklyStats.weeklyFound)}
                    </div>
                  </div>
                  <div className="bg-gray-800 p-2 rounded">
                    <div className="text-xs text-gray-400">Cars Recovered</div>
                    <div className="text-2xl font-bold text-blue-400">
                      {formatNumber(weeklyStats.weeklySecured)}
                    </div>
                  </div>
                  <div className="bg-gray-800 p-2 rounded">
                    <div className="text-xs text-gray-400">Scans</div>
                    <div className="text-2xl font-bold text-yellow-400">
                      {formatNumber(vehicleStats.totalScans)}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Main Charts - Compact grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 mb-3">
                {/* Activity Over Time Chart */}
                <div className="bg-gray-700 p-2 rounded border border-gray-600">
                  <h3 className="text-xs font-semibold text-blue-300 mb-2">
                    Activity Over Time
                  </h3>
                  
                  <div className="h-40 bg-gray-800 p-2 rounded">
                    {renderActivityChart()}
                  </div>
                </div>
                
                {/* Performance Metrics Chart */}
                <div className="bg-gray-700 p-2 rounded border border-gray-600">
                  <h3 className="text-xs font-semibold text-blue-300 mb-2">
                    Performance Metrics
                  </h3>
                  
                  <div className="h-40 bg-gray-800 p-2 rounded">
                    {renderPerformanceChart()}
                  </div>
                </div>
              </div>
              
              {/* Data Table - Compact with horizontal scroll */}
              <div className="bg-gray-700 p-2 rounded border border-gray-600 mb-3">
                <h3 className="text-xs font-semibold text-blue-300 mb-2">
                  Detailed Performance Data
                </h3>
                
                <div className="overflow-x-auto">
                  <table className="w-full text-xs divide-y divide-gray-600">
                    <thead>
                      <tr className="bg-gray-800">
                        <th className="py-1 px-2 text-left text-gray-300 font-medium">Metric</th>
                        <th className="py-1 px-2 text-right text-gray-300 font-medium">Today</th>
                        <th className="py-1 px-2 text-right text-gray-300 font-medium">Week</th>
                        <th className="py-1 px-2 text-right text-gray-300 font-medium">Month</th>
                        <th className="py-1 px-2 text-right text-gray-300 font-medium">YTD</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-700">
                      <tr>
                        <td className="py-1 px-2 whitespace-nowrap font-medium text-white">Orders</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userOpenOrdersToday)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userOpenOrdersWeek)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userOpenOrdersMonth)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userOpenOrdersYTD)}</td>
                      </tr>
                      <tr>
                        <td className="py-1 px-2 whitespace-nowrap font-medium text-white">Cars Found</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.carsFoundToday)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(weeklyStats.weeklyFound)}
                        </td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(aggregatedVehicleData?.month?.totalFound || combinedStats?.carsFoundMonth || 0)}
                        </td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(aggregatedVehicleData?.ytd?.totalFound || combinedStats?.carsFoundYTD || 0)}
                        </td>
                      </tr>
                      <tr>
                        <td className="py-1 px-2 whitespace-nowrap font-medium text-white">Cars Recovered</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.carsRecoveredToday)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(weeklyStats.weeklySecured)}
                        </td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(aggregatedVehicleData?.month?.totalSecured || combinedStats?.carsRecoveredMonth || 0)}
                        </td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(aggregatedVehicleData?.ytd?.totalSecured || combinedStats?.carsRecoveredYTD || 0)}
                        </td>
                      </tr>
                      <tr>
                        <td className="py-1 px-2 whitespace-nowrap font-medium text-white">Scans</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userScansToday)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(vehicleStats?.totalScans || combinedStats?.userScansWeek || 0)}
                        </td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(aggregatedVehicleData?.month?.totalScans || combinedStats?.userScansMonth || 0)}
                        </td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(aggregatedVehicleData?.ytd?.totalScans || combinedStats?.userScansYTD || 0)}
                        </td>
                      </tr>
                      <tr>
                        <td className="py-1 px-2 whitespace-nowrap font-medium text-white">Hours Worked</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(timeCardData?.dailyHours || userStats.userHoursWorkedToday || 0)}
                        </td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(timeCardData?.weeklyHours || userStats.userHoursWorkedWeek || 0)}
                        </td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(timeCardData?.monthlyHours || userStats.userHoursWorkedMonth || 0)}
                        </td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">
                          {formatNumber(timeCardData?.ytdHours || userStats.userHoursWorkedYTD || 0)}
                        </td>
                      </tr>
                      <tr>
                        <td className="py-1 px-2 whitespace-nowrap font-medium text-white">Miles Traveled</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userMilesTraveledToday)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userMilesTraveledWeek)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userMilesTraveledMonth)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userMilesTraveledYTD)}</td>
                      </tr>
                      <tr>
                        <td className="py-1 px-2 whitespace-nowrap font-medium text-white">Markers Created</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userMarkersCreatedToday)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userMarkersCreatedWeek)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userMarkersCreatedMonth)}</td>
                        <td className="py-1 px-2 whitespace-nowrap text-right text-gray-300">{formatNumber(userStats.userMarkersCreatedYTD)}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              
              {/* Vehicle Stats Summary Section */}
              <div className="bg-gray-700 p-2 rounded border border-gray-600 mb-3">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-xs font-semibold text-blue-300">Vehicle Tracking Summary</h3>
                  <span className="text-xs text-gray-400">{getTimeFilterLabel()}</span>
                </div>
                
                <div className="grid grid-cols-3 gap-2 mb-2">
                  {/* Weekly Stats */}
                  <div className="bg-gray-800 p-2 rounded">
                    <div className="text-xs text-gray-400">Weekly Stats</div>
                    <div className="text-lg font-bold text-blue-400">{formatNumber(vehicleStats.totalScans)} Scans</div>
                    <div className="text-xs text-gray-300">{formatNumber(weeklyStats.weeklyFound)} Found / {formatNumber(weeklyStats.weeklySecured)} Secured</div>
                    <div className="text-xs text-gray-400">{formatNumber(weeklyStats.weeklyRecoveryRate)}% Recovery Rate</div>
                    <div className="text-xs text-gray-300 mt-1">
                      {formatNumber(timeCardData?.weeklyHours || userStats.userHoursWorkedWeek || 0)} Hours
                    </div>
                  </div>
                  
                  {/* Monthly Stats */}
                  <div className="bg-gray-800 p-2 rounded">
                    <div className="text-xs text-gray-400">Monthly Stats</div>
                    <div className="text-lg font-bold text-purple-400">{formatNumber(aggregatedVehicleData?.month?.totalScans || 0)} Scans</div>
                    <div className="text-xs text-gray-300">{formatNumber(aggregatedVehicleData?.month?.totalFound || 0)} Found / {formatNumber(aggregatedVehicleData?.month?.totalSecured || 0)} Secured</div>
                    <div className="text-xs text-gray-400">{formatNumber(aggregatedVehicleData?.month?.recoveryRate || 0)}% Recovery Rate</div>
                    <div className="text-xs text-gray-300 mt-1">
                      {formatNumber(timeCardData?.monthlyHours || userStats.userHoursWorkedMonth || 0)} Hours
                    </div>
                  </div>
                  
                  {/* YTD Stats */}
                  <div className="bg-gray-800 p-2 rounded">
                    <div className="text-xs text-gray-400">YTD Stats</div>
                    <div className="text-lg font-bold text-teal-400">{formatNumber(aggregatedVehicleData?.ytd?.totalScans || 0)} Scans</div>
                    <div className="text-xs text-gray-300">{formatNumber(aggregatedVehicleData?.ytd?.totalFound || 0)} Found / {formatNumber(aggregatedVehicleData?.ytd?.totalSecured || 0)} Secured</div>
                    <div className="text-xs text-gray-400">{formatNumber(aggregatedVehicleData?.ytd?.recoveryRate || 0)}% Recovery Rate</div>
                    <div className="text-xs text-gray-300 mt-1">
                      {formatNumber(timeCardData?.ytdHours || userStats.userHoursWorkedYTD || 0)} Hours
                    </div>
                  </div>
                </div>
              </div>
              
              {/* TimeCard Data Summary Section */}
              <div className="bg-gray-700 p-2 rounded border border-gray-600 mb-3">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-xs font-semibold text-blue-300">TimeCard Hours Summary</h3>
                  <span className="text-xs text-gray-400">{getTimeFilterLabel()}</span>
                </div>
                
                <div className="grid grid-cols-4 gap-2">
                  {/* Daily Hours */}
                  <div className="bg-gray-800 p-2 rounded">
                    <div className="text-xs text-gray-400">Today</div>
                    <div className="text-lg font-bold text-green-400">
                      {formatNumber(timeCardData?.dailyHours || userStats.userHoursWorkedToday || 0)}
                    </div>
                    <div className="text-xs text-gray-300">Hours</div>
                  </div>
                  
                  {/* Weekly Hours */}
                  <div className="bg-gray-800 p-2 rounded">
                    <div className="text-xs text-gray-400">This Week</div>
                    <div className="text-lg font-bold text-blue-400">
                      {formatNumber(timeCardData?.weeklyHours || userStats.userHoursWorkedWeek || 0)}
                    </div>
                    <div className="text-xs text-gray-300">Hours</div>
                  </div>
                  
                  {/* Monthly Hours */}
                  <div className="bg-gray-800 p-2 rounded">
                    <div className="text-xs text-gray-400">This Month</div>
                    <div className="text-lg font-bold text-purple-400">
                      {formatNumber(timeCardData?.monthlyHours || userStats.userHoursWorkedMonth || 0)}
                    </div>
                    <div className="text-xs text-gray-300">Hours</div>
                  </div>
                  
                  {/* YTD Hours */}
                  <div className="bg-gray-800 p-2 rounded">
                    <div className="text-xs text-gray-400">Year to Date</div>
                    <div className="text-lg font-bold text-indigo-400">
                      {formatNumber(timeCardData?.ytdHours || userStats.userHoursWorkedYTD || 0)}
                    </div>
                    <div className="text-xs text-gray-300">Hours</div>
                  </div>
                </div>
              </div>
              
              {/* Efficiency Metrics - Compact grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                <div className="bg-gray-700 p-2 rounded border border-gray-600">
                  <h3 className="text-xs font-semibold text-blue-300 mb-2">
                    Efficiency Rates
                  </h3>
                  
                  <div className="space-y-2">
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-300">Scans per Hour</span>
                        <span className="text-xs font-medium text-amber-400">
                          {formatNumber(calculateEfficiency().scansPerHour)}
                        </span>
                      </div>
                      <div className="w-full bg-gray-800 rounded-full h-1.5">
                        <div 
                          className="bg-amber-500 h-1.5 rounded-full" 
                          style={{ width: `${Math.min(100, calculateEfficiency().scansPerHour / 10)}%` }}
                          aria-hidden="true"
                        ></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-300">Cars per Hour</span>
                        <span className="text-xs font-medium text-blue-400">
                          {formatNumber(calculateEfficiency().carsPerHour)}
                        </span>
                      </div>
                      <div className="w-full bg-gray-800 rounded-full h-1.5">
                        <div 
                          className="bg-blue-500 h-1.5 rounded-full" 
                          style={{ width: `${Math.min(100, calculateEfficiency().carsPerHour * 20)}%` }}
                          aria-hidden="true"
                        ></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-300">Miles per Car</span>
                        <span className="text-xs font-medium text-indigo-400">
                          {formatNumber(calculateEfficiency().milesPerCar)}
                        </span>
                      </div>
                      <div className="w-full bg-gray-800 rounded-full h-1.5">
                        <div 
                          className="bg-indigo-500 h-1.5 rounded-full" 
                          style={{ width: `${Math.min(100, 100 - calculateEfficiency().milesPerCar / 2)}%` }}
                          aria-hidden="true"
                        ></div>
                      </div>
                      <div className="text-xs text-gray-400">Lower is better</div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-700 p-2 rounded border border-gray-600">
                  <h3 className="text-xs font-semibold text-blue-300 mb-2">
                    Recovery Stats
                  </h3>
                  
                  <div className="flex flex-col items-center justify-center">
                    <div className="text-xl font-bold text-green-400 mb-1">
                      {formatNumber(getContributionPercentages().recoveryRate)}%
                    </div>
                    <div className="text-xs text-gray-300 mb-2">Recovery Rate</div>
                    
                    <div className="w-full bg-gray-800 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-yellow-500 via-green-500 to-green-600 h-2 rounded-full" 
                        style={{ width: `${Math.min(100, getContributionPercentages().recoveryRate)}%` }}
                        aria-hidden="true"
                      ></div>
                    </div>
                    
                    <div className="flex justify-between w-full text-xs text-gray-400 mt-1">
                      <span>0%</span>
                      <span>50%</span>
                      <span>100%</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-700 p-2 rounded border border-gray-600 sm:col-span-2 lg:col-span-1">
                  <h3 className="text-xs font-semibold text-blue-300 mb-2">
                    Work Distribution
                  </h3>
                  
                  <div className="flex items-center h-32">
                    <div className="w-full h-full">
                      {renderWorkDistributionChart()}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Time Card Entry Analysis */}
              <div className="bg-gray-700 p-2 rounded border border-gray-600 mt-3">
                <h3 className="text-xs font-semibold text-blue-300 mb-2">
                  Time Card Entry Analysis
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="bg-gray-800 p-3 rounded">
                    <h4 className="text-sm font-medium text-blue-300 mb-2">Recent Time Card Activity</h4>
                    
                    {loading ? (
                      <div className="flex justify-center py-4">
                        <div className="animate-spin h-5 w-5 border-2 border-blue-500 rounded-full border-t-transparent"></div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {recentTimeCardEntries.length > 0 ? (
                          recentTimeCardEntries.map(entry => (
                            <div key={entry.id} className="flex justify-between items-center text-xs">
                              <div className="flex items-center">
                                {entry.adminAction ? (
                                  <>
                                    <span className="h-2 w-2 bg-yellow-500 rounded-full mr-1"></span>
                                    <span className="text-gray-300">
                                      {entry.type === 'clockIn' ? 'Admin Clock In' : 'Admin Clock Out'}
                                    </span>
                                  </>
                                ) : (
                                  <>
                                    <span className="h-2 w-2 bg-green-500 rounded-full mr-1"></span>
                                    <span className="text-gray-300">
                                      {entry.type === 'clockIn' ? 'Clock In' : 'Clock Out'}
                                    </span>
                                  </>
                                )}
                              </div>
                              <span className="text-gray-400 font-mono">
                                {formatDate(entry.timestamp)}
                              </span>
                            </div>
                          ))
                        ) : (
                          <div className="text-center text-gray-500 py-2">
                            No recent time card activity
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div className="bg-gray-800 p-3 rounded">
                    <h4 className="text-sm font-medium text-blue-300 mb-2">Hours Summary</h4>
                    
                    <div className="space-y-2">
                      <div>
                        <div className="flex justify-between items-center mb-1 text-xs">
                          <span className="text-gray-300">Today's Hours</span>
                          <span className="font-medium text-green-400">
                            {formatNumber(timeCardData?.dailyHours || userStats.userHoursWorkedToday || 0)} hrs
                          </span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-1.5">
                          <div 
                            className="bg-green-500 h-1.5 rounded-full" 
                            style={{ width: `${Math.min(100, ((timeCardData?.dailyHours || userStats.userHoursWorkedToday || 0) / 8) * 100)}%` }}
                            aria-hidden="true"
                          ></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center mb-1 text-xs">
                          <span className="text-gray-300">Weekly Hours</span>
                          <span className="font-medium text-blue-400">
                            {formatNumber(timeCardData?.weeklyHours || userStats.userHoursWorkedWeek || 0)} hrs
                          </span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-1.5">
                          <div 
                            className="bg-blue-500 h-1.5 rounded-full" 
                            style={{ width: `${Math.min(100, ((timeCardData?.weeklyHours || userStats.userHoursWorkedWeek || 0) / 40) * 100)}%` }}
                            aria-hidden="true"
                          ></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center mb-1 text-xs">
                          <span className="text-gray-300">Monthly Hours</span>
                          <span className="font-medium text-purple-400">
                            {formatNumber(timeCardData?.monthlyHours || userStats.userHoursWorkedMonth || 0)} hrs
                          </span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-1.5">
                          <div 
                            className="bg-purple-500 h-1.5 rounded-full" 
                            style={{ width: `${Math.min(100, ((timeCardData?.monthlyHours || userStats.userHoursWorkedMonth || 0) / 160) * 100)}%` }}
                            aria-hidden="true"
                          ></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center mb-1 text-xs">
                          <span className="text-gray-300">YTD Hours</span>
                          <span className="font-medium text-indigo-400">
                            {formatNumber(timeCardData?.ytdHours || userStats.userHoursWorkedYTD || 0)} hrs
                          </span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-1.5">
                          <div 
                            className="bg-indigo-500 h-1.5 rounded-full" 
                            style={{ width: `${Math.min(100, ((timeCardData?.ytdHours || userStats.userHoursWorkedYTD || 0) / 2000) * 100)}%` }}
                            aria-hidden="true"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center py-8">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-gray-600 mb-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
              </svg>
              <h3 className="text-base font-semibold text-gray-400 mb-1">Select a Team Member</h3>
              <p className="text-gray-500 text-center max-w-md text-xs">
                Choose a team member to view analytics
              </p>
            </div>
          )}
        </>
      ) : (
        <div className="flex flex-col items-center justify-center py-8">
          {loading ? (
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500" aria-hidden="true"></div>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-gray-600 mb-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
              </svg>
              {selectedTeam ? (
                <>
                  <h3 className="text-base font-semibold text-gray-400 mb-1">Select a Team Member</h3>
                  <p className="text-gray-500 text-center max-w-md text-xs">
                    Choose a team member to view analytics
                  </p>
                </>
              ) : (
                <>
                  <h3 className="text-base font-semibold text-gray-400 mb-1">No Team Selected</h3>
                  <p className="text-gray-500 text-center max-w-md text-xs">
                    Please select a team to continue
                  </p>
                </>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
}

export default UserDashboard;