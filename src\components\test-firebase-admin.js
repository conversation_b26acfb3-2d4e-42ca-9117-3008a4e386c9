// test-firebase-admin.js - Test Firebase Admin SDK connection
require('dotenv').config();
const admin = require('firebase-admin');

console.log('🔧 Testing Firebase Admin SDK Connection...\n');

// Method 1: Create service account from environment variables
try {
  const serviceAccount = {
    type: "service_account",
    project_id: "nwrepo-bf088",
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID || "d1f3586bbaf67d783e061a8ca8d561edfc762e69",
    private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL || "<EMAIL>",
    client_id: process.env.FIREBASE_CLIENT_ID || "101196901808483367742",
    auth_uri: "https://accounts.google.com/o/oauth2/auth",
    token_uri: "https://oauth2.googleapis.com/token",
    auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
    client_x509_cert_url: "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40nwrepo-bf088.iam.gserviceaccount.com",
    universe_domain: "googleapis.com"
  };

  // Log what we're using (without the private key)
  console.log('Service Account Details:');
  console.log('- Project ID:', serviceAccount.project_id);
  console.log('- Client Email:', serviceAccount.client_email);
  console.log('- Private Key ID:', serviceAccount.private_key_id);
  console.log('- Private Key Length:', serviceAccount.private_key ? serviceAccount.private_key.length : 0);
  
  // Initialize Firebase Admin
  if (!admin.apps.length) {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: "nwrepo-bf088"
    });
    console.log('\n✅ Firebase Admin initialized\n');
  }

  const db = admin.firestore();

  // Test 1: Try to read from root (should work with your rules)
  console.log('Test 1: Reading collections list...');
  try {
    const collections = await db.listCollections();
    console.log(`✅ Found ${collections.length} collections`);
    collections.slice(0, 5).forEach(col => {
      console.log(`   - ${col.id}`);
    });
  } catch (error) {
    console.error('❌ Failed to list collections:', error.message);
  }

  // Test 2: Try to read a specific document
  console.log('\nTest 2: Reading a test document...');
  try {
    // Try to read from users collection
    const usersRef = db.collection('users');
    const snapshot = await usersRef.limit(1).get();
    
    if (!snapshot.empty) {
      console.log('✅ Successfully read from users collection');
      console.log(`   Found ${snapshot.size} document(s)`);
    } else {
      console.log('⚠️  Users collection is empty or not accessible');
    }
  } catch (error) {
    console.error('❌ Failed to read users collection:', error.message);
  }

  // Test 3: Try to read teams
  console.log('\nTest 3: Reading teams collection...');
  try {
    const teamsRef = db.collection('teams');
    const teamsSnapshot = await teamsRef.limit(1).get();
    
    if (!teamsSnapshot.empty) {
      console.log('✅ Successfully read from teams collection');
      console.log(`   Found ${teamsSnapshot.size} team(s)`);
    } else {
      console.log('⚠️  Teams collection is empty');
      
      // Try to create a test team
      console.log('\nTest 3a: Creating a test team...');
      try {
        const testTeam = await teamsRef.add({
          name: 'Test Team',
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          createdBy: 'test-script'
        });
        console.log('✅ Successfully created test team:', testTeam.id);
        
        // Clean up
        await testTeam.delete();
        console.log('✅ Cleaned up test team');
      } catch (createError) {
        console.error('❌ Failed to create test team:', createError.message);
      }
    }
  } catch (error) {
    console.error('❌ Failed to read teams collection:', error.message);
    console.error('Error code:', error.code);
    console.error('Error details:', error.details);
  }

  // Test 4: Check authentication
  console.log('\nTest 4: Checking authentication status...');
  try {
    const app = admin.app();
    const token = await admin.auth().createCustomToken('test-user');
    console.log('✅ Admin SDK is properly authenticated');
    console.log('   Can create custom tokens');
  } catch (error) {
    console.error('❌ Authentication check failed:', error.message);
  }

  // Test 5: Direct Firestore REST API test
  console.log('\nTest 5: Testing direct Firestore access...');
  try {
    // Try to get project info
    const projectId = admin.app().options.projectId;
    console.log('✅ Project ID:', projectId);
    
    // Check if we can access Firestore settings
    const firestoreSettings = db._settings;
    console.log('✅ Firestore settings configured');
  } catch (error) {
    console.error('❌ Direct access test failed:', error.message);
  }

  console.log('\n📊 Summary:');
  console.log('If all tests passed, your Firebase Admin SDK is working correctly.');
  console.log('If you see permission errors, check:');
  console.log('1. Service account has "Firebase Admin" or "Editor" role in GCP Console');
  console.log('2. Firestore API is enabled in your project');
  console.log('3. The service account email matches your project');

} catch (error) {
  console.error('\n❌ Fatal error:', error);
  console.error('\nMake sure your .env file has the correct Firebase credentials.');
}