import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext'; // ✅ NEW: Added auth import
import { collection, getDocs, query, where, getDoc, doc, orderBy } from 'firebase/firestore'; // ✅ NEW: Added Firebase imports
import { db } from './firebase'; // ✅ NEW: Import Firebase db
import { 
  COLORS_MAP, 
  determineDriveType, 
  geocodeAddress, 
  generateMultipleCarViews,
  processAddresses,
  formatUserDisplayName // ✅ NEW: Added missing import
} from './utility-functions';
import { VehicleRender } from './ui-components';
import AddressComponent from './AddressComponent';

const OrderEditForm = ({ order, onSave, onCancel }) => {
  const { currentUser } = useAuth(); // ✅ NEW: Added auth context
  
  const [formData, setFormData] = useState({
    ...order,
    dueDate: order.dueDate ? (order.dueDate.toDate ? 
      order.dueDate.toDate().toISOString().split('T')[0] : 
      new Date(order.dueDate).toISOString().split('T')[0]) : '',
    teamId: order.teamId || '', // ✅ NEW: Include team assignment
  });
  
  const [addresses, setAddresses] = useState(processAddresses(order.addresses || []));
  const [vehicleImage, setVehicleImage] = useState(order.vehicleImage || null);
  const [isGeocodingInProgress, setIsGeocodingInProgress] = useState(false);
  
  // ✅ NEW: Team management state
  const [availableTeams, setAvailableTeams] = useState([]);
  const [loadingTeams, setLoadingTeams] = useState(true);
  const [selectedTeam, setSelectedTeam] = useState(null);
  const [error, setError] = useState(null);
  
  // State for vehicle image rendering
  const [vehicleImageUrl, setVehicleImageUrl] = useState(order.vehicleImage || '');
  const [isLoadingImage, setIsLoadingImage] = useState(false);
  const [renderedViews, setRenderedViews] = useState(order.vehicleRenderViews || []);
  const [selectedViewIndex, setSelectedViewIndex] = useState(0);

  // ✅ NEW: Load available teams (same logic as OrderForm)
  useEffect(() => {
    const loadUserTeams = async () => {
      if (!currentUser || !db) {
        setLoadingTeams(false);
        return;
      }
      
      try {
        setLoadingTeams(true);
        setError(null);
        console.log('🏢 Loading teams for user:', currentUser.uid);
        
        const teams = [];
        
        // Method 1: Get all teams and check if user is a member of each
        try {
          console.log('📋 Fetching all teams and checking membership...');
          
          const allTeamsQuery = query(
            collection(db, 'teams'),
            orderBy('name', 'asc')
          );
          
          const allTeamsSnapshot = await getDocs(allTeamsQuery);
          console.log(`Found ${allTeamsSnapshot.docs.length} total teams`);
          
          for (const teamDoc of allTeamsSnapshot.docs) {
            const teamData = teamDoc.data();
            const teamId = teamDoc.id;
            
            try {
              const teamMembersQuery = query(
                collection(db, 'teams', teamId, 'teamMembers'),
                where('userId', '==', currentUser.uid)
              );
              
              const teamMembersSnapshot = await getDocs(teamMembersQuery);
              
              if (!teamMembersSnapshot.empty) {
                const memberData = teamMembersSnapshot.docs[0].data();
                
                teams.push({
                  id: teamId,
                  name: teamData.name || 'Unnamed Team',
                  description: teamData.description || '',
                  memberRole: memberData.role || 'member',
                  memberSince: memberData.joinedAt || null,
                  isUserMember: true,
                  ...teamData
                });
                
                console.log(`✅ User is member of team: ${teamData.name} (role: ${memberData.role || 'member'})`);
              }
            } catch (memberCheckError) {
              console.warn(`Could not check membership for team ${teamId}:`, memberCheckError);
            }
          }
        } catch (teamQueryError) {
          console.error('❌ Error querying teams:', teamQueryError);
        }
        
        // Fallback methods (same as OrderForm)
        if (teams.length === 0) {
          try {
            console.log('📝 No teams found via membership, checking user profile...');
            
            const userProfileDoc = await getDoc(doc(db, 'userProfiles', currentUser.uid));
            if (userProfileDoc.exists()) {
              const profile = userProfileDoc.data();
              if (profile.teamId) {
                const teamDoc = await getDoc(doc(db, 'teams', profile.teamId));
                if (teamDoc.exists()) {
                  teams.push({
                    id: teamDoc.id,
                    name: teamDoc.data().name || 'My Team',
                    description: 'From user profile',
                    memberRole: 'member',
                    isUserMember: true,
                    ...teamDoc.data()
                  });
                  console.log(`✅ Found team from user profile: ${teamDoc.data().name}`);
                }
              }
            }
          } catch (profileError) {
            console.log('📝 No user profile found or error:', profileError);
          }
        }
        
        // Check localStorage for recent team (fallback)
        if (teams.length === 0) {
          try {
            console.log('🔄 No teams found, checking localStorage for recent team...');
            
            const lastTeamId = localStorage.getItem('lastUsedTeamId');
            if (lastTeamId) {
              const teamDoc = await getDoc(doc(db, 'teams', lastTeamId));
              if (teamDoc.exists()) {
                teams.push({
                  id: teamDoc.id,
                  name: teamDoc.data().name || 'Recent Team',
                  description: 'From recent usage',
                  memberRole: 'member',
                  isUserMember: false,
                  ...teamDoc.data()
                });
                console.log(`✅ Found recent team: ${teamDoc.data().name}`);
              }
            }
          } catch (teamError) {
            console.log('🏢 Recent team not found:', teamError);
          }
        }
        
        // Admin override - load all teams if user might be admin
        if (teams.length === 0) {
          try {
            console.log('👑 Checking if user might be admin...');
            
            const userEmail = currentUser.email?.toLowerCase() || '';
            const isLikelyAdmin = userEmail.includes('admin') || 
                                userEmail.includes('manager') || 
                                userEmail.includes('supervisor');
            
            if (isLikelyAdmin) {
              console.log('👑 User appears to be admin, loading all teams...');
              
              const allTeamsQuery = query(
                collection(db, 'teams'),
                orderBy('name', 'asc')
              );
              
              const allTeamsSnapshot = await getDocs(allTeamsQuery);
              allTeamsSnapshot.docs.forEach(doc => {
                teams.push({
                  id: doc.id,
                  name: doc.data().name || 'Team',
                  description: 'Admin access',
                  memberRole: 'admin',
                  isUserMember: false,
                  isAdminAccess: true,
                  ...doc.data()
                });
              });
              
              console.log(`👑 Loaded ${teams.length} teams for admin user`);
            }
          } catch (adminError) {
            console.log('👑 Admin check failed:', adminError);
          }
        }
        
        console.log(`✅ Final result: Found ${teams.length} teams for user`);
        setAvailableTeams(teams);
        
        // ✅ Auto-select team logic - check if order already has a team assigned
        if (formData.teamId) {
          const existingTeam = teams.find(t => t.id === formData.teamId);
          if (existingTeam) {
            setSelectedTeam(existingTeam);
            console.log(`🎯 Found existing team assignment: ${existingTeam.name}`);
          } else {
            // Team exists in order but not in available teams (maybe user lost access)
            console.warn(`⚠️ Order has team ${formData.teamId} but user doesn't have access`);
          }
        } else if (teams.length === 1) {
          // If user only belongs to one team, suggest it
          setSelectedTeam(teams[0]);
          console.log(`🎯 Suggesting single available team: ${teams[0].name}`);
        } else {
          // Check localStorage for last used team
          const lastTeamId = localStorage.getItem('lastUsedTeamId');
          const lastTeam = teams.find(t => t.id === lastTeamId);
          if (lastTeam) {
            console.log(`🎯 Found recent team: ${lastTeam.name}`);
          }
        }
        
      } catch (error) {
        console.error('❌ Error loading teams:', error);
        setError('Failed to load teams. Please refresh the page.');
      } finally {
        setLoadingTeams(false);
      }
    };
    
    loadUserTeams();
  }, [currentUser, formData.teamId]);

  // ✅ NEW: Handle team selection
  const handleTeamChange = (e) => {
    const teamId = e.target.value;
    const team = availableTeams.find(t => t.id === teamId);
    
    setSelectedTeam(team);
    setFormData(prev => ({ ...prev, teamId: teamId }));
    
    // Save to localStorage for future use
    if (teamId) {
      localStorage.setItem('lastUsedTeamId', teamId);
    }
  };

  // ✅ NEW: Enhanced team selection dropdown
  const renderTeamDropdown = () => {
    return (
      <div className="mb-4 p-3 bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg">
        <h4 className="font-semibold text-sm mb-3 text-blue-300 flex items-center">
          <span className="mr-2">🏢</span>
          Team Assignment
          {availableTeams.length > 0 && (
            <span className="ml-2 bg-blue-600 text-white px-2 py-1 rounded-full text-xs">
              {availableTeams.length} available
            </span>
          )}
        </h4>
        
        {loadingTeams ? (
          <div className="flex items-center text-blue-300">
            <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading teams...
          </div>
        ) : availableTeams.length === 0 ? (
          <div className="space-y-3">
            <div className="text-yellow-300 text-sm flex items-center">
              <span className="mr-2">⚠️</span>
              No teams found for your account
            </div>
            <div className="text-xs text-gray-400 bg-gray-800 p-3 rounded border border-gray-600">
              <p className="font-semibold mb-2">Possible solutions:</p>
              <ul className="space-y-1">
                <li>• Contact your administrator to add you to a team</li>
                <li>• Check if you're logged in with the correct account</li>
                <li>• Try refreshing the page</li>
              </ul>
            </div>
          </div>
        ) : (
          <div>
            <label className="block text-blue-200 text-xs mb-2">
              Select Team for this Order
            </label>
            <select
              value={formData.teamId}
              onChange={handleTeamChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            >
              <option value="">-- Select Team --</option>
              
              {/* User's teams first */}
              {availableTeams.filter(team => team.isUserMember).length > 0 && (
                <optgroup label="Your Teams">
                  {availableTeams
                    .filter(team => team.isUserMember)
                    .map(team => (
                      <option key={team.id} value={team.id}>
                        ⭐ {team.name} 
                        {team.memberRole && team.memberRole !== 'member' ? ` (${team.memberRole})` : ''}
                      </option>
                    ))}
                </optgroup>
              )}
              
              {/* Other teams */}
              {availableTeams.filter(team => !team.isUserMember).length > 0 && (
                <optgroup label="Other Teams">
                  {availableTeams
                    .filter(team => !team.isUserMember)
                    .map(team => (
                      <option key={team.id} value={team.id}>
                        {team.name}
                        {team.isAdminAccess ? ' (Admin Access)' : ''}
                      </option>
                    ))}
                </optgroup>
              )}
            </select>
            
            {/* Selected team info */}
            {selectedTeam && (
              <div className="mt-3 p-3 bg-green-900 bg-opacity-30 border border-green-600 rounded">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-green-200 font-semibold text-sm">
                    {selectedTeam.name}
                  </span>
                  {selectedTeam.isUserMember && (
                    <span className="ml-2 bg-green-600 text-white px-2 py-1 rounded-full text-xs">
                      Member
                    </span>
                  )}
                  {selectedTeam.isAdminAccess && (
                    <span className="ml-2 bg-purple-600 text-white px-2 py-1 rounded-full text-xs">
                      Admin
                    </span>
                  )}
                </div>
                
                {selectedTeam.description && (
                  <p className="text-green-300 text-xs mt-1">
                    {selectedTeam.description}
                  </p>
                )}
                
                <div className="mt-2 text-xs text-green-300">
                  ✅ Order will be assigned to this team and appear on their map
                </div>
              </div>
            )}
            
            {/* Show current assignment if different from selection */}
            {order.teamId && order.teamId !== formData.teamId && (
              <div className="mt-2 p-2 bg-yellow-900 bg-opacity-30 border border-yellow-600 rounded">
                <div className="text-yellow-300 text-xs">
                  <strong>Current Assignment:</strong> {order.teamName || order.teamId}
                  {formData.teamId && formData.teamId !== order.teamId && (
                    <span className="block mt-1">
                      ⚠️ You are changing the team assignment for this order
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  
  // Function to search for vehicle images using Cars API and 3D rendering
  const searchForVehicleImage = async () => {
    if (!formData.make || !formData.model) return;
    
    setIsLoadingImage(true);
    
    try {
      // Generate multiple angles for the vehicle using our new function
      const generatedViews = await generateMultipleCarViews(
        formData.make, 
        formData.model, 
        formData.year, 
        formData.color
      );
      
      if (generatedViews.length > 0) {
        setRenderedViews(generatedViews);
        
        // Set the first view as the main image
        setVehicleImageUrl(generatedViews[0].url);
        setVehicleImage(generatedViews[0].url);
        setSelectedViewIndex(0);
      } else {
        // Ultimate fallback - text SVG
        const fallbackSvgUrl = `data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="400" height="225" viewBox="0 0 400 225"><rect width="400" height="225" fill="#1A2642" /><text x="200" y="112.5" font-family="Arial" font-size="20" fill="white" text-anchor="middle">${formData.year} ${formData.make} ${formData.model}</text></svg>`;
        
        setVehicleImageUrl(fallbackSvgUrl);
        setVehicleImage(fallbackSvgUrl);
        setRenderedViews([{
          url: fallbackSvgUrl,
          angle: '1',
          colorName: formData.color || 'Unknown',
          colorCode: formData.color ? COLORS_MAP[formData.color] || formData.color.toLowerCase() : 'white'
        }]);
      }
    } catch (error) {
      console.error("Error searching for vehicle image:", error);
      
      // Text-based SVG fallback on error
      const fallbackSvgUrl = `data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="400" height="225" viewBox="0 0 400 225"><rect width="400" height="225" fill="#1A2642" /><text x="200" y="112.5" font-family="Arial" font-size="20" fill="white" text-anchor="middle">${formData.year} ${formData.make} ${formData.model}</text></svg>`;
      
      setVehicleImageUrl(fallbackSvgUrl);
      setVehicleImage(fallbackSvgUrl);
      setRenderedViews([{
        url: fallbackSvgUrl,
        angle: '1',
        colorName: formData.color || 'Unknown',
        colorCode: 'white'
      }]);
    }
    
    setIsLoadingImage(false);
  };
  
  // Handle switching between available vehicle views
  const handleSwitchView = (index) => {
    if (renderedViews[index]) {
      setVehicleImageUrl(renderedViews[index].url);
      setVehicleImage(renderedViews[index].url);
      setSelectedViewIndex(index);
    }
  };
  
  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Auto-set drive type when make, model, or year changes
  useEffect(() => {
    if (formData.make && formData.model) {
      const suggestedDriveType = determineDriveType(formData.make, formData.model, formData.year);
      setFormData(prev => ({ ...prev, driveType: suggestedDriveType }));
    }
  }, [formData.make, formData.model, formData.year]);
  
  // Handle adding a new address
  const handleAddAddress = () => {
    setAddresses([...addresses, {
      street: '',
      city: '',
      state: '',
      zip: '',
      checkIns: []
    }]);
  };
  
  // Handle address change with geocoding
  const handleAddressChange = async (index, newAddress, forceGeocode = false) => {
    try {
      if (forceGeocode || !newAddress.position) {
        setIsGeocodingInProgress(true);
        
        // Geocode the address
        const geocodedAddress = await geocodeAddress(newAddress);
        
        // Update the address with geocoded data
        const updatedAddresses = [...addresses];
        updatedAddresses[index] = geocodedAddress;
        setAddresses(updatedAddresses);
        
        setIsGeocodingInProgress(false);
      } else {
        // Just update the address without geocoding
        const updatedAddresses = [...addresses];
        updatedAddresses[index] = newAddress;
        setAddresses(updatedAddresses);
      }
    } catch (error) {
      console.error("Error updating address:", error);
      setIsGeocodingInProgress(false);
      
      // Still update the address even if geocoding fails
      const updatedAddresses = [...addresses];
      updatedAddresses[index] = newAddress;
      setAddresses(updatedAddresses);
    }
  };
  
  // Handle address deletion
  const handleDeleteAddress = (index) => {
    const updatedAddresses = [...addresses];
    updatedAddresses.splice(index, 1);
    setAddresses(updatedAddresses);
  };
  
  // Handle address check-in
  const handleAddressCheckIn = (index) => {
    const updatedAddresses = [...addresses];
    const address = updatedAddresses[index];
    
    if (!address.checkIns) {
      address.checkIns = [];
    }
    
    // Only allow up to 6 check-ins
    if (address.checkIns.length < 6) {
      const now = new Date();
      address.checkIns.push({
        timestamp: now,
        date: now.toISOString(),
        userId: currentUser?.uid,
        userName: formatUserDisplayName(currentUser)
      });
    }
    
    setAddresses(updatedAddresses);
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Geocode any addresses that don't have coordinates
    setIsGeocodingInProgress(true);
    const geocodedAddresses = await Promise.all(
      addresses.map(async (addr) => {
        if (addr.street && (!addr.position || !addr.position.lat || !addr.position.lng)) {
          return await geocodeAddress(addr);
        }
        return addr;
      })
    );
    setIsGeocodingInProgress(false);
    
    // Due date handling
    let dueDate = null;
    if (formData.dueDate) {
      dueDate = new Date(formData.dueDate);
    }
    
    // Determine position for the order based on first address with valid coordinates
    const firstValidAddress = geocodedAddresses.find(addr => addr.position?.lat && addr.position?.lng);
    let position = null;
    
    // Only set position if we have a valid address with coordinates
    if (firstValidAddress?.position) {
      position = firstValidAddress.position;
    }
    
    // ✅ UPDATED: Create the updated order data with team assignment
    const updatedOrderData = {
      ...formData,
      vehicleImage,
      vehicleRenderViews: renderedViews,  // Store all rendered views
      addresses: geocodedAddresses,
      dueDate,
      position,
      teamId: formData.teamId, // ✅ Include team assignment
      teamName: selectedTeam?.name || formData.teamName || 'Unknown Team', // ✅ For debugging
      updatedAt: new Date()
    };
    
    // Remove any temporary fields used for the form
    delete updatedOrderData.id;
    
    // Update the name field for LocationsPanel integration
    updatedOrderData.name = `${formData.year} ${formData.make} ${formData.model} - ${formData.licensePlate}`;
    
    console.log("✅ Saving updated order with team assignment:", {
      orderId: order.id,
      teamId: updatedOrderData.teamId,
      teamName: updatedOrderData.teamName,
      vehicle: `${formData.year} ${formData.make} ${formData.model}`,
      hasPosition: !!updatedOrderData.position,
      addressCount: geocodedAddresses.length
    });
    
    onSave(order.id, updatedOrderData);
  };
  
  return (
    <form onSubmit={handleSubmit} className="bg-gray-800 rounded-lg shadow-lg p-4 border border-gray-700 animate-fadeIn">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">
          Edit Order: {order.year} {order.make} {order.model}
        </h3>
        <div className="space-x-2">
          <button
            type="button"
            onClick={onCancel}
            className="px-3 py-1 text-sm text-gray-300 border border-gray-700 rounded hover:bg-gray-700 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isGeocodingInProgress}
            className={`px-3 py-1 text-sm bg-gradient-to-r from-blue-600 to-indigo-600 ${isGeocodingInProgress ? 'opacity-70 cursor-not-allowed' : 'hover:from-blue-500 hover:to-indigo-500'} text-white rounded shadow transition-colors duration-200 flex items-center`}
          >
            {isGeocodingInProgress && (
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            Save Changes
          </button>
        </div>
      </div>
      
      {/* ✅ NEW: Team Selection Section */}
      {renderTeamDropdown()}
      
      {/* Error message for team loading */}
      {error && (
        <div className="mb-4 p-3 bg-red-900 bg-opacity-30 border border-red-600 rounded-lg">
          <div className="text-red-200 text-sm flex items-center">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-semibold text-sm mb-3 text-blue-300 border-b border-gray-700 pb-2">Vehicle Details</h4>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">Make*</label>
              <input
                type="text"
                name="make"
                value={formData.make || ''}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
                required
              />
            </div>
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">Model*</label>
              <input
                type="text"
                name="model"
                value={formData.model || ''}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
                required
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">Year*</label>
              <input
                type="text"
                name="year"
                value={formData.year || ''}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
                required
              />
            </div>
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">Color</label>
              <select
                name="color"
                value={formData.color || ''}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
              >
                <option value="">Select color</option>
                <option value="Black">Black</option>
                <option value="White">White</option>
                <option value="Silver">Silver</option>
                <option value="Gray">Gray</option>
                <option value="Red">Red</option>
                <option value="Blue">Blue</option>
                <option value="Green">Green</option>
                <option value="Yellow">Yellow</option>
                <option value="Brown">Brown</option>
                <option value="Orange">Orange</option>
                <option value="Purple">Purple</option>
                <option value="Gold">Gold</option>
                <option value="Beige">Beige</option>
                <option value="Burgundy">Burgundy</option>
              </select>
            </div>
          </div>
          
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">VIN*</label>
            <input
              type="text"
              name="vin"
              value={formData.vin || ''}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
              required
            />
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">License Plate*</label>
              <input
                type="text"
                name="licensePlate"
                value={formData.licensePlate || ''}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
                required
              />
            </div>
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">Case Number*</label>
              <input
                type="text"
                name="caseNumber"
                value={formData.caseNumber || ''}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
                required
              />
            </div>
          </div>
          
          <div className="mb-3">
            <label className="flex justify-between text-gray-300 text-xs mb-1">
              <span>Drive Type</span> 
              <span className="text-blue-400">(Auto-detected)</span>
            </label>
            <div className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white">
              {formData.driveType || 'Will be auto-detected'}
            </div>
          </div>
          
          {/* Multiple Addresses with geocoding support */}
          <div className="mb-3">
            <div className="flex justify-between items-center mb-1">
              <label className="block text-gray-300 text-xs">Vehicle Addresses</label>
              <button 
                type="button"
                onClick={async () => {
                  // Geocode all addresses that don't have coordinates
                  setIsGeocodingInProgress(true);
                  const geocodedAddresses = await Promise.all(
                    addresses.map(async (addr) => {
                      if (addr.street && (!addr.position || !addr.position.lat || !addr.position.lng)) {
                        return await geocodeAddress(addr);
                      }
                      return addr;
                    })
                  );
                  setAddresses(geocodedAddresses);
                  setIsGeocodingInProgress(false);
                }}
                className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded flex items-center"
                disabled={isGeocodingInProgress}
              >
                {isGeocodingInProgress ? (
                  <svg className="animate-spin -ml-1 mr-1 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                )}
                Geocode All
              </button>
            </div>
            
            <AddressComponent 
              addresses={addresses}
              onAddressChange={handleAddressChange}
              onAddressAdd={handleAddAddress}
              onAddressDelete={handleDeleteAddress}
              onAddressCheckIn={handleAddressCheckIn}
              isGeocodingInProgress={isGeocodingInProgress}
            />
          </div>
        </div>
        
        <div>
          <h4 className="font-semibold text-sm mb-3 text-blue-300 border-b border-gray-700 pb-2">Order Details</h4>
          
          {/* Customer Name */}
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Customer Name</label>
            <input
              type="text"
              name="customerName"
              value={formData.customerName || ''}
              onChange={handleChange}
              placeholder="Enter customer's full name"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            />
          </div>
          
          {/* Due Date */}
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Due Date</label>
            <input
              type="date"
              name="dueDate"
              value={formData.dueDate || ''}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            />
          </div>
          
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Lienholder</label>
            <input
              type="text"
              name="lienholder"
              value={formData.lienholder || ''}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            />
          </div>
          
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Account Number</label>
            <input
              type="text"
              name="accountNumber"
              value={formData.accountNumber || ''}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            />
          </div>
          
          {/* Vehicle Image Preview with multiple views */}
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Vehicle 3D Render</label>
            <VehicleRender 
              vehicleImageUrl={vehicleImageUrl}
              renderedViews={renderedViews}
              make={formData.make}
              model={formData.model}
              year={formData.year}
              color={formData.color}
              selectedViewIndex={selectedViewIndex}
              onSwitchView={handleSwitchView}
              isLoading={isLoadingImage}
            />
            
            {/* Refresh render button */}
            {formData.make && formData.model && (
              <button
                type="button"
                onClick={searchForVehicleImage}
                className="mt-1 text-sm text-blue-400 hover:text-blue-300 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
                Generate new 3D render
              </button>
            )}
            
            {/* Color indicator */}
            {formData.color && (
              <div className="mt-1 flex items-center text-xs text-gray-400">
                <div className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: COLORS_MAP[formData.color] || formData.color.toLowerCase() }}></div>
                Color: {formData.color}
              </div>
            )}
          </div>
          
          {/* Enhanced Status Dropdown with new options */}
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Status</label>
            <select
              name="status"
              value={formData.status || 'open'}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            >
              <option value="open">Open</option>
              <option value="pending-pickup">Pending Pickup</option>
              <option value="secure">Secure</option>
              <option value="on-hold">On-Hold</option>
              <option value="claim">Claim</option>
              <option value="closed">Closed</option>
              <option value="restricted">Restricted</option>
            </select>
          </div>
          
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Security Status</label>
            <div className="flex mt-1">
              <button 
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, secure: false }))}
                className={`flex-1 py-2 flex items-center justify-center rounded-l ${!formData.secure ? 'bg-red-600' : 'bg-gray-700 hover:bg-red-600'} transition-colors duration-200`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                Not Secure
              </button>
              <button 
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, secure: true }))}
                className={`flex-1 py-2 flex items-center justify-center rounded-r ${formData.secure ? 'bg-green-600' : 'bg-gray-700 hover:bg-green-600'} transition-colors duration-200`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Secure
              </button>
            </div>
          </div>
          
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Notes</label>
            <textarea
              name="notes"
              value={formData.notes || ''}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
              rows="4"
            />
          </div>
        </div>
      </div>
    </form>
  );
};

export default OrderEditForm;