// setup-slack-channels.js
// <PERSON>ript to configure Slack channels for teams in Firebase

require('dotenv').config();
const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID || process.env.REACT_APP_PROD_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    })
  });
}

const db = admin.firestore();

// Configuration: Add your team-to-channel mappings here
const TEAM_CHANNEL_MAPPINGS = [
  {
    teamName: 'Team Alpha',        // Replace with your actual team name
    slackChannel: 'C1234567890',   // Replace with actual channel ID
    notifyOnNewVehicle: true,      // Optional: notify when new vehicles are added
    notifyOnStatusChange: true,    // Optional: notify on status changes
    dailyReportTime: '09:00',      // Optional: time for daily summary (24hr format)
  },
  {
    teamName: 'Team Beta',
    slackChannel: 'C0987654321',
    notifyOnNewVehicle: true,
    notifyOnStatusChange: true,
    dailyReportTime: '08:30',
  },
  // Add more team mappings as needed
];

async function setupSlackChannels() {
  console.log('🔧 Setting up Slack channels for teams...\n');

  try {
    // Get all teams
    const teamsSnapshot = await db.collection('teams').get();
    const teams = new Map();
    
    teamsSnapshot.forEach(doc => {
      const data = doc.data();
      teams.set(data.name, { id: doc.id, ...data });
    });

    console.log(`Found ${teams.size} teams in Firebase\n`);

    // Process each mapping
    for (const mapping of TEAM_CHANNEL_MAPPINGS) {
      const team = teams.get(mapping.teamName);
      
      if (!team) {
        console.log(`❌ Team "${mapping.teamName}" not found in Firebase`);
        continue;
      }

      // Update team with Slack configuration
      const updateData = {
        slackChannel: mapping.slackChannel,
        slackIntegration: {
          enabled: true,
          channelId: mapping.slackChannel,
          notifyOnNewVehicle: mapping.notifyOnNewVehicle || false,
          notifyOnStatusChange: mapping.notifyOnStatusChange || false,
          dailyReportTime: mapping.dailyReportTime || null,
          configuredAt: admin.firestore.FieldValue.serverTimestamp(),
        }
      };

      await db.collection('teams').doc(team.id).update(updateData);
      
      console.log(`✅ Updated team "${mapping.teamName}"`);
      console.log(`   - Channel: ${mapping.slackChannel}`);
      console.log(`   - Notifications: New Vehicle (${mapping.notifyOnNewVehicle}), Status Change (${mapping.notifyOnStatusChange})`);
      if (mapping.dailyReportTime) {
        console.log(`   - Daily Report: ${mapping.dailyReportTime}`);
      }
      console.log('');
    }

    // Show summary
    console.log('\n📊 Summary:');
    const updatedTeams = await db.collection('teams')
      .where('slackChannel', '!=', null)
      .get();
    
    console.log(`Total teams with Slack integration: ${updatedTeams.size}`);
    
    // List all configured teams
    console.log('\nConfigured teams:');
    updatedTeams.forEach(doc => {
      const data = doc.data();
      console.log(`- ${data.name}: ${data.slackChannel}`);
    });

  } catch (error) {
    console.error('❌ Error setting up Slack channels:', error);
  }
}

async function listCurrentTeams() {
  console.log('📋 Current teams in Firebase:\n');
  
  try {
    const teamsSnapshot = await db.collection('teams').get();
    
    if (teamsSnapshot.empty) {
      console.log('No teams found in Firebase');
      return;
    }

    teamsSnapshot.forEach(doc => {
      const data = doc.data();
      console.log(`Team: ${data.name}`);
      console.log(`  ID: ${doc.id}`);
      console.log(`  Members: ${data.teamMembers?.length || 0}`);
      console.log(`  Slack Channel: ${data.slackChannel || 'Not configured'}`);
      console.log('');
    });

    // Check which teams need configuration
    const teamsWithoutSlack = [];
    teamsSnapshot.forEach(doc => {
      const data = doc.data();
      if (!data.slackChannel) {
        teamsWithoutSlack.push(data.name);
      }
    });

    if (teamsWithoutSlack.length > 0) {
      console.log('\n⚠️  Teams needing Slack configuration:');
      teamsWithoutSlack.forEach(name => console.log(`  - ${name}`));
    }

  } catch (error) {
    console.error('Error listing teams:', error);
  }
}

async function testSlackConnection(channelId) {
  console.log(`\n🧪 Testing Slack connection for channel ${channelId}...`);
  
  try {
    // Import the bot app
    const { app } = require('./src/components/slack-vehicle-bot.js');
    
    // Try to post a test message
    const result = await app.client.chat.postMessage({
      channel: channelId,
      text: '🔗 Slack integration test successful!',
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '✅ *Slack Integration Test*\nYour Vehicle Tracker bot is successfully connected to this channel!'
          }
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Test performed at ${new Date().toLocaleString()}`
            }
          ]
        }
      ]
    });

    if (result.ok) {
      console.log('✅ Successfully posted test message!');
      console.log(`   Message timestamp: ${result.ts}`);
    }

  } catch (error) {
    console.error('❌ Failed to post to channel:', error.data?.error || error.message);
    console.log('\nPossible issues:');
    console.log('- Bot might not be invited to the channel');
    console.log('- Channel ID might be incorrect');
    console.log('- Bot might not have chat:write permissions');
  }
}

// Command line interface
const command = process.argv[2];

async function main() {
  switch (command) {
    case 'list':
      await listCurrentTeams();
      break;
    
    case 'setup':
      await setupSlackChannels();
      break;
    
    case 'test':
      const channelId = process.argv[3];
      if (!channelId) {
        console.log('❌ Please provide a channel ID: node setup-slack-channels.js test C1234567890');
        break;
      }
      await testSlackConnection(channelId);
      break;
    
    default:
      console.log('Vehicle Tracker Slack Channel Setup\n');
      console.log('Usage:');
      console.log('  node setup-slack-channels.js list    - List all teams and their Slack status');
      console.log('  node setup-slack-channels.js setup   - Configure Slack channels for teams');
      console.log('  node setup-slack-channels.js test <channel-id>  - Test bot connection to a channel');
      console.log('\nBefore running setup:');
      console.log('1. Edit TEAM_CHANNEL_MAPPINGS in this file');
      console.log('2. Add your team names and channel IDs');
      console.log('3. Run "node setup-slack-channels.js setup"');
  }
  
  process.exit(0);
}

// Run the script
if (require.main === module) {
  main();
}