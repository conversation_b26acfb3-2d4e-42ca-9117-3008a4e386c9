import { useContext } from 'react';
import { MapContext } from '../components/MapContext';

/**
 * Custom hook to access the MapContext
 * This simplifies accessing the context across multiple components
 */
const useMap = () => {
  const context = useContext(MapContext);
  
  if (context === undefined) {
    throw new Error('useMap must be used within a MapProvider');
  }
  
  return context;
};

export default useMap;