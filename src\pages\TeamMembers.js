import React, { useState } from 'react';

const TeamMembers = ({
  allUsers = [],
  currentUser = null,
  userDisplayNames = {},
  userProfilePictures = {},
  userTags = {},
  isAdmin = false,
  navigateToUserLocation,
  handleConfirmDeleteTrail,
  isLoading = false,
  teamMembersLoaded = false,
  teamMembers = [],
  screenConfig = {},
  sectionHeight = 200
}) => {
  // State for section expansion/collapse
  const [isExpanded, setIsExpanded] = useState(true);

  // Helper function to get initials from name
  const getInitials = (name) => {
    if (!name) return "??";
    
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Helper function to get role tag color
  const getRoleTagColor = (role) => {
    switch (role) {
      case 'admin':
        return { bg: 'bg-purple-700', text: 'text-purple-100' };
      case 'tow':
        return { bg: 'bg-yellow-700', text: 'text-yellow-100' };
      default:
        return { bg: 'bg-blue-700', text: 'text-blue-100' };
    }
  };

  // Check if a user is currently active
  const isUserActive = (user) => {
    // Current user is always considered active in the UI
    if (user.uid === currentUser?.uid) return true;
    
    // If no lastActive timestamp, consider them offline
    if (!user.lastActive) return false;
    
    // If this is a placeholder user without activity data, they're offline
    if (user.isPlaceholder) return false;
    
    // Convert Firebase timestamp to Date if needed
    const lastActive = user.lastActive.toDate ? user.lastActive.toDate() : new Date(user.lastActive);
    
    // Consider users active if they've had activity in the last 5 minutes
    const cutoffTime = new Date(Date.now() - 5 * 60 * 1000);
    return lastActive > cutoffTime;
  };

  // Function to get text color based on background color for tags
  const getTextColor = (hexColor) => {
    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    
    // Calculate brightness (YIQ formula)
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    
    return yiq >= 150 ? '#000000' : '#FFFFFF';
  };

  return (
    <div className="border-b border-gray-700 w-full mb-3">
      <div className="p-2 flex justify-between items-center bg-gray-800 w-full rounded-t">
        <div className="flex items-center">
          <span className="text-xs font-medium text-gray-300">
            {teamMembers.length > 0 ? 'TEAM MEMBERS' : 'USERS'} ({allUsers.length})
          </span>
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-2 text-gray-400 hover:text-white"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-4 w-4 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
        {!teamMembersLoaded && (
          <span className="text-xs text-gray-400">
            <span className="animate-pulse">Loading team members...</span>
          </span>
        )}
        {teamMembersLoaded && teamMembers.length > 0 && (
          <span className="text-xs text-gray-400">
            {allUsers.filter(user => isUserActive(user)).length} active / {teamMembers.length} total
          </span>
        )}
      </div>
      
      {isExpanded && (
        <div 
          className="overflow-y-auto w-full bg-gray-900 rounded-b"
          style={{ maxHeight: `${sectionHeight}px` }}
        >
          {isLoading || !teamMembersLoaded ? (
            <div className="flex justify-center items-center py-6">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : allUsers && allUsers.length > 0 ? (
            <div className="divide-y divide-gray-700 w-full">
              {allUsers.map(user => {
                const isCurrentUser = currentUser && user.uid === currentUser.uid;
                const displayName = userDisplayNames[user.uid] || user.displayName || user.email || "Unknown User";
                const initials = getInitials(displayName);
                const profilePic = userProfilePictures[user.uid];
                const tags = userTags[user.uid] || [];
                const userRole = user.role || 'user';
                const roleTagStyle = getRoleTagColor(userRole);
                const userIsActive = isUserActive(user);
                
                // Get a deterministic color based on the user's ID
                const colorIndex = user.uid?.charCodeAt(0) % 8 || 0;
                const bgColors = [
                  'bg-blue-600', 'bg-purple-600', 'bg-green-600', 
                  'bg-red-600', 'bg-yellow-600', 'bg-pink-600',
                  'bg-indigo-600', 'bg-teal-600'
                ];
                const bgColor = bgColors[colorIndex];
                
                return (
                  <div 
                    key={user.uid || user.id} 
                    className={`flex items-center justify-between p-2 hover:bg-gray-700 w-full ${isCurrentUser ? 'bg-gray-800' : ''}`}
                  >
                    <div className="flex items-center min-w-0 w-full">
                      {/* Avatar with profile picture if available, otherwise initials */}
                      <div className={`w-8 h-8 rounded-full overflow-hidden mr-2 flex-shrink-0 ${bgColor} flex items-center justify-center text-white font-bold text-xs`}>
                        {profilePic ? (
                          <img src={profilePic} alt={displayName} className="w-full h-full object-cover" />
                        ) : (
                          initials
                        )}
                      </div>
                      
                      <div className="min-w-0 flex-1">
                        <div className="flex justify-between items-center">
                          <div className={`font-medium truncate ${screenConfig?.isSmallScreen ? 'text-xs' : 'text-sm'} ${isCurrentUser ? 'text-blue-300' : ''}`}>
                            {isCurrentUser ? 'You (' : ''}
                            {displayName}
                            {isCurrentUser ? ')' : ''}
                          </div>
                          
                          {!isCurrentUser && user.currentLocation && (
                            <button
                              onClick={() => navigateToUserLocation(user)}
                              className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded ml-1 flex-shrink-0 whitespace-nowrap"
                            >
                              Navigate
                            </button>
                          )}
                        </div>
                        
                        <div className="flex items-center">
                          <div className="text-xs text-gray-400 flex items-center mr-2">
                            {userIsActive ? (
                              <>
                                <span className="h-2 w-2 bg-green-500 rounded-full mr-1"></span>
                                Online
                              </>
                            ) : (
                              <>
                                <span className="h-2 w-2 bg-gray-500 rounded-full mr-1"></span>
                                Offline
                              </>
                            )}
                          </div>
                          
                          {/* Role tag */}
                          <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${roleTagStyle.bg} ${roleTagStyle.text}`}>
                            {userRole}
                          </span>
                        </div>
                        
                        {/* Display user tags */}
                        {tags && tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-1">
                            {tags.map((tag, tagIndex) => (
                              <span
                                key={tagIndex}
                                className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium"
                                style={{
                                  backgroundColor: tag.color || '#4B5563',
                                  color: tag.color ? getTextColor(tag.color) : '#FFFFFF'
                                }}
                              >
                                {tag.name || tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {isAdmin && (
                      <div className="flex space-x-1">
                        {/* Trail control button for admin */}
                        <button
                          onClick={() => handleConfirmDeleteTrail(user.uid)}
                          className="text-xs bg-red-900 hover:bg-red-800 text-red-200 px-2 py-1 rounded flex-shrink-0 flex items-center"
                          title="Clear location trail for this user"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          Trail
                        </button>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="p-4 text-center text-gray-400">
              {teamMembers.length > 0 ? 'No team members are currently online' : 'No users are currently online'}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TeamMembers;