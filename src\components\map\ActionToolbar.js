import React, { useContext } from 'react';
// In all components
import { MapContext } from '../MapContext';

// Format clock time string
const formatClockTime = (date) => {
  return date ? date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : '';
};

const ActionToolbar = () => {
  const {
    currentUser,
    isAdmin,
    isTowTruckUser,
    isClockedIn,
    clockInTime,
    totalMilesCovered,
    selectedLocation,
    isNavigating,
    setIsNavigating,
    isAddingMarker,
    setIsAddingMarker,
    isAddingAdminMarker, 
    setIsAddingAdminMarker,
    isSettingLocation,
    setIsSettingLocation,
    optimizedRoute,
    setSelectedLocation,
    setDetailsPanelLocation,
    setDetailsVisible,
    setIsFollowingOptimizedRoute
  } = useContext(MapContext);
  
  // Check if near Home location for clock in/out
  const isNearHomeLocation = () => {
    // This is actually implemented in UserLocation component
    // Here we're just simulating it for UI purposes
    return true; // Always allow in this demo
  };
  
  // Toggle clock in/out status with vehicle inspection
  const toggleClockStatus = () => {
    // This is implemented in UserLocation component
    // The button is still rendered here
    console.log("Toggle clock status - handled by UserLocation component");
  };
  
  // Start navigation to the selected location
  const startNavigation = () => {
    if (selectedLocation) {
      setIsNavigating(true);
    }
  };
  
  // Stop navigation
  const stopNavigation = () => {
    setIsNavigating(false);
    setIsFollowingOptimizedRoute(false);
  };
  
  // Start optimized route navigation
  const startOptimizedRoute = () => {
    if (optimizedRoute.length > 0) {
      setSelectedLocation(optimizedRoute[0].location);
      setDetailsPanelLocation(optimizedRoute[0].location);
      setDetailsVisible(true);
      setIsNavigating(true);
      setIsFollowingOptimizedRoute(true);
    }
  };

  return (
    <div className="p-2 bg-gray-800 border-t border-gray-700">
      <div className="flex justify-between items-center flex-wrap">
        <div className="flex items-center gap-2 flex-wrap">
          <button
            onClick={() => setIsAddingMarker(true)}
            disabled={!currentUser}
            className="bg-blue-600 hover:bg-blue-700 text-white px-2 sm:px-3 py-1 rounded flex items-center disabled:opacity-50 text-xs sm:text-sm"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            Add
          </button>
          
          {isAdmin && (
            <button
              onClick={() => setIsAddingAdminMarker(true)}
              className="bg-red-500 hover:bg-red-600 text-white px-2 sm:px-3 py-1 rounded flex items-center text-xs sm:text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Admin
            </button>
          )}
          
          <button
            onClick={() => setIsSettingLocation(true)}
            className="bg-gray-600 hover:bg-gray-700 text-white px-2 sm:px-3 py-1 rounded text-xs sm:text-sm flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
            </svg>
            Set Loc
          </button>
        </div>
        
        <div className="flex items-center gap-2 flex-wrap">
          <button
            onClick={toggleClockStatus}
            disabled={!isNearHomeLocation()}
            className={`px-2 sm:px-3 py-1 rounded flex items-center ${isClockedIn ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'} text-white text-xs sm:text-sm ${!isNearHomeLocation() ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
            {isClockedIn ? 'Clock Out' : 'Clock In'}
          </button>
          
          {isClockedIn && (
            <div className="text-gray-300 text-xs sm:text-sm ml-2">
              <span className="mr-2">{clockInTime ? formatClockTime(clockInTime) : ''}</span>
              <span>{totalMilesCovered.toFixed(1)} miles</span>
            </div>
          )}
          
          {selectedLocation && !isNavigating && (
            <button
              onClick={startNavigation}
              className="bg-green-500 hover:bg-green-600 text-white px-2 sm:px-3 py-1 rounded flex items-center text-xs sm:text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              Navigate
            </button>
          )}
          
          {isNavigating && (
            <button
              onClick={stopNavigation}
              className="bg-red-500 hover:bg-red-600 text-white px-2 sm:px-3 py-1 rounded text-xs sm:text-sm"
            >
              Stop Nav
            </button>
          )}
          
          {optimizedRoute.length > 1 && !isNavigating && (
            <button
              onClick={startOptimizedRoute}
              className="bg-yellow-500 hover:bg-yellow-600 text-white px-2 sm:px-3 py-1 rounded flex items-center text-xs sm:text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              Optimize
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActionToolbar;