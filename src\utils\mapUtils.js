/**
 * Utility functions for map operations
 */
import L from 'leaflet';
import 'leaflet.markercluster';
import { getDoc, doc } from 'firebase/firestore';

/**
 * Initialize a Leaflet map with proper configuration
 * 
 * @param {HTMLElement} container - DOM element to contain the map
 * @param {Object} options - Map initialization options
 * @returns {Object} Initialized Leaflet map object and layers
 */
export const initializeMap = (container, options = {}) => {
  if (!container) {
    console.error("Map container is required");
    return null;
  }
  
  const {
    center = { lat: 37.7749, lng: -122.4194 },
    zoom = 14,
    darkMode = true,
    maxZoom = 19
  } = options;
  
  try {
    // Create the map with appropriate tile layer
    const map = L.map(container, {
      center: [center.lat, center.lng],
      zoom: zoom,
      layers: [
        L.tileLayer(
          darkMode 
            ? 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png'
            : 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
          {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors' + 
                        (darkMode ? ' &copy; <a href="https://carto.com/attributions">CARTO</a>' : ''),
            maxZoom: maxZoom,
            updateWhenIdle: true,
            updateWhenZooming: false,
            updateInterval: 500
          }
        )
      ],
      zoomControl: true,
      attributionControl: true,
      preferCanvas: true, // Better performance with many markers
      renderer: L.canvas()
    });
    
    // Create marker clusters for better performance
    const markerCluster = L.markerClusterGroup({
      maxClusterRadius: 30,
      disableClusteringAtZoom: 15,
      spiderfyOnMaxZoom: true
    }).addTo(map);
    
    const userMarkerCluster = L.markerClusterGroup({
      maxClusterRadius: 50,
      disableClusteringAtZoom: 13,
      spiderfyOnMaxZoom: true
    }).addTo(map);
    
    // Create layer groups
    const userPathsLayer = L.layerGroup().addTo(map);
    const drivingPathLayer = L.layerGroup().addTo(map);
    
    return {
      map,
      markerCluster,
      userMarkerCluster,
      userPathsLayer,
      drivingPathLayer
    };
  } catch (error) {
    console.error("Error initializing map:", error);
    return null;
  }
};

/**
 * Create a user marker with optional profile picture
 * 
 * @param {Object} map - Leaflet map object
 * @param {Object} position - Position with lat/lng
 * @param {Object} options - Marker options
 * @returns {Object} Created marker object
 */
export const createUserMarker = (map, position, options = {}) => {
  if (!map || !position) {
    console.error("Map and position are required");
    return null;
  }
  
  try {
    const {
      profilePicture = null,
      displayName = 'You',
      isCurrentUser = true,
      zIndexOffset = 1000
    } = options;
    
    // Create icon based on whether there's a profile picture
    const userIcon = L.divIcon({
      className: isCurrentUser ? 'current-location-marker' : 'other-user-marker',
      html: profilePicture 
        ? `<div class="user-profile-marker ${!isCurrentUser ? 'other-user' : ''}" style="background-image: url('${profilePicture}');"></div>`
        : `<div class="${isCurrentUser ? 'current-location-pin' : 'other-user-pin'}"></div>`,
      iconSize: isCurrentUser ? [40, 40] : [36, 36],
      iconAnchor: isCurrentUser ? [20, 20] : [18, 18]
    });
    
    // Create marker
    const marker = L.marker([position.lat, position.lng], {
      icon: userIcon,
      title: displayName,
      zIndexOffset: zIndexOffset
    }).addTo(map);
    
    // Add user label if display name is provided
    let label = null;
    if (displayName) {
      label = L.marker([position.lat, position.lng], {
        icon: L.divIcon({
          className: 'user-label',
          html: `<div class="user-label-content ${isCurrentUser ? 'current-user' : ''}">${displayName}</div>`,
          iconSize: [100, 20],
          iconAnchor: [50, 0] // Position it at the top of the marker
        }),
        zIndexOffset: zIndexOffset + 1
      }).addTo(map);
    }
    
    return { marker, label };
  } catch (error) {
    console.error("Error creating user marker:", error);
    return null;
  }
};

/**
 * Create a location marker with custom styling
 * 
 * @param {Object} markerCluster - Leaflet marker cluster
 * @param {Object} location - Location data
 * @param {Function} onClick - Click handler function
 * @returns {Object} Created marker object
 */
export const createLocationMarker = (markerCluster, location, onClick) => {
  if (!markerCluster || !location) {
    console.error("Marker cluster and location are required");
    return null;
  }
  
  try {
    // Determine marker appearance
    const isSelected = location.isSelected || false;
    const markerClass = isSelected ? 'selected' : (location.status === 'picked-up' ? 'picked-up' : (location.isAdminOnly ? 'admin' : 'regular'));
    const priorityClass = location.isPriority ? 'priority' : '';
    
    // Create HTML for marker
    let markerHtml = '';
    
    if (location.images && location.images.length > 0) {
      // Use the first image as marker background
      markerHtml = `<div class="location-marker-image ${markerClass} ${priorityClass}" style="background-image: url('${location.images[0]}');">`;
      
      // Add status indicator if picked up
      if (location.status === 'picked-up') {
        markerHtml += `<div class="location-status">Picked Up</div>`;
      }
      
      markerHtml += `</div>`;
    } else {
      // Use fallback with first letter of location name
      const firstLetter = location.name.charAt(0).toUpperCase();
      markerHtml = `<div class="location-marker-fallback ${markerClass} ${priorityClass}">`;
      
      // Add status indicator if picked up
      if (location.status === 'picked-up') {
        markerHtml += `<div class="location-status">Picked Up</div>`;
      }
      
      markerHtml += `${firstLetter}</div>`;
    }
    
    // Create icon
    const icon = L.divIcon({
      className: 'location-marker',
      html: markerHtml,
      iconSize: [40, 40],
      iconAnchor: [20, 20]
    });
    
    // Create marker
    const marker = L.marker([location.position.lat, location.position.lng], {
      icon: icon,
      title: location.name,
      id: location.id,
      zIndexOffset: isSelected ? 800 : (location.status === 'picked-up' ? 750 : (location.isAdminOnly ? 700 : 600))
    });
    
    // Add click handler if provided
    if (onClick) {
      marker.on('click', () => onClick(location, marker));
    }
    
    // Add to marker cluster
    markerCluster.addLayer(marker);
    
    return marker;
  } catch (error) {
    console.error("Error creating location marker:", error);
    return null;
  }
};

/**
 * Create a navigation route between two points
 * 
 * @param {Object} map - Leaflet map object
 * @param {Object} from - Starting position with lat/lng
 * @param {Object} to - Destination position with lat/lng
 * @param {Object} options - Routing options
 * @returns {Object} Routing control object
 */
export const createRoute = (map, from, to, options = {}) => {
  if (!map || !from || !to) {
    console.error("Map and positions are required");
    return null;
  }
  
  try {
    // Default options
    const {
      fitRoute = true,
      showMarkers = false,
      lineColor = '#3B82F6',
      lineWeight = 5,
      onRouteFound = null,
      onRoutingError = null
    } = options;
    
    // Create routing control
    const routingControl = L.Routing.control({
      waypoints: [
        L.latLng(from.lat, from.lng),
        L.latLng(to.lat, to.lng)
      ],
      routeWhileDragging: false,
      router: L.Routing.osrmv1({
        serviceUrl: 'https://router.project-osrm.org/route/v1',
        profile: 'driving'
      }),
      lineOptions: {
        styles: [
          {color: lineColor, opacity: 0.8, weight: lineWeight}
        ],
        addWaypoints: false,
        extendToWaypoints: true,
        missingRouteTolerance: 0
      },
      createMarker: function(i, waypoint, n) {
        return showMarkers ? L.marker(waypoint.latLng) : null;
      },
      fitSelectedRoutes: fitRoute,
      showAlternatives: false,
      plan: new L.Routing.Plan([
        L.latLng(from.lat, from.lng),
        L.latLng(to.lat, to.lng)
      ], {
        createMarker: function(i, waypoint, n) {
          return showMarkers ? L.marker(waypoint.latLng) : null;
        },
        draggableWaypoints: false,
        routeWhileDragging: false
      }),
      collapsible: true,
      show: false // Don't show the instructions panel
    });
    
    // Add routing control to map
    routingControl.addTo(map);
    
    // Setup event handlers
    if (onRouteFound) {
      routingControl.on('routesfound', onRouteFound);
    }
    
    if (onRoutingError) {
      routingControl.on('routingerror', onRoutingError);
    }
    
    return routingControl;
  } catch (error) {
    console.error("Error creating route:", error);
    return null;
  }
};

/**
 * Calculate distance between two points in miles
 * @param {Object} point1 - Point with lat/lng
 * @param {Object} point2 - Point with lat/lng
 * @returns {number} - Distance in miles
 */
export const calculateDistance = (point1, point2) => {
  if (!point1 || !point2) return 0;
  
  const R = 3958.8; // Earth's radius in miles
  const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
  const φ2 = point2.lat * Math.PI/180;
  const Δφ = (point2.lat-point1.lat) * Math.PI/180;
  const Δλ = (point2.lng-point1.lng) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  const d = R * c; // in miles
  return d;
};

/**
 * Function to find the closest location from a position
 * @param {Object} fromPosition - Origin position with lat/lng
 * @param {Array} locationsList - Array of locations with position property
 * @returns {Object} - Object with closest location and distance
 */
export const findClosestLocation = (fromPosition, locationsList) => {
  if (!locationsList || locationsList.length === 0) return null;
  
  let closestLocation = null;
  let shortestDistance = Infinity;
  
  for (const location of locationsList) {
    const distance = calculateDistance(fromPosition, location.position);
    if (distance < shortestDistance) {
      shortestDistance = distance;
      closestLocation = location;
    }
  }
  
  return { location: closestLocation, distance: shortestDistance };
};

/**
 * Function to optimize route
 * @param {Object} startPosition - Start position with lat/lng
 * @param {Array} locationsList - Array of locations with position property
 * @returns {Array} - Optimized route as array of objects
 */
export const optimizeRoute = (startPosition, locationsList) => {
  if (!locationsList || locationsList.length === 0) return [];
  
  const unvisitedLocations = [...locationsList];
  const optimizedRoute = [];
  let currentPosition = startPosition;
  
  while (unvisitedLocations.length > 0) {
    const { location: closestLocation, distance } = findClosestLocation(currentPosition, unvisitedLocations);
    
    if (closestLocation) {
      optimizedRoute.push({
        location: closestLocation,
        distanceFromPrevious: distance
      });
      
      const index = unvisitedLocations.findIndex(loc => loc.id === closestLocation.id);
      if (index !== -1) {
        unvisitedLocations.splice(index, 1);
      }
      currentPosition = closestLocation.position;
    }
  }
  
  return optimizedRoute;
};

/**
 * Calculate bearing between two points in degrees
 * @param {Object} start - Start point with lat/lng
 * @param {Object} end - End point with lat/lng
 * @returns {number} - Bearing in degrees
 */
export const calculateBearing = (start, end) => {
  const startLat = start.lat * Math.PI / 180;
  const startLng = start.lng * Math.PI / 180;
  const endLat = end.lat * Math.PI / 180;
  const endLng = end.lng * Math.PI / 180;

  const y = Math.sin(endLng - startLng) * Math.cos(endLat);
  const x = Math.cos(startLat) * Math.sin(endLat) -
            Math.sin(startLat) * Math.cos(endLat) * Math.cos(endLng - startLng);
  
  const bearing = Math.atan2(y, x) * 180 / Math.PI;
  return (bearing + 360) % 360; // Normalize to 0-360
};

/**
 * Get direction text from bearing
 * @param {number} bearing - Bearing in degrees
 * @returns {string} - Direction text (North, Northeast, etc.)
 */
export const getDirectionText = (bearing) => {
  const directions = ['North', 'Northeast', 'East', 'Southeast', 'South', 'Southwest', 'West', 'Northwest'];
  const index = Math.round(bearing / 45) % 8;
  return directions[index];
};

/**
 * Get direction arrow emoji from bearing
 * @param {number} bearing - Bearing in degrees
 * @returns {string} - Direction arrow emoji
 */
export const getDirectionArrow = (bearing) => {
  // Map bearing to one of 8 cardinal directions with corresponding arrows
  const directions = [
    { min: 337.5, max: 360, arrow: '⬆️' }, // North
    { min: 0, max: 22.5, arrow: '⬆️' },    // North
    { min: 22.5, max: 67.5, arrow: '↗️' },  // Northeast
    { min: 67.5, max: 112.5, arrow: '➡️' }, // East
    { min: 112.5, max: 157.5, arrow: '↘️' }, // Southeast
    { min: 157.5, max: 202.5, arrow: '⬇️' }, // South
    { min: 202.5, max: 247.5, arrow: '↙️' }, // Southwest
    { min: 247.5, max: 292.5, arrow: '⬅️' }, // West
    { min: 292.5, max: 337.5, arrow: '↖️' }  // Northwest
  ];
  
  // Find the direction that matches the bearing
  for (const dir of directions) {
    if ((bearing >= dir.min && bearing < dir.max) || 
        (dir.min > dir.max && (bearing >= dir.min || bearing < dir.max))) {
      return dir.arrow;
    }
  }
  
  return '⬆️'; // Default to North if something goes wrong
};

/**
 * Format distance in human-readable form
 * @param {number} miles - Distance in miles
 * @returns {string} - Formatted distance string
 */
export const formatDistance = (miles) => {
  if (miles < 0.1) {
    return `${Math.round(miles * 5280)} feet`;
  } else {
    return `${miles.toFixed(1)} miles`;
  }
};

/**
 * Format time in human-readable form
 * @param {number} seconds - Time in seconds
 * @returns {string} - Formatted time string
 */
export const formatTime = (seconds) => {
  if (seconds < 60) {
    return `${Math.round(seconds)} seconds`;
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)} minutes`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
};

/**
 * Gets address information from coordinates using a geocoding service
 * @param {Object} position - Position with lat and lng properties
 * @returns {Promise<Object>} Promise that resolves to an address object
 */
export const getAddressFromCoordinates = async (position) => {
  if (!position) {
    throw new Error("No position provided");
  }
  
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.lat}&lon=${position.lng}&addressdetails=1`,
      {
        headers: {
          'User-Agent': 'MyMapApp/1.0' // Nominatim requires a user agent
        }
      }
    );
    
    if (!response.ok) {
      throw new Error("Geocoding service error");
    }
    
    const data = await response.json();
    
    return {
      formatted_address: data.display_name,
      streets: getStreetsFromNominatimResponse(data),
      raw: data
    };
  } catch (error) {
    console.error("Error getting address from coordinates:", error);
    throw error;
  }
};

/**
 * Extract street names from Nominatim response
 * @param {Object} data - Nominatim response data
 * @returns {Array} - Array of street names
 */
export const getStreetsFromNominatimResponse = (data) => {
  const streets = [];
  
  // Try to get road
  if (data.address && data.address.road) {
    streets.push(data.address.road);
  }
  
  // Try to get nearest roads
  if (data.address) {
    const possibleRoads = ['pedestrian', 'footway', 'path', 'street', 'residential', 'avenue', 'highway'];
    
    for (const roadType of possibleRoads) {
      if (data.address[roadType] && !streets.includes(data.address[roadType])) {
        streets.push(data.address[roadType]);
      }
    }
  }
  
  return streets;
};

/**
 * Gets a user's profile picture from Firestore
 * @param {string} userId - User ID
 * @param {Object} db - Firestore database reference
 * @returns {Promise<string>} Promise that resolves to the profile picture URL
 */
export const getUserProfilePicture = async (userId, db) => {
  if (!userId || !db) return null;
  
  try {
    const userDocRef = doc(db, "userProfiles", userId);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      if (userDoc.data().photoBase64) {
        return userDoc.data().photoBase64;
      } else if (userDoc.data().profilePicture) {
        return userDoc.data().profilePicture;
      }
    }
    
    return null;
  } catch (error) {
    console.error("Error getting user profile picture:", error);
    return null;
  }
};

/**
 * Gets a user's display name from Firestore
 * @param {string} userId - User ID
 * @param {Object} db - Firestore database reference
 * @returns {Promise<string>} Promise that resolves to the display name
 */
export const getUserDisplayName = async (userId, db) => {
  if (!userId || !db) return null;
  
  try {
    const userDocRef = doc(db, "userProfiles", userId);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      return userDoc.data().displayName || null;
    }
    
    return null;
  } catch (error) {
    console.error("Error getting user display name:", error);
    return null;
  }
};

/**
 * Checks if a user has a specific tag in Firestore
 * @param {string} userId - User ID
 * @param {string} tag - Tag to check for
 * @param {Object} db - Firestore database reference
 * @returns {Promise<boolean>} Promise that resolves to true if user has the tag
 */
export const checkUserHasTag = async (userId, tag, db) => {
  if (!userId || !tag || !db) return false;
  
  try {
    const userDocRef = doc(db, "userProfiles", userId);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists() && userDoc.data().tags) {
      const userTags = userDoc.data().tags;
      return Array.isArray(userTags) && userTags.includes(tag);
    }
    
    return false;
  } catch (error) {
    console.error("Error checking user tags:", error);
    return false;
  }
};

/**
 * Create a direct line between two points
 * @param {Object} map - Leaflet map object
 * @param {Object} from - Starting position with lat/lng
 * @param {Object} to - Destination position with lat/lng
 * @param {Object} options - Line options
 * @returns {Object} Polyline object
 */
export const createDirectLine = (map, from, to, options = {}) => {
  if (!map || !from || !to) {
    console.error("Map and positions are required");
    return null;
  }
  
  try {
    // Default options
    const {
      color = '#3B82F6',
      weight = 5,
      opacity = 0.8,
      dashArray = '10, 10',
      fitBounds = true
    } = options;
    
    // Create line
    const line = L.polyline([
      [from.lat, from.lng],
      [to.lat, to.lng]
    ], {
      color,
      weight,
      opacity,
      dashArray,
      lineCap: 'round'
    }).addTo(map);
    
    // Fit bounds if requested
    if (fitBounds) {
      map.fitBounds([
        [from.lat, from.lng],
        [to.lat, to.lng]
      ], { padding: [50, 50] });
    }
    
    return line;
  } catch (error) {
    console.error("Error creating direct line:", error);
    return null;
  }
};

/**
 * Create a user path (breadcrumb trail)
 * @param {Object} map - Leaflet map object
 * @param {Array} coordinates - Array of positions with lat/lng
 * @param {Object} options - Path options
 * @returns {Object} Polyline object
 */
export const createUserPath = (map, coordinates, options = {}) => {
  if (!map || !coordinates || coordinates.length < 2) {
    console.error("Map and at least two coordinates are required");
    return null;
  }
  
  try {
    // Default options
    const {
      color = '#4285F4',
      weight = 5,
      opacity = 0.8,
      dashArray = null,
      isCurrentUser = false
    } = options;
    
    // Determine line style based on whether it's current user
    const lineStyle = isCurrentUser 
      ? { color, weight, opacity } // Current user: solid, thicker
      : { color, weight: Math.max(2, weight - 2), opacity: Math.max(0.5, opacity - 0.2), dashArray: dashArray || '5, 10' }; // Other users: dashed, thinner
    
    // Create polyline
    const path = L.polyline(
      coordinates.map(pos => [pos.lat, pos.lng]), 
      lineStyle
    ).addTo(map);
    
    return path;
  } catch (error) {
    console.error("Error creating user path:", error);
    return null;
  }
};

/**
 * Generate consistent color from user ID
 * @param {string} userId - User ID
 * @returns {string} - Hex color code
 */
export const getUserColor = (userId) => {
  // List of distinct colors for different users
  const colors = [
    '#FF5555', // Red (default color)
    '#4CAF50', // Green
    '#2196F3', // Blue
    '#FF9800', // Orange
    '#9C27B0', // Purple
    '#00BCD4', // Cyan
    '#FFEB3B', // Yellow
    '#795548', // Brown
    '#009688', // Teal
    '#E91E63', // Pink
    '#673AB7', // Deep Purple
    '#FFC107', // Amber
    '#8BC34A', // Light Green
    '#03A9F4', // Light Blue
    '#FF5722', // Deep Orange
    '#607D8B'  // Blue Grey
  ];
  
  if (!userId) return colors[0]; // Default color
  
  // Create a simple hash from the user ID
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // Use the hash to pick a color
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

/**
 * Compress image to base64
 * @param {File} file - Image file
 * @param {number} maxWidth - Maximum width in pixels
 * @param {number} maxHeight - Maximum height in pixels
 * @param {number} quality - JPEG quality (0-1)
 * @returns {Promise} - Promise resolving to base64 string
 */
export const compressImageToBase64 = (file, maxWidth = 800, maxHeight = 800, quality = 0.7) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        // Create a canvas to resize the image
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;
        
        // Calculate new dimensions while maintaining aspect ratio
        if (width > height) {
          if (width > maxWidth) {
            height = Math.round(height * maxWidth / width);
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = Math.round(width * maxHeight / height);
            height = maxHeight;
          }
        }
        
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, width, height);
        
        // Get base64 representation
        const base64 = canvas.toDataURL('image/jpeg', quality);
        resolve(base64);
      };
      img.onerror = (error) => reject(error);
    };
    reader.onerror = (error) => reject(error);
  });
};

/**
 * Add metadata overlay to image
 * @param {string} imageBase64 - Base64 image
 * @param {Object} metadata - Metadata to overlay
 * @returns {Promise} - Promise resolving to base64 string with metadata
 */
export const addMetadataToImage = async (imageBase64, metadata) => {
  return new Promise((resolve, reject) => {
    try {
      const img = new Image();
      img.src = imageBase64;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const width = img.width;
        const height = img.height;
        
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        
        // Draw the original image
        ctx.drawImage(img, 0, 0, width, height);
        
        // Add semi-transparent background for text
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(0, height - 80, width, 80);
        
        // Add metadata text
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        
        // Format date and time
        const dateTime = metadata.dateTime ? 
          metadata.dateTime.toLocaleString() : 
          new Date().toLocaleString();
        
        ctx.fillText(`Date/Time: ${dateTime}`, 10, height - 60);
        
        if (metadata.latitude && metadata.longitude) {
          ctx.fillText(`Location: ${metadata.latitude.toFixed(6)}, ${metadata.longitude.toFixed(6)}`, 10, height - 40);
        }
        
        if (metadata.address) {
          ctx.fillText(`Address: ${metadata.address}`, 10, height - 20);
        }
        
        // Convert back to base64
        const newImageBase64 = canvas.toDataURL('image/jpeg', 0.7);
        resolve(newImageBase64);
      };
      img.onerror = (error) => reject(error);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Create a custom navigation panel element
 * @param {HTMLElement} mapContainer - Map container element
 * @param {Object} content - Panel content data
 * @returns {HTMLElement} Created panel element
 */
export const createNavigationPanel = (mapContainer, content) => {
  if (!mapContainer) {
    console.error("Map container is required");
    return null;
  }
  
  try {
    // Extract content data
    const {
      imageUrl = null,
      locationName = 'Destination',
      parkingSide = null,
      vehicleInfo = null,
      status = null,
      distance = null,
      duration = null,
      direction = null,
      directionArrow = null
    } = content;
    
    // Create panel element
    const navPanel = document.createElement('div');
    navPanel.className = 'navigation-panel dark-mode';
    
    // Parking side indicator HTML
    const parkingSideHtml = parkingSide ? `
      <div class="parking-side-indicator">
        Vehicle Parked on ${parkingSide.charAt(0).toUpperCase() + parkingSide.slice(1)} Side 
        ${parkingSide === 'left' ? '←' : '→'}
      </div>
    ` : '';
    
    // Status indicator HTML
    const statusHtml = status === 'picked-up' ? `
      <div class="parking-side-indicator" style="background-color: #10B981;">
        Picked Up
      </div>
    ` : '';
    
    // Vehicle info HTML
    const vehicleInfoHtml = vehicleInfo ? `
      <div class="vehicle-info" style="font-size: 10px; margin-top: 3px; margin-bottom: 3px;">
        ${vehicleInfo.make && vehicleInfo.model ? `${vehicleInfo.make} ${vehicleInfo.model}` : ''}
        ${vehicleInfo.plateNumber ? `• Plate: ${vehicleInfo.plateNumber}` : ''}
      </div>
    ` : '';
    
    // Generate HTML
    navPanel.innerHTML = `
      <div class="navigation-image">
        ${imageUrl ? `<img src="${imageUrl}" alt="Destination" style="width:100%; height:100%; object-fit:cover;">` : 
        `<div style="color: #9CA3AF; text-align: center;">No image available</div>`}
      </div>
      <div class="navigation-details">
        ${parkingSideHtml}
        ${statusHtml}
        <div class="navigation-destination">${locationName || 'Destination'}</div>
        ${vehicleInfoHtml}
        <div class="navigation-distance-time">
          <div>${distance || 'Calculating...'}</div>
          <div>${duration || 'Calculating...'}</div>
        </div>
        <div class="navigation-direction">
          <div class="navigation-direction-arrow">${directionArrow || '⏳'}</div>
          <div>${direction || 'Calculating route...'}</div>
        </div>
        <button id="stop-navigation-btn" style="
          background-color: #EF4444;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 4px 8px;
          margin-top: 8px;
          width: 100%;
          cursor: pointer;
          font-size: 12px;
        ">Stop</button>
      </div>
    `;
    
    // Add to map container
    mapContainer.appendChild(navPanel);
    
    return navPanel;
  } catch (error) {
    console.error("Error creating navigation panel:", error);
    return null;
  }
};

/**
 * Plays a notification sound
 * @param {string} soundName - Optional name of the sound to play
 */
export const playNotificationSound = (soundName = 'default') => {
  try {
    let audioFile = './assets/audio/notification.mp3'; // Default sound
    
    // Map sound names to file paths
    const soundMap = {
      default: './assets/audio/notification.mp3',
      alert: './assets/audio/alert.mp3',
      message: './assets/audio/message.mp3',
      update: './assets/audio/update.mp3'
    };
    
    if (soundMap[soundName]) {
      audioFile = soundMap[soundName];
    }
    
    // Create and play audio
    const audio = new Audio(audioFile);
    audio.play().catch(error => {
      console.warn("Could not play notification sound:", error);
      // Fallback to vibration if available
      if (window.navigator && window.navigator.vibrate) {
        window.navigator.vibrate(200);
      }
    });
  } catch (error) {
    console.error("Error playing notification sound:", error);
  }
};

/**
 * Reset map view (clear layers and restore defaults)
 * @param {Object} map - Leaflet map
 * @param {Object} options - Reset options
 */
export const resetMapView = (map, options = {}) => {
  if (!map) {
    console.error("Map is required");
    return;
  }
  
  try {
    const {
      center = null,
      zoom = null,
      preserveCenter = true
    } = options;
    
    // Store current center and zoom if preserving
    const currentCenter = preserveCenter ? map.getCenter() : null;
    const currentZoom = preserveCenter ? map.getZoom() : null;
    
    // Clear all layers except base tile layer
    map.eachLayer(layer => {
      if (!(layer instanceof L.TileLayer)) {
        map.removeLayer(layer);
      }
    });
    
    // Restore view
    map.setView(
      center ? [center.lat, center.lng] : (currentCenter ? [currentCenter.lat, currentCenter.lng] : map.getCenter()),
      zoom || currentZoom || map.getZoom()
    );
  } catch (error) {
    console.error("Error resetting map view:", error);
  }
};

/**
 * Creates a debounced function that delays invoking func until after wait milliseconds
 * @param {Function} func - Function to debounce
 * @param {number} wait - Milliseconds to wait
 * @param {Object} options - Optional configuration
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait = 300, options = {}) => {
  let timeout;
  
  return function executedFunction(...args) {
    const context = this;
    const later = () => {
      timeout = null;
      if (!options.leading) func.apply(context, args);
    };
    
    const callNow = options.leading && !timeout;
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(context, args);
  };
};