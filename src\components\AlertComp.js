import React, { useState, useEffect, useRef } from 'react';
import { collection, doc, getDoc, setDoc, updateDoc, getFirestore } from 'firebase/firestore';

const AlertComp = ({ 
  currentUser, 
  isAdmin, 
  teamId,
  setMapContainerClass // Function to signal alert system status
}) => {
  // Initialize Firestore
  const db = getFirestore();
  
  // State for the alert system
  const [isAlerting, setIsAlerting] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [volume, setVolume] = useState(50);
  const [customSound, setCustomSound] = useState(null);
  const [soundName, setSoundName] = useState('Default Alarm');
  const [soundUrl, setSoundUrl] = useState(null);
  const [strobeSpeed, setStrobeSpeed] = useState(300); // ms between flashes
  const [isMuted, setIsMuted] = useState(false); // New state for mute functionality
  const [isComponentMounted, setIsComponentMounted] = useState(false);
  
  // New states for strobe patterns and colors
  const [strobePattern, setStrobePattern] = useState('alternating');
  const [colorCombination, setColorCombination] = useState('red-white');
  
  // Refs for the audio element and strobe intervals
  const audioRef = useRef(null);
  const strobeIntervalsRef = useRef([]);
  const fileInputRef = useRef(null);
  
  // FIXED: Better component mounting and map integration
  useEffect(() => {
    // Signal that alert component is mounted
    setIsComponentMounted(true);
    
    if (setMapContainerClass) {
      setMapContainerClass('alert-enabled-map');
    }
    
    return () => {
      // Cleanup on unmount
      setIsComponentMounted(false);
      
      if (setMapContainerClass) {
        setMapContainerClass('');
      }
      
      // Stop any active alerts
      stopAlert();
    };
  }, [setMapContainerClass]);
  
  // Load user alert settings when component mounts
  useEffect(() => {
    const loadUserSettings = async () => {
      if (!currentUser || !currentUser.uid) return;
      
      try {
        const userSettingsRef = doc(db, 'userAlertSettings', currentUser.uid);
        const docSnap = await getDoc(userSettingsRef);
        
        if (docSnap.exists()) {
          const data = docSnap.data();
          setVolume(data.volume || 50);
          setSoundName(data.soundName || 'Default Alarm');
          setSoundUrl(data.soundUrl || null);
          setStrobeSpeed(data.strobeSpeed || 300);
          setStrobePattern(data.strobePattern || 'alternating');
          setColorCombination(data.colorCombination || 'red-white');
        }
      } catch (error) {
        console.error('Error loading alert settings:', error);
      }
    };
    
    if (isComponentMounted) {
      loadUserSettings();
    }
  }, [currentUser, db, isComponentMounted]);
  
  // Function to apply colors based on the selected color combination
  const applyColors = () => {
    const leftContainer = document.querySelector('.alert-box-left');
    const rightContainer = document.querySelector('.alert-box-right');
    
    if (!leftContainer || !rightContainer) return;
    
    // Remove all previous color classes
    const allStrobeLights = document.querySelectorAll('.strobe-light');
    allStrobeLights.forEach(light => {
      light.classList.remove('red-light', 'white-light', 'blue-light', 'green-light', 'amber-light', 'purple-light');
    });
    
    const leftLights = leftContainer.querySelectorAll('.strobe-light');
    const rightLights = rightContainer.querySelectorAll('.strobe-light');
    
    switch (colorCombination) {
      case 'red-white':
        // Original colors: alternating red and white
        leftLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'red-light' : 'white-light');
        });
        rightLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'white-light' : 'red-light');
        });
        break;
        
      case 'blue-white':
        // Blue and white combination
        leftLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'blue-light' : 'white-light');
        });
        rightLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'white-light' : 'blue-light');
        });
        break;
        
      case 'red-blue':
        // Red and blue combination
        leftLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'red-light' : 'blue-light');
        });
        rightLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'blue-light' : 'red-light');
        });
        break;
        
      case 'green-white':
        // Green and white combination
        leftLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'green-light' : 'white-light');
        });
        rightLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'white-light' : 'green-light');
        });
        break;
        
      case 'amber-blue':
        // Amber and blue combination
        leftLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'amber-light' : 'blue-light');
        });
        rightLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'blue-light' : 'amber-light');
        });
        break;
        
      case 'rainbow':
        // Rainbow pattern with multiple colors
        const colors = ['red-light', 'amber-light', 'green-light', 'blue-light', 'purple-light', 'white-light'];
        leftLights.forEach((light, index) => {
          light.classList.add(colors[index % colors.length]);
        });
        rightLights.forEach((light, index) => {
          light.classList.add(colors[(index + 3) % colors.length]);
        });
        break;
        
      default:
        // Default to red-white if unknown combination
        leftLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'red-light' : 'white-light');
        });
        rightLights.forEach((light, index) => {
          light.classList.add(index % 2 === 0 ? 'white-light' : 'red-light');
        });
    }
  };
  
  // Apply the selected strobe pattern
  const applyStrobePattern = (elements) => {
    // Clear any existing intervals
    stopStrobeEffects();
    
    switch (strobePattern) {
      case 'alternating':
        // Alternating pattern (one side at a time)
        const leftLights = document.querySelectorAll('.alert-box-left .strobe-light');
        const rightLights = document.querySelectorAll('.alert-box-right .strobe-light');
        
        let leftActive = true;
        const interval = setInterval(() => {
          if (leftActive) {
            leftLights.forEach(light => light.classList.add('active'));
            rightLights.forEach(light => light.classList.remove('active'));
          } else {
            leftLights.forEach(light => light.classList.remove('active'));
            rightLights.forEach(light => light.classList.add('active'));
          }
          leftActive = !leftActive;
        }, strobeSpeed);
        
        strobeIntervalsRef.current.push(interval);
        break;
        
      case 'sequential':
        // Sequential pattern (lights in sequence)
        let currentIndex = 0;
        const sequentialInterval = setInterval(() => {
          elements.forEach((element, index) => {
            if (index === currentIndex) {
              element.classList.add('active');
            } else {
              element.classList.remove('active');
            }
          });
          currentIndex = (currentIndex + 1) % elements.length;
        }, strobeSpeed / 2);
        
        strobeIntervalsRef.current.push(sequentialInterval);
        break;
        
      case 'random':
        // Random pattern (random lights on/off)
        const randomInterval = setInterval(() => {
          elements.forEach((element) => {
            if (Math.random() > 0.5) {
              element.classList.toggle('active');
            }
          });
        }, strobeSpeed);
        
        strobeIntervalsRef.current.push(randomInterval);
        break;
        
      case 'allOn':
        // All lights on/off together
        const allOnInterval = setInterval(() => {
          const shouldBeActive = !elements[0].classList.contains('active');
          elements.forEach(element => {
            if (shouldBeActive) {
              element.classList.add('active');
            } else {
              element.classList.remove('active');
            }
          });
        }, strobeSpeed);
        
        strobeIntervalsRef.current.push(allOnInterval);
        break;
        
      case 'chaotic':
        // Chaotic pattern with individual timing
        elements.forEach((element) => {
          const randomSpeed = strobeSpeed + (Math.random() * 200) - 100; // ±100ms variation
          const chaosInterval = setInterval(() => {
            element.classList.toggle('active');
          }, randomSpeed);
          
          strobeIntervalsRef.current.push(chaosInterval);
        });
        break;
        
      case 'pulse':
        // Pulsing pattern (fade in/out effect using opacity)
        let pulseDirection = true; // true = getting brighter, false = dimming
        let opacityLevel = 0;
        
        elements.forEach(element => element.classList.add('active'));
        
        const pulseInterval = setInterval(() => {
          if (pulseDirection) {
            opacityLevel += 0.1;
            if (opacityLevel >= 1) {
              opacityLevel = 1;
              pulseDirection = false;
            }
          } else {
            opacityLevel -= 0.1;
            if (opacityLevel <= 0) {
              opacityLevel = 0;
              pulseDirection = true;
            }
          }
          
          elements.forEach(element => {
            element.style.opacity = opacityLevel;
          });
        }, strobeSpeed / 10);
        
        strobeIntervalsRef.current.push(pulseInterval);
        break;
        
      default:
        // Default to simple flashing if pattern unknown
        elements.forEach((element) => {
          const interval = setInterval(() => {
            element.classList.toggle('active');
          }, strobeSpeed);
          
          strobeIntervalsRef.current.push(interval);
        });
    }
  };
  
  // Function to trigger the alert system
  const triggerAlert = () => {
    if (isAlerting) return; // Prevent multiple triggers
    
    setIsAlerting(true);
    
    // Apply selected colors
    applyColors();
    
    // Get all strobe elements
    const strobeElements = document.querySelectorAll('.strobe-light');
    
    // Apply the selected strobe pattern
    applyStrobePattern(strobeElements);
    
    // Play the alert sound if not muted
    if (audioRef.current && !isMuted) {
      audioRef.current.volume = volume / 100;
      audioRef.current.play().catch(err => console.error('Error playing audio:', err));
    }
    
    // Stop the alert after 6 seconds
    setTimeout(() => {
      stopAlert();
    }, 6000);
  };
  
  // Function to stop the alert
  const stopAlert = () => {
    stopStrobeEffects();
    
    // Reset opacity if pulse pattern was used
    const strobeElements = document.querySelectorAll('.strobe-light');
    strobeElements.forEach(element => {
      element.style.opacity = 1;
    });
    
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    
    setIsAlerting(false);
  };
  
  // Function to toggle mute
  const toggleMute = () => {
    setIsMuted(!isMuted);
    
    // If currently alerting and audio is playing, stop it
    if (isAlerting && audioRef.current && !isMuted) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
  };
  
  // Function to stop strobe effects
  const stopStrobeEffects = () => {
    // Clear all intervals
    strobeIntervalsRef.current.forEach(interval => clearInterval(interval));
    strobeIntervalsRef.current = [];
    
    // Reset all strobe lights
    const strobeElements = document.querySelectorAll('.strobe-light');
    strobeElements.forEach(element => {
      element.classList.remove('active');
    });
  };
  
  // Function to open the settings modal
  const openSettings = () => {
    setShowSettings(true);
  };
  
  // Function to save settings
  const saveSettings = async () => {
    if (!currentUser || !currentUser.uid) return;
    
    try {
      const userSettingsRef = doc(db, 'userAlertSettings', currentUser.uid);
      
      await setDoc(userSettingsRef, {
        volume,
        soundName,
        soundUrl,
        strobeSpeed,
        strobePattern,
        colorCombination,
        updatedAt: new Date()
      }, { merge: true });
      
      setShowSettings(false);
    } catch (error) {
      console.error('Error saving alert settings:', error);
      alert('Failed to save settings. Please try again.');
    }
  };
  
  // Function to handle sound file upload
  const handleSoundUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    // Check if it's an MP3 file
    if (!file.type.includes('audio/mp3') && !file.type.includes('audio/mpeg')) {
      alert('Please select an MP3 file.');
      return;
    }
    
    // Check file size (limit to 2MB)
    if (file.size > 2 * 1024 * 1024) {
      alert('File size exceeds 2MB limit. Please choose a smaller file.');
      return;
    }
    
    // Read the file as a data URL
    const reader = new FileReader();
    reader.onload = (event) => {
      setCustomSound(event.target.result);
      setSoundUrl(event.target.result);
      setSoundName(file.name);
    };
    reader.onerror = () => {
      alert('Error reading file. Please try again.');
    };
    reader.readAsDataURL(file);
  };
  
  // Function to handle strobe speed change
  const handleStrobeSpeedChange = (value) => {
    // Convert the value to a speed between 100ms and 500ms
    // Lower value = faster strobing
    const speed = 500 - value * 4; // 0-100 slider to 500-100ms range
    setStrobeSpeed(speed);
  };
  
  // Function to preview strobe pattern
  const previewStrobePattern = () => {
    if (isAlerting) {
      stopAlert();
    }
    
    // Apply colors first
    applyColors();
    
    // Start a short preview
    setIsAlerting(true);
    
    const strobeElements = document.querySelectorAll('.strobe-light');
    applyStrobePattern(strobeElements);
    
    // Stop after 3 seconds
    setTimeout(() => {
      stopAlert();
    }, 3000);
  };

  // FIXED: Only render if component is properly mounted
  if (!isComponentMounted) {
    return null;
  }
  
  return (
    <>
      {/* FIXED: Improved CSS with better z-index management */}
      <style>
        {`
          /* FIXED: Strobe light containers with better positioning */
          .alert-box-container {
            position: absolute;
            height: 100%;
            width: 20px;
            display: flex;
            flex-direction: column;
            z-index: 500; /* Lower than map controls but higher than map */
            pointer-events: none;
            top: 0;
          }
          
          .alert-box-left {
            left: 0;
            border-right: 1px solid #374151;
          }
          
          .alert-box-right {
            right: 0;
            border-left: 1px solid #374151;
          }
          
          /* Strobe light styling */
          .strobe-light {
            flex: 1;
            transition: background-color 0.1s ease;
            position: relative;
            width: 20px;
          }
          
          /* Base colors */
          .red-light {
            background-color: #330000;
          }
          
          .white-light {
            background-color: #111111;
          }
          
          .blue-light {
            background-color: #000033;
          }
          
          .green-light {
            background-color: #003300;
          }
          
          .amber-light {
            background-color: #332200;
          }
          
          .purple-light {
            background-color: #220033;
          }
          
          /* Active colors */
          .red-light.active {
            background-color: #ff0000;
            box-shadow: 0 0 20px #ff0000;
          }
          
          .white-light.active {
            background-color: #ffffff;
            box-shadow: 0 0 20px #ffffff;
          }
          
          .blue-light.active {
            background-color: #0000ff;
            box-shadow: 0 0 20px #0000ff;
          }
          
          .green-light.active {
            background-color: #00ff00;
            box-shadow: 0 0 20px #00ff00;
          }
          
          .amber-light.active {
            background-color: #ffbf00;
            box-shadow: 0 0 20px #ffbf00;
          }
          
          .purple-light.active {
            background-color: #cc00ff;
            box-shadow: 0 0 20px #cc00ff;
          }
          
          /* FIXED: Alert control container with better positioning */
          .alert-controls-container {
            position: absolute;
            top: 10px;
            left: 30px;
            z-index: 600; /* Higher than strobe lights */
            display: flex;
            gap: 10px;
            pointer-events: auto;
          }
          
          /* Settings icon */
          .settings-icon {
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            background-color: rgba(55, 65, 81, 0.8);
            padding: 5px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: color 0.2s, background-color 0.2s;
            height: 30px;
            width: 30px;
            backdrop-filter: blur(4px);
          }
          
          .settings-icon:hover {
            color: white;
            background-color: rgba(75, 85, 101, 0.9);
          }
          
          /* FIXED: Center alert button container with better z-index */
          .center-alert-container {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 600; /* Higher than strobe lights */
            pointer-events: auto;
            display: flex;
            gap: 10px;
          }
          
          /* FIXED: Settings modal with highest z-index */
          .settings-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 9999; /* Highest z-index */
            display: flex;
            justify-content: center;
            align-items: center;
          }
          
          .settings-content {
            background-color: #1F2937;
            border-radius: 0.5rem;
            padding: 1.5rem;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            max-height: 90vh;
            overflow-y: auto;
            border: 2px solid #3B82F6;
          }
          
          /* Test button for admins */
          .admin-test-button {
            padding: 5px 10px;
            background-color: #DC2626;
            color: white;
            border-radius: 4px;
            font-weight: 600;
            font-size: 12px;
            border: none;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: background-color 0.2s, transform 0.1s;
            height: 30px;
            display: flex;
            align-items: center;
            backdrop-filter: blur(4px);
          }
          
          .admin-test-button:hover {
            background-color: #B91C1C;
          }
          
          .admin-test-button:active {
            transform: scale(0.95);
          }
          
          /* Mute button */
          .mute-button {
            padding: 5px 10px;
            background-color: #4B5563;
            color: white;
            border-radius: 4px;
            font-weight: 600;
            font-size: 12px;
            border: none;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: background-color 0.2s, transform 0.1s;
            height: 30px;
            display: flex;
            align-items: center;
            backdrop-filter: blur(4px);
          }
          
          .mute-button:hover {
            background-color: #374151;
          }
          
          .mute-button:active {
            transform: scale(0.95);
          }
          
          .mute-button.muted {
            background-color: #9CA3AF;
          }
          
          /* FIXED: Better alert-enabled-map styling */
          .alert-enabled-map {
            position: relative;
          }
          
          /* FIXED: Ensure map container doesn't interfere with alerts */
          .alert-enabled-map .leaflet-container {
            position: relative;
            z-index: 1;
          }
          
          /* FIXED: Ensure map controls stay above alerts */
          .alert-enabled-map .map-controls {
            z-index: 700 !important;
          }
          
          /* Select boxes in settings */
          .settings-select {
            width: 100%;
            padding: 0.5rem;
            border-radius: 0.25rem;
            background-color: #374151;
            color: white;
            border: 1px solid #4B5563;
            margin-top: 0.5rem;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 1rem;
          }
          
          /* Colors preview box */
          .color-preview-container {
            display: flex;
            margin-top: 0.5rem;
            height: 30px;
            border-radius: 4px;
            overflow: hidden;
          }
          
          .color-preview-item {
            flex: 1;
            height: 100%;
          }
          
          /* Preview button */
          .preview-button {
            padding: 5px 10px;
            background-color: #4B5563;
            color: white;
            border-radius: 4px;
            font-weight: 600;
            font-size: 12px;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-top: 0.5rem;
          }
          
          .preview-button:hover {
            background-color: #374151;
          }
          
          /* Responsive adjustments for mobile */
          @media (max-width: 640px) {
            .alert-controls-container {
              top: 5px;
              left: 25px;
            }
            
            .center-alert-container {
              top: 5px;
            }
            
            .settings-icon {
              height: 24px;
              width: 24px;
            }
            
            .admin-test-button, .mute-button {
              padding: 3px 8px;
              font-size: 11px;
              height: 24px;
            }
            
            .settings-content {
              width: 95%;
              padding: 1rem;
            }
          }
        `}
      </style>
      
      {/* Alert controls container - Settings icon only now */}
      <div className="alert-controls-container">
        {/* Settings gear icon */}
        <div className="settings-icon-container">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="settings-icon" 
            width="20" 
            height="20" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
            onClick={openSettings}
          >
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
          </svg>
        </div>
      </div>
      
      {/* Center container for Alert and Mute buttons */}
      <div className="center-alert-container">
        {/* Admin test button - Moved to center */}
        {isAdmin && (
          <button 
            className="admin-test-button"
            onClick={triggerAlert}
            title="Test Alert System"
          >
            Test Alert
          </button>
        )}
        
        {/* Mute button */}
        <button 
          className={`mute-button ${isMuted ? 'muted' : ''}`}
          onClick={toggleMute}
          title={isMuted ? "Unmute Alerts" : "Mute Alerts"}
        >
          {isMuted ? (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '4px' }}>
                <line x1="1" y1="1" x2="23" y2="23"></line>
                <path d="M9 9v3a3 3 0 0 0 5.12 2.12M15 9.34V4a3 3 0 0 0-5.94-.6"></path>
                <path d="M17 16.95A7 7 0 0 1 5 12v-2m14 0v2a7 7 0 0 1-.11 1.23"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
              </svg>
              Unmute
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '4px' }}>
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
              </svg>
              Mute
            </>
          )}
        </button>
      </div>
      
      {/* Left alert boxes */}
      <div className="alert-box-container alert-box-left">
        <div className="strobe-light red-light"></div>
        <div className="strobe-light white-light"></div>
        <div className="strobe-light red-light"></div>
        <div className="strobe-light white-light"></div>
        <div className="strobe-light red-light"></div>
        <div className="strobe-light white-light"></div>
      </div>
      
      {/* Right alert boxes */}
      <div className="alert-box-container alert-box-right">
        <div className="strobe-light white-light"></div>
        <div className="strobe-light red-light"></div>
        <div className="strobe-light white-light"></div>
        <div className="strobe-light red-light"></div>
        <div className="strobe-light white-light"></div>
        <div className="strobe-light red-light"></div>
      </div>
      
      {/* Settings Modal */}
      {showSettings && (
        <div className="settings-modal">
          <div className="settings-content">
            <h2 className="text-xl font-bold mb-4 text-white">Alert System Settings</h2>
            
            {/* Volume Control */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">Alert Volume</label>
              <input 
                type="range" 
                min="0" 
                max="100" 
                value={volume} 
                onChange={(e) => setVolume(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-gray-400 text-xs mt-1">
                <span>0%</span>
                <span>{volume}%</span>
                <span>100%</span>
              </div>
            </div>
            
            {/* Strobe Speed Control */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">Strobe Speed</label>
              <input 
                type="range" 
                min="0" 
                max="100" 
                value={(500 - strobeSpeed) / 4} // Convert 500-100ms to 0-100 slider
                onChange={(e) => handleStrobeSpeedChange(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-gray-400 text-xs mt-1">
                <span>Slow</span>
                <span>Medium</span>
                <span>Fast</span>
              </div>
            </div>
            
            {/* Strobe Pattern Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Strobe Pattern
              </label>
              <select 
                value={strobePattern}
                onChange={(e) => setStrobePattern(e.target.value)}
                className="settings-select"
              >
                <option value="alternating">Alternating (Left-Right)</option>
                <option value="sequential">Sequential</option>
                <option value="random">Random</option>
                <option value="allOn">All On/Off</option>
                <option value="chaotic">Chaotic</option>
                <option value="pulse">Pulse Effect</option>
              </select>
              <button 
                className="preview-button w-full"
                onClick={previewStrobePattern}
              >
                Preview Pattern
              </button>
            </div>
            
            {/* Color Combination Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Color Combination
              </label>
              <select 
                value={colorCombination}
                onChange={(e) => setColorCombination(e.target.value)}
                className="settings-select"
              >
                <option value="red-white">Red & White (Classic)</option>
                <option value="blue-white">Blue & White</option>
                <option value="red-blue">Red & Blue</option>
                <option value="green-white">Green & White</option>
                <option value="amber-blue">Amber & Blue</option>
                <option value="rainbow">Rainbow</option>
              </select>
              
              {/* Color preview box */}
              <div className="color-preview-container">
                {colorCombination === 'red-white' && (
                  <>
                    <div className="color-preview-item" style={{ backgroundColor: '#ff0000' }}></div>
                    <div className="color-preview-item" style={{ backgroundColor: '#ffffff' }}></div>
                  </>
                )}
                {colorCombination === 'blue-white' && (
                  <>
                    <div className="color-preview-item" style={{ backgroundColor: '#0000ff' }}></div>
                    <div className="color-preview-item" style={{ backgroundColor: '#ffffff' }}></div>
                  </>
                )}
                {colorCombination === 'red-blue' && (
                  <>
                    <div className="color-preview-item" style={{ backgroundColor: '#ff0000' }}></div>
                    <div className="color-preview-item" style={{ backgroundColor: '#0000ff' }}></div>
                  </>
                )}
                {colorCombination === 'green-white' && (
                  <>
                    <div className="color-preview-item" style={{ backgroundColor: '#00ff00' }}></div>
                    <div className="color-preview-item" style={{ backgroundColor: '#ffffff' }}></div>
                  </>
                )}
                {colorCombination === 'amber-blue' && (
                  <>
                    <div className="color-preview-item" style={{ backgroundColor: '#ffbf00' }}></div>
                    <div className="color-preview-item" style={{ backgroundColor: '#0000ff' }}></div>
                  </>
                )}
                {colorCombination === 'rainbow' && (
                  <>
                    <div className="color-preview-item" style={{ backgroundColor: '#ff0000' }}></div>
                    <div className="color-preview-item" style={{ backgroundColor: '#ffbf00' }}></div>
                    <div className="color-preview-item" style={{ backgroundColor: '#00ff00' }}></div>
                    <div className="color-preview-item" style={{ backgroundColor: '#0000ff' }}></div>
                    <div className="color-preview-item" style={{ backgroundColor: '#cc00ff' }}></div>
                    <div className="color-preview-item" style={{ backgroundColor: '#ffffff' }}></div>
                  </>
                )}
              </div>
            </div>
            
            {/* Custom Sound Upload */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Custom Alert Sound ({soundName})
              </label>
              <input
                ref={fileInputRef}
                type="file"
                accept=".mp3,audio/mp3,audio/mpeg"
                onChange={handleSoundUpload}
                className="hidden"
              />
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => fileInputRef.current.click()}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm"
                >
                  Choose MP3 File
                </button>
                {soundUrl && (
                  <button
                    onClick={() => {
                      if (audioRef.current) {
                        audioRef.current.volume = volume / 100;
                        audioRef.current.play();
                      }
                    }}
                    className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md text-sm"
                  >
                    Preview
                  </button>
                )}
              </div>
              <p className="text-xs text-gray-400 mt-2">MP3 files only, 2MB maximum</p>
            </div>
            
            {/* Status Information */}
            <div className="mb-4 p-3 bg-gray-800 rounded-lg">
              <div className="text-sm text-gray-300">
                <div className="mb-2">
                  <strong>Alert System Status:</strong> {isComponentMounted ? '✅ Active' : '❌ Inactive'}
                </div>
                {isAdmin && (
                  <div className="mb-2 text-red-300">
                    <strong>Admin Mode:</strong> ✅ Test alerts available
                  </div>
                )}
                <div className="mb-2">
                  <strong>Audio:</strong> {isMuted ? '🔇 Muted' : '🔊 Enabled'}
                </div>
                <div className="text-xs text-gray-400">
                  Team: {teamId || 'Not specified'} • User: {currentUser?.displayName || currentUser?.email || 'Not logged in'}
                </div>
              </div>
            </div>
            
            {/* Save and Cancel buttons */}
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowSettings(false)}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md"
              >
                Cancel
              </button>
              <button
                onClick={saveSettings}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md"
              >
                Save Settings
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Audio element for alert sound */}
      <audio 
        ref={audioRef}
        src={soundUrl || "/sounds/default-alert.mp3"} // Fallback to default sound
        preload="auto"
      />
    </>
  );
};

export default AlertComp;