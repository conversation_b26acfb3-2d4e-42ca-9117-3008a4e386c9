import React, { useState, useEffect, useRef } from 'react';
import { doc, getDoc, collection, getDocs, updateDoc, arrayUnion, serverTimestamp, setDoc, addDoc } from 'firebase/firestore';
import { db } from './firebase'; // Adjust this path based on your actual file location

const DetailsPanel = ({ 
  detailsPanelLocation, 
  detailsVisible, 
  setDetailsVisible, 
  userDisplayNames,
  userProfilePictures,
  handleStartDM, 
  confirmDeleteTrail, 
  isAdmin, 
  handleSelectLocation, 
  startNavigation, 
  markLocationAsPickedUp, 
  handleDeleteMarker, 
  isTowTruckUser, 
  currentUser,
  mapRef,
  openImageViewer,
  screenConfig,
  currentLocation = null,
  calculateDistance = null,
  teamId = null,
  // New props for team vehicle integration
  onSecureTeamVehicle = null,
  onMarkInRoute = null,
  onMarkArrived = null,
  onMarkBottomStatus = null,
  onRecheckVehicle = null,
  canSecureVehicles = false,
  soundEnabled = true
}) => {
  // Helper function to check if screen is small
  const isSmallScreen = screenConfig?.isSmallScreen || false;
  
  // Add state to track if user is within proximity
  const [isWithinProximity, setIsWithinProximity] = useState(false);
  
  // Add state to track the actual distance
  const [distanceToVehicle, setDistanceToVehicle] = useState(null);
  
  // State for the check-in confirmation popup
  const [showCheckInConfirmation, setShowCheckInConfirmation] = useState(false);
  // States for button animations
  const [activeButton, setActiveButton] = useState(null);
  // Track order details
  const [orderDetails, setOrderDetails] = useState(null);
  // Track vehicle presence status from check-ins
  const [vehiclePresent, setVehiclePresent] = useState(true);
  // State for loading indicator
  const [isLoading, setIsLoading] = useState(false);
  // State for the "Can't Claim" note
  const [cantClaimNote, setCantClaimNote] = useState('');
  // State for showing the can't claim note form
  const [showCantClaimForm, setShowCantClaimForm] = useState(false);
  // State for saved notes
  const [savedNotes, setSavedNotes] = useState([]);
  // State for check-in count
  const [checkInCount, setCheckInCount] = useState(0);
  // State for prompting user to claim vehicle
  const [showClaimPrompt, setShowClaimPrompt] = useState(false);
  // State for check-ins data
  const [checkIns, setCheckIns] = useState([]);
  // State for registration info (new)
  const [registrationInfo, setRegistrationInfo] = useState(null);
  
  // NEW: State for image cycling functionality
  const [selectedViewIndex, setSelectedViewIndex] = useState(0);
  const [isAutoCycling, setIsAutoCycling] = useState(false);
  const autoCycleIntervalRef = useRef(null);
  const [renderedViews, setRenderedViews] = useState([]);
  
  // NEW: State for address selection functionality
  const [showAddressSelection, setShowAddressSelection] = useState(false);
  
  // NEW: State for bottom status selection
  const [showBottomStatusMenu, setShowBottomStatusMenu] = useState(false);
  
  // NEW: State for live ETA updates
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [etaCountdown, setEtaCountdown] = useState(null);
  
  // Auto-close timer
  const autoCloseTimerRef = useRef(null);
  const AUTO_CLOSE_TIMEOUT = 30000; // 30 seconds
  
  // Countdown timer state
  const [timeRemaining, setTimeRemaining] = useState(30);
  const [showCountdown, setShowCountdown] = useState(false);
  const countdownIntervalRef = useRef(null);
  
  // IMPORTANT FIX: Add local state to track visibility
  const [isPanelVisible, setIsPanelVisible] = useState(detailsVisible);

  // Parse vehicle info from name if individual fields aren't available
  const parseVehicleInfo = () => {
    if (detailsPanelLocation?.make && detailsPanelLocation?.model && detailsPanelLocation?.year) {
      return {
        make: detailsPanelLocation.make,
        model: detailsPanelLocation.model,
        year: detailsPanelLocation.year
      };
    }
    
    // Try to parse from name field
    const name = detailsPanelLocation?.name || '';
    const parts = name.trim().split(' ');
    
    if (parts.length >= 3) {
      // Assume format: YEAR MAKE MODEL...
      const yearMatch = parts[0].match(/^\d{4}$/);
      if (yearMatch) {
        return {
          year: parts[0],
          make: parts[1],
          model: parts.slice(2).join(' ')
        };
      }
    }
    
    // Try to parse from vehicle field (for team vehicles)
    if (detailsPanelLocation?.vehicle) {
      const vehicleParts = detailsPanelLocation.vehicle.trim().split(' ');
      if (vehicleParts.length >= 3) {
        const yearMatch = vehicleParts[0].match(/^\d{4}$/);
        if (yearMatch) {
          return {
            year: vehicleParts[0],
            make: vehicleParts[1],
            model: vehicleParts.slice(2).join(' ')
          };
        }
      }
    }
    
    // Fallback
    return {
      make: detailsPanelLocation?.make || 'N/A',
      model: detailsPanelLocation?.model || 'N/A',
      year: detailsPanelLocation?.year || 'N/A'
    };
  };

  // Auto-determine drive type based on make/model - ENHANCED VERSION
  const getDriveTypeFromVehicle = (make, model) => {
    const upperMake = (make || '').toUpperCase();
    const upperModel = (model || '').toUpperCase();
    
    // AWD/4WD vehicles
    const awdKeywords = ['AWD', '4WD', 'QUATTRO', 'XDRIVE', '4MATIC', 'SH-AWD', 'SYMMETRICAL', 'ALL-WHEEL', '4MOTION'];
    const awdMakes = ['SUBARU', 'AUDI'];
    const trucks = ['F-150', 'F-250', 'F-350', 'SILVERADO', 'SIERRA', 'RAM', 'TUNDRA', 'TACOMA', 'RANGER', 'COLORADO', 'CANYON', 'TITAN', 'FRONTIER', 'RIDGELINE'];
    const suvs = ['TAHOE', 'SUBURBAN', 'YUKON', 'EXPEDITION', 'EXPLORER', 'HIGHLANDER', '4RUNNER', 'WRANGLER', 'CHEROKEE', 'PILOT', 'CRV', 'CR-V', 'RAV4', 'PATHFINDER', 'ARMADA', 'QX80', 'SEQUOIA', 'LAND CRUISER'];
    
    // Check for AWD/4WD
    if (awdMakes.includes(upperMake)) return 'AWD';
    if (awdKeywords.some(keyword => upperModel.includes(keyword))) return 'AWD';
    if (trucks.some(truck => upperModel.includes(truck))) return '4WD';
    if (suvs.some(suv => upperModel.includes(suv))) {
      // Many modern SUVs are AWD
      if (upperModel.includes('4WD') || upperMake === 'JEEP') return '4WD';
      return 'AWD';
    }
    
    // RWD vehicles
    const rwdMakes = ['BMW', 'MERCEDES-BENZ', 'MERCEDES', 'PORSCHE', 'JAGUAR', 'MASERATI', 'FERRARI', 'LAMBORGHINI', 'BENTLEY', 'ROLLS-ROYCE', 'ASTON MARTIN', 'MCLAREN', 'GENESIS', 'LEXUS'];
    const rwdModels = ['MUSTANG', 'CAMARO', 'CHALLENGER', 'CHARGER', 'CORVETTE', 'MIATA', 'MX-5', 'BRZ', 'GR86', '86', 'SUPRA', 'Z06', 'GT', 'VIPER', 'NSX'];
    const rwdSedans = ['IS', 'GS', 'LS', 'RC', 'LC', 'M3', 'M4', 'M5', 'M6', 'M8', 'C-CLASS', 'E-CLASS', 'S-CLASS', 'CLS', 'G70', 'G80', 'G90'];
    
    if (rwdMakes.includes(upperMake)) {
      // Check for AWD variants
      if (awdKeywords.some(keyword => upperModel.includes(keyword))) return 'AWD';
      return 'RWD';
    }
    if (rwdModels.some(model => upperModel.includes(model))) return 'RWD';
    if (rwdSedans.some(model => upperModel.includes(model))) {
      // Check for AWD variants
      if (awdKeywords.some(keyword => upperModel.includes(keyword))) return 'AWD';
      return 'RWD';
    }
    
    // Default to FWD for most cars
    return 'FWD';
  };

  // Get vehicle type icon based on make/model
  const getVehicleTypeIcon = (make, model) => {
    const upperMake = (make || '').toUpperCase();
    const upperModel = (model || '').toUpperCase();
    
    // Trucks
    const trucks = ['F-150', 'F-250', 'F-350', 'SILVERADO', 'SIERRA', 'RAM', 'TUNDRA', 'TACOMA', 'RANGER', 'COLORADO', 'CANYON', 'TITAN', 'FRONTIER', 'RIDGELINE'];
    if (trucks.some(truck => upperModel.includes(truck))) return '🚛';
    
    // SUVs
    const suvs = ['TAHOE', 'SUBURBAN', 'YUKON', 'EXPEDITION', 'EXPLORER', 'HIGHLANDER', '4RUNNER', 'WRANGLER', 'CHEROKEE', 'PILOT', 'CRV', 'CR-V', 'RAV4', 'EDGE', 'ESCAPE', 'ROGUE', 'MURANO', 'PATHFINDER', 'ARMADA', 'QX80', 'MDX', 'RDX'];
    if (suvs.some(suv => upperModel.includes(suv))) return '🚙';
    
    // Sports cars
    const sportsCars = ['MUSTANG', 'CAMARO', 'CHALLENGER', 'CORVETTE', 'MIATA', 'MX-5', 'GTR', 'GT-R', 'SUPRA', 'Z06', 'HELLCAT', 'BRZ', 'GR86', '86', 'NSX', 'VIPER'];
    if (sportsCars.some(car => upperModel.includes(car))) return '🏎️';
    
    // Luxury
    const luxuryMakes = ['BMW', 'MERCEDES-BENZ', 'MERCEDES', 'AUDI', 'LEXUS', 'CADILLAC', 'LINCOLN', 'PORSCHE', 'JAGUAR', 'MASERATI', 'BENTLEY', 'ROLLS-ROYCE', 'GENESIS', 'ACURA', 'INFINITI'];
    if (luxuryMakes.includes(upperMake)) return '🚘';
    
    // Electric
    if (upperMake === 'TESLA' || upperModel.includes('BOLT') || upperModel.includes('LEAF') || upperModel.includes('IONIQ') || upperModel.includes('EV6') || upperModel.includes('ID.4')) return '🔋';
    
    // Vans
    if (upperModel.includes('ODYSSEY') || upperModel.includes('SIENNA') || upperModel.includes('PACIFICA') || upperModel.includes('CARNIVAL')) return '🚐';
    
    // Default sedan
    return '🚗';
  };

  // Calculate ETA
  const calculateETA = (inRouteTimestamp) => {
    if (!inRouteTimestamp) return null;
    const inRouteTime = inRouteTimestamp.toDate ? inRouteTimestamp.toDate() : new Date(inRouteTimestamp);
    const eta = new Date(inRouteTime.getTime() + 15 * 60 * 1000); // 15 minutes
    return eta;
  };

  // Get time remaining to ETA
  const getTimeToETA = (eta) => {
    if (!eta) return null;
    const now = new Date();
    const remaining = eta - now;
    if (remaining <= 0) return 'Arriving Now';
    const minutes = Math.ceil(remaining / 60000);
    return `${minutes} min`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    const timeStr = date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });

    if (date.toDateString() === now.toDateString()) {
      return `Today ${timeStr}`;
    } else if (diffDays === 1) {
      return `Yesterday ${timeStr}`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago ${timeStr}`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    }
  };

  // Update local state when prop changes
  useEffect(() => {
    console.log("detailsVisible prop changed:", detailsVisible);
    
    if (detailsVisible === true) {
      setIsPanelVisible(true);
    } else {
      setIsPanelVisible(false);
    }
  }, [detailsVisible]);

  // NEW: Add effect to handle location changes
  useEffect(() => {
    if (detailsPanelLocation) {
      console.log("New location received in details panel:", detailsPanelLocation.id);
      setIsPanelVisible(true);
      resetAutoCloseTimer();
      
      if (!detailsPanelLocation.registrationInfo) {
        setRegistrationInfo(null);
      } else {
        setRegistrationInfo(detailsPanelLocation.registrationInfo);
      }
      
      processVehicleImages();
      
      // Reset states
      setShowBottomStatusMenu(false);
      setShowClaimPrompt(false);
      setShowCantClaimForm(false);
    }
  }, [detailsPanelLocation]);
  
  // Live timer for ETA updates
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(Date.now());
      
      // Update ETA countdown if driver is in route
      if (detailsPanelLocation?.inRouteTimestamp) {
        const eta = calculateETA(detailsPanelLocation.inRouteTimestamp);
        const timeToETA = getTimeToETA(eta);
        setEtaCountdown(timeToETA);
      } else {
        setEtaCountdown(null);
      }
    }, 1000); // Update every second for smooth countdown

    return () => clearInterval(timer);
  }, [detailsPanelLocation]);
  
  // NEW: Process vehicle images for cycling
  const processVehicleImages = () => {
    if (!detailsPanelLocation) return;
    
    stopAutoCycle();
    setSelectedViewIndex(0);
    
    if (detailsPanelLocation.vehicleRenderViews && Array.isArray(detailsPanelLocation.vehicleRenderViews) && detailsPanelLocation.vehicleRenderViews.length > 0) {
      setRenderedViews(detailsPanelLocation.vehicleRenderViews);
      return;
    }
    
    if (detailsPanelLocation.images && Array.isArray(detailsPanelLocation.images) && detailsPanelLocation.images.length > 0) {
      const formattedViews = detailsPanelLocation.images.map((img, index) => {
        const imageUrl = typeof img === 'string' ? img : img.url;
        
        return {
          url: imageUrl,
          angle: `${index + 1}`,
          colorName: detailsPanelLocation.color || 'Unknown',
          colorCode: detailsPanelLocation.color ? null : 'white'
        };
      });
      setRenderedViews(formattedViews);
      return;
    }
    
    if (detailsPanelLocation.vehicleImage) {
      setRenderedViews([{
        url: detailsPanelLocation.vehicleImage,
        angle: '1',
        colorName: detailsPanelLocation.color || 'Unknown',
        colorCode: detailsPanelLocation.color ? null : 'white'
      }]);
      return;
    }
    
    setRenderedViews([]);
  };
  
  // Auto-cycle functions
  const startAutoCycle = () => {
    stopAutoCycle();
    
    if (renderedViews.length <= 1) return;
    
    setIsAutoCycling(true);
    autoCycleIntervalRef.current = setInterval(() => {
      setSelectedViewIndex(prevIndex => (prevIndex + 1) % renderedViews.length);
    }, 2000);
  };
  
  const stopAutoCycle = () => {
    if (autoCycleIntervalRef.current) {
      clearInterval(autoCycleIntervalRef.current);
      autoCycleIntervalRef.current = null;
    }
    setIsAutoCycling(false);
  };
  
  const toggleAutoCycle = () => {
    resetAutoCloseTimer();
    if (isAutoCycling) {
      stopAutoCycle();
    } else {
      startAutoCycle();
    }
  };
  
  const handleSwitchView = (index) => {
    resetAutoCloseTimer();
    stopAutoCycle();
    setSelectedViewIndex(index);
  };
  
  // Reset auto-close timer
  const resetAutoCloseTimer = () => {
    if (!isPanelVisible) return;
    
    if (autoCloseTimerRef.current) {
      clearTimeout(autoCloseTimerRef.current);
      autoCloseTimerRef.current = null;
    }
    
    if (countdownIntervalRef.current && !isWithinProximity) {
      clearInterval(countdownIntervalRef.current);
      setTimeRemaining(30);
      
      countdownIntervalRef.current = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            clearInterval(countdownIntervalRef.current);
            countdownIntervalRef.current = null;
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    
    if (isPanelVisible) {
      autoCloseTimerRef.current = setTimeout(() => {
        if (!isWithinProximity && detailsPanelLocation && 
            detailsPanelLocation.type !== 'user' && 
            !detailsPanelLocation.userSelected) {
          console.log("Auto-closing details panel - not in proximity");
        } else {
          console.log("Auto-closing details panel after 30 seconds of inactivity");
        }
        handleClosePanel();
      }, AUTO_CLOSE_TIMEOUT);
    }
  };

  const handleClosePanel = () => {
    console.log("Closing details panel");
    setIsPanelVisible(false);
    
    if (typeof setDetailsVisible === 'function') {
      setDetailsVisible(false);
    }
    
    setShowCheckInConfirmation(false);
    setShowClaimPrompt(false);
    setShowCantClaimForm(false);
    setShowAddressSelection(false);
    setShowBottomStatusMenu(false);
    
    if (autoCloseTimerRef.current) {
      clearTimeout(autoCloseTimerRef.current);
      autoCloseTimerRef.current = null;
    }
    
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }
    
    stopAutoCycle();
    
    setShowCountdown(false);
  };

  const getDistanceToLocation = () => {
    if (!detailsPanelLocation || !detailsPanelLocation.position) {
      return null;
    }

    if (typeof calculateDistance === 'function' && currentLocation) {
      try {
        const distance = calculateDistance(currentLocation, detailsPanelLocation.position);
        return distance;
      } catch (error) {
        console.error("Error using parent's calculateDistance:", error);
      }
    }

    if (currentLocation) {
      try {
        const R = 3958.8;
        
        const lat1 = currentLocation.lat * Math.PI/180;
        const lon1 = currentLocation.lng * Math.PI/180;
        const lat2 = detailsPanelLocation.position.lat * Math.PI/180;
        const lon2 = detailsPanelLocation.position.lng * Math.PI/180;
        
        const dlon = lon2 - lon1;
        const dlat = lat2 - lat1;
        const a = Math.sin(dlat/2)**2 + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dlon/2)**2;
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        const distance = R * c;
        
        return distance;
      } catch (error) {
        console.error("Error calculating distance locally:", error);
        return null;
      }
    }

    return null;
  };

  const checkProximity = () => {
    if (detailsPanelLocation && detailsPanelLocation.userSelected) {
      setIsWithinProximity(true);
      return true;
    }
    
    if (!detailsPanelLocation || !currentLocation || detailsPanelLocation.type === 'user') {
      setDistanceToVehicle(null);
      setIsWithinProximity(false);
      return false;
    }
    
    try {
      const distance = getDistanceToLocation();
      
      if (distance === null) {
        console.log("Cannot check proximity: distance calculation failed");
        setDistanceToVehicle(null);
        setIsWithinProximity(false);
        return false;
      }
      
      setDistanceToVehicle(distance);
      
      const isClose = distance <= 0.225;
      
      console.log(`Distance to selected location: ${distance.toFixed(2)} miles. Within proximity: ${isClose}`);
      
      setIsWithinProximity(isClose);
      
      if (!isClose && isWithinProximity && !detailsPanelLocation.userSelected) {
        setShowCountdown(true);
        setTimeRemaining(30);
      }
      
      if (isClose && !isWithinProximity) {
        setShowCountdown(false);
      }
      
      return isClose;
    } catch (error) {
      console.error("Error checking proximity:", error);
      setDistanceToVehicle(null);
      setIsWithinProximity(false);
      return false;
    }
  };
  
  useEffect(() => {
    console.log("Checking proximity - current location:", currentLocation);
    console.log("Checking proximity - details location:", detailsPanelLocation?.position);
    checkProximity();
  }, [currentLocation, detailsPanelLocation]);

  // Handle team vehicle actions - ALIGNED WITH TeamVehicleTracker
  const handleMarkInRoute = async () => {
    resetAutoCloseTimer();
    
    const vehicleName = detailsPanelLocation.name || detailsPanelLocation.vehicle || 'this vehicle';
    if (!confirm(`Are you sure you want to mark yourself as IN ROUTE to ${vehicleName}?`)) {
      return;
    }
    
    setActiveButton('inroute');
    setTimeout(() => setActiveButton(null), 500);
    
    if (onMarkInRoute && detailsPanelLocation) {
      await onMarkInRoute(detailsPanelLocation);
    }
  };

  const handleMarkArrived = async () => {
    resetAutoCloseTimer();
    setActiveButton('arrived');
    setTimeout(() => setActiveButton(null), 500);
    
    if (onMarkArrived && detailsPanelLocation) {
      await onMarkArrived(detailsPanelLocation);
    }
  };

  const handleSecureTeamVehicle = async () => {
    resetAutoCloseTimer();
    
    const vehicleName = detailsPanelLocation.name || detailsPanelLocation.vehicle || 'this vehicle';
    const vin = detailsPanelLocation.vin || 'Unknown VIN';
    
    if (!confirm(`Are you sure you want to secure ${vehicleName} (VIN: ${vin})?`)) {
      return;
    }
    
    setActiveButton('secure');
    setTimeout(() => setActiveButton(null), 500);
    
    if (onSecureTeamVehicle && detailsPanelLocation) {
      await onSecureTeamVehicle(detailsPanelLocation);
    }
  };

  // UPDATED: Handle bottom status with special CANT SECURE logic
  const handleMarkBottomStatus = async (status) => {
    resetAutoCloseTimer();
    setShowBottomStatusMenu(false);
    
    // Special handling for CANT SECURE
    if (status === 'CANT SECURE') {
      const vehicleName = detailsPanelLocation.name || detailsPanelLocation.vehicle || 'this vehicle';
      const vin = detailsPanelLocation.vin || 'Unknown VIN';
      
      const confirmMessage = `Are you sure this vehicle CANNOT BE SECURED?\n\n` +
        `Vehicle: ${vehicleName}\n` +
        `VIN: ${vin}\n\n` +
        `This will immediately move the vehicle to the "Never Secured" list and remove it from active tracking.`;
      
      if (!confirm(confirmMessage)) {
        return;
      }
    }
    
    if (onMarkBottomStatus && detailsPanelLocation) {
      await onMarkBottomStatus(detailsPanelLocation, status);
    }
  };

  const handleRecheckVehicle = async () => {
    resetAutoCloseTimer();
    
    const vehicleName = detailsPanelLocation.name || detailsPanelLocation.vehicle || 'this vehicle';
    
    if (!confirm(`Are you sure you want to recheck ${vehicleName}? This will reset its status.`)) {
      return;
    }
    
    setActiveButton('recheck');
    setTimeout(() => setActiveButton(null), 500);
    
    if (onRecheckVehicle && detailsPanelLocation) {
      await onRecheckVehicle(detailsPanelLocation);
    }
  };

  const fetchVehicleDetails = async (vehicleId) => {
    if (detailsPanelLocation && detailsPanelLocation.userSelected === true && detailsPanelLocation.fromDatabase === false) {
      console.log('Using provided vehicle details for user-selected vehicle from search');
      const vehicleData = detailsPanelLocation;
      
      setCheckInCount(0);
      setSavedNotes([]);
      setCheckIns([]);
      
      if (vehicleData.registrationInfo) {
        setRegistrationInfo(vehicleData.registrationInfo);
      }
      
      setIsLoading(false);
      return;
    }
    
    setIsLoading(true);
    try {
      console.log('Fetching vehicle details for:', vehicleId);
      
      const orderDoc = await getDoc(doc(db, 'orders', vehicleId));
      
      if (orderDoc.exists()) {
        const orderData = orderDoc.data();
        console.log('Order data found directly:', orderData);
        
        const checkInCount = orderData.checkInCount || 0;
        setCheckInCount(checkInCount);
        
        const vehicleNotes = Array.isArray(orderData.notes) ? orderData.notes : [];
        setSavedNotes(vehicleNotes);
        
        const checkInsData = Array.isArray(orderData.checkIns) ? orderData.checkIns : [];
        setCheckIns(checkInsData);
        
        const parsedInfo = parseVehicleInfo();
        
        setOrderDetails({
          driveType: orderData.driveType || getDriveTypeFromVehicle(parsedInfo.make, parsedInfo.model),
          orderId: vehicleId,
          checkIns: checkInsData
        });
        
        if (orderData.vehicleRenderViews && Array.isArray(orderData.vehicleRenderViews)) {
          setRenderedViews(orderData.vehicleRenderViews);
        } else if (orderData.vehicleImage) {
          setRenderedViews([{
            url: orderData.vehicleImage,
            angle: '1',
            colorName: orderData.color || 'Unknown',
            colorCode: orderData.color ? null : 'white'
          }]);
        }
        
        setIsLoading(false);
        return {
          driveType: orderData.driveType || getDriveTypeFromVehicle(detailsPanelLocation?.make, detailsPanelLocation?.model),
          orderId: vehicleId,
          checkIns: checkInsData
        };
      }
      
      const vehicleDoc = await getDoc(doc(db, 'vehicles', vehicleId));
      
      if (!vehicleDoc.exists()) {
        console.error('Document not found in orders or vehicles collection');
        setIsLoading(false);
        return null;
      }
      
      const vehicleData = vehicleDoc.data();
      console.log('Vehicle data:', vehicleData);
      
      const parsedInfo = parseVehicleInfo();
      let driveType = getDriveTypeFromVehicle(parsedInfo.make, parsedInfo.model);
      let vehicleNotes = [];
      let checkInCount = 0;
      let checkInsData = [];
      
      if (vehicleData.orderId) {
        console.log('Found orderId:', vehicleData.orderId);
        
        const orderDoc = await getDoc(doc(db, 'orders', vehicleData.orderId));
        
        if (orderDoc.exists()) {
          const orderData = orderDoc.data();
          console.log('Order data:', orderData);
          
          driveType = orderData.driveType || driveType;
          vehicleNotes = Array.isArray(orderData.notes) ? orderData.notes : [];
          checkInCount = orderData.checkInCount || 0;
          checkInsData = Array.isArray(orderData.checkIns) ? orderData.checkIns : [];
          
          if (orderData.vehicleRenderViews && Array.isArray(orderData.vehicleRenderViews)) {
            setRenderedViews(orderData.vehicleRenderViews);
          } else if (orderData.vehicleImage) {
            setRenderedViews([{
              url: orderData.vehicleImage,
              angle: '1',
              colorName: orderData.color || 'Unknown',
              colorCode: orderData.color ? null : 'white'
            }]);
          }
        }
      }
      
      setCheckInCount(checkInCount);
      setSavedNotes(vehicleNotes);
      setCheckIns(checkInsData);
      
      setOrderDetails({
        driveType,
        orderId: vehicleData.orderId,
        checkIns: checkInsData
      });
      
      setIsLoading(false);
      return {
        driveType,
        orderId: vehicleData.orderId,
        checkIns: checkInsData
      };
    } catch (error) {
      console.error('Error fetching vehicle details:', error);
      setIsLoading(false);
      setSavedNotes([]);
      setCheckIns([]);
      return null;
    }
  };

  useEffect(() => {
    if (detailsPanelLocation && detailsPanelLocation.id && detailsPanelLocation.type !== 'user') {
      fetchVehicleDetails(detailsPanelLocation.id);
      processVehicleImages();
    } else {
      setOrderDetails(null);
      setSavedNotes([]);
      setCheckInCount(0);
      setCheckIns([]);
      setShowClaimPrompt(false);
      setShowCantClaimForm(false);
      setRenderedViews([]);
      setSelectedViewIndex(0);
      stopAutoCycle();
    }
  }, [detailsPanelLocation]);

  const formatDate = (dateValue) => {
    if (!dateValue) return 'N/A';
    
    try {
      const date = dateValue.toDate ? dateValue.toDate() : new Date(dateValue);
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).format(date);
    } catch (error) {
      console.error("Error formatting date:", error);
      return 'Invalid date';
    }
  };

  const getVehicleImageUrl = () => {
    if (renderedViews.length > 0 && renderedViews[selectedViewIndex]) {
      return renderedViews[selectedViewIndex].url;
    }
    
    if (detailsPanelLocation?.images && detailsPanelLocation.images.length > 0) {
      const firstImage = detailsPanelLocation.images[0];
      return typeof firstImage === 'string' ? firstImage : firstImage.url;
    }
    
    if (detailsPanelLocation?.vehicleImage) {
      return detailsPanelLocation.vehicleImage;
    }
    
    // Use parsed vehicle info for generating image URL
    const parsedInfo = parseVehicleInfo();
    const make = encodeURIComponent(parsedInfo.make !== 'N/A' ? parsedInfo.make : '');
    const model = encodeURIComponent(parsedInfo.model !== 'N/A' ? parsedInfo.model : '');
    const year = parsedInfo.year !== 'N/A' ? parsedInfo.year : '';
    
    if (make && model) {
      return `https://cdn.imagin.studio/getimage?customer=img&make=${make}&modelFamily=${model}&year=${year}&angle=1`;
    }
    
    return null;
  };

  const formatAddress = (address) => {
    if (!address) return 'N/A';
    
    if (typeof address === 'string') {
      return address;
    }
    
    let parts = [];
    if (address.street) parts.push(address.street);
    
    let cityStateZip = '';
    if (address.city) cityStateZip += address.city;
    if (address.state) {
      if (cityStateZip) cityStateZip += ', ';
      cityStateZip += address.state;
    }
    if (address.zip) {
      if (cityStateZip) cityStateZip += ' ';
      cityStateZip += address.zip;
    }
    
    if (cityStateZip) parts.push(cityStateZip);
    
    return parts.join(', ');
  };

  const handleOpenAddressSelection = (e) => {
    e.stopPropagation();
    resetAutoCloseTimer();
    setShowAddressSelection(true);
  };

  const handleAddressSelect = (address) => {
    resetAutoCloseTimer();
    
    const locationWithSelectedAddress = {
      ...detailsPanelLocation,
      position: address.position
    };
    
    setShowAddressSelection(false);
    
    handleSelectLocation(locationWithSelectedAddress);
    startNavigation();
  };

  const hasValidCoordinates = (address) => {
    return address && 
           address.position && 
           typeof address.position.lat === 'number' && 
           typeof address.position.lng === 'number';
  };

  const getStatusInfo = () => {
    const status = detailsPanelLocation?.status || 'unknown';
    
    if (status === 'picked-up') {
      return { label: 'Picked Up', styleClass: 'picked-up' };
    } else if (status === 'secure' || status === 'SECURED') {
      return { label: 'Secured', styleClass: 'secure' };
    } else if (status === 'completed') {
      return { label: 'Completed', styleClass: 'completed' };
    } else if (status === 'pending-pickup' || status === 'PENDING PICKUP') {
      return { label: 'Pending Pickup', styleClass: 'pending-pickup' };
    } else if (status === 'awaiting-pickup') {
      return { label: 'Awaiting Pickup', styleClass: 'awaiting-pickup' };
    } else if (status === 'claim') {
      return { label: 'Claim', styleClass: 'claim' };
    } else if (status === 'on-hold') {
      return { label: 'On Hold', styleClass: 'on-hold' };
    } else if (status === 'restricted') {
      return { label: 'Restricted', styleClass: 'restricted' };
    } else if (status === 'open' || status === 'open-order' || status === 'pending') {
      return { label: 'Open Order', styleClass: 'open-order' };
    } else if (status === 'FOUND') {
      return { label: 'Found', styleClass: 'found' };
    } else {
      return { label: status || 'Unknown', styleClass: 'default' };
    }
  };

  const isSearchResult = () => {
    return detailsPanelLocation && 
           detailsPanelLocation.userSelected === true && 
           detailsPanelLocation.fromDatabase === false;
  };

  const isTeamVehicle = () => {
    return detailsPanelLocation && detailsPanelLocation.isTeamVehicle === true;
  };

  const isOwnVehicle = () => {
    return detailsPanelLocation && detailsPanelLocation.isOwnVehicle === true;
  };

  const renderProximityWarning = () => {
    if (isSearchResult()) {
      return null;
    }
    
    if (detailsPanelLocation && detailsPanelLocation.type !== 'user' && !isWithinProximity) {
      return (
        <div className="proximity-warning">
          <p className="proximity-distance">
            Current distance: {distanceToVehicle ? `${distanceToVehicle.toFixed(2)} miles` : 'Unknown'}
          </p>
          
          <button 
            className="details-panel-action-btn action-navigate"
            onClick={(e) => {
              e.stopPropagation();
              resetAutoCloseTimer();
              handleSelectLocation(detailsPanelLocation);
              startNavigation();
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 16 16" fill="currentColor">
              <path d="M9.502 11a3 3 0 0 1-2.397-4.798l5.44-6.55a1 1 0 1 1 1.537 1.274l-5.01 6.53A3 3 0 0 1 9.502 11m1.732-4h.768a2.5 2.5 0 0 1 0 5h-.768zM6.5 1A.5.5 0 0 1 7 .5h2a.5.5 0 0 1 0 1v.57l1.5-1.5A.5.5 0 0 1 11 0h2.5a.5.5 0 0 1 0 1h-2.158l-2 2H11.5a.5.5 0 0 1 0 1h-2v2.5L8 9V13h2.5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1H8V9l-1.5-1.5V3H6.5a.5.5 0 0 1 0-1"/>
            </svg>
            Navigate to Location
          </button>
          
          {showCountdown && (
            <div className="auto-close-countdown">
              <p>Panel will close in <span className="countdown-time">{timeRemaining}</span> seconds</p>
              <div className="countdown-bar-container">
                <div 
                  className="countdown-bar" 
                  style={{ width: `${(timeRemaining / 30) * 100}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  const AddressSelectionUI = () => {
    const addresses = detailsPanelLocation?.addresses || [];
    
    const hasAddressesWithCoordinates = addresses.some(address => hasValidCoordinates(address));
    
    const hasVehiclePosition = detailsPanelLocation && 
                               detailsPanelLocation.position && 
                               typeof detailsPanelLocation.position.lat === 'number' && 
                               typeof detailsPanelLocation.position.lng === 'number';
    
    if (!hasAddressesWithCoordinates && !hasVehiclePosition) {
      return (
        <div className="address-selection-container">
          <div className="address-selection-header">
            <h4>No Navigation Data</h4>
            <button 
              className="address-selection-close-btn"
              onClick={(e) => {
                e.stopPropagation();
                resetAutoCloseTimer();
                setShowAddressSelection(false);
              }}
            >
              ×
            </button>
          </div>
          <div className="address-selection-no-data">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-500 mb-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
            </svg>
            <p>No GPS coordinates available for navigation.</p>
          </div>
          <div className="address-selection-footer">
            <button 
              className="address-selection-cancel-btn"
              onClick={(e) => {
                e.stopPropagation();
                resetAutoCloseTimer();
                setShowAddressSelection(false);
              }}
            >
              Close
            </button>
          </div>
        </div>
      );
    }
    
    return (
      <div className="address-selection-container">
        <div className="address-selection-header">
          <h4>Select Navigation Destination</h4>
          <button 
            className="address-selection-close-btn"
            onClick={(e) => {
              e.stopPropagation();
              resetAutoCloseTimer();
              setShowAddressSelection(false);
            }}
          >
            ×
          </button>
        </div>
        
        {hasVehiclePosition && (
          <div 
            className="address-selection-item address-selection-vehicle"
            onClick={(e) => {
              e.stopPropagation();
              resetAutoCloseTimer();
              handleAddressSelect(detailsPanelLocation);
            }}
          >
            <div className="address-selection-text">
              <strong>Current Vehicle Location</strong>
              <div className="text-xs text-blue-400 mt-1">
                GPS: {detailsPanelLocation.position.lat.toFixed(6)}, {detailsPanelLocation.position.lng.toFixed(6)}
              </div>
            </div>
            <div className="address-selection-icon">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-1h3a1 1 0 001-1v-3a1 1 0 00-.7-.997l-2.099-4.199A1 1 0 0011.2 4H3zm1 2h6.308l1.7 3.4H4V6z" />
              </svg>
            </div>
          </div>
        )}
        
        {addresses.length > 0 && (
          <>
            <div className="address-selection-subtitle">Available Address Locations:</div>
            <div className="address-selection-list">
              {addresses.map((address, index) => (
                <div 
                  key={index} 
                  className={`address-selection-item ${!hasValidCoordinates(address) ? 'address-no-gps' : ''}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    resetAutoCloseTimer();
                    if (hasValidCoordinates(address)) {
                      handleAddressSelect(address);
                    }
                  }}
                >
                  <div className="address-selection-text">
                    {formatAddress(address)}
                    {hasValidCoordinates(address) && (
                      <div className="text-xs text-blue-400 mt-1">
                        GPS: {address.position.lat.toFixed(6)}, {address.position.lng.toFixed(6)}
                      </div>
                    )}
                  </div>
                  {hasValidCoordinates(address) ? (
                    <div className="address-has-gps">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                    </div>
                  ) : (
                    <div className="address-no-gps-label">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </>
        )}
        
        <div className="address-selection-footer">
          <button 
            className="address-selection-cancel-btn"
            onClick={(e) => {
              e.stopPropagation();
              resetAutoCloseTimer();
              setShowAddressSelection(false);
            }}>
            Cancel
          </button>
        </div>
      </div>
    );
  };

  const renderRegistrationInfo = () => {
    if (!registrationInfo) return null;
    
    return (
      <div className="details-panel-section mt-4">
        <div className="details-panel-section-title">Registration Information</div>
        
        <div className="grid-container">
          {registrationInfo.registrationState && (
            <div className="grid-item">
              <div className="item-label">Registration State</div>
              <div className="item-value">{registrationInfo.registrationState}</div>
            </div>
          )}
          
          {registrationInfo.licensePlate && (
            <div className="grid-item">
              <div className="item-label">License Plate</div>
              <div className="item-value highlight-value">{registrationInfo.licensePlate}</div>
            </div>
          )}
          
          {registrationInfo.registrationExpiry && (
            <div className="grid-item">
              <div className="item-label">Expires</div>
              <div className="item-value">{registrationInfo.registrationExpiry}</div>
            </div>
          )}
          
          {registrationInfo.titleStatus && (
            <div className="grid-item">
              <div className="item-label">Title Status</div>
              <div className={`item-value ${registrationInfo.titleStatus === 'CLEAN TITLE' ? 'clean-title' : 'lien-title'}`}>
                {registrationInfo.titleStatus}
              </div>
            </div>
          )}
          
          {registrationInfo.lastOdometerReading && (
            <div className="grid-item">
              <div className="item-label">Odometer</div>
              <div className="item-value">
                {registrationInfo.lastOdometerReading}
                {registrationInfo.lastOdometerDate && (
                  <span className="text-xs text-gray-400 ml-1">({registrationInfo.lastOdometerDate})</span>
                )}
              </div>
            </div>
          )}
          
          {registrationInfo.hasLiens !== undefined && (
            <div className="grid-item">
              <div className="item-label">Liens</div>
              <div className={`item-value ${registrationInfo.hasLiens ? 'has-lien' : 'no-lien'}`}>
                {registrationInfo.hasLiens ? 'YES' : 'NONE'}
              </div>
            </div>
          )}
        </div>
        
        {registrationInfo.registeredOwner && (
          <div className="registration-section mt-2">
            <div className="section-label">Registered Owner</div>
            <div className="section-content font-mono">
              {registrationInfo.registeredOwner}
              {registrationInfo.registeredAddress && (
                <div className="text-gray-300">{registrationInfo.registeredAddress}</div>
              )}
            </div>
          </div>
        )}
        
        {registrationInfo.previousRegistrations && registrationInfo.previousRegistrations.length > 0 && (
          <div className="registration-section mt-3">
            <div className="section-label">Previous Registrations</div>
            <div className="section-content">
              {registrationInfo.previousRegistrations.map((reg, index) => (
                <div key={index} className="prev-registration">
                  <span className="state-badge">{reg.state}</span>
                  <span className="plate-number">{reg.plate}</span>
                  <span className="date-text">{reg.date}</span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {registrationInfo.source && (
          <div className="text-xs text-gray-400 text-center mt-2">
            Source: {registrationInfo.source}
          </div>
        )}
      </div>
    );
  };

  // Bottom Status Menu Component - UPDATED TO MATCH TeamVehicleTracker
  const BottomStatusMenu = () => {
    if (!showBottomStatusMenu) return null;
    
    return (
      <div className="bottom-status-overlay" onClick={(e) => {
        e.stopPropagation();
        resetAutoCloseTimer();
      }}>
        <div className="bottom-status-dialog">
          <h4 className="bottom-status-title">Select Vehicle Status</h4>
          <p className="bottom-status-subtitle">Why can't this vehicle be secured?</p>
          
          <div className="bottom-status-options">
            <button 
              className="bottom-status-option gone"
              onClick={(e) => {
                e.stopPropagation();
                handleMarkBottomStatus('GONE');
              }}
            >
              <span className="status-icon">🚫</span>
              <span className="status-text">Gone on Arrival</span>
              <span className="status-desc">Vehicle not at location</span>
            </button>
            
            <button 
              className="bottom-status-option blocked"
              onClick={(e) => {
                e.stopPropagation();
                handleMarkBottomStatus('BLOCKED IN');
              }}
            >
              <span className="status-icon">🚧</span>
              <span className="status-text">Blocked In</span>
              <span className="status-desc">Can't access vehicle</span>
            </button>
            
            <button 
              className="bottom-status-option cant-secure"
              onClick={(e) => {
                e.stopPropagation();
                handleMarkBottomStatus('CANT SECURE');
              }}
            >
              <span className="status-icon">⛔</span>
              <span className="status-text">Can't Secure</span>
              <span className="status-desc">Unable to secure (moves to Never Secured)</span>
            </button>
            
            <button 
              className="bottom-status-option interference"
              onClick={(e) => {
                e.stopPropagation();
                handleMarkBottomStatus('DEBTOR INTERFERENCE');
              }}
            >
              <span className="status-icon">👤</span>
              <span className="status-text">Debtor Interference</span>
              <span className="status-desc">Owner preventing recovery</span>
            </button>
          </div>
          
          <button 
            className="bottom-status-cancel"
            onClick={(e) => {
              e.stopPropagation();
              setShowBottomStatusMenu(false);
            }}
          >
            Cancel
          </button>
        </div>
      </div>
    );
  };

  const renderDetailsPanelContent = () => {
    if (!detailsPanelLocation) return null;
    
    if (detailsPanelLocation.type === 'user') {
      return (
        <>
          <div className="details-panel-title">
            User Profile
            {detailsPanelLocation.online ? 
              <span className="details-panel-status picked-up">Online</span> : 
              <span className="details-panel-status pending">Offline</span>
            }
          </div>
          
          {detailsPanelLocation.photoBase64 && (
            <div className="details-panel-image-grid" style={{ gridTemplateColumns: '1fr' }}>
              <img 
                src={detailsPanelLocation.photoBase64} 
                alt="User" 
                className="details-panel-image"
                style={{ 
                  height: isSmallScreen ? '100px' : '150px', 
                  width: isSmallScreen ? '100px' : '150px', 
                  borderRadius: '50%', 
                  margin: '0 auto' 
                }}
              />
            </div>
          )}
          
          <div className="details-panel-info">
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Name:</div>
              <div className="details-panel-info-value">{detailsPanelLocation.name}</div>
            </div>
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Status:</div>
              <div className="details-panel-info-value">
                {detailsPanelLocation.online ? 
                  <span style={{ color: '#10B981' }}>Online</span> : 
                  <span style={{ color: '#9CA3AF' }}>Offline</span>
                }
              </div>
            </div>
            {detailsPanelLocation.lastUpdated && (
              <div className="details-panel-info-item">
                <div className="details-panel-info-label">Last Seen:</div>
                <div className="details-panel-info-value">
                  {formatDate(detailsPanelLocation.lastUpdated)}
                </div>
              </div>
            )}
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Location:</div>
              <div className="details-panel-info-value">
                {detailsPanelLocation.position.lat.toFixed(6)}, {detailsPanelLocation.position.lng.toFixed(6)}
              </div>
            </div>
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Breadcrumb:</div>
              <div className="details-panel-info-value">
                {detailsPanelLocation.hasTrail ? 'Active' : 'None'}
              </div>
            </div>
          </div>
          
          <div className="details-panel-actions">
            <button 
              className="details-panel-action-btn action-navigate"
              onClick={(e) => {
                e.stopPropagation();
                resetAutoCloseTimer();
                if (mapRef.current) {
                  mapRef.current.setView([
                    detailsPanelLocation.position.lat,
                    detailsPanelLocation.position.lng
                  ], 16);
                }
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 16 16" fill="currentColor">
                <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10m0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6"/>
              </svg>
              Center on Map
            </button>
            
            <button 
              className="details-panel-action-btn action-dm"
              onClick={(e) => {
                e.stopPropagation();
                resetAutoCloseTimer();
                handleStartDM(detailsPanelLocation);
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 16 16" fill="currentColor">
                <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
              </svg>
              Send Message
            </button>
            
            {isAdmin && detailsPanelLocation.hasTrail && (
              <button 
                className="details-panel-action-btn action-delete"
                onClick={(e) => {
                  e.stopPropagation();
                  resetAutoCloseTimer();
                  confirmDeleteTrail(detailsPanelLocation.id);
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"/>
                  <path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z"/>
                </svg>
                Delete Trail
              </button>
            )}
          </div>
        </>
      );
    } else {
      const vehicleImageUrl = getVehicleImageUrl();
      const statusInfo = getStatusInfo();
      
      const isSearchResultValue = isSearchResult();
      const isFromTeam = isTeamVehicle();
      const isMyVehicle = isOwnVehicle();
      const parsedVehicleInfo = parseVehicleInfo();
      const driveType = orderDetails?.driveType || getDriveTypeFromVehicle(parsedVehicleInfo.make, parsedVehicleInfo.model);
      const vehicleIcon = getVehicleTypeIcon(parsedVehicleInfo.make, parsedVehicleInfo.model);
      
      // Check driver status
      const isInRoute = detailsPanelLocation?.inRouteDriverId === currentUser?.uid;
      const hasArrived = detailsPanelLocation?.arrivedDriverId === currentUser?.uid;
      const someoneElseInRoute = detailsPanelLocation?.inRouteDriverId && detailsPanelLocation?.inRouteDriverId !== currentUser?.uid;
      const someoneElseArrived = detailsPanelLocation?.arrivedDriverId && detailsPanelLocation?.arrivedDriverId !== currentUser?.uid;
      
      // Check if vehicle is eligible for team actions
      const isEligibleForTeamActions = !isSearchResultValue && 
                                       (detailsPanelLocation.status === 'FOUND' || 
                                        detailsPanelLocation.status === 'PENDING PICKUP' || 
                                        isFromTeam);
      
      return (
        <>
          {isLoading && (
            <div className="loading-indicator">
              <div className="spinner"></div>
              <span>Loading vehicle details...</span>
            </div>
          )}
        
          <div className="details-panel-title">
            <span className="vehicle-title-icon">{vehicleIcon}</span>
            {detailsPanelLocation.name || detailsPanelLocation.vehicle || `${parsedVehicleInfo.year} ${parsedVehicleInfo.make} ${parsedVehicleInfo.model}`}
            <span className={`details-panel-status ${statusInfo.styleClass}`}>
              {statusInfo.label}
            </span>
            {isSearchResultValue && (
              <span className="details-panel-status search-result">Search Result</span>
            )}
            {isFromTeam && (
              <span className="details-panel-status team-vehicle">Team Vehicle</span>
            )}
            {detailsPanelLocation.isAdminOnly && 
              <span className="details-panel-status admin">Admin</span>
            }
          </div>
          
          {/* Team Member Info for Team Vehicles */}
          {isFromTeam && detailsPanelLocation.teamMemberName && (
            <div className="team-member-info">
              <div className="team-member-avatar">
                {detailsPanelLocation.teamMemberName.charAt(0).toUpperCase()}
              </div>
              <div className="team-member-details">
                <div className="team-member-name">From: {detailsPanelLocation.teamMemberName}</div>
                {detailsPanelLocation.weekRange && (
                  <div className="team-member-week">{detailsPanelLocation.weekRange}</div>
                )}
              </div>
            </div>
          )}
          
          {/* Live Tracking Status - VISIBLE TO EVERYONE */}
          {(detailsPanelLocation.inRouteDriverId || detailsPanelLocation.arrivedDriverId) && (
            <div className={`live-tracking-status ${detailsPanelLocation.arrivedDriverId ? 'arrived' : 'in-route'}`}>
              <div className="tracking-icon">
                {detailsPanelLocation.arrivedDriverId ? (
                  <span className="pulse-arrived">📍</span>
                ) : (
                  <span className="pulse-moving">🚚</span>
                )}
              </div>
              <div className="tracking-details">
                {detailsPanelLocation.arrivedDriverId ? (
                  <>
                    <div className="tracking-title">DRIVER ARRIVED</div>
                    <div className="tracking-driver">{detailsPanelLocation.arrivedDriverName}</div>
                    {detailsPanelLocation.arrivedTimestamp && (
                      <div className="tracking-time">
                        {formatTimestamp(detailsPanelLocation.arrivedTimestamp)}
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    <div className="tracking-title">DRIVER IN ROUTE</div>
                    <div className="tracking-driver">{detailsPanelLocation.inRouteDriverName}</div>
                    {detailsPanelLocation.inRouteTimestamp && (
                      <div className="tracking-time">
                        Departed {formatTimestamp(detailsPanelLocation.inRouteTimestamp)}
                      </div>
                    )}
                  </>
                )}
              </div>
              {detailsPanelLocation.inRouteDriverId && !detailsPanelLocation.arrivedDriverId && etaCountdown && (
                <div className="tracking-eta">
                  <div className="eta-label">ETA</div>
                  <div className="eta-time" key={currentTime}>{etaCountdown}</div>
                </div>
              )}
            </div>
          )}
          
          {/* Bottom Status Warning */}
          {detailsPanelLocation.bottomStatus && (
            <div className="bottom-status-warning">
              <div className="warning-icon">⚠️</div>
              <div className="warning-content">
                <div className="warning-title">Bottom Status: {detailsPanelLocation.bottomStatus}</div>
                <div className="warning-subtitle">
                  Attempt {detailsPanelLocation.bottomStatusCount || 1} of 3
                </div>
              </div>
            </div>
          )}
          
          {/* Proximity Warning */}
          {renderProximityWarning()}
          
          {/* Team Vehicle Action Buttons - ONLY FOR TOW TRUCK DRIVERS */}
          {isEligibleForTeamActions && (
            <>
              {isMyVehicle ? (
                // If it's the user's own vehicle, just show a watch status message
                <div className="bg-blue-900 bg-opacity-30 rounded-lg p-3 text-center border border-blue-600 mb-3">
                  <p className="text-blue-300 text-sm font-semibold">
                    👁️ This is your vehicle - Watch live tracking status
                  </p>
                </div>
              ) : canSecureVehicles && (isTowTruckUser || isAdmin) ? (
                // Show action buttons only for tow truck drivers and admins
                <div className="team-vehicle-actions">
                  {!detailsPanelLocation.inRouteDriverId && !detailsPanelLocation.arrivedDriverId ? (
                    <button
                      onClick={handleMarkInRoute}
                      className={`team-action-btn in-route ${activeButton === 'inroute' ? 'btn-pulse' : ''}`}
                    >
                      <span className="btn-icon">🚚</span>
                      <span className="btn-text">MARK IN ROUTE</span>
                    </button>
                  ) : isInRoute && !hasArrived ? (
                    <button
                      onClick={handleMarkArrived}
                      className={`team-action-btn arrived ${activeButton === 'arrived' ? 'btn-pulse' : ''}`}
                    >
                      <span className="btn-icon">📍</span>
                      <span className="btn-text">MARK ARRIVED</span>
                    </button>
                  ) : hasArrived ? (
                    <>
                      {detailsPanelLocation.bottomStatus && (
                        <button
                          onClick={handleRecheckVehicle}
                          className={`team-action-btn recheck ${activeButton === 'recheck' ? 'btn-pulse' : ''}`}
                        >
                          <span className="btn-icon">🔄</span>
                          <span className="btn-text">RECHECK VEHICLE (Reset Status)</span>
                        </button>
                      )}
                      
                      <button
                        onClick={handleSecureTeamVehicle}
                        className={`team-action-btn secure ${activeButton === 'secure' ? 'btn-pulse' : ''}`}
                      >
                        <span className="btn-icon">🔒</span>
                        <span className="btn-text">SECURE THIS VEHICLE</span>
                      </button>
                      
                      {/* Bottom Status Buttons Grid */}
                      <div className="bottom-status-grid">
                        <button
                          onClick={() => handleMarkBottomStatus('GONE')}
                          className="bottom-status-btn gone"
                        >
                          Gone on Arrival
                        </button>
                        <button
                          onClick={() => handleMarkBottomStatus('BLOCKED IN')}
                          className="bottom-status-btn blocked"
                        >
                          Blocked In
                        </button>
                        <button
                          onClick={() => handleMarkBottomStatus('CANT SECURE')}
                          className="bottom-status-btn cant-secure"
                        >
                          Can't Secure
                        </button>
                        <button
                          onClick={() => handleMarkBottomStatus('DEBTOR INTERFERENCE')}
                          className="bottom-status-btn interference"
                        >
                          Debtor Interference
                        </button>
                      </div>
                    </>
                  ) : (
                    // Someone else is handling it - show notice
                    <div className="driver-assigned-notice">
                      {someoneElseArrived ? 
                        `🚧 ${detailsPanelLocation.arrivedDriverName} is handling this vehicle` : 
                        `🚚 ${detailsPanelLocation.inRouteDriverName} is on the way`
                      }
                    </div>
                  )}
                </div>
              ) : !isMyVehicle ? (
                // Non-tow truck drivers see this message
                <div className="bg-gray-700 rounded-lg p-3 text-center mb-3">
                  <p className="text-gray-300 text-sm">
                    🔒 Tow truck drivers only
                  </p>
                </div>
              ) : null}
            </>
          )}
          
          {/* Vehicle Image Container */}
          <div className="vehicle-image-container" onClick={(e) => {
            e.stopPropagation();
            resetAutoCloseTimer();
          }}>
            {vehicleImageUrl ? (
              <>
                <img 
                  src={vehicleImageUrl} 
                  alt={`${detailsPanelLocation.make || ''} ${detailsPanelLocation.model || ''}`}
                  className="vehicle-main-image"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="300" height="180" viewBox="0 0 300 180"><rect width="300" height="180" fill="%231A2642" /><path d="M60 120 L90 90 L210 90 L240 120 L240 135 L225 135 A15 15 0 0 1 195 135 L105 135 A15 15 0 0 1 75 135 L60 135 Z" fill="%23374151"/><path d="M90 90 L105 60 L195 60 L210 90" fill="%234B5563"/><circle cx="90" cy="135" r="15" fill="%231F2937"/><circle cx="210" cy="135" r="15" fill="%231F2937"/></svg>';
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    resetAutoCloseTimer();
                    if (detailsPanelLocation.images && Array.isArray(detailsPanelLocation.images) && detailsPanelLocation.images.length > 0) {
                      const imageUrls = detailsPanelLocation.images.map(img => 
                        typeof img === 'string' ? img : img.url
                      );
                      openImageViewer(imageUrls, 0);
                    } else if (renderedViews.length > 0) {
                      const imageUrls = renderedViews.map(view => view.url);
                      openImageViewer(imageUrls, selectedViewIndex);
                    }
                  }}
                />
                
                {renderedViews.length > 1 && (
                  <div className="auto-cycle-control">
                    <button 
                      className={`auto-cycle-btn ${isAutoCycling ? 'auto-cycle-active' : ''}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleAutoCycle();
                      }}
                      title={isAutoCycling ? "Stop auto-cycling" : "Start auto-cycling"}
                    >
                      {isAutoCycling ? (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 16 16" fill="currentColor">
                          <path d="M5.5 3.5A1.5 1.5 0 0 1 7 5v6a1.5 1.5 0 0 1-3 0V5a1.5 1.5 0 0 1 1.5-1.5zm5 0A1.5 1.5 0 0 1 12 5v6a1.5 1.5 0 0 1-3 0V5a1.5 1.5 0 0 1 1.5-1.5z"/>
                        </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 16 16" fill="currentColor">
                          <path d="m11.596 8.697-6.363 3.692c-.54.313-1.233-.066-1.233-.697V4.308c0-.63.692-1.01 1.233-.696l6.363 3.692a.802.802 0 0 1 0 1.393z"/>
                        </svg>
                      )}
                    </button>
                  </div>
                )}
                
                {/* Drive Type Badge */}
                <div className={`drive-type-badge ${driveType.toLowerCase()}`}>
                  {driveType}
                </div>
              </>
            ) : (
              <div className="vehicle-image-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                  <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-1h3a1 1 0 001-1v-3a1 1 0 00-.7-.997l-2.099-4.199A1 1 0 0011.2 4H3zm1 2h6.308l1.7 3.4H4V6z" />
                </svg>
                <span>{parsedVehicleInfo.year || ''} {parsedVehicleInfo.make || ''} {parsedVehicleInfo.model || ''}</span>
              </div>
            )}
          </div>
          
          {/* View selector for multiple vehicle angles */}
          {renderedViews.length > 1 && (
            <div className="view-selector-container">
              <div className="view-selector-scroll">
                {renderedViews.map((view, index) => (
                  <div 
                    key={index} 
                    className={`view-selector-item ${selectedViewIndex === index ? 'selected' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSwitchView(index);
                    }}
                    title={`View angle ${view.angle || index + 1}`}
                  >
                    <img 
                      src={view.url} 
                      alt={`Angle ${view.angle || index + 1}`}
                      className="view-selector-image"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="45" viewBox="0 0 60 45"><rect width="60" height="45" fill="%231A2642" /></svg>';
                      }}
                    />
                    <span className="view-angle-badge">{index + 1}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* Additional images */}
          {detailsPanelLocation.images && Array.isArray(detailsPanelLocation.images) && detailsPanelLocation.images.length > 1 && !renderedViews.length > 1 && (
            <div className={`details-panel-image-grid ${isSmallScreen ? 'grid-cols-2' : 'grid-cols-4'}`} onClick={(e) => {
              e.stopPropagation();
              resetAutoCloseTimer();
            }}>
              {detailsPanelLocation.images.slice(1).map((img, index) => {
                const imageUrl = typeof img === 'string' ? img : img.url;
                
                return (
                  <img 
                    key={index}
                    src={imageUrl} 
                    alt={`${detailsPanelLocation.name || ''} ${index + 2}`}
                    className="details-panel-image"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%231A2642" /><path d="M25 60 L35 50 L65 50 L75 60 L75 70 L65 70 L35 70 L25 70 Z" fill="%23374151"/></svg>';
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      resetAutoCloseTimer();
                      const imageUrls = detailsPanelLocation.images.map(i => 
                        typeof i === 'string' ? i : i.url
                      );
                      openImageViewer(imageUrls, index + 1);
                    }}
                  />
                );
              })}
            </div>
          )}
          
          {detailsPanelLocation.parkingSide && (
            <div className="parking-side-indicator" style={{ marginBottom: '10px' }} onClick={(e) => {
              e.stopPropagation();
              resetAutoCloseTimer();
            }}>
              Vehicle Parked on {detailsPanelLocation.parkingSide.charAt(0).toUpperCase() + detailsPanelLocation.parkingSide.slice(1)} Side 
              {detailsPanelLocation.parkingSide === 'left' ? ' ←' : ' →'}
            </div>
          )}
          
          {/* Registration info for search results */}
          {isSearchResultValue && renderRegistrationInfo()}
          
          {/* Vehicle Information */}
          <div className="details-panel-info" onClick={(e) => {
            e.stopPropagation();
            resetAutoCloseTimer();
          }}>
            <div className="details-panel-section-title">Vehicle Information</div>
            
            <div className="vehicle-info-grid">
              <div className="info-grid-item">
                <div className="info-icon">🏭</div>
                <div className="info-content">
                  <div className="info-label">Make</div>
                  <div className="info-value">{parsedVehicleInfo.make}</div>
                </div>
              </div>
              
              <div className="info-grid-item">
                <div className="info-icon">🚗</div>
                <div className="info-content">
                  <div className="info-label">Model</div>
                  <div className="info-value">{parsedVehicleInfo.model}</div>
                </div>
              </div>
              
              <div className="info-grid-item">
                <div className="info-icon">📅</div>
                <div className="info-content">
                  <div className="info-label">Year</div>
                  <div className="info-value">{parsedVehicleInfo.year}</div>
                </div>
              </div>
              
              <div className="info-grid-item">
                <div className="info-icon">🎨</div>
                <div className="info-content">
                  <div className="info-label">Color</div>
                  <div className="info-value">{detailsPanelLocation.color || 'N/A'}</div>
                </div>
              </div>
              
              <div className="info-grid-item">
                <div className="info-icon">🔑</div>
                <div className="info-content">
                  <div className="info-label">VIN</div>
                  <div className="info-value vin-value">{detailsPanelLocation.vin || 'N/A'}</div>
                </div>
              </div>
              
              <div className="info-grid-item">
                <div className="info-icon">⚙️</div>
                <div className="info-content">
                  <div className="info-label">Drive Type</div>
                  <div className="info-value drive-value">{driveType}</div>
                </div>
              </div>
              
              {detailsPanelLocation.plateNumber && (
                <div className="info-grid-item">
                  <div className="info-icon">🏷️</div>
                  <div className="info-content">
                    <div className="info-label">Plate</div>
                    <div className="info-value">{detailsPanelLocation.plateNumber}</div>
                  </div>
                </div>
              )}
              
              {detailsPanelLocation.accountNumber && (
                <div className="info-grid-item">
                  <div className="info-icon">📋</div>
                  <div className="info-content">
                    <div className="info-label">Account</div>
                    <div className="info-value">{detailsPanelLocation.accountNumber}</div>
                  </div>
                </div>
              )}
              
              {detailsPanelLocation.financier && (
                <div className="info-grid-item col-span-2">
                  <div className="info-icon">🏦</div>
                  <div className="info-content">
                    <div className="info-label">Financier</div>
                    <div className="info-value">{detailsPanelLocation.financier}</div>
                  </div>
                </div>
              )}
            </div>
            
            {/* Location Information */}
            <div className="details-panel-section-title mt-4">Location Information</div>
            
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Address:</div>
              <div className="details-panel-info-value">
                {detailsPanelLocation.address || 'N/A'}
              </div>
            </div>
            
            {/* GPS Status */}
            {detailsPanelLocation.position && (
              <div className="gps-status-indicator">
                <div className="gps-icon">📍</div>
                <div className="gps-text">GPS Available</div>
                <div className="gps-coords">
                  {detailsPanelLocation.position.lat.toFixed(6)}, {detailsPanelLocation.position.lng.toFixed(6)}
                </div>
              </div>
            )}
            
            {/* Status Information */}
            <div className="details-panel-section-title mt-4">Status Information</div>
            
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Status:</div>
              <div className="details-panel-info-value">
                <span className={`status-badge ${statusInfo.styleClass}`}>{statusInfo.label}</span>
              </div>
            </div>
            
            {detailsPanelLocation.createdAt && (
              <div className="details-panel-info-item">
                <div className="details-panel-info-label">Created:</div>
                <div className="details-panel-info-value">{formatDate(detailsPanelLocation.createdAt)}</div>
              </div>
            )}
            
            {detailsPanelLocation.secureTimestamp && (
              <div className="details-panel-info-item">
                <div className="details-panel-info-label">Secured At:</div>
                <div className="details-panel-info-value">{formatDate(detailsPanelLocation.secureTimestamp)}</div>
              </div>
            )}
          </div>
          
          {/* Notes Section */}
          {(detailsPanelLocation.details || detailsPanelLocation.notes) && (
            <div className="details-panel-description" onClick={(e) => {
              e.stopPropagation();
              resetAutoCloseTimer();
            }}>
              <div className="details-panel-section-title">Notes</div>
              <div className="notes-content">
                {detailsPanelLocation.details || detailsPanelLocation.notes || 'No notes available'}
              </div>
            </div>
          )}
          
          {/* Action buttons */}
          <div className="details-panel-actions" onClick={(e) => {
            e.stopPropagation();
            resetAutoCloseTimer();
          }}>
            {!isSearchResultValue && detailsPanelLocation.position && (
              <button 
                className="details-panel-action-btn action-navigate"
                onClick={(e) => {
                  e.stopPropagation();
                  resetAutoCloseTimer();
                  handleSelectLocation(detailsPanelLocation);
                  startNavigation();
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M9.502 11a3 3 0 0 1-2.397-4.798l5.44-6.55a1 1 0 1 1 1.537 1.274l-5.01 6.53A3 3 0 0 1 9.502 11m1.732-4h.768a2.5 2.5 0 0 1 0 5h-.768zM6.5 1A.5.5 0 0 1 7 .5h2a.5.5 0 0 1 0 1v.57l1.5-1.5A.5.5 0 0 1 11 0h2.5a.5.5 0 0 1 0 1h-2.158l-2 2H11.5a.5.5 0 0 1 0 1h-2v2.5L8 9V13h2.5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1H8V9l-1.5-1.5V3H6.5a.5.5 0 0 1 0-1"/>
                </svg>
                Navigate
              </button>
            )}
            
            {!isSearchResultValue && isTowTruckUser && detailsPanelLocation.status !== 'picked-up' && detailsPanelLocation.status !== 'SECURED' && (
              <button 
                className="details-panel-action-btn action-pickup"
                onClick={(e) => {
                  e.stopPropagation();
                  resetAutoCloseTimer();
                  markLocationAsPickedUp(detailsPanelLocation);
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M2.5 3.5a.5.5 0 0 1 0-1h11a.5.5 0 0 1 0 1zm2-2a.5.5 0 0 1 0-1h7a.5.5 0 0 1 0 1zM0 13a1.5 1.5 0 0 0 1.5 1.5h13A1.5 1.5 0 0 0 16 13V6a1.5 1.5 0 0 0-1.5-1.5h-13A1.5 1.5 0 0 0 0 6zm1.5.5A.5.5 0 0 1 1 13V6a.5.5 0 0 1 .5-.5h13a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5z"/>
                </svg>
                Pick Up
              </button>
            )}
          </div>
        </>
      );
    }
  };

  return (
    <div 
      className={`details-panel details-custom-styles ${!isPanelVisible ? 'hidden' : ''}`}
      onClick={(e) => {
        e.stopPropagation();
        resetAutoCloseTimer();
      }}
    >
      <div className="details-panel-header">
        <h3 className={`font-bold ${isSmallScreen ? 'text-md' : 'text-lg'}`}>Details</h3>
        <button 
          className="close-panel-btn"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            handleClosePanel();
          }}
        >
          ×
        </button>
      </div>
      
      <div className="details-panel-content">
        {detailsPanelLocation ? (
          renderDetailsPanelContent()
        ) : (
          <div className="text-center text-gray-400 mt-4">
            <p>Select a location or user to view details</p>
          </div>
        )}
      </div>
      
      <BottomStatusMenu />
      
      {showAddressSelection && <AddressSelectionUI />}
      
      <style>{`
        .details-custom-styles {
          height: 100%;
          display: flex;
          flex-direction: column;
          width: 100%;
        }
        
        .details-panel-header {
          padding: 0.75rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #374151;
          background: linear-gradient(to right, #1F2937, #111827);
        }
        
        .close-panel-btn {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background-color: #4B5563;
          color: white;
          font-size: 18px;
          cursor: pointer;
          transition: all 0.2s;
        }
        
        .close-panel-btn:hover {
          background-color: #6B7280;
          transform: scale(1.1);
        }
        
        .details-panel-content {
          flex: 1;
          overflow-y: auto;
          padding: 1rem;
          position: relative;
        }
        
        .details-panel-title {
          font-size: ${isSmallScreen ? '1rem' : '1.25rem'};
          font-weight: 600;
          margin-bottom: 1rem;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 0.5rem;
        }
        
        .vehicle-title-icon {
          font-size: 1.5rem;
          margin-right: 0.25rem;
        }
        
        .details-panel-status {
          padding: 0.125rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
        }
        
        .details-panel-status.picked-up {
          background-color: #065F46;
          color: #D1FAE5;
        }
        
        .details-panel-status.pending {
          background-color: #92400E;
          color: #FEF3C7;
        }
        
        .details-panel-status.open-order {
          background-color: #1E40AF;
          color: #DBEAFE;
        }
        
        .details-panel-status.secure {
          background-color: #065F46;
          color: #D1FAE5;
        }
        
        .details-panel-status.found {
          background-color: #059669;
          color: #D1FAE5;
        }
        
        .details-panel-status.admin {
          background-color: #1E40AF;
          color: #DBEAFE;
        }
        
        .details-panel-status.search-result {
          background-color: #4F46E5;
          color: #E0E7FF;
        }
        
        .details-panel-status.team-vehicle {
          background-color: #7C3AED;
          color: #EDE9FE;
        }
        
        /* Team Member Info */
        .team-member-info {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          background-color: rgba(124, 58, 237, 0.1);
          border: 1px solid rgba(124, 58, 237, 0.3);
          border-radius: 0.5rem;
          padding: 0.75rem;
          margin-bottom: 1rem;
        }
        
        .team-member-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: linear-gradient(135deg, #7C3AED, #6D28D9);
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          color: white;
          font-size: 1.25rem;
        }
        
        .team-member-details {
          flex: 1;
        }
        
        .team-member-name {
          font-weight: 600;
          color: #E9D5FF;
        }
        
        .team-member-week {
          font-size: 0.75rem;
          color: #C4B5FD;
        }
        
        /* Live Tracking Status */
        .live-tracking-status {
          display: flex;
          align-items: center;
          gap: 1rem;
          padding: 1rem;
          border-radius: 0.5rem;
          margin-bottom: 1rem;
          position: relative;
          overflow: hidden;
        }
        
        .live-tracking-status.in-route {
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.2));
          border: 1px solid rgba(59, 130, 246, 0.4);
        }
        
        .live-tracking-status.arrived {
          background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.2));
          border: 1px solid rgba(16, 185, 129, 0.4);
        }
        
        .tracking-icon {
          font-size: 2rem;
        }
        
        .pulse-moving {
          animation: pulse-blue 2s infinite;
        }
        
        .pulse-arrived {
          animation: pulse-green 2s infinite;
        }
        
        @keyframes pulse-blue {
          0% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.2); opacity: 0.7; }
          100% { transform: scale(1); opacity: 1; }
        }
        
        @keyframes pulse-green {
          0% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.2); opacity: 0.7; }
          100% { transform: scale(1); opacity: 1; }
        }
        
        .tracking-details {
          flex: 1;
        }
        
        .tracking-title {
          font-weight: 700;
          font-size: 0.875rem;
          margin-bottom: 0.25rem;
        }
        
        .in-route .tracking-title {
          color: #60A5FA;
        }
        
        .arrived .tracking-title {
          color: #34D399;
        }
        
        .tracking-driver {
          font-size: 0.875rem;
          color: #E5E7EB;
        }
        
        .tracking-time {
          font-size: 0.75rem;
          color: #9CA3AF;
          margin-top: 0.25rem;
        }
        
        .tracking-eta {
          text-align: center;
          padding: 0.5rem 1rem;
          background-color: rgba(0, 0, 0, 0.3);
          border-radius: 0.5rem;
        }
        
        .eta-label {
          font-size: 0.625rem;
          color: #FDE047;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        
        .eta-time {
          font-size: 1.25rem;
          font-weight: 700;
          color: #FEF3C7;
        }
        
        /* Bottom Status Warning */
        .bottom-status-warning {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          background-color: rgba(239, 68, 68, 0.1);
          border: 1px solid rgba(239, 68, 68, 0.3);
          border-radius: 0.5rem;
          padding: 0.75rem;
          margin-bottom: 1rem;
        }
        
        .warning-icon {
          font-size: 1.5rem;
        }
        
        .warning-content {
          flex: 1;
        }
        
        .warning-title {
          font-weight: 600;
          color: #FCA5A5;
          font-size: 0.875rem;
        }
        
        .warning-subtitle {
          font-size: 0.75rem;
          color: #F87171;
        }
        
        /* Team Vehicle Actions */
        .team-vehicle-actions {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          margin-bottom: 1rem;
          padding: 1rem;
          background-color: rgba(17, 24, 39, 0.5);
          border-radius: 0.5rem;
          border: 1px solid #374151;
        }
        
        .team-action-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          padding: 0.875rem;
          border-radius: 0.5rem;
          font-weight: 600;
          transition: all 0.3s;
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: none;
        }
        
        .team-action-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .team-action-btn.in-route {
          background: linear-gradient(135deg, #3B82F6, #2563EB);
          color: white;
        }
        
        .team-action-btn.arrived {
          background: linear-gradient(135deg, #F97316, #EA580C);
          color: white;
        }
        
        .team-action-btn.secure {
          background: linear-gradient(135deg, #10B981, #059669);
          color: white;
        }
        
        .team-action-btn.recheck {
          background: linear-gradient(135deg, #6366F1, #4F46E5);
          color: white;
        }
        
        .team-action-btn.bottom-status {
          background: linear-gradient(135deg, #6B7280, #4B5563);
          color: white;
        }
        
        .btn-icon {
          font-size: 1.25rem;
        }
        
        .btn-text {
          font-size: 0.875rem;
          letter-spacing: 0.05em;
        }
        
        .driver-assigned-notice {
          text-align: center;
          padding: 0.75rem;
          background-color: rgba(75, 85, 99, 0.3);
          border-radius: 0.5rem;
          color: #D1D5DB;
          font-size: 0.875rem;
        }
        
        /* Bottom Status Grid - Aligned with TeamVehicleTracker */
        .bottom-status-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 0.5rem;
          margin-top: 0.5rem;
        }
        
        .bottom-status-btn {
          padding: 0.5rem;
          border-radius: 0.375rem;
          font-size: 0.75rem;
          font-weight: 600;
          transition: all 0.2s;
          cursor: pointer;
          border: none;
          color: white;
        }
        
        .bottom-status-btn.gone {
          background-color: #DC2626;
        }
        
        .bottom-status-btn.gone:hover {
          background-color: #B91C1C;
        }
        
        .bottom-status-btn.blocked {
          background-color: #EA580C;
        }
        
        .bottom-status-btn.blocked:hover {
          background-color: #C2410C;
        }
        
        .bottom-status-btn.cant-secure {
          background-color: #6B7280;
        }
        
        .bottom-status-btn.cant-secure:hover {
          background-color: #4B5563;
        }
        
        .bottom-status-btn.interference {
          background-color: #7C3AED;
        }
        
        .bottom-status-btn.interference:hover {
          background-color: #6D28D9;
        }
        
        /* Bottom Status Menu */
        .bottom-status-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.7);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 10000;
          animation: fadeIn 0.3s ease-out;
        }
        
        .bottom-status-dialog {
          background-color: #1F2937;
          border-radius: 0.75rem;
          padding: 1.5rem;
          width: 90%;
          max-width: 450px;
          border: 1px solid #374151;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
          animation: slideUp 0.3s ease-out;
        }
        
        .bottom-status-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: white;
          margin-bottom: 0.5rem;
          text-align: center;
        }
        
        .bottom-status-subtitle {
          font-size: 0.875rem;
          color: #9CA3AF;
          text-align: center;
          margin-bottom: 1.5rem;
        }
        
        .bottom-status-options {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 0.75rem;
          margin-bottom: 1rem;
        }
        
        .bottom-status-option {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 1rem;
          border-radius: 0.5rem;
          border: 2px solid transparent;
          transition: all 0.2s;
          cursor: pointer;
        }
        
        .bottom-status-option:hover {
          transform: translateY(-2px);
        }
        
        .bottom-status-option.gone {
          background-color: rgba(239, 68, 68, 0.2);
          border-color: rgba(239, 68, 68, 0.4);
        }
        
        .bottom-status-option.gone:hover {
          background-color: rgba(239, 68, 68, 0.3);
          border-color: #EF4444;
        }
        
        .bottom-status-option.blocked {
          background-color: rgba(251, 146, 60, 0.2);
          border-color: rgba(251, 146, 60, 0.4);
        }
        
        .bottom-status-option.blocked:hover {
          background-color: rgba(251, 146, 60, 0.3);
          border-color: #FB923C;
        }
        
        .bottom-status-option.cant-secure {
          background-color: rgba(107, 114, 128, 0.2);
          border-color: rgba(107, 114, 128, 0.4);
        }
        
        .bottom-status-option.cant-secure:hover {
          background-color: rgba(107, 114, 128, 0.3);
          border-color: #6B7280;
        }
        
        .bottom-status-option.interference {
          background-color: rgba(124, 58, 237, 0.2);
          border-color: rgba(124, 58, 237, 0.4);
        }
        
        .bottom-status-option.interference:hover {
          background-color: rgba(124, 58, 237, 0.3);
          border-color: #7C3AED;
        }
        
        .status-icon {
          font-size: 2rem;
          margin-bottom: 0.5rem;
        }
        
        .status-text {
          font-weight: 600;
          color: white;
          font-size: 0.875rem;
          text-align: center;
        }
        
        .status-desc {
          font-size: 0.625rem;
          color: #9CA3AF;
          text-align: center;
          margin-top: 0.25rem;
        }
        
        .bottom-status-cancel {
          width: 100%;
          padding: 0.75rem;
          background-color: #4B5563;
          color: white;
          border-radius: 0.375rem;
          font-weight: 500;
          transition: all 0.2s;
        }
        
        .bottom-status-cancel:hover {
          background-color: #374151;
        }
        
        /* Vehicle Info Grid */
        .vehicle-info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 0.75rem;
          margin-top: 0.75rem;
        }
        
        .info-grid-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background-color: #111827;
          padding: 0.75rem;
          border-radius: 0.5rem;
          border: 1px solid #374151;
        }
        
        .info-grid-item.col-span-2 {
          grid-column: span 2;
        }
        
        .info-icon {
          font-size: 1.25rem;
        }
        
        .info-content {
          flex: 1;
        }
        
        .info-label {
          font-size: 0.625rem;
          color: #9CA3AF;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        
        .info-value {
          font-weight: 600;
          color: white;
          font-size: 0.875rem;
        }
        
        .vin-value {
          font-family: monospace;
          color: #60A5FA;
          word-break: break-all;
        }
        
        .drive-value {
          color: #F59E0B;
        }
        
        /* Drive Type Badge */
        .drive-type-badge {
          position: absolute;
          bottom: 0.5rem;
          left: 0.5rem;
          background-color: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-size: 0.75rem;
          font-weight: 600;
          letter-spacing: 0.05em;
        }
        
        .drive-type-badge.fwd {
          background-color: rgba(59, 130, 246, 0.8);
        }
        
        .drive-type-badge.rwd {
          background-color: rgba(239, 68, 68, 0.8);
        }
        
        .drive-type-badge.awd,
        .drive-type-badge.4wd {
          background-color: rgba(16, 185, 129, 0.8);
        }
        
        /* GPS Status Indicator */
        .gps-status-indicator {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background-color: rgba(16, 185, 129, 0.1);
          border: 1px solid rgba(16, 185, 129, 0.3);
          border-radius: 0.5rem;
          padding: 0.5rem 0.75rem;
          margin-top: 0.75rem;
        }
        
        .gps-icon {
          font-size: 1rem;
        }
        
        .gps-text {
          font-size: 0.75rem;
          color: #34D399;
          font-weight: 600;
        }
        
        .gps-coords {
          margin-left: auto;
          font-size: 0.625rem;
          color: #6EE7B7;
          font-family: monospace;
        }
        
        /* Auto-close countdown styles */
        .auto-close-countdown {
          background-color: rgba(234, 88, 12, 0.2);
          border: 1px solid rgba(234, 88, 12, 0.4);
          border-radius: 0.375rem;
          padding: 0.75rem;
          margin-top: 0.75rem;
          text-align: center;
        }
        
        .countdown-time {
          font-weight: 700;
          color: #F97316;
        }
        
        .countdown-bar-container {
          width: 100%;
          height: 6px;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 3px;
          margin-top: 0.5rem;
          overflow: hidden;
        }
        
        .countdown-bar {
          height: 100%;
          background-color: #F97316;
          border-radius: 3px;
          transition: width 1s linear;
        }
        
        /* Loading indicator */
        .loading-indicator {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 1rem;
          margin-bottom: 1rem;
          background-color: rgba(17, 24, 39, 0.7);
          border-radius: 0.5rem;
        }
        
        .spinner {
          border: 3px solid rgba(59, 130, 246, 0.3);
          border-radius: 50%;
          border-top: 3px solid #3B82F6;
          width: 24px;
          height: 24px;
          animation: spin 1s linear infinite;
          margin-bottom: 0.5rem;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        /* Proximity warning styles */
        .proximity-warning {
          background-color: rgba(220, 38, 38, 0.1);
          border: 1px solid rgba(220, 38, 38, 0.3);
          border-radius: 0.5rem;
          padding: 1rem;
          margin: 1rem 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
        }
        
        .proximity-distance {
          font-weight: 600;
          margin: 0.5rem 0 1rem 0;
          color: #60A5FA;
        }
        
        /* Enhanced vehicle image container */
        .vehicle-image-container {
          position: relative;
          width: 100%;
          height: 200px;
          margin-bottom: 0.5rem;
          overflow: hidden;
          border-radius: 0.75rem;
          background-color: #1F2937;
          border: 1px solid #374151;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        
        .vehicle-main-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
          cursor: pointer;
        }
        
        .vehicle-main-image:hover {
          transform: scale(1.05);
        }
        
        .vehicle-image-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #9CA3AF;
          height: 100%;
          width: 100%;
        }
        
        /* Auto-cycle control */
        .auto-cycle-control {
          position: absolute;
          top: 8px;
          right: 8px;
          z-index: 5;
        }
        
        .auto-cycle-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28px;
          height: 28px;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.5);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.2s;
        }
        
        .auto-cycle-btn:hover {
          background-color: rgba(59, 130, 246, 0.7);
          transform: scale(1.1);
        }
        
        .auto-cycle-active {
          background-color: rgba(59, 130, 246, 0.7);
          border-color: rgba(255, 255, 255, 0.4);
          box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
        }
        
        /* View selector for multiple angles */
        .view-selector-container {
          margin-bottom: 1rem;
          padding: 0.5rem;
          background-color: #111827;
          border-radius: 0.5rem;
          border: 1px solid #374151;
        }
        
        .view-selector-scroll {
          display: flex;
          overflow-x: auto;
          gap: 0.5rem;
          padding-bottom: 0.25rem;
          scrollbar-width: thin;
          scrollbar-color: #4B5563 #1F2937;
        }
        
        .view-selector-scroll::-webkit-scrollbar {
          height: 4px;
        }
        
        .view-selector-scroll::-webkit-scrollbar-thumb {
          background-color: #4B5563;
          border-radius: 2px;
        }
        
        .view-selector-scroll::-webkit-scrollbar-track {
          background-color: #1F2937;
          border-radius: 2px;
        }
        
        .view-selector-item {
          flex: 0 0 auto;
          width: 60px;
          height: 45px;
          position: relative;
          border-radius: 0.25rem;
          overflow: hidden;
          cursor: pointer;
          border: 2px solid transparent;
          transition: all 0.2s;
        }
        
        .view-selector-item.selected {
          border-color: #3B82F6;
          box-shadow: 0 0 0 1px #3B82F6;
        }
          .view-selector-item:hover {
          transform: translateY(-2px);
        }
        
        .view-selector-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .view-angle-badge {
          position: absolute;
          bottom: 2px;
          right: 2px;
          background-color: rgba(0, 0, 0, 0.6);
          color: white;
          border-radius: 4px;
          font-size: 8px;
          padding: 1px 3px;
        }
        
        .details-panel-image-grid {
          display: grid;
          grid-template-columns: ${isSmallScreen ? 'repeat(2, 1fr)' : 'repeat(4, 1fr)'};
          gap: 0.5rem;
          margin-bottom: 1rem;
        }
        
        .details-panel-image {
          width: 100%;
          aspect-ratio: 1;
          border-radius: 0.375rem;
          object-fit: cover;
          cursor: pointer;
          transition: transform 0.2s;
          border: 1px solid #374151;
        }
        
        .details-panel-image:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .parking-side-indicator {
          background-color: #374151;
          color: #D1D5DB;
          padding: 0.5rem;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          margin-bottom: 1rem;
          text-align: center;
        }
        
        .details-panel-info {
          margin-bottom: 1rem;
        }
        
        .details-panel-section-title {
          font-weight: 600;
          font-size: 0.875rem;
          color: #60A5FA;
          margin-bottom: 0.5rem;
          padding-bottom: 0.25rem;
          border-bottom: 1px solid #374151;
        }
        
        .details-panel-info-item {
          display: ${isSmallScreen ? 'block' : 'flex'};
          margin-bottom: 0.5rem;
          font-size: ${isSmallScreen ? '0.75rem' : '0.875rem'};
        }
        
        .details-panel-info-label {
          font-weight: 500;
          color: #9CA3AF;
          width: ${isSmallScreen ? 'auto' : '30%'};
          margin-bottom: ${isSmallScreen ? '0.125rem' : '0'};
        }
        
        .details-panel-info-value {
          color: white;
          flex: 1;
          word-break: break-word;
        }
        
        .details-panel-description {
          background-color: #1F2937;
          padding: 0.75rem;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          margin-bottom: 1rem;
        }
        
        .notes-content {
          white-space: pre-wrap;
          margin-top: 0.5rem;
          color: #D1D5DB;
        }
        
        .status-badge {
          padding: 0.125rem 0.375rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          background-color: #374151;
          color: white;
          display: inline-block;
        }
        
        .status-badge.picked-up {
          background-color: #065F46;
          color: #D1FAE5;
        }
        
        .status-badge.pending-pickup {
          background-color: #92400E; 
          color: #FEF3C7;
        }
        
        .status-badge.awaiting-pickup {
          background-color: #B45309;
          color: #FEF3C7;
        }
        
        .status-badge.open-order {
          background-color: #1E40AF;
          color: #DBEAFE;
        }
        
        .status-badge.secure {
          background-color: #065F46;
          color: #D1FAE5;
        }
        
        .status-badge.claim {
          background-color: #9D174D;
          color: #FBCFE8;
        }
        
        .status-badge.on-hold {
          background-color: #6D28D9;
          color: #EDE9FE;
        }
        
        .status-badge.restricted {
          background-color: #DC2626;
          color: #FEE2E2;
        }
        
        .status-badge.completed {
          background-color: #4B5563;
          color: #F9FAFB;
        }
        
        .status-badge.found {
          background-color: #059669;
          color: #D1FAE5;
        }
        
        .details-panel-actions {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          margin-top: 1rem;
        }
        
        .details-panel-action-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0.5rem 0.75rem;
          border-radius: 0.375rem;
          font-size: 0.75rem;
          font-weight: 500;
          transition: all 0.3s;
          flex: 1;
          min-width: 80px;
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: none;
        }
        
        .details-panel-action-btn::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 5px;
          height: 5px;
          background: rgba(255, 255, 255, 0.5);
          opacity: 0;
          border-radius: 100%;
          transform: scale(1, 1) translate(-50%);
          transform-origin: 50% 50%;
        }
        
        .details-panel-action-btn:hover::after {
          animation: ripple 1s ease-out;
        }
        
        @keyframes ripple {
          0% {
            transform: scale(0, 0);
            opacity: 0.5;
          }
          100% {
            transform: scale(20, 20);
            opacity: 0;
          }
        }
        
        .action-navigate {
          background-color: #1D4ED8;
          color: white;
        }
        
        .action-navigate:hover {
          background-color: #1E40AF;
          transform: translateY(-2px);
        }
        
        .action-pickup {
          background-color: #047857;
          color: white;
        }
        
        .action-pickup:hover {
          background-color: #065F46;
          transform: translateY(-2px);
        }
        
        .action-dm {
          background-color: #6D28D9;
          color: white;
        }
        
        .action-dm:hover {
          background-color: #5B21B6;
          transform: translateY(-2px);
        }
        
        .action-delete {
          background-color: #DC2626;
          color: white;
        }
        
        .action-delete:hover {
          background-color: #B91C1C;
          transform: translateY(-2px);
        }
        
        /* Button pulse animation */
        .btn-pulse {
          animation: buttonPulse 0.5s ease-out;
        }
        
        @keyframes buttonPulse {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
          }
        }
        
        /* Address Selection UI */
        .address-selection-container {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 90%;
          max-width: 400px;
          background-color: #1F2937;
          border-radius: 0.5rem;
          padding: 1rem;
          border: 1px solid #374151;
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
          animation: slideUp 0.3s ease-out;
          z-index: 10001;
        }
        
        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }
        
        @keyframes slideUp {
          from {
            transform: translate(-50%, -40%);
            opacity: 0;
          }
          to {
            transform: translate(-50%, -50%);
            opacity: 1;
          }
        }
        
        .address-selection-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
          padding-bottom: 0.75rem;
          border-bottom: 1px solid #374151;
        }
        
        .address-selection-header h4 {
          font-size: 1rem;
          font-weight: 600;
          color: #60A5FA;
        }
        
        .address-selection-close-btn {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background-color: #4B5563;
          color: white;
          font-size: 18px;
          cursor: pointer;
          transition: all 0.2s;
        }
        
        .address-selection-close-btn:hover {
          background-color: #6B7280;
          transform: scale(1.1);
        }
        
        .address-selection-subtitle {
          font-size: 0.8rem;
          color: #9CA3AF;
          margin-bottom: 0.5rem;
          margin-top: 1rem;
        }
        
        .address-selection-list {
          max-height: 300px;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          margin-bottom: 1rem;
          scrollbar-width: thin;
          scrollbar-color: #4B5563 #1F2937;
          padding-right: 0.25rem;
        }
        
        .address-selection-list::-webkit-scrollbar {
          width: 4px;
        }
        
        .address-selection-list::-webkit-scrollbar-thumb {
          background-color: #4B5563;
          border-radius: 2px;
        }
        
        .address-selection-list::-webkit-scrollbar-track {
          background-color: #1F2937;
          border-radius: 2px;
        }
        
        .address-selection-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem;
          border-radius: 0.375rem;
          background-color: #111827;
          border: 1px solid #374151;
          cursor: pointer;
          transition: all 0.2s;
        }
        
        .address-selection-item:hover {
          background-color: #1E40AF;
          border-color: #3B82F6;
          transform: translateY(-2px);
        }
        
        .address-selection-item.address-no-gps {
          background-color: #31271E;
          border-color: #92400E;
          cursor: not-allowed;
          opacity: 0.7;
        }
        
        .address-selection-item.address-no-gps:hover {
          transform: none;
        }
        
        .address-selection-text {
          flex: 1;
          margin-right: 0.5rem;
          word-break: break-word;
        }
        
        .address-selection-vehicle {
          background-color: #1E3A8A;
          border-color: #3B82F6;
        }
        
        .address-has-gps {
          color: #10B981;
        }
        
        .address-no-gps-label {
          color: #F59E0B;
        }
        
        .address-selection-icon {
          color: #60A5FA;
        }
        
        .address-selection-footer {
          display: flex;
          justify-content: flex-end;
          padding-top: 0.75rem;
          border-top: 1px solid #374151;
        }
        
        .address-selection-cancel-btn {
          padding: 0.5rem 1rem;
          background-color: #4B5563;
          color: white;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: background-color 0.2s;
        }
        
        .address-selection-cancel-btn:hover {
          background-color: #374151;
        }
        
        .address-selection-no-data {
          padding: 2rem 1rem;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #9CA3AF;
          background-color: #111827;
          border-radius: 0.375rem;
          margin-bottom: 1rem;
        }
        
        /* Registration info styles */
        .details-panel-section {
          background-color: #1F2937;
          border-radius: 0.5rem;
          padding: 1rem;
          margin-bottom: 1rem;
          border: 1px solid #374151;
        }
        
        .grid-container {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
          gap: 0.75rem;
          margin-top: 0.75rem;
        }
        
        .grid-item {
          background-color: #111827;
          padding: 0.625rem;
          border-radius: 0.375rem;
          border: 1px solid #374151;
        }
        
        .item-label {
          font-size: 0.7rem;
          color: #9CA3AF;
          margin-bottom: 0.25rem;
        }
        
        .item-value {
          font-size: 0.875rem;
          font-weight: 500;
        }
        
        .highlight-value {
          font-size: 1rem;
          color: #60A5FA;
          font-weight: 600;
          letter-spacing: 0.05em;
        }
        
        .clean-title {
          color: #10B981;
        }
        
        .lien-title {
          color: #F59E0B;
        }
        
        .has-lien {
          color: #EF4444;
        }
        
        .no-lien {
          color: #10B981;
        }
        
        .registration-section {
          background-color: #111827;
          padding: 0.75rem;
          border-radius: 0.375rem;
          border: 1px solid #374151;
        }
        
        .section-label {
          font-size: 0.75rem;
          color: #9CA3AF;
          margin-bottom: 0.5rem;
        }
        
        .section-content {
          font-size: 0.875rem;
        }
        
        .prev-registration {
          display: flex;
          align-items: center;
          margin-bottom: 0.5rem;
          font-size: 0.8rem;
        }
        
        .prev-registration:last-child {
          margin-bottom: 0;
        }
        
        .state-badge {
          background-color: #4B5563;
          padding: 0.125rem 0.375rem;
          border-radius: 0.25rem;
          margin-right: 0.5rem;
          color: white;
          font-weight: 500;
        }
        
        .plate-number {
          color: #60A5FA;
          font-weight: 600;
          letter-spacing: 0.05em;
          margin-right: 0.5rem;
        }
        
        .date-text {
          color: #9CA3AF;
          font-size: 0.7rem;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 768px) {
          .details-panel-actions {
            flex-direction: column;
          }
          
          .vehicle-image-container {
            height: 150px;
          }
          
          .vehicle-info-grid {
            grid-template-columns: 1fr;
          }
          
          .info-grid-item.col-span-2 {
            grid-column: span 1;
          }
          
          .team-vehicle-actions {
            padding: 0.75rem;
          }
          
          .bottom-status-options {
            grid-template-columns: 1fr;
          }
          
          .bottom-status-grid {
            grid-template-columns: 1fr;
          }
          
          .grid-container {
            grid-template-columns: repeat(2, 1fr);
          }
        }
        
        /* Helper classes */
        .hidden {
          display: none !important;
        }
        
        .mt-2 {
          margin-top: 0.5rem;
        }
        
        .mt-3 {
          margin-top: 0.75rem;
        }
        
        .mt-4 {
          margin-top: 1rem;
        }
        
        .ml-1 {
          margin-left: 0.25rem;
        }
        
        .font-mono {
          font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
        }
        
        .text-xs {
          font-size: 0.75rem;
        }
        
        .text-gray-300 {
          color: #D1D5DB;
        }
        
        .text-gray-400 {
          color: #9CA3AF;
        }
        
        .text-center {
          text-align: center;
        }
        
        .font-bold {
          font-weight: 700;
        }
        
        .bg-blue-900 {
          background-color: rgb(30 58 138);
        }
        
        .bg-gray-700 {
          background-color: rgb(55 65 81);
        }
        
        .bg-opacity-30 {
          background-color: rgba(30, 58, 138, 0.3);
        }
        
        .rounded-lg {
          border-radius: 0.5rem;
        }
        
        .p-3 {
          padding: 0.75rem;
        }
        
        .mb-3 {
          margin-bottom: 0.75rem;
        }
        
        .border {
          border-width: 1px;
        }
        
        .border-blue-600 {
          border-color: rgb(37 99 235);
        }
        
        .text-blue-300 {
          color: rgb(147 197 253);
        }
        
        .text-gray-300 {
          color: rgb(209 213 219);
        }
        
        .text-sm {
          font-size: 0.875rem;
        }
        
        .font-semibold {
          font-weight: 600;
        }
      `}</style>
    </div>
  );
};

export default DetailsPanel;