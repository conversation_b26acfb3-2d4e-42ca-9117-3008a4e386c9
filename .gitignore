# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.development
.env.production

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log

# editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS files
Thumbs.db

# Slack bot specific
slack-app-manifest.json
slack-credentials.json

# Firebase
firebase-debug.log
.firebase/
firebase-export-*

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env*

# ngrok
ngrok.exe
ngrok
*.ngrok.io