import React, { useState, useEffect } from 'react';
import { getFirestore, doc, getDoc, collection, query, orderBy, limit, getDocs, setDoc, updateDoc, serverTimestamp, where, addDoc, Timestamp } from 'firebase/firestore';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext.js';

const PayInfo = () => {
  // State variables
  const [loading, setLoading] = useState(true);
  const [paystubs, setPaystubs] = useState([]);
  const [currentPaystub, setCurrentPaystub] = useState(null); // Track selected week's paystub
  const [currentPay, setCurrentPay] = useState({
    hoursWorkedToday: 0,
    hoursWorkedWeek: 0,
    hourlyRate: 15,
    currentPayPeriodEarnings: 0,
    bonusPay: 0,
    scanBonus: 0,
    totalScans: 0,
    carsSecured: 0,
    totalPay: 0,
    startDate: null,
    endDate: null
  });
  
  // Add tax state variables with real tax rates
  const [taxWithholdings, setTaxWithholdings] = useState({
    federalTax: 0,
    stateTax: 0,
    socialSecurity: 0,
    medicare: 0,
    healthInsurance: 0,
    totalWithholdings: 0,
    netPay: 0
  });
  
  // Add year-to-date tax withholdings state
  const [ytdTaxWithholdings, setYtdTaxWithholdings] = useState({
    federalTax: 0,
    stateTax: 0,
    socialSecurity: 0,
    medicare: 0,
    healthInsurance: 0,
    totalWithholdings: 0,
    grossPay: 0
  });
  
  // Add tax filing information state
  const [taxFilingInfo, setTaxFilingInfo] = useState({
    filingStatus: "Single",
    allowances: 1,
    additionalWithholding: 0
  });
  
  const [dailyPayConfig, setDailyPayConfig] = useState({
    enabled: true,
    fee: 5.99,
    maxPercentage: 60
  });
  
  const [dailyPayUsedToday, setDailyPayUsedToday] = useState(false);
  const [dailyPayHistory, setDailyPayHistory] = useState([]);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentError, setPaymentError] = useState(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [userProfile, setUserProfile] = useState(null);
  const [editingHourlyRate, setEditingHourlyRate] = useState(false);
  const [newHourlyRate, setNewHourlyRate] = useState(15);
  const [saveRateSuccess, setSaveRateSuccess] = useState(false);
  const [timeCardData, setTimeCardData] = useState(null);
  const [clockedInStatus, setClockedInStatus] = useState({
    clockedIn: false,
    clockInTime: null
  });
  
  // Previous week functionality
  const [currentWeekOffset, setCurrentWeekOffset] = useState(0); // 0 = current week, -1 = last week, etc.
  const [availableWeeks, setAvailableWeeks] = useState([]);
  const [securedVehicles, setSecuredVehicles] = useState([]);
  const [weeksInitialized, setWeeksInitialized] = useState(false);
  
  // Auth and navigation
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  
  // Get the target user ID from the URL
  const [targetUserId, setTargetUserId] = useState(null);
  const [viewingAsAdmin, setViewingAsAdmin] = useState(false);
  const [accessDenied, setAccessDenied] = useState(false);
  
  // Set up privacy protection
  useEffect(() => {
    // Ensure user is authenticated
    if (!currentUser) {
      navigate('/login');
      return;
    }
    
    // Parse the URL to check if we're trying to view a specific user's data
    try {
      const pathParts = window.location.pathname.split('/');
      const potentialUserId = pathParts[pathParts.length - 1];
      
      // If it's a user ID format and not just 'pay-info'
      if (potentialUserId && potentialUserId !== 'pay-info' && potentialUserId.length > 10) {
        // If viewing someone else's data
        if (potentialUserId !== currentUser.uid) {
          if (isAdmin) {
            // Admin can view other users' data
            setTargetUserId(potentialUserId);
            setViewingAsAdmin(true);
            console.log(`Admin ${currentUser.uid} viewing data for user ${potentialUserId}`);
          } else {
            // Non-admin trying to view others' data - block access
            setAccessDenied(true);
            console.warn(`Unauthorized access attempt by ${currentUser.uid} to view ${potentialUserId}`);
          }
        } else {
          // User viewing their own data via ID
          setTargetUserId(currentUser.uid);
        }
      } else {
        // Normal /pay-info route - show current user's data
        setTargetUserId(currentUser.uid);
      }
    } catch (error) {
      console.error("Error parsing URL:", error);
      // Fallback to current user
      setTargetUserId(currentUser.uid);
    }
  }, [currentUser, isAdmin, navigate]);
  
  // Generate past weeks data for selection
  useEffect(() => {
    const generatePastWeeks = () => {
      const weeks = [];
      const today = new Date();
      
      // Generate data for the past 12 weeks
      for (let i = 0; i < 12; i++) {
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay() - (7 * i));
        startOfWeek.setHours(0, 0, 0, 0); // Start of day
        
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        endOfWeek.setHours(23, 59, 59, 999); // End of day
        
        weeks.push({
          offset: -i,
          startDate: startOfWeek,
          endDate: endOfWeek,
          label: `${formatDate(startOfWeek)} - ${formatDate(endOfWeek)}`
        });
      }
      
      // Set available weeks and indicate they're initialized
      setAvailableWeeks(weeks);
      setWeeksInitialized(true);
    };
    
    generatePastWeeks();
  }, []);
  
  // Wait for weeks to be initialized before loading week data
  useEffect(() => {
    if (weeksInitialized && currentUser && targetUserId && !accessDenied) {
      loadWeekData(currentWeekOffset);
    }
  }, [weeksInitialized, currentUser, targetUserId, accessDenied, currentWeekOffset]);
  
  // Calculate tax withholdings based on gross pay using actual rates
  const calculateTaxes = (grossPay) => {
    // Actual tax rates
    const federalTaxRate = 0.15; // 15% federal income tax (typical for this income level)
    const illinoisTaxRate = 0.0495; // 4.95% Illinois state tax (actual flat rate)
    const socialSecurityRate = 0.062; // 6.2% Social Security (actual rate)
    const medicareRate = 0.0145; // 1.45% Medicare (actual rate)
    const healthInsuranceAmount = 58; // Fixed health insurance premium per week
    
    // Calculate individual tax amounts
    const federalTax = grossPay * federalTaxRate;
    const stateTax = grossPay * illinoisTaxRate;
    const socialSecurity = grossPay * socialSecurityRate;
    const medicare = grossPay * medicareRate;
    
    // Calculate total withholdings
    const totalWithholdings = federalTax + stateTax + socialSecurity + medicare + healthInsuranceAmount;
    
    // Calculate net pay
    const netPay = grossPay - totalWithholdings;
    
    return {
      federalTax,
      stateTax,
      socialSecurity,
      medicare,
      healthInsurance: healthInsuranceAmount,
      totalWithholdings,
      netPay
    };
  };
  
  // Update time every second with a more robust implementation
  useEffect(() => {
    // Initialize with current time
    setCurrentTime(new Date());
    
    // Create interval for updating the time every second
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    
    // Clean up interval on component unmount
    return () => clearInterval(timer);
  }, []);
  
  // Load data on component mount
  useEffect(() => {
    if (currentUser && targetUserId && !accessDenied) {
      loadData();
      loadUserProfile();
      loadTimeCardData();
      loadAvailableWeeksData();
      getCurrentClockStatus();
      loadTaxFilingInfo(); // Load tax filing information
      initializeYearToDateTaxes(); // Initialize YTD tax info
    } else if (!currentUser) {
      navigate('/login');
    }
  }, [currentUser, navigate, targetUserId, accessDenied]);

  // Initialize year-to-date tax withholdings
  const initializeYearToDateTaxes = async () => {
    if (!currentUser || !targetUserId || accessDenied) return;
    
    try {
      const db = getFirestore();
      const taxSummaryRef = doc(db, 'users', targetUserId, 'taxSummary', 'ytd');
      const taxSummaryDoc = await getDoc(taxSummaryRef);
      
      if (taxSummaryDoc.exists()) {
        setYtdTaxWithholdings(taxSummaryDoc.data());
      } else {
        // If no YTD data exists, calculate it from paystubs
        const paystubsQuery = query(
          collection(db, 'users', targetUserId, 'paystubs'),
          where('paymentDate', '>=', new Date(new Date().getFullYear(), 0, 1)),
          orderBy('paymentDate', 'asc')
        );
        
        const paystubsSnapshot = await getDocs(paystubsQuery);
        
        if (!paystubsSnapshot.empty) {
          let totalFederalTax = 0;
          let totalStateTax = 0;
          let totalSocialSecurity = 0;
          let totalMedicare = 0;
          let totalHealthInsurance = 0;
          let totalGrossPay = 0;
          
          paystubsSnapshot.forEach(doc => {
            const paystub = doc.data();
            totalFederalTax += paystub.federalTax || 0;
            totalStateTax += paystub.stateTax || 0;
            totalSocialSecurity += paystub.socialSecurity || 0;
            totalMedicare += paystub.medicare || 0;
            totalHealthInsurance += paystub.healthInsurance || 0;
            totalGrossPay += paystub.grossPay || 0;
          });
          
          const ytdData = {
            federalTax: totalFederalTax,
            stateTax: totalStateTax,
            socialSecurity: totalSocialSecurity,
            medicare: totalMedicare,
            healthInsurance: totalHealthInsurance,
            totalWithholdings: totalFederalTax + totalStateTax + totalSocialSecurity + totalMedicare + totalHealthInsurance,
            grossPay: totalGrossPay,
            lastUpdated: serverTimestamp()
          };
          
          setYtdTaxWithholdings(ytdData);
          await setDoc(taxSummaryRef, ytdData);
        } else {
          // No paystubs for this year, use estimates based on current pay period
          const weeksInYearSoFar = Math.ceil((new Date() - new Date(new Date().getFullYear(), 0, 1)) / (7 * 24 * 60 * 60 * 1000));
          
          const taxDetails = calculateTaxes(currentPay.totalPay);
          const ytdData = {
            federalTax: taxDetails.federalTax * weeksInYearSoFar,
            stateTax: taxDetails.stateTax * weeksInYearSoFar,
            socialSecurity: taxDetails.socialSecurity * weeksInYearSoFar,
            medicare: taxDetails.medicare * weeksInYearSoFar,
            healthInsurance: taxDetails.healthInsurance * weeksInYearSoFar,
            totalWithholdings: taxDetails.totalWithholdings * weeksInYearSoFar,
            grossPay: currentPay.totalPay * weeksInYearSoFar,
            lastUpdated: serverTimestamp()
          };
          
          setYtdTaxWithholdings(ytdData);
          await setDoc(taxSummaryRef, ytdData);
        }
      }
    } catch (error) {
      console.error("Error initializing YTD tax data:", error);
    }
  };

  // Load tax filing information
  const loadTaxFilingInfo = async () => {
    try {
      if (!currentUser || !targetUserId || accessDenied) return;
      
      const db = getFirestore();
      const taxInfoRef = doc(db, 'users', targetUserId, 'taxInfo', 'current');
      const taxInfoDoc = await getDoc(taxInfoRef);
      
      if (taxInfoDoc.exists()) {
        setTaxFilingInfo(taxInfoDoc.data());
      } else {
        // If no tax filing info exists, create a default one
        const defaultTaxInfo = {
          filingStatus: "Single",
          allowances: 1,
          additionalWithholding: 0,
          lastUpdated: serverTimestamp()
        };
        
        await setDoc(taxInfoRef, defaultTaxInfo);
        setTaxFilingInfo(defaultTaxInfo);
      }
    } catch (error) {
      console.error("Error loading tax filing info:", error);
    }
  };
  
  // Save tax withholdings to Firestore
  const saveTaxWithholdingsToFirestore = async (taxDetails, grossPay, startDate, endDate) => {
    try {
      if (!currentUser || !targetUserId || accessDenied) return;
      
      const db = getFirestore();
      
      // Get current week info
      const weekId = `${startDate.toISOString().split('T')[0]}_${endDate.toISOString().split('T')[0]}`;
      const weekTaxRef = doc(db, 'users', targetUserId, 'weeklyTaxes', weekId);
      
      // Prepare data to save
      const taxData = {
        ...taxDetails,
        grossPay,
        startDate,
        endDate,
        lastUpdated: serverTimestamp()
      };
      
      // Save to Firestore
      await setDoc(weekTaxRef, taxData);
      
      console.log(`Successfully saved tax information for week ${weekId}`);
      
    } catch (error) {
      console.error("Error saving tax information:", error);
    }
  };
  
  // Check if user is currently clocked in
  const getCurrentClockStatus = async () => {
    try {
      if (!currentUser || !targetUserId || accessDenied) return;
      
      const db = getFirestore();
      const timeCardRef = doc(db, 'timeCards', targetUserId);
      const timeCardDoc = await getDoc(timeCardRef);
      
      if (timeCardDoc.exists() && timeCardDoc.data().clockedIn && !timeCardDoc.data().clockedOut) {
        setClockedInStatus({
          clockedIn: true,
          clockInTime: timeCardDoc.data().clockInTime?.toDate()
        });
      } else {
        setClockedInStatus({
          clockedIn: false,
          clockInTime: null
        });
      }
    } catch (error) {
      console.error("Error getting clock status:", error);
      setClockedInStatus({ clockedIn: false, clockInTime: null });
    }
  };
  
  // Load available weeks data from Firestore
  const loadAvailableWeeksData = async () => {
    try {
      if (!currentUser || !targetUserId || accessDenied) return;
      
      const db = getFirestore();
      
      // Fetch all paystubs to ensure we have data for each week
      const payStubsQuery = query(
        collection(db, 'users', targetUserId, 'paystubs'),
        orderBy('endDate', 'desc'),
        limit(12)
      );
      
      const payStubsSnapshot = await getDocs(payStubsQuery);
      const fetchedPaystubs = payStubsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        startDate: doc.data().startDate?.toDate(),
        endDate: doc.data().endDate?.toDate(),
        paymentDate: doc.data().paymentDate?.toDate(),
        // Make sure to include tax data in the paystub objects
        federalTax: doc.data().federalTax || 0,
        stateTax: doc.data().stateTax || 0,
        socialSecurity: doc.data().socialSecurity || 0,
        medicare: doc.data().medicare || 0,
        healthInsurance: doc.data().healthInsurance || 0,
        totalWithholdings: doc.data().totalWithholdings || 0
      }));
      
      setPaystubs(fetchedPaystubs);
      
    } catch (error) {
      console.error("Error loading paystubs:", error);
    }
  };
  
  // Load time card data for current week
  const loadTimeCardData = async () => {
    try {
      if (!currentUser || !targetUserId || accessDenied) return;
      
      const db = getFirestore();
      const timeCardSummaryRef = doc(db, 'timeCardSummary', targetUserId);
      const timeCardDoc = await getDoc(timeCardSummaryRef);
      
      if (timeCardDoc.exists()) {
        setTimeCardData(timeCardDoc.data());
      } else {
        // If no summary exists, calculate from actual time card entries
        const today = new Date();
        const startOfDay = new Date(today.setHours(0, 0, 0, 0));
        
        // Get today's hours
        const todayQuery = query(
          collection(db, 'timeCardHistory'),
          where('userId', '==', targetUserId),
          where('timestamp', '>=', startOfDay),
          orderBy('timestamp')
        );
        
        const todaySnapshot = await getDocs(todayQuery);
        let lastClockIn = null;
        let todayHours = 0;
        
        todaySnapshot.forEach(doc => {
          const entry = doc.data();
          if (entry.type === 'clockIn') {
            lastClockIn = entry.timestamp?.toDate();
          } else if (entry.type === 'clockOut' && lastClockIn) {
            const clockOut = entry.timestamp?.toDate();
            if (clockOut) {
              const duration = (clockOut - lastClockIn) / (1000 * 60 * 60); // Convert to hours
              todayHours += duration;
            }
            lastClockIn = null;
          }
        });
        
        // If still clocked in, calculate hours until now
        if (lastClockIn) {
          const now = new Date();
          const duration = (now - lastClockIn) / (1000 * 60 * 60); // Convert to hours
          todayHours += duration;
        }
        
        // Get this week's hours
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday
        startOfWeek.setHours(0, 0, 0, 0);
        
        const weekQuery = query(
          collection(db, 'timeCardHistory'),
          where('userId', '==', targetUserId),
          where('timestamp', '>=', startOfWeek),
          orderBy('timestamp')
        );
        
        const weekSnapshot = await getDocs(weekQuery);
        lastClockIn = null;
        let weeklyHours = 0;
        
        weekSnapshot.forEach(doc => {
          const entry = doc.data();
          if (entry.type === 'clockIn') {
            lastClockIn = entry.timestamp?.toDate();
          } else if (entry.type === 'clockOut' && lastClockIn) {
            const clockOut = entry.timestamp?.toDate();
            if (clockOut) {
              const duration = (clockOut - lastClockIn) / (1000 * 60 * 60); // Convert to hours
              weeklyHours += duration;
            }
            lastClockIn = null;
          }
        });
        
        // If still clocked in, calculate hours until now
        if (lastClockIn) {
          const now = new Date();
          const duration = (now - lastClockIn) / (1000 * 60 * 60); // Convert to hours
          weeklyHours += duration;
        }
        
        setTimeCardData({
          dailyHours: parseFloat(todayHours.toFixed(2)) || 0,
          weeklyHours: parseFloat(weeklyHours.toFixed(2)) || 0
        });
        
        // Update current pay with these hours
        setCurrentPay(prev => ({
          ...prev,
          hoursWorkedToday: parseFloat(todayHours.toFixed(2)) || 0,
          hoursWorkedWeek: parseFloat(weeklyHours.toFixed(2)) || 0
        }));
      }
    } catch (error) {
      console.error("Error loading time card data:", error);
    }
  };
  
  // Load user profile data
  const loadUserProfile = async () => {
    try {
      if (!currentUser || !targetUserId || accessDenied) return;
      
      const db = getFirestore();
      const userProfileDoc = await getDoc(doc(db, 'users', targetUserId, 'profile', 'info'));
      
      if (userProfileDoc.exists()) {
        const profileData = userProfileDoc.data();
        setUserProfile(profileData);
        
        // Set hourly rate from user profile if it exists
        if (profileData.hourlyRate) {
          setCurrentPay(prev => ({
            ...prev,
            hourlyRate: profileData.hourlyRate
          }));
          setNewHourlyRate(profileData.hourlyRate);
        }
      } else {
        // Create a basic profile if none exists
        const basicProfile = {
          displayName: currentUser.displayName || currentUser.email.split('@')[0],
          email: currentUser.email,
          hourlyRate: 15, // Default hourly rate
          createdAt: serverTimestamp()
        };
        
        await setDoc(doc(db, 'users', targetUserId, 'profile', 'info'), basicProfile);
        setUserProfile(basicProfile);
      }
    } catch (error) {
      console.error("Error loading user profile:", error);
    }
  };
  
  // Load weekly data with proper error handling
  const loadWeekData = async (offset) => {
    setLoading(true);
    setCurrentWeekOffset(offset);
    
    if (!currentUser || !targetUserId || accessDenied) {
      setLoading(false);
      return;
    }
    
    try {
      // Make sure we have initialized weeks
      if (availableWeeks.length === 0) {
        console.error("Available weeks not initialized yet");
        setLoading(false);
        return;
      }
      
      // Find the selected week in availableWeeks
      const selectedWeek = availableWeeks.find(week => week.offset === offset);
      if (!selectedWeek) {
        console.error("Selected week not found, availableWeeks length:", availableWeeks.length);
        console.error("Requested offset:", offset);
        console.error("Available offsets:", availableWeeks.map(w => w.offset));
        setLoading(false);
        return;
      }
      
      const db = getFirestore();
      
      // Query for the specific week's data in Firestore
      const weekStartDate = selectedWeek.startDate;
      const weekEndDate = selectedWeek.endDate;
      
      console.log(`Loading data for week: ${formatDate(weekStartDate)} to ${formatDate(weekEndDate)}`);
      
      // STEP 1: Find the paystub for this week if it exists
      let weekPaystub = null;
      for (const paystub of paystubs) {
        // If paystub date range overlaps with selected week
        if (paystub.startDate <= weekEndDate && paystub.endDate >= weekStartDate) {
          weekPaystub = paystub;
          break;
        }
      }
      
      setCurrentPaystub(weekPaystub);
      
      // STEP 2: Fetch the actual time card data for this week from Firestore
      const timeCardQuery = query(
        collection(db, 'timeCardHistory'),
        where('userId', '==', targetUserId),
        where('timestamp', '>=', weekStartDate),
        where('timestamp', '<=', weekEndDate),
        orderBy('timestamp')
      );
      
      const timeCardSnapshot = await getDocs(timeCardQuery);
      
      // Calculate actual hours worked from timecard entries
      let lastClockIn = null;
      let totalHoursWorked = 0;
      
      timeCardSnapshot.forEach(doc => {
        const entry = doc.data();
        if (entry.type === 'clockIn') {
          lastClockIn = entry.timestamp?.toDate();
        } else if (entry.type === 'clockOut' && lastClockIn) {
          const clockOut = entry.timestamp?.toDate();
          if (clockOut) {
            const duration = (clockOut - lastClockIn) / (1000 * 60 * 60); // Convert to hours
            totalHoursWorked += duration;
          }
          lastClockIn = null;
        }
      });
      
      console.log(`Actual hours worked for selected week: ${totalHoursWorked}`);
      
      // STEP 3: Load secured vehicles from Firestore - only user's actual vehicles
      const vehiclesQuery = query(
        collection(db, 'securedVehicles'),
        where('userId', '==', targetUserId),
        where('securedDate', '>=', weekStartDate),
        where('securedDate', '<=', weekEndDate)
      );
      
      const vehiclesSnapshot = await getDocs(vehiclesQuery);
      const actualSecuredVehicles = [];
      
      vehiclesSnapshot.forEach(doc => {
        const vehicle = doc.data();
        actualSecuredVehicles.push({
          id: doc.id,
          date: vehicle.date?.toDate()?.toISOString().split('T')[0] || 'Unknown',
          vehicle: vehicle.make + ' ' + vehicle.model || 'Unknown Vehicle',
          vin: vehicle.vin || 'Unknown',
          status: vehicle.status || 'SECURED',
          securedDate: vehicle.securedDate?.toDate()?.toISOString().split('T')[0] || 'Unknown'
        });
      });
      
      const carCount = actualSecuredVehicles.length;
      console.log(`Found ${carCount} secured vehicles for this week`);
      
      // STEP 4: Fetch actual scans for this week
      const scansQuery = query(
        collection(db, 'scans'),
        where('userId', '==', targetUserId),
        where('timestamp', '>=', weekStartDate),
        where('timestamp', '<=', weekEndDate)
      );
      
      const scansSnapshot = await getDocs(scansQuery);
      const totalScans = scansSnapshot.size;
      
      console.log(`Total scans for selected week: ${totalScans}`);
      
      // STEP 5: Get saved pay period if it exists
      const payPeriodQuery = query(
        collection(db, 'users', targetUserId, 'payPeriods'),
        where('startDate', '>=', weekStartDate),
        where('startDate', '<=', weekEndDate),
        limit(1)
      );
      
      const payPeriodSnapshot = await getDocs(payPeriodQuery);
      let payPeriodData = null;
      
      if (!payPeriodSnapshot.empty) {
        payPeriodData = payPeriodSnapshot.docs[0].data();
      }
      
      // STEP 6: Calculate today's hours - only relevant for current week
      let todayHours = 0;
      
      if (offset === 0) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const todayQuery = query(
          collection(db, 'timeCardHistory'),
          where('userId', '==', targetUserId),
          where('timestamp', '>=', today),
          orderBy('timestamp')
        );
        
        const todaySnapshot = await getDocs(todayQuery);
        lastClockIn = null;
        
        todaySnapshot.forEach(doc => {
          const entry = doc.data();
          if (entry.type === 'clockIn') {
            lastClockIn = entry.timestamp?.toDate();
          } else if (entry.type === 'clockOut' && lastClockIn) {
            const clockOut = entry.timestamp?.toDate();
            if (clockOut) {
              const duration = (clockOut - lastClockIn) / (1000 * 60 * 60); // Convert to hours
              todayHours += duration;
            }
            lastClockIn = null;
          }
        });
        
        // If still clocked in, calculate hours until now
        if (lastClockIn) {
          const now = new Date();
          const duration = (now - lastClockIn) / (1000 * 60 * 60); // Convert to hours
          todayHours += duration;
        }
        
        // Also update clocked in status
        getCurrentClockStatus();
        
        console.log(`Hours worked today: ${todayHours}`);
      }
      
      // STEP 7: Use actual hourly rate from profile, payPeriod, or paystub
      let hourlyRate = 16; // Default rate if nothing is found
      
      if (userProfile && userProfile.hourlyRate) {
        hourlyRate = userProfile.hourlyRate;
      } else if (payPeriodData && payPeriodData.hourlyRate) {
        hourlyRate = payPeriodData.hourlyRate;
      } else if (weekPaystub && weekPaystub.hourlyRate) {
        hourlyRate = weekPaystub.hourlyRate;
      }
      
      // STEP 8: Calculate pay values
      const hoursToUse = totalHoursWorked || (weekPaystub ? weekPaystub.hoursWorked : 0);
      const weeklyPay = hoursToUse * hourlyRate;
      const carBonus = carCount * 20; // $20 per car
      
      // Calculate scan bonus based on actual scans
      let scanBonus = 0;
      if (totalScans >= 70000) {
        scanBonus = 300;
      } else if (totalScans >= 65000) {
        scanBonus = 250;
      }
      
      const grossPay = weeklyPay + carBonus + scanBonus;
      
      // Calculate tax withholdings
      const taxDetails = calculateTaxes(grossPay);
      
      // STEP 9: Update all state variables with actual data
      setCurrentPay({
        hoursWorkedToday: parseFloat(todayHours.toFixed(2)) || 0,
        hoursWorkedWeek: parseFloat(hoursToUse.toFixed(2)) || 0,
        hourlyRate: hourlyRate,
        currentPayPeriodEarnings: weeklyPay,
        bonusPay: carBonus,
        scanBonus: scanBonus,
        totalScans: totalScans,
        carsSecured: carCount,
        totalPay: grossPay,
        startDate: weekStartDate,
        endDate: weekEndDate
      });
      
      setTaxWithholdings(taxDetails);
      setSecuredVehicles(actualSecuredVehicles);
      
      // If we have a paystub, use its tax values
      if (weekPaystub) {
        setTaxWithholdings({
          federalTax: weekPaystub.federalTax || taxDetails.federalTax,
          stateTax: weekPaystub.stateTax || taxDetails.stateTax,
          socialSecurity: weekPaystub.socialSecurity || taxDetails.socialSecurity,
          medicare: weekPaystub.medicare || taxDetails.medicare,
          healthInsurance: weekPaystub.healthInsurance || taxDetails.healthInsurance,
          totalWithholdings: weekPaystub.totalWithholdings || taxDetails.totalWithholdings,
          netPay: weekPaystub.netPay || taxDetails.netPay
        });
      }
      
      console.log("Updated pay data with actual figures from database");
      
    } catch (error) {
      console.error("Error loading week data:", error);
      // On error, set empty state with correct dates
      const selectedWeek = availableWeeks.find(week => week.offset === offset);
      if (selectedWeek) {
        setSecuredVehicles([]);
        setCurrentPay(prev => ({
          ...prev,
          hoursWorkedToday: 0,
          hoursWorkedWeek: 0,
          carsSecured: 0,
          totalScans: 0,
          startDate: selectedWeek.startDate,
          endDate: selectedWeek.endDate
        }));
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Load all necessary data
  const loadData = async () => {
    try {
      if (!currentUser || !targetUserId || accessDenied) return;
      
      setLoading(true);
      const db = getFirestore();
      
      // 1. Load daily pay configuration
      const settingsDoc = await getDoc(doc(db, 'settings', 'dailyPay'));
      if (settingsDoc.exists()) {
        setDailyPayConfig(settingsDoc.data());
      } else {
        // Create default settings if they don't exist
        await setDoc(doc(db, 'settings', 'dailyPay'), {
          enabled: true,
          fee: 5.99,
          maxPercentage: 60
        });
      }
      
      // 2. Check if daily pay has been used today
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const dailyPayQuery = query(
        collection(db, 'users', targetUserId, 'dailyPayments'),
        where('date', '>=', today),
        limit(1)
      );
      
      const dailyPaySnapshot = await getDocs(dailyPayQuery);
      setDailyPayUsedToday(!dailyPaySnapshot.empty);
      
      // 3. Load daily pay history
      const dailyPayHistoryQuery = query(
        collection(db, 'users', targetUserId, 'dailyPayments'),
        orderBy('date', 'desc'),
        limit(5)
      );
      
      const dailyPayHistorySnapshot = await getDocs(dailyPayHistoryQuery);
      const dailyPayHistoryData = dailyPayHistorySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        date: doc.data().date?.toDate()
      }));
      
      setDailyPayHistory(dailyPayHistoryData);
      
      // 4. Load past paystubs
      const paystubsQuery = query(
        collection(db, 'users', targetUserId, 'paystubs'),
        orderBy('endDate', 'desc'),
        limit(12)
      );
      
      const paystubsSnapshot = await getDocs(paystubsQuery);
      const paystubsData = paystubsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        startDate: doc.data().startDate?.toDate(),
        endDate: doc.data().endDate?.toDate(),
        paymentDate: doc.data().paymentDate?.toDate(),
        // Make sure to include tax data from each paystub
        federalTax: doc.data().federalTax || 0,
        stateTax: doc.data().stateTax || 0,
        socialSecurity: doc.data().socialSecurity || 0,
        medicare: doc.data().medicare || 0,
        healthInsurance: doc.data().healthInsurance || 0,
        totalWithholdings: doc.data().totalWithholdings || 0
      }));
      
      setPaystubs(paystubsData);
      
    } catch (error) {
      console.error("Error loading pay information:", error);
    } finally {
      setLoading(false);
    }
  };
  
  // Calculate available daily pay amount
  const calculateDailyPayAmount = () => {
    if (!dailyPayConfig.enabled || dailyPayUsedToday) return 0;
    
    const todayHourlyPay = currentPay.hoursWorkedToday * currentPay.hourlyRate;
    const availableAmount = todayHourlyPay * (dailyPayConfig.maxPercentage / 100);
    
    return Math.max(0, availableAmount);
  };
  
  // Calculate the fee and net amount
  const calculateNetAmount = (amount) => {
    const fee = dailyPayConfig.fee;
    return Math.max(0, amount - fee);
  };
  
  // Handle daily pay withdrawal
  const handleDailyPayWithdrawal = async () => {
    try {
      // Security check: Only allow withdrawals for the current user, not for others
      if (!currentUser || viewingAsAdmin || accessDenied || targetUserId !== currentUser.uid) {
        setPaymentError("You can only withdraw daily pay for your own account");
        return;
      }
      
      setProcessingPayment(true);
      setPaymentError(null);
      
      const db = getFirestore();
      const amount = calculateDailyPayAmount();
      const fee = dailyPayConfig.fee;
      const netAmount = calculateNetAmount(amount);
      
      // Create a record of the daily pay withdrawal
      await addDoc(collection(db, 'users', currentUser.uid, 'dailyPayments'), {
        date: new Date(),
        amount: amount,
        fee: fee,
        netAmount: netAmount,
        status: 'processed'
      });
      
      // Log the transaction for security tracking
      await addDoc(collection(db, 'auditLogs'), {
        action: 'DAILY_PAY_WITHDRAWAL',
        userId: currentUser.uid,
        performedBy: currentUser.uid,
        timestamp: serverTimestamp(),
        details: {
          amount,
          fee,
          netAmount
        }
      });
      
      // Update user's current pay period record
      const userPayDoc = doc(db, 'users', currentUser.uid, 'payPeriods', 'current');
      const userPaySnapshot = await getDoc(userPayDoc);
      
      if (userPaySnapshot.exists()) {
        const userPayData = userPaySnapshot.data();
        await updateDoc(userPayDoc, {
          dailyPayTotal: (userPayData.dailyPayTotal || 0) + amount,
          dailyPayFees: (userPayData.dailyPayFees || 0) + fee,
          lastDailyPayment: new Date()
        });
      } else {
        await setDoc(userPayDoc, {
          dailyPayTotal: amount,
          dailyPayFees: fee,
          lastDailyPayment: new Date()
        });
      }
      
      setDailyPayUsedToday(true);
      setShowConfirmation(false);
      setPaymentSuccess(true);
      setProcessingPayment(false);
      
      // Reload data after a successful payment
      setTimeout(() => {
        loadData();
      }, 2000);
    } catch (error) {
      console.error("Error processing daily pay:", error);
      setProcessingPayment(false);
      setPaymentError("There was an error processing your payment. Please try again.");
    }
  };
  
  // Handle updating hourly rate
  const handleUpdateHourlyRate = async () => {
    // Security check: Only admins can update hourly rate
    if (!isAdmin) {
      setPaymentError("You don't have permission to update hourly rate");
      return;
    }
    
    try {
      setLoading(true);
      
      const db = getFirestore();
      
      // Log the admin action for security auditing
      await addDoc(collection(db, 'auditLogs'), {
        action: 'ADMIN_UPDATE_HOURLY_RATE',
        userId: targetUserId,
        adminId: currentUser.uid,
        timestamp: serverTimestamp(),
        details: {
          oldRate: currentPay.hourlyRate,
          newRate: parseFloat(newHourlyRate)
        }
      });
      
      // Update the user's hourly rate in their profile
      const userProfileRef = doc(db, 'users', targetUserId, 'profile', 'info');
      const userProfileDoc = await getDoc(userProfileRef);
      
      if (userProfileDoc.exists()) {
        await updateDoc(userProfileRef, {
          hourlyRate: parseFloat(newHourlyRate),
          lastRateUpdate: serverTimestamp()
        });
      } else {
        // Create the profile document if it doesn't exist
        await setDoc(userProfileRef, {
          hourlyRate: parseFloat(newHourlyRate),
          displayName: userProfile?.displayName || "User",
          email: userProfile?.email || "<EMAIL>",
          lastRateUpdate: serverTimestamp(),
          createdAt: serverTimestamp()
        });
      }
      
      // Also update in current pay period - check if it exists first
      const currentPayPeriodRef = doc(db, 'users', targetUserId, 'payPeriods', 'current');
      const currentPayDoc = await getDoc(currentPayPeriodRef);
      
      if (currentPayDoc.exists()) {
        // Update the existing document
        await updateDoc(currentPayPeriodRef, {
          hourlyRate: parseFloat(newHourlyRate),
          rateUpdatedAt: serverTimestamp()
        });
      } else {
        // Create a new current pay period document
        await setDoc(currentPayPeriodRef, {
          hourlyRate: parseFloat(newHourlyRate),
          rateUpdatedAt: serverTimestamp(),
          createdAt: serverTimestamp(),
          startDate: currentPay.startDate,
          endDate: currentPay.endDate,
          hoursWorked: currentPay.hoursWorkedWeek,
          grossPay: currentPay.hoursWorkedWeek * parseFloat(newHourlyRate),
          // Add other required fields
          carsSecured: currentPay.carsSecured,
          totalScans: currentPay.totalScans,
          dailyPayTotal: 0,
          dailyPayFees: 0
        });
      }
      
      // Update the local state to reflect the change
      setCurrentPay(prev => {
        const hourlyPay = prev.hoursWorkedWeek * newHourlyRate;
        const totalPay = hourlyPay + prev.bonusPay + prev.scanBonus;
        
        return {
          ...prev,
          hourlyRate: parseFloat(newHourlyRate),
          currentPayPeriodEarnings: hourlyPay,
          totalPay: totalPay
        };
      });
      
      // Update user profile state
      setUserProfile(prev => ({
        ...prev,
        hourlyRate: parseFloat(newHourlyRate)
      }));
      
      // Show success message
      setSaveRateSuccess(true);
      setTimeout(() => {
        setSaveRateSuccess(false);
      }, 3000);
      
      // Exit edit mode
      setEditingHourlyRate(false);
      
      // Reload the current week data to reflect new hourly rate
      loadWeekData(currentWeekOffset);
      
    } catch (error) {
      console.error("Error updating hourly rate:", error);
      alert(`Error updating hourly rate: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // Format currency
  const formatCurrency = (amount) => {
    if (amount === undefined || amount === null) return '$0';
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };
  
  // Format date
  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };
  
  // Enhanced time formatting with better readability
  const formatTime = (date) => {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) return '--:--:--';
    
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };
  
  // Format elapsed time for clock status
  const formatElapsedTime = (startTime) => {
    if (!startTime) return '00:00:00';
    
    const elapsed = new Date() - startTime;
    const hours = Math.floor(elapsed / (1000 * 60 * 60));
    const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  
  // Format percentage
  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(2)}%`;
  };
  
  // Get user display name
  const getUserDisplayName = () => {
    if (userProfile && userProfile.displayName) {
      return userProfile.displayName.toUpperCase();
    } else if (currentUser && currentUser.displayName) {
      return currentUser.displayName.toUpperCase();
    } else if (currentUser && currentUser.email) {
      return currentUser.email.split('@')[0].toUpperCase();
    }
    return "USER";
  };

  // If access is denied, show error message
  if (accessDenied) {
    return (
      <div className="bg-gray-900 min-h-screen text-white flex items-center justify-center">
        <div className="bg-red-900 p-8 rounded-lg shadow-lg max-w-md text-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <h1 className="text-2xl font-bold text-red-300 mb-4">Access Denied</h1>
          <p className="text-gray-300 mb-6">You don't have permission to view this user's pay information.</p>
          <button
            onClick={() => navigate('/pay-info')}
            className="bg-red-700 hover:bg-red-600 text-white px-4 py-2 rounded-md shadow-md"
          >
            View My Pay Information
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-900 min-h-screen text-white">
      {/* Security Banner when viewing someone else's data as admin */}
      {viewingAsAdmin && (
        <div className="bg-yellow-900 text-yellow-100 py-2 px-4 text-center">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-yellow-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>ADMIN VIEW: You are viewing another user's pay information</span>
            </div>
            <button 
              onClick={() => navigate('/pay-info')}
              className="text-sm bg-yellow-800 hover:bg-yellow-700 px-3 py-1 rounded"
            >
              Return to My Pay Info
            </button>
          </div>
        </div>
      )}
      
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 shadow-md border-b border-gray-700 py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">
              Pay Information
            </h1>
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-md shadow-md"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
      
      {/* User Info and Time Banner */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-700 shadow-md mb-6 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div className="flex items-center mb-2 sm:mb-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span className="text-gray-300 text-sm sm:text-base truncate">
                User: {userProfile?.email || currentUser?.email || 'Not logged in'}
                {viewingAsAdmin && (
                  <span className="ml-2 bg-yellow-800 text-yellow-100 px-2 py-0.5 rounded text-xs">
                    Viewing as Admin
                  </span>
                )}
              </span>
            </div>
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="flex items-center">
                <span className="text-gray-300">{formatDate(currentTime)}</span>
                <span className="mx-2 text-gray-500">|</span>
                <span className="text-blue-300 font-mono">{formatTime(currentTime)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Clock Status - Only shown when clocked in */}
      {clockedInStatus.clockedIn && clockedInStatus.clockInTime && currentWeekOffset === 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-4">
          <div className="bg-green-900 bg-opacity-30 border border-green-700 rounded-lg p-3 flex justify-between items-center">
            <div className="flex items-center">
              <div className="relative flex h-3 w-3 mr-2">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-3 w-3 bg-green-500"></span>
              </div>
              <span className="text-green-300 font-medium">Currently Clocked In</span>
            </div>
            <div className="text-right">
              <span className="text-gray-300 text-sm">Since: {formatTime(clockedInStatus.clockInTime)}</span>
              <div className="text-green-300 font-mono">{formatElapsedTime(clockedInStatus.clockInTime)}</div>
            </div>
          </div>
        </div>
      )}
      
      {/* Week Selector */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
        <div className="bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg shadow-lg p-4 border border-blue-700">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <h2 className="text-lg font-semibold text-blue-200">
              {currentWeekOffset === 0 ? 'Current Pay Period' : 'Historical Pay Period'}
            </h2>
            
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => loadWeekData(Math.min(0, currentWeekOffset + 1))}
                disabled={currentWeekOffset === 0}
                className={`p-2 rounded-full ${currentWeekOffset === 0 ? 'bg-gray-700 text-gray-500 cursor-not-allowed' : 'bg-blue-700 text-white hover:bg-blue-600'}`}
                aria-label="Previous week"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              <select
                value={currentWeekOffset}
                onChange={(e) => loadWeekData(parseInt(e.target.value))}
                className="bg-gray-800 border border-gray-700 text-white rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="Select pay period"
              >
                {availableWeeks.map((week) => (
                  <option key={week.offset} value={week.offset}>
                    {week.label} {week.offset === 0 ? '(Current)' : ''}
                  </option>
                ))}
              </select>
              
              <button 
                onClick={() => loadWeekData(Math.max(-11, currentWeekOffset - 1))}
                disabled={currentWeekOffset <= -11}
                className={`p-2 rounded-full ${currentWeekOffset <= -11 ? 'bg-gray-700 text-gray-500 cursor-not-allowed' : 'bg-blue-700 text-white hover:bg-blue-600'}`}
                aria-label="Next week"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
            
            {currentWeekOffset !== 0 && (
              <button
                onClick={() => loadWeekData(0)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-md shadow-md transition-colors"
              >
                Return to Current Week
              </button>
            )}
          </div>
        </div>
      </div>
      
      {/* Admin Controls - Only shown to admins */}
      {isAdmin && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-4">
          <div className="bg-yellow-800 bg-opacity-50 border border-yellow-700 rounded-lg p-4">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-300 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span className="text-yellow-200 font-semibold">ADMIN CONTROLS</span>
              </div>
              
              {editingHourlyRate ? (
                <div className="flex items-center bg-gray-900 border border-yellow-600 rounded-lg p-2">
                  <span className="text-white mr-2">New Hourly Rate:</span>
                  <input 
                    type="number" 
                    value={newHourlyRate}
                    onChange={(e) => setNewHourlyRate(e.target.value)} 
                    className="bg-gray-800 border border-yellow-500 rounded px-3 py-2 text-white text-lg w-24 mr-2"
                    step="0.01"
                    min="0"
                  />
                  <div className="flex space-x-2">
                    <button 
                      onClick={handleUpdateHourlyRate}
                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
                    >
                      Save
                    </button>
                    <button 
                      onClick={() => {
                        setEditingHourlyRate(false);
                        setNewHourlyRate(currentPay.hourlyRate);
                      }}
                      className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <button 
                  onClick={() => setEditingHourlyRate(true)}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg shadow-md flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                  Edit Hourly Rate (Currently: {formatCurrency(currentPay.hourlyRate)}/hr)
                </button>
              )}
            </div>
            
            {saveRateSuccess && (
              <div className="mt-2 text-green-300 text-sm bg-green-900 bg-opacity-50 p-2 rounded-lg">
                ✓ Hourly rate updated successfully to {formatCurrency(currentPay.hourlyRate)}/hr for {userProfile?.email || "user"}!
              </div>
            )}
          </div>
        </div>
      )}
      
{/* Main content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="ml-3 text-blue-300">Loading pay information...</span>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Current Pay Summary */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700">
                  <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4 flex justify-between items-center">
                    <span>
                      {currentWeekOffset === 0 ? 'Current Pay Period' : 'Historical Pay Period'}
                      {currentWeekOffset !== 0 && (
                        <span className="ml-2 text-sm text-gray-400">
                          (Historical Data)
                        </span>
                      )}
                    </span>
                    <span className="text-lg text-gray-300">
                      {currentPaystub ? 'Official Paystub' : 'Estimated Pay'}
                    </span>
                  </h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700">
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Pay Period:</span>
                        <span className="text-white font-medium">
                          {formatDate(currentPay.startDate)} - {formatDate(currentPay.endDate)}
                        </span>
                      </div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Hours Worked (Today):</span>
                        <span className="text-white font-medium">
                          {currentWeekOffset === 0 ? `${currentPay.hoursWorkedToday.toFixed(2)} hrs` : 'N/A'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Hours Worked (Week):</span>
                        <span className="text-white font-medium">{currentPay.hoursWorkedWeek.toFixed(2)} hrs</span>
                      </div>
                    </div>
                    
                    <div className="bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700">
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Weekly Earnings:</span>
                        <span className="text-white font-medium">{formatCurrency(currentPay.currentPayPeriodEarnings)}</span>
                      </div>
                      <div className="flex justify-between mb-2">
                          <span className="text-gray-400">Secured Vehicles:</span>
                          <span className="text-white font-medium">{currentPay.carsSecured} cars</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total Scans:</span>
                        <span className="text-white font-medium">{currentPay.totalScans.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="bg-blue-900 bg-opacity-40 p-4 rounded-lg border border-blue-800">
                      <div className="flex flex-col items-center">
                        <h3 className="text-lg font-semibold text-blue-300">Gross Pay</h3>
                        <p className="text-sm text-gray-400 mt-1">Before taxes and deductions</p>
                        <div className="text-2xl font-bold text-green-400 mt-2">
                          {formatCurrency(currentPay.totalPay)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-green-900 bg-opacity-40 p-4 rounded-lg border border-green-800">
                      <div className="flex flex-col items-center">
                        <h3 className="text-lg font-semibold text-green-300">Net Pay</h3>
                        <p className="text-sm text-gray-400 mt-1">After taxes and deductions</p>
                        <div className="text-2xl font-bold text-green-400 mt-2">
                          {formatCurrency(taxWithholdings.netPay)}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700 mb-6">
                    <h3 className="text-lg font-semibold text-blue-300 mb-3">Pay Breakdown</h3>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Hourly Pay ({formatCurrency(currentPay.hourlyRate)}/hr × {currentPay.hoursWorkedWeek.toFixed(2)} hrs):</span>
                        <span className="text-white">{formatCurrency(currentPay.currentPayPeriodEarnings)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Car Recovery Bonus ($20 × {currentPay.carsSecured} cars):</span>
                        <span className="text-white">{formatCurrency(currentPay.bonusPay)}</span>
                      </div>
                      
                      {/* Scan bonus section - now properly shows total scans */}
                      <div className="flex justify-between">
                        <div className="flex items-center">
                          <span className="text-gray-400">Scan Bonus:</span>
                          <span className="ml-2 text-xs text-gray-500">
                            ({currentPay.totalScans.toLocaleString()} scans)
                          </span>
                        </div>
                        <span className="text-white">{formatCurrency(currentPay.scanBonus)}</span>
                      </div>
                      
                      {/* Scan bonus explanation */}
                      {currentPay.totalScans > 0 && (
                        <div className="text-xs text-gray-500 italic pl-4 border-l-2 border-gray-700">
                          {currentPay.totalScans >= 70000 ? (
                            "70,000+ scans = $300 bonus"
                          ) : currentPay.totalScans >= 65000 ? (
                            "65,000+ scans = $250 bonus"
                          ) : (
                            <>
                              <span className="text-yellow-500">Scan bonus tiers:</span><br />
                              65,000 scans = $250 bonus<br />
                              70,000 scans = $300 bonus
                            </>
                          )}
                        </div>
                      )}
                      
                      <div className="border-t border-gray-700 my-2 pt-2 flex justify-between font-medium">
                        <span className="text-blue-300">Gross Pay:</span>
                        <span className="text-blue-300">{formatCurrency(currentPay.totalPay)}</span>
                      </div>
                      
                      {/* Tax withholdings section */}
                      <div className="bg-gray-800 p-3 rounded-lg my-3">
                        <h4 className="text-md font-medium text-red-300 mb-2">Tax Withholdings & Deductions</h4>
                        
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-400">Federal Income Tax (15%):</span>
                            <span className="text-red-300">- {formatCurrency(taxWithholdings.federalTax)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">Illinois State Tax (4.95%):</span>
                            <span className="text-red-300">- {formatCurrency(taxWithholdings.stateTax)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">Social Security (6.2%):</span>
                            <span className="text-red-300">- {formatCurrency(taxWithholdings.socialSecurity)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">Medicare (1.45%):</span>
                            <span className="text-red-300">- {formatCurrency(taxWithholdings.medicare)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">Health Insurance:</span>
                            <span className="text-red-300">- {formatCurrency(taxWithholdings.healthInsurance)}</span>
                          </div>
                          <div className="border-t border-gray-700 pt-2 flex justify-between font-medium">
                            <span className="text-red-300">Total Withholdings:</span>
                            <span className="text-red-300">- {formatCurrency(taxWithholdings.totalWithholdings)}</span>
                          </div>
                        </div>
                      </div>
                      
                      {/* Only show daily pay deductions if they exist and daily pay is enabled */}
                      {currentWeekOffset === 0 && dailyPayConfig.enabled && dailyPayUsedToday && (
                        <>
                          <div className="flex justify-between text-red-400">
                            <span>Daily Pay Advance:</span>
                            <span>- {formatCurrency(calculateDailyPayAmount())}</span>
                          </div>
                          <div className="flex justify-between text-red-400">
                            <span>Daily Pay Fee:</span>
                            <span>- {formatCurrency(dailyPayConfig.fee)}</span>
                          </div>
                        </>
                      )}
                      
                      <div className="border-t border-gray-700 my-2 pt-2 flex justify-between font-bold">
                        <span className="text-green-400">Net Pay (Estimated Direct Deposit):</span>
                        <span className="text-green-400">
                          {formatCurrency(dailyPayConfig.enabled && dailyPayUsedToday && currentWeekOffset === 0 ? 
                            taxWithholdings.netPay - calculateDailyPayAmount() - dailyPayConfig.fee : 
                            taxWithholdings.netPay)
                          }
                        </span>
                      </div>
                    </div>
                    
                    <p className="mt-4 text-xs text-gray-500">
                      {currentPaystub ? 
                        "This is the official paystub data for this pay period." : 
                        "Note: Actual pay may vary based on final hours worked, bonuses earned, and taxes/deductions. Direct deposit occurs every Friday for the previous week's work."}
                    </p>
                  </div>
                  
                  {/* Tax Breakdown Visualization */}
                  <div className="bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700">
                    <h3 className="text-lg font-semibold text-blue-300 mb-3">Pay Distribution</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-400 mb-2">Your Take-Home Pay</h4>
                        <div className="flex items-center mb-2">
                          <div className="w-full bg-gray-800 rounded-full h-4">
                            <div 
                              className="bg-green-500 h-4 rounded-full" 
                              style={{ width: `${currentPay.totalPay > 0 ? (taxWithholdings.netPay / currentPay.totalPay) * 100 : 0}%` }}
                            ></div>
                          </div>
                          <span className="ml-2 text-green-400 font-medium">
                            {currentPay.totalPay > 0 ? ((taxWithholdings.netPay / currentPay.totalPay) * 100).toFixed(1) : 0}%
                          </span>
                        </div>
                        
                        <h4 className="text-sm font-medium text-gray-400 mb-2 mt-4">Tax & Deductions</h4>
                        <div className="flex items-center">
                          <div className="w-full bg-gray-800 rounded-full h-4">
                            <div 
                              className="bg-red-500 h-4 rounded-full" 
                              style={{ width: `${currentPay.totalPay > 0 ? (taxWithholdings.totalWithholdings / currentPay.totalPay) * 100 : 0}%` }}
                            ></div>
                          </div>
                          <span className="ml-2 text-red-400 font-medium">
                            {currentPay.totalPay > 0 ? ((taxWithholdings.totalWithholdings / currentPay.totalPay) * 100).toFixed(1) : 0}%
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-gray-400 mb-2">Tax Breakdown</h4>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span className="text-gray-400">Federal Tax:</span>
                            <span className="text-white">{currentPay.totalPay > 0 ? formatPercentage(taxWithholdings.federalTax / currentPay.totalPay) : '0%'}</span>
                          </div>
                          <div className="flex justify-between text-xs">
                            <span className="text-gray-400">State Tax:</span>
                            <span className="text-white">{currentPay.totalPay > 0 ? formatPercentage(taxWithholdings.stateTax / currentPay.totalPay) : '0%'}</span>
                          </div>
                          <div className="flex justify-between text-xs">
                            <span className="text-gray-400">Social Security:</span>
                            <span className="text-white">{currentPay.totalPay > 0 ? formatPercentage(taxWithholdings.socialSecurity / currentPay.totalPay) : '0%'}</span>
                          </div>
                          <div className="flex justify-between text-xs">
                            <span className="text-gray-400">Medicare:</span>
                            <span className="text-white">{currentPay.totalPay > 0 ? formatPercentage(taxWithholdings.medicare / currentPay.totalPay) : '0%'}</span>
                          </div>
                          <div className="flex justify-between text-xs">
                            <span className="text-gray-400">Health Insurance:</span>
                            <span className="text-white">{currentPay.totalPay > 0 ? formatPercentage(taxWithholdings.healthInsurance / currentPay.totalPay) : '0%'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-4 bg-gray-800 p-3 rounded-lg text-sm">
                      <h4 className="text-gray-300 font-medium mb-1">Tax Information</h4>
                      <p className="text-xs text-gray-400">
                        Federal tax rates are based on your income bracket. The amounts shown are estimates. Illinois has a flat income tax rate of 4.95%. Social Security tax is 6.2% and Medicare tax is 1.45% of your gross pay.
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Secured Vehicles List */}
                <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700 mt-6">
                  <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4 flex justify-between items-center">
                    <span>
                      Secured Vehicles ({securedVehicles.length})
                      {currentWeekOffset !== 0 && (
                        <span className="ml-2 text-sm text-gray-400">
                          {formatDate(currentPay.startDate)} - {formatDate(currentPay.endDate)}
                        </span>
                      )}
                    </span>
                    <span className="text-sm text-gray-400">
                      {securedVehicles.length === 0 ? 'No vehicles secured' : `$${securedVehicles.length * 20} bonus`}
                    </span>
                  </h2>
                  
                  <div className="overflow-x-auto bg-gray-900 rounded-lg">
                    <table className="min-w-full divide-y divide-gray-800 text-sm">
                      <thead className="bg-gray-800">
                        <tr>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase">Date</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase">Vehicle</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase">VIN</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase">Secured Date</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-800">
                        {securedVehicles.length === 0 ? (
                          <tr>
                            <td colSpan="4" className="px-4 py-3 text-center text-gray-400">
                              {currentWeekOffset === 0 
                                ? "No secured vehicles for this week." 
                                : "No secured vehicles found for this time period."}
                            </td>
                          </tr>
                        ) : (
                          securedVehicles.map((vehicle, index) => (
                            <tr key={index} className="hover:bg-gray-800">
                              <td className="px-4 py-2 whitespace-nowrap text-gray-300">{vehicle.date}</td>
                              <td className="px-4 py-2 whitespace-nowrap text-gray-300">{vehicle.vehicle}</td>
                              <td className="px-4 py-2 whitespace-nowrap text-red-400 font-medium">{vehicle.vin}</td>
                              <td className="px-4 py-2 whitespace-nowrap text-green-400">{vehicle.securedDate}</td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
                
                {/* Daily Pay History - Only show if daily pay is enabled and it's current week */}
                {currentWeekOffset === 0 && dailyPayConfig.enabled && dailyPayHistory.length > 0 && (
                  <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700 mt-6">
                    <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">
                      Daily Pay History
                    </h2>
                    
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-700">
                        <thead className="bg-gray-800">
                          <tr>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                              Date
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                              Amount
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                              Fee
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                              Net Amount
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                              Status
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-gray-900 divide-y divide-gray-800">
                          {dailyPayHistory.map((payment) => (
                            <tr key={payment.id}>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                                {formatDate(payment.date)}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                                {formatCurrency(payment.amount)}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-red-400">
                                {formatCurrency(payment.fee)}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-green-400 font-medium">
                                {formatCurrency(payment.netAmount)}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm">
                                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-900 text-green-300">
                                  {payment.status}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
              
              <div>
                {/* Only show Daily Pay section for current week and if viewing own data (not admin view) */}
                {currentWeekOffset === 0 && dailyPayConfig.enabled && !viewingAsAdmin && (
                  <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700 mb-6">
                    <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">
                      Daily Pay
                    </h2>
                    
                    {!dailyPayConfig.enabled ? (
                      <div className="bg-red-900 bg-opacity-50 border border-red-800 rounded-lg p-4 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto text-red-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 className="text-lg font-semibold text-red-300 mb-1">Daily Pay Disabled</h3>
                        <p className="text-sm text-gray-300">
                          The daily pay feature is currently disabled by the administrator.
                        </p>
                      </div>
                    ) : dailyPayUsedToday ? (
                      <div className="bg-yellow-900 bg-opacity-50 border border-yellow-800 rounded-lg p-4 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto text-yellow-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 className="text-lg font-semibold text-yellow-300 mb-1">Daily Pay Used</h3>
                        <p className="text-sm text-gray-300">
                          You've already used your daily pay for today. The next available withdrawal will be tomorrow.
                        </p>
                      </div>
                    ) : calculateDailyPayAmount() <= 0 ? (
                      <div className="bg-yellow-900 bg-opacity-50 border border-yellow-800 rounded-lg p-4 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto text-yellow-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 className="text-lg font-semibold text-yellow-300 mb-1">No Pay Available</h3>
                        <p className="text-sm text-gray-300">
                          You haven't logged any hours today yet. Daily pay is available after you've worked hours today.
                        </p>
                      </div>
                    ) : (
                      <div className="bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700">
                        <h3 className="text-lg font-semibold text-blue-300 mb-3">Available Daily Pay</h3>
                        
                        <div className="space-y-3 mb-4">
                          <div className="flex justify-between">
                            <span className="text-gray-400">Hours Worked Today:</span>
                            <span className="text-white font-medium">{currentPay.hoursWorkedToday.toFixed(2)} hrs</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">Today's Pay:</span>
                            <span className="text-white font-medium">
                              {formatCurrency(currentPay.hoursWorkedToday * currentPay.hourlyRate)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">Available Amount ({dailyPayConfig.maxPercentage}%):</span>
                            <span className="text-green-400 font-medium">
                              {formatCurrency(calculateDailyPayAmount())}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">Daily Pay Fee:</span>
                            <span className="text-red-400 font-medium">
                              - {formatCurrency(dailyPayConfig.fee)}
                            </span>
                          </div>
                          <div className="border-t border-gray-700 pt-2 flex justify-between">
                            <span className="text-gray-400 font-semibold">You'll Receive:</span>
                            <span className="text-green-400 font-bold">
                              {formatCurrency(calculateNetAmount(calculateDailyPayAmount()))}
                            </span>
                          </div>
                        </div>
                        
                        <button
                          onClick={() => setShowConfirmation(true)}
                          className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white py-3 rounded-lg shadow-md font-medium transition-all duration-200 transform hover:scale-105"
                        >
                          Get Paid Today
                        </button>
                        
                        <p className="mt-4 text-xs text-gray-500">
                          Daily Pay allows you to access {dailyPayConfig.maxPercentage}% of your daily earnings before payday for a small fee of {formatCurrency(dailyPayConfig.fee)}. The amount will be deducted from your next direct deposit.
                        </p>
                      </div>
                    )}
                    
                    {paymentSuccess && (
                      <div className="mt-4 bg-green-900 bg-opacity-50 border border-green-800 rounded-lg p-4 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto text-green-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <h3 className="text-lg font-semibold text-green-300 mb-1">Payment Successful!</h3>
                        <p className="text-sm text-gray-300">
                          Your daily pay has been processed and will be sent to your account shortly.
                        </p>
                      </div>
                    )}
                    
                    {paymentError && (
                      <div className="mt-4 bg-red-900 bg-opacity-50 border border-red-800 rounded-lg p-4 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto text-red-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 className="text-lg font-semibold text-red-300 mb-1">Payment Error</h3>
                        <p className="text-sm text-gray-300">{paymentError}</p>
                      </div>
                    )}
                  </div>
                )}
                
                {/* Current/Selected Week's Paystub */}
                {currentPaystub && (
                  <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700 mb-6">
                    <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4 flex justify-between items-center">
                      <span>Official Paystub</span>
                      <span className="text-sm text-gray-400">
                        Paid on {formatDate(currentPaystub.paymentDate)}
                      </span>
                    </h2>
                    
                    <div className="bg-gray-900 bg-opacity-70 p-4 rounded-lg border border-gray-700">
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Pay Period:</span>
                        <span className="text-white font-medium">
                          {formatDate(currentPaystub.startDate)} - {formatDate(currentPaystub.endDate)}
                        </span>
                      </div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Gross Pay:</span>
                        <span className="text-white font-medium">{formatCurrency(currentPaystub.grossPay)}</span>
                      </div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Net Pay:</span>
                        <span className="text-green-400 font-bold">{formatCurrency(currentPaystub.netPay)}</span>
                      </div>
                    </div>
                    
                    <div className="mt-4 bg-gray-900 bg-opacity-70 p-4 rounded-lg border border-gray-700">
                      <h3 className="text-sm font-semibold text-blue-300 mb-2">Paystub Details</h3>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Hours Worked:</span>
                          <span className="text-white">{currentPaystub.hoursWorked?.toFixed(2) || '0.00'} hrs</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Hourly Rate:</span>
                          <span className="text-white">{formatCurrency(currentPaystub.hourlyRate)}/hr</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Hourly Pay:</span>
                          <span className="text-white">{formatCurrency(currentPaystub.hourlyPay)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Bonuses:</span>
                          <span className="text-white">{formatCurrency(currentPaystub.bonusPay)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Total Deductions:</span>
                          <span className="text-red-400">{formatCurrency(currentPaystub.totalWithholdings)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-4 text-center">
                      <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
                        View Full Paystub
                      </button>
                    </div>
                  </div>
                )}
                
                {/* Tax Summary Section */}
                <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700 mb-6">
                  <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">
                    Tax Summary
                  </h2>
                  
                  <div className="bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700">
                    <h3 className="text-md font-semibold text-blue-300 mb-3">Current Tax Year</h3>
                    
                    <div className="space-y-3">
                      <div className="bg-gray-800 rounded-lg p-3">
                        <h4 className="text-sm font-medium text-gray-300 mb-2">Year-to-Date Withholdings</h4>
                        
                        <div className="grid grid-cols-2 gap-2">
                          <div className="bg-gray-900 rounded p-2">
                            <div className="text-xs text-gray-400">Federal Tax</div>
                            <div className="text-white font-medium">{formatCurrency(ytdTaxWithholdings.federalTax)}</div>
                          </div>
                          <div className="bg-gray-900 rounded p-2">
                            <div className="text-xs text-gray-400">Illinois Tax</div>
                            <div className="text-white font-medium">{formatCurrency(ytdTaxWithholdings.stateTax)}</div>
                          </div>
                          <div className="bg-gray-900 rounded p-2">
                            <div className="text-xs text-gray-400">Social Security</div>
                            <div className="text-white font-medium">{formatCurrency(ytdTaxWithholdings.socialSecurity)}</div>
                          </div>
                          <div className="bg-gray-900 rounded p-2">
                            <div className="text-xs text-gray-400">Medicare</div>
                            <div className="text-white font-medium">{formatCurrency(ytdTaxWithholdings.medicare)}</div>
                          </div>
                        </div>

                        <div className="mt-2 pt-2 border-t border-gray-700">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">YTD Gross Pay:</span>
                            <span className="text-white font-medium">{formatCurrency(ytdTaxWithholdings.grossPay)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">YTD Total Withholdings:</span>
                            <span className="text-red-400 font-medium">{formatCurrency(ytdTaxWithholdings.totalWithholdings)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">YTD Net Pay:</span>
                            <span className="text-green-400 font-medium">{formatCurrency(ytdTaxWithholdings.grossPay - ytdTaxWithholdings.totalWithholdings)}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-blue-900 bg-opacity-30 rounded-lg p-3">
                        <h4 className="text-sm font-medium text-blue-300 mb-2">Tax Filing Information</h4>
                        <div className="text-xs text-gray-300 space-y-1">
                          <div className="flex justify-between">
                            <span>Filing Status:</span>
                            <span className="font-medium">{taxFilingInfo.filingStatus}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Allowances:</span>
                            <span className="font-medium">{taxFilingInfo.allowances}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Additional Withholding:</span>
                            <span className="font-medium">{formatCurrency(taxFilingInfo.additionalWithholding || 0)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-4 bg-gray-800 p-3 rounded-lg text-xs text-gray-400">
                      <p>Your tax withholdings are calculated based on your W-4 information. To make changes to your withholdings, please contact HR or update your W-4 form.</p>
                    </div>
                  </div>
                </div>
                
                {/* Paystubs History - Always show regardless of daily pay setting */}
                <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700">
                  <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4 flex justify-between items-center">
                    <span>Previous Paystubs</span>
                    <span className="text-sm text-blue-400 cursor-pointer hover:underline">
                      View All
                    </span>
                  </h2>
                  
                  {paystubs.length === 0 ? (
                    <div className="bg-gray-900 bg-opacity-60 p-6 rounded-lg border border-gray-700 text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mx-auto text-gray-500 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <p className="text-gray-400">No previous paystubs found.</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {paystubs.map((paystub) => (
                        <div 
                          key={paystub.id}
                          onClick={() => {
                            // Find the week that matches this paystub's date range
                            const matchingWeek = availableWeeks.find(week => 
                              (week.startDate <= paystub.endDate && week.endDate >= paystub.startDate)
                            );
                            
                            if (matchingWeek) {
                              loadWeekData(matchingWeek.offset);
                            }
                          }}
                          className={`bg-gray-900 bg-opacity-60 p-4 rounded-lg border ${
                            currentPaystub && currentPaystub.id === paystub.id 
                              ? 'border-blue-500 ring-2 ring-blue-500' 
                              : 'border-gray-700 hover:border-blue-700'
                          } transition-colors duration-200 cursor-pointer`}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <h3 className="text-white font-medium">
                                {formatDate(paystub.startDate)} - {formatDate(paystub.endDate)}
                              </h3>
                              <p className="text-xs text-gray-400 mt-1">
                                Paid on {formatDate(paystub.paymentDate)}
                              </p>
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold text-green-400">
                                {formatCurrency(paystub.netPay)}
                              </div>
                              <span className="text-xs text-gray-400">
                                {currentPaystub && currentPaystub.id === paystub.id 
                                  ? 'Currently Viewing' 
                                  : 'Click to View'
                                }
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {paystubs.length > 0 && paystubs.length < 12 && (
                    <div className="mt-4">
                      <button className="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 rounded-lg border border-gray-600">
                        Load More Paystubs
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Live time display in fixed position */}
      <div className="fixed bottom-4 right-4 bg-gray-800 rounded-full px-4 py-2 border border-blue-600 shadow-lg">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-sm font-mono text-blue-300">{formatTime(currentTime)}</span>
        </div>
      </div>
      
      {/* Daily Pay Confirmation Modal - Only show if daily pay is enabled */}
      {dailyPayConfig.enabled && showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-md w-full border border-gray-700">
            <div className="p-6">
              <h3 className="text-xl font-bold text-white mb-4">Confirm Daily Pay</h3>
              
              <div className="bg-gray-900 p-4 rounded-lg mb-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Amount:</span>
                    <span className="text-white font-medium">{formatCurrency(calculateDailyPayAmount())}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Fee:</span>
                    <span className="text-red-400">- {formatCurrency(dailyPayConfig.fee)}</span>
                  </div>
                  <div className="border-t border-gray-700 pt-2 mt-2 flex justify-between">
                    <span className="text-gray-400 font-semibold">You'll Receive:</span>
                    <span className="text-green-400 font-bold">
                      {formatCurrency(calculateNetAmount(calculateDailyPayAmount()))}
                    </span>
                  </div>
                </div>
              </div>
              
              <p className="text-sm text-gray-300 mb-6">
                This amount will be deducted from your next direct deposit. You can only use Daily Pay once per day.
              </p>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowConfirmation(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors"
                  disabled={processingPayment}
                >
                  Cancel
                </button>
                <button
                  onClick={handleDailyPayWithdrawal}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors flex items-center"
                  disabled={processingPayment}
                >
                  {processingPayment ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : "Confirm Withdrawal"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PayInfo;