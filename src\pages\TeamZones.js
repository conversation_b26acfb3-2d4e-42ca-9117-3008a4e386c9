import React, { useState, useEffect, useRef, useCallback } from 'react';
import { getFirestore, collection, getDocs, addDoc, deleteDoc, doc, updateDoc, query, where } from 'firebase/firestore';

// Enhanced TeamZones component with user ZIP code assignments - admin only access
function TeamZones({ 
  teamId, 
  mapRef, 
  visible = true,
  onClose,
  geoJsonPaths = {
    il: '/data/il-zip-codes.geojson', // Illinois ZIP code boundaries
    in: '/data/in-zip-codes.geojson', // Indiana ZIP code boundaries
    wi: '/data/wi-zip-codes.geojson'  // Wisconsin ZIP code boundaries
  },
  alwaysShowZones = true, // Always keep zones visible on map regardless of UI state
  userDisplayNames = {}, // Map of userIds to display names
  userProfilePictures = {}, // Map of userIds to profile pictures
  teamMembers = [], // Array of team member user IDs
  isAdmin = false, // Whether current user is an admin
  currentUser = null, // Current user ID
  defaultOpacity = 0.3 // Default opacity for zones
}) {
  const db = getFirestore();
  
  // State management for multiple states
  const [activeStates, setActiveStates] = useState(['il', 'in', 'wi']); // All states active by default
  const [stateGeoJsonData, setStateGeoJsonData] = useState({}); // Store data per state
  const [combinedZipCodeData, setCombinedZipCodeData] = useState(null); // Combined data
  
  const [zipCodeData, setZipCodeData] = useState(null);
  const [zipCodeBoundaries, setZipCodeBoundaries] = useState({});
  const [showAllZipCodes, setShowAllZipCodes] = useState(true); // Changed to TRUE by default
  const [showOnlyAssigned, setShowOnlyAssigned] = useState(true); // Default to showing only assigned ZIP codes
  const [selectedZipCodes, setSelectedZipCodes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [debugMessage, setDebugMessage] = useState(null);
  const [customGeoJsonPath, setCustomGeoJsonPath] = useState(null);
  
  // New state for user assignments
  const [userZipAssignments, setUserZipAssignments] = useState({});
  const [selectedUser, setSelectedUser] = useState(null);
  const [showAssignmentMode, setShowAssignmentMode] = useState(false);
  const [assignmentHistory, setAssignmentHistory] = useState([]);
  const [zipCodeStats, setZipCodeStats] = useState({});
  
  // NEW: Flag to track if admin panel is open
  const [adminPanelOpen, setAdminPanelOpen] = useState(false);
  
  // GeoJSON loading tracking refs
  const allZipCodesLayerRef = useRef(null);
  const zipCodesLoaded = useRef(false);
  const maxLoadAttempts = useRef(0);
  const geoJsonLoadInProgress = useRef(false);
  const componentMountedRef = useRef(true);
  const displayTriggeredRef = useRef(false);
  
  // References to functions to avoid circular dependencies
  const displayAllZipCodesRef = useRef(null);
  const forceShowAllBoundariesRef = useRef(null);
  
  // User color mapping
  const userColorsRef = useRef({});
  
  // NEW: Track when zones were last displayed
  const lastDisplayTime = useRef(0);
  
  // NEW: Track if we're in an emergency refresh mode
  const emergencyRefreshMode = useRef(false);
  
  // Permissions check - prevent non-admins from accessing admin features
  const canAssignZones = isAdmin;
  
  // NEW: Effect to track admin panel open state
  useEffect(() => {
    // When this component is visible, set adminPanelOpen to true if admin
    if (visible && isAdmin) {
      setAdminPanelOpen(true);
      
      // Force display zones when admin panel opens
      if (zipCodeData && mapRef.current) {
        console.log("Admin panel opened - forcing display of all zones");
        // Set emergency mode for more persistent display
        emergencyRefreshMode.current = true;
        
        // Force display after a short delay to ensure state is updated
        setTimeout(() => {
          if (displayAllZipCodesRef.current) {
            displayAllZipCodesRef.current();
          }
        }, 200);
      }
    } else if (!visible && isAdmin) {
      setAdminPanelOpen(false);
      emergencyRefreshMode.current = false;
    }
    
    return () => {
      if (isAdmin) {
        setAdminPanelOpen(false);
        emergencyRefreshMode.current = false;
      }
    };
  }, [visible, isAdmin, zipCodeData, mapRef]);
  
  // Multi-state path helper functions
  const getGeoJsonPaths = useCallback(() => {
    const paths = [];
    activeStates.forEach(state => {
      if (geoJsonPaths[state]) {
        paths.push({ state, path: geoJsonPaths[state] });
      }
    });
    return paths;
  }, [activeStates, geoJsonPaths]);
  
  // Helper function to get GeoJSON path with fallbacks
  const getGeoJsonPath = useCallback(() => {
    // First try the window global path if available
    if (typeof window !== 'undefined' && window.zipCodeGeoJsonPath) {
      console.log(`Using global zipCodeGeoJsonPath: ${window.zipCodeGeoJsonPath}`);
      return window.zipCodeGeoJsonPath;
    }
    
    // Then try custom path from component state
    if (customGeoJsonPath) {
      console.log(`Using customGeoJsonPath: ${customGeoJsonPath}`);
      return customGeoJsonPath;
    }
    
    // Get first active state path as fallback
    const paths = getGeoJsonPaths();
    if (paths.length > 0) {
      const path = paths[0].path;
      console.log(`Using first active state path: ${path}`);
      return path;
    }
    
    // Fallback to Illinois if nothing else
    console.log(`Using fallback Illinois path: ${geoJsonPaths.il}`);
    return geoJsonPaths.il;
  }, [customGeoJsonPath, getGeoJsonPaths, geoJsonPaths.il]);
  
  // Update componentMountedRef when component unmounts
  useEffect(() => {
    componentMountedRef.current = true;
    return () => {
      componentMountedRef.current = false;
    };
  }, []);
  
  // Helper to extract polygon points from GeoJSON feature
  const extractPolygonPointsFromGeoJson = useCallback((feature) => {
    if (!feature || !feature.geometry) return null;
    
    try {
      let coordinates = [];
      
      if (feature.geometry.type === 'Polygon') {
        // Use the first (outer) ring of the polygon
        coordinates = feature.geometry.coordinates[0];
      } else if (feature.geometry.type === 'MultiPolygon') {
        // Use the largest polygon (by number of points)
        let largestPolygon = [];
        feature.geometry.coordinates.forEach(polygon => {
          if (polygon[0].length > largestPolygon.length) {
            largestPolygon = polygon[0];
          }
        });
        coordinates = largestPolygon;
      } else {
        console.warn("Unsupported geometry type:", feature.geometry.type);
        return null;
      }
      
      // Convert to our format
      const polygonPoints = coordinates.map(coord => ({
        lat: coord[1], // Y is latitude
        lng: coord[0]  // X is longitude
      }));
      
      // Calculate bounds
      let minLat = Infinity, maxLat = -Infinity;
      let minLng = Infinity, maxLng = -Infinity;
      
      polygonPoints.forEach(point => {
        minLat = Math.min(minLat, point.lat);
        maxLat = Math.max(maxLat, point.lat);
        minLng = Math.min(minLng, point.lng);
        maxLng = Math.max(maxLng, point.lng);
      });
      
      return {
        polygonPoints,
        bounds: {
          southWest: { lat: minLat, lng: minLng },
          northEast: { lat: maxLat, lng: maxLng }
        },
        center: {
          lat: (minLat + maxLat) / 2,
          lng: (minLng + maxLng) / 2
        }
      };
    } catch (error) {
      console.error("Error extracting polygon points:", error);
      return null;
    }
  }, []);
  
  // Generate a consistent color for each user
  const getUserColor = useCallback((userId) => {
    if (!userId) return { bg: '#3b82f6', text: '#ffffff' }; // Default blue
    
    // If we already assigned a color to this user, return it
    if (userColorsRef.current[userId]) {
      return userColorsRef.current[userId];
    }
    
    // List of visually distinct colors for assignments
    const colorOptions = [
      { bg: '#ef4444', text: '#ffffff' }, // red-500
      { bg: '#f97316', text: '#ffffff' }, // orange-500
      { bg: '#f59e0b', text: '#ffffff' }, // amber-500
      { bg: '#10b981', text: '#ffffff' }, // emerald-500
      { bg: '#06b6d4', text: '#ffffff' }, // cyan-500
      { bg: '#6366f1', text: '#ffffff' }, // indigo-500
      { bg: '#8b5cf6', text: '#ffffff' }, // violet-500
      { bg: '#ec4899', text: '#ffffff' }, // pink-500
      { bg: '#14b8a6', text: '#ffffff' }, // teal-500
      { bg: '#84cc16', text: '#ffffff' }, // lime-500
      { bg: '#22c55e', text: '#ffffff' }, // green-500
      { bg: '#3b82f6', text: '#ffffff' }, // blue-500
      { bg: '#a855f7', text: '#ffffff' }, // purple-500
      { bg: '#f43f5e', text: '#ffffff' }, // rose-500
    ];
    
    // Deterministically choose a color based on user ID
    const userIndex = userId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colorOptions.length;
    const color = colorOptions[userIndex];
    
    // Store for future reference
    userColorsRef.current[userId] = color;
    return color;
  }, []);
  
  // Find which user is assigned to a ZIP code
  const findZipCodeAssignment = useCallback((zipCode) => {
    if (!zipCode) return null;
    
    for (const [userId, assignments] of Object.entries(userZipAssignments)) {
      for (const assignment of assignments) {
        if (assignment.zipCode === zipCode) {
          return {
            userId,
            assignment
          };
        }
      }
    }
    
    return null;
  }, [userZipAssignments]);
  
  // Remove all zip code boundaries from the map
  const removeAllZipCodes = useCallback(() => {
    if (allZipCodesLayerRef.current && mapRef.current) {
      try {
        mapRef.current.removeLayer(allZipCodesLayerRef.current);
      } catch (error) {
        // Layer might have already been removed
        console.log("Layer may have already been removed:", error.message);
      }
      allZipCodesLayerRef.current = null;
    }
  }, [mapRef]);
  
  // Get all assigned ZIP codes
  const getAllAssignedZipCodes = useCallback(() => {
    const assigned = [];
    
    for (const assignments of Object.values(userZipAssignments)) {
      for (const assignment of assignments) {
        assigned.push(assignment.zipCode);
      }
    }
    
    return assigned;
  }, [userZipAssignments]);
  
  // Get a list of all ZIP codes assigned to a specific user
  const getUserZipCodes = useCallback((userId) => {
    if (!userId || !userZipAssignments[userId]) return [];
    return userZipAssignments[userId].map(a => a.zipCode);
  }, [userZipAssignments]);
  
  // Get ZIP codes assigned to current user
  const getCurrentUserZipCodes = useCallback(() => {
    return getUserZipCodes(currentUser);
  }, [getUserZipCodes, currentUser]);
  
  // NEW: Get all ZIP codes that should be visible to current user
  const getVisibleZipCodes = useCallback(() => {
    // Admins with panel open see all ZIP codes regardless of assignment
    if (isAdmin && adminPanelOpen) {
      return null; // null means show all
    }
    
    // If showing only assigned, get user's assignments plus selected codes
    if (showOnlyAssigned && !isAdmin) {
      const userCodes = getCurrentUserZipCodes();
      return [...new Set([...userCodes, ...selectedZipCodes])];
    }
    
    // Get all assigned codes plus selected for regular display
    return [...new Set([...getAllAssignedZipCodes(), ...selectedZipCodes])];
  }, [isAdmin, adminPanelOpen, showOnlyAssigned, getCurrentUserZipCodes, getAllAssignedZipCodes, selectedZipCodes]);
  
  // Display zip codes - either all or only assigned depending on mode
  const displayAllZipCodes = useCallback(() => {
    // NEW: Throttle displays to prevent excessive calls
    const now = Date.now();
    if (now - lastDisplayTime.current < 300 && !emergencyRefreshMode.current) {
      // Skip if we just displayed recently and not in emergency mode
      return;
    }
    lastDisplayTime.current = now;
    
    if (!mapRef.current || !zipCodeData) {
      console.log("Cannot display zip codes: Map or GeoJSON data not ready yet");
      // Schedule a retry after a short delay
      if (componentMountedRef.current) {
        setTimeout(() => {
          if (mapRef.current && zipCodeData) {
            console.log("Retrying displayAllZipCodes after short delay");
            displayAllZipCodesRef.current();
          }
        }, 1000);
      }
      return;
    }
    
    try {
      // Set display flag to avoid multiple simultaneous refreshes
      if (displayTriggeredRef.current && !emergencyRefreshMode.current) {
        console.log("Display already in progress, skipping");
        return;
      }
      displayTriggeredRef.current = true;
      
      // Remove existing layer if present
      removeAllZipCodes();
      
      // Create a new layer group with a unique name to help prevent accidental removal
      const layerGroupName = `zip-boundaries-${teamId || 'default'}-${Date.now()}`;
      allZipCodesLayerRef.current = window.L.layerGroup([], { className: layerGroupName }).addTo(mapRef.current);
      
      // Get current map view parameters
      const currentZoom = mapRef.current.getZoom();
      const currentBounds = mapRef.current.getBounds();
      console.log(`Current zoom level: ${currentZoom}`);
      
      // Get ZIP codes that should be visible for filtering
      const visibleZipCodes = getVisibleZipCodes();
      console.log(`Visibility mode: ${visibleZipCodes === null ? 'ALL' : `${visibleZipCodes.length} specific codes`}`);
      
      // Count for tracking
      let visibleFeatures = 0;
      let skipCount = 0;
      let boundsCheckSkipped = 0;
      
      // Process in batches for better performance
      const BATCH_SIZE = 50;
      const features = zipCodeData.features;
      let currentIndex = 0;
      
      const processBatch = () => {
        if (!componentMountedRef.current || currentIndex >= features.length) {
          displayTriggeredRef.current = false;
          console.log(`Completed displaying ${visibleFeatures} zip codes`);
          
          // NEW: If in emergency mode, reset after completed display
          if (emergencyRefreshMode.current) {
            console.log("Exiting emergency refresh mode");
            emergencyRefreshMode.current = false;
          }
          return;
        }
        
        const endIndex = Math.min(currentIndex + BATCH_SIZE, features.length);
        
        // Process a batch of features
        for (let i = currentIndex; i < endIndex; i++) {
          try {
            const feature = features[i];
            
            // Extract zip code - try multiple property names that might contain it
            const zipCode = feature.properties.ZCTA5CE10 || 
                          feature.properties.ZIP || 
                          feature.properties.zipCode ||
                          feature.properties.GEOID10 ||
                          feature.properties.zip ||
                          feature.properties.postalCode ||
                          feature.properties.ZCTA ||
                          feature.properties.zcta;
            
            if (!zipCode) {
              skipCount++;
              continue;
            }
            
            // FIXED: Simplify visibility logic based on user type and getVisibleZipCodes result
            
            // If we have specific visible codes and this code isn't in the list, skip it
            if (visibleZipCodes !== null && !visibleZipCodes.includes(zipCode)) {
              skipCount++;
              continue;
            }
            
            // Extract shape data
            const shapeData = extractPolygonPointsFromGeoJson(feature);
            if (!shapeData || !shapeData.polygonPoints || shapeData.polygonPoints.length < 3) {
              skipCount++;
              continue;
            }
            
            // For non-admins OR when zoomed out, skip if outside current view (optimization)
            // Admins with panel open see all regardless of bounds
            if ((currentZoom < 9 && !(isAdmin && adminPanelOpen)) || (!isAdmin && currentZoom < 12)) {
              const center = shapeData.center;
              if (!currentBounds.contains([center.lat, center.lng])) {
                boundsCheckSkipped++;
                continue;
              }
            }
            
            // Check if this ZIP code is assigned to a user
            const assignment = findZipCodeAssignment(zipCode);
            
            // Determine if selected
            const isSelected = selectedZipCodes.includes(zipCode);
            
            // Determine if this is assigned to current user (for highlighting)
            const isCurrentUserZip = currentUser && assignment?.userId === currentUser;
            
            // Determine styling based on assignment and selection
            let color, fillColor, weight, fillOpacity;
            
            if (assignment) {
              // User assignment styling
              const userColor = getUserColor(assignment.userId);
              color = userColor.bg;
              fillColor = userColor.bg;
              
              // Highlight current user's assignments with thicker border
              weight = isCurrentUserZip ? 3 : 2;
              
              // Use higher opacity for current user's assignments
              fillOpacity = isCurrentUserZip ? defaultOpacity * 1.5 : defaultOpacity;
            } else if (isSelected) {
              // Selected styling
              color = '#10b981'; // Emerald green
              fillColor = '#10b981';
              weight = 2;
              fillOpacity = 0.4;
            } else {
              // Default styling - more visible when admin panel is open
              color = '#3b82f6'; // Blue
              fillColor = '#3b82f6';
              weight = isAdmin && adminPanelOpen ? 1.5 : 1;
              fillOpacity = isAdmin && adminPanelOpen ? 0.25 : 0.2;
            }
            
            // Create polygon with appropriate styling
            const polygon = window.L.polygon(shapeData.polygonPoints, {
              color,
              weight,
              opacity: 0.8,
              fillColor,
              fillOpacity,
              className: `zip-boundary zip-${zipCode}`,
              zipCode: zipCode
            });
            
            // Tooltip content with assignment info if applicable
            let tooltipContent = zipCode;
            if (assignment) {
              const userName = userDisplayNames[assignment.userId] || 'Unknown User';
              tooltipContent = `${zipCode} - ${userName}`;
            }
            
            // Add tooltip with zip code (and assignment info if applicable)
            polygon.bindTooltip(tooltipContent, {
              permanent: false,
              direction: 'center',
              className: 'zip-tooltip'
            });
            
            // Add click handler - only admins can make assignments
            polygon.on('click', (e) => {
              // Stop propagation
              if (e && e.originalEvent) {
                e.originalEvent.stopPropagation();
              }
              
              // If in assignment mode and a user is selected, assign this ZIP code
              // Only admins can assign zones
              if (canAssignZones && showAssignmentMode && selectedUser) {
                assignZipCodeToUser(zipCode, selectedUser);
                return;
              }
              
              // Otherwise toggle selection - anyone can select for viewing
              setSelectedZipCodes(prev => {
                if (prev.includes(zipCode)) {
                  // Deselect
                  if (!assignment) {
                    polygon.setStyle({
                      fillOpacity: 0.2,
                      weight: 1,
                      color: '#3b82f6'
                    });
                  }
                  return prev.filter(code => code !== zipCode);
                } else {
                  // Select
                  if (!assignment) {
                    polygon.setStyle({
                      fillOpacity: 0.5,
                      weight: 2,
                      color: '#10b981'
                    });
                  }
                  return [...prev, zipCode];
                }
              });
            });
            
            // Add to layer group - check if it still exists
            if (allZipCodesLayerRef.current) {
              try {
                allZipCodesLayerRef.current.addLayer(polygon);
                visibleFeatures++;
              } catch (error) {
                console.error("Error adding polygon to layer group:", error);
              }
            }
          } catch (error) {
            console.warn("Error displaying zip code:", error);
          }
        }
        
        // Update index for next batch
        currentIndex = endIndex;
        
        // Process next batch asynchronously
        setTimeout(processBatch, 0);
      };
      
      // Start processing
      processBatch();
      
    } catch (error) {
      console.error("Error displaying all zip codes:", error);
      displayTriggeredRef.current = false;
      
      // NEW: Schedule an emergency retry
      if (componentMountedRef.current) {
        console.log("Scheduling emergency retry due to display error");
        setTimeout(() => {
          if (componentMountedRef.current) {
            emergencyRefreshMode.current = true;
            displayAllZipCodesRef.current();
          }
        }, 1000);
      }
    }
  }, [
    mapRef, 
    zipCodeData, 
    selectedZipCodes, 
    extractPolygonPointsFromGeoJson, 
    componentMountedRef, 
    findZipCodeAssignment, 
    getUserColor, 
    defaultOpacity,
    userDisplayNames,
    showAssignmentMode,
    selectedUser,
    isAdmin,
    canAssignZones,
    currentUser,
    removeAllZipCodes,
    getVisibleZipCodes,
    adminPanelOpen,
    teamId
  ]);
  
  // Store the reference to displayAllZipCodes
  useEffect(() => {
    displayAllZipCodesRef.current = displayAllZipCodes;
  }, [displayAllZipCodes]);
  
  // Force show all boundaries regardless of zoom level with retry capability
  const forceShowAllBoundaries = useCallback(() => {
    console.log("FORCE SHOWING ALL BOUNDARIES");
    
    // Set emergency mode for more persistent display
    emergencyRefreshMode.current = true;
    
    // Save current states
    const currentShowOnlyAssigned = showOnlyAssigned;
    
    // Show all
    setShowOnlyAssigned(false);
    
    setTimeout(() => {
      // Display them
      displayAllZipCodesRef.current();
      
      // Reset after a delay
      setTimeout(() => {
        setShowOnlyAssigned(currentShowOnlyAssigned);
        emergencyRefreshMode.current = false;
      }, 10000); // Show all for 10 seconds then reset
    }, 100);
  }, [showOnlyAssigned]);
  
  // Store reference to forceShowAllBoundaries
  useEffect(() => {
    forceShowAllBoundariesRef.current = forceShowAllBoundaries;
  }, [forceShowAllBoundaries]);
  
  // Toggle showing all zip codes
  const toggleAllZipCodes = useCallback(() => {
    if (showAllZipCodes) {
      removeAllZipCodes();
      setShowAllZipCodes(false);
    } else {
      setShowAllZipCodes(true);
      displayAllZipCodesRef.current();
    }
  }, [showAllZipCodes, removeAllZipCodes]);
  
  // Toggle showing only assigned ZIP codes
  const toggleOnlyAssigned = useCallback(() => {
    setShowOnlyAssigned(!showOnlyAssigned);
    
    // Refresh display after state change
    setTimeout(() => {
      displayAllZipCodesRef.current();
    }, 10);
  }, [showOnlyAssigned]);
  
  // Load user ZIP code assignments from Firestore
  const loadUserZipAssignments = useCallback(async () => {
    if (!teamId) {
      console.log("No team ID provided, skipping ZIP code assignment loading");
      return;
    }
    
    try {
      console.log(`Loading ZIP code assignments for team ${teamId}`);
      setIsLoading(true);
      
      const assignmentsQuery = query(collection(db, 'team_zip_assignments'), where('teamId', '==', teamId));
      const querySnapshot = await getDocs(assignmentsQuery);
      
      const assignments = {};
      const history = [];
      const stats = {};
      
      querySnapshot.forEach(doc => {
        const data = doc.data();
        
        // Store the assignment with the document ID for later updates
        if (data.zipCode && data.userId) {
          if (!assignments[data.userId]) {
            assignments[data.userId] = [];
          }
          
          assignments[data.userId].push({
            id: doc.id,
            zipCode: data.zipCode,
            assignedAt: data.assignedAt?.toDate() || new Date(),
            assignedBy: data.assignedBy || 'Unknown'
          });
          
          // Update stats
          if (!stats[data.userId]) {
            stats[data.userId] = { count: 0 };
          }
          stats[data.userId].count++;
          
          // Add to history
          history.push({
            id: doc.id,
            zipCode: data.zipCode,
            userId: data.userId,
            assignedAt: data.assignedAt?.toDate() || new Date(),
            assignedBy: data.assignedBy || 'Unknown'
          });
        }
      });
      
      // Sort history by assigned date (newest first)
      history.sort((a, b) => b.assignedAt - a.assignedAt);
      
      console.log(`Loaded ${Object.keys(assignments).length} user assignments with ${history.length} total ZIP codes`);
      setUserZipAssignments(assignments);
      setAssignmentHistory(history);
      setZipCodeStats(stats);
      
      // After loading assignments, refresh the display
      if (zipCodeData && mapRef.current) {
        // Use emergency mode to ensure display
        emergencyRefreshMode.current = true;
        displayAllZipCodesRef.current();
      }
    } catch (error) {
      console.error("Error loading ZIP code assignments:", error);
      setError(`Failed to load ZIP code assignments: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [teamId, db, zipCodeData, mapRef]);
  
  // Remove a ZIP code assignment - ADMIN ONLY
  const removeZipCodeAssignment = useCallback(async (assignmentId) => {
    // Check permissions - only admins can remove assignments
    if (!canAssignZones) {
      console.error("Permission denied: Only admins can remove ZIP code assignments");
      setError("Permission denied: Only admins can remove ZIP code assignments");
      return false;
    }
    
    if (!assignmentId) {
      console.error("Missing assignment ID for removal");
      return false;
    }
    
    try {
      console.log(`Removing ZIP code assignment ${assignmentId}`);
      
      // Find the assignment in our local state
      let foundAssignment = null;
      let foundUserId = null;
      
      // Search through the user assignments
      for (const [userId, assignments] of Object.entries(userZipAssignments)) {
        const assignment = assignments.find(a => a.id === assignmentId);
        if (assignment) {
          foundAssignment = assignment;
          foundUserId = userId;
          break;
        }
      }
      
      if (!foundAssignment) {
        console.error(`Assignment with ID ${assignmentId} not found`);
        return false;
      }
      
      // Delete from Firestore
      await deleteDoc(doc(db, 'team_zip_assignments', assignmentId));
      console.log(`Deleted assignment with ID: ${assignmentId}`);
      
      // Update local state
      setUserZipAssignments(prev => {
        const newAssignments = { ...prev };
        
        if (foundUserId && newAssignments[foundUserId]) {
          newAssignments[foundUserId] = newAssignments[foundUserId].filter(a => a.id !== assignmentId);
          
          // Remove the user key if they have no more assignments
          if (newAssignments[foundUserId].length === 0) {
            delete newAssignments[foundUserId];
          }
        }
        
        return newAssignments;
      });
      
      // Update history
      setAssignmentHistory(prev => prev.filter(item => item.id !== assignmentId));
      
      // Update stats
      setZipCodeStats(prev => {
        const newStats = { ...prev };
        if (foundUserId && newStats[foundUserId]) {
          newStats[foundUserId].count--;
          if (newStats[foundUserId].count <= 0) {
            delete newStats[foundUserId];
          }
        }
        return newStats;
      });
      
      // Refresh the display
      emergencyRefreshMode.current = true;
      displayAllZipCodesRef.current();
      
      return true;
    } catch (error) {
      console.error("Error removing ZIP code assignment:", error);
      setError(`Failed to remove ZIP code assignment: ${error.message}`);
      return false;
    }
  }, [userZipAssignments, db, canAssignZones]);
  
  // Assign a ZIP code to a user - ADMIN ONLY
  const assignZipCodeToUser = useCallback(async (zipCode, userId) => {
    // Check permissions - only admins can assign 
    if (!canAssignZones) {
      console.error("Permission denied: Only admins can assign ZIP codes");
      setError("Permission denied: Only admins can assign ZIP codes");
      return false;
    }
    
    if (!teamId || !zipCode || !userId) {
      console.error("Missing required data for ZIP code assignment");
      return false;
    }
    
    try {
      console.log(`Assigning ZIP code ${zipCode} to user ${userId}`);
      
      // Check if this ZIP code is already assigned to this user
      const existingAssignment = userZipAssignments[userId]?.find(a => a.zipCode === zipCode);
      if (existingAssignment) {
        console.log(`ZIP code ${zipCode} is already assigned to user ${userId}`);
        return false;
      }
      
      // Check if this ZIP code is assigned to another user and warn
      const currentAssignment = findZipCodeAssignment(zipCode);
      if (currentAssignment && currentAssignment.userId !== userId) {
        const confirmMsg = `ZIP code ${zipCode} is already assigned to ${userDisplayNames[currentAssignment.userId] || currentAssignment.userId}. Reassign to ${userDisplayNames[userId] || userId}?`;
        if (!window.confirm(confirmMsg)) {
          return false;
        }
        
        // Delete the current assignment first
        await removeZipCodeAssignment(currentAssignment.assignment.id);
      }
      
      // Create a new assignment
      const assignmentData = {
        teamId,
        zipCode,
        userId,
        assignedAt: new Date(),
        assignedBy: currentUser || 'Unknown'
      };
      
      const docRef = await addDoc(collection(db, 'team_zip_assignments'), assignmentData);
      console.log(`Created new assignment with ID: ${docRef.id}`);
      
      // Update local state
      setUserZipAssignments(prev => {
        const newAssignments = { ...prev };
        if (!newAssignments[userId]) {
          newAssignments[userId] = [];
        }
        
        newAssignments[userId].push({
          id: docRef.id,
          zipCode,
          assignedAt: new Date(),
          assignedBy: currentUser || 'Unknown'
        });
        
        return newAssignments;
      });
      
      // Add to history
      setAssignmentHistory(prev => [{
        id: docRef.id,
        zipCode,
        userId,
        assignedAt: new Date(),
        assignedBy: currentUser || 'Unknown'
      }, ...prev]);
      
      // Update stats
      setZipCodeStats(prev => {
        const newStats = { ...prev };
        if (!newStats[userId]) {
          newStats[userId] = { count: 0 };
        }
        newStats[userId].count++;
        return newStats;
      });
      
      // Use emergency mode to ensure display refresh works
      emergencyRefreshMode.current = true;
      
      // Refresh the display
      displayAllZipCodesRef.current();
      
      return true;
    } catch (error) {
      console.error("Error assigning ZIP code:", error);
      setError(`Failed to assign ZIP code: ${error.message}`);
      return false;
    }
  }, [teamId, userZipAssignments, findZipCodeAssignment, currentUser, db, canAssignZones, userDisplayNames, removeZipCodeAssignment]);
  
  // Batch assign multiple ZIP codes to a user - ADMIN ONLY
  const batchAssignZipCodes = useCallback(async (zipCodes, userId) => {
    // Check permissions - only admins can batch assign
    if (!canAssignZones) {
      console.error("Permission denied: Only admins can batch assign ZIP codes");
      setError("Permission denied: Only admins can batch assign ZIP codes");
      return false;
    }
    
    if (!teamId || !zipCodes.length || !userId) {
      console.error("Missing required data for batch ZIP code assignment");
      return false;
    }
    
    try {
      console.log(`Batch assigning ${zipCodes.length} ZIP codes to user ${userId}`);
      
      // Filter out ZIP codes that are already assigned to this user
      const existingZipCodes = getUserZipCodes(userId);
      const newZipCodes = zipCodes.filter(z => !existingZipCodes.includes(z));
      
      if (newZipCodes.length === 0) {
        console.log("All selected ZIP codes are already assigned to this user");
        return false;
      }
      
      // Create assignments one by one
      let successCount = 0;
      
      for (const zipCode of newZipCodes) {
        const result = await assignZipCodeToUser(zipCode, userId);
        if (result) successCount++;
      }
      
      console.log(`Successfully assigned ${successCount} out of ${newZipCodes.length} ZIP codes`);
      
      // Final display refresh
      emergencyRefreshMode.current = true;
      displayAllZipCodesRef.current();
      
      return successCount > 0;
    } catch (error) {
      console.error("Error batch assigning ZIP codes:", error);
      setError(`Failed to batch assign ZIP codes: ${error.message}`);
      return false;
    }
  }, [teamId, getUserZipCodes, assignZipCodeToUser, canAssignZones]);
  
  // Initialize ZIP code path detection
  const initializeZipBoundaries = useCallback(() => {
    // Add to window for global access
    if (typeof window !== 'undefined') {
      // Function to update the GeoJSON path across components
      window.updateGeoJsonPath = (newPath) => {
        console.log("Updating global GeoJSON path to:", newPath);
        window.zipCodeGeoJsonPath = newPath;
        
        // Also notify any listening components
        const event = new CustomEvent('geojsonpathchange', { detail: { path: newPath } });
        window.dispatchEvent(event);
        
        // Attempt to refresh any boundaries displayed
        setTimeout(() => {
          if (window.refreshBoundaries) {  // Check inside the timeout callback
            console.log("Triggering boundary refresh with new path");
            window.refreshBoundaries();
          } else {
            console.log("window.refreshBoundaries not available, skipping refresh");
          }
        }, 500);
        
        return true;
      };
      
      // Initial path detection - try to find the geojson in common locations
      const testPaths = async () => {
        // Default paths to try in order - MODIFIED TO CHECK BETTER PATHS FIRST
        const pathsToTry = [
          '/data/il-zip-codes.geojson',
          '/data/wi-zip-codes.geojson',
          '/data/in-zip-codes.geojson',
          'data/il-zip-codes.geojson', // Try without leading slash
          'data/wi-zip-codes.geojson',
          'data/in-zip-codes.geojson',
          '../data/il-zip-codes.geojson', // Try parent directory
          '/assets/data/il-zip-codes.geojson', // Try assets directory
          '/public/data/il-zip-codes.geojson',
          '/public/data/zip-codes.geojson',
          '/zip-codes.geojson',
          // Fallback to a known public GeoJSON source
          'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/il_illinois_zip_codes_geo.min.json'
        ];
        
        // Try MapDisplay.js global variable if it exists
        if (typeof window !== 'undefined' && window.mapDisplayGeoJsonData && window.mapDisplayGeoJsonData.illinois) {
          console.log("Found Illinois GeoJSON data in MapDisplay global variable");
          setZipCodeData(window.mapDisplayGeoJsonData.illinois);
          zipCodesLoaded.current = true;
          return;
        }
        
        for (const path of pathsToTry) {
          try {
            console.log(`Testing GeoJSON path: ${path}`);
            const response = await fetch(path, { 
              headers: { 'Accept': 'application/json, application/geo+json' }, // FIXED: Accept geo+json too
              cache: 'no-store'
            });
            
            if (response.ok) {
              // Check if it's actually JSON and not HTML
              const contentType = response.headers.get('content-type');
              const isJsonOrGeoJson = contentType && (
                contentType.includes('application/json') || 
                contentType.includes('application/geo+json') // FIXED: Accept geo+json content type
              );
              
              if (isJsonOrGeoJson) {
                // Try to parse as JSON to verify
                const text = await response.text();
                const json = JSON.parse(text);
                
                // Check if it's a GeoJSON FeatureCollection
                if (json.type === 'FeatureCollection' && Array.isArray(json.features)) {
                  console.log(`✅ Found valid GeoJSON at ${path} with ${json.features.length} features`);
                  window.updateGeoJsonPath(path);
                  break;
                } else {
                  console.log(`❌ Found JSON at ${path} but it's not a valid GeoJSON FeatureCollection`);
                }
              } else {
                console.log(`❌ Found file at ${path} but content type is ${contentType}, not JSON`);
              }
            } else {
              console.log(`❌ Path ${path} returned status ${response.status}`);
            }
          } catch (err) {
            console.log(`❌ Error testing path ${path}: ${err.message}`);
          }
        }
      };
      
      // Run the path test
      testPaths();
    }
  }, []);
  
  // NEW: More robust map change event handler with debounce
  const handleMapChange = useCallback(() => {
    if (!mapRef.current || !showAllZipCodes) return;
    
    // Skip if display already in progress
    if (displayTriggeredRef.current) {
      return;
    }
    
    // Get current timestamp
    const now = Date.now();
    
    // Skip if we just displayed and not in emergency mode (debounce)
    if (now - lastDisplayTime.current < 300 && !emergencyRefreshMode.current) {
      return;
    }
    
    // Use a small delay to prevent too frequent refreshes while panning/zooming
    setTimeout(() => {
      displayAllZipCodesRef.current();
    }, 300);
  }, [mapRef, showAllZipCodes]);
  
  // Refresh zip code display when map view changes if showing zip codes
  useEffect(() => {
    if (!mapRef.current || !showAllZipCodes) return;
    
    // Add event listeners with the debounced handler
    mapRef.current.on('moveend', handleMapChange);
    mapRef.current.on('zoomend', handleMapChange);
    
    return () => {
      if (mapRef.current) {
        mapRef.current.off('moveend', handleMapChange);
        mapRef.current.off('zoomend', handleMapChange);
      }
    };
  }, [mapRef, showAllZipCodes, handleMapChange]);

  // NEW: Force redisplay on window resize to fix responsive issues
  useEffect(() => {
    if (!showAllZipCodes) return;
    
    const handleResize = () => {
      // Use emergency mode for resize to ensure display
      if (mapRef.current && zipCodeData) {
        console.log("Window resized - refreshing boundaries");
        emergencyRefreshMode.current = true;
        displayAllZipCodesRef.current();
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [showAllZipCodes, mapRef, zipCodeData]);
  
  // Improved GeoJSON loading function with better error handling
  const loadGeoJsonWithRetry = useCallback(async () => {
    if (zipCodesLoaded.current || geoJsonLoadInProgress.current) return;
    
    geoJsonLoadInProgress.current = true;
    setIsLoading(true);
    
    // FIRST CHECK: See if MapDisplay already loaded the data
    if (typeof window !== 'undefined' && window.mapDisplayGeoJsonData) {
      console.log("Using already loaded GeoJSON data from MapDisplay global object");
      const data = window.mapDisplayGeoJsonData.illinois || window.mapDisplayGeoJsonData.wisconsin || window.mapDisplayGeoJsonData.indiana;
      
      if (data) {
        console.log(`Found preloaded GeoJSON data with ${data.features?.length || 0} features`);
        setZipCodeData(data);
        
        // Create mapping of zip codes to boundaries
        const boundaries = {};
        let foundZipCodes = 0;
        data.features.forEach(feature => {
          // Try multiple potential property names for zip codes
          let zipCode = feature.properties.ZCTA5CE10 || 
                       feature.properties.ZIP || 
                       feature.properties.zipCode ||
                       feature.properties.GEOID10 ||
                       feature.properties.zip ||
                       feature.properties.postalCode ||
                       feature.properties.ZCTA ||
                       feature.properties.zcta;
                       
          // Convert to string if it's a number
          if (typeof zipCode === 'number') {
            zipCode = zipCode.toString();
          }
          
          if (zipCode) {
            boundaries[zipCode] = feature;
            foundZipCodes++;
          }
        });
        
        console.log(`Created boundaries mapping for ${foundZipCodes} zip codes from global object`);
        setZipCodeBoundaries(boundaries);
        setError(null);
        
        zipCodesLoaded.current = true;
        geoJsonLoadInProgress.current = false;
        
        // Continue with user assignments
        loadUserZipAssignments();
        
        // Display zip codes with emergency mode
        emergencyRefreshMode.current = true;
        setTimeout(() => {
          displayAllZipCodesRef.current();
        }, 500);
        
        setIsLoading(false);
        return;
      }
    }
    
    // Get paths for all active states
    const paths = getGeoJsonPaths();
    
    if (paths.length === 0) {
      setError("No states selected for display");
      setIsLoading(false);
      geoJsonLoadInProgress.current = false;
      return;
    }
    
    try {
      // Load data for each state
      const stateDataPromises = paths.map(async ({ state, path }) => {
        try {
          const response = await fetch(path, {
            headers: {
              'Accept': 'application/json, application/geo+json',
              'Cache-Control': 'no-cache'
            }
          });
          
          if (!response.ok) {
            throw new Error(`Failed to load ${state.toUpperCase()} GeoJSON: HTTP ${response.status}`);
          }
          
          const data = await response.json();
          return { state, data };
        } catch (error) {
          console.error(`Error loading ${state.toUpperCase()} data:`, error);
          return { state, error: error.message };
        }
      });
      
      // Wait for all state data to load
      const results = await Promise.all(stateDataPromises);
      
      // Combine all features from successful loads
      const combinedFeatures = [];
      const stateData = {};
      let successCount = 0;
      
      results.forEach(({ state, data, error }) => {
        if (data && data.features) {
          stateData[state] = data;
          combinedFeatures.push(...data.features);
          successCount++;
          console.log(`Successfully loaded ${state.toUpperCase()} with ${data.features.length} features`);
        } else {
          console.error(`Failed to load ${state.toUpperCase()}:`, error);
        }
      });
      
      if (combinedFeatures.length === 0) {
        throw new Error("No valid GeoJSON data loaded for any state");
      }
      
      // Create combined GeoJSON structure
      const combinedData = {
        type: "FeatureCollection",
        features: combinedFeatures
      };
      
      console.log(`Combined GeoJSON data loaded with ${combinedFeatures.length} total features from ${successCount} states`);
      
      setStateGeoJsonData(stateData);
      setCombinedZipCodeData(combinedData);
      setZipCodeData(combinedData); // Use combined data for display
      zipCodesLoaded.current = true;
      
      // Create boundaries mapping from combined data
      const boundaries = {};
      let foundZipCodes = 0;
      combinedData.features.forEach(feature => {
        let zipCode = feature.properties.ZCTA5CE10 || 
                     feature.properties.ZIP || 
                     feature.properties.zipCode ||
                     feature.properties.GEOID10 ||
                     feature.properties.zip ||
                     feature.properties.postalCode ||
                     feature.properties.ZCTA ||
                     feature.properties.zcta;
                     
        if (typeof zipCode === 'number') {
          zipCode = zipCode.toString();
        }
        
        if (zipCode) {
          boundaries[zipCode] = feature;
          foundZipCodes++;
        }
      });
      
      console.log(`Created boundaries mapping for ${foundZipCodes} zip codes`);
      setZipCodeBoundaries(boundaries);
      setError(null);
      
      // Load user assignments
      await loadUserZipAssignments();
      
      // Automatically display zip codes when ready
      if (mapRef.current) {
        emergencyRefreshMode.current = true;
        setTimeout(() => {
          displayAllZipCodesRef.current();
        }, 500);
      }
      
      geoJsonLoadInProgress.current = false;
      maxLoadAttempts.current = 0; // Reset attempts counter on success
    } catch (error) {
      console.error("Error loading multi-state GeoJSON data:", error);
      geoJsonLoadInProgress.current = false;
      setError(`Failed to load ZIP code data: ${error.message}`);
      
      // Update UI with helpful debugging information
      setDebugMessage(`
        Error Type: ${error.name}
        
        Possible solutions:
        1. Check that the GeoJSON files exist
        2. Verify the files are valid GeoJSON
        3. Make sure the server properly sends Content-Type: application/json or application/geo+json
        
        Tip: Try using the Illinois fallback ZIP codes by clicking the button below.
      `);
      
      // Schedule retry with exponential backoff if we haven't exceeded max attempts
      maxLoadAttempts.current++;
      if (maxLoadAttempts.current < 3) { // Try up to 3 times
        const delay = Math.min(1000 * Math.pow(2, maxLoadAttempts.current - 1), 5000); // Exponential backoff with 5s max
        console.log(`Scheduling retry in ${delay}ms`);
        
        setTimeout(() => {
          if (componentMountedRef.current && !zipCodesLoaded.current) {
            console.log("Retrying GeoJSON data load");
            loadGeoJsonWithRetry();
          }
        }, delay);
      } else {
        console.warn("Maximum GeoJSON load attempts reached - trying fallback file");
        
        // Try a known working fallback after all attempts fail
        if (!paths.some(p => p.path === 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/il_illinois_zip_codes_geo.min.json')) {
          // Set the custom path to the fallback
          setCustomGeoJsonPath('https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/il_illinois_zip_codes_geo.min.json');
          
          // Reset for next attempt
          maxLoadAttempts.current = 0;
          zipCodesLoaded.current = false;
          geoJsonLoadInProgress.current = false;
          
          // Try again with the fallback after a short delay
          setTimeout(() => {
            loadGeoJsonWithRetry();
          }, 1000);
        } else {
          // We already tried the fallback, give up
          setError("Unable to load ZIP code data after multiple attempts. Contact support for assistance.");
          maxLoadAttempts.current = 0; // Reset for potential manual retry
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [getGeoJsonPaths, mapRef, loadUserZipAssignments]);

  // Add effect to listen for GeoJSON path changes
  useEffect(() => {
    // Initialize ZIP code path detection
    initializeZipBoundaries();
    
    // Listen for path change events
    const handlePathChange = (event) => {
      console.log("Received geojson path change event:", event.detail.path);
      setCustomGeoJsonPath(event.detail.path);
      
      // If already loaded, don't reload
      if (zipCodesLoaded.current) {
        console.log("ZIP codes already loaded, skipping reload");
        return;
      }
      
      // Force reload with new path
      zipCodesLoaded.current = false;
      geoJsonLoadInProgress.current = false;
      maxLoadAttempts.current = 0;
      loadGeoJsonWithRetry();
    };
    
    window.addEventListener('geojsonpathchange', handlePathChange);
    
    return () => {
      window.removeEventListener('geojsonpathchange', handlePathChange);
    };
  }, [initializeZipBoundaries, loadGeoJsonWithRetry]);

  // Use fallback GeoJSON
  const useFallbackGeoJson = useCallback(() => {
    const fallbackUrl = 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/il_illinois_zip_codes_geo.min.json';
    
    console.log("Switching to fallback GeoJSON:", fallbackUrl);
    setCustomGeoJsonPath(fallbackUrl);
    
    // Update global path if available
    if (typeof window !== 'undefined' && typeof window.updateGeoJsonPath === 'function') {
      window.updateGeoJsonPath(fallbackUrl);
    }
    
    // Reset loading state
    zipCodesLoaded.current = false;
    geoJsonLoadInProgress.current = false;
    maxLoadAttempts.current = 0;
    
    // Start loading
    loadGeoJsonWithRetry();
  }, [loadGeoJsonWithRetry]);

  // Check for MapDisplay.js data integration
  const checkForMapDisplayData = useCallback(() => {
    if (typeof window !== 'undefined' && window.mapDisplayGeoJsonData) {
      // If MapDisplay has already loaded the GeoJSON data, use it
      console.log("Found preloaded GeoJSON data from MapDisplay");
      return true;
    }
    return false;
  }, []);

  // Update initialization effect
  useEffect(() => {
    // Ensure refreshBoundaries is initialized first
    const ensureRefreshBoundaries = () => {
      if (typeof window.refreshBoundaries !== 'function') {
        // If not available yet, check again after a short delay
        setTimeout(() => {
          if (typeof window.refreshBoundaries !== 'function') {
            console.log("window.refreshBoundaries still not available, setting placeholder");
            window.refreshBoundaries = () => {
              console.log("Placeholder refreshBoundaries called - real function not ready yet");
            };
          }
          // Initialize boundaries after placeholder is set
          ensureInitialization();
        }, 100);
      } else {
        ensureInitialization();
      }
    };
    
    const ensureInitialization = () => {
      if (!componentMountedRef.current || zipCodesLoaded.current) return;
      
      // First check if MapDisplay already has the data
      if (checkForMapDisplayData()) {
        loadGeoJsonWithRetry(); // This will detect and use the global data
      } else {
        // Otherwise load normally
        loadGeoJsonWithRetry();
      }
    };
    
    ensureRefreshBoundaries();
  }, [loadGeoJsonWithRetry, checkForMapDisplayData]);

  // Add effect to handle delayed availability of the map reference
  useEffect(() => {
    if (mapRef.current && zipCodeData && !allZipCodesLayerRef.current && !geoJsonLoadInProgress.current) {
      console.log("Map became available after GeoJSON data was loaded - displaying boundaries");
      emergencyRefreshMode.current = true;
      displayAllZipCodesRef.current();
    }
  }, [mapRef, zipCodeData, geoJsonLoadInProgress]);

  // NEW: Additional display stabilization - check every 10 seconds if zones are missing
  useEffect(() => {
    if (!showAllZipCodes || !visible) return;
    
    // Periodic check to ensure zones are displayed
    const stabilityInterval = setInterval(() => {
      if (mapRef.current && zipCodeData) {
        // If layer is missing but should be displayed
        if (!allZipCodesLayerRef.current && showAllZipCodes) {
          console.log("🚨 STABILITY CHECK: Zone layer missing but should be displayed - refreshing");
          emergencyRefreshMode.current = true;
          displayAllZipCodesRef.current();
        }
        
        // If layer exists, check if it has any layers
        if (allZipCodesLayerRef.current) {
          try {
            // Check if layer has any content (this might throw if layer is invalid)
            if (allZipCodesLayerRef.current.getLayers().length === 0) {
              console.log("🚨 STABILITY CHECK: Zone layer exists but is empty - refreshing");
              emergencyRefreshMode.current = true;
              displayAllZipCodesRef.current();
            }
          } catch (error) {
            console.log("🚨 STABILITY CHECK: Error checking layer - refreshing", error);
            emergencyRefreshMode.current = true;
            displayAllZipCodesRef.current();
          }
        }
      }
    }, 10000); // Check every 10 seconds
    
    return () => {
      clearInterval(stabilityInterval);
    };
  }, [mapRef, zipCodeData, showAllZipCodes, visible]);

  // Add stylesheet for tooltip styling
  useEffect(() => {
    // Create a style element for custom tooltip styles
    const styleEl = document.createElement('style');
    styleEl.id = 'zipcode-boundary-styles';
    styleEl.textContent = `
      .zip-boundary {
        transition: all 0.2s ease;
      }
      
      .zip-boundary:hover {
        fillOpacity: 0.3 !important;
        weight: 2 !important;
        cursor: pointer;
      }
      
      .zip-tooltip {
        background: rgba(0, 0, 0, 0.7) !important;
        border: none !important;
        color: white !important;
        font-weight: bold;
        padding: 2px 6px !important;
      }
      
      /* Container styles */
      .boundaries-container {
        max-height: 80vh;
        overflow-y: auto;
        z-index: 1000 !important;
      }
      
      .boundaries-panel {
        width: 320px;
        max-width: 90vw;
        z-index: 1000 !important;
      }
      
      /* User assignment styles */
      .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
        background-color: #374151;
      }
      
      .user-item {
        transition: all 0.2s ease;
      }
      
      .user-item:hover {
        background-color: rgba(55, 65, 81, 0.5);
      }
      
      .user-item.selected {
        background-color: rgba(79, 70, 229, 0.2);
        border-color: #6366f1;
      }
    `;
    document.head.appendChild(styleEl);
    
    return () => {
      if (document.getElementById('zipcode-boundary-styles')) {
        document.head.removeChild(document.getElementById('zipcode-boundary-styles'));
      }
    };
  }, []);

  // Global exposure for debug and control
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.displayAllZipCodes = () => {
        // Set emergency mode to ensure display works
        emergencyRefreshMode.current = true;
        displayAllZipCodesRef.current();
      };
      
      window.forceShowAllBoundaries = () => {
        // Only admins can force show all
        if (canAssignZones) {
          forceShowAllBoundariesRef.current();
        } else {
          console.log("Only admins can force show all boundaries");
        }
      };
      
      window.loadGeoJsonWithRetry = loadGeoJsonWithRetry;
      window.refreshZipAssignments = loadUserZipAssignments;
      
      // NEW: Improved emergency refresh function with better error handling
      window.refreshBoundaries = () => {
        console.log("🚨 EMERGENCY REFRESH TRIGGERED FROM GLOBAL FUNCTION 🚨");
        
        // Set emergency mode to ensure display works
        emergencyRefreshMode.current = true;
        
        if (mapRef.current && zipCodeData) {
          try {
            // First try to remove all existing boundaries
            if (allZipCodesLayerRef.current) {
              try {
                mapRef.current.removeLayer(allZipCodesLayerRef.current);
              } catch (error) {
                console.log("Error removing layer, might already be gone:", error.message);
              }
              allZipCodesLayerRef.current = null;
            }
            
            // Then wait a bit and display them
            setTimeout(() => {
              displayAllZipCodesRef.current();
            }, 100);
          } catch (error) {
            console.error("Error in emergency refresh:", error);
            
            // Last resort - complete reset and retry
            setTimeout(() => {
              try {
                removeAllZipCodes();
                setTimeout(() => {
                  displayAllZipCodesRef.current();
                }, 200);
              } catch (error) {
                console.error("Final resort refresh failed:", error);
              }
            }, 500);
          }
        } else {
          console.warn("Map or zip code data not available for refresh");
        }
      };
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        delete window.displayAllZipCodes;
        delete window.forceShowAllBoundaries;
        delete window.loadGeoJsonWithRetry;
        delete window.refreshZipAssignments;
        delete window.refreshBoundaries;
      }
    };
  }, [
    canAssignZones,
    forceShowAllBoundaries, 
    loadGeoJsonWithRetry, 
    removeAllZipCodes, 
    mapRef, 
    zipCodeData, 
    showAllZipCodes,
    loadUserZipAssignments
  ]);

  // Auto-initialize boundaries when component mounts
  useEffect(() => {
    console.log("🚀 AUTO-INITIALIZE BOUNDARIES: Component mounted");
    
    // Try to display boundaries if data is already loaded
    const initializeBoundaries = () => {
      if (!zipCodeData || !mapRef.current) return;
      
      console.log("🔄 Auto-displaying zip code boundaries");
      
      // Set showAllZipCodes to true to ensure display
      setShowAllZipCodes(true);
      
      // Use emergency mode to ensure display works
      emergencyRefreshMode.current = true;
      
      // Small delay to ensure state update before displaying
      setTimeout(() => {
        displayAllZipCodesRef.current();
      }, 100);
    };
    
    // Try right away
    initializeBoundaries();
    
    // Also try with longer delays to account for async loading
    const timers = [
      setTimeout(initializeBoundaries, 500),
      setTimeout(initializeBoundaries, 1500),
      setTimeout(initializeBoundaries, 3000),
      setTimeout(initializeBoundaries, 6000)  // Add longer timeout
    ];
    
    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [zipCodeData, mapRef]);

  // Load user assignments when the component mounts or when teamId changes
  useEffect(() => {
    if (teamId) {
      loadUserZipAssignments();
    }
  }, [teamId, loadUserZipAssignments]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log("Cleaning up boundaries component");
      removeAllZipCodes();
    };
  }, [removeAllZipCodes]);

  // For UI visibility only - don't unmount component or unsubscribe from data
  if (!visible) {
    return null;
  }

  // Get a formatted timestamp
  const formatTimestamp = (date) => {
    if (!date) return 'Unknown';
    
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    }).format(date);
  };

  // Get user stats for display
  const getUserStats = (userId) => {
    const stats = zipCodeStats[userId] || { count: 0 };
    const userName = userDisplayNames[userId] || 'Unknown User';
    const color = getUserColor(userId);
    
    return {
      ...stats,
      userId,
      userName,
      color
    };
  };

  // Render UI focused on boundaries and user assignments
  return (
    <div className="boundaries-container fixed top-16 right-4 z-20 max-w-xs md:max-w-xs">
      <div className="boundaries-panel w-80 bg-gray-900 bg-opacity-95 border border-gray-700 rounded-lg shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex-none bg-gradient-to-r from-gray-800 to-gray-900 p-3 border-b border-gray-700 flex justify-between items-center rounded-t-lg">
          <h2 className="font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">
            ZIP Code Territory Assignments
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        {/* Error Message */}
        {error && (
          <div className="bg-red-900 bg-opacity-75 text-red-100 px-4 py-3">
            <div className="flex">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span>{error}</span>
            </div>
            {debugMessage && (
              <button
                onClick={useFallbackGeoJson}
                className="mt-2 bg-blue-600 text-white text-sm px-3 py-1 rounded-md hover:bg-blue-700"
              >
                Use Illinois ZIP Codes Fallback
              </button>
            )}
          </div>
        )}
        
        {/* Loading Indicator */}
        {isLoading && (
          <div className="px-4 py-3 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mr-2"></div>
            <p className="text-gray-300">Loading ZIP code data...</p>
          </div>
        )}
        
        {/* Main Content Area */}
        <div className="flex-grow overflow-y-auto max-h-[calc(100vh-12rem)] p-4">
          <div className="space-y-4">
            {/* State Selection Controls */}
            <div className="bg-gray-800 p-3 rounded-md border border-gray-700">
              <h4 className="text-white font-medium mb-2">States to Display</h4>
              <div className="flex space-x-4">
                {Object.keys(geoJsonPaths).map(state => (
                  <label key={state} className="flex items-center text-gray-300 text-sm">
                    <input
                      type="checkbox"
                      checked={activeStates.includes(state)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setActiveStates(prev => [...prev, state]);
                        } else {
                          setActiveStates(prev => prev.filter(s => s !== state));
                        }
                        // Reload data with new state selection
                        zipCodesLoaded.current = false;
                        loadGeoJsonWithRetry();
                      }}
                      className="mr-2"
                    />
                    {state.toUpperCase()}
                  </label>
                ))}
              </div>
              
              {/* State Data Status */}
              <div className="mt-2 space-y-1">
                {Object.keys(geoJsonPaths).map(state => {
                  const stateData = stateGeoJsonData[state];
                  return (
                    <div key={state} className="flex items-center text-xs text-gray-400">
                      <div className={`w-2 h-2 rounded-full mr-2 ${stateData ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      {state.toUpperCase()}: {stateData ? `${stateData.features?.length || 0} ZIP codes` : 'Not loaded'}
                    </div>
                  );
                })}
              </div>
            </div>
            
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-white">Team Territory Map</h3>
              <div className="flex space-x-2">
                <button
                  onClick={toggleAllZipCodes}
                  className={`px-3 py-1 ${showAllZipCodes ? 'bg-blue-600' : 'bg-gray-600'} hover:bg-blue-700 text-white text-sm rounded-md`}
                >
                  {showAllZipCodes ? 'Hide' : 'Show'} Zones
                </button>
                {canAssignZones && (
                  <button
                    onClick={() => setShowAssignmentMode(!showAssignmentMode)}
                    className={`px-3 py-1 ${showAssignmentMode ? 'bg-green-600' : 'bg-gray-600'} hover:bg-green-700 text-white text-sm rounded-md`}
                  >
                    {showAssignmentMode ? 'Exit Assignment' : 'Assign Zones'}
                  </button>
                )}
              </div>
            </div>
            
            {/* Display Options - Admin Only */}
            {canAssignZones && (
              <div className="bg-gray-800 p-2 rounded-md border border-gray-700">
                <div className="flex justify-between items-center">
                  <span className="text-white text-sm">Display Options</span>
                </div>
                <div className="mt-2 space-y-2">
                  <label className="flex items-center text-gray-300 text-sm">
                    <input
                      type="checkbox"
                      checked={showOnlyAssigned}
                      onChange={toggleOnlyAssigned}
                      className="mr-2"
                    />
                    Only show assigned ZIP codes
                  </label>
                </div>
              </div>
            )}
            
            {/* Assignment Mode Instructions - Admin Only */}
            {canAssignZones && showAssignmentMode && (
              <div className="bg-green-900 bg-opacity-50 border border-green-800 rounded-md p-3">
                <p className="text-green-200 text-sm">
                  <strong>Assignment Mode Active</strong><br />
                  1. Select a team member below<br />
                  2. Click ZIP codes on the map to assign them
                </p>
                {selectedUser && (
                  <div className="mt-2 bg-green-800 bg-opacity-50 p-2 rounded-md">
                    <p className="text-white text-sm">
                      Assigning territories to: <strong>{userDisplayNames[selectedUser] || selectedUser}</strong>
                    </p>
                    {selectedZipCodes.length > 0 && (
                      <button
                        onClick={() => batchAssignZipCodes(selectedZipCodes, selectedUser)}
                        className="mt-2 w-full px-3 py-1 bg-green-600 hover:bg-green-500 text-white text-sm rounded-md"
                      >
                        Assign {selectedZipCodes.length} Selected ZIP Codes
                      </button>
                    )}
                  </div>
                )}
              </div>
            )}
            
            {/* Team Member List with Assignment Stats */}
            <div className="bg-gray-800 p-3 rounded-md border border-gray-700">
              <h4 className="text-white font-medium mb-2">Team Members</h4>
              
              {/* Display team members with their assigned territories */}
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {teamMembers.length > 0 ? (
                  teamMembers.map(userId => {
                    const stats = getUserStats(userId);
                    const isSelected = selectedUser === userId;
                    
                    return (
                      <div 
                        key={userId}
                        className={`user-item flex items-center border p-2 rounded-md ${isSelected ? 'selected' : 'border-gray-700'}`}
                        onClick={() => canAssignZones && showAssignmentMode && setSelectedUser(isSelected ? null : userId)}
                        style={{ 
                          cursor: canAssignZones && showAssignmentMode ? 'pointer' : 'default',
                          borderLeftWidth: '4px', 
                          borderLeftColor: stats.color.bg 
                        }}
                      >
                        <img 
                          src={userProfilePictures[userId] || '/default-avatar.png'} 
                          alt={stats.userName}
                          className="user-avatar mr-2"
                        />
                        <div className="flex-grow">
                          <div className="text-white font-medium">{stats.userName}</div>
                          <div className="text-gray-400 text-xs">
                            {stats.count} assigned ZIP code{stats.count !== 1 ? 's' : ''}
                          </div>
                        </div>
                        {canAssignZones && showAssignmentMode && (
                          <div className="ml-2">
                            {isSelected ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                              </svg>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })
                ) : (
                  <p className="text-gray-400 text-sm">No team members found.</p>
                )}
              </div>
            </div>
            
            {/* Selected ZIP Codes */}
            {selectedZipCodes.length > 0 && (
              <div className="bg-gray-800 p-2 rounded-md border border-gray-700">
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-white font-medium text-sm">Selected ZIP Codes</h4>
                  <button
                    onClick={() => setSelectedZipCodes([])}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    Clear All
                  </button>
                </div>
                <div className="max-h-24 overflow-y-auto">
                  <div className="flex flex-wrap gap-1">
                    {selectedZipCodes.map(zipCode => (
                      <span 
                        key={zipCode}
                        className="bg-green-900 text-white text-xs px-2 py-1 rounded-md flex items-center"
                      >
                        {zipCode}
                        <button
                          onClick={() => setSelectedZipCodes(prev => prev.filter(z => z !== zipCode))}
                          className="ml-1 text-green-300 hover:text-white"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}
            
            {/* Recent Territory Assignments */}
            {assignmentHistory.length > 0 && (
              <div className="bg-gray-800 p-3 rounded-md border border-gray-700">
                <h4 className="text-white font-medium mb-2">Recent Assignments</h4>
                <div className="max-h-48 overflow-y-auto">
                  {assignmentHistory.slice(0, 10).map(item => {
                    const userName = userDisplayNames[item.userId] || 'Unknown User';
                    const assignerName = userDisplayNames[item.assignedBy] || 'Unknown';
                    const userColor = getUserColor(item.userId);
                    
                    return (
                      <div key={item.id} className="border-b border-gray-700 py-2 last:border-b-0">
                        <div className="flex justify-between">
                          <div>
                            <span 
                              className="inline-block px-2 py-0.5 text-xs rounded-md" 
                              style={{ backgroundColor: userColor.bg, color: userColor.text }}
                            >
                              {userName}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-400 text-xs">
                              {formatTimestamp(item.assignedAt)}
                            </span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center mt-1">
                          <div>
                            <span className="text-white text-sm">ZIP: {item.zipCode}</span>
                          </div>
                          {canAssignZones && (
                            <button
                              onClick={() => removeZipCodeAssignment(item.id)}
                              className="text-red-400 hover:text-red-300 text-xs"
                              title="Remove assignment"
                            >
                              Remove
                            </button>
                          )}
                        </div>
                        <div className="text-gray-400 text-xs">
                          Assigned by: {assignerName}
                        </div>
                      </div>
                    );
                  })}
                  
                  {assignmentHistory.length > 10 && (
                    <div className="text-center text-gray-400 text-xs mt-2">
                      + {assignmentHistory.length - 10} more assignments
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {/* Legend */}
            <div className="bg-gray-800 p-3 rounded-md border border-gray-700">
              <h4 className="text-white font-medium mb-2">Color Legend</h4>
              <div className="grid grid-cols-1 gap-2">
                {teamMembers.length > 0 ? (
                  teamMembers.map(userId => {
                    const stats = getUserStats(userId);
                    return (
                      <div key={userId} className="flex items-center">
                        <div 
                          className="w-4 h-4 rounded-sm mr-2" 
                          style={{ backgroundColor: stats.color.bg }}
                        ></div>
                        <span className="text-white text-sm">{stats.userName} ({stats.count})</span>
                      </div>
                    );
                  })
                ) : (
                  <p className="text-gray-400 text-sm">No team members assigned yet.</p>
                )}
                
                <div className="border-t border-gray-700 my-1 pt-1"></div>
                
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded-sm mr-2 bg-blue-500"></div>
                  <span className="text-white text-sm">Unassigned ZIP Codes</span>
                </div>
                
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded-sm mr-2 bg-green-500"></div>
                  <span className="text-white text-sm">Selected ZIP Codes</span>
                </div>
              </div>
            </div>
            
            {/* Management Tools (Admin Only) */}
            {canAssignZones && (
              <div className="bg-gray-800 p-3 rounded-md border border-gray-700">
                <h4 className="text-white font-medium mb-2">Admin Tools</h4>
                <div className="space-y-2">
                  <button
                    onClick={loadUserZipAssignments}
                    className="w-full px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                    </svg>
                    Refresh Assignments
                  </button>
                  
                  <button
                    onClick={() => {
                      emergencyRefreshMode.current = true;
                      displayAllZipCodesRef.current();
                    }}
                    className="w-full px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm rounded-md flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                    </svg>
                    Refresh Map Display
                  </button>
                  
                  {selectedZipCodes.length > 0 && selectedUser && (
                    <button
                      onClick={() => batchAssignZipCodes(selectedZipCodes, selectedUser)}
                      className="w-full px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md flex items-center justify-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5z" />
                        <path d="M11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                      </svg>
                      Batch Assign {selectedZipCodes.length} ZIP Codes
                    </button>
                  )}
                </div>
              </div>
            )}
            
            {/* Status and Information */}
            <div className="mt-2 p-2 bg-gray-800 rounded-md border border-gray-700">
              <p className="text-xs text-gray-400">
                {zipCodeData ? 
                  `GeoJSON loaded with ${Object.keys(zipCodeBoundaries).length} zip code boundaries.` :
                  "Loading GeoJSON data..."}
              </p>
              
              <p className="text-xs text-gray-400 mt-1">
                {Object.keys(userZipAssignments).length > 0 ? 
                  `${Object.values(userZipAssignments).flat().length} ZIP codes assigned to ${Object.keys(userZipAssignments).length} team members.` :
                  "No ZIP code assignments found."}
              </p>
              
              {/* Source path info */}
              <p className="text-xs text-gray-500 mt-1 truncate">
                Source: {getGeoJsonPath()}
              </p>
            </div>
            
            {/* Debug message if any */}
            {debugMessage && (
              <div className="mt-2 p-2 bg-gray-800 rounded-md border border-gray-700">
                <p className="text-xs text-yellow-400 font-medium mb-1">Debug Information:</p>
                <pre className="text-xs text-gray-300 whitespace-pre-wrap">{debugMessage}</pre>
                
                <button
                  onClick={useFallbackGeoJson}
                  className="mt-2 bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs w-full"
                >
                  Try Illinois ZIP Codes Fallback
                </button>
              </div>
            )}
            
            {/* Integration status */}
            <div className="mt-2 p-2 bg-gray-800 rounded-md border border-gray-700">
              <details>
                <summary className="text-xs text-gray-400 cursor-pointer hover:text-gray-300">
                  Integration Status
                </summary>
                <div className="mt-2 space-y-1">
                  <p className="text-xs text-gray-400">
                    MapDisplay Integration: {typeof window !== 'undefined' && window.mapDisplayGeoJsonData ? '✅ Connected' : '❌ Not detected'}
                  </p>
                  <p className="text-xs text-gray-400">
                    Path Detection: {typeof window !== 'undefined' && window.zipCodeGeoJsonPath ? '✅ Path found' : '❌ No path set'}
                  </p>
                  <p className="text-xs text-gray-400">
                    ZIP Code Data: {zipCodeData ? `✅ ${zipCodeData.features?.length || 0} features` : '❌ Not loaded'}
                  </p>
                </div>
              </details>
            </div>
            
            {/* Permissions Notice (for non-admins) */}
            {!canAssignZones && (
              <div className="mt-2 p-2 bg-gray-800 rounded-md border border-gray-700">
                <p className="text-xs text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline-block mr-1 mb-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                  Only administrators can assign territories. You can view the current assignments, but cannot make changes.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default TeamZones;