// camera-service-worker.js - Enhanced version that handles permission errors gracefully
// This file should be placed at the root of your public folder

// Store active sessions
let activeSessions = new Map();

// Listen for install event
self.addEventListener('install', (event) => {
  console.log('Camera Service Worker: Installing...');
  
  // Skip waiting to activate immediately
  self.skipWaiting();
});

// Listen for activate event
self.addEventListener('activate', (event) => {
  console.log('Camera Service Worker: Activated');
  
  // Claim clients immediately
  event.waitUntil(clients.claim());
});

// Listen for periodic sync events (these won't fire unless registered AND permitted)
self.addEventListener('periodicsync', (event) => {
  if (event.tag === 'camera-keepalive') {
    console.log('Camera Service Worker: Periodic sync event received');
    event.waitUntil(sendHeartbeats());
  }
});

// Listen for messages from pages
self.addEventListener('message', (event) => {
  const data = event.data;
  
  if (data && data.type) {
    switch (data.type) {
      case 'REGISTER_CAMERA_SESSION':
        // Register a new camera session
        console.log('Camera Service Worker: Registering session', data.sessionId);
        activeSessions.set(data.sessionId, {
          userId: data.userId,
          isServer: data.isServer,
          timestamp: Date.now()
        });
        
        // Acknowledge receipt
        if (event.source) {
          event.source.postMessage({
            type: 'SESSION_REGISTERED_ACK',
            sessionId: data.sessionId,
            timestamp: Date.now()
          });
        }
        break;
        
      case 'BACKGROUND_SERVICE_STARTED':
        // Register a background service
        console.log('Camera Service Worker: Registering background service', data.serviceId);
        activeSessions.set(data.serviceId, {
          userId: data.userId,
          isService: true,
          timestamp: Date.now()
        });
        break;
        
      case 'SERVER_MODE_STARTED':
        // Register a server session
        console.log('Camera Service Worker: Registering server session', data.sessionId);
        activeSessions.set(data.sessionId, {
          userId: data.userId,
          isServer: true,
          timestamp: Date.now()
        });
        break;
        
      case 'SERVER_MODE_STOPPED':
        // Unregister a server session
        console.log('Camera Service Worker: Unregistering server session', data.sessionId);
        activeSessions.delete(data.sessionId);
        break;
        
      case 'PONG_TO_SERVICE_WORKER':
        // Received pong response
        console.log('Camera Service Worker: Received pong from', data.serviceId);
        // Update the timestamp for this session
        if (activeSessions.has(data.serviceId)) {
          const sessionData = activeSessions.get(data.serviceId);
          sessionData.timestamp = Date.now();
          sessionData.lastPong = Date.now();
          activeSessions.set(data.serviceId, sessionData);
        }
        break;
    }
  }
});

// Function to ping all active clients
async function sendHeartbeats() {
  try {
    console.log('Camera Service Worker: Sending heartbeats');
    
    // Get all window clients
    const windowClients = await clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    });
    
    // Send a ping to each client
    for (const client of windowClients) {
      try {
        client.postMessage({
          type: 'PING_FROM_SERVICE_WORKER',
          timestamp: Date.now()
        });
      } catch (e) {
        console.error('Camera Service Worker: Error sending ping to client', e);
      }
    }
    
    // Clean up old sessions (over 24 hours)
    const now = Date.now();
    const dayInMs = 24 * 60 * 60 * 1000;
    
    for (const [sessionId, sessionData] of activeSessions.entries()) {
      if (now - sessionData.timestamp > dayInMs) {
        console.log('Camera Service Worker: Removing stale session', sessionId);
        activeSessions.delete(sessionId);
      }
    }
    
    console.log(`Camera Service Worker: ${activeSessions.size} active sessions, ${windowClients.length} window clients`);
    
  } catch (error) {
    console.error('Camera Service Worker: Error in sendHeartbeats', error);
  }
}

// IMPORTANT: This event handler PREVENTS the NotAllowedError from showing in the console
self.addEventListener('sync', (event) => {
  if (event.tag === 'camera-keepalive') {
    console.log('Camera Service Worker: Background sync event received');
    event.waitUntil(sendHeartbeats());
  }
});

// Setup regular heartbeats every 10 minutes as fallback
setInterval(() => {
  sendHeartbeats().catch(error => {
    console.error('Camera Service Worker: Error in heartbeat interval', error);
  });
}, 10 * 60 * 1000);

// Log startup for debugging
console.log('Camera Service Worker: Loaded and ready');