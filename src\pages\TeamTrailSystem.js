import React, { useEffect, useState, useRef, useCallback } from 'react';
import L from 'leaflet';

const TeamTrailSystem = ({ 
  map, 
  teamMembers = [],
  onlineUsers = [],
  currentUser,
  isAdmin,
  userDisplayNames = {},
  userProfilePictures = {},
  confirmDeleteTrail,
  clearTrail
}) => {
  // State to track user markers and trails
  const [userMarkers, setUserMarkers] = useState({});
  const [userTrails, setUserTrails] = useState({});
  const [trailColors, setTrailColors] = useState({});
  
  // Trail visibility and time range controls
  const [showTrails, setShowTrails] = useState(true);
  const [trailTimeRange, setTrailTimeRange] = useState('24h');
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [showControlPanel, setShowControlPanel] = useState(false);
  const [showAllUsers, setShowAllUsers] = useState(true);
  
  // Refs to maintain Leaflet objects between renders
  const userMarkersRef = useRef({});
  const userTrailsRef = useRef({});
  const trailPointsRef = useRef({});
  
  // CRITICAL FIX: Track last positions with lower threshold
  const lastKnownPositionsRef = useRef({});
  
  // Generate a unique color for each user
  const generateUserColor = (userId) => {
    const colors = [
      '#FF5733', '#33FF57', '#3357FF', '#F033FF', '#FF33F0', '#33FFF0',
      '#F0FF33', '#FF8333', '#8333FF', '#33FF83', '#FF3383', '#3383FF'
    ];
    
    const charSum = userId.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
    const colorIndex = charSum % colors.length;
    return colors[colorIndex];
  };
  
  // Initialize trail colors for all team members
  useEffect(() => {
    const newColors = {};
    
    // Process all team members (includes location data)
    if (teamMembers && teamMembers.length > 0) {
      teamMembers.forEach(member => {
        const userId = member.uid || member.id || (typeof member === 'string' ? member : null);
        if (userId && !trailColors[userId]) {
          // Use provided trail color or generate one
          newColors[userId] = member.trailColor || generateUserColor(userId);
        }
      });
    }
    
    // Process online users
    if (onlineUsers && onlineUsers.length > 0) {
      onlineUsers.forEach(user => {
        if (user.uid && !trailColors[user.uid] && !newColors[user.uid]) {
          newColors[user.uid] = user.trailColor || generateUserColor(user.uid);
        }
      });
    }
    
    if (Object.keys(newColors).length > 0) {
      setTrailColors(prev => ({ ...prev, ...newColors }));
    }
  }, [teamMembers, onlineUsers, trailColors]);
  
  // CRITICAL FIX: Lower threshold for position changes (0.5 meters instead of 1 meter)
  const hasLocationChanged = useCallback((userId, newLocation) => {
    if (!newLocation || !newLocation.lat || !newLocation.lng) return false;
    
    const lastPos = lastKnownPositionsRef.current[userId];
    if (!lastPos) return true; // First time seeing this user
    
    // LOWERED THRESHOLD: 0.000005 degrees ≈ 0.5 meters
    const latDiff = Math.abs(newLocation.lat - lastPos.lat);
    const lngDiff = Math.abs(newLocation.lng - lastPos.lng);
    const hasChanged = latDiff > 0.000005 || lngDiff > 0.000005;
    
    if (hasChanged) {
      console.log(`📍 User ${userId} moved:`, {
        from: `${lastPos.lat.toFixed(6)}, ${lastPos.lng.toFixed(6)}`,
        to: `${newLocation.lat.toFixed(6)}, ${newLocation.lng.toFixed(6)}`,
        distance: Math.sqrt(latDiff * latDiff + lngDiff * lngDiff) * 111320 // rough meters
      });
    }
    
    return hasChanged;
  }, []);
  
  // CRITICAL FIX: Update user marker and trail with better data handling
  const updateUserMarkerAndTrail = useCallback((userData) => {
    if (!map || !userData) return;
    
    // Handle different data structures
    const userId = userData.uid || userData.id;
    const location = userData.currentLocation || userData.location;
    
    if (!userId || !location || !location.lat || !location.lng) {
      console.warn("Invalid user data for marker update:", userData);
      return;
    }
    
    // Check if location actually changed
    if (!hasLocationChanged(userId, location)) {
      return; // Skip update if location hasn't changed significantly
    }
    
    // Update last known position
    lastKnownPositionsRef.current[userId] = {
      lat: location.lat,
      lng: location.lng,
      timestamp: Date.now()
    };
    
    const userName = userDisplayNames[userId] || userData.displayName || userData.email || 'Unknown User';
    const profilePic = userProfilePictures[userId] || userData.profilePicture;
    const isCurrentUser = currentUser && userId === currentUser.uid;
    const userColor = trailColors[userId] || userData.trailColor || generateUserColor(userId);
    
    // Update or create marker
    let marker = userMarkersRef.current[userId];
    
    if (marker) {
      // Update existing marker position
      try {
        marker.setLatLng([location.lat, location.lng]);
        console.log(`✅ Updated marker for ${userName} to ${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}`);
        
        // Update heading if available
        if (location.heading !== undefined && location.heading !== null) {
          const element = marker.getElement();
          if (element) {
            const headingIndicator = element.querySelector('.heading-indicator');
            if (headingIndicator) {
              headingIndicator.style.transform = `rotate(${location.heading}deg)`;
            }
          }
        }
      } catch (error) {
        console.error(`❌ Error updating marker for ${userId}:`, error);
        // Recreate marker if update failed
        marker = null;
      }
    }
    
    if (!marker) {
      // Create new marker with enhanced styling
      const userIcon = L.divIcon({
        className: 'team-member-marker',
        html: `
          <div class="team-member-marker-inner ${isCurrentUser ? 'current-user' : ''}" style="background-color: ${userColor};">
            ${profilePic ? 
              `<img src="${profilePic}" alt="${userName}" class="user-avatar" />` : 
              `<div class="user-initials">${userName.substring(0, 2).toUpperCase()}</div>`
            }
            ${userData.online !== false ? 
              `<div class="online-indicator" style="position: absolute; bottom: 2px; right: 2px; width: 8px; height: 8px; background-color: #10B981; border-radius: 50%; border: 2px solid white;"></div>` : 
              ''
            }
          </div>
          ${location.heading !== undefined && location.heading !== null ? 
            `<div class="heading-indicator" style="position: absolute; top: -8px; left: 50%; transform: rotate(${location.heading}deg); transform-origin: center; margin-left: -6px;">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="12" height="12">
                <path fill="#FFFFFF" d="M12,2L4,13h16L12,2z"/>
              </svg>
            </div>` : 
            ''
          }
          <div class="user-label">${userName}</div>
        `,
        iconSize: [40, 40],
        iconAnchor: [20, 40]
      });
      
      marker = L.marker([location.lat, location.lng], { 
        icon: userIcon,
        zIndexOffset: isCurrentUser ? 1000 : 500
      });
      
      // Add popup with user info and buttons
      const popupContent = `
        <div class="user-popup">
          <h3>${userName}</h3>
          ${isAdmin || isCurrentUser ? 
            `<button class="clear-trail-btn" data-user-id="${userId}">Clear Trail</button>` : 
            ''
          }
          <button class="toggle-trail-btn" data-user-id="${userId}">
            ${selectedUsers.includes(userId) ? 'Hide Trail' : 'Show Trail'}
          </button>
        </div>
      `;
      
      marker.bindPopup(popupContent);
      
      // Add event listener to the popup
      marker.on('popupopen', (e) => {
        setTimeout(() => {
          const clearBtn = document.querySelector(`.clear-trail-btn[data-user-id="${userId}"]`);
          if (clearBtn) {
            clearBtn.addEventListener('click', () => {
              if (typeof confirmDeleteTrail === 'function') {
                confirmDeleteTrail(userId);
              }
              marker.closePopup();
            });
          }
          
          const toggleBtn = document.querySelector(`.toggle-trail-btn[data-user-id="${userId}"]`);
          if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
              setSelectedUsers(prev => {
                if (prev.includes(userId)) {
                  return prev.filter(id => id !== userId);
                } else {
                  return [...prev, userId];
                }
              });
              setShowAllUsers(false);
              marker.closePopup();
            });
          }
        }, 10);
      });
      
      // Add to map
      marker.addTo(map);
      userMarkersRef.current[userId] = marker;
      
      console.log(`🆕 Created new marker for ${userName} at ${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}`);
    }
    
    // Update the trail for this user
    updateUserTrail(userId, location, userColor);
  }, [map, userDisplayNames, userProfilePictures, currentUser, isAdmin, confirmDeleteTrail, selectedUsers, hasLocationChanged, trailColors]);
  
  // CRITICAL FIX: Process team members and online users separately for better data handling
  useEffect(() => {
    if (!map) return;
    
    const processedUsers = new Map();
    
    // Process team members first (they have location data)
    if (teamMembers && teamMembers.length > 0) {
      console.log(`🔄 Processing ${teamMembers.length} team members for marker updates`);
      
      teamMembers.forEach(member => {
        // Handle different data structures
        const userId = member.uid || member.id || (typeof member === 'string' ? member : null);
        const location = member.location || member.currentLocation;
        
        if (userId && location && location.lat && location.lng) {
          const userData = {
            uid: userId,
            currentLocation: location,
            displayName: member.displayName || userDisplayNames[userId],
            profilePicture: member.profilePicture || userProfilePictures[userId],
            online: member.online !== false,
            trailColor: member.trailColor
          };
          
          updateUserMarkerAndTrail(userData);
          processedUsers.set(userId, true);
        }
      });
    }
    
    // Then process online users (might have additional/updated data)
    if (onlineUsers && onlineUsers.length > 0) {
      console.log(`🔄 Processing ${onlineUsers.length} online users for marker updates`);
      
      onlineUsers.forEach(user => {
        if (user.uid && user.currentLocation && !processedUsers.has(user.uid)) {
          updateUserMarkerAndTrail(user);
          processedUsers.set(user.uid, true);
        }
      });
    }
    
    // Clean up markers for users who are no longer in the data
    const allUserIds = new Set([
      ...teamMembers.map(m => m.uid || m.id || m).filter(Boolean),
      ...onlineUsers.map(u => u.uid).filter(Boolean)
    ]);
    
    Object.keys(userMarkersRef.current).forEach(userId => {
      if (!allUserIds.has(userId)) {
        console.log(`🗑️ Removing marker for offline/removed user: ${userId}`);
        const marker = userMarkersRef.current[userId];
        if (marker) {
          try {
            map.removeLayer(marker);
          } catch (error) {
            console.warn("Error removing user marker:", error);
          }
          delete userMarkersRef.current[userId];
          delete lastKnownPositionsRef.current[userId];
        }
      }
    });
    
  }, [map, teamMembers, onlineUsers, updateUserMarkerAndTrail, userDisplayNames, userProfilePictures]);
  
  // ADDITIONAL FIX: High-frequency position check for real-time updates
  useEffect(() => {
    if (!map || (!teamMembers.length && !onlineUsers.length)) return;
    
    const intervalId = setInterval(() => {
      // Check both teamMembers and onlineUsers for position changes
      const allUsers = [
        ...teamMembers.filter(m => m.location || m.currentLocation),
        ...onlineUsers.filter(u => u.currentLocation)
      ];
      
      allUsers.forEach(userData => {
        const userId = userData.uid || userData.id;
        const location = userData.location || userData.currentLocation;
        
        if (userId && location) {
          const lastPos = lastKnownPositionsRef.current[userId];
          if (!lastPos || 
              Math.abs(location.lat - lastPos.lat) > 0.000005 || 
              Math.abs(location.lng - lastPos.lng) > 0.000005) {
            
            console.log(`⏰ Interval detected location change for user ${userId}`);
            updateUserMarkerAndTrail(userData);
          }
        }
      });
    }, 1000); // Check every second for real-time updates
    
    return () => clearInterval(intervalId);
  }, [map, teamMembers, onlineUsers, updateUserMarkerAndTrail]);
  
  // Get timestamp for the current time range selection
  const getTimeRangeTimestamp = () => {
    const now = Date.now();
    switch (trailTimeRange) {
      case '8h': return now - (8 * 60 * 60 * 1000);
      case '24h': return now - (24 * 60 * 60 * 1000);
      case '1w': return now - (7 * 24 * 60 * 60 * 1000);
      case '1m': return now - (30 * 24 * 60 * 60 * 1000);
      default: return now - (24 * 60 * 60 * 1000);
    }
  };
  
  // Format time range for display
  const formatTimeRange = (range) => {
    switch (range) {
      case '8h': return '8 Hours';
      case '24h': return '24 Hours';
      case '1w': return '1 Week';
      case '1m': return '1 Month';
      default: return range;
    }
  };
  
  // Update a user's trail with their new position
  const updateUserTrail = (userId, location, userColor) => {
    if (!map || !location || !location.lat || !location.lng) return;
    
    // Get or create points array for this user's trail
    let trailPoints = trailPointsRef.current[userId] || [];
    
    // Add new point with timestamp
    const newPoint = {
      lat: location.lat,
      lng: location.lng,
      timestamp: location.timestamp || Date.now()
    };
    
    // Only add if it's different from the last point
    const lastPoint = trailPoints[trailPoints.length - 1];
    if (!lastPoint || 
        Math.abs(lastPoint.lat - newPoint.lat) > 0.000001 || 
        Math.abs(lastPoint.lng - newPoint.lng) > 0.000001) {
      
      trailPoints.push(newPoint);
      console.log(`📍 Added trail point for ${userId}: ${newPoint.lat.toFixed(6)}, ${newPoint.lng.toFixed(6)}`);
    }
    
    // Limit trail points to prevent memory issues (keep last 1000 points)
    if (trailPoints.length > 1000) {
      trailPoints.splice(0, trailPoints.length - 1000);
    }
    
    // Store updated points
    trailPointsRef.current[userId] = trailPoints;
    
    // Only update trail display if trails are enabled
    if (showTrails) {
      refreshUserTrail(userId, userColor);
    }
  };
  
  // Refresh a user's trail based on current time settings
  const refreshUserTrail = (userId, userColor) => {
    if (!map || !userId || !trailPointsRef.current[userId]) return;
    
    const allPoints = trailPointsRef.current[userId];
    
    // Filter points by time range
    const minTimestamp = getTimeRangeTimestamp();
    const filteredPoints = allPoints.filter(point => point.timestamp >= minTimestamp);
    
    // Check if this user's trail should be visible based on selection
    const shouldShowUserTrail = showAllUsers || selectedUsers.includes(userId);
    
    // Only create/update trail if we have at least 2 points and should show this user's trail
    if (filteredPoints.length >= 2 && shouldShowUserTrail) {
      let trail = userTrailsRef.current[userId];
      const points = filteredPoints.map(point => L.latLng(point.lat, point.lng));
      
      if (trail) {
        // Update existing trail
        trail.setLatLngs(points);
        trail.setStyle({ color: userColor || trailColors[userId] || generateUserColor(userId) });
      } else {
        // Create new trail with user's color
        const color = userColor || trailColors[userId] || generateUserColor(userId);
        
        trail = L.polyline(points, {
          color: color,
          weight: 3,
          opacity: 0.7,
          lineJoin: 'round',
          dashArray: '5, 10',
          className: 'user-trail'
        }).addTo(map);
        
        // Add tooltip with username
        const userName = userDisplayNames[userId] || userId;
        trail.bindTooltip(`${userName}'s Trail`, {sticky: true});
        
        userTrailsRef.current[userId] = trail;
        setUserTrails(prev => ({ ...prev, [userId]: trail }));
        
        console.log(`✅ Created trail for ${userName} with ${points.length} points in color ${color}`);
      }
    } else if (userTrailsRef.current[userId] && (!shouldShowUserTrail || filteredPoints.length < 2)) {
      // Remove trail if it shouldn't be visible or doesn't have enough points
      try {
        map.removeLayer(userTrailsRef.current[userId]);
      } catch (error) {
        console.warn(`Error removing trail for user ${userId}:`, error);
      }
      
      delete userTrailsRef.current[userId];
      setUserTrails(prev => {
        const updated = { ...prev };
        delete updated[userId];
        return updated;
      });
    }
  };
  
  // Refresh all trails based on current settings
  const refreshAllTrails = () => {
    Object.keys(trailPointsRef.current).forEach(userId => {
      const userColor = trailColors[userId] || generateUserColor(userId);
      refreshUserTrail(userId, userColor);
    });
  };
  
  // Effect to refresh trails when settings change
  useEffect(() => {
    refreshAllTrails();
  }, [showTrails, trailTimeRange, showAllUsers, selectedUsers]);
  
  // State for confirmation dialog
  const [showClearConfirmation, setShowClearConfirmation] = useState(false);
  
  // Clear all trails
  const clearAllTrails = () => {
    Object.entries(userTrailsRef.current).forEach(([userId, trail]) => {
      try {
        if (map && trail) {
          map.removeLayer(trail);
        }
      } catch (error) {
        console.warn(`Error removing trail for user ${userId}:`, error);
      }
    });
    
    userTrailsRef.current = {};
    trailPointsRef.current = {};
    setUserTrails({});
    
    if (typeof clearTrail === 'function') {
      clearTrail();
    }
    
    setShowClearConfirmation(false);
  };
  
  // Show confirmation dialog before clearing all trails
  const promptClearAllTrails = () => {
    setShowClearConfirmation(true);
  };
  
  // Clear a specific user's trail
  const clearUserTrail = (userId) => {
    if (!userId) return;
    
    const trail = userTrailsRef.current[userId];
    if (map && trail) {
      try {
        map.removeLayer(trail);
      } catch (error) {
        console.warn(`Error removing trail for user ${userId}:`, error);
      }
      
      delete userTrailsRef.current[userId];
      delete trailPointsRef.current[userId];
      
      setUserTrails(prev => {
        const updated = { ...prev };
        delete updated[userId];
        return updated;
      });
    }
  };
  
  // Toggle trail visibility for a specific user
  const toggleUserTrail = (userId) => {
    setSelectedUsers(prev => {
      if (prev.includes(userId)) {
        return prev.filter(id => id !== userId);
      } else {
        return [...prev, userId];
      }
    });
    setShowAllUsers(false);
  };
  
  // Toggle all users trails
  const toggleAllUsersTrails = () => {
    setShowAllUsers(!showAllUsers);
  };
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clean up all markers
      Object.values(userMarkersRef.current).forEach(marker => {
        if (map && marker) {
          try {
            map.removeLayer(marker);
          } catch (error) {
            console.warn("Error removing marker during cleanup:", error);
          }
        }
      });
      
      // Clean up all trails
      Object.values(userTrailsRef.current).forEach(trail => {
        if (map && trail) {
          try {
            map.removeLayer(trail);
          } catch (error) {
            console.warn("Error removing trail during cleanup:", error);
          }
        }
      });
    };
  }, [map]);
  
  // DEBUGGING: Expose debugging functions
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.debugTeamTrails = () => {
        console.log('🔍 Team Trail Debug Info:', {
          teamMembersCount: teamMembers.length,
          onlineUsersCount: onlineUsers.length,
          markersCount: Object.keys(userMarkersRef.current).length,
          trailsCount: Object.keys(userTrailsRef.current).length,
          trailPointsCount: Object.keys(trailPointsRef.current).map(userId => ({
            userId,
            points: trailPointsRef.current[userId]?.length || 0
          })),
          lastKnownPositions: lastKnownPositionsRef.current,
          trailColors: trailColors,
          teamMembers: teamMembers.map(m => ({
            uid: m.uid || m.id,
            hasLocation: !!(m.location || m.currentLocation),
            location: (m.location || m.currentLocation) ? 
              `${(m.location || m.currentLocation).lat?.toFixed(6)}, ${(m.location || m.currentLocation).lng?.toFixed(6)}` : 
              'none'
          })),
          onlineUsers: onlineUsers.map(u => ({
            uid: u.uid,
            hasLocation: !!(u.currentLocation?.lat && u.currentLocation?.lng),
            location: u.currentLocation ? `${u.currentLocation.lat.toFixed(6)}, ${u.currentLocation.lng.toFixed(6)}` : 'none'
          }))
        });
      };
      
      window.forceUpdateAllMarkers = () => {
        console.log('🔄 Forcing update of all markers...');
        // Clear last known positions to force updates
        lastKnownPositionsRef.current = {};
        
        // Process all users
        const allUsers = [
          ...teamMembers.filter(m => m.location || m.currentLocation),
          ...onlineUsers.filter(u => u.currentLocation)
        ];
        
        allUsers.forEach(userData => {
          updateUserMarkerAndTrail(userData);
        });
      };
      
      window.clearAllTeamTrails = clearAllTrails;
      window.clearUserTrail = clearUserTrail;
      window.toggleUserTrail = toggleUserTrail;
      window.setTrailTimeRange = (range) => {
        if (['8h', '24h', '1w', '1m'].includes(range)) {
          setTrailTimeRange(range);
        }
      };
      window.toggleTrailVisibility = () => setShowTrails(!showTrails);
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        delete window.debugTeamTrails;
        delete window.forceUpdateAllMarkers;
        delete window.clearAllTeamTrails;
        delete window.clearUserTrail;
        delete window.toggleUserTrail;
        delete window.setTrailTimeRange;
        delete window.toggleTrailVisibility;
      }
    };
  }, [teamMembers, onlineUsers, updateUserMarkerAndTrail, clearAllTrails, showTrails, clearUserTrail, trailColors]);
  
  // Add CSS styles for trail system UI
  useEffect(() => {
    const styleEl = document.createElement('style');
    styleEl.id = 'team-trail-system-styles';
    styleEl.textContent = `
      .team-member-marker-inner {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #374151;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        overflow: hidden;
        position: relative;
      }
      
      .team-member-marker-inner.current-user {
        border-color: #10B981;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.5), 0 2px 4px rgba(0,0,0,0.3);
      }
      
      .user-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .user-initials {
        color: white;
        font-weight: bold;
        font-size: 14px;
      }
      
      .user-label {
        background-color: rgba(0,0,0,0.7);
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 12px;
        white-space: nowrap;
        position: absolute;
        bottom: -18px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
      }
      
      .user-popup {
        padding: 4px 8px;
        min-width: 120px;
      }
      
      .user-popup h3 {
        margin: 0 0 8px 0;
        font-size: 14px;
        text-align: center;
      }
      
      .online-indicator {
        animation: online-pulse 2s infinite;
      }
      
      @keyframes online-pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }
      
      .heading-indicator {
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
      }
      
      .trail-controls {
        position: fixed;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        background: rgba(17, 24, 39, 0.9);
        border-radius: 8px;
        padding: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(75, 85, 99, 0.5);
        display: flex;
        flex-direction: column;
      }
      
      .trail-controls-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }
      
      .trail-controls-title {
        color: white;
        font-weight: bold;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;
      }
      
      .trail-controls-toggle {
        background-color: transparent;
        border: none;
        color: rgba(156, 163, 175, 0.8);
        cursor: pointer;
        font-size: 18px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
      }
      
      .trail-controls-toggle:hover {
        color: white;
      }
      
      .trail-controls-body {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
      
      .trail-control-btn {
        background: rgba(55, 65, 81, 0.7);
        border: 1px solid rgba(75, 85, 99, 0.5);
        color: white;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: background-color 0.2s;
      }
      
      .trail-control-btn:hover {
        background: rgba(75, 85, 99, 0.9);
      }
      
      .trail-control-btn.active {
        background: rgba(59, 130, 246, 0.6);
        border-color: rgba(59, 130, 246, 0.8);
      }
      
      .trail-control-btn.active:hover {
        background: rgba(59, 130, 246, 0.8);
      }
      
      .user-list {
        margin-top: 8px;
        max-height: 150px;
        overflow-y: auto;
        width: 100%;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 4px;
      }
      
      .user-list::-webkit-scrollbar {
        width: 4px;
      }
      
      .user-list::-webkit-scrollbar-track {
        background: rgba(31, 41, 55, 0.5);
      }
      
      .user-list::-webkit-scrollbar-thumb {
        background: rgba(75, 85, 99, 0.8);
        border-radius: 2px;
      }
      
      .user-list-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #E5E7EB;
        padding: 4px;
        border-radius: 4px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .user-list-item:hover {
        background: rgba(55, 65, 81, 0.7);
      }
      
      .user-list-item.selected {
        background: rgba(59, 130, 246, 0.3);
      }
      
      .user-color-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        flex-shrink: 0;
      }
      
      .trail-controls-collapsed {
        position: fixed;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        background: rgba(17, 24, 39, 0.8);
        border-radius: 8px;
        padding: 6px 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(75, 85, 99, 0.3);
        cursor: pointer;
      }
      
      .trail-controls-collapsed:hover {
        background: rgba(31, 41, 55, 0.9);
      }
      
      @media (max-width: 640px) {
        .trail-controls {
          width: 90%;
          max-width: 320px;
        }
      }
      
      .clear-trail-btn, .toggle-trail-btn {
        background: #4B5563;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        margin-top: 4px;
        width: 100%;
      }
      
      .clear-trail-btn:hover {
        background: #EF4444;
      }
      
      .toggle-trail-btn:hover {
        background: #3B82F6;
      }
    `;
    document.head.appendChild(styleEl);
    
    return () => {
      const existingStyle = document.getElementById('team-trail-system-styles');
      if (existingStyle) {
        document.head.removeChild(existingStyle);
      }
    };
  }, []);
  
  // Get all users (combine team members and online users)
  const getAllUsers = () => {
    const userMap = new Map();
    
    // Add team members
    teamMembers.forEach(member => {
      const userId = member.uid || member.id || (typeof member === 'string' ? member : null);
      if (userId) {
        userMap.set(userId, {
          userId,
          displayName: member.displayName || userDisplayNames[userId] || userId,
          online: member.online !== false,
          color: trailColors[userId] || member.trailColor || generateUserColor(userId)
        });
      }
    });
    
    // Add/update with online users
    onlineUsers.forEach(user => {
      if (user.uid) {
        userMap.set(user.uid, {
          userId: user.uid,
          displayName: user.displayName || userDisplayNames[user.uid] || user.uid,
          online: true,
          color: trailColors[user.uid] || user.trailColor || generateUserColor(user.uid)
        });
      }
    });
    
    return Array.from(userMap.values());
  };
  
  // Render the trail control UI
  return (
    <>
      {showControlPanel ? (
        <div className="trail-controls">
          <div className="trail-controls-header">
            <div className="trail-controls-title">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M18 6H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h13l4-3.5L18 6Z"></path>
                <path d="M12 13v9"></path>
                <path d="M12 2v4"></path>
              </svg>
              Team Trails
            </div>
            <button 
              className="trail-controls-toggle" 
              onClick={() => setShowControlPanel(false)}
              title="Minimize"
            >
              -
            </button>
          </div>
          
          <div className="trail-controls-body">
            <button 
              className={`trail-control-btn ${showTrails ? 'active' : ''}`}
              onClick={() => setShowTrails(!showTrails)}
            >
              {showTrails ? 'Hide Trails' : 'Show Trails'}
            </button>
            
            {showTrails && (
              <>
                <div className="flex gap-1">
                  <button 
                    className={`trail-control-btn ${trailTimeRange === '8h' ? 'active' : ''}`}
                    onClick={() => setTrailTimeRange('8h')}
                  >
                    8h
                  </button>
                  <button 
                    className={`trail-control-btn ${trailTimeRange === '24h' ? 'active' : ''}`}
                    onClick={() => setTrailTimeRange('24h')}
                  >
                    24h
                  </button>
                  <button 
                    className={`trail-control-btn ${trailTimeRange === '1w' ? 'active' : ''}`}
                    onClick={() => setTrailTimeRange('1w')}
                  >
                    1w
                  </button>
                  <button 
                    className={`trail-control-btn ${trailTimeRange === '1m' ? 'active' : ''}`}
                    onClick={() => setTrailTimeRange('1m')}
                  >
                    1m
                  </button>
                </div>
                
                <button 
                  className={`trail-control-btn ${showAllUsers ? 'active' : ''}`}
                  onClick={toggleAllUsersTrails}
                >
                  {showAllUsers ? 'All Users' : 'Selected Users'}
                </button>
                
                {isAdmin && (
                  <button 
                    className="trail-control-btn"
                    onClick={promptClearAllTrails}
                    title="Clear all trails"
                  >
                    Clear All
                  </button>
                )}
              </>
            )}
          </div>
          
          {showTrails && !showAllUsers && (
            <div className="user-list">
              {getAllUsers().map(user => {
                const isSelected = selectedUsers.includes(user.userId);
                
                return (
                  <div 
                    key={user.userId}
                    className={`user-list-item ${isSelected ? 'selected' : ''}`}
                    onClick={() => toggleUserTrail(user.userId)}
                    title={`${isSelected ? 'Hide' : 'Show'} ${user.displayName}'s trail`}
                  >
                    <div className="user-color-dot" style={{ backgroundColor: user.color }}></div>
                    <div className="truncate">{user.displayName}</div>
                    {user.online && (
                      <div className="w-2 h-2 bg-green-500 rounded-full ml-1 animate-pulse" title="Online"></div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      ) : (
        <div 
          className="trail-controls-collapsed"
          onClick={() => setShowControlPanel(true)}
        >
          <div className="text-white text-xs font-medium flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 6H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h13l4-3.5L18 6Z"></path>
              <path d="M12 13v9"></path>
              <path d="M12 2v4"></path>
            </svg>
            Trails: {showTrails ? formatTimeRange(trailTimeRange) : 'Off'}
          </div>
        </div>
      )}
      
      {/* Confirmation Dialog for Clear All Trails */}
      {showClearConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[2000]">
          <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg max-w-md p-4">
            <h3 className="text-lg font-semibold text-white mb-2">Confirm Clear All Trails</h3>
            <p className="text-gray-300 mb-4">
              Are you sure you want to clear all user trails? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button 
                onClick={() => setShowClearConfirmation(false)}
                className="px-4 py-2 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button 
                onClick={clearAllTrails}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Clear All Trails
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TeamTrailSystem;