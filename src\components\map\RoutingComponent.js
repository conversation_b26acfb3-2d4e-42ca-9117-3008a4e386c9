import React, { useContext, useEffect, useRef } from 'react';
// In all components
import { MapContext } from '../MapContext';
import L from 'leaflet';

const RoutingComponent = () => {
  const {
    mapRef,
    currentLocation,
    selectedLocation,
    isNavigating,
    setIsNavigating,
    optimizedRoute,
    isFollowingOptimizedRoute,
    setSelectedLocation,
    setDetailsPanelLocation
  } = useContext(MapContext);
  
  // Local state refs for managing routing UI
  const routingControlRef = useRef(null);
  const navigationElementsRef = useRef([]);
  const nextDirectionStepRef = useRef("");
  const distanceToDestinationRef = useRef(null);
  const estimatedTimeRef = useRef(null);
  
  // Effect for navigation state
  useEffect(() => {
    if (!mapRef.current || !isNavigating || !selectedLocation) return;
    
    // Calculate and display the road-based route
    calculateAndDisplayRoute(currentLocation, selectedLocation.position);
    
    // Clean up function
    return () => {
      cleanup();
    };
  }, [isNavigating, selectedLocation, currentLocation]);
  
  // Function to calculate and display route
  const calculateAndDisplayRoute = (origin, destination) => {
    // Clean up existing controls
    cleanup();
    
    // Create routing control
    try {
      if (!mapRef.current) return;
      
      // Create routing control
      const routingControl = L.Routing.control({
        waypoints: [
          L.latLng(origin.lat, origin.lng),
          L.latLng(destination.lat, destination.lng)
        ],
        routeWhileDragging: false,
        showAlternatives: false,
        lineOptions: {
          styles: [
            {color: '#3B82F6', opacity: 0.8, weight: 5}
          ],
          addWaypoints: false,
          extendToWaypoints: true,
          missingRouteTolerance: 0
        },
        createMarker: function() { return null; }, // Suppress default markers
        fitSelectedRoutes: false
      }).addTo(mapRef.current);
      
      routingControlRef.current = routingControl;
      
      // Listen for route calculation
      routingControl.on('routesfound', function(e) {
        try {
          const routes = e.routes;
          const summary = routes[0].summary;
          
          // Extract useful information
          const distance = (summary.totalDistance / 1609.34).toFixed(1); // convert meters to miles
          const duration = formatTime(summary.totalTime);
          const steps = routes[0].instructions || [];
          
          // Save to state refs
          distanceToDestinationRef.current = summary.totalDistance / 1609.34; // convert meters to miles
          estimatedTimeRef.current = summary.totalTime; // in seconds
          
          // Set next direction step
          if (steps && steps.length > 0) {
            nextDirectionStepRef.current = steps[0].text;
          }
          
          // Create navigation panel with information
          createNavigationPanel({
            distance,
            duration,
            instructions: steps
          });
          
        } catch (err) {
          console.error("Error processing route:", err);
          fallbackToDirectRoute(origin, destination);
        }
      });
      
      // Handle routing errors
      routingControl.on('routingerror', function(e) {
        console.error("Routing error:", e);
        fallbackToDirectRoute(origin, destination);
      });
      
    } catch (err) {
      console.error("Error creating routing control:", err);
      fallbackToDirectRoute(origin, destination);
    }
  };
  
  // Create navigation panel with route information
  const createNavigationPanel = (routeInfo) => {
    if (!mapRef.current) return;
    
    const navPanel = document.createElement('div');
    navPanel.className = 'navigation-panel dark-mode';
    
    // Determine image to show
    let imageUrl = '';
    if (selectedLocation && selectedLocation.images && selectedLocation.images.length > 0) {
      imageUrl = selectedLocation.images[0];
    }
    
    // Determine parking side info
    let parkingSideHtml = '';
    if (selectedLocation && selectedLocation.parkingSide) {
      const side = selectedLocation.parkingSide.charAt(0).toUpperCase() + selectedLocation.parkingSide.slice(1);
      const arrow = selectedLocation.parkingSide === 'left' ? '←' : '→';
      parkingSideHtml = `
        <div class="parking-side-indicator">
          Vehicle Parked on ${side} Side ${arrow}
        </div>
      `;
    }
    
    // Status indicator
    let statusHtml = '';
    if (selectedLocation && selectedLocation.status === 'picked-up') {
      statusHtml = `<div class="parking-side-indicator" style="background-color: #10B981;">
        Picked Up
      </div>`;
    }
    
    // Vehicle info section
    let vehicleInfoHtml = '';
    if (selectedLocation && (selectedLocation.make || selectedLocation.model || selectedLocation.plateNumber)) {
      vehicleInfoHtml = `
        <div class="vehicle-info" style="font-size: 10px; margin-top: 3px; margin-bottom: 3px;">
          ${selectedLocation.make && selectedLocation.model ? `${selectedLocation.make} ${selectedLocation.model}` : ''}
          ${selectedLocation.plateNumber ? `• Plate: ${selectedLocation.plateNumber}` : ''}
        </div>
      `;
    }
    
    // Generate HTML for the panel
    navPanel.innerHTML = `
      <div class="navigation-image">
        ${imageUrl ? `<img src="${imageUrl}" alt="Destination" style="width:100%; height:100%; object-fit:cover;">` : 
        `<div style="color: #9CA3AF; text-align: center;">No image available</div>`}
      </div>
      <div class="navigation-details">
        ${parkingSideHtml}
        ${statusHtml}
        <div class="navigation-destination">${selectedLocation?.name || 'Destination'}</div>
        ${vehicleInfoHtml}
        <div class="navigation-distance-time">
          <div>${routeInfo.distance} miles</div>
          <div>${routeInfo.duration}</div>
        </div>
        <div class="navigation-direction">
          <div class="navigation-direction-arrow">→</div>
          <div>${routeInfo.instructions[0]?.text || 'Proceed to destination'}</div>
        </div>
        
        <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm w-full mt-2">
          Stop Navigation
        </button>
      </div>
    `;
    
    // Add the panel to the map container
    const mapContainer = mapRef.current.getContainer();
    mapContainer.appendChild(navPanel);
    
    // Add event listener to stop button
    const stopButton = navPanel.querySelector('button');
    stopButton.addEventListener('click', stopNavigation);
    
    // Save reference for cleanup
    navigationElementsRef.current.push({
      remove: () => {
        if (navPanel.parentNode) {
          navPanel.parentNode.removeChild(navPanel);
        }
      }
    });
  };
  
  // Stop navigation function
  const stopNavigation = () => {
    // Clean up all routing elements
    cleanup();
    
    // Reset navigation state
    setIsNavigating(false);
    
    // Reset refs
    nextDirectionStepRef.current = "";
    distanceToDestinationRef.current = null;
    estimatedTimeRef.current = null;
  };
  
  // Cleanup function for routing elements
  const cleanup = () => {
    // Clean up routing control
    if (routingControlRef.current && mapRef.current) {
      try {
        mapRef.current.removeControl(routingControlRef.current);
      } catch (err) {
        console.warn("Error removing routing control:", err);
      }
      routingControlRef.current = null;
    }
    
    // Clean up navigation elements
    navigationElementsRef.current.forEach(element => {
      if (element && element.remove) {
        try {
          element.remove();
        } catch (err) {
          console.warn("Error removing navigation element:", err);
        }
      }
    });
    navigationElementsRef.current = [];
  };
  
  // Fallback to direct route when routing service fails
  const fallbackToDirectRoute = (origin, destination) => {
    if (!mapRef.current) return;
    
    try {
      // Calculate straight-line distance
      const distance = calculateDistance(origin, destination);
      
      // Draw a straight line
      const fallbackRoute = L.polyline([
        [origin.lat, origin.lng],
        [destination.lat, destination.lng]
      ], {
        color: '#3B82F6',
        weight: 5,
        opacity: 0.8,
        dashArray: '10, 10'
      }).addTo(mapRef.current);
      
      navigationElementsRef.current.push({
        remove: () => {
          if (fallbackRoute && mapRef.current) {
            fallbackRoute.remove();
          }
        }
      });
      
      // Update refs with estimated info
      distanceToDestinationRef.current = distance;
      const estimatedTimeSeconds = (distance / 40) * 3600; // 40 mph average speed
      estimatedTimeRef.current = estimatedTimeSeconds;
      nextDirectionStepRef.current = `Head toward destination`;
      
      // Create navigation panel with fallback info
      createNavigationPanel({
        distance: distance.toFixed(1),
        duration: formatTime(estimatedTimeSeconds),
        instructions: [{ text: "Head toward destination" }]
      });
      
    } catch (err) {
      console.error("Error creating fallback route:", err);
    }
  };
  
  // Helper function to format time
  const formatTime = (seconds) => {
    if (seconds < 60) {
      return `${Math.round(seconds)} seconds`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)} minutes`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.round((seconds % 3600) / 60);
      return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }
  };
  
  // Helper function to calculate distance
  const calculateDistance = (point1, point2) => {
    const R = 3958.8; // Earth's radius in miles
    const φ1 = point1.lat * Math.PI/180;
    const φ2 = point2.lat * Math.PI/180;
    const Δφ = (point2.lat-point1.lat) * Math.PI/180;
    const Δλ = (point2.lng-point1.lng) * Math.PI/180;
    
    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    
    return R * c; // in miles
  };
  
  // Nothing to render directly - this component manages the routing UI elements separately
  return null;
};

export default RoutingComponent;