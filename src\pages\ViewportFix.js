import { useEffect } from 'react';

function ViewportFix() {
  useEffect(() => {
    // Set proper viewport meta tag
    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
    
    // Remove any existing viewport meta tags
    const existingMeta = document.querySelector('meta[name="viewport"]');
    if (existingMeta) {
      existingMeta.parentNode.removeChild(existingMeta);
    }
    
    document.head.appendChild(meta);
    
    // Detect device type and add appropriate classes
    const detectDevice = () => {
      const isIPad = /iPad/.test(navigator.userAgent) || 
        (/Macintosh/.test(navigator.userAgent) && 'ontouchend' in document);
      const isIPhone = /iPhone|iPod/.test(navigator.userAgent);
      
      if (isIPad) {
        document.body.classList.add('ipad');
      }
      
      if (isIPhone) {
        document.body.classList.add('iphone');
      }
      
      if (isIPad || isIPhone) {
        document.body.classList.add('ios');
      }
      
      if (window.innerWidth <= 767) {
        document.body.classList.add('mobile');
      } else if (window.innerWidth <= 1024) {
        document.body.classList.add('tablet');
      } else {
        document.body.classList.add('desktop');
      }
    };
    
    // Fix for vh units in mobile browsers
    const setVhVariable = () => {
      // First get the viewport height and multiply it by 1% to get a value for 1vh unit
      const vh = window.innerHeight * 0.01;
      // Set the value in the --vh custom property
      document.documentElement.style.setProperty('--vh', `${vh}px`);
      
      // Also set app-height
      document.documentElement.style.setProperty('--app-height', `${window.innerHeight}px`);
      
      // Check orientation
      const isPortrait = window.innerHeight > window.innerWidth;
      if (isPortrait) {
        document.body.classList.add('portrait');
        document.body.classList.remove('landscape');
      } else {
        document.body.classList.add('landscape');
        document.body.classList.remove('portrait');
      }
    };
    
    // Initial setup
    detectDevice();
    setVhVariable();
    
    // Update on resize
    const handleResize = () => {
      setVhVariable();
    };
    
    // Handle iOS Safari resize issues
    const handleOrientationChange = () => {
      // Set a timeout to ensure the browser has completed any UI updates
      setTimeout(() => {
        setVhVariable();
        
        // Dispatch a custom event that components can listen for
        window.dispatchEvent(new CustomEvent('orientationChanged', {
          detail: { 
            isPortrait: window.innerHeight > window.innerWidth,
            width: window.innerWidth,
            height: window.innerHeight
          }
        }));
      }, 100);
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    return () => {
      if (meta.parentNode) meta.parentNode.removeChild(meta);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);
  
  return null;
}

export default ViewportFix;