import React, { useState } from 'react';
import { formatAddress, calculateDaysSinceLastCheckIn } from './utility-functions';
import { CoordinateEditor } from './ui-components';

const AddressComponent = ({ 
  addresses, 
  onAddressChange, 
  onAddressAdd, 
  onAddressDelete, 
  onAddressCheckIn, 
  readOnly = false,
  isGeocodingInProgress = false 
}) => {
  const [editingIndex, setEditingIndex] = useState(null);
  const [editValues, setEditValues] = useState({
    street: '',
    city: '',
    state: '',
    zip: ''
  });
  // State for coordinate editing
  const [editingCoordinates, setEditingCoordinates] = useState(null);

  const handleEdit = (index) => {
    const address = addresses[index];
    // Parse the address into components (assuming format: "street, city, state zip")
    let street = '', city = '', state = '', zip = '';
    
    if (address && address.street) {
      // Already in structured format
      setEditValues(address);
    } else if (address && typeof address === 'string') {
      // Parse from string format
      const parts = address.split(',');
      street = parts[0] || '';
      
      if (parts.length > 1) {
        const cityStateParts = parts[1].trim().split(' ');
        if (cityStateParts.length > 2) {
          city = cityStateParts.slice(0, -2).join(' ');
          state = cityStateParts[cityStateParts.length - 2];
          zip = cityStateParts[cityStateParts.length - 1];
        } else if (cityStateParts.length === 2) {
          city = cityStateParts[0];
          state = cityStateParts[1];
        } else {
          city = parts[1].trim();
        }
      }
      
      if (parts.length > 2) {
        const stateZipParts = parts[2].trim().split(' ');
        state = stateZipParts[0];
        zip = stateZipParts[1] || '';
      }
      
      setEditValues({ street, city, state, zip });
    } else {
      setEditValues({ street: '', city: '', state: '', zip: '' });
    }
    
    setEditingIndex(index);
  };

  const handleInputChange = (field, value) => {
    setEditValues(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    if (editValues.street.trim()) {
      onAddressChange(editingIndex, editValues);
    }
    setEditingIndex(null);
  };

  const handleCancel = () => {
    setEditingIndex(null);
  };

  // Handle manual coordinate editing
  const handleEditCoordinates = (index) => {
    setEditingCoordinates(index);
  };

  // Save manually edited coordinates
  const handleSaveCoordinates = (newCoordinates) => {
    const address = addresses[editingCoordinates];
    const updatedAddress = {
      ...address,
      position: newCoordinates
    };
    onAddressChange(editingCoordinates, updatedAddress);
    setEditingCoordinates(null);
  };

  // Cancel coordinate editing
  const handleCancelCoordinateEdit = () => {
    setEditingCoordinates(null);
  };

  // Check if check-in is needed (5 days since last check or no check-ins)
  const isCheckInNeeded = (address) => {
    if (!address.checkIns || address.checkIns.length === 0) return true;
    
    const lastCheckIn = address.checkIns[address.checkIns.length - 1].timestamp;
    const daysSinceLastCheckIn = calculateDaysSinceLastCheckIn(lastCheckIn);
    
    return daysSinceLastCheckIn === null || daysSinceLastCheckIn >= 5;
  };

  // Check if the address has valid coordinates
  const hasValidCoordinates = (address) => {
    return address?.position?.lat && address?.position?.lng;
  };

  return (
    <div className="space-y-2">
      {addresses.map((address, index) => (
        <div 
          key={index} 
          className="flex flex-col rounded-md p-2 animate-fadeIn transition-all duration-300
                    bg-gradient-to-r from-gray-700 to-gray-800
                    border border-blue-500/30 shadow-lg
                    hover:border-blue-400/50 hover:shadow-blue-900/20
                    hover:from-gray-700/90 hover:to-gray-800/90"
          style={{
            animation: `pulse 2s infinite ease-in-out, fadeIn 0.5s ease-in-out ${index * 0.1}s`,
            boxShadow: '0 0 8px rgba(59, 130, 246, 0.3)'
          }}
        >
          {editingIndex === index ? (
            <div className="flex-1 space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={editValues.street}
                  onChange={(e) => handleInputChange('street', e.target.value)}
                  className="flex-1 bg-gray-800 border border-gray-600 rounded px-2 py-1 text-sm text-white focus:outline-none focus:border-blue-500"
                  placeholder="Street address"
                  autoFocus
                />
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={editValues.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  className="flex-1 bg-gray-800 border border-gray-600 rounded px-2 py-1 text-sm text-white focus:outline-none focus:border-blue-500"
                  placeholder="City"
                />
                <input
                  type="text"
                  value={editValues.state}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  className="w-16 bg-gray-800 border border-gray-600 rounded px-2 py-1 text-sm text-white focus:outline-none focus:border-blue-500"
                  placeholder="State"
                />
                <input
                  type="text"
                  value={editValues.zip}
                  onChange={(e) => handleInputChange('zip', e.target.value)}
                  className="w-24 bg-gray-800 border border-gray-600 rounded px-2 py-1 text-sm text-white focus:outline-none focus:border-blue-500"
                  placeholder="ZIP"
                />
              </div>
              <div className="flex justify-end space-x-2 mt-2">
                <button 
                  onClick={handleSave}
                  className="text-green-400 hover:text-green-300 p-1 transition-colors duration-200 flex items-center"
                  title="Save"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Save
                </button>
                <button 
                  onClick={handleCancel}
                  className="text-red-400 hover:text-red-300 p-1 transition-colors duration-200 flex items-center"
                  title="Cancel"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l1.293 1.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  Cancel
                </button>
              </div>
            </div>
          ) : editingCoordinates === index ? (
            // Coordinate editor when editing coordinates
            <CoordinateEditor 
              position={address.position}
              onSave={handleSaveCoordinates}
              onCancel={handleCancelCoordinateEdit}
            />
          ) : (
            <>
              <div className="flex items-center justify-between">
                <div className="flex-1 text-sm text-gray-300">
                  {formatAddress(address)}
                  {/* GPS Coordinates Indicator */}
                  {hasValidCoordinates(address) ? (
                    <span className="ml-2 text-xs text-green-400 inline-flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                      GPS data
                    </span>
                  ) : (
                    <span className="ml-2 text-xs text-yellow-500 inline-flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      No GPS data
                    </span>
                  )}
                </div>
                <div className="flex space-x-1">
                  {!readOnly && (
                    <>
                      <button 
                        onClick={() => handleEdit(index)}
                        className="text-blue-400 hover:text-blue-300 p-1 transition-colors duration-200"
                        title="Edit Address"
                                              >
                                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                      </button>
                      <button 
                        onClick={() => onAddressDelete(index)}
                        className="text-red-400 hover:text-red-300 p-1 transition-colors duration-200"
                        title="Delete"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </>
                  )}
                  {address.checkIns && address.checkIns.length > 0 && (
                    <div className="flex items-center ml-2" title={`Last check-in: ${address.checkIns[address.checkIns.length - 1].date}`}>
                      <span className="bg-gray-700 rounded-full px-1.5 py-0.5 text-xs text-gray-300 font-semibold mr-1">
                        {address.checkIns.length}/6
                      </span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                  {!readOnly && (
                    <button 
                      onClick={() => onAddressCheckIn(index)}
                      className={`ml-2 p-1 rounded-full transition-colors duration-200 flex items-center justify-center ${
                        isCheckInNeeded(address) 
                          ? 'bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/40' 
                          : 'bg-green-500/20 text-green-400'
                      }`}
                      title={isCheckInNeeded(address) ? "Check-in needed" : "Check-in complete"}
                      disabled={!isCheckInNeeded(address)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </button>
                  )}
                  {/* Geocode button */}
                  {!readOnly && !hasValidCoordinates(address) && (
                    <button 
                      onClick={() => onAddressChange(index, address, true)}
                      className="ml-2 p-1 rounded-full bg-blue-500/20 text-blue-400 hover:bg-blue-500/40 transition-colors duration-200 flex items-center justify-center"
                      title="Get GPS coordinates"
                      disabled={isGeocodingInProgress}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${isGeocodingInProgress ? 'animate-spin' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                    </button>
                  )}
                  {/* Manual coordinate edit button */}
                  {!readOnly && hasValidCoordinates(address) && (
                    <button 
                      onClick={() => handleEditCoordinates(index)}
                      className="ml-2 p-1 rounded-full bg-green-500/20 text-green-400 hover:bg-green-500/40 transition-colors duration-200 flex items-center justify-center"
                      title="Edit GPS coordinates"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
              {/* Check-in timeline display */}
              {address.checkIns && address.checkIns.length > 0 && (
                <div className="mt-1 flex space-x-1 items-center">
                  <div className="flex-1 h-1 bg-gray-700 rounded-full overflow-hidden">
                    <div 
                      className="h-1 bg-blue-500 rounded-full" 
                      style={{ width: `${Math.min(100, (address.checkIns.length / 6) * 100)}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-gray-400">
                    {calculateDaysSinceLastCheckIn(address.checkIns[address.checkIns.length - 1].timestamp)} days ago
                  </span>
                </div>
              )}
              {/* Position coordinates display if available */}
              {hasValidCoordinates(address) && (
                <div className="mt-1 text-xs text-gray-500">
                  GPS: {address.position.lat.toFixed(6)}, {address.position.lng.toFixed(6)}
                </div>
              )}
            </>
          )}
        </div>
      ))}
      {!readOnly && (
        <button
          onClick={onAddressAdd}
          className="flex items-center text-blue-400 hover:text-blue-300 text-sm transition-colors duration-200 group"
        >
          <div className="p-1 mr-1 rounded-full bg-blue-900/30 group-hover:bg-blue-900/50 transition-colors duration-200 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
          </div>
          Add Address
        </button>
      )}
      
      <style>{`
        @keyframes pulse {
          0% { box-shadow: 0 0 8px rgba(59, 130, 246, 0.3); }
          50% { box-shadow: 0 0 15px rgba(59, 130, 246, 0.5); }
          100% { box-shadow: 0 0 8px rgba(59, 130, 246, 0.3); }
        }
        
        @keyframes fadeIn {
          0% { opacity: 0; transform: translateY(10px); }
          100% { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.5s ease-in-out;
        }
      `}</style>
    </div>
  );
};

export default AddressComponent;