import React, { useContext, useState } from 'react';
import { doc, deleteDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
// In all components
import { MapContext } from '../MapContext';
import { playNotificationSound } from '../../utils/mapUtils';

// Function to open image viewer
const openImageViewer = (images, initialIndex) => {
  try {
    // Create image viewer overlay
    const viewer = document.createElement('div');
    viewer.className = 'image-viewer';
    viewer.innerHTML = `
      <div class="image-viewer-content">
        <button class="image-viewer-close">×</button>
        <img src="${images[initialIndex]}" alt="Enlarged image">
      </div>
    `;
    
    document.body.appendChild(viewer);
    
    // Add close event
    viewer.querySelector('.image-viewer-close').addEventListener('click', () => {
      try {
        document.body.removeChild(viewer);
      } catch (err) {
        console.warn("Error removing image viewer:", err);
      }
    });
    
    // Close on background click
    viewer.addEventListener('click', (e) => {
      if (e.target === viewer) {
        try {
          document.body.removeChild(viewer);
        } catch (err) {
          console.warn("Error removing image viewer:", err);
        }
      }
    });
  } catch (err) {
    console.error("Error opening image viewer:", err);
  }
};

const LocationDetails = () => {
  const {
    mapRef,
    firestoreRef,
    isAdmin,
    isTowTruckUser,
    currentUser,
    detailsPanelLocation,
    setDetailsPanelLocation,
    detailsVisible,
    setDetailsVisible,
    isLargeScreen,
    selectedLocation,
    setSelectedLocation,
    setIsNavigating,
    setActiveTab,
    setError
  } = useContext(MapContext);
  
  // State for editing
  const [isEditing, setIsEditing] = useState(false);
  const [editFormData, setEditFormData] = useState({});
  
  // Handle deletion of a location
  const handleDeleteLocation = async (id) => {
    try {
      if (!firestoreRef.current) {
        console.error("Firestore reference not available");
        return;
      }
      
      // Delete from Firestore
      await deleteDoc(doc(firestoreRef.current, 'locations', id));
      
      console.log("Deleted location:", id);
      
      // If we had this location in the details panel, clear it
      if (detailsPanelLocation && detailsPanelLocation.id === id) {
        setDetailsPanelLocation(null);
      }
      
      // If we had this location selected, clear it
      if (selectedLocation && selectedLocation.id === id) {
        setSelectedLocation(null);
      }
      
      // Play notification sound
      playNotificationSound();
    } catch (err) {
      console.error("Error deleting location from Firestore:", err);
      setError("Failed to delete location. Please try again.");
    }
  };
  
  // Start navigation to a location
  const startNavigation = () => {
    if (detailsPanelLocation) {
      setSelectedLocation(detailsPanelLocation);
      setIsNavigating(true);
    }
  };
  
  // Mark a location as picked up (for tow truck users)
  const markLocationAsPickedUp = (location) => {
    if (!currentUser || !isTowTruckUser || !location) return;
    
    // Launch the vehicle pickup form
    launchVehiclePickupForm(location);
  };
  
  // Launch vehicle pickup form
  const launchVehiclePickupForm = (location) => {
    // Set as current edit location
    setDetailsPanelLocation(location);
    
    // Move to vehicle pickup tab
    setActiveTab('vehiclePickup');
  };
  
  // Launch edit mode
  const startEditing = (location) => {
    setIsEditing(true);
    setEditFormData({
      name: location.name,
      details: location.details || '',
      isPriority: location.isPriority || false,
      parkingSide: location.parkingSide || null,
      plateNumber: location.plateNumber || '',
      vin: location.vin || '',
      driveType: location.driveType || '',
      make: location.make || '',
      model: location.model || '',
      year: location.year || '',
      case: location.case || '',
      mileage: location.mileage || ''
    });
  };
  
  // Cancel editing
  const cancelEditing = () => {
    setIsEditing(false);
    setEditFormData({});
  };
  
  // Save edited location
  const saveLocation = async () => {
    if (!detailsPanelLocation || !firestoreRef.current) return;
    
    try {
      // Update in Firestore
      await updateDoc(doc(firestoreRef.current, 'locations', detailsPanelLocation.id), editFormData);
      
      console.log("Updated location:", detailsPanelLocation.id);
      
      // Exit edit mode
      setIsEditing(false);
      setEditFormData({});
      
      // Play notification sound
      playNotificationSound();
    } catch (err) {
      console.error("Error updating location in Firestore:", err);
      setError("Failed to update location. Please try again.");
    }
  };
  
  // Function to start DM with a user
  const startDirectMessage = (userId) => {
    // This would be implemented in actual app to open direct messaging
    console.log("Starting DM with user:", userId);
  };
  
  // Format date for display
  const formatDate = (timestamp) => {
    if (!timestamp) return 'N/A';
    
    try {
      return new Date(timestamp.toDate()).toLocaleString();
    } catch (err) {
      return 'Invalid date';
    }
  };
  
  // Function to render the details panel content
  const renderDetailsPanelContent = () => {
    if (!detailsPanelLocation) return (
      <div className="text-center text-gray-400 mt-4">
        <p>Select a location or user to view details</p>
      </div>
    );
    
    // Handle user details case
    if (detailsPanelLocation.type === 'user') {
      return (
        <>
          <div className="details-panel-title">
            User Profile
            {detailsPanelLocation.online ? 
              <span className="details-panel-status picked-up">Online</span> : 
              <span className="details-panel-status pending">Offline</span>
            }
          </div>
          
          {detailsPanelLocation.photoBase64 && (
            <div className="details-panel-image-grid" style={{ gridTemplateColumns: '1fr' }}>
              <img 
                src={detailsPanelLocation.photoBase64} 
                alt="User" 
                className="details-panel-image"
                style={{ height: '150px', width: '150px', borderRadius: '50%', margin: '0 auto' }}
              />
            </div>
          )}
          
          <div className="details-panel-info">
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Name:</div>
              <div className="details-panel-info-value">{detailsPanelLocation.name}</div>
            </div>
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Status:</div>
              <div className="details-panel-info-value">
                {detailsPanelLocation.online ? 
                  <span style={{ color: '#10B981' }}>Online</span> : 
                  <span style={{ color: '#9CA3AF' }}>Offline</span>
                }
              </div>
            </div>
            {detailsPanelLocation.lastUpdated && (
              <div className="details-panel-info-item">
                <div className="details-panel-info-label">Last Seen:</div>
                <div className="details-panel-info-value">
                  {formatDate(detailsPanelLocation.lastUpdated)}
                </div>
              </div>
            )}
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Location:</div>
              <div className="details-panel-info-value">
                {detailsPanelLocation.position.lat.toFixed(6)}, {detailsPanelLocation.position.lng.toFixed(6)}
              </div>
            </div>
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Breadcrumb:</div>
              <div className="details-panel-info-value">
                {detailsPanelLocation.hasTrail ? 'Active' : 'None'}
              </div>
            </div>
          </div>
          
          <div className="details-panel-actions">
            <button 
              className="details-panel-action-btn action-navigate"
              onClick={() => {
                if (mapRef.current) {
                  mapRef.current.setView([
                    detailsPanelLocation.position.lat,
                    detailsPanelLocation.position.lng
                  ], 16);
                }
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10m0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6"/>
              </svg>
              Center on Map
            </button>
            
            <button 
              className="details-panel-action-btn action-dm"
              onClick={() => startDirectMessage(detailsPanelLocation.id)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
              </svg>
              Send Message
            </button>
            
            {isAdmin && detailsPanelLocation.hasTrail && (
              <button 
                className="details-panel-action-btn action-delete"
                onClick={() => confirmDeleteTrail(detailsPanelLocation.id)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"/>
                  <path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z"/>
                </svg>
                Delete Trail
              </button>
            )}
          </div>
        </>
      );
    }
    
    // Check if it's a home location
    const isHomeLocation = detailsPanelLocation.isHome || detailsPanelLocation.name === "Home";
    
    // Regular location details
    if (isEditing) {
      // Render edit form
      return (
        <div className="p-3">
          <h3 className="text-lg font-semibold mb-3">Edit Location</h3>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-gray-400">Name</label>
              <input 
                type="text" 
                value={editFormData.name}
                onChange={(e) => setEditFormData(prev => ({...prev, name: e.target.value}))}
                className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
              />
            </div>
            
            {!isHomeLocation && (
              <>
                <div>
                  <label className="block text-sm text-gray-400">Vehicle Make</label>
                  <input 
                    type="text" 
                    value={editFormData.make}
                    onChange={(e) => setEditFormData(prev => ({...prev, make: e.target.value}))}
                    className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-400">Vehicle Model</label>
                  <input 
                    type="text" 
                    value={editFormData.model}
                    onChange={(e) => setEditFormData(prev => ({...prev, model: e.target.value}))}
                    className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-400">Year</label>
                  <input 
                    type="text" 
                    value={editFormData.year}
                    onChange={(e) => setEditFormData(prev => ({...prev, year: e.target.value}))}
                    className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-400">License Plate</label>
                  <input 
                    type="text" 
                    value={editFormData.plateNumber}
                    onChange={(e) => setEditFormData(prev => ({...prev, plateNumber: e.target.value}))}
                    className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-400">VIN</label>
                  <input 
                    type="text" 
                    value={editFormData.vin}
                    onChange={(e) => setEditFormData(prev => ({...prev, vin: e.target.value}))}
                    className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-400">Drive Type</label>
                  <input 
                    type="text" 
                    value={editFormData.driveType}
                    onChange={(e) => setEditFormData(prev => ({...prev, driveType: e.target.value}))}
                    className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-400">Case Number</label>
                  <input 
                    type="text" 
                    value={editFormData.case}
                    onChange={(e) => setEditFormData(prev => ({...prev, case: e.target.value}))}
                    className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
                  />
                </div>
              </>
            )}
            
            <div>
              <label className="block text-sm text-gray-400">Details</label>
              <textarea 
                value={editFormData.details}
                onChange={(e) => setEditFormData(prev => ({...prev, details: e.target.value}))}
                className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm min-h-[100px]"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                id="priority-checkbox"
                checked={editFormData.isPriority}
                onChange={(e) => setEditFormData(prev => ({...prev, isPriority: e.target.checked}))} 
                className="form-checkbox h-4 w-4 text-blue-600"
              />
              <label htmlFor="priority-checkbox" className="text-sm">Mark as priority</label>
            </div>
            
            {!isHomeLocation && (
              <div>
                <label className="block text-sm text-gray-400">Parking Side</label>
                <div className="flex gap-2 mt-1">
                  <button
                    onClick={() => setEditFormData(prev => ({...prev, parkingSide: 'left'}))}
                    className={`px-2 py-1 rounded text-xs flex items-center gap-1 ${editFormData.parkingSide === 'left' ? 'bg-red-500 text-white' : 'bg-gray-700'}`}
                  >
                    ← Left Side
                  </button>
                  <button
                    onClick={() => setEditFormData(prev => ({...prev, parkingSide: null}))}
                    className={`px-2 py-1 rounded text-xs ${editFormData.parkingSide === null ? 'bg-blue-500 text-white' : 'bg-gray-700'}`}
                  >
                    None
                  </button>
                  <button
                    onClick={() => setEditFormData(prev => ({...prev, parkingSide: 'right'}))}
                    className={`px-2 py-1 rounded text-xs flex items-center gap-1 ${editFormData.parkingSide === 'right' ? 'bg-red-500 text-white' : 'bg-gray-700'}`}
                  >
                    Right Side →
                  </button>
                </div>
              </div>
            )}
            
            <div className="flex gap-3 mt-4">
              <button
                className="bg-blue-500 px-3 py-2 rounded text-white flex-1"
                onClick={saveLocation}
              >
                Save Changes
              </button>
              <button
                className="bg-gray-600 px-3 py-2 rounded text-white"
                onClick={cancelEditing}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      );
    }
    
    // Regular location view
    return (
      <>
        <div className="details-panel-title">
          {detailsPanelLocation.name}
          {detailsPanelLocation.status === 'picked-up' ? 
            <span className="details-panel-status picked-up">Picked Up</span> : 
            <span className="details-panel-status pending">Pending</span>
          }
          {detailsPanelLocation.isAdminOnly && 
            <span className="details-panel-status admin">Admin</span>
          }
          {isHomeLocation && 
            <span className="details-panel-status picked-up">Home</span>
          }
        </div>
        
        {detailsPanelLocation.images && detailsPanelLocation.images.length > 0 && (
          <div className="details-panel-image-grid">
            {detailsPanelLocation.images.map((img, index) => (
              <img 
                key={index}
                src={img} 
                alt={`${detailsPanelLocation.name} ${index + 1}`}
                className="details-panel-image"
                onClick={() => openImageViewer(detailsPanelLocation.images, index)}
              />
            ))}
          </div>
        )}
        
        {detailsPanelLocation.parkingSide && (
          <div className="parking-side-indicator" style={{ marginBottom: '10px' }}>
            Vehicle Parked on {detailsPanelLocation.parkingSide.charAt(0).toUpperCase() + detailsPanelLocation.parkingSide.slice(1)} Side 
            {detailsPanelLocation.parkingSide === 'left' ? ' ←' : ' →'}
          </div>
        )}
        
        <div className="details-panel-info">
          {!isHomeLocation && (
            <>
              <div className="details-panel-info-item">
                <div className="details-panel-info-label">Make:</div>
                <div className="details-panel-info-value">{detailsPanelLocation.make || 'N/A'}</div>
              </div>
              <div className="details-panel-info-item">
                <div className="details-panel-info-label">Model:</div>
                <div className="details-panel-info-value">{detailsPanelLocation.model || 'N/A'}</div>
              </div>
              <div className="details-panel-info-item">
                <div className="details-panel-info-label">Year:</div>
                <div className="details-panel-info-value">{detailsPanelLocation.year || 'N/A'}</div>
              </div>
              <div className="details-panel-info-item">
                <div className="details-panel-info-label">Plate:</div>
                <div className="details-panel-info-value">{detailsPanelLocation.plateNumber || 'N/A'}</div>
              </div>
              <div className="details-panel-info-item">
                <div className="details-panel-info-label">VIN:</div>
                <div className="details-panel-info-value">{detailsPanelLocation.vin || 'N/A'}</div>
              </div>
              <div className="details-panel-info-item">
                <div className="details-panel-info-label">Drive Type:</div>
                <div className="details-panel-info-value">{detailsPanelLocation.driveType || 'N/A'}</div>
              </div>
              {detailsPanelLocation.case && (
                <div className="details-panel-info-item">
                  <div className="details-panel-info-label">Case #:</div>
                  <div className="details-panel-info-value">{detailsPanelLocation.case}</div>
                </div>
              )}
              {detailsPanelLocation.mileage && (
                <div className="details-panel-info-item">
                  <div className="details-panel-info-label">Mileage:</div>
                  <div className="details-panel-info-value">{detailsPanelLocation.mileage}</div>
                </div>
              )}
            </>
          )}
          <div className="details-panel-info-item">
            <div className="details-panel-info-label">Address:</div>
            <div className="details-panel-info-value">{detailsPanelLocation.address || 'N/A'}</div>
          </div>
          {detailsPanelLocation.intersection && (
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Intersection:</div>
              <div className="details-panel-info-value">{detailsPanelLocation.intersection}</div>
            </div>
          )}
          {detailsPanelLocation.status === 'picked-up' && detailsPanelLocation.pickedUpBy && (
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Picked Up By:</div>
              <div className="details-panel-info-value">
                {detailsPanelLocation.pickedUpBy}
              </div>
            </div>
          )}
          {detailsPanelLocation.status === 'picked-up' && detailsPanelLocation.pickedUpAt && (
            <div className="details-panel-info-item">
              <div className="details-panel-info-label">Picked Up At:</div>
              <div className="details-panel-info-value">
                {formatDate(detailsPanelLocation.pickedUpAt)}
              </div>
            </div>
          )}
        </div>
        
        {detailsPanelLocation.details && (
          <div className="details-panel-description">
            {detailsPanelLocation.details}
          </div>
        )}
        
        <div className="details-panel-actions">
          <button 
            className="details-panel-action-btn action-navigate"
            onClick={startNavigation}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M9.502 11a3 3 0 0 1-2.397-4.798l5.44-6.55a1 1 0 1 1 1.537 1.274l-5.01 6.53A3 3 0 0 1 9.502 11m1.732-4h.768a2.5 2.5 0 0 1 0 5h-.768zM6.5 1A.5.5 0 0 1 7 .5h2a.5.5 0 0 1 0 1v.57l1.5-1.5A.5.5 0 0 1 11 0h2.5a.5.5 0 0 1 0 1h-2.158l-2 2H11.5a.5.5 0 0 1 0 1h-2v2.5L8 9V13h2.5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1H8V9l-1.5-1.5V3H6.5a.5.5 0 0 1 0-1"/>
            </svg>
            Navigate
          </button>
          
          {(isAdmin || (!detailsPanelLocation.isAdminOnly && currentUser && detailsPanelLocation.createdBy === currentUser?.uid)) && (
            <button 
              className="details-panel-action-btn action-edit"
              onClick={() => startEditing(detailsPanelLocation)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
              </svg>
              Edit
            </button>
          )}
          
          {(isAdmin || (!detailsPanelLocation.isAdminOnly && currentUser && detailsPanelLocation.createdBy === currentUser?.uid)) && (
            <button 
              className="details-panel-action-btn action-delete"
              onClick={() => handleDeleteLocation(detailsPanelLocation.id)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"/>
                <path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z"/>
              </svg>
              Delete
            </button>
          )}
          
          {isTowTruckUser && !isHomeLocation && detailsPanelLocation.status !== 'picked-up' && (
            <button 
              className="details-panel-action-btn action-pickup"
              onClick={() => markLocationAsPickedUp(detailsPanelLocation)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M2.5 3.5a.5.5 0 0 1 0-1h11a.5.5 0 0 1 0 1zm2-2a.5.5 0 0 1 0-1h7a.5.5 0 0 1 0 1zM0 13a1.5 1.5 0 0 0 1.5 1.5h13A1.5 1.5 0 0 0 16 13V6a1.5 1.5 0 0 0-1.5-1.5h-13A1.5 1.5 0 0 0 0 6zm1.5.5A.5.5 0 0 1 1 13V6a.5.5 0 0 1 .5-.5h13a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5z"/>
              </svg>
              Pick Up
            </button>
          )}
        </div>
      </>
    );
  };

  return (
    <div className={`details-panel ${!detailsVisible ? 'hidden' : ''}`}>
      <div className="details-panel-header">
        <h3 className="text-lg font-bold">Details</h3>
        <button 
          className="close-panel-btn"
          onClick={() => setDetailsVisible(false)}
        >
          ×
        </button>
      </div>
      
      <div className="details-panel-content">
        {renderDetailsPanelContent()}
      </div>
    </div>
  );
};

export default LocationDetails;