import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '../pages/firebase.js';
import { useAuth } from '../contexts/AuthContext.js';

function TagsManagement() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [systemTags, setSystemTags] = useState([]);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#3B82F6'); // Default blue color
  const [editingTagIndex, setEditingTagIndex] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const colorPickerRef = useRef(null);
  
  // Common colors for quick selection
  const colorOptions = [
    { label: 'Blue', value: '#3B82F6' },
    { label: 'Red', value: '#EF4444' },
    { label: 'Green', value: '#10B981' },
    { label: 'Yellow', value: '#F59E0B' },
    { label: 'Purple', value: '#8B5CF6' },
    { label: 'Pink', value: '#EC4899' },
    { label: 'Gray', value: '#6B7280' },
    { label: 'Indigo', value: '#6366F1' },
  ];

  // Fetch system tags
  useEffect(() => {
    async function fetchTags() {
      if (!isAdmin) {
        navigate('/dashboard');
        return;
      }
      
      try {
        // Fetch system tags
        const tagsRef = doc(db, "settings", "systemTags");
        const tagsDoc = await getDoc(tagsRef);
        
        if (tagsDoc.exists() && tagsDoc.data().tags) {
          setSystemTags(tagsDoc.data().tags);
        }
      } catch (error) {
        console.error("Error fetching tags:", error);
        setError("Failed to load tags data.");
      } finally {
        setLoading(false);
      }
    }
    
    fetchTags();
  }, [isAdmin, navigate]);

  // Function to determine text color based on background color
  const getTextColor = (hexColor) => {
    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    
    // Calculate brightness (YIQ formula)
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    
    return yiq >= 150 ? '#000000' : '#FFFFFF';
  };

  // Add a new tag
  const handleAddTag = () => {
    if (newTagName.trim()) {
      // Check if tag with same name already exists
      const tagExists = systemTags.some(tag => tag.name.toLowerCase() === newTagName.trim().toLowerCase());
      
      if (tagExists) {
        setError("A tag with this name already exists.");
        return;
      }
      
      const updatedTags = [
        ...systemTags,
        { name: newTagName.trim(), color: newTagColor }
      ];
      
      setSystemTags(updatedTags);
      saveTags(updatedTags);
      
      // Reset form
      setNewTagName('');
      setNewTagColor('#3B82F6');
    }
  };

  // Start editing a tag
  const handleStartEdit = (index) => {
    setEditingTagIndex(index);
    setNewTagName(systemTags[index].name);
    setNewTagColor(systemTags[index].color);
    setIsEditing(true);
  };

  // Save edited tag
  const handleSaveEdit = () => {
    if (newTagName.trim() && editingTagIndex !== null) {
      // Check if tag with same name already exists (except the current one)
      const tagExists = systemTags.some((tag, index) => 
        index !== editingTagIndex && 
        tag.name.toLowerCase() === newTagName.trim().toLowerCase()
      );
      
      if (tagExists) {
        setError("A tag with this name already exists.");
        return;
      }
      
      const updatedTags = [...systemTags];
      updatedTags[editingTagIndex] = { 
        name: newTagName.trim(), 
        color: newTagColor 
      };
      
      setSystemTags(updatedTags);
      saveTags(updatedTags);
      
      // Reset form
      setNewTagName('');
      setNewTagColor('#3B82F6');
      setEditingTagIndex(null);
      setIsEditing(false);
    }
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setNewTagName('');
    setNewTagColor('#3B82F6');
    setEditingTagIndex(null);
    setIsEditing(false);
  };

  // Delete a tag
  const handleDeleteTag = (index) => {
    const updatedTags = systemTags.filter((_, i) => i !== index);
    setSystemTags(updatedTags);
    saveTags(updatedTags);
  };

  // Save tags to Firestore
  const saveTags = async (tags) => {
    if (!isAdmin) return;
    
    setError('');
    setSuccess('');
    setSaving(true);
    
    try {
      const tagsRef = doc(db, "settings", "systemTags");
      await setDoc(tagsRef, {
        tags: tags,
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      setSuccess("Tags updated successfully!");
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (error) {
      console.error("Error saving tags:", error);
      setError("Failed to save tags. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex justify-center items-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-3 text-gray-300">Loading tags...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-900 flex justify-center items-center">
        <div className="bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full border border-gray-700">
          <h2 className="text-xl font-bold text-red-500 mb-4">Access Denied</h2>
          <p className="mb-4 text-gray-300">Only administrators can access tag management.</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white font-medium py-2 px-4 rounded-md shadow-md"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-300">
      <nav className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 shadow-md border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">NWRepo</h1>
              </div>
            </div>
            <div className="flex items-center">
              <button
                onClick={() => navigate('/dashboard')}
                className="bg-gray-700 hover:bg-gray-600 text-gray-200 px-4 py-2 rounded-md shadow-md transition"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </nav>
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Tag Management</h1>
          </div>
          
          {error && <div className="bg-red-900 border border-red-700 text-red-200 px-4 py-3 rounded mb-4">{error}</div>}
          {success && <div className="bg-green-900 border border-green-700 text-green-200 px-4 py-3 rounded mb-4">{success}</div>}
          
          <div className="bg-gray-800 shadow-lg overflow-hidden sm:rounded-lg p-6 border border-gray-700">
            <h2 className="text-lg font-medium text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">System Tags</h2>
            <p className="text-gray-400 mb-6">
              Create and manage tags that can be assigned to users. After creating tags here, you can assign them to users through their profile pages.
            </p>
            
            {/* Tag Creation Form */}
            <div className="mb-8 p-4 bg-gray-850 rounded-lg border border-gray-700 shadow-inner">
              <h3 className="text-md font-medium text-gray-200 mb-3">
                {isEditing ? 'Edit Tag' : 'Create New Tag'}
              </h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="tagName" className="block text-sm font-medium text-gray-300 mb-1">
                    Tag Name
                  </label>
                  <input
                    type="text"
                    id="tagName"
                    value={newTagName}
                    onChange={(e) => setNewTagName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-600 rounded-md shadow-inner focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-gray-700 text-gray-200"
                    placeholder="Enter tag name"
                  />
                </div>
                
                <div>
                  <label htmlFor="tagColor" className="block text-sm font-medium text-gray-300 mb-1">
                    Tag Color
                  </label>
                  <div className="flex items-center">
                    <input
                      type="color"
                      id="tagColor"
                      ref={colorPickerRef}
                      value={newTagColor}
                      onChange={(e) => setNewTagColor(e.target.value)}
                      className="h-10 w-10 p-0 border-0 rounded cursor-pointer bg-transparent"
                    />
                    <div className="ml-3 flex flex-wrap gap-1">
                      {colorOptions.map((color) => (
                        <button
                          key={color.value}
                          type="button"
                          onClick={() => setNewTagColor(color.value)}
                          className="w-6 h-6 rounded-full cursor-pointer border border-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          style={{ backgroundColor: color.value }}
                          title={color.label}
                        />
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-end">
                  <div className="preview mr-4 flex-grow">
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Preview
                    </label>
                    <div 
                      className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium shadow-md"
                      style={{ 
                        backgroundColor: newTagColor,
                        color: getTextColor(newTagColor)
                      }}
                    >
                      {newTagName || 'Tag Preview'}
                    </div>
                  </div>
                  
                  <div className="flex">
                    {isEditing ? (
                      <>
                        <button
                          type="button"
                          onClick={handleCancelEdit}
                          className="mr-2 px-3 py-2 border border-gray-600 rounded-md text-sm text-gray-300 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-md"
                        >
                          Cancel
                        </button>
                        <button
                          type="button"
                          onClick={handleSaveEdit}
                          disabled={!newTagName.trim()}
                          className={`px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-md ${!newTagName.trim() ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          Save Changes
                        </button>
                      </>
                    ) : (
                      <button
                        type="button"
                        onClick={handleAddTag}
                        disabled={!newTagName.trim()}
                        className={`px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-md ${!newTagName.trim() ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        Add Tag
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Tags List */}
            <div>
              <h3 className="text-md font-medium text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-3">Available Tags</h3>
              
              {systemTags.length > 0 ? (
                <div className="overflow-hidden border border-gray-700 rounded-md shadow-lg">
                  <table className="min-w-full divide-y divide-gray-700">
                    <thead className="bg-gradient-to-r from-gray-800 via-gray-750 to-gray-800">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                          Tag
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                          Color
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-gray-800 divide-y divide-gray-700">
                      {systemTags.map((tag, index) => (
                        <tr key={index} className="hover:bg-gray-750">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div 
                                className="h-8 w-8 rounded-full flex items-center justify-center shadow-md"
                                style={{ 
                                  backgroundColor: tag.color,
                                  color: getTextColor(tag.color)
                                }}
                              >
                                {tag.name.charAt(0).toUpperCase()}
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-200">{tag.name}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div 
                                className="h-6 w-6 rounded-full mr-2 shadow-md border border-gray-600"
                                style={{ backgroundColor: tag.color }}
                              ></div>
                              <span className="text-sm text-gray-400">{tag.color}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button 
                              onClick={() => handleStartEdit(index)}
                              className="text-blue-400 hover:text-blue-300 mr-4 transition"
                            >
                              Edit
                            </button>
                            <button 
                              onClick={() => handleDeleteTag(index)}
                              className="text-red-400 hover:text-red-300 transition"
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-850 rounded-md border border-gray-700 shadow-inner">
                  <p className="text-gray-400">No tags have been created yet. Create your first tag above.</p>
                </div>
              )}
            </div>
            
            {/* Instructions */}
            <div className="mt-8 p-4 bg-gradient-to-r from-blue-900/30 via-blue-800/30 to-blue-900/30 rounded-lg border border-blue-800/50 shadow-md">
              <h3 className="text-md font-medium text-blue-300 mb-2">How to Use Tags</h3>
              <ol className="list-decimal ml-5 text-sm text-blue-200 space-y-1">
                <li>Create tags here with descriptive names and distinctive colors</li>
                <li>Navigate to a user's profile page</li>
                <li>Use the "Add Tag" dropdown in the profile header to assign tags</li>
                <li>Tags can be used for filtering and organizing users</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TagsManagement;