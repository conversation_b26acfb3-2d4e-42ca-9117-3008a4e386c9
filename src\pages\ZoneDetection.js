import React, { useState, useEffect, useCallback } from 'react';
import { 
  doc, 
  collection, 
  onSnapshot,
  addDoc,
  serverTimestamp,
  getFirestore 
} from 'firebase/firestore';

function ZoneDetection({
  teamId,
  currentUser,
  currentLocation,
  isClockedIn
}) {
  const db = getFirestore();
  const [zones, setZones] = useState([]);
  const [activeZones, setActiveZones] = useState([]);
  const [lastEnteredZone, setLastEnteredZone] = useState(null);
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');
  const [notificationType, setNotificationType] = useState('info'); // 'info', 'warning', 'success'

  // Load zones for team
  useEffect(() => {
    if (!db || !teamId) return;
    
    const zonesRef = collection(db, 'teams', teamId, 'zones');
    const unsubscribe = onSnapshot(zonesRef, (snapshot) => {
      const zonesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setZones(zonesData);
    }, (error) => {
      console.error("Error loading zones:", error);
    });
    
    return () => unsubscribe();
  }, [db, teamId]);

  // Check if user is in a zone when location changes
  useEffect(() => {
    if (!currentLocation || !zones.length || !isClockedIn || !currentUser) return;
    
    const checkZones = () => {
      const currentUserZones = zones.filter(zone => {
        if (!zone.bounds || !zone.bounds.northEast || !zone.bounds.southWest) return false;
        
        // Check if user is assigned to this zone (if zone has assignments)
        const isUserAssigned = !zone.assignedUsers || 
          zone.assignedUsers.length === 0 || 
          zone.assignedUsers.includes(currentUser.uid);
        
        if (!isUserAssigned) return false;
        
        // Check if the user's location is within the zone boundaries
        return currentLocation.lat >= zone.bounds.southWest.lat && 
               currentLocation.lat <= zone.bounds.northEast.lat &&
               currentLocation.lng >= zone.bounds.southWest.lng &&
               currentLocation.lng <= zone.bounds.northEast.lng;
      });
      
      // Find zones the user just entered (in currentUserZones but not in activeZones)
      const enteredZones = currentUserZones.filter(
        zone => !activeZones.some(activeZone => activeZone.id === zone.id)
      );
      
      // Find zones the user just left (in activeZones but not in currentUserZones)
      const exitedZones = activeZones.filter(
        zone => !currentUserZones.some(currentZone => currentZone.id === zone.id)
      );
      
      // Update active zones
      if (enteredZones.length > 0 || exitedZones.length > 0) {
        setActiveZones(currentUserZones);
        
        // Show notification for zone entry
        if (enteredZones.length > 0) {
          const enteredZone = enteredZones[0]; // Just show notification for the first one if multiple
          setLastEnteredZone(enteredZone);
          setNotificationMessage(`Entered zone: ${enteredZone.name}`);
          setNotificationType('success');
          setShowNotification(true);
          
          // Log zone entry to Firestore
          logZoneEvent(enteredZone.id, 'enter');
          
          // Hide notification after a delay
          setTimeout(() => {
            setShowNotification(false);
          }, 5000);
        }
        
        // Show notification for zone exit
        if (exitedZones.length > 0) {
          const exitedZone = exitedZones[0]; // Just show notification for the first one if multiple
          setNotificationMessage(`Exited zone: ${exitedZone.name}`);
          setNotificationType('warning');
          setShowNotification(true);
          
          // Log zone exit to Firestore
          logZoneEvent(exitedZone.id, 'exit');
          
          // Hide notification after a delay
          setTimeout(() => {
            setShowNotification(false);
          }, 5000);
        }
      }
    };
    
    checkZones();
    
    // Set up an interval to periodically check zones (useful if user stays stationary)
    const intervalId = setInterval(checkZones, 60000); // Check every minute
    
    return () => clearInterval(intervalId);
  }, [currentLocation, zones, activeZones, isClockedIn, currentUser]);
  
  // Log zone entry/exit events to Firestore
  const logZoneEvent = useCallback(async (zoneId, eventType) => {
    if (!db || !teamId || !currentUser || !isClockedIn) return;
    
    try {
      const eventsCollection = collection(db, 'teams', teamId, 'zoneEvents');
      await addDoc(eventsCollection, {
        zoneId,
        userId: currentUser.uid,
        eventType, // 'enter' or 'exit'
        timestamp: serverTimestamp(),
        location: currentLocation
      });
      
      console.log(`Logged zone ${eventType} event for zone ${zoneId}`);
    } catch (error) {
      console.error(`Error logging zone ${eventType} event:`, error);
    }
  }, [db, teamId, currentUser, isClockedIn, currentLocation]);

  // Render nothing if no notification to show
  if (!showNotification) return null;
  
  // Determine notification style based on type
  let notificationColor = 'bg-blue-500';
  let icon = (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
    </svg>
  );
  
  if (notificationType === 'success') {
    notificationColor = 'bg-green-500';
    icon = (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    );
  } else if (notificationType === 'warning') {
    notificationColor = 'bg-yellow-500';
    icon = (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md animate-slide-in">
      <div className={`${notificationColor} text-white px-4 py-3 rounded-lg shadow-lg flex items-start`}>
        <div className="flex-shrink-0 mr-2">
          {icon}
        </div>
        <div>
          <p className="font-medium">{notificationMessage}</p>
          {lastEnteredZone && notificationType === 'success' && lastEnteredZone.description && (
            <p className="text-sm mt-1 text-white text-opacity-90">{lastEnteredZone.description}</p>
          )}
        </div>
        <button 
          className="ml-4 text-white opacity-75 hover:opacity-100"
          onClick={() => setShowNotification(false)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  );
}

export default ZoneDetection;