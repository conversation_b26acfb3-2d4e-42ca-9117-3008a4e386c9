import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { getFirestore, collection, doc, updateDoc, setDoc, serverTimestamp, getDocs, query, onSnapshot, where, Timestamp } from 'firebase/firestore';
import NoSleep from 'nosleep.js'; // You'll need to install this with npm install nosleep.js

// Added a visibility check function
const isDocumentVisible = () => {
  return !document.hidden;
};

const CameraSys = ({ streamingContext }) => {
  const { currentUser } = useAuth();
  const [cameraCount, setCameraCount] = useState(4);
  const [pendingCameraCount, setPendingCameraCount] = useState(4);
  const [saveNeeded, setSaveNeeded] = useState(false);
  const [systemKilled, setSystemKilled] = useState(false);
  const [priorCameraStates, setPriorCameraStates] = useState({});
  const [availableCameras, setAvailableCameras] = useState([]);
  const [streamErrors, setStreamErrors] = useState({});
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  
  // New states for server/client mode
  const [mode, setMode] = useState('standalone'); // 'standalone', 'server', or 'client'
  const [serverSessionId, setServerSessionId] = useState(null);
  const [remoteControlEnabled, setRemoteControlEnabled] = useState(true);
  
  // Reference to keep track of active streams across page navigations
  const activeStreamsRef = useRef({});
  
  // Flag for visibility change handling
  const wasHidden = useRef(false);
  
  // NoSleep instance to prevent device from sleeping
  const noSleepRef = useRef(null);
  
  // Initialize NoSleep
  useEffect(() => {
    noSleepRef.current = new NoSleep();
    return () => {
      if (noSleepRef.current) {
        try {
          noSleepRef.current.disable();
        } catch (e) {
          console.error("Error disabling NoSleep:", e);
        }
      }
    };
  }, []);
  
  // Silent audio element for keeping page active in background
  const createSilentAudio = () => {
    if (window._silentAudio) return window._silentAudio;
    
    const silentAudio = document.createElement('audio');
    silentAudio.src = 'data:audio/mp3;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFTb25vdGhlcXVlLm9yZwBURU5DAAAAHQAAA1N3aXRjaCBQbHVzIMKpIE5DSCBTb2Z0d2FyZQBUSVQyAAAABgAAAzIyMzUAVFNTRQAAAA8AAANMYXZmNTcuODMuMTAwAAAAAAAAAAAAAAD/80DEAAAAA0gAAAAATEFNRTMuMTAwVVVVVVVVVVVVVUxBTUUzLjEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/zQsRbAAADSAAAAABVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/zQMSkAAADSAAAAABVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV';
    silentAudio.loop = true;
    silentAudio.autoplay = true;
    silentAudio.muted = true;
    silentAudio.volume = 0;
    silentAudio.setAttribute('playsinline', '');
    document.body.appendChild(silentAudio);
    
    // Play it right away
    silentAudio.play().catch(e => {
      console.log("Silent audio playback blocked by browser, this is normal:", e);
    });
    
    window._silentAudio = silentAudio;
    return silentAudio;
  };
  
  // Helper function to rescue all demo videos - global rescue mechanism
  const rescueAllDemoVideos = () => {
    if (!window.activeVideos) return;
    
    console.log("Performing global demo video check...");
    
    Object.keys(window.activeVideos).forEach(key => {
      const videoInfo = window.activeVideos[key];
      const videoEl = videoInfo.element;
      
      if (!document.body.contains(videoEl)) {
        console.log(`Video element for ${key} is no longer in document, removing from tracking`);
        delete window.activeVideos[key];
        return;
      }
      
      if (videoEl.paused || videoEl.ended) {
        console.log(`Rescuing stopped demo video: ${key}`);
        
        if (videoInfo.source) {
          videoEl.src = videoInfo.source;
          videoEl.load();
          videoEl.play().catch(() => {
            // If direct play fails, try with user interaction simulation
            console.log(`Direct play failed for ${key}, trying with interaction workaround`);
            
            // Create a temporary button for "user interaction"
            const tempButton = document.createElement('button');
            tempButton.style.position = 'fixed';
            tempButton.style.top = '-100px'; // Off-screen
            tempButton.textContent = 'Play';
            document.body.appendChild(tempButton);
            
            // Click it and then play
            tempButton.click();
            videoEl.play().catch(() => {});
            
            // Remove the button
            setTimeout(() => {
              tempButton.remove();
            }, 500);
          });
        }
      }
    });
  };
  
  // Add global rescue system for demo videos in server mode
  useEffect(() => {
    // Only in server mode
    if (mode !== 'server') return;
    
    console.log("Setting up global demo video rescue system");
    
    // Create silent audio to help keep page active
    const silentAudio = createSilentAudio();
    
    // Enable NoSleep to prevent device from sleeping
    try {
      noSleepRef.current.enable().then(() => {
        console.log("NoSleep enabled successfully - device will stay awake");
      }).catch(err => {
        console.warn("NoSleep couldn't be enabled automatically:", err);
        console.log("Will try again with user interaction");
      });
    } catch (e) {
      console.error("Error enabling NoSleep:", e);
    }
    
    // Run rescue check every 30 seconds
    const rescueInterval = setInterval(rescueAllDemoVideos, 30000);
    
    // Also run on visibility change
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log("Document became visible, running global video rescue");
        rescueAllDemoVideos();
      } else {
        console.log("Document became hidden, ensuring background operation");
        
        // Make sure silent audio is playing
        if (silentAudio && silentAudio.paused) {
          silentAudio.play().catch(e => {});
        }
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Create a ping mechanism to keep the page alive even if it's in background
    const pingInterval = setInterval(() => {
      console.log("Sending background heartbeat...");
      
      // Update Firestore to indicate we're still running
      try {
        if (!serverSessionId) return;
        
        const db = getFirestore();
        const serverRef = doc(db, 'streamingSessions', serverSessionId);
        
        updateDoc(serverRef, {
          lastActive: serverTimestamp(),
          isBackgroundRunning: true,
          backgroundPing: Date.now()
        }).then(() => {
          console.log("Background heartbeat sent successfully");
        }).catch(err => {
          console.error("Error sending background heartbeat:", err);
        });
      } catch (e) {
        console.error("Error in background ping:", e);
      }
    }, 45000); // Every 45 seconds
    
    return () => {
      clearInterval(rescueInterval);
      clearInterval(pingInterval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      
      // Clean up silent audio
      if (window._silentAudio) {
        window._silentAudio.pause();
        window._silentAudio.remove();
        window._silentAudio = null;
      }
      
      // Disable NoSleep when component unmounts
      try {
        noSleepRef.current.disable();
      } catch (e) {
        console.error("Error disabling NoSleep:", e);
      }
    };
  }, [mode, serverSessionId]);
  
  // Load saved camera count from Firestore
  useEffect(() => {
    const loadSavedCameraCount = async () => {
      try {
        const db = getFirestore();
        // Look for a saved settings document
        const settingsQuery = query(collection(db, 'systemSettings'));
        const snapshot = await getDocs(settingsQuery);
        
        let settingsDoc = null;
        snapshot.forEach(doc => {
          settingsDoc = {
            id: doc.id,
            ...doc.data()
          };
        });
        
        // If we found settings with a cameraCount, use it
        if (settingsDoc && settingsDoc.cameraCount) {
          console.log(`Loading saved camera count: ${settingsDoc.cameraCount}`);
          setCameraCount(settingsDoc.cameraCount);
          setPendingCameraCount(settingsDoc.cameraCount);
        } else {
          console.log('No saved camera count found, using default');
        }
      } catch (error) {
        console.error('Error loading saved camera count:', error);
      }
    };
    
    loadSavedCameraCount();
  }, []);
  
  // Handle streaming context changes
  useEffect(() => {
    if (!streamingContext) return;
    
    if (streamingContext.isServer) {
      setMode('server');
      setServerSessionId(streamingContext.clientId);
      console.log('Running in SERVER mode with session ID:', streamingContext.clientId);
      
      // Check if we need to enable NoSleep for server mode
      if (noSleepRef.current) {
        noSleepRef.current.enable().catch(err => {
          console.warn("NoSleep couldn't be enabled automatically:", err);
        });
      }
    } else if (streamingContext.clientConnected) {
      setMode('client');
      console.log('Running in CLIENT mode, connected to server:', streamingContext.serverInfo);
    } else {
      setMode('standalone');
      console.log('Running in STANDALONE mode');
    }
  }, [streamingContext]);
  
  // Set up page visibility detection with enhanced background keep-alive
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is now hidden
        console.log("Page visibility changed to hidden");
        wasHidden.current = true;
        
        // If we're in server mode, we need to keep streams running
        if (mode === 'server') {
          console.log("In server mode - will keep streams active while page is hidden");
          
          // Create or ensure silent audio is playing to help keep the page active
          const silentAudio = createSilentAudio();
          if (silentAudio.paused) {
            silentAudio.play().catch(e => {});
          }
          
          // Add a class to the body to indicate background mode
          document.body.classList.add('camera-background-mode');
          
          // Send a background status update to Firestore
          try {
            if (!serverSessionId) return;
            
            const db = getFirestore();
            const serverRef = doc(db, 'streamingSessions', serverSessionId);
            
            updateDoc(serverRef, {
              lastActive: serverTimestamp(),
              visibilityState: 'hidden',
              backgroundMode: true
            }).catch(err => {
              console.error("Error updating background status:", err);
            });
          } catch (e) {
            console.error("Error in visibility change handler:", e);
          }
        }
      } else {
        // Page is now visible again
        console.log("Page visibility changed to visible");
        
        // Remove background mode class
        document.body.classList.remove('camera-background-mode');
        
        if (wasHidden.current) {
          // Only run this code when returning from hidden state
          console.log("Page was previously hidden, checking stream status");
          
          // Update visibility state in Firestore
          try {
            if (!serverSessionId) return;
            
            const db = getFirestore();
            const serverRef = doc(db, 'streamingSessions', serverSessionId);
            
            updateDoc(serverRef, {
              lastActive: serverTimestamp(),
              visibilityState: 'visible',
              backgroundMode: false
            }).catch(err => {
              console.error("Error updating visibility status:", err);
            });
          } catch (e) {
            console.error("Error in visibility change handler:", e);
          }
          
          // Check and restart any cameras that might have stopped
          Object.keys(activeStreamsRef.current).forEach(cameraIndex => {
            const streamInfo = activeStreamsRef.current[cameraIndex];
            
            if (streamInfo && streamInfo.active && streamInfo.source) {
              // Get the video element
              const container = document.querySelector(`[data-camera-index="${cameraIndex}"]`);
              if (container) {
                const videoEl = container.querySelector('video');
                
                // Check if the video is actually playing
                if (videoEl && (videoEl.paused || !videoEl.srcObject)) {
                  console.log(`Camera ${cameraIndex} needs to be restarted after visibility change`);
                  
                  // Force camera restart by triggering select change
                  const select = document.querySelector(`[data-camera-select="${cameraIndex}"]`);
                  if (select) {
                    select.value = streamInfo.source;
                    select.dispatchEvent(new Event('change', { bubbles: true }));
                  }
                }
              }
            }
          });
          
          wasHidden.current = false;
        }
      }
    };
    
    // Add visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Also handle page unload to save state
    const handleBeforeUnload = () => {
      console.log("Page is being unloaded - saving camera state");
      
      // Try to quickly update Firestore to indicate cameras should keep running
      if (mode === 'server' && serverSessionId) {
        try {
          // Use navigator.sendBeacon for more reliable background updates
          const formData = new FormData();
          formData.append('sessionId', serverSessionId);
          formData.append('userId', currentUser?.uid || 'anonymous');
          formData.append('action', 'server_still_running');
          
          navigator.sendBeacon('/api/camera-server-update', formData);
          
          // Also try to update Firestore directly
          const db = getFirestore();
          const serverRef = doc(db, 'streamingSessions', serverSessionId);
          
          updateDoc(serverRef, {
            lastActive: serverTimestamp(),
            unloadTimestamp: serverTimestamp(),
            shouldPersist: true
          }).catch(() => {});
        } catch (e) {
          console.error("Error in beforeunload handler:", e);
        }
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [mode, serverSessionId, currentUser]);
  
  // In server mode, we need to track connected clients and handle remote control commands
  useEffect(() => {
    if (mode !== 'server' || !serverSessionId) return;
    
    console.log("Setting up server-mode listeners");
    
    const db = getFirestore();
    // Listen for remote control commands
    const unsubscribeCommands = onSnapshot(
      collection(db, 'cameraCommands'),
      (snapshot) => {
        snapshot.docChanges().forEach(change => {
          if (change.type === 'added' || change.type === 'modified') {
            const commandData = change.doc.data();
            
            // Only process commands intended for this server and that haven't been processed yet
            if (commandData.targetServer === serverSessionId && 
                !commandData.processed && 
                remoteControlEnabled) {
              
              console.log("Received remote command:", commandData);
              
              // Process the command
              processRemoteCommand(commandData, change.doc.id);
            }
          }
        });
      }
    );
    
    // Function to process remote commands
    const processRemoteCommand = async (command, commandId) => {
      try {
        console.log(`Processing command: ${command.action} for camera ${command.cameraIndex}`);
        
        switch (command.action) {
          case 'setCameraSource':
            // Set a camera's source
            if (command.cameraIndex >= 0 && command.source) {
              const select = document.querySelector(`[data-camera-select="${command.cameraIndex}"]`);
              if (select) {
                // Update the select value
                select.value = command.source;
                select.dispatchEvent(new Event('change', { bubbles: true }));
                console.log(`Set camera ${command.cameraIndex} to source ${command.source}`);
              }
            }
            break;
            
          case 'setCameraCount':
            // Change number of cameras
            if (command.count && command.count > 0) {
              setPendingCameraCount(command.count);
              setSaveNeeded(true);
              // Auto-apply the change
              setTimeout(() => {
                applyCameraCountChange();
              }, 500);
            }
            break;
            
          case 'killAll':
            // Kill all cameras
            killAll();
            break;
            
          case 'restoreCameras':
            // Restore cameras
            if (systemKilled) {
              restoreCameras();
            }
            break;
            
          default:
            console.log(`Unknown command action: ${command.action}`);
        }
        
        // Mark command as processed
        const db = getFirestore();
        await updateDoc(doc(db, 'cameraCommands', commandId), {
          processed: true,
          processedAt: serverTimestamp(),
          processingServer: serverSessionId
        });
      } catch (error) {
        console.error('Error processing remote command:', error);
      }
    };
    
    // Update server status with camera counts
    const updateServerStatus = async () => {
      try {
        if (!serverSessionId) return;
        
        // Count active cameras
        const activeCameras = document.querySelectorAll('[data-camera-select]');
        let activeCount = 0;
        
        activeCameras.forEach(select => {
          if (select.value !== "off") activeCount++;
        });
        
        const db = getFirestore();
        // Update session in Firestore
        await updateDoc(doc(db, 'streamingSessions', serverSessionId), {
          cameraCount: cameraCount,
          activeCameras: activeCount,
          lastActive: serverTimestamp(),
          mode: 'server',
          deviceInfo: {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            vendor: navigator.vendor,
            screenWidth: window.screen.width,
            screenHeight: window.screen.height
          }
        });
      } catch (error) {
        console.error('Error updating server status:', error);
      }
    };
    
    // Update server status initially and periodically
    updateServerStatus();
    const statusInterval = setInterval(updateServerStatus, 30000);
    
    return () => {
      unsubscribeCommands();
      clearInterval(statusInterval);
    };
  }, [mode, serverSessionId, remoteControlEnabled, cameraCount, systemKilled, currentUser]);
  
  // Load and restore camera states from Firestore
  useEffect(() => {
    // For client mode, don't try to load camera states - just show remote status
    if (mode === 'client') {
      setInitialLoadComplete(true);
      return;
    }
    
    const loadSavedCameraStates = async () => {
      try {
        console.log("Loading saved camera states from Firestore...");
        
        const db = getFirestore();
        // Get all camera documents from Firestore
        const camerasQuery = query(collection(db, 'cameraStatus'));
        const snapshot = await getDocs(camerasQuery);
        const existingCameras = [];
        
        snapshot.forEach(doc => {
          existingCameras.push({
            id: doc.id,
            ...doc.data()
          });
        });
        
        console.log(`Found ${existingCameras.length} saved camera states in Firestore`);
        
        // We need to wait a bit for the camera dropdowns to be rendered
        setTimeout(() => {
          // Apply the saved states to our camera dropdowns
          existingCameras.forEach(camera => {
            // Find which index this camera corresponds to (based on label or index in data)
            const cameraIndex = getCameraIndexFromLabel(camera.label);
            
            if (cameraIndex !== -1) {
              console.log(`Restoring camera ${cameraIndex} to ${camera.active ? 'active' : 'inactive'} with source: ${camera.source || 'none'}`);
              
              // Find the select dropdown for this camera
              const select = document.querySelector(`[data-camera-select="${cameraIndex}"]`);
              
              if (select) {
                // If camera was active, set the source
                if (camera.active && camera.source) {
                  // Make sure the source option exists
                  const sourceExists = Array.from(select.options).some(option => option.value === camera.source);
                  
                  if (sourceExists) {
                    select.value = camera.source;
                  } else if (camera.source === 'demo') {
                    select.value = 'demo';
                  } else {
                    // If the exact source doesn't exist (e.g. device ID changed), use the first available camera
                    const firstCameraOption = Array.from(select.options).find(option => 
                      option.value !== 'off' && option.value !== 'demo'
                    );
                    
                    if (firstCameraOption) {
                      select.value = firstCameraOption.value;
                    } else {
                      select.value = 'demo'; // Fallback to demo if no real camera
                    }
                  }
                } else {
                  // If camera was inactive, set to off
                  select.value = 'off';
                }
                
                // Store this state in our active streams tracking
                activeStreamsRef.current[cameraIndex] = {
                  active: camera.active,
                  source: camera.source
                };
                
                // Trigger change event to activate the camera
                select.dispatchEvent(new Event('change', { bubbles: true }));
              }
            }
          });
          
          // Mark initial load as complete
          setInitialLoadComplete(true);
        }, 1000); // Wait 1 second for UI to fully initialize
      } catch (error) {
        console.error('Error loading saved camera states:', error);
        setInitialLoadComplete(true);
      }
    };
    
    // Helper function to extract camera index from label
    const getCameraIndexFromLabel = (label) => {
      if (!label) return -1;
      
      // Try to extract the index from the label (e.g. "Camera 1" -> 0)
      const match = label.match(/Camera\s+(\d+)/i);
      if (match && match[1]) {
        const index = parseInt(match[1]) - 1; // Convert to 0-based index
        return index;
      }
      
      return -1;
    };
    
    // Load saved camera states after a brief delay to ensure the component is mounted
    const timer = setTimeout(() => {
      loadSavedCameraStates();
    }, 500);
    
    return () => clearTimeout(timer);
  }, [mode]); // Run when mode changes
  
  // Helper function to detect OBS/Virtual cameras
  const isOBSCamera = (deviceId) => {
    if (!deviceId || typeof deviceId !== 'string') return false;
    
    // Check if the deviceId or related camera label indicates OBS
    return deviceId.toLowerCase().includes('obs') || 
           deviceId.toLowerCase().includes('virtual') ||
           availableCameras.some(c => c.id === deviceId && 
                               (c.label.toLowerCase().includes('obs') || 
                                c.label.toLowerCase().includes('virtual')));
  };
  
  // Add this function to help force OBS cameras to play
  const forceOBSCameraPlay = (videoElement, deviceId, cameraIndex) => {
    if (!videoElement || !isOBSCamera(deviceId)) return;
    
    console.log(`🔄 Applying special OBS camera handling for camera ${cameraIndex}`);
    
    // OBS sometimes requires multiple play attempts
    const attemptPlay = () => {
      if (!document.body.contains(videoElement)) return;
      
      videoElement.play().catch(err => {
        console.log(`OBS play attempt error (expected): ${err.name}`);
        
        // For OBS, we'll retry a couple times
        if (err.name !== 'NotAllowedError') {
          setTimeout(() => {
            if (document.body.contains(videoElement)) {
              console.log(`Retrying OBS camera play for camera ${cameraIndex}`);
              attemptPlay();
            }
          }, 500);
        }
      });
    };
    
    // Set a few special attributes that can help with OBS
    videoElement.setAttribute('autoplay', 'true');
    videoElement.setAttribute('playsinline', 'true');
    videoElement.setAttribute('muted', 'true');
    videoElement.setAttribute('disablePictureInPicture', 'true');
    videoElement.setAttribute('disableRemotePlayback', 'true');
    videoElement.style.transform = 'scaleX(1)'; // Force render refresh
    
    // Try to play immediately
    attemptPlay();
    
    // Also try after a delay, which sometimes helps with OBS
    setTimeout(attemptPlay, 500);
    setTimeout(attemptPlay, 1500);
    
    // Try to set video properties that might help
    try {
      videoElement.muted = true;
      videoElement.volume = 0;
      
      // Force camera to be visible
      const container = videoElement.parentElement;
      if (container) {
        container.style.overflow = 'visible';
      }
    } catch (e) {
      console.log('Error setting OBS video properties:', e);
    }
  };
  
  // Network status simulation state - STATIC VALUES to prevent re-renders
  const networkStatus = "connected";
  const networkSpeed = 5.2;
  const networkLatency = 28;
  const bandwidthUsage = 12.4;
  const signalStrength = 4;
  const packetLoss = 0.2;
  const dataTransferred = 125.4;
  
  // Use this ref to track all active videos for ensuring continuous playback
  const activeVideos = useRef({});
  
  // Camera drop-down options
  const cameraOptions = [1, 2, 4, 6, 9, 12, 16];
  
  // Function to continuously force video element to play
  const preventVideoSleep = (videoElement, cameraIndex) => {
    if (!videoElement) return;
    
    // Force video to keep playing
    videoElement.addEventListener('pause', function() {
      if (document.visibilityState === 'visible') {
        console.log(`Auto-play triggered for paused video ${cameraIndex}`);
        videoElement.play().catch(e => console.log('Auto-play prevented:', e.message));
      }
    });
    
    // Set up a ticker to keep the video active
    const keepAliveInterval = setInterval(() => {
      if (!document.body.contains(videoElement)) {
        clearInterval(keepAliveInterval);
        return;
      }
      
      if (videoElement.paused && document.visibilityState === 'visible') {
        console.log(`Keep-alive checking paused video ${cameraIndex}`);
        videoElement.play().catch(e => {});
      }
      
      // Also periodically reset the current time if it's near the end
      if (videoElement.duration && videoElement.currentTime > 0.8 * videoElement.duration) {
        console.log(`Resetting video time for ${cameraIndex} - near end`);
        videoElement.currentTime = 0;
      }
    }, 1000);
    
    // Store the interval for cleanup
    videoElement._keepAliveInterval = keepAliveInterval;
    
    return () => {
      clearInterval(keepAliveInterval);
    };
  };
  
  // Check camera permissions at startup
  useEffect(() => {
    // Skip permission check in client mode
    if (mode === 'client') return;
    
    async function checkCameraPermissions() {
      try {
        console.log("Requesting camera permission at startup...");
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        console.log("✅ Camera permission GRANTED at startup");
        
        // Log the default camera info
        const videoTrack = stream.getVideoTracks()[0];
        if (videoTrack) {
          console.log(`Default camera: ${videoTrack.label}`);
          
          try {
            const capabilities = videoTrack.getCapabilities();
            console.log("Camera capabilities:", capabilities);
          } catch (e) {
            console.log("Could not get camera capabilities:", e);
          }
        }
        
        // Stop the stream
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        console.error("❌ Camera permission DENIED at startup:", error);
        
        // Alert the user about permission issues
        alert("Camera access was denied. You'll need to grant permission to use your camera devices.");
      }
    }
    
    checkCameraPermissions();
  }, [mode]);
  
  // Detect available camera devices
  useEffect(() => {
    // Skip device enumeration in client mode
    if (mode === 'client') return;
    
    const getAvailableCameras = async () => {
      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
        console.log("Media devices API not supported in this browser");
        return;
      }
      
      try {
        // Request permissions first to get proper device labels
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ video: true });
          stream.getTracks().forEach(track => track.stop());
        } catch (permissionError) {
          console.log("Camera permission denied, continuing without full labels");
        }
        
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');
        
        setAvailableCameras(videoDevices.map((device, index) => ({
          id: device.deviceId || `device-${index}`,
          label: device.label || `Camera ${index + 1}`
        })));
        
        console.log(`Found ${videoDevices.length} video input devices`);
      } catch (error) {
        console.error("Error enumerating devices:", error);
      }
    };
    
    getAvailableCameras();
  }, [mode]);
  
  // Update camera status in Firestore - with throttling and cache
  const updateCameraStatusInFirestore = async (index, isActive, source = null) => {
    try {
      // Skip updating during initial load
      if (!initialLoadComplete) {
        return;
      }
      
      // Skip updates in client mode
      if (mode === 'client') {
        return;
      }
      
      // Update our active streams reference
      activeStreamsRef.current[index] = {
        active: isActive,
        source: source
      };
      
      // Check if we've recently updated this camera with the same state
      const cacheKey = `camera-${index}-${isActive}-${source}`;
      const lastUpdate = localStorage.getItem(cacheKey);
      const now = Date.now();
      
      if (lastUpdate && now - parseInt(lastUpdate) < 5000) {
        // Skip this update if it's the same as the last one within 5 seconds
        return;
      }
      
      console.log(`Updating Firestore for camera ${index}: active=${isActive}, source=${source}`);
      
      // Store this update in cache
      localStorage.setItem(cacheKey, now.toString());
      
      const db = getFirestore();
      // Get all camera documents
      const camerasQuery = query(collection(db, 'cameraStatus'));
      const snapshot = await getDocs(camerasQuery);
      const existingCameras = [];
      
      snapshot.forEach(doc => {
        existingCameras.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      // Find if this camera already exists
      let targetCamera = null;
      let targetIndex = -1;
      
      existingCameras.forEach((camera, idx) => {
        // Match by index or label containing index+1
        if (idx === index || (camera.label && camera.label.includes(`${index + 1}`))) {
          targetCamera = camera;
          targetIndex = idx;
        }
      });
      
      if (targetCamera) {
        // Update existing camera
        console.log(`Updating existing camera ${index} (${targetCamera.id}) to active=${isActive}, source=${source}`);
        const cameraRef = doc(db, 'cameraStatus', targetCamera.id);
        
        await updateDoc(cameraRef, {
          active: isActive,
          source: source,
          lastUpdated: serverTimestamp(),
          serverSessionId: mode === 'server' ? serverSessionId : null
        });
      } else {
        // Create new camera
        console.log(`Creating new camera ${index} with active=${isActive}, source=${source}`);
        const newCamera = {
          label: `Camera ${index + 1}`,
          active: isActive,
          source: source,
          lastUpdated: serverTimestamp(),
          createdBy: currentUser?.uid || 'anonymous',
          isDemoCamera: source === 'demo',
          serverSessionId: mode === 'server' ? serverSessionId : null
        };
        
        await setDoc(doc(collection(db, 'cameraStatus')), newCamera);
      }
    } catch (error) {
      console.error(`Error updating camera ${index} status:`, error);
    }
  };
  
  // Add a dedicated camera status updater that runs less frequently and checks properly
  useEffect(() => {
    // Skip if initial load is not complete or in client mode
    if (!initialLoadComplete || mode === 'client') return;
    
    console.log("Setting up smarter camera status updater effect");
    
    // Force immediate update on mount for all cameras - but wait longer to ensure proper status
    const forceCameraUpdates = () => {
      console.log("Performing initial camera status check");
      
      // Check actual video elements for activity
      for (let i = 0; i < cameraCount; i++) {
        const container = document.querySelector(`[data-camera-index="${i}"]`);
        if (container) {
          // Find the actual video element
          const videoEl = container.querySelector('video');
          const select = document.querySelector(`[data-camera-select="${i}"]`);
          
          // Get select value - the intended state
          const value = select ? select.value : "off";
          
          // Check if the video is ACTUALLY playing - the true state
          const isVideoActive = videoEl && 
                             videoEl.srcObject && 
                             !videoEl.paused && 
                             (videoEl.currentTime > 0 || videoEl.srcObject.active);
                             
          // If video has src attribute, it might be a demo video
          const isVideoSrcActive = videoEl && 
                               videoEl.src && 
                               videoEl.src.includes('.mp4') && 
                               !videoEl.paused;
                               
          // Check if it's an OBS camera with special handling
          const isOBS = isOBSCamera(value);
          const isOBSActive = isOBS && videoEl && videoEl.srcObject;
                             
          // Determine the actual active state
          const isActive = isVideoActive || isVideoSrcActive || isOBSActive;
          
          // Use the select value for source - what the user selected
          const source = value !== "off" ? value : null;
          
          console.log(`Camera ${i} status: select=${value}, videoActive=${isVideoActive}, srcActive=${isVideoSrcActive}, isOBS=${isOBS}, isOBSActive=${isOBSActive}, final=${isActive}`);
          
          // Update Firestore with the CORRECT status
          updateCameraStatusInFirestore(i, isActive, source);
        }
      }
    };
    
    // Run initial update with a longer delay to ensure videos have started
    const initialUpdateTimer = setTimeout(forceCameraUpdates, 5000);
    
    // Regular status update interval - less frequent
    const statusUpdater = setInterval(() => {
      if (systemKilled) {
        return;
      }
      
      // Similar to the initial update, but with less logging
      for (let i = 0; i < cameraCount; i++) {
        const container = document.querySelector(`[data-camera-index="${i}"]`);
        if (container) {
          const videoEl = container.querySelector('video');
          const select = document.querySelector(`[data-camera-select="${i}"]`);
          const value = select ? select.value : "off";
          
          // Check if the video is ACTUALLY playing
          const isVideoActive = videoEl && 
                             videoEl.srcObject && 
                             !videoEl.paused && 
                             (videoEl.currentTime > 0 || videoEl.srcObject.active);
                             
          // If video has src attribute, it might be a demo video
          const isVideoSrcActive = videoEl && 
                               videoEl.src && 
                               videoEl.src.includes('.mp4') && 
                               !videoEl.paused;
          
          // Check if it's an OBS camera with special handling
          const isOBS = isOBSCamera(value);
          const isOBSActive = isOBS && videoEl && videoEl.srcObject;
                             
          // Determine the actual active state
          const isActive = isVideoActive || isVideoSrcActive || isOBSActive;
          
          // Use the user's selected source
          const source = value !== "off" ? value : null;
          
          // Only update if there's actually a change to avoid flooding Firestore
          updateCameraStatusInFirestore(i, isActive, source);
        }
      }
    }, 10000); // Update every 10 seconds - much less frequent
    
    return () => {
      clearTimeout(initialUpdateTimer);
      clearInterval(statusUpdater);
    };
  }, [cameraCount, systemKilled, initialLoadComplete, mode, currentUser]);
  
  // NEW EFFECT: Keep streams alive when page is not visible (server mode only)
  useEffect(() => {
    // Only run in server mode
    if (mode !== 'server') return;
    
    console.log("Setting up video keepalive effect for server mode");
    
    // Function to ensure all active videos stay playing
    const ensureVideosPlaying = () => {
      if (systemKilled) return; // Skip if system is killed
      
      // Iterate through all video elements
      document.querySelectorAll('video').forEach((videoEl, idx) => {
        // Skip if video doesn't exist or isn't in document
        if (!videoEl || !document.body.contains(videoEl)) return;
        
        // Get the camera index
        const container = videoEl.closest('[data-camera-index]');
        if (!container) return;
        
        const cameraIndex = container.getAttribute('data-camera-index');
        if (cameraIndex === null) return;
        
        // Get the select value
        const select = document.querySelector(`[data-camera-select="${cameraIndex}"]`);
        if (!select || select.value === 'off') return;
        
        // Check if video element is playing
        if (videoEl.paused) {
          console.log(`Keepalive: Camera ${cameraIndex} video is paused, restarting...`);
          
          // If it has srcObject (real camera), restart it
          if (videoEl.srcObject) {
            videoEl.play().catch(error => {
              if (error.name !== 'AbortError') {
                console.log(`Error restarting camera ${cameraIndex}:`, error);
              }
            });
          } 
          // If it has src (demo video), restart it
          else if (videoEl.src && videoEl.src.includes('.mp4')) {
            videoEl.load();
            videoEl.play().catch(error => {
              if (error.name !== 'AbortError') {
                console.log(`Error restarting demo video ${cameraIndex}:`, error);
              }
            });
          }
          // If it has neither, try to restore from our active streams reference
          else {
            const streamInfo = activeStreamsRef.current[cameraIndex];
            if (streamInfo && streamInfo.active && streamInfo.source) {
              console.log(`Restoring camera ${cameraIndex} from savedState: ${streamInfo.source}`);
              
              // Force camera restart
              select.value = streamInfo.source;
              select.dispatchEvent(new Event('change', { bubbles: true }));
            }
          }
        }
        
        // Special OBS camera handling
        if (select.value !== 'off' && select.value !== 'demo' && isOBSCamera(select.value)) {
          forceOBSCameraPlay(videoEl, select.value, cameraIndex);
        }
      });
    };
    
    // Run the keepalive immediately
    ensureVideosPlaying();
    
    // Set up regular interval for keepalive - more frequent in hidden state
    const keepaliveInterval = setInterval(() => {
      if (document.hidden) {
        // When page is hidden, check more frequently and log it
        console.log("Hidden keepalive check running...");
        ensureVideosPlaying();
      } else {
        // When page is visible, still check occasionally
        ensureVideosPlaying();
      }
    }, document.hidden ? 2000 : 5000);
    
    // Change interval frequency when visibility changes
    const handleVisChange = () => {
      clearInterval(keepaliveInterval);
      
      // Restart with appropriate interval
      setInterval(ensureVideosPlaying, document.hidden ? 2000 : 5000);
      
      // Force immediate check on visibility change
      ensureVideosPlaying();
    };
    
    document.addEventListener('visibilitychange', handleVisChange);
    
    return () => {
      clearInterval(keepaliveInterval);
      document.removeEventListener('visibilitychange', handleVisChange);
    };
  }, [mode, systemKilled]);
  
  // Helper function to log video element state for debugging
  const logVideoElementState = (videoEl, cameraIndex) => {
    if (!videoEl) return;
    
    console.log(`Camera ${cameraIndex} state:`, {
      paused: videoEl.paused,
      currentTime: videoEl.currentTime,
      readyState: videoEl.readyState,
      networkState: videoEl.networkState,
      hasSource: !!videoEl.srcObject,
      hasVideoTrack: videoEl.srcObject ? videoEl.srcObject.getVideoTracks().length > 0 : false,
      trackEnabled: videoEl.srcObject && videoEl.srcObject.getVideoTracks().length > 0 
        ? videoEl.srcObject.getVideoTracks()[0].enabled : false,
      trackReadyState: videoEl.srcObject && videoEl.srcObject.getVideoTracks().length > 0 
        ? videoEl.srcObject.getVideoTracks()[0].readyState : 'no track',
      videoWidth: videoEl.videoWidth,
      videoHeight: videoEl.videoHeight
    });
  };
  
  // IMPROVED: Start a real camera stream with enhanced background capabilities
  const startRealCamera = async (videoElement, deviceId, cameraIndex) => {
    if (!videoElement || !deviceId) return () => {}; // Return empty function instead of undefined
    
    console.log(`Starting REAL camera for camera ${cameraIndex} with device ID: ${deviceId}`);
    
    // Check if this is OBS Virtual Camera based on deviceId or label
    const isOBS = isOBSCamera(deviceId);
    
    if (isOBS) {
      console.log(`⚠️ OBS Virtual Camera detected for camera ${cameraIndex}!`);
    }
    
    try {
      // Clean up existing streams
      if (videoElement.srcObject) {
        try {
          const tracks = videoElement.srcObject.getTracks();
          tracks.forEach(track => track.stop());
          videoElement.srcObject = null;
        } catch (e) {
          console.log("Error cleaning up existing stream:", e);
        }
      }
      
      // Reset video element completely
      videoElement.pause();
      videoElement.src = "";
      videoElement.srcObject = null;
      videoElement.load();
      
      // For demo fallback
      if (deviceId === "demo") {
        return startDemoVideo(videoElement, cameraIndex);
      }
      
      // IMPROVED CONSTRAINTS - CRITICAL FOR PERSISTENCE
      const constraints = {
        video: {
          deviceId: deviceId ? { exact: deviceId } : undefined,
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 15 },  // Lower frameRate to conserve resources
        },
        audio: false
      };
      
      // For OBS, use less strict constraints
      if (isOBS) {
        constraints.video = {
          deviceId: deviceId ? { ideal: deviceId } : undefined,
          width: { ideal: 640 },
          height: { ideal: 480 },
          frameRate: { ideal: 10 }
        };
      }
      
      console.log(`Requesting camera with constraints:`, constraints);
      
      // Request camera access - with retry
      let stream;
      try {
        stream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log(`Camera stream obtained successfully for camera ${cameraIndex}`);
        
        // ADDED: Debug the stream to see what tracks are available
        const videoTracks = stream.getVideoTracks();
        console.log(`Got ${videoTracks.length} video tracks:`, 
                   videoTracks.map(t => `${t.label} (enabled: ${t.enabled})`));
                   
        // CRITICAL: Set properties for persistent streams
        if (videoTracks.length > 0) {
          const videoTrack = videoTracks[0];
          if ('contentHint' in videoTrack) {
            videoTrack.contentHint = 'motion'; // Tell browser this needs to stay active
          }
          
          // This is a critical undocumented property that helps with background operation
          if (videoTrack.enabled) {
            // @ts-ignore - Property doesn't exist in type definitions
            videoTrack._persistentOperation = true;
          }
        }
      } catch (accessError) {
        console.error(`Camera access failed for deviceId ${deviceId}:`, accessError);
        
        // IMPROVED: For OBS/virtual cameras, try a less strict constraint
        if (accessError.name === 'OverconstrainedError' || 
            accessError.name === 'ConstraintNotSatisfiedError') {
          console.log("Trying less strict constraints for virtual camera...");
          try {
            stream = await navigator.mediaDevices.getUserMedia({ 
              video: { deviceId: { ideal: deviceId } } 
            });
            console.log("Less strict constraint worked!");
          } catch (retryError) {
            console.error("Retry with less strict constraints failed:", retryError);
            
            // For OBS, try without any deviceId constraint
            if (isOBS) {
              try {
                console.log("Trying with no device constraints for OBS...");
                stream = await navigator.mediaDevices.getUserMedia({ video: true });
                console.log("Generic camera access worked for OBS!");
              } catch (obsError) {
                console.error("OBS access with generic constraints failed:", obsError);
                return startDemoVideo(videoElement, cameraIndex);
              }
            } else {
              // Fall back to generic camera
              try {
                console.log("Attempting fallback to any camera...");
                stream = await navigator.mediaDevices.getUserMedia({ video: true });
                console.log("Generic camera access granted");
              } catch (genericError) {
                console.error("Generic camera access also failed:", genericError);
                
                // Fall back to demo video
                console.log("Falling back to demo video due to camera access failures");
                return startDemoVideo(videoElement, cameraIndex);
              }
            }
          }
        } else {
          // Try a more generic request if specific device failed
          try {
            console.log("Attempting fallback to any camera...");
            stream = await navigator.mediaDevices.getUserMedia({ video: true });
            console.log("Generic camera access granted");
          } catch (genericError) {
            console.error("Generic camera access also failed:", genericError);
            
            // Fall back to demo video
            console.log("Falling back to demo video due to camera access failures");
            return startDemoVideo(videoElement, cameraIndex);
          }
        }
      }
      
      // Safe check that video element is still in document
      if (!document.body.contains(videoElement)) {
        console.log(`Video element for camera ${cameraIndex} no longer in document, stopping stream`);
        stream.getTracks().forEach(track => track.stop());
        return () => {}; // Return empty function instead of undefined
      }
      
      // Display device info
      const videoTrack = stream.getVideoTracks()[0];
      if (videoTrack) {
        console.log(`Using camera: ${videoTrack.label}`);
        
        // ADDED: Make sure track is enabled
        if (!videoTrack.enabled) {
          console.log(`Track was disabled, enabling it now...`);
          videoTrack.enabled = true;
        }
        
        try {
          const capabilities = videoTrack.getCapabilities();
          console.log(`Camera capabilities for ${cameraIndex}:`, capabilities);
        } catch (e) {
          console.log("Could not get camera capabilities:", e);
        }
      }
      
      // IMPROVED: Attach stream to video element - with enhanced setup process
      try {
        // First, clear any existing source
        videoElement.srcObject = null;
        videoElement.src = "";
        
        // Set video attributes for better persistence
        videoElement.setAttribute('autoplay', 'true');
        videoElement.setAttribute('playsinline', 'true');
        videoElement.setAttribute('muted', 'true');
        videoElement.setAttribute('disablePictureInPicture', 'true');
        videoElement.setAttribute('disableRemotePlayback', 'true');
        videoElement.muted = true;
        videoElement.volume = 0;
        
        // IMPORTANT: Wait a small amount before setting the new source
        // This helps with virtual cameras like OBS
        setTimeout(() => {
          // Attach the stream
          videoElement.srcObject = stream;
          videoElement.muted = true;
          videoElement.volume = 0; // Ensure volume is zero
          
          // Clear any errors
          setStreamErrors(prev => ({ ...prev, [cameraIndex]: null }));
          
          // MOST IMPORTANT ADDITION: Force continuous playback
          const forceContinuousPlayback = () => {
            if (!videoElement.srcObject || !document.body.contains(videoElement)) return;
            
            if (videoElement.paused) {
              console.log(`Force playing camera ${cameraIndex}`);
              videoElement.play().catch(e => {
                console.log(`Couldn't force play: ${e.message}`);
              });
            }
            
            // Check again after a short delay
            videoElement._playbackTimer = setTimeout(forceContinuousPlayback, 2000);
          };
          
          // Start continuous playback enforcement
          forceContinuousPlayback();
          
          // Play with robust error handling
          videoElement.play()
            .then(() => {
              console.log(`Camera ${cameraIndex} playback started successfully`);
            })
            .catch((playError) => {
              if (playError.name === 'AbortError') {
                console.log("Play aborted - this is normal during changes");
                
                // Try playing again after a brief delay
                setTimeout(() => {
                  if (document.body.contains(videoElement)) {
                    videoElement.play().catch(e => {
                      if (e.name !== 'AbortError') {
                        console.log(`Retry play error for camera ${cameraIndex}:`, e);
                      }
                    });
                  }
                }, 500);
              } else {
                console.error(`Play error for camera ${cameraIndex}:`, playError);
                setStreamErrors(prev => ({ 
                  ...prev, 
                  [cameraIndex]: `Play error: ${playError.message}`
                }));
              }
            });
            
          // Special handling for OBS cameras
          if (isOBS) {
            console.log(`🛠️ Adding extra OBS camera handling for camera ${cameraIndex}`);
            forceOBSCameraPlay(videoElement, deviceId, cameraIndex);
            
            // For OBS, let's also try to detect if video is actually showing
            // even when normal browser checks say it isn't
            setTimeout(() => {
              if (videoElement.videoWidth > 0 && videoElement.videoHeight > 0) {
                console.log(`✅ OBS camera ${cameraIndex} is actually showing video!`);
                
                // Create a quick temporary canvas to check pixel data
                // This can help determine if the camera is sending real frames
                try {
                  const canvas = document.createElement('canvas');
                  canvas.width = 1;
                  canvas.height = 1;
                  const ctx = canvas.getContext('2d');
                  ctx.drawImage(videoElement, 0, 0, 1, 1);
                  const pixel = ctx.getImageData(0, 0, 1, 1).data;
                  console.log(`OBS camera ${cameraIndex} pixel sample:`, 
                            pixel[0], pixel[1], pixel[2], pixel[3]);
                            
                  // If we're getting actual pixel data, consider it active
                  if (pixel[3] > 0) {
                    console.log(`⭐ OBS camera ${cameraIndex} has active pixel data!`);
                    updateCameraStatusInFirestore(cameraIndex, true, deviceId);
                  }
                } catch (e) {
                  console.log('Canvas pixel check error:', e);
                }
              }
            }, 2000);
          }
        }, 100);
      } catch (streamError) {
        console.error(`Error attaching stream for camera ${cameraIndex}:`, streamError);
        setStreamErrors(prev => ({
          ...prev,
          [cameraIndex]: `Stream error: ${streamError.message}`
        }));
        
        // Clean up failed stream
        stream.getTracks().forEach(track => track.stop());
      }
      
      // IMPROVED: Enhanced heartbeat with better virtual camera support
      const heartbeat = setInterval(() => {
        // Check if element still exists
        if (!document.body.contains(videoElement)) {
          clearInterval(heartbeat);
          return;
        }
        
        // Check if stream is still active
        if (videoElement.srcObject) {
          const videoTracks = videoElement.srcObject.getVideoTracks();
          if (videoTracks.length > 0) {
            const videoTrack = videoTracks[0];
            
            // Log if track is not enabled or not ready state
            if (!videoTrack.enabled) {
              console.log(`Camera ${cameraIndex} track is disabled, re-enabling...`);
              videoTrack.enabled = true;
            }
            
            // Check if track has proper readyState
            if (videoTrack.readyState !== 'live') {
              console.log(`Camera ${cameraIndex} track ready state: ${videoTrack.readyState}`);
            }
          } else {
            console.log(`Camera ${cameraIndex} has no video tracks`);
          }
        } else if (!videoElement.paused && !videoElement.src) {
          console.log(`Camera ${cameraIndex} has no srcObject but is not paused`);
        }
        
        // Check if video is paused but should be playing
        if (videoElement.paused && videoElement.srcObject) {
          console.log(`Camera ${cameraIndex} is paused, trying to resume...`);
          videoElement.play().catch(e => {
            if (e.name !== 'AbortError') {
              console.log(`Error resuming camera ${cameraIndex}:`, e);
            }
          });
        }
        
        // For OBS cameras, do an extra check and force play
        if (isOBS) {
          forceOBSCameraPlay(videoElement, deviceId, cameraIndex);
        }
        
        // Visual indicator - add a DEBUG class to show the heartbeat is working
        const container = document.querySelector(`[data-camera-index="${cameraIndex}"]`);
        if (container) {
          container.classList.add('heartbeat-active');
          setTimeout(() => {
            container.classList.remove('heartbeat-active');
          }, 500);
        }
      }, 2000);
      
      // Return cleanup function
      return () => {
        clearInterval(heartbeat);
        
        // Clear any continuous playback timers
        if (videoElement._playbackTimer) {
          clearTimeout(videoElement._playbackTimer);
        }
        
        // Only clean up if video element is still in document
        if (document.body.contains(videoElement) && videoElement.srcObject) {
          const tracks = videoElement.srcObject.getTracks();
          tracks.forEach(track => track.stop());
          videoElement.srcObject = null;
        }
      };
    } catch (error) {
      console.error(`Unexpected error in startRealCamera for ${cameraIndex}:`, error);
      setStreamErrors(prev => ({
        ...prev,
        [cameraIndex]: `Camera error: ${error.message}`
      }));
      
      // Fall back to demo
      return startDemoVideo(videoElement, cameraIndex);
    }
  };
  
  // Improved demo video playback with enhanced keep-alive mechanisms
  const startDemoVideo = (videoElement, index) => {
    if (!videoElement) return () => {};
    
    console.log(`Setting up ROBUST demo video for camera ${index}`);
    
    // Clear any existing playback
    if (videoElement.srcObject) {
      try {
        videoElement.srcObject.getTracks().forEach(track => track.stop());
      } catch (e) {}
      videoElement.srcObject = null;
    }
    
    // Complete reset of video element
    videoElement.pause();
    videoElement.removeAttribute('src');
    videoElement.load();
    
    // Store a reference to this video element in the window object to ensure it's accessible
    // from keep-alive timers even if React re-renders components
    if (!window.activeVideos) window.activeVideos = {};
    window.activeVideos[`demo-${index}`] = {
      element: videoElement,
      lastPlayAttempt: Date.now(),
      keepAliveFn: null,
      startTime: Date.now()
    };
    
    // Enhanced video properties - critical for reliable playback
    videoElement.muted = true;
    videoElement.defaultMuted = true;  // Redundant muting for browser compatibility
    videoElement.volume = 0;
    videoElement.loop = true;
    videoElement.autoplay = true;
    videoElement.preload = "auto";
    videoElement.crossOrigin = "anonymous";
    videoElement.setAttribute('playsinline', '');
    videoElement.playsInline = true;
    videoElement.setAttribute('disablePictureInPicture', 'true');
    videoElement.setAttribute('disableRemotePlayback', 'true');
    
    // Add CSS class for debugging/tracking
    videoElement.classList.add('demo-video');
    videoElement.dataset.demoIndex = index;
    
    // Choose video source - starting with highest chance of working
    // 1. Try local videos first
    const videoSources = [
      '/data/n.mp4',  // Original path
      '/static/media/demo.mp4', // Common in React apps
      '/demo.mp4',    // Root path
      '/videos/demo.mp4', // Common path
      '/assets/video/demo.mp4', // Another common path
      // 2. Fallback to reliable external sources if local fails
      'https://www.w3schools.com/html/mov_bbb.mp4', // Commonly used reliable test video
      'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4' // Another reliable source
    ];
    
    // Try sources sequentially with a recursive approach
    const trySource = (sourceIndex) => {
      if (sourceIndex >= videoSources.length) {
        console.error(`All video sources failed for camera ${index}`);
        return;
      }
      
      const source = videoSources[sourceIndex];
      console.log(`Trying video source ${sourceIndex}: ${source} for camera ${index}`);
      
      // Set the source
      videoElement.src = source;
      
      // Force browser to load the video
      videoElement.load();
      
      // Set up error handler to try next source
      const errorHandler = () => {
        console.log(`Source ${source} failed, trying next`);
        videoElement.removeEventListener('error', errorHandler);
        // Try next source
        trySource(sourceIndex + 1);
      };
      
      // Add error listener
      videoElement.addEventListener('error', errorHandler, { once: true });
      
      // Try to play
      videoElement.play().then(() => {
        console.log(`✅ Video source ${source} started successfully for camera ${index}`);
        videoElement.removeEventListener('error', errorHandler);
        
        // If successfully playing, update the stored info
        if (window.activeVideos && window.activeVideos[`demo-${index}`]) {
          window.activeVideos[`demo-${index}`].source = source;
          window.activeVideos[`demo-${index}`].playSuccess = true;
        }
        
        // Set up a powerful keepalive system
        setupKeepAliveSystem(videoElement, index, source);
        
        // Add special video sleep prevention
        const cleanupSleepPrevention = preventVideoSleep(videoElement, index);
        
        // Store cleanup function
        window.activeVideos[`demo-${index}`].cleanupSleepPrevention = cleanupSleepPrevention;
      }).catch(err => {
        console.log(`Play failed for source ${source}: ${err.message}`);
        
        // Try with a "user interaction" workaround
        console.log(`Trying with user interaction workaround for ${index}`);
        
        // Create a temporary button for "user interaction"
        const tempButton = document.createElement('button');
        tempButton.style.position = 'fixed';
        tempButton.style.top = '-100px'; // Off-screen
        tempButton.textContent = 'Play';
        document.body.appendChild(tempButton);
        
        // Click it and then play
        tempButton.click();
        videoElement.play().then(() => {
          console.log(`✅ Video started after interaction for camera ${index}`);
          videoElement.removeEventListener('error', errorHandler);
          
          // If successfully playing, update the stored info
          if (window.activeVideos && window.activeVideos[`demo-${index}`]) {
            window.activeVideos[`demo-${index}`].source = source;
            window.activeVideos[`demo-${index}`].playSuccess = true;
          }
          
          // Set up a powerful keepalive system
          setupKeepAliveSystem(videoElement, index, source);
          
          // Add special video sleep prevention
          const cleanupSleepPrevention = preventVideoSleep(videoElement, index);
          
          // Store cleanup function
          window.activeVideos[`demo-${index}`].cleanupSleepPrevention = cleanupSleepPrevention;
        }).catch(() => {
          // Play failed even with interaction - try next source
          videoElement.removeEventListener('error', errorHandler);
          trySource(sourceIndex + 1);
        });
        
        // Remove the temporary button
        setTimeout(() => {
          tempButton.remove();
        }, 500);
      });
    };
    
    // Start trying sources
    trySource(0);
    
    // CRITICAL: Set up a comprehensive keepalive system for the video
    const setupKeepAliveSystem = (videoEl, idx, source) => {
      console.log(`Setting up keepalive system for camera ${idx}`);
      
      // Store the current time periodically to detect freezes
      let lastTime = 0;
      let lastTimeUpdate = Date.now();
      let stuckCount = 0;
      
      // Define a robust playback check and restart function
      const ensurePlayback = () => {
        if (!document.body.contains(videoEl)) {
          // Video element no longer in document, stop checking
          clearInterval(playbackKeepAliveInterval);
          return;
        }
        
        // Check if playback is actually progressing
        const currentTime = videoEl.currentTime;
        const now = Date.now();
        
        // Detect if playback is frozen (same time for too long)
        if (Math.abs(currentTime - lastTime) < 0.01 && now - lastTimeUpdate > 2000) {
          stuckCount++;
          console.log(`Video may be stuck: count=${stuckCount}, currentTime=${currentTime}, lastTime=${lastTime}`);
          
          if (stuckCount >= 3) {
            // Video is definitely stuck, restart it completely
            console.log(`⚠️ Video for camera ${idx} is stuck at ${currentTime}, RESETTING...`);
            
            // Full reset process
            videoEl.pause();
            videoEl.removeAttribute('src');
            videoEl.load();
            videoEl.src = source;
            videoEl.load();
            
            // Reset counters
            stuckCount = 0;
            lastTimeUpdate = now;
            
            // Try to play again, but handle errors robustly
            videoEl.play().catch(e => {
              console.log(`Error restarting video: ${e.message}`);
              
              // If play fails, try setting src again after a delay
              setTimeout(() => {
                if (document.body.contains(videoEl)) {
                  videoEl.src = source;
                  videoEl.load();
                  videoEl.play().catch(() => {}); // Ignore errors on retry
                }
              }, 1000);
            });
          }
        } else if (Math.abs(currentTime - lastTime) > 0.01) {
          // Video is progressing normally, reset counter
          stuckCount = 0;
          lastTime = currentTime;
          lastTimeUpdate = now;
        }
        
        // Handle paused videos
        if (videoEl.paused) {
          console.log(`Demo video ${idx} is paused, trying to play...`);
          videoEl.play().catch(e => {
            console.log(`Cannot restart paused video: ${e.message}`);
          });
        }
        
        // Handle ended videos (shouldn't happen with loop=true, but just in case)
        if (videoEl.ended) {
          console.log(`Demo video ${idx} ended, looping...`);
          videoEl.currentTime = 0;
          videoEl.play().catch(() => {});
        }
      };
      
      // Keep track of all intervals so we can clear them on cleanup
      const intervals = [];
      
      // Primary keepalive interval - checks every 1 second
      const playbackKeepAliveInterval = setInterval(ensurePlayback, 1000);
      intervals.push(playbackKeepAliveInterval);
      
      // Full restart every 5 minutes to prevent any kind of slow degradation
      const periodicRestartInterval = setInterval(() => {
        if (document.body.contains(videoEl)) {
          console.log(`Performing scheduled restart for camera ${idx}`);
          
          // If we're still visible, just seek to beginning
          if (!document.hidden) {
            videoEl.currentTime = 0;
            videoEl.play().catch(() => {});
          } 
          // If hidden, do a full reload
          else {
            videoEl.src = source;
            videoEl.load();
            videoEl.play().catch(() => {});
          }
        } else {
          // Clean up if video is gone
          clearInterval(periodicRestartInterval);
        }
      }, 300000); // 5 minutes
      intervals.push(periodicRestartInterval);
      
      // Document visibility handler - critical for restarting video when tab becomes visible
      const handleVisibilityChange = () => {
        if (!document.hidden && document.body.contains(videoEl)) {
          console.log(`Document became visible, ensuring demo ${idx} is playing`);
          
          // When page becomes visible again, force video to play
          if (videoEl.paused) {
            console.log(`Demo ${idx} was paused when page became visible, restarting`);
            
            // Wait a moment for browser to stabilize after visibility change
            setTimeout(() => {
              if (document.body.contains(videoEl)) {
                videoEl.src = source;
                videoEl.load();
                videoEl.play().catch(() => {});
              }
            }, 200);
          }
        }
      };
      
      // Add visibility change listener
      document.addEventListener('visibilitychange', handleVisibilityChange);
      
      // Store the keepalive function in the global registry
      if (window.activeVideos && window.activeVideos[`demo-${idx}`]) {
        window.activeVideos[`demo-${idx}`].keepAliveFn = () => {
          intervals.forEach(clearInterval);
          document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
      }
      
      // Return cleanup function
      return () => {
        intervals.forEach(clearInterval);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        
        // Remove from global registry
        if (window.activeVideos && window.activeVideos[`demo-${idx}`]) {
          delete window.activeVideos[`demo-${idx}`];
        }
      };
    };
    
    // Set Firestore status - use a more reliable approach that waits for success
    const updateStatusWhenPlaying = () => {
      // Only update if video is actually playing
      if (document.body.contains(videoElement) && 
          !videoElement.paused && 
          videoElement.currentTime > 0) {
        console.log(`Demo video for camera ${index} is confirmed playing, updating Firestore`);
        updateCameraStatusInFirestore(index, true, "demo");
      } else {
        // If not yet playing, try again soon
        setTimeout(updateStatusWhenPlaying, 1000);
      }
    };
    
    // Start the status check after a delay to give video time to load
    setTimeout(updateStatusWhenPlaying, 1000);
    
    // Return a function that properly cleans up everything
    return () => {
      console.log(`Cleaning up demo video for camera ${index}`);
      
      // Call the keepalive cleanup if it exists
      if (window.activeVideos && 
          window.activeVideos[`demo-${index}`] && 
          typeof window.activeVideos[`demo-${index}`].keepAliveFn === 'function') {
        window.activeVideos[`demo-${index}`].keepAliveFn();
      }
      
      // Call sleep prevention cleanup if it exists
      if (window.activeVideos && 
          window.activeVideos[`demo-${index}`] && 
          typeof window.activeVideos[`demo-${index}`].cleanupSleepPrevention === 'function') {
        window.activeVideos[`demo-${index}`].cleanupSleepPrevention();
      }
      
      // Remove from tracking
      if (window.activeVideos) {
        delete window.activeVideos[`demo-${index}`];
      }
      
      // Properly clean up the video element
      if (document.body.contains(videoElement)) {
        videoElement.pause();
        videoElement.removeAttribute('src');
        videoElement.load(); // Important to free memory
      }
    };
  };
  
  // Camera View Component - Properly update status on source change
  const CameraView = ({ index }) => {
    const videoRef = useRef(null);
    const containerRef = useRef(null);
    const [selectValue, setSelectValue] = useState("off");
    const initialRenderRef = useRef(true);
    const cameraActiveRef = useRef(false);
    
    // THIS IS IMPORTANT: Prevent double initialization of the camera
    // This will prevent the camera from being reset right after it's selected
    useEffect(() => {
      // Skip the first render - this prevents the "off" state from being
      // set during the initial component mount
      if (initialRenderRef.current) {
        initialRenderRef.current = false;
        return;
      }
      
      // In client mode, don't actually start cameras
      if (mode === 'client') {
        return;
      }
      
      const videoElement = videoRef.current;
      if (!videoElement) return;
      
      console.log(`CameraView effect for camera ${index}, selection: ${selectValue}`);
      
      let cleanupFunction = null;
      
      // Add debug interval to regularly log camera state
      const debugInterval = setInterval(() => {
        if (document.body.contains(videoElement)) {
          logVideoElementState(videoElement, index);
        } else {
          clearInterval(debugInterval);
        }
      }, 5000); // Check every 5 seconds
      
      // Handle based on selection
      if (selectValue === "demo") {
        // Special handling for demo videos
        console.log(`Starting demo video for camera ${index}`);
        cleanupFunction = startDemoVideo(videoElement, index);
        
        // Make sure NoSleep is enabled for demo videos
        if (noSleepRef.current && mode === 'server') {
          try {
            noSleepRef.current.enable().catch(e => {
              console.log("NoSleep enable error (normal):", e);
            });
          } catch (e) {}
        }
        
        // Update Firestore AFTER starting the video, not before
        setTimeout(() => {
          // By now the video should have started playing
          const isPlaying = !videoElement.paused && videoElement.src;
          if (isPlaying) {
            console.log(`Demo video is playing for camera ${index}, marking as active`);
            updateCameraStatusInFirestore(index, true, "demo");
            cameraActiveRef.current = true;
          }
        }, 1000);
      } else if (selectValue === "off") {
        console.log(`Turning off camera ${index}`);
        
        // Stop any existing streams
        if (videoElement.srcObject) {
          try {
            const tracks = videoElement.srcObject.getTracks();
            tracks.forEach(track => track.stop());
            videoElement.srcObject = null;
          } catch (e) {
            console.log("Error stopping tracks:", e);
          }
        }
        
        // Reset video element
        try {
          videoElement.pause();
          videoElement.src = "";
          videoElement.load();
        } catch (e) {
          console.log("Error resetting video:", e);
        }
        
        // Update Firestore - camera is definitely off
        updateCameraStatusInFirestore(index, false, null);
        cameraActiveRef.current = false;
      } else {
        // Real camera
        console.log(`Starting real camera ${selectValue} for camera ${index}`);
        cleanupFunction = startRealCamera(videoElement, selectValue, index);
        
        // Make sure NoSleep is enabled for real cameras too
        if (noSleepRef.current && mode === 'server') {
          try {
            noSleepRef.current.enable().catch(e => {
              console.log("NoSleep enable error (normal):", e);
            });
          } catch (e) {}
        }
        
        // Add special handling for OBS Virtual Camera 
        // Update Firestore after a delay to check if camera actually started
        const isOBS = isOBSCamera(selectValue);
                                        
        // Show debugging info for OBS camera                                   
        if (isOBS) {
          console.log(`DETECTED OBS CAMERA: ${selectValue} with label: ${
            availableCameras.find(c => c.id === selectValue)?.label || 'unknown'
          }`);
        }
        
        setTimeout(() => {
          // Check if video is playing
          const isVideoActive = videoElement.srcObject && 
                          !videoElement.paused && 
                          videoElement.srcObject.active;
                          
          const tracks = videoElement.srcObject?.getVideoTracks() || [];
          const hasEnabledTrack = tracks.length > 0 && tracks[0].enabled;
          
          console.log(`Camera ${index} playing status check:`, {
            isVideoActive,
            hasEnabledTrack,
            trackCount: tracks.length,
            paused: videoElement.paused,
            active: videoElement.srcObject?.active,
          });
          
          if (isVideoActive || hasEnabledTrack) {
            console.log(`Real camera is playing for camera ${index}, marking as active`);
            updateCameraStatusInFirestore(index, true, selectValue);
            cameraActiveRef.current = true;
          } else if (isOBS) {
            // Special handling for OBS - sometimes the browser doesn't register it as "playing"
            // but it is actually working
            console.log(`OBS camera for ${index} might be playing, forcing active state`);
            updateCameraStatusInFirestore(index, true, selectValue);
            
            // Force play again for OBS
            videoElement.play().catch(e => {
              console.log(`OBS play error (expected) for camera ${index}:`, e.name);
            });
            
            cameraActiveRef.current = true;
          }
        }, 2000);
      }
      
      // Cleanup function
      return () => {
        clearInterval(debugInterval);
        
        if (typeof cleanupFunction === 'function') {
          cleanupFunction();
        }
        
        // Additional cleanup
        if (videoElement.srcObject) {
          try {
            const tracks = videoElement.srcObject.getTracks();
            tracks.forEach(track => track.stop());
            videoElement.srcObject = null;
          } catch (e) {
            console.log("Error in cleanup:", e);
          }
        }
      };
    }, [selectValue, index, mode]);
    
    // CRITICAL MODIFICATION: This prevents the selection from being lost
    // due to component re-renders or other React lifecycle issues
    useEffect(() => {
      // Don't do anything on first render
      if (initialRenderRef.current) {
        return;
      }
      
      // Skip in client mode
      if (mode === 'client') {
        return;
      }
      
      // This checks for unwanted resets of the camera selection 
      const checkCameraState = () => {
        // Get the current selection from the DOM
        const select = document.querySelector(`[data-camera-select="${index}"]`);
        if (select && select.value !== selectValue && select.value !== "off") {
          console.log(`FIXING: Camera ${index} selection mismatch. DOM has ${select.value} but state has ${selectValue}`);
          // Update our state to match the DOM
          setSelectValue(select.value);
        }
      };
      
      // Check shortly after mounting
      const timeout = setTimeout(checkCameraState, 500);
      return () => clearTimeout(timeout);
    }, [index, selectValue, mode]);
    
    // Handle selection change
    const handleSelect = (e) => {
      const value = e.target.value;
      console.log(`Camera ${index} selection changed to: ${value}`);
      setSelectValue(value);
      
      // Don't update Firestore immediately - wait for the video to start
      // This will be handled in the useEffect
      
      // If in server mode, also handle camera control via Firebase
      if (mode === 'server' && serverSessionId) {
        // For server mode, update the active streams reference
        activeStreamsRef.current[index] = {
          active: value !== 'off',
          source: value !== 'off' ? value : null
        };
      }
      
      // If in client mode, send command to server
      if (mode === 'client' && streamingContext?.clientConnected) {
        // Send camera control command to the server
        const sendCommand = async () => {
          try {
            const db = getFirestore();
            // Create a command document in Firestore
            await setDoc(doc(collection(db, 'cameraCommands')), {
              action: 'setCameraSource',
              cameraIndex: index,
              source: value,
              targetServer: streamingContext.serverInfo?.id,
              createdAt: serverTimestamp(),
              createdBy: currentUser?.uid || 'anonymous',
              processed: false
            });
            
            console.log(`Sent remote camera command: set camera ${index} to ${value}`);
          } catch (error) {
            console.error('Error sending camera command:', error);
          }
        };
        
        sendCommand();
      }
    };
    
    // OBS-specific styles to try to force the video to display
    const getVideoStyles = () => {
      if (selectValue !== 'off' && selectValue !== 'demo' && isOBSCamera(selectValue)) {
        // For OBS, use specific styles that might help display
        return {
          position: 'absolute',
          inset: 0,
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          backgroundColor: 'black',
          transform: 'scaleX(1)', // Force GPU acceleration
          zIndex: 1
        };
      }
      
      // Default styles
      return {};
    };
    
    // For client mode, we might need to display camera statuses from the server
    const renderClientModeCamera = () => {
      // Instead of local cameras, show the remote camera status
      return (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-90 text-white">
          <div className="text-center p-4">
            <div className="text-xl mb-2">Camera {index + 1}</div>
            <div className="text-sm mb-4">Remote View</div>
            <div className="flex justify-center mb-2">
              <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
              <span>Connected to server</span>
            </div>
            
            {/* Dropdown for remote control */}
            <select 
              className="mt-4 bg-gray-800 text-white text-xs rounded px-2 py-1"
              onChange={handleSelect}
              value={selectValue}
              data-camera-select={index}
            >
              <option value="off">Camera Off</option>
              <option value="demo">Demo Camera</option>
              {availableCameras.map((camera) => (
                <option key={`${index}-${camera.id}`} value={camera.id}>
                  {camera.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      );
    };
    
    return (
      <div 
        className="relative bg-gray-900 border rounded-lg overflow-hidden" 
        style={{ aspectRatio: '16/9' }}
        ref={containerRef}
        data-camera-index={index}
      >
        {/* Client mode status banner */}
        {mode === 'client' && (
          <div className="absolute top-0 left-0 right-0 bg-blue-600 bg-opacity-80 text-white text-xs py-1 px-2 z-20 text-center">
            Remote Camera - Controlled from this device
          </div>
        )}
        
        {/* Server mode status banner */}
        {mode === 'server' && (
          <div className="absolute top-0 left-0 right-0 bg-green-600 bg-opacity-80 text-white text-xs py-1 px-2 z-20 text-center">
            Server Mode - Running 24/7
          </div>
        )}
        
        <div className="absolute top-2 left-2 z-10 bg-gray-900 bg-opacity-70 rounded px-2 py-1 text-sm text-white">
          Camera {index + 1}
        </div>
        
        {/* Only show camera selector in server or standalone mode */}
        {mode !== 'client' && (
          <select 
            className="absolute top-2 right-2 z-10 bg-gray-800 text-white text-xs rounded px-2 py-1"
            onChange={handleSelect}
            value={selectValue}
            data-camera-select={index}
          >
            <option value="off">Camera Off</option>
            <option value="demo">Demo Camera</option>
            {availableCameras.map((camera) => (
              <option key={`${index}-${camera.id}`} value={camera.id}>
                {camera.label}
              </option>
            ))}
          </select>
        )}
        
        {/* Static video element with OBS-specific styles when needed */}
        <video 
          ref={videoRef}
          className="absolute inset-0 w-full h-full object-cover bg-black"
          style={getVideoStyles()}
          playsInline
          muted
          autoPlay
          loop
          disablePictureInPicture
          disableRemotePlayback
        />
        
        {/* No Signal overlay */}
        {selectValue === "off" && (
          <div className="absolute inset-0 flex items-center justify-center bg-black text-yellow-500">
            No Signal
          </div>
        )}
        
        {/* Error overlay */}
        {streamErrors[index] && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-80 text-red-500">
            <div className="text-center p-4">
              <div className="font-bold mb-2">Error</div>
              <div className="text-sm">{streamErrors[index]}</div>
            </div>
          </div>
        )}
        
        {/* Client mode overlay */}
        {mode === 'client' && renderClientModeCamera()}
      </div>
    );
  };
  
  // Apply camera count with persistent storage in Firestore
  const applyCameraCountChange = async () => {
    // Update camera count and clear save needed flag
    setCameraCount(pendingCameraCount);
    setSaveNeeded(false);
    
    try {
      const db = getFirestore();
      // Save the camera count to Firestore
      console.log(`Saving camera count ${pendingCameraCount} to Firestore`);
      
      // Look for an existing settings document
      const settingsQuery = query(collection(db, 'systemSettings'));
      const snapshot = await getDocs(settingsQuery);
      
      let settingsDoc = null;
      snapshot.forEach(doc => {
        settingsDoc = {
          id: doc.id,
          ...doc.data()
        };
      });
      
      if (settingsDoc) {
        // Update existing settings
        const settingsRef = doc(db, 'systemSettings', settingsDoc.id);
        await updateDoc(settingsRef, {
          cameraCount: pendingCameraCount,
          lastUpdated: serverTimestamp(),
          updatedBy: currentUser?.uid || 'anonymous'
        });
      } else {
        // Create new settings document
        const newSettings = {
          cameraCount: pendingCameraCount,
          lastUpdated: serverTimestamp(),
          createdBy: currentUser?.uid || 'anonymous'
        };
        
        await setDoc(doc(collection(db, 'systemSettings')), newSettings);
      }
      
      // Save the current camera states (in case new ones were added)
      const saveCameraStates = () => {
        // For any new cameras, save their current state
        for (let i = 0; i < pendingCameraCount; i++) {
          const select = document.querySelector(`[data-camera-select="${i}"]`);
          if (select) {
            const value = select.value;
            const isActive = value !== "off";
            const source = isActive ? value : null;
            
            // Update Firestore with the current state
            updateCameraStatusInFirestore(i, isActive, source);
          }
        }
      };
      
      // Save camera states
      saveCameraStates();
    } catch (error) {
      console.error('Error saving camera count to Firestore:', error);
    }
    
    // Update Firestore for any new cameras
    if (pendingCameraCount > cameraCount) {
      // Add new cameras
      for (let i = cameraCount; i < pendingCameraCount; i++) {
        await updateCameraStatusInFirestore(i, false, null);
      }
    }
  };
  
  // Kill all cameras
  const killAll = () => {
    // In client mode, send a command to the server
    if (mode === 'client' && streamingContext?.clientConnected) {
      const sendKillCommand = async () => {
        try {
          const db = getFirestore();
          await setDoc(doc(collection(db, 'cameraCommands')), {
            action: 'killAll',
            targetServer: streamingContext.serverInfo?.id,
            createdAt: serverTimestamp(),
            createdBy: currentUser?.uid || 'anonymous',
            processed: false
          });
          
          console.log('Sent remote kill command to server');
        } catch (error) {
          console.error('Error sending kill command:', error);
        }
      };
      
      sendKillCommand();
      return;
    }
    
    // Store current states before killing
    const state = {};
    document.querySelectorAll('[data-camera-select]').forEach((select) => {
      const index = select.getAttribute('data-camera-select');
      state[index] = select.value;
      
      // Change to off
      select.value = 'off';
      select.dispatchEvent(new Event('change', { bubbles: true }));
      
      // Update Firestore
      updateCameraStatusInFirestore(index, false, null);
    });
    
    // Set system killed state and store prior states
    setPriorCameraStates(state);
    setSystemKilled(true);
    
    // Update Firestore to indicate system is killed
    try {
      const db = getFirestore();
      const settingsQuery = query(collection(db, 'systemSettings'));
      const snapshot = getDocs(settingsQuery);
      
      snapshot.then(snapshot => {
        let settingsDoc = null;
        snapshot.forEach(doc => {
          settingsDoc = {
            id: doc.id,
            ...doc.data()
          };
        });
        
        if (settingsDoc) {
          const settingsRef = doc(db, 'systemSettings', settingsDoc.id);
          updateDoc(settingsRef, {
            systemKilled: true,
            killedAt: serverTimestamp(),
            killedBy: currentUser?.uid || 'anonymous'
          }).catch(error => {
            console.error('Error updating system killed status:', error);
          });
        }
      });
    } catch (error) {
      console.error('Error updating killed status:', error);
    }
  };
  
  // Restore cameras
  const restoreCameras = () => {
    // In client mode, send a command to the server
    if (mode === 'client' && streamingContext?.clientConnected) {
      const sendRestoreCommand = async () => {
        try {
          const db = getFirestore();
          await setDoc(doc(collection(db, 'cameraCommands')), {
            action: 'restoreCameras',
            targetServer: streamingContext.serverInfo?.id,
            createdAt: serverTimestamp(),
            createdBy: currentUser?.uid || 'anonymous',
            processed: false
          });
          
          console.log('Sent restore cameras command to server');
        } catch (error) {
          console.error('Error sending restore command:', error);
        }
      };
      
      sendRestoreCommand();
      return;
    }
    
    // Restore from prior states
    document.querySelectorAll('[data-camera-select]').forEach((select) => {
      const index = select.getAttribute('data-camera-select');
      if (priorCameraStates[index]) {
        // Restore value
        select.value = priorCameraStates[index];
        select.dispatchEvent(new Event('change', { bubbles: true }));
        
        // Update Firestore
        const isActive = priorCameraStates[index] !== "off";
        updateCameraStatusInFirestore(
          index, 
          isActive, 
          isActive ? priorCameraStates[index] : null
        );
      }
    });
    
    // Clear system killed state
    setSystemKilled(false);
    
    // Update Firestore to indicate system is restored
    try {
      const db = getFirestore();
      const settingsQuery = query(collection(db, 'systemSettings'));
      const snapshot = getDocs(settingsQuery);
      
      snapshot.then(snapshot => {
        let settingsDoc = null;
        snapshot.forEach(doc => {
          settingsDoc = {
            id: doc.id,
            ...doc.data()
          };
        });
        
        if (settingsDoc) {
          const settingsRef = doc(db, 'systemSettings', settingsDoc.id);
          updateDoc(settingsRef, {
            systemKilled: false,
            restoredAt: serverTimestamp(),
            restoredBy: currentUser?.uid || 'anonymous'
          }).catch(error => {
            console.error('Error updating system restored status:', error);
          });
        }
      });
    } catch (error) {
      console.error('Error updating restored status:', error);
    }
  };
  
  // Get grid class based on camera count
  const getGridClass = () => {
    switch (cameraCount) {
      case 1: return 'grid-cols-1';
      case 2: return 'grid-cols-2';
      case 4: return 'grid-cols-2';
      case 6: return 'grid-cols-3';
      case 9: return 'grid-cols-3';
      case 12: return 'grid-cols-4';
      case 16: return 'grid-cols-4';
      default: return 'grid-cols-2';
    }
  };
  
  // Get network status color
  const getNetworkStatusColor = () => {
    switch (networkStatus) {
      case "connected": return "bg-green-500";
      case "unstable": return "bg-yellow-500";
      case "disconnected": return "bg-red-500";
      default: return "bg-gray-500";
    }
  };
  
  // Get signal strength bars
  const renderSignalBars = () => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((level) => (
          <div 
            key={`signal-bar-${level}`}
            className={`h-${level} w-1 rounded-sm ${level <= signalStrength ? 'bg-green-500' : 'bg-gray-600'}`}
          ></div>
        ))}
      </div>
    );
  };
  
  // Get color for latency
  const getLatencyColor = () => {
    if (networkLatency < 30) return "text-green-400";
    if (networkLatency < 60) return "text-yellow-400";
    return "text-red-400";
  };
  
  // Get color for packet loss
  const getPacketLossColor = () => {
    if (packetLoss < 0.5) return "text-green-400";
    if (packetLoss < 1.0) return "text-yellow-400";
    return "text-red-400";
  };
  
  // Count active cameras
  const countActiveCameras = () => {
    let count = 0;
    document.querySelectorAll('[data-camera-select]').forEach(select => {
      if (select.value !== "off") count++;
    });
    return count;
  };
  
  // Start server mode
  const startServerMode = async () => {
    if (!streamingContext) return;
    
    // Enable NoSleep first
    try {
      await noSleepRef.current.enable();
      console.log("NoSleep enabled successfully");
    } catch (e) {
      console.warn("NoSleep couldn't be enabled directly:", e);
      
      // Try with user gesture simulation
      const tempButton = document.createElement('button');
      tempButton.style.position = 'fixed';
      tempButton.style.top = '-100px';
      tempButton.textContent = 'Enable NoSleep';
      document.body.appendChild(tempButton);
      
      // Click the button to simulate user interaction
      tempButton.click();
      
      try {
        await noSleepRef.current.enable();
        console.log("NoSleep enabled via interaction simulation");
      } catch (err) {
        console.error("Failed to enable NoSleep even with interaction:", err);
      }
      
      // Remove the temporary button
      tempButton.remove();
    }
    
    // Create silent audio to help keep page active
    createSilentAudio();
    
    // Start the server
    const started = await streamingContext.startServerSession();
    if (started) {
      console.log("Started server mode!");
    }
  };
  
  // Stop server mode
  const stopServerMode = async () => {
    if (!streamingContext) return;
    
    // Disable NoSleep when stopping server
    try {
      noSleepRef.current.disable();
    } catch (e) {
      console.error("Error disabling NoSleep:", e);
    }
    
    // Clean up silent audio
    if (window._silentAudio) {
      window._silentAudio.pause();
      window._silentAudio.remove();
      window._silentAudio = null;
    }
    
    const stopped = await streamingContext.stopServerSession();
    if (stopped) {
      console.log("Stopped server mode!");
    }
  };
  
  // Connect to server
  const connectToServer = async (serverId) => {
    if (!streamingContext) return;
    
    const disconnect = await streamingContext.connectToServer(serverId);
    if (disconnect) {
      console.log("Connected to server!");
    }
  };
  
  // Disconnect from server
  const disconnectFromServer = async () => {
    if (!streamingContext) return;
    
    const disconnected = await streamingContext.disconnectFromServer();
    if (disconnected) {
      console.log("Disconnected from server!");
    }
  };
  
  // Server mode controls
  const renderServerControls = () => {
    if (!streamingContext) return null;
    
    return (
      <div className="bg-gray-800 rounded-lg p-4 mb-6">
        <h2 className="text-xl font-semibold mb-4">Streaming Server Controls</h2>
        
        {mode === 'server' ? (
          <div>
            <div className="flex items-center bg-green-800 rounded-lg p-3 mb-4">
              <div className="w-3 h-3 rounded-full bg-green-400 mr-2"></div>
              <span>Running as dedicated streaming server</span>
            </div>
            
            <div className="mb-4">
              <label className="flex items-center">
                <input 
                  type="checkbox" 
                  checked={remoteControlEnabled}
                  onChange={(e) => setRemoteControlEnabled(e.target.checked)}
                  className="mr-2"
                />
                <span>Allow remote control from clients</span>
              </label>
            </div>
            
            <button
              onClick={stopServerMode}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
            >
              Stop Server Mode
            </button>
          </div>
        ) : mode === 'client' ? (
          <div>
            <div className="flex items-center bg-blue-800 rounded-lg p-3 mb-4">
              <div className="w-3 h-3 rounded-full bg-blue-400 mr-2"></div>
              <span>Connected to streaming server {streamingContext.serverInfo?.id}</span>
            </div>
            
            <button
              onClick={disconnectFromServer}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
            >
              Disconnect from Server
            </button>
          </div>
        ) : (
          <div>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <button
                onClick={startServerMode}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
              >
                Start Server Mode
              </button>
              
              <div>
                <h3 className="text-sm font-semibold mb-2">Available Servers</h3>
                {streamingContext.streamingSessions.length > 0 ? (
                  <div className="space-y-2">
                    {streamingContext.streamingSessions
                      .filter(session => session.status === 'active')
                      .map(session => (
                        <div 
                          key={session.id} 
                          className="flex items-center justify-between bg-gray-700 rounded p-2"
                        >
                          <span className="text-sm">Server {session.id.substring(0, 8)}</span>
                          <button
                            onClick={() => connectToServer(session.id)}
                            className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 rounded"
                          >
                            Connect
                          </button>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="text-gray-400 text-sm">No active servers found</div>
                )}
              </div>
            </div>
            
            <div className="bg-gray-700 p-3 rounded-lg text-sm">
              <p>
                <strong>Server Mode:</strong> Start a dedicated streaming server that keeps cameras running even when you navigate away.
              </p>
              <p className="mt-2">
                <strong>Client Mode:</strong> Connect to a running server to view and control cameras remotely.
              </p>
            </div>
          </div>
        )}
      </div>
    );
  };
  
  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      <h1 className="text-2xl font-bold mb-4">Camera System</h1>
      
      {/* Server/Client Mode Controls */}
      {streamingContext && renderServerControls()}
      
      <div className="flex flex-wrap gap-4 mb-6">
        <select
          value={pendingCameraCount}
          onChange={(e) => {
            setPendingCameraCount(parseInt(e.target.value));
            setSaveNeeded(true);
            
            // For client mode, send command to server
            if (mode === 'client' && streamingContext?.clientConnected) {
              const count = parseInt(e.target.value);
              const sendCommand = async () => {
                try {
                  const db = getFirestore();
                  await setDoc(doc(collection(db, 'cameraCommands')), {
                    action: 'setCameraCount',
                    count: count,
                    targetServer: streamingContext.serverInfo?.id,
                    createdAt: serverTimestamp(),
                    createdBy: currentUser?.uid || 'anonymous',
                    processed: false
                  });
                  
                  console.log(`Sent setCameraCount command: ${count}`);
                } catch (error) {
                  console.error('Error sending setCameraCount command:', error);
                }
              };
              
              sendCommand();
            }
          }}
          className="bg-gray-800 border rounded px-3 py-2"
          disabled={mode === 'client'} // Disable in client mode, we just send commands
        >
          {cameraOptions.map(count => (
            <option key={count} value={count}>{count}</option>
          ))}
        </select>
        
        {saveNeeded && mode !== 'client' && (
          <button
            onClick={applyCameraCountChange}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
          >
            Save
          </button>
        )}
        
        <button
          onClick={systemKilled ? restoreCameras : killAll}
          className={`${systemKilled ? 'bg-green-600' : 'bg-red-600'} hover:bg-opacity-80 text-white px-6 py-2 rounded font-bold`}
        >
          {systemKilled ? 'TURN BACK ON' : 'KILL ALL'}
        </button>
      </div>
      
      {/* Status indicators */}
      <div className="flex flex-wrap items-center gap-4 mb-4">
        <div className="flex items-center bg-gray-800 rounded-lg px-4 py-2">
          <span className="mr-2 text-gray-300">System Status:</span>
          <div className={`h-3 w-3 rounded-full ${systemKilled ? 'bg-red-500' : 'bg-green-500'}`}></div>
          <span className="ml-2 text-sm font-medium">
            {systemKilled ? 'Inactive' : 'Active'}
          </span>
        </div>
        
        <div className="flex items-center bg-gray-800 rounded-lg px-4 py-2">
          <span className="mr-2 text-gray-300">Active Cameras:</span>
          <span className={`text-sm font-medium ${countActiveCameras() > 0 ? 'text-green-400' : 'text-red-400'}`}>
            {countActiveCameras()}/{cameraCount}
          </span>
        </div>
        
        <div className="flex items-center bg-gray-800 rounded-lg px-4 py-2">
          <span className="mr-2 text-gray-300">Available Camera Devices:</span>
          <span className="text-sm font-medium text-blue-400">
            {mode === 'client' ? 'N/A' : availableCameras.length}
          </span>
        </div>
        
        <div className="flex items-center bg-gray-800 rounded-lg px-4 py-2">
          <span className="mr-2 text-gray-300">Mode:</span>
          <span className={`text-sm font-medium ${
            mode === 'server' ? 'text-green-400' : 
            mode === 'client' ? 'text-blue-400' : 'text-gray-400'
          }`}>
            {mode.charAt(0).toUpperCase() + mode.slice(1)}
          </span>
        </div>
      </div>
      
      {/* Network Status Section */}
      <div className="flex flex-wrap items-center gap-4 mb-6 p-3 bg-gray-800 rounded-lg">
        <div className="flex flex-col w-full">
          <h3 className="text-sm font-semibold text-gray-400 mb-2">Network Dashboard</h3>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center">
              <span className="mr-2 text-gray-300">Status:</span>
              <div className={`h-3 w-3 rounded-full ${getNetworkStatusColor()}`}></div>
              <span className="ml-2 text-sm font-medium">
                {networkStatus.charAt(0).toUpperCase() + networkStatus.slice(1)}
              </span>
            </div>
            
            <div className="flex items-center">
              <span className="mr-2 text-gray-300">Speed:</span>
              <span className="text-sm font-medium text-blue-400">
                {networkSpeed.toFixed(1)} Mbps
              </span>
            </div>
            
            <div className="flex items-center">
              <span className="mr-2 text-gray-300">Latency:</span>
              <span className={`text-sm font-medium ${getLatencyColor()}`}>
                {Math.round(networkLatency)}ms
              </span>
            </div>
            
            <div className="flex items-center">
              <span className="mr-2 text-gray-300">Bandwidth:</span>
              <span className="text-sm font-medium text-yellow-400">
                {bandwidthUsage.toFixed(1)} MB/s
              </span>
            </div>
            
            <div className="flex items-center">
              <span className="mr-2 text-gray-300">Signal:</span>
              {renderSignalBars()}
            </div>
            
            <div className="flex items-center">
              <span className="mr-2 text-gray-300">Packet Loss:</span>
              <span className={`text-sm font-medium ${getPacketLossColor()}`}>
                {packetLoss.toFixed(1)}%
              </span>
            </div>
            
            <div className="flex items-center">
              <span className="mr-2 text-gray-300">Data Transferred:</span>
              <span className="text-sm font-medium text-blue-400">
                {dataTransferred} MB
              </span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Camera Grid */}
      <div className={`grid ${getGridClass()} gap-4 mb-6`}>
        {[...Array(cameraCount)].map((_, index) => (
          <CameraView key={`camera-${index}-${cameraCount}`} index={index} />
        ))}
      </div>
      
      {/* Information box */}
      <div className="bg-gray-800 rounded-lg p-4 mt-6">
        <h2 className="text-xl font-semibold mb-4">Camera System Information</h2>
        <div className="text-gray-300 space-y-2">
          <p>
            This monitoring system allows you to view and manage multiple camera feeds.
          </p>
          <p>
            <span className="text-blue-400 font-medium">24/7 Operation:</span> Run in server mode on a dedicated computer connected to your cameras, then access from any device.
          </p>
          <p>
            <span className="text-blue-400 font-medium">Demo Camera:</span> Shows a demo video for testing purposes.
          </p>
          <p>
            <span className="text-blue-400 font-medium">Real Cameras:</span> If your device has cameras and you've granted permission, they will appear in the dropdown list.
          </p>
          <p>
            <span className="text-blue-400 font-medium">Network Dashboard:</span> Displays network statistics for connected cameras.
          </p>
          <p>
            <span className="text-blue-400 font-medium">System Size:</span> Use the dropdown selector and click 'Save' to update how many cameras your system uses.
          </p>
          <p>
            <span className="text-blue-400 font-medium">Kill All:</span> Instantly turn off all camera feeds.
          </p>
          <p>
            <span className="text-blue-400 font-medium">Turn Back On:</span> Restore all camera feeds to their previous state.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CameraSys;