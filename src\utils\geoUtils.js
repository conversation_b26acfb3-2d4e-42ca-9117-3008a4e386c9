// Distance calculator utility - now returns in MILES
export function calculateDistance(point1, point2) {
  const R = 3958.8; // Earth's radius in MILES
  const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
  const φ2 = point2.lat * Math.PI/180;
  const Δφ = (point2.lat-point1.lat) * Math.PI/180;
  const Δλ = (point2.lng-point1.lng) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  const d = R * c; // in MILES
  return d;
}

// Function to find the closest location
export function findClosestLocation(fromPosition, locationsList) {
  if (!locationsList || locationsList.length === 0) return null;
  
  let closestLocation = null;
  let shortestDistance = Infinity;
  
  for (const location of locationsList) {
    const distance = calculateDistance(fromPosition, location.position);
    if (distance < shortestDistance) {
      shortestDistance = distance;
      closestLocation = location;
    }
  }
  
  return { location: closestLocation, distance: shortestDistance };
}

// Function to optimize route
export function optimizeRoute(startPosition, locationsList) {
  if (!locationsList || locationsList.length === 0) return [];
  
  const unvisitedLocations = [...locationsList];
  const optimizedRoute = [];
  let currentPosition = startPosition;
  
  while (unvisitedLocations.length > 0) {
    const { location: closestLocation, distance } = findClosestLocation(currentPosition, unvisitedLocations);
    
    if (closestLocation) {
      optimizedRoute.push({
        location: closestLocation,
        distanceFromPrevious: distance
      });
      
      const index = unvisitedLocations.findIndex(loc => loc.id === closestLocation.id);
      if (index !== -1) {
        unvisitedLocations.splice(index, 1);
      }
      currentPosition = closestLocation.position;
    }
  }
  
  return optimizedRoute;
}

// Calculate bearing between two points (in degrees)
export const calculateBearing = (start, end) => {
  const startLat = start.lat * Math.PI / 180;
  const startLng = start.lng * Math.PI / 180;
  const endLat = end.lat * Math.PI / 180;
  const endLng = end.lng * Math.PI / 180;

  const y = Math.sin(endLng - startLng) * Math.cos(endLat);
  const x = Math.cos(startLat) * Math.sin(endLat) -
            Math.sin(startLat) * Math.cos(endLat) * Math.cos(endLng - startLng);
  
  const bearing = Math.atan2(y, x) * 180 / Math.PI;
  return (bearing + 360) % 360; // Normalize to 0-360
};

// Get direction text from bearing
export const getDirectionText = (bearing) => {
  const directions = ['North', 'Northeast', 'East', 'Southeast', 'South', 'Southwest', 'West', 'Northwest'];
  const index = Math.round(bearing / 45) % 8;
  return directions[index];
};

// Format distance in human-readable form - MILES VERSION
export const formatDistance = (miles) => {
  if (miles < 0.1) {
    return `${Math.round(miles * 5280)} feet`;
  } else {
    return `${miles.toFixed(1)} miles`;
  }
};

// Format time in human-readable form
export const formatTime = (seconds) => {
  if (seconds < 60) {
    return `${Math.round(seconds)} seconds`;
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)} minutes`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
};

// Function to decode polyline points
// This is needed to parse the Google Directions API response
export const decodePolyline = (encoded) => {
  if (!encoded) return [];
  
  const poly = [];
  let index = 0, lat = 0, lng = 0;

  while (index < encoded.length) {
    let b, shift = 0, result = 0;
    do {
      b = encoded.charCodeAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);
    
    const dlat = ((result & 1) ? ~(result >> 1) : (result >> 1));
    lat += dlat;

    shift = 0;
    result = 0;
    do {
      b = encoded.charCodeAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);
    
    const dlng = ((result & 1) ? ~(result >> 1) : (result >> 1));
    lng += dlng;

    poly.push({
      lat: lat / 1e5,
      lng: lng / 1e5
    });
  }
  
  return poly;
};