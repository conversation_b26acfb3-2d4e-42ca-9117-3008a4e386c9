@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes drawLine {
    to {
        stroke-dashoffset: 0;
    }
}

@keyframes colorChange {

    0%,
    100% {
        fill: red;
    }

    50% {
        fill: #6b7280;
    }
}

.n-letter,
.w-letter,
.r-letter {
    opacity: 0;
    animation:
        colorChange 3s infinite,
        fadeIn 1s ease-in-out forwards;
}

.n-letter {
    animation-delay: 0s;
}

.w-letter {
    animation-delay: 0.2s;
}

.r-letter {
    animation-delay: 0.4s;
}

.underline-animation {
    stroke-dasharray: 200;
    stroke-dashoffset: 200;
    animation: drawLine 1s ease-in-out forwards;
    animation-delay: 1s;
}

.subtitle-animation {
    opacity: 0;
    animation: fadeIn 1s ease-in-out forwards;
    animation-delay: 1.5s;
}