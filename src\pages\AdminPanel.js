import React, { useState, useEffect, useRef } from 'react';
import { collection, getDocs, doc, setDoc, addDoc, deleteDoc, query, where, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '../pages/firebase.js';
import { useAuth } from '../contexts/AuthContext.js';
import { useNavigate } from 'react-router-dom';

function AdminPanel() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newUser, setNewUser] = useState({ email: '', password: '', role: 'user' });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [availableTags, setAvailableTags] = useState([]);
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [isTagDropdownOpen, setIsTagDropdownOpen] = useState(false);
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [resetPasswordUser, setResetPasswordUser] = useState(null);
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);
  const [userToResetPassword, setUserToResetPassword] = useState(null);
  const { isAdmin, currentUser, hashPassword } = useAuth();
  const navigate = useNavigate();
  const dropdownRef = useRef(null);

  // Generate a random password
  const generateRandomPassword = (length = 12) => {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const special = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = uppercase + lowercase + numbers + special;
    let password = '';
    
    // Ensure at least one character from each category
    password += uppercase.charAt(Math.floor(Math.random() * uppercase.length));
    password += lowercase.charAt(Math.floor(Math.random() * lowercase.length));
    password += numbers.charAt(Math.floor(Math.random() * numbers.length));
    password += special.charAt(Math.floor(Math.random() * special.length));
    
    // Fill up the rest of the password
    for (let i = 4; i < length; i++) {
      password += allChars.charAt(Math.floor(Math.random() * allChars.length));
    }
    
    // Shuffle the password
    password = password.split('').sort(() => 0.5 - Math.random()).join('');
    
    return password;
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsTagDropdownOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Redirect if not admin
  useEffect(() => {
    if (isAdmin === false) {
      navigate('/dashboard');
    }
  }, [isAdmin, navigate]);

  // Fetch users and system tags
  useEffect(() => {
    async function fetchUsersAndTags() {
      try {
        setLoading(true);
        
        // Fetch users from Firestore collection
        const usersCollection = collection(db, 'users');
        const userSnapshot = await getDocs(usersCollection);
        let usersList = userSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          tags: [] // Initialize tags array
        }));
        
        // Fetch user profiles to get the tags for each user
        for (let user of usersList) {
          const profileRef = doc(db, "userProfiles", user.id);
          const profileDoc = await getDoc(profileRef);
          
          if (profileDoc.exists() && profileDoc.data().tags) {
            user.tags = profileDoc.data().tags;
          }
        }
        
        setUsers(usersList);
        
        // Fetch available system tags
        const tagsRef = doc(db, "settings", "systemTags");
        const tagsDoc = await getDoc(tagsRef);
        
        if (tagsDoc.exists() && tagsDoc.data().tags) {
          setAvailableTags(tagsDoc.data().tags);
        } else {
          // Create default tags if none exist
          const defaultTags = [
            { name: "Admin", color: "#4F46E5" },
            { name: "User", color: "#10B981" },
            { name: "Tow", color: "#F59E0B" }
          ];
          
          await setDoc(doc(db, "settings", "systemTags"), {
            tags: defaultTags,
            updatedAt: new Date().toISOString()
          });
          
          setAvailableTags(defaultTags);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load users and tags.");
      } finally {
        setLoading(false);
      }
    }

    fetchUsersAndTags();
  }, []);

  // Function to determine text color based on background color
  const getTextColor = (hexColor) => {
    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    
    // Calculate brightness (YIQ formula)
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    
    return yiq >= 150 ? '#000000' : '#FFFFFF';
  };

  // Create new user - modified to store in Firestore only
  async function handleCreateUser(e) {
    e.preventDefault();
    setError('');
    setSuccess('');
    
    try {
      // Check if email already exists
      const usersRef = collection(db, "users");
      const q = query(usersRef, where("email", "==", newUser.email));
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        setError("User with this email already exists");
        return;
      }
      
      // Hash the password
      const passwordHash = hashPassword(newUser.password);
      
      // Create a new user document with custom ID
      const newUserId = Date.now().toString(); // Simple ID generation
      
      // Add user to Firestore with hashed password
      await setDoc(doc(db, "users", newUserId), {
        email: newUser.email,
        passwordHash: passwordHash, // Store hashed password
        role: newUser.role,
        createdAt: new Date().toISOString()
      });
      
      // Initialize empty userProfile document
      await setDoc(doc(db, "userProfiles", newUserId), {
        displayName: '',
        jobTitle: '',
        vehicle: '',
        location: '',
        notes: '',
        phoneNumber: '',
        todo: [],
        completedTasks: [],
        tags: [],
        createdAt: new Date().toISOString()
      });
      
      // Update local state
      setUsers([...users, {
        id: newUserId,
        email: newUser.email,
        role: newUser.role,
        createdAt: new Date().toISOString(),
        tags: []
      }]);
      
      setNewUser({ email: '', password: '', role: 'user' });
      setSuccess('User created successfully!');
    } catch (error) {
      console.error("Error creating user:", error);
      setError(error.message);
    }
  }

  // Change user role
  async function handleRoleChange(userId, newRole) {
    try {
      await setDoc(doc(db, "users", userId), { role: newRole }, { merge: true });
      
      // Also update the role in onlineUsers if the user is online
      const onlineUserRef = doc(db, "onlineUsers", userId);
      const onlineUserDoc = await getDoc(onlineUserRef);
      
      if (onlineUserDoc.exists()) {
        await setDoc(onlineUserRef, { role: newRole }, { merge: true });
      }
      
      // Update local state
      setUsers(users.map(user => 
        user.id === userId ? { ...user, role: newRole } : user
      ));
      
      setSuccess('User role updated successfully!');
    } catch (error) {
      console.error("Error updating role:", error);
      setError(error.message);
    }
  }

  // Show reset password modal
  function showResetPassword(user) {
    const newPassword = generateRandomPassword();
    setGeneratedPassword(newPassword);
    setUserToResetPassword(user);
    setShowResetPasswordModal(true);
  }

  // Reset user password directly in Firestore
  async function handleResetPassword() {
    setError('');
    setSuccess('');
    
    if (!userToResetPassword) return;
    
    try {
      setLoading(true);
      
      // Update user document with new hashed password
      const passwordHash = hashPassword(generatedPassword);
      await updateDoc(doc(db, "users", userToResetPassword.id), {
        passwordHash: passwordHash,
        passwordLastChanged: new Date().toISOString()
      });
      
      setSuccess(`Password reset successfully! New password: ${generatedPassword}`);
      setShowResetPasswordModal(false);
      setLoading(false);
      
      // Optionally copy to clipboard
      navigator.clipboard.writeText(generatedPassword)
        .then(() => console.log('Password copied to clipboard'))
        .catch(err => console.error('Failed to copy password', err));
        
    } catch (error) {
      console.error("Error resetting password:", error);
      setError(error.message);
      setLoading(false);
    }
  }

  // Copy generated password to clipboard
  function copyPasswordToClipboard() {
    navigator.clipboard.writeText(generatedPassword)
      .then(() => {
        setSuccess(`Password copied to clipboard!`);
      })
      .catch(err => {
        console.error('Failed to copy password', err);
        setError("Failed to copy password. Please copy it manually.");
      });
  }

  // Delete user
  async function handleDeleteUser(userId) {
    try {
      // Delete user document from Firestore
      await deleteDoc(doc(db, "users", userId));
      
      // Optionally delete user profile document
      await deleteDoc(doc(db, "userProfiles", userId));
      
      // Also delete from onlineUsers if they're there
      try {
        await deleteDoc(doc(db, "onlineUsers", userId));
      } catch (e) {
        console.log("User was not online or already removed from onlineUsers");
      }
      
      // Update local state
      setUsers(users.filter(user => user.id !== userId));
      
      setSuccess('User deleted successfully!');
    } catch (error) {
      console.error("Error deleting user:", error);
      setError(error.message);
    }
  }

  // Go to user profile
  const handleGoToProfile = (userId) => {
    navigate(`/profile/${userId}`);
  };

  // Toggle tag dropdown
  const handleToggleTagDropdown = (userId) => {
    // If we're already showing the dropdown for this user, close it
    if (selectedUserId === userId && isTagDropdownOpen) {
      setIsTagDropdownOpen(false);
      return;
    }
    
    // Otherwise, open the dropdown for this user
    setSelectedUserId(userId);
    setIsTagDropdownOpen(true);
  };

  // Add tag to user - improved version
  const handleAddTagToUser = async (userId, tag) => {
    try {
      setError(''); // Clear any previous errors
      
      // Find the user in our local state
      const userIndex = users.findIndex(user => user.id === userId);
      
      if (userIndex === -1) {
        setError("User not found");
        return;
      }
      
      // Check if user already has this tag
      const userTags = users[userIndex].tags || [];
      const tagExists = userTags.some(t => t.name === tag.name && t.color === tag.color);
      
      if (tagExists) {
        setError("User already has this tag assigned.");
        return;
      }
      
      // Update the user's tags in the profile document
      const profileRef = doc(db, "userProfiles", userId);
      const profileDoc = await getDoc(profileRef);
      
      let updatedTags = [];
      
      if (profileDoc.exists()) {
        // Update existing profile
        updatedTags = [...(profileDoc.data().tags || []), tag];
        
        // Explicitly update just the tags field
        await updateDoc(profileRef, { 
          tags: updatedTags,
          updatedAt: new Date().toISOString() 
        });
      } else {
        // Create new profile with tag
        updatedTags = [tag];
        await setDoc(profileRef, { 
          tags: updatedTags,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          displayName: '',
          jobTitle: '',
          vehicle: '',
          location: '',
          notes: '',
          phoneNumber: '',
          todo: [],
          completedTasks: []
        });
      }
      
      // Update local state
      const updatedUsers = [...users];
      updatedUsers[userIndex] = { ...updatedUsers[userIndex], tags: updatedTags };
      setUsers(updatedUsers);
      
      setSuccess('Tag added successfully!');
      setIsTagDropdownOpen(false); // Close the dropdown after adding
    } catch (error) {
      console.error("Error adding tag:", error);
      setError(`Error adding tag: ${error.message}`);
    }
  };

  // Remove tag from user
  const handleRemoveTagFromUser = async (userId, tagToRemove) => {
    try {
      setError(''); // Clear any previous errors
      
      const userIndex = users.findIndex(user => user.id === userId);
      
      if (userIndex === -1 || !users[userIndex].tags) {
        setError("User or user's tags not found");
        return;
      }
      
      // Filter out the tag to remove
      const updatedTags = users[userIndex].tags.filter(tag => 
        !(tag.name === tagToRemove.name && tag.color === tagToRemove.color)
      );
      
      // Update in Firestore
      const profileRef = doc(db, "userProfiles", userId);
      await updateDoc(profileRef, { 
        tags: updatedTags,
        updatedAt: new Date().toISOString() 
      });
      
      // Update local state
      const updatedUsers = [...users];
      updatedUsers[userIndex] = { ...updatedUsers[userIndex], tags: updatedTags };
      setUsers(updatedUsers);
      
      setSuccess('Tag removed successfully!');
    } catch (error) {
      console.error("Error removing tag:", error);
      setError(`Error removing tag: ${error.message}`);
    }
  };

  if (!isAdmin) {
    return <div className="w-full h-screen flex justify-center items-center bg-gray-900 text-white">
      <div className="text-xl">You don't have permission to access this page.</div>
    </div>;
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">Admin Panel - User Management</h1>
          <div className="flex gap-2">
            <button
              onClick={() => navigate('/tags')}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md flex items-center shadow-md"
            >
              <span>Manage Tags</span>
            </button>
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-gray-700 hover:bg-gray-600 text-gray-200 font-medium py-2 px-4 rounded-md flex items-center shadow-md"
            >
              <span>← Back to Dashboard</span>
            </button>
          </div>
        </div>
        
        {error && <div className="bg-red-900 border border-red-700 text-red-200 px-4 py-3 rounded-md mb-4 shadow-md">{error}</div>}
        {success && (
          <div className="bg-green-900 border border-green-700 text-green-200 px-4 py-3 rounded-md mb-4 shadow-md">
            <div className="flex justify-between items-center">
              <div>{success}</div>
              {generatedPassword && (
                <button 
                  onClick={copyPasswordToClipboard}
                  className="bg-green-700 hover:bg-green-600 text-white px-2 py-1 rounded-md text-sm"
                >
                  Copy Password
                </button>
              )}
            </div>
          </div>
        )}
        
        {/* Create User Form */}
        <div className="bg-gray-800 p-6 rounded-lg shadow-md mb-8 border border-gray-700">
          <h2 className="text-xl font-semibold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Create New User</h2>
          <form onSubmit={handleCreateUser}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-gray-300 text-sm font-bold mb-2">Email</label>
                <input
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white shadow-inner focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-300 text-sm font-bold mb-2">Password</label>
                <input
                  type="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white shadow-inner focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-300 text-sm font-bold mb-2">Role</label>
                <select
                  value={newUser.role}
                  onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white shadow-inner focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="user">User</option>
                  <option value="admin">Admin</option>
                  <option value="tow">Tow Truck</option>
                </select>
              </div>
            </div>
            <button
              type="submit"
              className="mt-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white font-bold py-2 px-4 rounded shadow-md"
            >
              Create User
            </button>
          </form>
        </div>
        
        {/* Users List */}
        <div className="bg-gray-800 p-6 rounded-lg shadow-md border border-gray-700">
          <h2 className="text-xl font-semibold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Manage Users</h2>
          
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full bg-gray-800 border-collapse">
                <thead>
                  <tr className="bg-gray-900">
                    <th className="py-2 px-4 border-b border-gray-700 text-left text-gray-300">Email</th>
                    <th className="py-2 px-4 border-b border-gray-700 text-left text-gray-300">Role</th>
                    <th className="py-2 px-4 border-b border-gray-700 text-left text-gray-300">Created At</th>
                    <th className="py-2 px-4 border-b border-gray-700 text-left text-gray-300">Tags</th>
                    <th className="py-2 px-4 border-b border-gray-700 text-left text-gray-300">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map(user => (
                    <tr key={user.id} className="hover:bg-gray-700">
                      <td className="py-2 px-4 border-b border-gray-700">
                        <button 
                          onClick={() => handleGoToProfile(user.id)}
                          className="text-blue-400 hover:text-blue-300 focus:outline-none"
                        >
                          {user.email}
                        </button>
                      </td>
                      <td className="py-2 px-4 border-b border-gray-700">
                        <select
                          value={user.role}
                          onChange={(e) => handleRoleChange(user.id, e.target.value)}
                          className="px-2 py-1 bg-gray-700 border border-gray-600 rounded-md text-white shadow-inner focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="user">User</option>
                          <option value="admin">Admin</option>
                          <option value="tow">Tow Truck</option>
                        </select>
                      </td>
                      <td className="py-2 px-4 border-b border-gray-700 text-gray-300">
                        {user.createdAt ? new Date(user.createdAt).toLocaleString() : 'N/A'}
                      </td>
                      <td className="py-2 px-4 border-b border-gray-700">
                        <div className="flex flex-wrap gap-1">
                          {user.tags && user.tags.length > 0 ? (
                            user.tags.map((tag, tagIndex) => (
                              <div
                                key={tagIndex}
                                className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"
                                style={{
                                  backgroundColor: tag.color,
                                  color: getTextColor(tag.color)
                                }}
                              >
                                <span>{tag.name}</span>
                                <button
                                  type="button"
                                  onClick={() => handleRemoveTagFromUser(user.id, tag)}
                                  className="ml-1 focus:outline-none"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>
                              </div>
                            ))
                          ) : (
                            <span className="text-gray-500 text-xs italic">No tags</span>
                          )}
                          
                          {/* Tag Dropdown Toggle */}
                          <div className="relative" ref={dropdownRef}>
                            <button
                              type="button"
                              onClick={() => handleToggleTagDropdown(user.id)}
                              className="inline-flex items-center ml-1 px-2 py-1 border border-gray-600 rounded-md text-xs bg-gray-700 hover:bg-gray-600 shadow-sm"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                              </svg>
                            </button>
                            
                            {/* Tag Selection Dropdown */}
                            {isTagDropdownOpen && selectedUserId === user.id && (
                              <div className="absolute z-10 mt-1 w-48 bg-gray-800 rounded-md shadow-lg border border-gray-700">
                                <div className="max-h-48 overflow-y-auto p-2">
                                  <div className="text-xs font-medium text-gray-400 mb-2 px-2">Add tag</div>
                                  {availableTags && availableTags.length > 0 ? (
                                    availableTags.map((tag, index) => {
                                      // Check if tag is already assigned
                                      const isAssigned = user.tags && user.tags.some(t => 
                                        t.name === tag.name && t.color === tag.color
                                      );
                                      
                                      return (
                                        <button
                                          key={index}
                                          type="button"
                                          onClick={() => {
                                            if (!isAssigned) {
                                              handleAddTagToUser(user.id, tag);
                                            }
                                          }}
                                          className={`w-full text-left px-2 py-2 rounded-md flex items-center ${
                                            isAssigned ? 'opacity-50 cursor-not-allowed bg-gray-700' : 'hover:bg-gray-700'
                                          }`}
                                          disabled={isAssigned}
                                        >
                                          <span 
                                            className="w-3 h-3 rounded-full mr-2"
                                            style={{ backgroundColor: tag.color }}
                                          ></span>
                                          <span className="text-sm text-gray-300">{tag.name}</span>
                                          {isAssigned && (
                                            <span className="ml-auto text-xs text-gray-500">Added</span>
                                          )}
                                        </button>
                                      );
                                    })
                                  ) : (
                                    <div className="px-2 py-2 text-xs text-gray-500">
                                      No tags available
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="py-2 px-4 border-b border-gray-700">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleGoToProfile(user.id)}
                            className="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm shadow-md"
                          >
                            Profile
                          </button>
                          <button
                            onClick={() => showResetPassword(user)}
                            className="bg-yellow-600 hover:bg-yellow-700 text-white py-1 px-3 rounded text-sm shadow-md"
                          >
                            Reset PW
                          </button>
                          <button
                            onClick={() => handleDeleteUser(user.id)}
                            className="bg-red-600 hover:bg-red-700 text-white py-1 px-3 rounded text-sm shadow-md"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
      
      {/* Reset Password Modal */}
      {showResetPasswordModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg shadow-xl max-w-md w-full border border-gray-700">
            <h2 className="text-xl font-bold mb-4">Reset Password</h2>
            <p className="mb-4 text-gray-300">
              Reset password for user: <span className="font-semibold text-blue-400">{userToResetPassword?.email}</span>
            </p>
            
            <div className="mb-4">
              <p className="text-sm text-gray-400 mb-2">Generated Password:</p>
              <div className="flex">
                <input 
                  type="text" 
                  value={generatedPassword} 
                  readOnly 
                  className="flex-grow bg-gray-700 border border-gray-600 rounded-l-md p-2 text-white"
                />
                <button
                  onClick={copyPasswordToClipboard}
                  className="bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded-r-md"
                  title="Copy to clipboard"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                  </svg>
                </button>
              </div>
              <p className="mt-2 text-sm text-green-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                This password will be updated directly in the database. Make sure to share it securely with the user.
              </p>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button 
                onClick={() => setShowResetPasswordModal(false)}
                className="bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded shadow-md"
              >
                Cancel
              </button>
              <button 
                onClick={handleResetPassword}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded shadow-md"
                disabled={loading}
              >
                {loading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                ) : "Reset Password"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default AdminPanel;