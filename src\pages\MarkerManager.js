import L from 'leaflet';

/**
 * Safe layer removal helper function
 * @param {L.Map} map - The map instance
 * @param {<PERSON><PERSON>Layer} layer - The layer to remove
 */
export const safeRemoveLayer = (map, layer) => {
  if (!map || !layer) return;

  try {
    if (map.hasLayer(layer)) {
      map.removeLayer(layer);
    }
  } catch (error) {
    console.warn("Error safely removing layer:", error);
  }
};

/**
 * Safe layer removal from cluster helper function
 * @param {L.MarkerClusterGroup} cluster - The marker cluster
 * @param {L.Layer} layer - The layer to remove
 */
export const safeRemoveFromCluster = (cluster, layer) => {
  if (!cluster || !layer) return;

  try {
    if (cluster.hasLayer(layer)) {
      cluster.removeLayer(layer);
    }
  } catch (error) {
    console.warn("Error safely removing layer from cluster:", error);
  }
};

/**
 * Function to get vehicle icon based on make/model/type
 * @param {Object} vehicle - The vehicle object with make, model, etc.
 * @returns {string} - SVG path for the vehicle icon
 */
export const getVehicleIcon = (vehicle) => {
  // Default icon for unknown vehicles
  let iconPath = "M9,11H11V9H9M9,15H11V13H9M9,7H11V5H9M9,3H11V1H9M13,5H15V3H13M17,11V5H15V7H13V9H15V11M17,15H19V13H17V11H15V13H13V15H15V17H17M1,17H5V15H3V13H1V17M5,11V9H3V11M1,9H3V7H5V5H1V9M5,3V1H1V5H5Z";

  // Check if we have vehicle make information
  if (vehicle && vehicle.make) {
    const make = vehicle.make.toLowerCase();

    // Sedan body style
    if (vehicle.bodyStyle === 'sedan' || make.includes('honda') || make.includes('toyota') || make.includes('nissan')) {
      iconPath = "M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z";
    }
    // SUV or truck
    else if (vehicle.bodyStyle === 'suv' || make.includes('jeep') || make.includes('ford') || make.includes('chevrolet')) {
      iconPath = "M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z";
    }
    // Truck or pickup
    else if (vehicle.bodyStyle === 'truck' || make.includes('ram') || make.includes('silverado') || make.includes('f-150')) {
      iconPath = "M18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5M19.5,9.5L21.46,12H17V9.5M6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5M20,8H17V4H3C1.89,4 1,4.89 1,6V17H3A3,3 0 0,0 6,20A3,3 0 0,0 9,17H15A3,3 0 0,0 18,20A3,3 0 0,0 21,17H23V12L20,8Z";
    }
    // Luxury sedan or sports car
    else if (make.includes('mercedes') || make.includes('bmw') || make.includes('lexus') || make.includes('audi')) {
      iconPath = "M16,6H6L1,12V15H3A3,3 0 0,0 6,18A3,3 0 0,0 9,15H15A3,3 0 0,0 18,18A3,3 0 0,0 21,15H23V12C23,10.89 22.11,10 21,10H19L16,6M6.5,7.5H10.5V10H4.5L6.5,7.5M12,7.5H15.5L17.46,10H12V7.5M6,13.5A1.5,1.5 0 0,1 7.5,15A1.5,1.5 0 0,1 6,16.5A1.5,1.5 0 0,1 4.5,15A1.5,1.5 0 0,1 6,13.5M18,13.5A1.5,1.5 0 0,1 19.5,15A1.5,1.5 0 0,1 18,16.5A1.5,1.5 0 0,1 16.5,15A1.5,1.5 0 0,1 18,13.5Z";
    }
  }

  return iconPath;
};

/**
 * Helper function to get status style information for markers
 * @param {string} status - The status value
 * @returns {Object} - Style object with colors and icons
 */
export const getStatusStyle = (status) => {
  // First map the status from Orders component to display values
  let displayStatus = status || 'unknown';

  // Special handling for 'pending' coming from Orders
  if (displayStatus === 'pending') {
    displayStatus = 'open-order';  // Treat 'pending' as 'open-order' for display
  }

  // Now determine display style based on mapped status
  switch (displayStatus.toLowerCase()) {
    case 'picked-up':
      return {
        label: 'Picked Up',
        bg: '#10B981', // green-600 
        text: '#FFFFFF',
        pulseColor: 'rgba(16, 185, 129, 0.6)',
        iconPath: "M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2,4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"
      };
    case 'pending-pickup':
      return {
        label: 'Pending Pickup',
        bg: '#D97706', // yellow-600
        text: '#FFFFFF',
        pulseColor: 'rgba(217, 119, 6, 0.6)',
        iconPath: "M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z"
      };
    case 'awaiting-pickup':
      return {
        label: 'Awaiting Pickup',
        bg: '#EA580C', // orange-600
        text: '#FFFFFF',
        pulseColor: 'rgba(234, 88, 12, 0.6)',
        iconPath: "M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"
      };
    case 'open-order':
    case 'open':
      return {
        label: 'Open Order',
        bg: '#2563EB', // blue-600
        text: '#FFFFFF',
        pulseColor: 'rgba(37, 99, 235, 0.6)',
        iconPath: "M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"
      };
    case 'completed':
      return {
        label: 'Completed',
        bg: '#4B5563', // gray-600
        text: '#FFFFFF',
        pulseColor: 'rgba(75, 85, 99, 0.6)',
        iconPath: "M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"
      };
    default:
      return {
        label: status || 'Unknown',
        bg: '#4B5563', // gray-600
        text: '#FFFFFF',
        pulseColor: 'rgba(75, 85, 99, 0.6)',
        iconPath: "M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"
      };
  }
};

/**
 * Process multiple addresses for orders
 * @param {Object} order - The order object
 * @returns {Array} - Array of processed order objects for each address
 */
export const processOrderAddresses = (order) => {
  if (!order || !order.addresses || !order.position) return [order];

  // Create a marker for each address
  return order.addresses.map((address, index) => {
    // Create a modified order object for this specific address
    const positionOverride = address.position || order.position;

    return {
      ...order,
      id: `${order.id}-address-${index}`,
      name: `${order.name || ''} - Address ${index + 1}`,
      address: address,
      formattedAddress: address.street ?
        `${address.street}, ${address.city || ''} ${address.state || ''} ${address.zip || ''}` :
        'Address details unavailable',
      position: positionOverride,
      sourceOrder: order.id, // Reference to parent order
      addressIndex: index
    };
  });
};

/**
 * FIXED: Update user location on map with better error handling and safer map operations
 * This version fixes the "_leaflet_pos" error
 * @param {Object} location - The location object with lat and lng props
 * @param {Object} options - Options including map and marker references
 * @returns {boolean} - True if location updated successfully, false otherwise
 */
export const updateUserLocation = (location, options) => {
  const {
    zoomedOutMap,
    zoomedInMap,
    zoomedOutMarkerCluster,
    zoomedInMarkerCluster,
    currentLocationMarkerRef,
    currentMarkerRef,
    destinationRef,
    isRotationEnabled,
    updateMapRotation,
    setRoutingWaypoints,
    updateInProgressRef,
    lastKnownPositionRef,
    ZOOMED_OUT_LEVEL,
    ZOOMED_IN_LEVEL
  } = options;

  if (updateInProgressRef.current) return false;
  updateInProgressRef.current = true;

  if (!location || !location.lat || !location.lng) {
    console.error("Invalid location for updateUserLocation:", location);
    updateInProgressRef.current = false;
    return false;
  }

  console.log("Updating user location on map:", location);

  try {
    // If maps aren't initialized yet, just store the location
    if (!zoomedOutMap || !zoomedInMap) {
      if (lastKnownPositionRef) {
        lastKnownPositionRef.current = location;
      }
      updateInProgressRef.current = false;
      return false;
    }

    // FIXED: Safer map centering with comprehensive validation and error handling
    const safeSetView = (map, level, name) => {
      if (!map) {
        console.warn(`${name} map is not available`);
        return false;
      }
      
      try {
        // First, verify the map is properly initialized
        if (!map._container || !map._leaflet_id || typeof map.setView !== 'function') {
          console.warn(`${name} map is not fully initialized`);
          return false;
        }
        
        // Check if container is visible and has dimensions
        if (map._container.offsetWidth === 0 || map._container.offsetHeight === 0) {
          console.warn(`${name} map container has zero dimensions`);
          return false;
        }
        
        // Use safe approach with explicit pan options to avoid _leaflet_pos errors
        setTimeout(() => {
          try {
            map.setView([location.lat, location.lng], level, {
              animate: false,
              pan: {
                animate: false,
                duration: 0,
                easeLinearity: 1,
                noMoveStart: true
              }
            });
            return true;
          } catch (innerError) {
            console.warn(`Failed to set view on ${name} map:`, innerError);
            
            // Extra fallback - try direct panning instead
            try {
              map.panTo([location.lat, location.lng], {
                animate: false,
                duration: 0,
                easeLinearity: 1,
                noMoveStart: true
              });
            } catch (panError) {
              console.warn(`Failed to pan ${name} map:`, panError);
            }
            return false;
          }
        }, 0);
        
        return true;
      } catch (error) {
        console.warn(`Error centering ${name} map:`, error);
        return false;
      }
    };

    // Try to center both maps with the safer approach
    safeSetView(zoomedOutMap, ZOOMED_OUT_LEVEL, "zoomed out");
    safeSetView(zoomedInMap, ZOOMED_IN_LEVEL, "zoomed in");

    // Remove existing user markers safely
    if (currentLocationMarkerRef.current) {
      try {
        if (currentLocationMarkerRef.current.zoomed_out) {
          if (zoomedOutMarkerCluster) {
            safeRemoveFromCluster(zoomedOutMarkerCluster, currentLocationMarkerRef.current.zoomed_out);
          } else if (zoomedOutMap) {
            safeRemoveLayer(zoomedOutMap, currentLocationMarkerRef.current.zoomed_out);
          }
        }
      } catch (error) {
        console.warn("Error removing zoomed out user marker:", error);
      }

      try {
        if (currentLocationMarkerRef.current.zoomed_in) {
          if (zoomedInMarkerCluster) {
            safeRemoveFromCluster(zoomedInMarkerCluster, currentLocationMarkerRef.current.zoomed_in);
          } else if (zoomedInMap) {
            safeRemoveLayer(zoomedInMap, currentLocationMarkerRef.current.zoomed_in);
          }
        }
      } catch (error) {
        console.warn("Error removing zoomed in user marker:", error);
      }
    }

    // Create new user markers with enhanced visibility
    const userIcon = L.divIcon({
      className: 'current-location-marker',
      html: `<div class="current-marker-inner"></div>`,
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });

    // FIXED: Create markers only if maps and clusters are available
    let zoomedOutAdded = false;
    let zoomedInAdded = false;
    let userMarkerZoomedOut = null;
    let userMarkerZoomedIn = null;
    
    // Create the markers only if maps are ready
    if (zoomedOutMap && zoomedOutMap._leaflet_id) {
      userMarkerZoomedOut = L.marker([location.lat, location.lng], {
        icon: userIcon,
        zIndexOffset: 1000 // Higher zIndex to appear above other markers
      });
    }
    
    if (zoomedInMap && zoomedInMap._leaflet_id) {
      userMarkerZoomedIn = L.marker([location.lat, location.lng], {
        icon: userIcon,
        zIndexOffset: 1000
      });
    }

    // Add markers to zoomed out map with comprehensive error handling
    if (userMarkerZoomedOut) {
      try {
        if (zoomedOutMarkerCluster && typeof zoomedOutMarkerCluster.addLayer === 'function') {
          try {
            zoomedOutMarkerCluster.addLayer(userMarkerZoomedOut);
            zoomedOutAdded = true;
          } catch (e) {
            console.warn("Error adding to zoomed out cluster:", e);
            // Fallback to adding directly to map
            if (zoomedOutMap && typeof zoomedOutMap.addLayer === 'function') {
              try {
                zoomedOutMap.addLayer(userMarkerZoomedOut);
                zoomedOutAdded = true;
              } catch (mapError) {
                console.warn("Error adding to zoomed out map:", mapError);
              }
            }
          }
        } else if (zoomedOutMap && typeof zoomedOutMap.addLayer === 'function') {
          try {
            zoomedOutMap.addLayer(userMarkerZoomedOut);
            zoomedOutAdded = true;
          } catch (mapError) {
            console.warn("Error adding to zoomed out map:", mapError);
          }
        }
      } catch (outError) {
        console.warn("Error handling zoomed out user marker:", outError);
      }
    }

    // Add markers to zoomed in map with comprehensive error handling
    if (userMarkerZoomedIn) {
      try {
        if (zoomedInMarkerCluster && typeof zoomedInMarkerCluster.addLayer === 'function') {
          try {
            zoomedInMarkerCluster.addLayer(userMarkerZoomedIn);
            zoomedInAdded = true;
          } catch (e) {
            console.warn("Error adding to zoomed in cluster:", e);
            // Fallback to adding directly to map
            if (zoomedInMap && typeof zoomedInMap.addLayer === 'function') {
              try {
                zoomedInMap.addLayer(userMarkerZoomedIn);
                zoomedInAdded = true;
              } catch (mapError) {
                console.warn("Error adding to zoomed in map:", mapError);
              }
            }
          }
        } else if (zoomedInMap && typeof zoomedInMap.addLayer === 'function') {
          try {
            zoomedInMap.addLayer(userMarkerZoomedIn);
            zoomedInAdded = true;
          } catch (mapError) {
            console.warn("Error adding to zoomed in map:", mapError);
          }
        }
      } catch (inError) {
        console.warn("Error handling zoomed in user marker:", inError);
      }
    }

    // Store references only if successful
    if (zoomedOutAdded || zoomedInAdded) {
      currentLocationMarkerRef.current = {
        zoomed_out: zoomedOutAdded ? userMarkerZoomedOut : null,
        zoomed_in: zoomedInAdded ? userMarkerZoomedIn : null
      };

      // Also update the main current marker ref for compatibility
      if (currentMarkerRef && zoomedOutAdded) {
        currentMarkerRef.current = userMarkerZoomedOut;
      }
    }

    // Update rotation if we have a destination
    if (destinationRef && destinationRef.current && isRotationEnabled && typeof updateMapRotation === 'function') {
      try {
        updateMapRotation();
      } catch (error) {
        console.warn("Error updating map rotation:", error);
      }
    }

    // Update route if we have a destination - use map separation approach
    if (destinationRef && destinationRef.current && typeof setRoutingWaypoints === 'function') {
      // Wait a moment before updating the route to avoid timing issues
      setTimeout(() => {
        // Call setRoutingWaypoints with current location and destination
        try {
          setRoutingWaypoints(location, destinationRef.current);
        } catch (error) {
          console.warn("Error updating routes after user location change:", error);
        }
      }, 150);
    }

    updateInProgressRef.current = false;
    return (zoomedOutAdded || zoomedInAdded);
  } catch (error) {
    console.error("Error updating user location on map:", error);
    updateInProgressRef.current = false;
    return false;
  }
};

/**
 * Set destination with better error handling and map separation 
 * @param {number} lat - Latitude
 * @param {number} lng - Longitude
 * @param {Object} options - Options including map and marker references
 * @returns {boolean} - True if destination set successfully
 */
export const setDestination = (lat, lng, options) => {
  const {
    zoomedOutMap,
    zoomedInMap,
    zoomedOutMarkerCluster,
    zoomedInMarkerCluster,
    destinationMarkerRef,
    currentLocation,
    destinationRef,
    isRotationEnabled,
    updateMapRotation,
    setRoutingWaypoints,
    updateInProgressRef
  } = options;

  if (updateInProgressRef.current) return false;
  updateInProgressRef.current = true;

  console.log(`Setting destination to: ${lat}, ${lng}`);

  try {
    // Store destination in ref for later use
    destinationRef.current = { lat, lng };

    // Set routing waypoints using our safer approach
    if (currentLocation && currentLocation.lat && currentLocation.lng) {
      setRoutingWaypoints(currentLocation, { lat, lng });
    }

    // Add destination marker to maps
    if (zoomedOutMap && zoomedInMap) {
      // Remove any existing destination markers safely
      if (destinationMarkerRef.current) {
        if (destinationMarkerRef.current.zoomed_out) {
          if (zoomedOutMarkerCluster) {
            safeRemoveFromCluster(zoomedOutMarkerCluster, destinationMarkerRef.current.zoomed_out);
          } else {
            safeRemoveLayer(zoomedOutMap, destinationMarkerRef.current.zoomed_out);
          }
        }

        if (destinationMarkerRef.current.zoomed_in) {
          if (zoomedInMarkerCluster) {
            safeRemoveFromCluster(zoomedInMarkerCluster, destinationMarkerRef.current.zoomed_in);
          } else {
            safeRemoveLayer(zoomedInMap, destinationMarkerRef.current.zoomed_in);
          }
        }
      }

      // Create icon for destination marker
      const destinationIcon = L.divIcon({
        className: 'destination-marker',
        html: '<div class="destination-marker-inner"></div>',
        iconSize: [32, 32],
        iconAnchor: [16, 16]
      });

      // Create new markers
      const destinationMarker = L.marker([lat, lng], { icon: destinationIcon });
      const destinationMarkerClone = L.marker([lat, lng], { icon: destinationIcon });

      // Add popup with helpful information
      const popupContent = `
        <div class="text-center">
          <strong>Navigation Destination</strong><br>
          <span class="text-xs">${lat.toFixed(6)}, ${lng.toFixed(6)}</span><br>
          <button id="center-maps-btn" class="bg-blue-600 text-white px-2 py-1 rounded text-xs mt-1">
            Center Maps Here
          </button>
        </div>
      `;

      destinationMarker.bindPopup(popupContent);
      destinationMarkerClone.bindPopup(popupContent);

      // Add event listener for centering maps
      destinationMarker.on('popupopen', () => {
        setTimeout(() => {
          const centerBtn = document.getElementById('center-maps-btn');
          if (centerBtn) {
            centerBtn.addEventListener('click', () => {
              try {
                // Safely center maps
                if (zoomedOutMap && typeof zoomedOutMap.setView === 'function') {
                  zoomedOutMap.setView([lat, lng], 15, { animate: true });
                }
                if (zoomedInMap && typeof zoomedInMap.setView === 'function') {
                  zoomedInMap.setView([lat, lng], 19, { animate: true });
                }
              } catch (error) {
                console.warn("Error centering maps from popup:", error);
              }
            });
          }
        }, 10);
      });

      // Add markers to maps - with error handling for each map
      let zoomedOutAdded = false;
      let zoomedInAdded = false;

      try {
        if (zoomedOutMarkerCluster && typeof zoomedOutMarkerCluster.addLayer === 'function') {
          try {
            zoomedOutMarkerCluster.addLayer(destinationMarkerClone);
            zoomedOutAdded = true;
          } catch (e) {
            console.warn("Error adding to zoomed out cluster:", e);
            // Fallback to adding directly to map
            if (zoomedOutMap && typeof zoomedOutMap.addLayer === 'function') {
              try {
                zoomedOutMap.addLayer(destinationMarkerClone);
                zoomedOutAdded = true;
              } catch (mapError) {
                console.warn("Error adding to zoomed out map:", mapError);
              }
            }
          }
        } else if (zoomedOutMap && typeof zoomedOutMap.addLayer === 'function') {
          try {
            zoomedOutMap.addLayer(destinationMarkerClone);
            zoomedOutAdded = true;
          } catch (mapError) {
            console.warn("Error adding to zoomed out map:", mapError);
          }
        }
      } catch (outError) {
        console.warn("Error handling zoomed out marker:", outError);
      }

      try {
        if (zoomedInMarkerCluster && typeof zoomedInMarkerCluster.addLayer === 'function') {
          try {
            zoomedInMarkerCluster.addLayer(destinationMarker);
            zoomedInAdded = true;
          } catch (e) {
            console.warn("Error adding to zoomed in cluster:", e);
            // Fallback to adding directly to map
            if (zoomedInMap && typeof zoomedInMap.addLayer === 'function') {
              try {
                zoomedInMap.addLayer(destinationMarker);
                zoomedInAdded = true;
              } catch (mapError) {
                console.warn("Error adding to zoomed in map:", mapError);
              }
            }
          }
        } else if (zoomedInMap && typeof zoomedInMap.addLayer === 'function') {
          try {
            zoomedInMap.addLayer(destinationMarker);
            zoomedInAdded = true;
          } catch (mapError) {
            console.warn("Error adding to zoomed in map:", mapError);
          }
        }
      } catch (inError) {
        console.warn("Error handling zoomed in marker:", inError);
      }

      // Only store references if successful
      if (zoomedOutAdded && zoomedInAdded) {
        destinationMarkerRef.current = {
          zoomed_out: destinationMarkerClone,
          zoomed_in: destinationMarker
        };
      }

      // Update rotation
      if (isRotationEnabled && typeof updateMapRotation === 'function') {
        try {
          updateMapRotation();
        } catch (error) {
          console.warn("Error updating map rotation:", error);
        }
      }

      // Safely center both maps on the destination
      try {
        if (zoomedOutMap && typeof zoomedOutMap.setView === 'function') {
          setTimeout(() => {
            try {
              zoomedOutMap.setView([lat, lng], 15, { 
                animate: true,
                pan: {
                  animate: true,
                  duration: 1,
                  easeLinearity: 0.25
                }
              });
            } catch (error) {
              console.warn("Error in delayed centering of zoomed out map:", error);
            }
          }, 50);
        }
      } catch (error) {
        console.warn("Error scheduling centering of zoomed out map:", error);
      }

      try {
        if (zoomedInMap && typeof zoomedInMap.setView === 'function') {
          setTimeout(() => {
            try {
              zoomedInMap.setView([lat, lng], 19, { 
                animate: true,
                pan: {
                  animate: true,
                  duration: 1,
                  easeLinearity: 0.25
                }
              });
            } catch (error) {
              console.warn("Error in delayed centering of zoomed in map:", error);
            }
          }, 100);
        }
      } catch (error) {
        console.warn("Error scheduling centering of zoomed in map:", error);
      }
    }

    updateInProgressRef.current = false;
    return true;
  } catch (error) {
    console.error("Error setting destination:", error);
    updateInProgressRef.current = false;
    return false;
  }
};

/**
 * Add status markers for open orders and pending pickups
 * @param {Array} locationsList - Array of location objects 
 * @param {Object} options - Options including map and marker references
 */
export const addStatusMarkers = (locationsList, options) => {
  const {
    zoomedOutMap,
    zoomedInMap,
    zoomedOutMarkerCluster,
    zoomedInMarkerCluster,
    openOrderMarkersRef,
    pendingPickupMarkersRef,
    handleSelectLocation
  } = options;
  
  if (!locationsList || !locationsList.length || !zoomedOutMap || !zoomedInMap) return;

  console.log(`Adding location markers for ${locationsList.length} locations`);

  // Clear any existing markers first
  const clearExistingMarkers = () => {
    // Clear open order markers
    if (openOrderMarkersRef && openOrderMarkersRef.current) {
      openOrderMarkersRef.current.zoomedOut.forEach(marker => {
        if (zoomedOutMarkerCluster) {
          safeRemoveFromCluster(zoomedOutMarkerCluster, marker);
        } else if (zoomedOutMap) {
          safeRemoveLayer(zoomedOutMap, marker);
        }
      });

      openOrderMarkersRef.current.zoomedIn.forEach(marker => {
        if (zoomedInMarkerCluster) {
          safeRemoveFromCluster(zoomedInMarkerCluster, marker);
        } else if (zoomedInMap) {
          safeRemoveLayer(zoomedInMap, marker);
        }
      });
    }

    // Clear pending pickup markers
    if (pendingPickupMarkersRef && pendingPickupMarkersRef.current) {
      pendingPickupMarkersRef.current.zoomedOut.forEach(marker => {
        if (zoomedOutMarkerCluster) {
          safeRemoveFromCluster(zoomedOutMarkerCluster, marker);
        } else if (zoomedOutMap) {
          safeRemoveLayer(zoomedOutMap, marker);
        }
      });

      pendingPickupMarkersRef.current.zoomedIn.forEach(marker => {
        if (zoomedInMarkerCluster) {
          safeRemoveFromCluster(zoomedInMarkerCluster, marker);
        } else if (zoomedInMap) {
          safeRemoveLayer(zoomedInMap, marker);
        }
      });
    }
  };

  // Clear existing markers
  clearExistingMarkers();

  // Initialize or reset the markers arrays
  if (openOrderMarkersRef && openOrderMarkersRef.current) {
    openOrderMarkersRef.current = {
      zoomedOut: [],
      zoomedIn: []
    };
  }

  if (pendingPickupMarkersRef && pendingPickupMarkersRef.current) {
    pendingPickupMarkersRef.current = {
      zoomedOut: [],
      zoomedIn: []
    };
  }

  // Sort locations by status to process correctly
  const openOrders = [];
  const pendingPickups = [];

  // Group locations by status type
  locationsList.forEach(location => {
    if (!location.position || !location.position.lat || !location.position.lng) {
      return; // Skip locations without valid coordinates
    }

    const status = location.status ? location.status.toLowerCase() : '';

    if (status === 'pending-pickup' || status === 'awaiting-pickup') {
      pendingPickups.push(location);
    } else if (status === 'open' || status === 'open-order' || status === 'pending') {
      openOrders.push(location);
    }
  });

  console.log(`Found ${openOrders.length} open orders and ${pendingPickups.length} pending pickups`);

  // Add open order markers
  openOrders.forEach(order => {
    // Get vehicle information for the icon
    const vehicleInfo = {
      make: order.make || '',
      model: order.model || '',
      year: order.year || '',
      bodyStyle: order.bodyStyle || ''
    };

    // Get appropriate vehicle SVG path based on make/model
    const vehiclePath = getVehicleIcon(vehicleInfo);

    // Get status style info for this marker
    const statusStyle = getStatusStyle(order.status);

    // Create a more visually appealing open order marker with vehicle icon
    const createOpenOrderIcon = (isZoomedIn) => {
      // Calculate size and colors based on zoom level
      const size = isZoomedIn ? 36 : 28;
      const bgColor = order.priority ? '#EF4444' : statusStyle.bg || '#2563EB'; // Red if priority, status color otherwise
      const strokeColor = '#FFFFFF';
      const iconColor = '#FFFFFF';

      return L.divIcon({
        className: `vehicle-marker ${order.priority ? 'priority' : ''} status-${order.status || 'unknown'}`,
        html: `
          <div class="vehicle-marker-container" style="width:${size}px; height:${size}px;">
            <div class="vehicle-marker-bg" style="background-color:${bgColor}; width:${size}px; height:${size}px; border-radius:50%; position:relative; border:2px solid ${strokeColor};">
              <svg viewBox="0 0 24 24" style="position:absolute; top:50%; left:50%; transform:translate(-50%, -50%); width:${size * 0.65}px; height:${size * 0.65}px;">
                <path fill="${iconColor}" d="${vehiclePath}" />
              </svg>
            </div>
            <div class="vehicle-marker-pulse" style="position:absolute; top:0; left:0; width:${size}px; height:${size}px; border-radius:50%; background-color:${bgColor}; opacity:0.3; z-index:-1;"></div>
          </div>
        `,
        iconSize: [size, size],
        iconAnchor: [size / 2, size / 2]
      });
    };

    // Create markers for both maps
    const markerZoomedOut = L.marker([order.position.lat, order.position.lng], {
      icon: createOpenOrderIcon(false),
      title: `${order.year || ''} ${order.make || ''} ${order.model || ''} - ${order.name || 'Order'}`,
      riseOnHover: true,
      zIndexOffset: order.priority ? 1000 : 800
    });

    const markerZoomedIn = L.marker([order.position.lat, order.position.lng], {
      icon: createOpenOrderIcon(true),
      title: `${order.year || ''} ${order.make || ''} ${order.model || ''} - ${order.name || 'Order'}`,
      riseOnHover: true,
      zIndexOffset: order.priority ? 1000 : 800
    });

    // Create a popup with more details about the order
    const createPopupContent = () => {
      return `
        <div class="order-popup p-2">
          <h3 class="font-bold text-sm">${order.year || ''} ${order.make || ''} ${order.model || ''}</h3>
          ${order.plate ? `<p class="text-xs">Plate: ${order.plate}</p>` : ''}
          ${order.caseNumber ? `<p class="text-xs">Case: ${order.caseNumber}</p>` : ''}
          ${order.status ? `<p class="text-xs">Status: <span class="font-semibold">${getStatusStyle(order.status).label}</span></p>` : ''}
          <div class="mt-2">
            <button id="order-details-btn" class="bg-blue-600 text-white text-xs px-2 py-1 rounded">
              View Details
            </button>
          </div>
        </div>
      `;
    };

    // Add popup to markers
    const popupContent = createPopupContent();
    markerZoomedOut.bindPopup(popupContent);
    markerZoomedIn.bindPopup(popupContent);

    // Add click handlers to pass location to parent via handleSelectLocation
    const addClickHandler = (marker) => {
      // Direct click handler for the marker
      marker.on('click', () => {
        if (typeof handleSelectLocation === 'function') {
          // Pass order to details panel
          handleSelectLocation(order);
        }
      });

      // Click handler for the details button in popup
      marker.on('popupopen', () => {
        setTimeout(() => {
          const detailsBtn = document.getElementById('order-details-btn');
          if (detailsBtn) {
            detailsBtn.addEventListener('click', () => {
              if (typeof handleSelectLocation === 'function') {
                handleSelectLocation(order);
              }
              marker.closePopup();
            });
          }
        }, 10);
      });
    };

    // Add click handlers to both markers
    addClickHandler(markerZoomedOut);
    addClickHandler(markerZoomedIn);

    // Store reference to the markers in parent component
    if (openOrderMarkersRef && openOrderMarkersRef.current) {
      openOrderMarkersRef.current.zoomedOut.push(markerZoomedOut);
      openOrderMarkersRef.current.zoomedIn.push(markerZoomedIn);
    }

    // Add to marker clusters
    if (zoomedOutMarkerCluster) {
      try {
        zoomedOutMarkerCluster.addLayer(markerZoomedOut);
      } catch (e) {
        // Fallback to adding directly to map
        if (zoomedOutMap) {
          zoomedOutMap.addLayer(markerZoomedOut);
        }
      }
    }

    if (zoomedInMarkerCluster) {
      try {
        zoomedInMarkerCluster.addLayer(markerZoomedIn);
      } catch (e) {
        // Fallback to adding directly to map
        if (zoomedInMap) {
          zoomedInMap.addLayer(markerZoomedIn);
        }
      }
    }
  });

  // Add pending pickup markers
  pendingPickups.forEach(pickup => {
    // Get vehicle information for the icon
    const vehicleInfo = {
      make: pickup.make || '',
      model: pickup.model || '',
      year: pickup.year || '',
      bodyStyle: pickup.bodyStyle || ''
    };

    // Get status style info for this marker
    const statusStyle = getStatusStyle(pickup.status);

    // Create a more visually appealing pending pickup marker with status icon
    const createPendingPickupIcon = (isZoomedIn) => {
      // Calculate size and colors based on zoom level
      const size = isZoomedIn ? 36 : 28;
      const bgColor = statusStyle.bg; // Use status-specific color
      const strokeColor = '#FFFFFF';
      const iconColor = '#FFFFFF';

      return L.divIcon({
        className: `pending-pickup-marker status-${pickup.status || 'unknown'}`,
        html: `
          <div class="pending-pickup-container" style="width:${size}px; height:${size}px;">
            <div class="pending-pickup-bg" style="background-color:${bgColor}; width:${size}px; height:${size}px; border-radius:50%; position:relative; border:2px solid ${strokeColor};">
              <svg viewBox="0 0 24 24" style="position:absolute; top:50%; left:50%; transform:translate(-50%, -50%); width:${size * 0.65}px; height:${size * 0.65}px;">
                <path fill="${iconColor}" d="${statusStyle.iconPath}" />
              </svg>
            </div>
            <div class="pending-pickup-pulse" style="position:absolute; top:0; left:0; width:${size}px; height:${size}px; border-radius:50%; background-color:${bgColor}; opacity:0.3; z-index:-1;"></div>
          </div>
        `,
        iconSize: [size, size],
        iconAnchor: [size / 2, size / 2]
      });
    };

    // Create markers for both maps
    const markerZoomedOut = L.marker([pickup.position.lat, pickup.position.lng], {
      icon: createPendingPickupIcon(false),
      title: `${pickup.year || ''} ${pickup.make || ''} ${pickup.model || ''} - ${pickup.name || 'Pickup'} (${statusStyle.label})`,
      riseOnHover: true,
      zIndexOffset: 900 // Higher than open orders but below user location
    });

    const markerZoomedIn = L.marker([pickup.position.lat, pickup.position.lng], {
      icon: createPendingPickupIcon(true),
      title: `${pickup.year || ''} ${pickup.make || ''} ${pickup.model || ''} - ${pickup.name || 'Pickup'} (${statusStyle.label})`,
      riseOnHover: true,
      zIndexOffset: 900
    });

    // Create a popup with more details about the pending pickup
    const createPopupContent = () => {
      return `
        <div class="order-popup p-2">
          <h3 class="font-bold text-sm">${pickup.year || ''} ${pickup.make || ''} ${pickup.model || ''}</h3>
          ${pickup.plate ? `<p class="text-xs">Plate: ${pickup.plate}</p>` : ''}
          ${pickup.caseNumber ? `<p class="text-xs">Case: ${pickup.caseNumber}</p>` : ''}
          <p class="text-xs">Status: <span class="font-semibold" style="color:${statusStyle.bg}">${statusStyle.label}</span></p>
          <div class="mt-2">
            <button id="pickup-details-btn" class="bg-blue-600 text-white text-xs px-2 py-1 rounded">
              View Details
            </button>
            <button id="pickup-navigate-btn" class="bg-green-600 text-white text-xs px-2 py-1 rounded ml-2">
              Navigate
            </button>
          </div>
        </div>
      `;
    };

    // Add popup to markers
    const popupContent = createPopupContent();
    markerZoomedOut.bindPopup(popupContent);
    markerZoomedIn.bindPopup(popupContent);

    // Add click handlers to pass location to parent via handleSelectLocation
    const addClickHandler = (marker) => {
      // Direct click handler for the marker
      marker.on('click', () => {
        if (typeof handleSelectLocation === 'function') {
          // Pass pickup to details panel
          handleSelectLocation(pickup);
        }
      });

      // Click handlers for the buttons in popup
      marker.on('popupopen', () => {
        setTimeout(() => {
          const detailsBtn = document.getElementById('pickup-details-btn');
          if (detailsBtn) {
            detailsBtn.addEventListener('click', () => {
              if (typeof handleSelectLocation === 'function') {
                handleSelectLocation(pickup);
              }
              marker.closePopup();
            });
          }

          const navigateBtn = document.getElementById('pickup-navigate-btn');
          if (navigateBtn) {
            navigateBtn.addEventListener('click', () => {
              if (typeof handleSelectLocation === 'function') {
                // Select the location and then start navigation
                handleSelectLocation(pickup);
                // Set destination for auto-rotation
                if (typeof window.setMapDestination === 'function') {
                  window.setMapDestination(pickup.position.lat, pickup.position.lng);
                }
              }
              marker.closePopup();
            });
          }
        }, 10);
      });
    };

    // Add click handlers to both markers
    addClickHandler(markerZoomedOut);
    addClickHandler(markerZoomedIn);

    // Store reference to the markers
    if (pendingPickupMarkersRef && pendingPickupMarkersRef.current) {
      pendingPickupMarkersRef.current.zoomedOut.push(markerZoomedOut);
      pendingPickupMarkersRef.current.zoomedIn.push(markerZoomedIn);
    }

    // Add to marker clusters
    if (zoomedOutMarkerCluster) {
      try {
        zoomedOutMarkerCluster.addLayer(markerZoomedOut);
      } catch (e) {
        // Fallback to adding directly to map
        if (zoomedOutMap) {
          zoomedOutMap.addLayer(markerZoomedOut);
        }
      }
    }

    if (zoomedInMarkerCluster) {
      try {
        zoomedInMarkerCluster.addLayer(markerZoomedIn);
      } catch (e) {
        // Fallback to adding directly to map
        if (zoomedInMap) {
          zoomedInMap.addLayer(markerZoomedIn);
        }
      }
    }
  });

  console.log(`Added ${openOrderMarkersRef?.current.zoomedOut.length || 0} open order markers and ${pendingPickupMarkersRef?.current.zoomedOut.length || 0} pending pickup markers`);
};

export default {
  safeRemoveLayer,
  safeRemoveFromCluster,
  getVehicleIcon,
  getStatusStyle,
  processOrderAddresses,
  updateUserLocation,
  setDestination,
  addStatusMarkers
};