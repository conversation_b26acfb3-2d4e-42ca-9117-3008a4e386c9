import { 
  collection, 
  query, 
  where, 
  onSnapshot, 
  doc, 
  getDoc, 
  getDocs, 
  setDoc, 
  addDoc, 
  updateDoc,
  deleteDoc,
  serverTimestamp,
  orderBy,
  limit
} from 'firebase/firestore';

// Function to subscribe to team locations
export const subscribeToTeamLocations = (db, teamId, onSuccess, onError) => {
  console.log(`Subscribing to locations for team: ${teamId}`);
  if (!db || !teamId) {
    console.error("Missing required parameters for subscribeToTeamLocations");
    if (onError) onError(new Error("Missing required parameters"));
    return () => {};
  }

  try {
    // Query locations based on teamId
    const locationsRef = collection(db, "locations");
    const q = query(locationsRef, where("teamId", "==", teamId));
    
    return onSnapshot(q, (snapshot) => {
      const locations = [];
      snapshot.forEach(doc => {
        locations.push({
          id: doc.id,
          ...doc.data(),
          // Ensure position exists to prevent errors
          position: doc.data().position || { lat: 0, lng: 0 }
        });
      });
      
      console.log(`Retrieved ${locations.length} locations for team ${teamId}`);
      if (onSuccess) onSuccess(locations);
    }, (error) => {
      console.error("Error subscribing to team locations:", error);
      if (onError) onError(error);
    });
  } catch (error) {
    console.error("Exception in subscribeToTeamLocations:", error);
    if (onError) onError(error);
    return () => {};
  }
};

// Function to subscribe to team user locations
export const subscribeToTeamUserLocations = (db, teamId, onSuccess, onError, options = {}) => {
  console.log(`Subscribing to user locations for team: ${teamId}`);
  const { onlineOnly = true, excludeCurrentUser = false, currentUserId } = options;
  
  if (!db || !teamId) {
    console.error("Missing required parameters for subscribeToTeamUserLocations");
    if (onError) onError(new Error("Missing required parameters"));
    return () => {};
  }

  try {
    // First get all team members
    const getTeamMembers = async () => {
      try {
        const membersRef = collection(db, `teams/${teamId}/teamMembers`);
        const membersSnapshot = await getDocs(membersRef);
        
        if (membersSnapshot.empty) {
          console.log(`No members found for team ${teamId}`);
          if (onSuccess) onSuccess([]);
          return [];
        }
        
        const memberIds = membersSnapshot.docs.map(doc => doc.data().userId);
        console.log(`Found ${memberIds.length} team members for team ${teamId}`);
        return memberIds;
      } catch (error) {
        console.error("Error fetching team members:", error);
        if (onError) onError(error);
        return [];
      }
    };

    // Get team members, then set up user locations listener
    getTeamMembers().then(memberIds => {
      if (memberIds.length === 0) {
        if (onSuccess) onSuccess([]);
        return;
      }

      // Query onlineUsers collection for team members
      const usersRef = collection(db, "onlineUsers");
      let constraints = [where("teamId", "==", teamId)];
      
      // Don't limit to team ID if we have specific member IDs
      if (memberIds.length > 0) {
        // Firebase has a limit of 10 items in an in query
        // For larger teams, we'll need to split this into multiple queries
        if (memberIds.length <= 10) {
          constraints = [where("uid", "in", memberIds)];
        }
      }
      
      if (onlineOnly) {
        constraints.push(where("online", "==", true));
      }
      
      const q = query(usersRef, ...constraints);
      
      return onSnapshot(q, async (snapshot) => {
        const users = [];
        
        for (const doc of snapshot.docs) {
          const userData = doc.data();
          
          // Skip current user if excludeCurrentUser is true
          if (excludeCurrentUser && userData.uid === currentUserId) {
            continue;
          }
          
          // If we have more than 10 memberIds, we need to do individual filtering
          if (memberIds.length > 10 && !memberIds.includes(doc.id)) {
            continue;
          }
          
          // Add user data to results
          users.push({
            uid: doc.id,
            ...userData
          });
        }
        
        console.log(`Retrieved ${users.length} user locations for team ${teamId}`);
        
        // Fetch additional user details if needed
        for (let i = 0; i < users.length; i++) {
          try {
            const userProfileRef = doc(db, "userProfiles", users[i].uid);
            const profileSnap = await getDoc(userProfileRef);
            
            if (profileSnap.exists()) {
              const profileData = profileSnap.data();
              users[i].displayName = profileData.displayName || users[i].displayName;
              users[i].profilePicture = profileData.photoBase64 || profileData.profilePicture;
            }
          } catch (profileError) {
            console.error(`Error fetching profile for user ${users[i].uid}:`, profileError);
          }
        }
        
        if (onSuccess) onSuccess(users);
      }, (error) => {
        console.error("Error subscribing to team user locations:", error);
        if (onError) onError(error);
      });
    });
    
    // Return empty unsubscribe function initially
    // The real one will be returned after async operation completes
    return () => {};
  } catch (error) {
    console.error("Exception in subscribeToTeamUserLocations:", error);
    if (onError) onError(error);
    return () => {};
  }
};

// Function to update user location
export const updateUserLocation = async (db, position, user, teamId = null) => {
  if (!db || !position || !user) {
    console.error("Missing required parameters for updateUserLocation");
    return false;
  }
  
  try {
    const userRef = doc(db, "onlineUsers", user.uid);
    const updateData = {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName || user.email?.split('@')[0] || 'Unknown User',
      online: true,
      lastSeen: serverTimestamp(),
      currentLocation: position
    };
    
    // Add teamId to the data if provided
    if (teamId) {
      updateData.teamId = teamId;
    }
    
    await setDoc(userRef, updateData, { merge: true });
    return true;
  } catch (error) {
    console.error("Error updating user location:", error);
    return false;
  }
};

// Function to update user online status
export const updateUserOnlineStatus = async (db, isOnline, user, teamId = null) => {
  if (!db || !user) {
    console.error("Missing required parameters for updateUserOnlineStatus");
    return false;
  }
  
  try {
    const userRef = doc(db, "onlineUsers", user.uid);
    const updateData = {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName || user.email?.split('@')[0] || 'Unknown User',
      online: isOnline,
      lastSeen: serverTimestamp()
    };
    
    // Add teamId to the data if provided
    if (teamId) {
      updateData.teamId = teamId;
    }
    
    await setDoc(userRef, updateData, { merge: true });
    return true;
  } catch (error) {
    console.error("Error updating user online status:", error);
    return false;
  }
};

// Function to subscribe to team user traces (breadcrumb paths)
export const subscribeToTeamUserTraces = (db, teamId, onSuccess, onError) => {
  console.log(`Subscribing to user traces for team: ${teamId}`);
  if (!db || !teamId) {
    console.error("Missing required parameters for subscribeToTeamUserTraces");
    if (onError) onError(new Error("Missing required parameters"));
    return () => {};
  }

  try {
    // First get all team members
    const getTeamMembers = async () => {
      try {
        const membersRef = collection(db, `teams/${teamId}/teamMembers`);
        const membersSnapshot = await getDocs(membersRef);
        
        if (membersSnapshot.empty) {
          console.log(`No members found for team ${teamId}`);
          if (onSuccess) onSuccess({});
          return [];
        }
        
        const memberIds = membersSnapshot.docs.map(doc => doc.data().userId);
        console.log(`Found ${memberIds.length} team members for team ${teamId}`);
        return memberIds;
      } catch (error) {
        console.error("Error fetching team members:", error);
        if (onError) onError(error);
        return [];
      }
    };

    // Get team members, then set up trace listeners
    getTeamMembers().then(memberIds => {
      if (memberIds.length === 0) {
        if (onSuccess) onSuccess({});
        return;
      }

      const traces = {};
      const unsubscribes = [];
      let loadedCount = 0;
      
      memberIds.forEach(userId => {
        const traceRef = collection(db, `userTraces/${userId}/points`);
        const q = query(
          traceRef, 
          where("teamId", "==", teamId),
          orderBy("timestamp", "asc")
        );
        
        const unsubscribe = onSnapshot(q, (snapshot) => {
          const points = [];
          
          snapshot.forEach(doc => {
            const data = doc.data();
            if (data.position) {
              points.push({
                id: doc.id,
                lat: data.position.lat,
                lng: data.position.lng,
                timestamp: data.timestamp
              });
            }
          });
          
          traces[userId] = points;
          loadedCount++;
          
          // When we've loaded traces for all members, call onSuccess
          if (loadedCount === memberIds.length) {
            console.log(`Retrieved traces for all ${memberIds.length} team members`);
            if (onSuccess) onSuccess(traces);
          }
        }, (error) => {
          console.error(`Error subscribing to traces for user ${userId}:`, error);
          loadedCount++;
          
          if (loadedCount === memberIds.length) {
            if (onSuccess) onSuccess(traces);
          }
        });
        
        unsubscribes.push(unsubscribe);
      });
      
      // Return combined unsubscribe function
      return () => {
        unsubscribes.forEach(unsubscribe => unsubscribe());
      };
    });
    
    // Return empty unsubscribe function initially
    return () => {};
  } catch (error) {
    console.error("Exception in subscribeToTeamUserTraces:", error);
    if (onError) onError(error);
    return () => {};
  }
};

// Function to update user trace (breadcrumb)
export const updateUserTrace = async (db, position, user, teamId = null) => {
  if (!db || !position || !user) {
    console.error("Missing required parameters for updateUserTrace");
    return false;
  }
  
  try {
    const traceRef = collection(db, `userTraces/${user.uid}/points`);
    
    const pointData = {
      position: position,
      timestamp: serverTimestamp(),
      userId: user.uid
    };
    
    // Add teamId to the data if provided
    if (teamId) {
      pointData.teamId = teamId;
    }
    
    await addDoc(traceRef, pointData);
    return true;
  } catch (error) {
    console.error("Error updating user trace:", error);
    return false;
  }
};

// Function to clear user trace
export const clearUserTrace = async (db, userId, currentUser, teamId = null) => {
  if (!db || !userId) {
    console.error("Missing required parameters for clearUserTrace");
    return false;
  }
  
  try {
    // Get all trace points for the user
    const traceRef = collection(db, `userTraces/${userId}/points`);
    let q;
    
    if (teamId) {
      q = query(traceRef, where("teamId", "==", teamId));
    } else {
      q = query(traceRef);
    }
    
    const snapshot = await getDocs(q);
    
    if (snapshot.empty) {
      console.log(`No trace points found for user ${userId}`);
      return true;
    }
    
    // Delete all trace points
    const deletePromises = snapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(deletePromises);
    
    console.log(`Cleared trace for user ${userId}`);
    return true;
  } catch (error) {
    console.error(`Error clearing trace for user ${userId}:`, error);
    return false;
  }
};

// Function to subscribe to team chat messages
export const subscribeToTeamChatMessages = (db, teamId, onSuccess, onError, messageLimit = 100) => {
  console.log(`Subscribing to chat messages for team: ${teamId}`);
  if (!db || !teamId) {
    console.error("Missing required parameters for subscribeToTeamChatMessages");
    if (onError) onError(new Error("Missing required parameters"));
    return () => {};
  }

  try {
    const messagesRef = collection(db, `teams/${teamId}/messages`);
    const q = query(
      messagesRef,
      orderBy("timestamp", "desc"),
      limit(messageLimit)
    );
    
    return onSnapshot(q, async (snapshot) => {
      const messages = [];
      
      for (const doc of snapshot.docs) {
        const messageData = doc.data();
        
        // Add message data to results with any additional processing
        messages.push({
          id: doc.id,
          ...messageData
        });
      }
      
      // Sort messages by timestamp (oldest first)
      messages.sort((a, b) => {
        const timestampA = a.timestamp?.toDate ? a.timestamp.toDate() : new Date(a.timestamp);
        const timestampB = b.timestamp?.toDate ? b.timestamp.toDate() : new Date(b.timestamp);
        return timestampA - timestampB;
      });
      
      console.log(`Retrieved ${messages.length} chat messages for team ${teamId}`);
      if (onSuccess) onSuccess(messages);
    }, (error) => {
      console.error("Error subscribing to team chat messages:", error);
      if (onError) onError(error);
    });
  } catch (error) {
    console.error("Exception in subscribeToTeamChatMessages:", error);
    if (onError) onError(error);
    return () => {};
  }
};

// Function to get user profile
export const getUserProfile = async (db, userId) => {
  if (!db || !userId) {
    console.error("Missing required parameters for getUserProfile");
    return null;
  }
  
  try {
    const profileRef = doc(db, "userProfiles", userId);
    const profileSnap = await getDoc(profileRef);
    
    if (profileSnap.exists()) {
      return {
        id: userId,
        ...profileSnap.data()
      };
    } else {
      console.log(`No profile found for user ${userId}`);
      return null;
    }
  } catch (error) {
    console.error(`Error getting profile for user ${userId}:`, error);
    return null;
  }
};

// Function to get user clock status
export const getUserClockStatus = async (db, userId) => {
  if (!db || !userId) {
    console.error("Missing required parameters for getUserClockStatus");
    return { clockedIn: false, clockInTime: null };
  }
  
  try {
    const clockRef = doc(db, "userClockStatus", userId);
    const clockSnap = await getDoc(clockRef);
    
    if (clockSnap.exists()) {
      const data = clockSnap.data();
      return {
        clockedIn: data.clockedIn || false,
        clockInTime: data.clockInTime?.toDate ? data.clockInTime.toDate() : data.clockInTime
      };
    } else {
      return { clockedIn: false, clockInTime: null };
    }
  } catch (error) {
    console.error(`Error getting clock status for user ${userId}:`, error);
    return { clockedIn: false, clockInTime: null };
  }
};

// Function to get team data
export const getTeamData = async (db, teamId) => {
  if (!db || !teamId) {
    console.error("Missing required parameters for getTeamData");
    return null;
  }
  
  try {
    console.log(`Getting data for team ${teamId}`);
    const teamRef = doc(db, "teams", teamId);
    const teamSnap = await getDoc(teamRef);
    
    if (teamSnap.exists()) {
      const teamData = {
        id: teamId,
        ...teamSnap.data()
      };
      
      // Check if area data exists, if not but areaId exists, fetch area
      if (teamData.areaId && !teamData.area) {
        try {
          const areaRef = doc(db, "areas", teamData.areaId);
          const areaSnap = await getDoc(areaRef);
          
          if (areaSnap.exists()) {
            teamData.area = {
              id: teamData.areaId,
              ...areaSnap.data()
            };
          }
        } catch (areaError) {
          console.error(`Error fetching area for team ${teamId}:`, areaError);
        }
      }
      
      console.log(`Retrieved data for team ${teamId}:`, teamData.name);
      return teamData;
    } else {
      console.log(`No data found for team ${teamId}`);
      return null;
    }
  } catch (error) {
    console.error(`Error getting data for team ${teamId}:`, error);
    return null;
  }
};

// Updated function to validate team access
export const validateTeamAccess = async (db, userId, teamId, isAdmin) => {
  // Skip validation for these cases:
  if (!db || !userId || !teamId) {
    console.log("validateTeamAccess: Missing required parameters");
    return false;
  }
  
  console.log(`validateTeamAccess: Checking if user ${userId} has access to team ${teamId}`);
  
  // Admins always have access
  if (isAdmin) {
    console.log("validateTeamAccess: User is admin, access granted");
    return true;
  }
  
  try {
    // Check if user is a member of the team
    const teamMembersRef = collection(db, `teams/${teamId}/teamMembers`);
    const q = query(teamMembersRef, where("userId", "==", userId));
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      console.log("validateTeamAccess: User is a team member, access granted");
      return true;
    }
    
    // If we're checking this during direct user selection, we can be more permissive
    // This is a hack, but allows users to select teams even if they're not explicitly members
    if (document.querySelector('.team-selector-overlay')) {
      console.log("validateTeamAccess: Team selector is open, granting access");
      return true;
    }
    
    console.log("validateTeamAccess: User is not a team member, access denied");
    return false;
  } catch (error) {
    console.error("validateTeamAccess: Error checking team access", error);
    // In case of error, we'll err on the side of caution and deny access
    return false;
  }
};

// Function to get user teams
export const getUserTeams = async (db, userId) => {
  if (!db || !userId) {
    console.error("Missing required parameters for getUserTeams");
    return [];
  }
  
  try {
    // Get teams where user is a member
    const teamMembersRef = collection(db, "teamMembers");
    const q = query(teamMembersRef, where("userId", "==", userId));
    const snapshot = await getDocs(q);
    
    if (snapshot.empty) {
      console.log(`User ${userId} is not a member of any teams`);
      return [];
    }
    
    // Get team data for each team
    const teamIds = snapshot.docs.map(doc => doc.data().teamId);
    const teamsData = [];
    
    for (const teamId of teamIds) {
      const teamData = await getTeamData(db, teamId);
      if (teamData) {
        teamsData.push(teamData);
      }
    }
    
    console.log(`Retrieved ${teamsData.length} teams for user ${userId}`);
    return teamsData;
  } catch (error) {
    console.error(`Error getting teams for user ${userId}:`, error);
    return [];
  }
};

// Function to create team
export const createTeam = async (db, teamData, createdByUserId) => {
  if (!db || !teamData || !createdByUserId) {
    console.error("Missing required parameters for createTeam");
    throw new Error("Missing required parameters");
  }
  
  try {
    // Add additional team metadata
    const team = {
      ...teamData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy: createdByUserId
    };
    
    // Create team document
    const teamRef = await addDoc(collection(db, "teams"), team);
    console.log(`Created new team: ${teamData.name} with ID: ${teamRef.id}`);
    
    return teamRef.id;
  } catch (error) {
    console.error("Error creating team:", error);
    throw error;
  }
};

// Function to add user to team
export const addUserToTeam = async (db, userId, teamId, role = 'user', addedByUserId) => {
  if (!db || !userId || !teamId) {
    console.error("Missing required parameters for addUserToTeam");
    throw new Error("Missing required parameters");
  }
  
  try {
    // Check if user is already in the team
    const membersRef = collection(db, `teams/${teamId}/teamMembers`);
    const q = query(membersRef, where("userId", "==", userId));
    const snapshot = await getDocs(q);
    
    if (!snapshot.empty) {
      console.log(`User ${userId} is already a member of team ${teamId}`);
      return;
    }
    
    // Add user to team
    const memberData = {
      userId: userId,
      role: role || 'user',
      addedAt: serverTimestamp(),
      addedBy: addedByUserId || userId
    };
    
    const memberRef = await addDoc(membersRef, memberData);
    
    // Update team member count
    const teamRef = doc(db, "teams", teamId);
    const teamDoc = await getDoc(teamRef);
    
    if (teamDoc.exists()) {
      const teamData = teamDoc.data();
      const memberCount = teamData.memberCount || 0;
      
      await updateDoc(teamRef, {
        memberCount: memberCount + 1,
        updatedAt: serverTimestamp()
      });
    }
    
    console.log(`Added user ${userId} to team ${teamId}`);
  } catch (error) {
    console.error(`Error adding user ${userId} to team ${teamId}:`, error);
    throw error;
  }
};

// Export all functions
export default {
  subscribeToTeamLocations,
  subscribeToTeamUserLocations,
  updateUserLocation,
  updateUserOnlineStatus,
  subscribeToTeamUserTraces,
  updateUserTrace,
  clearUserTrace,
  subscribeToTeamChatMessages,
  getUserProfile,
  getUserClockStatus,
  getTeamData,
  validateTeamAccess,
  getUserTeams,
  createTeam,
  addUserToTeam
};