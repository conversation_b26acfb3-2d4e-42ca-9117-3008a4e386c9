import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { collection, getDocs, getDoc, doc, updateDoc, onSnapshot, query, where, orderBy, addDoc, Timestamp, arrayUnion, arrayRemove, deleteDoc } from 'firebase/firestore';
import { db } from '../pages/firebase.js';
import { useAuth } from '../contexts/AuthContext.js';

function TeamMeeting() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const { meetingId: urlMeetingId } = useParams(); // Get meeting ID from URL if present
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [meetingActive, setMeetingActive] = useState(false);
  const [activeParticipants, setActiveParticipants] = useState([]);
  const [localStream, setLocalStream] = useState(null);
  const [localVideoEnabled, setLocalVideoEnabled] = useState(false);
  const [localAudioEnabled, setLocalAudioEnabled] = useState(true);
  const [participantVolumes, setParticipantVolumes] = useState({});
  const [meetingName, setMeetingName] = useState('');
  const [showParticipantSelector, setShowParticipantSelector] = useState(true);
  const [meetingId, setMeetingId] = useState(null);
  const [userProfiles, setUserProfiles] = useState({});
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [meetingCreator, setMeetingCreator] = useState(null);
  const [masterVolume, setMasterVolume] = useState(100);

  // New state variables for camera option
  const [showCameraModal, setShowCameraModal] = useState(false);
  const [cameraPreference, setCameraPreference] = useState("no-camera");
  const [hasCamera, setHasCamera] = useState(false);

  // Participant management modals
  const [showAddParticipantModal, setShowAddParticipantModal] = useState(false);
  const [showManageParticipantsModal, setShowManageParticipantsModal] = useState(false);
  const [availableUsers, setAvailableUsers] = useState([]);

  // New state for meeting discovery
  const [activeMeetings, setActiveMeetings] = useState([]);
  const [meetingView, setMeetingView] = useState('create'); // 'create' or 'list'
  
  // Join request states
  const [pendingJoinRequests, setPendingJoinRequests] = useState([]);
  const [joinRequestSent, setJoinRequestSent] = useState({});
  const [showJoinRequestsModal, setShowJoinRequestsModal] = useState(false);
  const [deleteConfirmMeetingId, setDeleteConfirmMeetingId] = useState(null);

  const localVideoRef = useRef(null);
  const messagesEndRef = useRef(null);
  const previewVideoRef = useRef(null);
  const audioElements = useRef({});

  // Auto-join if meeting ID is in URL and user is authorized
  useEffect(() => {
    if (urlMeetingId && !meetingActive) {
      checkAndJoinMeetingFromUrl(urlMeetingId);
    }
  }, [urlMeetingId, userProfiles]);

  // Function to check if user can join meeting from URL
  const checkAndJoinMeetingFromUrl = async (meetingIdToCheck) => {
    try {
      setLoading(true);
      
      // Check if meeting exists and is active
      const meetingRef = doc(db, "meetings", meetingIdToCheck);
      const meetingSnap = await getDoc(meetingRef);
      
      if (!meetingSnap.exists()) {
        alert("Meeting not found. The meeting may have been deleted or never existed.");
        navigate('/team-meeting', { replace: true });
        setLoading(false);
        return;
      }
      
      const meetingData = meetingSnap.data();
      
      if (!meetingData.active) {
        alert("This meeting has ended.");
        navigate('/team-meeting', { replace: true });
        setLoading(false);
        return;
      }
      
      // User can join if:
      // 1. They are the creator
      // 2. They are an admin
      // 3. They are in the participants list
      if (
        meetingData.createdBy === currentUser.uid || 
        isAdmin || 
        meetingData.participants.includes(currentUser.uid)
      ) {
        // Set up for joining
        setMeetingId(meetingIdToCheck);
        setMeetingName(meetingData.name || "Unnamed Meeting");
        setMeetingCreator(meetingData.createdBy);
        
        // Show camera preference modal before joining
        setShowCameraModal(true);
      } else {
        // User is not authorized to join directly
        alert("You need to request access to join this meeting.");
        navigate('/team-meeting', { replace: true });
      }
      
      setLoading(false);
    } catch (error) {
      console.error("Error checking meeting access:", error);
      setLoading(false);
      navigate('/team-meeting', { replace: true });
    }
  };

  // Check if device has a camera
  useEffect(() => {
    if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
      navigator.mediaDevices.enumerateDevices()
        .then(devices => {
          const videoDevices = devices.filter(device => device.kind === 'videoinput');
          setHasCamera(videoDevices.length > 0);
        })
        .catch(err => {
          console.error("Error checking for camera:", err);
          setHasCamera(false);
        });
    } else {
      setHasCamera(false);
    }
  }, []);

  // Show camera preview if selected
  useEffect(() => {
    let stream = null;
    if (showCameraModal && cameraPreference === "with-camera" && previewVideoRef.current) {
      navigator.mediaDevices.getUserMedia({ video: true })
        .then(videoStream => {
          previewVideoRef.current.srcObject = videoStream;
          stream = videoStream;
        })
        .catch(err => {
          console.error("Error accessing camera for preview:", err);
          setCameraPreference("no-camera");
        });
    }
    
    // Cleanup function
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [showCameraModal, cameraPreference]);

  // Apply master volume to all participant volumes
  useEffect(() => {
    // Update volume for each audio element based on individual and master volume
    Object.keys(audioElements.current).forEach(userId => {
      const audioEl = audioElements.current[userId];
      if (audioEl) {
        // Calculate actual volume: individual volume * master volume / 100
        const individualVolume = participantVolumes[userId] || 100;
        audioEl.volume = (individualVolume * masterVolume) / 10000;
      }
    });
  }, [participantVolumes, masterVolume]);

  // Fetch users and active meetings
  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        
        // Fetch users from Firestore
        const usersCollection = collection(db, 'users');
        const userSnapshot = await getDocs(usersCollection);
        let usersList = userSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          selected: false,
          cameraEnabled: true,
          micEnabled: true,
          volume: 100
        }));
        
        // Pre-select current user
        const updatedUsers = usersList.map(user => {
          if (user.id === currentUser.uid) {
            return { ...user, selected: true };
          }
          return user;
        });
        
        setUsers(updatedUsers);
        setSelectedUsers([currentUser.uid]);
        
        // Fetch user profiles for display names
        const profiles = {};
        for (const user of usersList) {
          const profileRef = doc(db, "userProfiles", user.id);
          const profileDoc = await getDoc(profileRef);
          
          if (profileDoc.exists()) {
            profiles[user.id] = profileDoc.data();
          } else {
            profiles[user.id] = { displayName: user.email.split('@')[0] };
          }
        }
        
        setUserProfiles(profiles);
        
        // Fetch active meetings
        await fetchActiveMeetings();
        
        // Fetch join requests for the current user's meetings
        await fetchJoinRequests();
        
        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    }

    fetchData();
  }, [currentUser]);
  
  // Fetch join requests for meetings I created
  const fetchJoinRequests = async () => {
    try {
      const requestsQuery = query(
        collection(db, "meetingJoinRequests"),
        where("meetingCreatorId", "==", currentUser.uid),
        where("status", "==", "pending")
      );
      
      const requestsSnapshot = await getDocs(requestsQuery);
      
      if (requestsSnapshot.empty) {
        setPendingJoinRequests([]);
        return;
      }
      
      const requests = requestsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        requestedAt: doc.data().requestedAt?.toDate() || new Date()
      }));
      
      setPendingJoinRequests(requests);
    } catch (error) {
      console.error("Error fetching join requests:", error);
    }
  };

  // Fetch active meetings the user can join
  const fetchActiveMeetings = async () => {
    try {
      // Query for meetings that are active
      const meetingsQuery = query(
        collection(db, "meetings"),
        where("active", "==", true)
      );
      
      const meetingsSnapshot = await getDocs(meetingsQuery);
      
      if (meetingsSnapshot.empty) {
        setActiveMeetings([]);
        return;
      }
      
      // Get all meetings, filtering will be done in the UI
      const allMeetings = meetingsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })).sort((a, b) => b.createdAt - a.createdAt); // Sort newest first
      
      setActiveMeetings(allMeetings);
      
      // Check if user has pending join requests
      const sentRequestsQuery = query(
        collection(db, "meetingJoinRequests"),
        where("requesterId", "==", currentUser.uid),
        where("status", "==", "pending")
      );
      
      const sentRequestsSnapshot = await getDocs(sentRequestsQuery);
      
      const requestMap = {};
      sentRequestsSnapshot.docs.forEach(doc => {
        const data = doc.data();
        requestMap[data.meetingId] = doc.id;
      });
      
      setJoinRequestSent(requestMap);
      
    } catch (error) {
      console.error("Error fetching active meetings:", error);
    }
  };
  
  // Handle sending a join request
  const sendJoinRequest = async (meetingId, meetingName, creatorId) => {
    try {
      // Check if request already sent
      if (joinRequestSent[meetingId]) {
        alert("You have already requested to join this meeting. Please wait for approval.");
        return;
      }
      
      // Create join request
      const requestData = {
        meetingId,
        meetingName,
        requesterId: currentUser.uid,
        requesterName: userProfiles[currentUser.uid]?.displayName || currentUser.email,
        meetingCreatorId: creatorId,
        status: "pending",
        requestedAt: Timestamp.now()
      };
      
      const requestRef = await addDoc(collection(db, "meetingJoinRequests"), requestData);
      
      // Update local state
      setJoinRequestSent(prev => ({
        ...prev,
        [meetingId]: requestRef.id
      }));
      
      alert("Join request sent. You will be notified when the host responds.");
      
    } catch (error) {
      console.error("Error sending join request:", error);
      alert("Failed to send join request. Please try again.");
    }
  };
  
  // Handle approving a join request
  const approveJoinRequest = async (requestId, meetingId, requesterId) => {
    try {
      // Update request status
      await updateDoc(doc(db, "meetingJoinRequests", requestId), {
        status: "approved",
        respondedAt: Timestamp.now()
      });
      
      // Add user to meeting participants
      await updateDoc(doc(db, "meetings", meetingId), {
        participants: arrayUnion(requesterId)
      });
      
      // Add system message to meeting chat
      await addDoc(collection(db, "meetings", meetingId, "messages"), {
        content: `${userProfiles[requesterId]?.displayName || "A user"} was added to the meeting by ${userProfiles[currentUser.uid]?.displayName || currentUser.email}`,
        senderId: "system",
        senderName: "System",
        timestamp: Timestamp.now(),
        type: "system"
      });
      
      // Remove request from pending list
      setPendingJoinRequests(prev => prev.filter(req => req.id !== requestId));
      
      alert("Join request approved.");
      
    } catch (error) {
      console.error("Error approving join request:", error);
      alert("Failed to approve request. Please try again.");
    }
  };
  
  // Handle denying a join request
  const denyJoinRequest = async (requestId) => {
    try {
      // Update request status
      await updateDoc(doc(db, "meetingJoinRequests", requestId), {
        status: "denied",
        respondedAt: Timestamp.now()
      });
      
      // Remove request from pending list
      setPendingJoinRequests(prev => prev.filter(req => req.id !== requestId));
      
      alert("Join request denied.");
      
    } catch (error) {
      console.error("Error denying join request:", error);
      alert("Failed to deny request. Please try again.");
    }
  };
  
  // Delete meeting (admin only)
  const deleteMeeting = async (meetingId) => {
    try {
      // Only allow admin or creator
      const meetingRef = doc(db, "meetings", meetingId);
      const meetingDoc = await getDoc(meetingRef);
      
      if (!meetingDoc.exists()) {
        alert("Meeting not found.");
        return;
      }
      
      const meetingData = meetingDoc.data();
      
      if (meetingData.createdBy !== currentUser.uid && !isAdmin) {
        alert("You don't have permission to delete this meeting.");
        return;
      }
      
      // Add system message before deleting
      await addDoc(collection(db, "meetings", meetingId, "messages"), {
        content: `Meeting was deleted by ${userProfiles[currentUser.uid]?.displayName || currentUser.email}`,
        senderId: "system",
        senderName: "System",
        timestamp: Timestamp.now(),
        type: "system"
      });
      
      // Mark meeting as inactive (soft delete)
      await updateDoc(meetingRef, {
        active: false,
        deletedAt: Timestamp.now(),
        deletedBy: currentUser.uid
      });
      
      // Refresh meetings list
      await fetchActiveMeetings();
      
      setDeleteConfirmMeetingId(null);
      
      alert("Meeting has been deleted.");
      
    } catch (error) {
      console.error("Error deleting meeting:", error);
      alert("Failed to delete meeting. Please try again.");
    }
  };

  // Set up local stream based on user preferences
  const initLocalStream = async (withVideo = false) => {
    try {
      // First try to get audio-only stream if no video is requested
      const constraints = {
        audio: true,
        video: withVideo
      };
      
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      setLocalStream(stream);
      
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }
      
      setLocalVideoEnabled(withVideo);
      return stream;
    } catch (error) {
      console.error("Error accessing media devices:", error);
      
      // If failed with video, try audio only
      if (withVideo) {
        try {
          const audioOnlyStream = await navigator.mediaDevices.getUserMedia({ 
            audio: true, 
            video: false 
          });
          
          setLocalStream(audioOnlyStream);
          setLocalVideoEnabled(false);
          
          return audioOnlyStream;
        } catch (audioError) {
          console.error("Error accessing audio:", audioError);
          alert("Could not access your microphone. Please check your permissions.");
          return null;
        }
      } else {
        alert("Could not access your microphone. Please check your permissions.");
        return null;
      }
    }
  };

  // Clean up media streams when component unmounts
  useEffect(() => {
    return () => {
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [localStream]);

  // Scroll chat to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Monitor meeting status and messages if in a meeting
  useEffect(() => {
    if (!meetingId) return;
    
    const meetingRef = doc(db, "meetings", meetingId);
    const messagesRef = collection(db, "meetings", meetingId, "messages");
    const messagesQuery = query(messagesRef, orderBy("timestamp", "asc"));
    
    // Listen for meeting updates
    const meetingUnsubscribe = onSnapshot(meetingRef, (docSnapshot) => {
      if (docSnapshot.exists()) {
        const meetingData = docSnapshot.data();
        setActiveParticipants(meetingData.participants || []);
        setMeetingCreator(meetingData.createdBy);
        setMeetingName(meetingData.name || "Unnamed Meeting");
        
        // Update the selected users to match the current participants in the meeting
        setSelectedUsers(meetingData.participants || []);
        
        // Check if meeting was ended
        if (meetingData.active === false) {
          alert("This meeting has been ended by the host.");
          resetMeetingState();
        }
      } else {
        // Meeting document no longer exists
        alert("This meeting is no longer available.");
        resetMeetingState();
      }
    });
    
    // Listen for new messages
    const messagesUnsubscribe = onSnapshot(messagesQuery, (querySnapshot) => {
      const chatMessages = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date()
      }));
      
      setMessages(chatMessages);
    });
    
    return () => {
      meetingUnsubscribe();
      messagesUnsubscribe();
    };
  }, [meetingId]);
  
  // Listen for join request notifications
  useEffect(() => {
    if (!currentUser) return;
    
    const joinRequestsRef = collection(db, "meetingJoinRequests");
    const joinRequestsQuery = query(
      joinRequestsRef, 
      where("meetingCreatorId", "==", currentUser.uid),
      where("status", "==", "pending")
    );
    
    const unsubscribe = onSnapshot(joinRequestsQuery, (snapshot) => {
      // Only show notification if there are new requests
      if (!snapshot.empty && snapshot.docChanges().some(change => change.type === 'added')) {
        const requests = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          requestedAt: doc.data().requestedAt?.toDate() || new Date()
        }));
        
        setPendingJoinRequests(requests);
        
        // Notify user of new join request
        if (requests.length > 0) {
          const latestRequest = requests.sort((a, b) => b.requestedAt - a.requestedAt)[0];
          alert(`${latestRequest.requesterName} requested to join your meeting "${latestRequest.meetingName}"`);
        }
      }
    });
    
    return () => unsubscribe();
  }, [currentUser]);

  // Reset meeting state when leaving or ending meeting
  const resetMeetingState = () => {
    // Stop all media tracks
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
    }
    
    setMeetingActive(false);
    setShowParticipantSelector(true);
    setLocalStream(null);
    setMeetingId(null);
    setMessages([]);
    setMeetingView('create');
    
    // Clear URL parameter if it exists
    if (urlMeetingId) {
      navigate('/team-meeting', { replace: true });
    }
  };

  // Update available users for adding to meeting
  useEffect(() => {
    if (meetingActive && showAddParticipantModal) {
      // Filter users to show only those not already in the meeting
      const nonParticipants = users.filter(user => !selectedUsers.includes(user.id));
      setAvailableUsers(nonParticipants);
    }
  }, [showAddParticipantModal, users, selectedUsers, meetingActive]);

  // Toggle user selection
  const toggleUserSelection = (userId) => {
    const updatedUsers = users.map(user => {
      if (user.id === userId) {
        return { ...user, selected: !user.selected };
      }
      return user;
    });
    
    setUsers(updatedUsers);
    
    const newSelectedUsers = updatedUsers
      .filter(user => user.selected)
      .map(user => user.id);
    
    setSelectedUsers(newSelectedUsers);
  };

  // Start the meeting preparation by showing camera modal
  const prepareStartMeeting = () => {
    if (selectedUsers.length === 0) {
      alert("Please select at least one participant for the meeting.");
      return;
    }
    
    if (!meetingName.trim()) {
      alert("Please enter a name for the meeting.");
      return;
    }
    
    setShowCameraModal(true);
  };

  // Start the meeting after camera preference is set
  const startMeeting = async () => {
    try {
      setLoading(true);
      setShowCameraModal(false);
      
      // Initialize local media stream based on camera preference
      const stream = await initLocalStream(cameraPreference === "with-camera");
      if (!stream) {
        setLoading(false);
        return;
      }
      
      // Create new meeting in Firestore
      const meetingData = {
        name: meetingName,
        createdBy: currentUser.uid,
        createdAt: Timestamp.now(),
        participants: selectedUsers,
        active: true
      };
      
      const meetingRef = await addDoc(collection(db, "meetings"), meetingData);
      setMeetingId(meetingRef.id);
      setMeetingCreator(currentUser.uid);
      
      // Update URL with meeting ID for easy sharing
      navigate(`/team-meeting/${meetingRef.id}`, { replace: true });
      
      // Initialize participant volumes
      const initialVolumes = {};
      selectedUsers.forEach(userId => {
        initialVolumes[userId] = 100;
      });
      
      setParticipantVolumes(initialVolumes);
      setMeetingActive(true);
      setShowParticipantSelector(false);
      setLoading(false);
      
      // Add initial system message
      await addDoc(collection(db, "meetings", meetingRef.id, "messages"), {
        content: `Meeting "${meetingName}" started by ${userProfiles[currentUser.uid]?.displayName || currentUser.email}`,
        senderId: "system",
        senderName: "System",
        timestamp: Timestamp.now(),
        type: "system"
      });
      
      // Add system message about camera status
      await addDoc(collection(db, "meetings", meetingRef.id, "messages"), {
        content: `${userProfiles[currentUser.uid]?.displayName || currentUser.email} joined ${cameraPreference === "with-camera" ? "with camera" : "audio only"}`,
        senderId: "system",
        senderName: "System",
        timestamp: Timestamp.now(),
        type: "system"
      });
      
    } catch (error) {
      console.error("Error starting meeting:", error);
      setLoading(false);
      alert("Failed to start meeting. Please try again.");
    }
  };

  // End the meeting
  const endMeeting = async () => {
    // Only allow meeting creator to end meeting
    if (meetingCreator !== currentUser.uid && !isAdmin) {
      alert("Only the meeting creator can end the meeting for everyone.");
      return;
    }
    
    try {
      if (meetingId) {
        await updateDoc(doc(db, "meetings", meetingId), {
          active: false,
          endedAt: Timestamp.now()
        });
        
        await addDoc(collection(db, "meetings", meetingId, "messages"), {
          content: `Meeting ended by ${userProfiles[currentUser.uid]?.displayName || currentUser.email}`,
          senderId: "system",
          senderName: "System",
          timestamp: Timestamp.now(),
          type: "system"
        });
      }
      
      resetMeetingState();
      
    } catch (error) {
      console.error("Error ending meeting:", error);
    }
  };

  // Leave meeting (for non-creators)
  const leaveMeeting = async () => {
    try {
      // Add system message about leaving
      if (meetingId) {
        await addDoc(collection(db, "meetings", meetingId, "messages"), {
          content: `${userProfiles[currentUser.uid]?.displayName || currentUser.email} left the meeting`,
          senderId: "system",
          senderName: "System",
          timestamp: Timestamp.now(),
          type: "system"
        });
        
        // Remove current user from participants
        await updateDoc(doc(db, "meetings", meetingId), {
          participants: arrayRemove(currentUser.uid)
        });
      }
      
      resetMeetingState();
      
    } catch (error) {
      console.error("Error leaving meeting:", error);
    }
  };

  // Handle joining the meeting after selecting camera preference
  const handleJoinMeeting = async () => {
    try {
      setLoading(true);
      setShowCameraModal(false);
      
      // Initialize local media stream based on camera preference
      const stream = await initLocalStream(cameraPreference === "with-camera");
      if (!stream) {
        setLoading(false);
        return;
      }
      
      // Add current user to meeting participants if not already present
      const meetingRef = doc(db, "meetings", meetingId);
      const meetingSnap = await getDoc(meetingRef);
      
      if (meetingSnap.exists()) {
        const meetingData = meetingSnap.data();
        if (!meetingData.participants.includes(currentUser.uid)) {
          await updateDoc(meetingRef, {
            participants: arrayUnion(currentUser.uid)
          });
        }
      }
      
      // Initialize participant volumes for all participants
      const meetingParticipants = meetingSnap.data().participants || [];
      const initialVolumes = {};
      meetingParticipants.forEach(userId => {
        initialVolumes[userId] = 100;
      });
      
      setParticipantVolumes(initialVolumes);
      setMeetingActive(true);
      setShowParticipantSelector(false);
      setLoading(false);
      
      // Add system message about joining
      await addDoc(collection(db, "meetings", meetingId, "messages"), {
        content: `${userProfiles[currentUser.uid]?.displayName || currentUser.email} joined the meeting ${cameraPreference === "with-camera" ? "with camera" : "audio only"}`,
        senderId: "system",
        senderName: "System",
        timestamp: Timestamp.now(),
        type: "system"
      });
      
    } catch (error) {
      console.error("Error joining meeting:", error);
      setLoading(false);
      alert("Failed to join meeting. Please try again.");
    }
  };

  // Add new participants to the active meeting
  const addParticipantsToMeeting = async (userIds) => {
    if (!meetingId || meetingCreator !== currentUser.uid) return;
    
    try {
      const participantsToAdd = userIds.filter(id => !selectedUsers.includes(id));
      
      if (participantsToAdd.length === 0) return;
      
      // Update meeting participants in Firestore
      await updateDoc(doc(db, "meetings", meetingId), {
        participants: arrayUnion(...participantsToAdd)
      });
      
      // Add system message about new participants
      const addedUserNames = participantsToAdd.map(id => 
        userProfiles[id]?.displayName || users.find(u => u.id === id)?.email || 'Unknown user'
      ).join(', ');
      
      await addDoc(collection(db, "meetings", meetingId, "messages"), {
        content: `${addedUserNames} ${participantsToAdd.length > 1 ? 'were' : 'was'} added to the meeting by ${userProfiles[currentUser.uid]?.displayName || currentUser.email}`,
        senderId: "system",
        senderName: "System",
        timestamp: Timestamp.now(),
        type: "system"
      });
      
      setShowAddParticipantModal(false);
    } catch (error) {
      console.error("Error adding participants:", error);
      alert("Failed to add participants. Please try again.");
    }
  };

  // Remove participant from meeting
  const removeParticipantFromMeeting = async (userId) => {
    if (!meetingId || (meetingCreator !== currentUser.uid && !isAdmin)) return;
    
    // Don't allow removing the creator
    if (userId === meetingCreator) {
      alert("You cannot remove the meeting creator.");
      return;
    }
    
    try {
      // Update meeting participants in Firestore
      await updateDoc(doc(db, "meetings", meetingId), {
        participants: arrayRemove(userId)
      });
      
      // Add system message about removed participant
      const userName = userProfiles[userId]?.displayName || users.find(u => u.id === userId)?.email || 'Unknown user';
      
      await addDoc(collection(db, "meetings", meetingId, "messages"), {
        content: `${userName} was removed from the meeting by ${userProfiles[currentUser.uid]?.displayName || currentUser.email}`,
        senderId: "system",
        senderName: "System",
        timestamp: Timestamp.now(),
        type: "system"
      });
    } catch (error) {
      console.error("Error removing participant:", error);
      alert("Failed to remove participant. Please try again.");
    }
  };

  // Toggle local video
  const toggleVideo = async () => {
    if (!localStream) return;
    
    try {
      // If video is currently disabled and we want to enable it
      if (!localVideoEnabled) {
        // Stop the current stream
        localStream.getTracks().forEach(track => track.stop());
        
        // Get a new stream with video
        const newStream = await navigator.mediaDevices.getUserMedia({ 
          audio: true, 
          video: true 
        });
        
        // Set as local stream
        setLocalStream(newStream);
        
        // Update the video element
        if (localVideoRef.current) {
          localVideoRef.current.srcObject = newStream;
        }
        
        setLocalVideoEnabled(true);
      } else {
        // If video is enabled and we want to disable it
        const videoTracks = localStream.getVideoTracks();
        videoTracks.forEach(track => {
          track.stop();
        });
        
        // Get an audio-only stream
        const audioOnlyStream = await navigator.mediaDevices.getUserMedia({ 
          audio: true, 
          video: false 
        });
        
        // Set as local stream
        setLocalStream(audioOnlyStream);
        
        // Update the video element
        if (localVideoRef.current) {
          localVideoRef.current.srcObject = audioOnlyStream;
        }
        
        setLocalVideoEnabled(false);
      }
      
      // Add system message about camera toggle
      if (meetingId) {
        await addDoc(collection(db, "meetings", meetingId, "messages"), {
          content: `${userProfiles[currentUser.uid]?.displayName || currentUser.email} turned ${!localVideoEnabled ? "on" : "off"} their camera`,
          senderId: "system",
          senderName: "System",
          timestamp: Timestamp.now(),
          type: "system"
        });
      }
    } catch (error) {
      console.error("Error toggling video:", error);
      alert("Failed to toggle camera. Please check your camera permissions.");
    }
  };

  // Toggle local audio
  const toggleAudio = () => {
    if (!localStream) return;
    
    const audioTracks = localStream.getAudioTracks();
    if (audioTracks.length === 0) return;
    
    const enabled = !localAudioEnabled;
    audioTracks.forEach(track => {
      track.enabled = enabled;
    });
    
    setLocalAudioEnabled(enabled);
    
    // Add system message about microphone toggle
    if (meetingId) {
      addDoc(collection(db, "meetings", meetingId, "messages"), {
        content: `${userProfiles[currentUser.uid]?.displayName || currentUser.email} ${enabled ? "unmuted" : "muted"} their microphone`,
        senderId: "system",
        senderName: "System",
        timestamp: Timestamp.now(),
        type: "system"
      });
    }
  };

  // Handle individual volume change for a participant
  const handleVolumeChange = (userId, volume) => {
    setParticipantVolumes(prev => ({
      ...prev,
      [userId]: volume
    }));
    
    // If we have an audio element for this user, update its volume
    if (audioElements.current[userId]) {
      const actualVolume = (volume * masterVolume) / 10000;
      audioElements.current[userId].volume = actualVolume;
    }
  };

  // Handle master volume change
  const handleMasterVolumeChange = (volume) => {
    setMasterVolume(volume);
  };

  // Send chat message
  const sendMessage = async (e) => {
    e.preventDefault();
    
    if (!newMessage.trim() || !meetingId) return;
    
    try {
      const messageData = {
        content: newMessage,
        senderId: currentUser.uid,
        senderName: userProfiles[currentUser.uid]?.displayName || currentUser.email,
        timestamp: Timestamp.now(),
        type: "chat"
      };
      
      await addDoc(collection(db, "meetings", meetingId, "messages"), messageData);
      setNewMessage('');
      
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  // Format timestamp for messages
  const formatTime = (date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format timestamp for meeting list
  const formatDateTime = (date) => {
    return date.toLocaleString([], { 
      month: 'short', 
      day: 'numeric', 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  // Cancel meeting preparation
  const cancelMeetingPreparation = () => {
    setShowCameraModal(false);
    // Stop camera preview if it's active
    if (previewVideoRef.current && previewVideoRef.current.srcObject) {
      const stream = previewVideoRef.current.srcObject;
      stream.getTracks().forEach(track => track.stop());
      previewVideoRef.current.srcObject = null;
    }
  };

  // Reference audio element for volume control
  const setAudioRef = (userId, element) => {
    if (element) {
      audioElements.current[userId] = element;
    }
  };

  // For add participant modal
  const [selectedNewUsers, setSelectedNewUsers] = useState([]);

  const toggleNewUserSelection = (userId) => {
    if (selectedNewUsers.includes(userId)) {
      setSelectedNewUsers(selectedNewUsers.filter(id => id !== userId));
    } else {
      setSelectedNewUsers([...selectedNewUsers, userId]);
    }
  };

  const handleAddParticipants = () => {
    if (selectedNewUsers.length > 0) {
      addParticipantsToMeeting(selectedNewUsers);
      setSelectedNewUsers([]);
    } else {
      alert("Please select at least one participant to add.");
    }
  };

  // Copy meeting ID to clipboard for sharing
  const copyMeetingLink = () => {
    if (!meetingId) return;
    
    const meetingLink = `${window.location.origin}/team-meeting/${meetingId}`;
    navigator.clipboard.writeText(meetingLink)
      .then(() => {
        alert("Meeting link copied to clipboard! Share this with others to invite them.");
      })
      .catch((err) => {
        console.error("Error copying link:", err);
        // Fallback for browsers that don't support clipboard API
        alert(`Share this link with others to invite them: ${meetingLink}`);
      });
  };
  
  // Format time for join requests
  const formatTimeAgo = (date) => {
    if (!date) return '';
    
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    
    if (diffSec < 60) {
      return 'just now';
    } else if (diffMin < 60) {
      return `${diffMin} min${diffMin > 1 ? 's' : ''} ago`;
    } else if (diffHour < 24) {
      return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">
            {meetingActive ? `Meeting: ${meetingName}` : 'Team Meeting'}
          </h1>
          <div className="flex gap-2">
            {/* Join Requests Badge */}
            {pendingJoinRequests.length > 0 && (
              <button
                onClick={() => setShowJoinRequestsModal(true)}
                className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md flex items-center shadow-md"
              >
                <span className="relative flex h-3 w-3 mr-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                </span>
                <span>Join Requests ({pendingJoinRequests.length})</span>
              </button>
            )}
            
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-gray-700 hover:bg-gray-600 text-gray-200 font-medium py-2 px-4 rounded-md flex items-center shadow-md"
            >
              <span>← Back to Dashboard</span>
            </button>
          </div>
        </div>
        
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        )}
        
        {!loading && !meetingActive && showParticipantSelector && (
          <div className="bg-gray-800 rounded-lg p-6 shadow-md border border-gray-700">
            {/* Tabs for meeting options */}
            <div className="flex space-x-1 mb-6 bg-gray-700 p-1 rounded-md">
              <button
                onClick={() => setMeetingView('create')}
                className={`flex-1 py-2 px-4 rounded-md ${
                  meetingView === 'create' ? 'bg-blue-600 text-white' : 'hover:bg-gray-600 text-gray-300'
                }`}
              >
                Create Meeting
              </button>
              <button
                onClick={() => {
                  setMeetingView('list');
                  fetchActiveMeetings();
                }}
                className={`flex-1 py-2 px-4 rounded-md ${
                  meetingView === 'list' ? 'bg-blue-600 text-white' : 'hover:bg-gray-600 text-gray-300'
                }`}
              >
                Active Meetings
              </button>
            </div>
            
            {/* Create Meeting View */}
            {meetingView === 'create' && (
              <>
                <div className="mb-6">
                  <label className="block text-gray-300 text-sm font-bold mb-2">Meeting Name</label>
                  <input
                    type="text"
                    value={meetingName}
                    onChange={(e) => setMeetingName(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white shadow-inner focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter meeting name..."
                    required
                  />
                </div>
                
                <h2 className="text-xl font-semibold mb-4">Select Participants</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-6">
                  {users.map((user) => (
                    <div 
                      key={user.id} 
                      className={`flex items-center p-3 rounded-lg cursor-pointer border ${
                        user.selected 
                          ? 'bg-blue-900 bg-opacity-50 border-blue-500' 
                          : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                      }`}
                      onClick={() => user.id !== currentUser.uid && toggleUserSelection(user.id)}
                    >
                      <div className="mr-3">
                        <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center text-xl font-bold">
                          {userProfiles[user.id]?.displayName?.charAt(0).toUpperCase() || user.email.charAt(0).toUpperCase()}
                        </div>
                      </div>
                      <div className="flex-grow">
                        <div className="font-medium">
                          {userProfiles[user.id]?.displayName || user.email.split('@')[0]}
                        </div>
                        <div className="text-sm text-gray-400">{user.email}</div>
                      </div>
                      <div>
                        <input
                          type="checkbox"
                          checked={user.selected}
                          onChange={() => user.id !== currentUser.uid && toggleUserSelection(user.id)}
                          className={`h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500 ${
                            user.id === currentUser.uid ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                          disabled={user.id === currentUser.uid}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-end">
                  <button
                    onClick={prepareStartMeeting}
                    disabled={selectedUsers.length === 0 || !meetingName.trim()}
                    className={`px-6 py-2 rounded-md font-medium flex items-center ${
                      selectedUsers.length === 0 || !meetingName.trim()
                        ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700 text-white'
                    }`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    Start Meeting ({selectedUsers.length} participant{selectedUsers.length !== 1 ? 's' : ''})
                  </button>
                </div>
              </>
            )}
            
            {/* Active Meetings List View */}
            {meetingView === 'list' && (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Active Meetings</h2>
                  <button
                    onClick={fetchActiveMeetings}
                    className="text-blue-400 hover:text-blue-300 flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Refresh
                  </button>
                </div>
                
                {activeMeetings.length === 0 ? (
                  <div className="text-center py-10 bg-gray-700 rounded-lg border border-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-gray-400">No active meetings found</p>
                    <button
                      onClick={() => setMeetingView('create')}
                      className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
                    >
                      Create a New Meeting
                    </button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {activeMeetings.map(meeting => {
                      const isCreator = meeting.createdBy === currentUser.uid;
                      const isParticipant = meeting.participants.includes(currentUser.uid);
                      const creatorName = userProfiles[meeting.createdBy]?.displayName || 
                                        users.find(u => u.id === meeting.createdBy)?.email?.split('@')[0] || 
                                        'Unknown user';
                      const canJoin = isCreator || isParticipant || isAdmin;
                      const hasRequestedJoin = joinRequestSent[meeting.id] != null;
                      
                      return (
                        <div 
                          key={meeting.id} 
                          className="bg-gray-700 rounded-lg p-4 border border-gray-600 hover:border-blue-500 transition-colors"
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-semibold text-lg">{meeting.name}</h3>
                              <p className="text-gray-400 text-sm">
                                Started by {creatorName} • {formatDateTime(meeting.createdAt)}
                              </p>
                              <div className="mt-2 flex items-center text-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                {meeting.participants.length} participant{meeting.participants.length !== 1 ? 's' : ''}
                              </div>
                              <div className="mt-1 flex flex-wrap gap-1">
                                {isCreator && (
                                  <span className="inline-block bg-purple-900 text-purple-200 text-xs px-2 py-1 rounded-full">
                                    Your Meeting
                                  </span>
                                )}
                                {isParticipant && !isCreator && (
                                  <span className="inline-block bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded-full">
                                    Invited
                                  </span>
                                )}
                                {hasRequestedJoin && (
                                  <span className="inline-block bg-yellow-900 text-yellow-200 text-xs px-2 py-1 rounded-full">
                                    Request Pending
                                  </span>
                                )}
                              </div>
                            </div>
                            
                            <div className="flex flex-col gap-2">
                              {canJoin ? (
                                <a
                                  href={`/team-meeting/${meeting.id}`}
                                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center justify-center"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                  </svg>
                                  Join
                                </a>
                              ) : (
                                <button
                                  onClick={() => sendJoinRequest(meeting.id, meeting.name, meeting.createdBy)}
                                  disabled={hasRequestedJoin}
                                  className={`px-4 py-2 rounded-md flex items-center justify-center ${
                                    hasRequestedJoin 
                                      ? 'bg-gray-600 text-gray-300 cursor-not-allowed' 
                                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                                  }`}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                                  </svg>
                                  {hasRequestedJoin ? 'Request Sent' : 'Request to Join'}
                                </button>
                              )}
                              
                              {/* Admin delete button */}
                              {(isAdmin || isCreator) && (
                                <button
                                  onClick={() => setDeleteConfirmMeetingId(meeting.id)}
                                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md flex items-center justify-center"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                  Delete
                                </button>
                              )}
                            </div>
                          </div>
                          <div className="mt-3 flex items-center text-xs text-gray-400">
                            <span className="truncate">ID: {meeting.id}</span>
                            <button
                              onClick={() => {
                                navigator.clipboard.writeText(`${window.location.origin}/team-meeting/${meeting.id}`);
                                alert("Meeting link copied to clipboard!");
                              }}
                              className="ml-2 text-blue-400 hover:text-blue-300"
                              title="Copy meeting link"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            )}
          </div>
        )}
        
        {/* Join Requests Modal */}
        {showJoinRequestsModal && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 max-w-xl w-full border border-gray-700 shadow-xl">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">Pending Join Requests</h2>
                <button
                  onClick={() => setShowJoinRequestsModal(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              {pendingJoinRequests.length === 0 ? (
                <div className="text-center py-8 text-gray-400">
                  No pending join requests
                </div>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {pendingJoinRequests.map(request => (
                    <div key={request.id} className="bg-gray-700 rounded-lg p-4 border border-gray-600">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-lg font-medium">{request.requesterName}</p>
                          <p className="text-sm text-gray-400">
                            Requested to join "{request.meetingName}" {formatTimeAgo(request.requestedAt)}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={() => approveJoinRequest(request.id, request.meetingId, request.requesterId)}
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-sm"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => denyJoinRequest(request.id)}
                            className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-md text-sm"
                          >
                            Deny
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Delete Meeting Confirmation Modal */}
        {deleteConfirmMeetingId && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700 shadow-xl">
              <h2 className="text-xl font-bold mb-4">Confirm Delete</h2>
              <p className="text-gray-300 mb-6">
                Are you sure you want to delete this meeting? This action cannot be undone.
              </p>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setDeleteConfirmMeetingId(null)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-md"
                >
                  Cancel
                </button>
                <button
                  onClick={() => deleteMeeting(deleteConfirmMeetingId)}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-md flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Delete Meeting
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* Camera Preference Modal */}
        {showCameraModal && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700 shadow-xl">
              <h2 className="text-xl font-bold mb-4">Join Meeting Options</h2>
              <p className="text-gray-300 mb-6">How would you like to join the meeting?</p>
              
<div className="space-y-4 mb-6">
                {hasCamera && (
                  <div 
                    className={`flex items-center p-4 border rounded-lg cursor-pointer ${
                      cameraPreference === "with-camera" 
                        ? "border-blue-500 bg-blue-900 bg-opacity-20" 
                        : "border-gray-600 hover:bg-gray-700"
                    }`}
                    onClick={() => setCameraPreference("with-camera")}
                  >
                    <div className="bg-blue-600 p-2 rounded-full mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">Join with camera</h3>
                      <p className="text-gray-400 text-sm">Other participants will be able to see you</p>
                    </div>
                    <input 
                      type="radio" 
                      name="camera-preference" 
                      className="ml-auto" 
                      checked={cameraPreference === "with-camera"}
                      onChange={() => setCameraPreference("with-camera")}
                    />
                  </div>
                )}
                
                <div 
                  className={`flex items-center p-4 border rounded-lg cursor-pointer ${
                    cameraPreference === "no-camera" 
                      ? "border-blue-500 bg-blue-900 bg-opacity-20" 
                      : "border-gray-600 hover:bg-gray-700"
                  }`}
                  onClick={() => setCameraPreference("no-camera")}
                >
                  <div className="bg-purple-600 p-2 rounded-full mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium">Audio only</h3>
                    <p className="text-gray-400 text-sm">Join with microphone, no camera</p>
                  </div>
                  <input 
                    type="radio" 
                    name="camera-preference" 
                    className="ml-auto" 
                    checked={cameraPreference === "no-camera"}
                    onChange={() => setCameraPreference("no-camera")}
                  />
                </div>
              </div>
              
              {/* Camera Preview */}
              {cameraPreference === "with-camera" && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-300 mb-2">Camera Preview:</h3>
                  <div className="relative bg-black rounded-lg overflow-hidden aspect-video flex items-center justify-center">
                    <video
                      ref={previewVideoRef}
                      autoPlay
                      muted
                      playsInline
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40">
                      <p className="text-white text-sm">Camera preview</p>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelMeetingPreparation}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-md"
                >
                  Cancel
                </button>
                <button
                  onClick={meetingId ? handleJoinMeeting : startMeeting}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {meetingId ? "Join Meeting" : "Start Meeting"}
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* Add Participant Modal */}
        {showAddParticipantModal && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 max-w-xl w-full border border-gray-700 shadow-xl">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">Add Participants</h2>
                <button
                  onClick={() => {
                    setShowAddParticipantModal(false);
                    setSelectedNewUsers([]);
                  }}
                  className="text-gray-400 hover:text-white"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              {availableUsers.length === 0 ? (
                <div className="text-center py-6 text-gray-400">
                  All users are already in the meeting
                </div>
              ) : (
                <div className="max-h-96 overflow-y-auto mb-4">
                  <div className="grid gap-2">
                    {availableUsers.map(user => (
                      <div 
                        key={user.id} 
                        className={`flex items-center p-3 rounded-lg cursor-pointer border ${
                          selectedNewUsers.includes(user.id) 
                            ? 'bg-blue-900 bg-opacity-50 border-blue-500' 
                            : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                        }`}
                        onClick={() => toggleNewUserSelection(user.id)}
                      >
                        <div className="mr-3">
                          <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center text-xl font-bold">
                            {userProfiles[user.id]?.displayName?.charAt(0).toUpperCase() || user.email.charAt(0).toUpperCase()}
                          </div>
                        </div>
                        <div className="flex-grow">
                          <div className="font-medium">
                            {userProfiles[user.id]?.displayName || user.email.split('@')[0]}
                          </div>
                          <div className="text-sm text-gray-400">{user.email}</div>
                        </div>
                        <div>
                          <input
                            type="checkbox"
                            checked={selectedNewUsers.includes(user.id)}
                            onChange={() => toggleNewUserSelection(user.id)}
                            className="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex justify-end">
                <button
                  onClick={handleAddParticipants}
                  disabled={selectedNewUsers.length === 0}
                  className={`px-4 py-2 rounded-md ${
                    selectedNewUsers.length === 0
                      ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`}
                >
                  Add Selected ({selectedNewUsers.length})
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* Manage Participants Modal */}
        {showManageParticipantsModal && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 max-w-xl w-full border border-gray-700 shadow-xl">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">Manage Participants</h2>
                <button
                  onClick={() => setShowManageParticipantsModal(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="max-h-96 overflow-y-auto mb-4">
                <div className="grid gap-2">
                  {selectedUsers.map(userId => {
                    const user = users.find(u => u.id === userId);
                    const isCreator = userId === meetingCreator;
                    
                    return (
                      <div 
                        key={userId} 
                        className="flex items-center p-3 rounded-lg border border-gray-600 bg-gray-700"
                      >
                        <div className="mr-3">
                          <div className={`h-10 w-10 rounded-full ${isCreator ? 'bg-purple-600' : 'bg-blue-600'} flex items-center justify-center text-xl font-bold`}>
                            {userProfiles[userId]?.displayName?.charAt(0).toUpperCase() || user?.email?.charAt(0).toUpperCase()}
                          </div>
                        </div>
                        <div className="flex-grow">
                          <div className="font-medium">
                            {userProfiles[userId]?.displayName || user?.email?.split('@')[0] || 'Unknown user'}
                            {isCreator && <span className="ml-2 text-xs bg-purple-900 text-purple-200 px-2 py-0.5 rounded-full">Creator</span>}
                            {userId === currentUser.uid && <span className="ml-2 text-xs bg-blue-900 text-blue-200 px-2 py-0.5 rounded-full">You</span>}
                          </div>
                          <div className="text-sm text-gray-400">{user?.email}</div>
                        </div>
                        {!isCreator && meetingCreator === currentUser.uid && (
                          <button
                            onClick={() => removeParticipantFromMeeting(userId)}
                            className="text-red-400 hover:text-red-300 p-1"
                            title="Remove from meeting"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
              
              <div className="flex justify-between">
                <button
                  onClick={() => {
                    setShowManageParticipantsModal(false);
                    setShowAddParticipantModal(true);
                  }}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white flex items-center"
                  disabled={meetingCreator !== currentUser.uid}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Add More
                </button>
                <button
                  onClick={() => setShowManageParticipantsModal(false)}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-500 rounded-md"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
        
        {meetingActive && (
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Video Grid */}
            <div className="lg:w-8/12 bg-gray-800 rounded-lg p-4 border border-gray-700">
              <div className="mb-4 flex flex-wrap justify-between items-center gap-2">
                <div className="flex items-center">
                  <h2 className="text-xl font-semibold">{meetingName}</h2>
                  {/* Copy meeting link button */}
                  <button
                    onClick={copyMeetingLink}
                    className="ml-2 text-gray-400 hover:text-blue-400 p-1 rounded-full hover:bg-gray-700 transition-colors"
                    title="Copy meeting link to invite others"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                  </button>
                </div>
                
                <div className="flex flex-wrap gap-2 items-center">
                  {/* Audio Controls */}
                  <div className="flex items-center bg-gray-700 rounded-md px-3 py-1 mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-300 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                    </svg>
                    <input 
                      type="range" 
                      min="0" 
                      max="100" 
                      value={masterVolume}
                      onChange={(e) => handleMasterVolumeChange(parseInt(e.target.value))}
                      className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                      title="Master Volume"
                    />
                  </div>
                  
                  {/* Join Requests Badge (in meeting) */}
                  {pendingJoinRequests.length > 0 && meetingCreator === currentUser.uid && (
                    <button
                      onClick={() => setShowJoinRequestsModal(true)}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-full flex items-center"
                    >
                      <span className="relative flex h-2 w-2 mr-1">
                        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
                      </span>
                      <span className="text-xs font-medium">{pendingJoinRequests.length}</span>
                    </button>
                  )}
                  
                  {/* Participant Management (only for creator) */}
                  {meetingCreator === currentUser.uid && (
                    <button
                      onClick={() => setShowManageParticipantsModal(true)}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md flex items-center"
                      title="Manage participants"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      <span className="hidden sm:inline">Participants</span>
                    </button>
                  )}
                  
                  {/* Video Toggle */}
                  <button
                    onClick={toggleVideo}
                    className={`p-2 rounded-full ${
                      localVideoEnabled ? 'bg-gray-700 hover:bg-gray-600' : 'bg-red-700 hover:bg-red-600'
                    }`}
                    title={localVideoEnabled ? "Turn off camera" : "Turn on camera"}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      {localVideoEnabled ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                      )}
                    </svg>
                  </button>
                  
                  {/* Audio Toggle */}
                  <button
                    onClick={toggleAudio}
                    className={`p-2 rounded-full ${
                      localAudioEnabled ? 'bg-gray-700 hover:bg-gray-600' : 'bg-red-700 hover:bg-red-600'
                    }`}
                    title={localAudioEnabled ? "Mute microphone" : "Unmute microphone"}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      {localAudioEnabled ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                      ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" strokeDasharray="2" />
                      )}
                    </svg>
                  </button>
                  
                  {/* End/Leave Meeting Button */}
                  {meetingCreator === currentUser.uid ? (
                    <button
                      onClick={endMeeting}
                      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md"
                    >
                      End Meeting
                    </button>
                  ) : (
                    <button
                      onClick={leaveMeeting}
                      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md"
                    >
                      Leave
                    </button>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Local user video - always show first */}
                <div className="aspect-video bg-gray-900 rounded-lg overflow-hidden relative border border-blue-500">
                  <video
                    ref={localVideoRef}
                    autoPlay
                    muted
                    playsInline
                    className={`w-full h-full object-cover ${!localVideoEnabled ? 'hidden' : ''}`}
                  />
                  
                  {!localVideoEnabled && (
                    <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-800">
                      <div className="h-20 w-20 rounded-full bg-blue-600 flex items-center justify-center text-2xl font-bold mb-2">
                        {userProfiles[currentUser.uid]?.displayName?.charAt(0).toUpperCase() || currentUser.email.charAt(0).toUpperCase()}
                      </div>
                      <div className="text-center">
                        <div className="font-medium">
                          {userProfiles[currentUser.uid]?.displayName || currentUser.email.split('@')[0]}
                        </div>
                        <div className="text-xs text-gray-400">Camera off</div>
                      </div>
                    </div>
                  )}
                  
                  <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 px-2 py-1 rounded text-sm flex items-center">
                    <div className="mr-2 w-2 h-2 rounded-full bg-green-500"></div>
                    {userProfiles[currentUser.uid]?.displayName || currentUser.email.split('@')[0]} (You)
                    {!localAudioEnabled && (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" strokeDasharray="2" />
                      </svg>
                    )}
                  </div>
                </div>
                
                {/* Remote participants - would connect to real video streams in a complete implementation */}
                {selectedUsers
                  .filter(userId => userId !== currentUser.uid)
                  .map((userId) => {
                    const user = users.find(u => u.id === userId);
                    const isActive = activeParticipants.includes(userId);
                    const isCreator = userId === meetingCreator;
                    
                    return (
                      <div 
                        key={userId} 
                        className={`aspect-video bg-gray-900 rounded-lg overflow-hidden relative ${
                          isActive ? (isCreator ? 'border-2 border-purple-500' : 'border border-green-500') : 'border border-gray-700'
                        }`}
                      >
                        {/* In a real implementation, this would be the remote video stream */}
                        <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-800">
                          <div className={`h-20 w-20 rounded-full ${isCreator ? 'bg-purple-600' : 'bg-blue-600'} flex items-center justify-center text-2xl font-bold mb-2`}>
                            {userProfiles[userId]?.displayName?.charAt(0).toUpperCase() || user?.email?.charAt(0).toUpperCase()}
                          </div>
                          <div className="text-center">
                            <div className="font-medium">
                              {userProfiles[userId]?.displayName || user?.email?.split('@')[0]}
                              {isCreator && <span className="ml-2 text-xs bg-purple-900 text-purple-200 px-1 rounded">Host</span>}
                            </div>
                            <div className="text-xs text-gray-400">
                              {isActive ? "Camera off" : "Not connected"}
                            </div>
                          </div>
                        </div>
                        
                        <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 px-2 py-1 rounded text-sm flex items-center">
                          <div className={`mr-2 w-2 h-2 rounded-full ${isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
                          {userProfiles[userId]?.displayName || user?.email?.split('@')[0]}
                          
                          {/* Mute indicator - randomly show for demonstration purposes */}
                          {isActive && Math.random() > 0.5 && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" strokeDasharray="2" />
                            </svg>
                          )}
                        </div>
                        
                        {/* Volume control */}
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 px-2 py-1 rounded flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                          </svg>
                          <input 
                            type="range" 
                            min="0" 
                            max="100" 
                            value={participantVolumes[userId] || 100}
                            onChange={(e) => handleVolumeChange(userId, parseInt(e.target.value))}
                            className="w-16 h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                          />
                        </div>
                        
                        {/* Add hidden audio element for volume control */}
                        <audio 
                          ref={(el) => setAudioRef(userId, el)}
                          autoPlay 
                          className="hidden"
                        />
                      </div>
                    );
                  })
                }
              </div>
            </div>
            
            {/* Chat Panel */}
            <div className="lg:w-4/12 bg-gray-800 rounded-lg border border-gray-700 flex flex-col">
              <div className="p-4 border-b border-gray-700">
                <h2 className="text-xl font-semibold">Meeting Chat</h2>
              </div>
              
              <div className="flex-grow p-4 overflow-y-auto max-h-96 lg:max-h-[calc(100vh-16rem)]">
                {messages.length === 0 ? (
                  <div className="text-center text-gray-500 my-8">
                    No messages yet. Start the conversation!
                  </div>
                ) : (
                  <div className="space-y-3">
                    {messages.map((message) => (
                      <div key={message.id} className={`${
                        message.type === 'system' 
                          ? 'bg-gray-700 bg-opacity-50 text-center py-1 px-3 rounded text-gray-400 text-sm'
                          : message.senderId === currentUser.uid
                            ? 'bg-blue-900 bg-opacity-50 ml-8 rounded-lg p-3'
                            : 'bg-gray-700 mr-8 rounded-lg p-3'
                      }`}>
                        {message.type !== 'system' && (
                          <div className="flex justify-between items-center mb-1">
                            <span className="font-medium text-sm">
                              {message.senderId === currentUser.uid 
                                ? 'You' 
                                : message.senderName}
                            </span>
                            <span className="text-xs text-gray-400">
                              {formatTime(message.timestamp)}
                            </span>
                          </div>
                        )}
                        <div className={message.type === 'system' ? '' : 'text-sm'}>
                          {message.content}
                        </div>
                      </div>
                    ))}
                    <div ref={messagesEndRef} />
                  </div>
                )}
              </div>
              
              <div className="p-4 border-t border-gray-700">
                <form onSubmit={sendMessage} className="flex">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type a message..."
                    className="flex-grow px-3 py-2 bg-gray-700 border border-gray-600 rounded-l-md text-white shadow-inner focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <button
                    type="submit"
                    disabled={!newMessage.trim()}
                    className={`px-4 py-2 rounded-r-md ${
                      !newMessage.trim() 
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </button>
                </form>
              </div>
              
              <div className="p-4 border-t border-gray-700">
                <h3 className="font-medium mb-2">Participants ({activeParticipants.length}/{selectedUsers.length})</h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {selectedUsers.map((userId) => {
                    const user = users.find(u => u.id === userId);
                    const isActive = activeParticipants.includes(userId) || userId === currentUser.uid;
                    const isCreator = userId === meetingCreator;
                    
                    return (
                      <div key={userId} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className={`h-2 w-2 rounded-full mr-2 ${
                            isActive ? 'bg-green-500' : 'bg-red-500'
                          }`}></div>
                          <span>
                            {userProfiles[userId]?.displayName || user?.email?.split('@')[0]}
                            {userId === currentUser.uid && ' (You)'}
                            {isCreator && (
                              <span className="ml-1 text-xs text-purple-400">(Host)</span>
                            )}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-xs text-gray-400 mr-2">
                            {isActive ? 'Connected' : 'Offline'}
                          </span>
                          
                          {/* Only allow host to remove participants, and can't remove self or other hosts */}
                          {meetingCreator === currentUser.uid && userId !== currentUser.uid && !isCreator && (
                            <button
                              onClick={() => removeParticipantFromMeeting(userId)}
                              className="text-red-400 hover:text-red-300 p-1"
                              title="Remove from meeting"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          )}
                          
                          {/* Volume control for each participant in the sidebar */}
                          <div className="relative ml-1 group">
                            <button className="text-gray-400 hover:text-gray-300 p-1">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                              </svg>
                            </button>
                            <div className="hidden group-hover:block absolute right-0 top-0 -mt-12 bg-gray-900 border border-gray-700 rounded-md p-2 shadow-lg z-10">
                              <input 
                                type="range" 
                                min="0" 
                                max="100" 
                                value={participantVolumes[userId] || 100}
                                onChange={(e) => handleVolumeChange(userId, parseInt(e.target.value))}
                                className="w-16 h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                
                {/* Add participant button (only for creator) */}
                {meetingCreator === currentUser.uid && (
                  <button 
                    onClick={() => setShowAddParticipantModal(true)}
                    className="mt-4 w-full py-2 text-sm bg-blue-600 hover:bg-blue-700 rounded-md flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Add Participants
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default TeamMeeting;