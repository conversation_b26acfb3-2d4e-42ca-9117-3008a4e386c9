import React, { useState } from 'react';
import { 
  STATUS_OPTIONS, 
  COLORS_MAP, 
  calculateDaysPastDue, 
  formatDate, 
  processAddresses,
  getFallbackImageUrl
} from './utility-functions';
import { 
  StatusBadge, 
  ConfirmationDialog, 
  VehicleRender 
} from './ui-components';
import AddressComponent from './AddressComponent';
import OrderEditForm from './OrderEditForm';

const OrderCard = ({ order, onStatusChange, onDeleteOrder, onUpdateOrder, currentUser }) => {
  const [expanded, setExpanded] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [showSecureConfirmation, setShowSecureConfirmation] = useState(false);
  const [showUnsecureConfirmation, setShowUnsecureConfirmation] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isGeocodingInProgress, setIsGeocodingInProgress] = useState(false);
  const [selectedViewIndex, setSelectedViewIndex] = useState(0);
  
  const statusStyle = STATUS_OPTIONS[order.status] || STATUS_OPTIONS['open'];
  const secureClass = order.secure ? 'bg-green-900/20 border-green-700/50' : 'bg-red-900/20 border-red-700/50';
  
  // Calculate days past due for display
  const daysPastDue = order.dueDate ? calculateDaysPastDue(order.dueDate) : 0;
  
  // Get vehicle render views or create a default set
  const getVehicleRenderViews = () => {
    if (order.vehicleRenderViews && order.vehicleRenderViews.length > 0) {
      return order.vehicleRenderViews;
    }
    
    // Generate a single view if no render views are available
    if (order.vehicleImage) {
      return [{
        url: order.vehicleImage,
        angle: '1',
        colorName: order.color || 'Unknown',
        colorCode: order.color ? COLORS_MAP[order.color] || order.color.toLowerCase() : 'white'
      }];
    }
    
    return [];
  };
  
  // Handle view switching
  const handleSwitchView = (index) => {
    const views = getVehicleRenderViews();
    if (views[index]) {
      setSelectedViewIndex(index);
    }
  };
  
  // Handle confirmation of deletion
  const handleConfirmDelete = () => {
    onDeleteOrder(order.id);
    setShowDeleteConfirmation(false);
  };
  
  // Handle confirmation of secure status change
  const handleConfirmSecure = () => {
    onStatusChange(order.id, true);
    setShowSecureConfirmation(false);
  };
  
  // Handle confirmation of unsecure status change
  const handleConfirmUnsecure = () => {
    onStatusChange(order.id, false);
    setShowUnsecureConfirmation(false);
  };
  
  // Handle geocode addresses button
  const handleGeocodeAllAddresses = async () => {
    if (!order.addresses || order.addresses.length === 0) return;
    
    setIsGeocodingInProgress(true);
    
    try {
      // Process addresses to make sure they're in the right format
      const addresses = processAddresses(order.addresses);
      
      // Geocode each address that doesn't have coordinates
      const geocodedAddresses = await Promise.all(
        addresses.map(async (addr) => {
          if (addr.street && (!addr.position || !addr.position.lat || !addr.position.lng)) {
            return await geocodeAddress(addr);
          }
          return addr;
        })
      );
      
      // Determine the main order position from the first valid address
      const firstValidAddress = geocodedAddresses.find(addr => addr.position?.lat && addr.position?.lng);
      let position = null; // Don't use default position
      
      // Only set position if we have valid coordinates from an address
      if (firstValidAddress?.position) {
        position = firstValidAddress.position;
      }
      
      // Update the order with geocoded addresses and position
      onUpdateOrder(order.id, { 
        addresses: geocodedAddresses,
        position
      });
      
      setIsGeocodingInProgress(false);
    } catch (error) {
      console.error("Error geocoding addresses:", error);
      setIsGeocodingInProgress(false);
    }
  };
  
  // Handle address change
  const handleAddressChange = async (index, newAddress, forceGeocode = false) => {
    try {
      const addresses = processAddresses(order.addresses || []);
      
      if (forceGeocode || !newAddress.position) {
        setIsGeocodingInProgress(true);
        
        // Geocode the address
        const geocodedAddress = await geocodeAddress(newAddress);
        addresses[index] = geocodedAddress;
        
        // Check if any addresses have valid coordinates
        const validAddresses = addresses.filter(addr => addr.position?.lat && addr.position?.lng);
        
        // If we have valid coordinates, use the first one for the order position
        let position = null;
        if (validAddresses.length > 0) {
          position = validAddresses[0].position;
        }
        
        // Update the order with geocoded addresses and potentially a new position
        onUpdateOrder(order.id, { 
          addresses,
          position
        });
        
        setIsGeocodingInProgress(false);
      } else {
        // Just update the address without geocoding
        addresses[index] = newAddress;
        onUpdateOrder(order.id, { addresses });
      }
    } catch (error) {
      console.error("Error updating address:", error);
      setIsGeocodingInProgress(false);
      
      // Still update the address even if geocoding fails
      const addresses = processAddresses(order.addresses || []);
      addresses[index] = newAddress;
      onUpdateOrder(order.id, { addresses });
    }
  };
  
  // Handle adding a new address
  const handleAddAddress = () => {
    const addresses = processAddresses(order.addresses || []);
    addresses.push({
      street: '',
      city: '',
      state: '',
      zip: '',
      checkIns: []
    });
    onUpdateOrder(order.id, { addresses });
  };
  
  // Handle deleting an address
  const handleDeleteAddress = (index) => {
    const addresses = processAddresses(order.addresses || []);
    addresses.splice(index, 1);
    
    // If we deleted the address that provided position data, update position
    const validAddresses = addresses.filter(addr => addr.position?.lat && addr.position?.lng);
    let position = null;
    if (validAddresses.length > 0) {
      position = validAddresses[0].position;
    }
    
    onUpdateOrder(order.id, { 
      addresses,
      position
    });
  };
  
  // Handle address check-in
  const handleAddressCheckIn = (index) => {
    const addresses = processAddresses(order.addresses || []);
    const address = addresses[index];
    
    if (!address.checkIns) {
      address.checkIns = [];
    }
    
    // Only allow up to 6 check-ins
    if (address.checkIns.length < 6) {
      const now = new Date();
      address.checkIns.push({
        timestamp: now,
        date: now.toISOString(),
        userId: currentUser?.uid,
        userName: formatUserDisplayName(currentUser)
      });
      
      onUpdateOrder(order.id, { addresses });
    }
  };
  
  // Handle saving edited order
  const handleSaveEdit = (orderId, updatedData) => {
    onUpdateOrder(orderId, updatedData);
    setIsEditing(false);
    // Expand the card to show changes
    setExpanded(true);
  };
  
  // Check if the order has geocoded addresses
  const hasGeocodedAddresses = () => {
    if (!order.addresses || order.addresses.length === 0) return false;
    
    return order.addresses.some(addr => 
      addr.position && typeof addr.position.lat === 'number' && typeof addr.position.lng === 'number'
    );
  };
  
  // Check if any addresses need geocoding
  const needsGeocoding = () => {
    if (!order.addresses || order.addresses.length === 0) return false;
    
    return order.addresses.some(addr => 
      addr.street && (!addr.position || typeof addr.position.lat !== 'number' || typeof addr.position.lng !== 'number')
    );
  };
  
  // If in edit mode, show the edit form
  if (isEditing) {
    return (
      <OrderEditForm 
        order={order} 
        onSave={handleSaveEdit} 
        onCancel={() => setIsEditing(false)} 
      />
    );
  }
  
  // Get the render views array
  const renderViews = getVehicleRenderViews();
  const currentView = renderViews[selectedViewIndex] || (renderViews.length > 0 ? renderViews[0] : null);
  
  return (
    <div className={`mb-4 rounded-lg shadow-lg border ${secureClass} overflow-hidden transition-all duration-300 hover:shadow-xl`}>
      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog 
        isOpen={showDeleteConfirmation}
        title="Delete Order"
        message={`Are you sure you want to delete the order for ${order.year} ${order.make} ${order.model}? This action cannot be undone.`}
        onConfirm={handleConfirmDelete}
        onCancel={() => setShowDeleteConfirmation(false)}
        confirmText="Delete"
        confirmButtonClass="bg-red-600 hover:bg-red-700"
      />
      
      {/* Secure Confirmation Dialog */}
      <ConfirmationDialog 
        isOpen={showSecureConfirmation}
        title="Mark Vehicle as Secure"
        message={`Are you sure you want to mark this ${order.year} ${order.make} ${order.model} as secure?`}
        onConfirm={handleConfirmSecure}
        onCancel={() => setShowSecureConfirmation(false)}
        confirmText="Mark Secure"
        confirmButtonClass="bg-green-600 hover:bg-green-700"
      />
      
      {/* Unsecure Confirmation Dialog */}
      <ConfirmationDialog 
        isOpen={showUnsecureConfirmation}
        title="Mark Vehicle as Not Secure"
        message={`Are you sure you want to mark this ${order.year} ${order.make} ${order.model} as not secure?`}
        onConfirm={handleConfirmUnsecure}
        onCancel={() => setShowUnsecureConfirmation(false)}
        confirmText="Mark Not Secure"
        confirmButtonClass="bg-red-600 hover:bg-red-700"
      />
      
      <div className="p-4 bg-gradient-to-r from-gray-800 to-gray-900">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-bold text-lg text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">
              {order.year} {order.make} {order.model}
            </h3>
            <div className="flex items-center mt-1 space-x-4">
              <span className="text-sm text-gray-300">
                <span className="text-xs uppercase text-gray-400">License:</span> {order.licensePlate}
              </span>
              <span className="text-sm text-gray-300">
                <span className="text-xs uppercase text-gray-400">VIN:</span> {order.vin}
              </span>
            </div>
            
            {/* ✅ NEW: Team Assignment Display */}
            {(order.teamId || order.teamName) && (
              <div className="mt-2 flex items-center">
                <span className="px-3 py-1 bg-blue-900 bg-opacity-50 border border-blue-600 rounded-lg text-sm font-medium text-blue-300 flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                  </svg>
                  Team: {order.teamName || order.teamId}
                </span>
                {/* Debug info for development */}
                {process.env.NODE_ENV === 'development' && order.teamId && (
                  <span className="ml-2 text-xs text-gray-500">
                    ID: {order.teamId}
                  </span>
                )}
              </div>
            )}
            
            {/* Show warning if no team assigned */}
            {!order.teamId && !order.teamName && (
              <div className="mt-2 flex items-center">
                <span className="px-3 py-1 bg-yellow-900 bg-opacity-50 border border-yellow-600 rounded-lg text-sm font-medium text-yellow-300 flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  No Team Assigned
                </span>
              </div>
            )}
            
            {/* Customer Name */}
            {order.customerName && (
              <div className="mt-1 text-sm text-gray-300">
                <span className="text-xs uppercase text-gray-400">Customer:</span> {order.customerName}
              </div>
            )}
            
            {/* Color indicator */}
            {order.color && (
              <div className="mt-1 flex items-center text-sm text-gray-300">
                <span className="text-xs uppercase text-gray-400 mr-1">Color:</span>
                <div className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: COLORS_MAP[order.color] || order.color.toLowerCase() }}></div>
                {order.color}
              </div>
            )}
            
            {/* GPS Data Indicator */}
            <div className="mt-1">
              {hasGeocodedAddresses() ? (
                <span className="px-2 py-1 bg-green-900/20 border border-green-700/40 rounded text-xs font-medium text-green-400 flex items-center w-fit">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  Address GPS Available
                </span>
              ) : (
                <span className="px-2 py-1 bg-yellow-900/20 border border-yellow-700/40 rounded text-xs font-medium text-yellow-400 flex items-center w-fit">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  No Address GPS
                </span>
              )}
            </div>
            
            {/* Days Past Due */}
            {daysPastDue > 0 && (
              <div className="mt-1">
                <span className={`px-2 py-1 rounded text-xs font-medium ${daysPastDue > 30 ? 'bg-red-600' : daysPastDue > 14 ? 'bg-orange-600' : 'bg-yellow-600'} text-white`}>
                  {daysPastDue} {daysPastDue === 1 ? 'Day' : 'Days'} Past Due
                </span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            <StatusBadge status={order.status} />
            
            {/* Edit Button */}
            <button 
              onClick={() => setIsEditing(true)} 
              className="text-gray-300 hover:text-blue-400 transition-colors duration-200"
              title="Edit Order"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
            </button>
            
            {/* Delete Button */}
            <button 
              onClick={() => setShowDeleteConfirmation(true)} 
              className="text-gray-300 hover:text-red-400 transition-colors duration-200"
              title="Delete Order"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </button>
            
            {/* Secure/Unsecure buttons */}
            <div className="flex">
              <button 
                onClick={() => setShowUnsecureConfirmation(true)}
                className={`px-3 py-2 flex items-center justify-center rounded-l overflow-hidden text-sm font-medium transition-colors duration-200 ${!order.secure ? 'bg-red-600 text-white' : 'bg-gray-700 hover:bg-red-600 text-white'}`}
                title="Mark vehicle as not secure"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                Not Secure
              </button>
              <button 
                onClick={() => setShowSecureConfirmation(true)}
                className={`px-3 py-2 flex items-center justify-center rounded-r overflow-hidden text-sm font-medium transition-colors duration-200 ${order.secure ? 'bg-green-600 text-white' : 'bg-gray-700 hover:bg-green-600 text-white'}`}
                title="Mark vehicle as secure"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Secure
              </button>
            </div>
            
            <button 
              onClick={() => setExpanded(!expanded)} 
              className="text-gray-300 hover:text-white transition-colors duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 transition-transform duration-300 ${expanded ? 'transform rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
        
        <div className="flex items-center justify-between mt-2 text-xs text-gray-400">
          <div>
            <span className="mr-4">Created: {formatDate(order.createdAt)}</span>
            {order.updatedAt && <span>Updated: {formatDate(order.updatedAt)}</span>}
          </div>
          <div>
            <span className="px-2 py-1 bg-gray-800 rounded">Case #{order.caseNumber}</span>
          </div>
        </div>
        
        {/* Always show vehicle render */}
        <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-1">
            {/* 3D Render */}
            <VehicleRender 
              vehicleImageUrl={currentView?.url}
              renderedViews={renderViews}
              make={order.make}
              model={order.model}
              year={order.year}
              color={order.color}
              selectedViewIndex={selectedViewIndex}
              onSwitchView={handleSwitchView}
              getFallbackImageUrl={getFallbackImageUrl}
            />
          </div>
          
          <div className="md:col-span-2">
            {/* Always show addresses */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm text-blue-300">Vehicle Addresses</h4>
                {/* Geocode All Addresses Button */}
                {needsGeocoding() && (
                  <button
                    onClick={handleGeocodeAllAddresses}
                    disabled={isGeocodingInProgress}
                    className={`text-xs px-2 py-1 rounded flex items-center ${isGeocodingInProgress ? 'bg-gray-700 text-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'}`}
                  >
                    {isGeocodingInProgress ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-1 h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Geocoding...
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                        </svg>
                        Geocode All
                      </>
                    )}
                  </button>
                )}
              </div>
              
              <AddressComponent 
                addresses={processAddresses(order.addresses || [])}
                onAddressChange={handleAddressChange}
                onAddressAdd={handleAddAddress}
                onAddressDelete={handleDeleteAddress}
                onAddressCheckIn={handleAddressCheckIn}
                readOnly={!expanded}
                isGeocodingInProgress={isGeocodingInProgress}
              />
            </div>
          </div>
        </div>
        
        {/* Display secure timestamp and user who secured if available */}
        {order.secureTimestamp && (
          <div className="mt-2 px-2 py-1 bg-green-900/30 border border-green-700/50 rounded text-xs text-green-300">
            <div className="mb-1">
              <span className="font-semibold">Secured on:</span> {formatDate(order.secureTimestamp)}
              {order.securedBy && 
                <span> by <span className="font-semibold">{order.securedBy}</span></span>
              }
            </div>
          </div>
        )}
      </div>
      
      {expanded && (
        <div className="p-4 bg-gray-800 border-t border-gray-700 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-semibold text-sm mb-2 text-blue-300">Vehicle Details</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Make</span>
                  <span className="text-white">{order.make}</span>
                </div>
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Model</span>
                  <span className="text-white">{order.model}</span>
                </div>
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Year</span>
                  <span className="text-white">{order.year}</span>
                </div>
              </div>
              <div>
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">VIN</span>
                  <span className="text-white font-mono">{order.vin}</span>
                </div>
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">License Plate</span>
                  <span className="text-white font-mono">{order.licensePlate}</span>
                </div>
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Color</span>
                  <div className="flex items-center text-white">
                    {order.color && (
                      <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: COLORS_MAP[order.color] || order.color.toLowerCase() }}></div>
                    )}
                    {order.color || 'N/A'}
                  </div>
                </div>
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Drive Type</span>
                  <span className="text-white">{order.driveType || 'N/A'}</span>
                </div>
              </div>
            </div>
            
            {/* ✅ NEW: Team Assignment Section in Expanded Details */}
            <div className="mt-4">
              <h4 className="font-semibold text-sm mb-2 text-blue-300">Assignment Information</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Assigned Team</span>
                  <div className="flex items-center">
                    {(order.teamId || order.teamName) ? (
                      <span className="text-blue-300 font-medium">
                        {order.teamName || order.teamId}
                      </span>
                    ) : (
                      <span className="text-yellow-400">No Team Assigned</span>
                    )}
                  </div>
                </div>
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Created By</span>
                  <span className="text-white">{order.createdByName || 'Unknown'}</span>
                </div>
                {order.teamId && process.env.NODE_ENV === 'development' && (
                  <div className="mb-2 col-span-2">
                    <span className="text-xs text-gray-400 block">Team ID (Debug)</span>
                    <span className="text-gray-500 font-mono text-xs">{order.teamId}</span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Customer Information - Enhanced */}
            <div className="mt-4">
              <h4 className="font-semibold text-sm mb-2 text-blue-300">Customer Information</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Customer Name</span>
                  <span className="text-white">{order.customerName || 'N/A'}</span>
                </div>
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Days Past Due</span>
                  <span className={`${daysPastDue > 30 ? 'text-red-400' : daysPastDue > 14 ? 'text-orange-400' : daysPastDue > 0 ? 'text-yellow-400' : 'text-green-400'}`}>
                    {daysPastDue > 0 ? `${daysPastDue} days` : 'Current'}
                  </span>
                </div>
                {order.dueDate && (
                  <div className="mb-2">
                    <span className="text-xs text-gray-400 block">Due Date</span>
                    <span className="text-white">{formatDate(order.dueDate)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div>
            <div className="mb-4">
              <h4 className="font-semibold text-sm mb-2 text-blue-300">Lienholder Information</h4>
              <div className="text-sm">
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Lienholder</span>
                  <span className="text-white">{order.lienholder || 'N/A'}</span>
                </div>
                <div className="mb-2">
                  <span className="text-xs text-gray-400 block">Account Number</span>
                  <span className="text-white font-mono">{order.accountNumber || 'N/A'}</span>
                </div>
              </div>
            </div>
            
            {/* Other vehicle views row */}
            {renderViews.length > 1 && (
              <div className="mb-4">
                <h4 className="font-semibold text-sm mb-2 text-blue-300">Other Vehicle Views</h4>
                <div className="grid grid-cols-4 gap-2">
                  {renderViews.map((view, index) => (
                    <div 
                      key={index} 
                      className={`cursor-pointer rounded overflow-hidden border ${selectedViewIndex === index ? 'border-blue-500' : 'border-gray-700'}`}
                      onClick={() => handleSwitchView(index)}
                    >
                      <img 
                        src={view.url}
                        alt={`${order.make} ${order.model} view ${index + 1}`} 
                        className="w-full h-auto"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = `data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="80" viewBox="0 0 100 80"><rect width="100" height="80" fill="#1A2642" /><text x="50" y="40" font-family="Arial" font-size="10" fill="white" text-anchor="middle">View ${index + 1}</text></svg>`;
                        }}
                      />
                      <div className="text-xs bg-gray-900 text-center py-1">View {index + 1}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <div className="md:col-span-2">
            <h4 className="font-semibold text-sm mb-2 text-blue-300">Notes</h4>
            <p className="text-sm text-gray-300 bg-gray-900 p-2 rounded border border-gray-700">
              {order.notes || 'No notes available for this order.'}
            </p>
          </div>
        </div>
      )}
      
      <style>{`
        @keyframes pulse {
          0% { box-shadow: 0 0 8px rgba(59, 130, 246, 0.3); }
          50% { box-shadow: 0 0 15px rgba(59, 130, 246, 0.5); }
          100% { box-shadow: 0 0 8px rgba(59, 130, 246, 0.3); }
        }
        
        @keyframes fadeIn {
          0% { opacity: 0; transform: translateY(10px); }
          100% { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.5s ease-in-out;
        }
      `}</style>
    </div>
  );
};

export default OrderCard;