import React, { useState, useContext } from 'react';
import { collection, addDoc } from 'firebase/firestore';
// In all components
import { MapContext } from '../MapContext';

// Helper function to compress image to base64
const compressImageToBase64 = (file, maxWidth = 800, maxHeight = 800, quality = 0.7) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        // Create a canvas to resize the image
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;
        
        // Calculate new dimensions while maintaining aspect ratio
        if (width > height) {
          if (width > maxWidth) {
            height = Math.round(height * maxWidth / width);
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = Math.round(width * maxHeight / height);
            height = maxHeight;
          }
        }
        
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, width, height);
        
        // Get base64 representation
        const base64 = canvas.toDataURL('image/jpeg', quality);
        resolve(base64);
      };
      img.onerror = (error) => reject(error);
    };
    reader.onerror = (error) => reject(error);
  });
};

const VehicleInspectionModal = ({ isStartInspection, onComplete, onCancel }) => {
  const { currentAddress, currentLocation, setError } = useContext(MapContext);
  
  // Inspection state
  const [currentInspectionPart, setCurrentInspectionPart] = useState('frontSide');
  const [inspectionImages, setInspectionImages] = useState({
    frontSide: null,
    rightSide: null,
    backSide: null,
    leftSide: null,
    frontTires: null,
    backTires: null,
    interior: null,
    mileage: '',
    vehicleNumber: ''
  });
  
  // Take a photo for vehicle inspection
  const takeInspectionPhoto = (part) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.capture = 'environment';
    
    input.onchange = async (e) => {
      if (e.target.files && e.target.files[0]) {
        try {
          const base64 = await compressImageToBase64(e.target.files[0]);
          
          // Update the appropriate part in the inspection images state
          setInspectionImages(prev => ({
            ...prev,
            [part]: base64
          }));
        } catch (err) {
          console.error("Error processing inspection photo:", err);
          setError("Failed to process photo. Please try again.");
        }
      }
    };
    
    input.click();
  };
  
  // Handle form completion
  const handleComplete = () => {
    // Validate that all photos are taken
    const allPartsTaken = Object.entries(inspectionImages).every(([key, value]) => {
      // Skip mileage and vehicleNumber text fields from photo validation
      if (key === 'mileage' || key === 'vehicleNumber') return true;
      return !!value;
    });
    
    if (!allPartsTaken) {
      setError("Please take photos of all required parts of the vehicle.");
      return;
    }
    
    onComplete(inspectionImages);
  };
  
  return (
    <div className="vehicle-inspection-modal">
      <div className="vehicle-inspection-container">
        <div className="vehicle-inspection-header">
          <h2 className="vehicle-inspection-title">
            {isStartInspection ? 'Start of Shift Inspection' : 'End of Shift Inspection'}
          </h2>
          <button 
            className="vehicle-inspection-close"
            onClick={onCancel}
          >
            ×
          </button>
        </div>
        
        {/* Progress Steps */}
        <div className="vehicle-inspection-progress">
          <div className="vehicle-inspection-progress-bar">
            <div 
              className="vehicle-inspection-progress-fill"
              style={{ 
                width: (() => {
                  const partIndex = [
                    'frontSide', 'rightSide', 'backSide', 'leftSide', 
                    'frontTires', 'backTires', 'interior', 'mileage'
                  ].indexOf(currentInspectionPart);
                  return `${(partIndex / 7) * 100}%`;
                })()
              }}
            ></div>
          </div>
          
          {['frontSide', 'rightSide', 'backSide', 'leftSide', 'frontTires', 'backTires', 'interior', 'mileage'].map((part, index) => (
            <div 
              key={part}
              className={`vehicle-inspection-step ${
                currentInspectionPart === part 
                  ? 'active' 
                  : inspectionImages[part] ? 'completed' : ''
              }`}
              onClick={() => setCurrentInspectionPart(part)}
            >
              {index + 1}
              <div className="vehicle-inspection-step-label">
                {part === 'frontSide' 
                  ? 'Front' 
                  : part === 'rightSide' 
                    ? 'Right Side' 
                    : part === 'backSide' 
                      ? 'Back' 
                      : part === 'leftSide' 
                        ? 'Left Side' 
                        : part === 'frontTires' 
                          ? 'Front Tires' 
                          : part === 'backTires' 
                            ? 'Back Tires' 
                            : part === 'interior' 
                              ? 'Interior' 
                              : 'Vehicle Info'}
              </div>
            </div>
          ))}
        </div>
        
        <div className="vehicle-inspection-content">
          {/* Vehicle Info Form */}
          {currentInspectionPart === 'mileage' ? (
            <div className="vehicle-inspection-form">
              <div className="vehicle-inspection-form-group">
                <label htmlFor="vehicle-number">Vehicle Number</label>
                <input 
                  type="text" 
                  id="vehicle-number"
                  value={inspectionImages.vehicleNumber || ''}
                  onChange={(e) => setInspectionImages(prev => ({
                    ...prev,
                    vehicleNumber: e.target.value
                  }))}
                  placeholder="Enter vehicle number"
                />
              </div>
              
              <div className="vehicle-inspection-form-group">
                <label htmlFor="vehicle-mileage">Current Mileage</label>
                <input 
                  type="text" 
                  id="vehicle-mileage"
                  value={inspectionImages.mileage || ''}
                  onChange={(e) => setInspectionImages(prev => ({
                    ...prev,
                    mileage: e.target.value
                  }))}
                  placeholder="Enter current mileage"
                />
              </div>
              
              <div className="vehicle-inspection-form-group">
                <label>Current Location</label>
                <div className="bg-gray-800 p-2 text-gray-300 rounded">
                  {currentAddress || 'Location not available'}
                  {currentLocation && (
                    <div className="text-xs text-gray-400 mt-1">
                      {currentLocation.lat.toFixed(6)}, {currentLocation.lng.toFixed(6)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="vehicle-inspection-photo-area">
              <div className="vehicle-inspection-photo-label">
                {currentInspectionPart === 'frontSide' 
                  ? 'Front of Vehicle' 
                  : currentInspectionPart === 'rightSide' 
                    ? 'Right Side of Vehicle' 
                    : currentInspectionPart === 'backSide' 
                      ? 'Back of Vehicle' 
                      : currentInspectionPart === 'leftSide' 
                        ? 'Left Side of Vehicle' 
                        : currentInspectionPart === 'frontTires' 
                          ? 'Front Tires' 
                          : currentInspectionPart === 'backTires' 
                            ? 'Back Tires' 
                            : 'Vehicle Interior'}
              </div>
              
              <div className="vehicle-inspection-photo-description">
                Take a clear photo of the {
                  currentInspectionPart === 'frontSide' 
                    ? 'front of the vehicle' 
                    : currentInspectionPart === 'rightSide' 
                      ? 'right side of the vehicle' 
                      : currentInspectionPart === 'backSide' 
                        ? 'back of the vehicle' 
                        : currentInspectionPart === 'leftSide' 
                          ? 'left side of the vehicle' 
                          : currentInspectionPart === 'frontTires' 
                            ? 'front tires (both sides)' 
                            : currentInspectionPart === 'backTires' 
                              ? 'back tires (both sides)' 
                              : 'vehicle interior (dashboard and seats)'
                }
              </div>
              
              <div className="vehicle-inspection-photo-preview">
                {inspectionImages[currentInspectionPart] ? (
                  <img 
                    src={inspectionImages[currentInspectionPart]} 
                    alt={`${currentInspectionPart} preview`}
                  />
                ) : (
                  <div className="text-gray-400">
                    No photo taken yet
                  </div>
                )}
              </div>
              
              <div className="vehicle-inspection-photo-buttons">
                <button 
                  className="vehicle-inspection-photo-btn camera"
                  onClick={() => takeInspectionPhoto(currentInspectionPart)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M15 12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1h1.172a3 3 0 0 0 2.12-.879l.83-.828A1 1 0 0 1 6.827 3h2.344a1 1 0 0 1 .707.293l.828.828A3 3 0 0 0 12.828 5H14a1 1 0 0 1 1 1zM2 4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2h-1.172a2 2 0 0 1-1.414-.586l-.828-.828A2 2 0 0 0 9.172 2H6.828a2 2 0 0 0-1.414.586l-.828.828A2 2 0 0 1 3.172 4z"/>
                    <path d="M8 11a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5m0 1a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7M3 6.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0"/>
                  </svg>
                  Take Photo
                </button>
                
                {inspectionImages[currentInspectionPart] && (
                  <button 
                    className="vehicle-inspection-photo-btn remove"
                    onClick={() => setInspectionImages(prev => ({
                      ...prev,
                      [currentInspectionPart]: null
                    }))}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"/>
                      <path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z"/>
                    </svg>
                    Remove
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
        
        <div className="vehicle-inspection-actions">
          <button 
            className="vehicle-inspection-btn back"
            onClick={() => {
              const parts = ['frontSide', 'rightSide', 'backSide', 'leftSide', 'frontTires', 'backTires', 'interior', 'mileage'];
              const currentIndex = parts.indexOf(currentInspectionPart);
              if (currentIndex > 0) {
                setCurrentInspectionPart(parts[currentIndex - 1]);
              }
            }}
            disabled={currentInspectionPart === 'frontSide'}
          >
            Back
          </button>
          
          {currentInspectionPart === 'mileage' ? (
            <button 
              className="vehicle-inspection-btn submit"
              onClick={handleComplete}
            >
              Complete
            </button>
          ) : (
            <button 
              className="vehicle-inspection-btn next"
              onClick={() => {
                const parts = ['frontSide', 'rightSide', 'backSide', 'leftSide', 'frontTires', 'backTires', 'interior', 'mileage'];
                const currentIndex = parts.indexOf(currentInspectionPart);
                if (currentIndex < parts.length - 1) {
                  setCurrentInspectionPart(parts[currentIndex + 1]);
                }
              }}
            >
              Next
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default VehicleInspectionModal;