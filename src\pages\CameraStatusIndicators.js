import React, { useState, useEffect } from 'react';
import { collection, onSnapshot, query } from 'firebase/firestore';
import { db } from './firebase';
import { useNavigate } from 'react-router-dom';

const CameraStatusIndicators = ({ teamId }) => {
  const [cameraStatus, setCameraStatus] = useState({});
  const [isBlinking, setIsBlinking] = useState({});
  const navigate = useNavigate();
  
  // Load camera status from Firestore
  useEffect(() => {
    let unsubscribe = () => {};
    
    try {
      // Set up listener for the camera status collection
      const camerasQuery = query(collection(db, 'cameraStatus'));
      
      unsubscribe = onSnapshot(
        camerasQuery,
        (snapshot) => {
          const statusData = {};
          snapshot.forEach(doc => {
            statusData[doc.id] = doc.data();
          });
          setCameraStatus(statusData);
        },
        (error) => {
          console.error("Error getting camera status:", error);
        }
      );
    } catch (error) {
      console.error("Error setting up camera status listener:", error);
    }
    
    return () => unsubscribe();
  }, []);
  
  // Set up blinking effect for active cameras
  useEffect(() => {
    const intervals = {};
    
    // Set up interval for each active camera
    Object.entries(cameraStatus).forEach(([cameraId, data]) => {
      if (data?.active) {
        intervals[cameraId] = setInterval(() => {
          setIsBlinking(prev => ({
            ...prev,
            [cameraId]: !prev[cameraId]
          }));
        }, 800 + Math.random() * 500); // Random timing for more realistic effect
      }
    });
    
    // Clean up intervals on unmount or when camera status changes
    return () => {
      Object.values(intervals).forEach(interval => clearInterval(interval));
    };
  }, [cameraStatus]);
  
  // Get counts for display
  const activeCameras = Object.values(cameraStatus).filter(data => data?.active).length;
  const totalCameras = Object.keys(cameraStatus).length;
  
  // Don't show anything if no cameras are configured
  if (totalCameras === 0) return null;
  
  return (
    <div 
      className="flex items-center bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg px-3 py-1 shadow-md cursor-pointer mr-2"
      onClick={() => navigate('/camera')}
      title="Go to Camera System"
    >
      <div className="mr-2 flex space-x-1 flex-wrap max-w-[120px]">
        {/* FIXED: Show ALL indicators, not just the first 4 */}
        {Object.entries(cameraStatus).map(([cameraId, data], index) => (
          <div 
            key={`indicator-${cameraId}-${index}`}
            className={`h-2 w-2 rounded-full ${
              data?.active && isBlinking[cameraId] 
                ? 'bg-green-400 shadow-sm' 
                : data?.active 
                  ? 'bg-green-600' 
                  : 'bg-red-600'
            }`}
            style={{
              boxShadow: data?.active && isBlinking[cameraId] ? '0 0 4px #4ade80' : 'none'
            }}
            title={`Camera ${index + 1}: ${data?.active ? 'Online' : 'Offline'}`}
          ></div>
        ))}
        
        {/* REMOVED: No more "+X" indicator since we're showing all cameras */}
      </div>
      <span className="text-xs">
        <span className={`font-medium ${activeCameras > 0 ? 'text-green-400' : 'text-red-400'}`}>
          {activeCameras}/{totalCameras}
        </span> Cameras
      </span>
    </div>
  );
};

export default CameraStatusIndicators;