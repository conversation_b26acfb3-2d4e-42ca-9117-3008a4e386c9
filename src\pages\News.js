import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext.js';
import { useNavigate } from 'react-router-dom';
import { 
  getFirestore, 
  collection, 
  doc, 
  addDoc, 
  getDocs, 
  deleteDoc, 
  updateDoc, 
  query, 
  orderBy, 
  serverTimestamp 
} from 'firebase/firestore';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

function News() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const db = getFirestore();
  const storage = getStorage();
  
  // State for news articles
  const [newsArticles, setNewsArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  
  // State for creating/editing news
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isUrgent, setIsUrgent] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [currentArticleId, setCurrentArticleId] = useState(null);
  
  // UI states
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formError, setFormError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  
  // Fetch news articles
  useEffect(() => {
    const fetchNews = async () => {
      try {
        const newsQuery = query(collection(db, 'news'), orderBy('timestamp', 'desc'));
        const snapshot = await getDocs(newsQuery);
        
        const articles = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate() || new Date()
        }));
        
        setNewsArticles(articles);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching news:", error);
        setLoading(false);
      }
    };
    
    fetchNews();
  }, [db]);
  
  // Handle image selection
  const handleImageChange = (e) => {
    if (e.target.files[0]) {
      setSelectedImage(e.target.files[0]);
      
      // Create image preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(e.target.files[0]);
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!title.trim() || !content.trim()) {
      setFormError('Please provide both a title and content for the news article.');
      return;
    }
    
    setFormError('');
    let imageUrl = null;
    
    try {
      // Upload image if selected
      if (selectedImage) {
        const storageRef = ref(storage, `news/${Date.now()}_${selectedImage.name}`);
        await uploadBytes(storageRef, selectedImage);
        imageUrl = await getDownloadURL(storageRef);
      }
      
      const newsData = {
        title: title.trim(),
        content: content.trim(),
        isUrgent,
        timestamp: serverTimestamp(),
        author: currentUser.displayName || currentUser.email,
        authorId: currentUser.uid,
        imageUrl
      };
      
      if (isEditing && currentArticleId) {
        // Update existing article
        const articleRef = doc(db, 'news', currentArticleId);
        
        // If no new image was selected, keep the existing image URL
        if (!selectedImage) {
          const currentArticle = newsArticles.find(article => article.id === currentArticleId);
          newsData.imageUrl = currentArticle.imageUrl;
        }
        
        await updateDoc(articleRef, newsData);
        setSuccessMessage('News article updated successfully!');
      } else {
        // Create new article
        await addDoc(collection(db, 'news'), newsData);
        setSuccessMessage('News article created successfully!');
      }
      
      // Reset form
      resetForm();
      // Refresh the news list
      const newsQuery = query(collection(db, 'news'), orderBy('timestamp', 'desc'));
      const snapshot = await getDocs(newsQuery);
      
      const articles = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date()
      }));
      
      setNewsArticles(articles);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
      
    } catch (error) {
      console.error("Error creating/updating news:", error);
      setFormError('An error occurred. Please try again.');
    }
  };
  
  // Handle editing an article
  const handleEdit = (article) => {
    setTitle(article.title);
    setContent(article.content);
    setIsUrgent(article.isUrgent || false);
    setImagePreview(article.imageUrl);
    setIsEditing(true);
    setCurrentArticleId(article.id);
    setShowCreateForm(true);
    window.scrollTo(0, 0);
  };
  
  // Handle deleting an article
  const handleDelete = async (articleId) => {
    if (window.confirm('Are you sure you want to delete this news article? This action cannot be undone.')) {
      try {
        await deleteDoc(doc(db, 'news', articleId));
        setNewsArticles(newsArticles.filter(article => article.id !== articleId));
        setSuccessMessage('News article deleted successfully!');
        
        // Hide success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage('');
        }, 3000);
      } catch (error) {
        console.error("Error deleting news:", error);
        setFormError('An error occurred while deleting. Please try again.');
      }
    }
  };
  
  // Reset form
  const resetForm = () => {
    setTitle('');
    setContent('');
    setIsUrgent(false);
    setSelectedImage(null);
    setImagePreview(null);
    setIsEditing(false);
    setCurrentArticleId(null);
    setShowCreateForm(false);
  };
  
  // Format date
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 shadow-md border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold" style={{
              backgroundImage: 'linear-gradient(to right, #60a5fa, #a78bfa)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>News</h1>
            
            <div className="flex space-x-3">
              <button
                onClick={() => navigate('/dashboard')}
                className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md shadow-md transition duration-200"
              >
                Back to Dashboard
              </button>
              
              {isAdmin && (
                <button
                  onClick={() => setShowCreateForm(!showCreateForm)}
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md shadow-md transition duration-200"
                >
                  {showCreateForm ? 'Hide Form' : 'Create News'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Success message */}
        {successMessage && (
          <div className="mb-6 bg-green-900 bg-opacity-50 border border-green-700 text-green-300 px-4 py-3 rounded-md flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{successMessage}</span>
          </div>
        )}
        
        {/* Create/Edit News Form (Admin Only) */}
        {isAdmin && showCreateForm && (
          <div className="mb-8 bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-6">
            <h2 className="text-xl font-semibold mb-4">{isEditing ? 'Edit News Article' : 'Create News Article'}</h2>
            
            {formError && (
              <div className="mb-4 bg-red-900 bg-opacity-50 border border-red-700 text-red-300 px-4 py-3 rounded-md">
                {formError}
              </div>
            )}
            
            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-300 mb-1">
                    Title
                  </label>
                  <input
                    type="text"
                    id="title"
                    className="w-full bg-gray-700 border border-gray-600 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="News article title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                  />
                </div>
                
                <div>
                  <label htmlFor="content" className="block text-sm font-medium text-gray-300 mb-1">
                    Content
                  </label>
                  <textarea
                    id="content"
                    rows="6"
                    className="w-full bg-gray-700 border border-gray-600 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="News article content..."
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                  ></textarea>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="urgent"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-500 rounded"
                    checked={isUrgent}
                    onChange={(e) => setIsUrgent(e.target.checked)}
                  />
                  <label htmlFor="urgent" className="ml-2 block text-sm text-gray-300">
                    Mark as Urgent News
                  </label>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Image (Optional)
                  </label>
                  
                  <div className="flex items-center space-x-4">
                    <label className="flex flex-col items-center px-4 py-2 bg-gray-700 text-blue-500 rounded-lg border border-gray-600 cursor-pointer hover:bg-gray-600 transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <span className="mt-2 text-sm">Select Image</span>
                      <input
                        type="file"
                        className="hidden"
                        accept="image/*"
                        onChange={handleImageChange}
                      />
                    </label>
                    
                    {imagePreview && (
                      <div className="relative">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="h-20 w-20 object-cover rounded-md"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setSelectedImage(null);
                            setImagePreview(null);
                          }}
                          className="absolute -top-2 -right-2 bg-red-600 rounded-full p-1 text-white"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex space-x-3">
                  <button
                    type="submit"
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-6 py-2 rounded-md shadow-md transition duration-200"
                  >
                    {isEditing ? 'Update Article' : 'Publish Article'}
                  </button>
                  <button
                    type="button"
                    onClick={resetForm}
                    className="bg-gray-700 hover:bg-gray-600 text-white px-6 py-2 rounded-md shadow-md transition duration-200"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </form>
          </div>
        )}
        
        {/* News Articles List */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">All News Articles</h2>
          
          {loading ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : newsArticles.length === 0 ? (
            <div className="bg-gray-800 rounded-lg p-6 text-center border border-gray-700">
              <p className="text-gray-400">No news articles have been published yet.</p>
              {isAdmin && (
                <button
                  onClick={() => setShowCreateForm(true)}
                  className="mt-4 bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-md shadow-md transition duration-200"
                >
                  Create the first article
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {newsArticles.map(article => (
                <div
                  key={article.id}
                  className={`bg-gray-800 rounded-lg shadow-lg overflow-hidden border ${
                    article.isUrgent ? 'border-red-600' : 'border-gray-700'
                  }`}
                >
                  {article.isUrgent && (
                    <div className="bg-red-700 text-white text-xs font-bold px-3 py-1 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      URGENT NEWS
                    </div>
                  )}
                  
                  {article.imageUrl && (
                    <div className="w-full h-48 overflow-hidden">
                      <img
                        src={article.imageUrl}
                        alt={article.title}
                        className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                      />
                    </div>
                  )}
                  
                  <div className="p-6">
                    <h3 className="text-xl font-semibold mb-2">{article.title}</h3>
                    
                    <div className="text-sm text-gray-400 mb-3 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      {formatDate(article.timestamp)}
                    </div>
                    
                    <p className="text-gray-300 mb-4 line-clamp-3">{article.content}</p>
                    
                    <div className="mt-4 flex justify-between items-center">
                      <div className="text-sm text-gray-400">
                        Posted by: {article.author}
                      </div>
                      
                      {isAdmin && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEdit(article)}
                            className="bg-blue-700 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm flex items-center"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Edit
                          </button>
                          
                          <button
                            onClick={() => handleDelete(article.id)}
                            className="bg-red-700 hover:bg-red-600 text-white px-3 py-1 rounded text-sm flex items-center"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            Delete
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default News;