// src/components/Tags.js
import React, { useState } from 'react';
import PropTypes from 'prop-types';

/**
 * Tags component for creating and displaying pill-shaped tag icons
 * with custom colors and names that can be assigned to users.
 */
const Tags = ({
  initialTags = [],
  onTagsChange,
  readOnly = false,
  allowCreate = true,
  allowEdit = true,
  allowDelete = true,
  availableTags = [],
  onTagClick,
  className = ''
}) => {
  const [tags, setTags] = useState(initialTags);
  const [isAdding, setIsAdding] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingTagIndex, setEditingTagIndex] = useState(null);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#3B82F6'); // Default blue color
  
  // Common colors for quick selection
  const colorOptions = [
    { label: 'Blue', value: '#3B82F6' },
    { label: 'Red', value: '#EF4444' },
    { label: 'Green', value: '#10B981' },
    { label: 'Yellow', value: '#F59E0B' },
    { label: 'Purple', value: '#8B5CF6' },
    { label: 'Pink', value: '#EC4899' },
    { label: 'Gray', value: '#6B7280' },
    { label: 'Indigo', value: '#6366F1' },
  ];

  // Handle adding a new tag
  const handleAddTag = () => {
    if (newTagName.trim()) {
      const updatedTags = [...tags, { name: newTagName.trim(), color: newTagColor }];
      setTags(updatedTags);
      
      // Reset form
      setNewTagName('');
      setIsAdding(false);
      
      // Call the onTagsChange prop if provided
      if (onTagsChange) {
        onTagsChange(updatedTags);
      }
    }
  };

  // Handle editing an existing tag
  const handleEditTag = () => {
    if (newTagName.trim() && editingTagIndex !== null) {
      const updatedTags = [...tags];
      updatedTags[editingTagIndex] = { 
        ...updatedTags[editingTagIndex],
        name: newTagName.trim(),
        color: newTagColor 
      };
      
      setTags(updatedTags);
      
      // Reset form
      setNewTagName('');
      setEditingTagIndex(null);
      setIsEditing(false);
      
      // Call the onTagsChange prop if provided
      if (onTagsChange) {
        onTagsChange(updatedTags);
      }
    }
  };

  // Start editing an existing tag
  const startEditTag = (index) => {
    setIsEditing(true);
    setEditingTagIndex(index);
    setNewTagName(tags[index].name);
    setNewTagColor(tags[index].color);
  };

  // Handle removing a tag
  const handleRemoveTag = (indexToRemove) => {
    const updatedTags = tags.filter((_, index) => index !== indexToRemove);
    setTags(updatedTags);
    
    // Call the onTagsChange prop if provided
    if (onTagsChange) {
      onTagsChange(updatedTags);
    }
  };

  // Handle selecting a predefined tag
  const handleSelectTag = (tag) => {
    // Check if the tag is already in the list
    const tagExists = tags.some(t => t.name === tag.name && t.color === tag.color);
    
    if (!tagExists) {
      const updatedTags = [...tags, tag];
      setTags(updatedTags);
      
      // Call the onTagsChange prop if provided
      if (onTagsChange) {
        onTagsChange(updatedTags);
      }
    }
    
    setIsAdding(false);
  };

  // Generate contrasting text color based on background color
  const getTextColor = (hexColor) => {
    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    
    // Calculate brightness (YIQ formula)
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    
    return yiq >= 150 ? '#000000' : '#FFFFFF';
  };

  // Handle tag click
  const handleTagClick = (tag, index) => {
    if (onTagClick) {
      onTagClick(tag, index);
    }
  };

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {/* Display existing tags */}
      {tags.map((tag, index) => (
        <div 
          key={index}
          className="flex items-center rounded-full px-3 py-1 text-sm font-medium transition-all"
          style={{ 
            backgroundColor: tag.color,
            color: getTextColor(tag.color)
          }}
        >
          <span 
            className="cursor-pointer"
            onClick={() => handleTagClick(tag, index)}
          >
            {tag.name}
          </span>
          
          {/* Edit button */}
          {!readOnly && allowEdit && (
            <button
              type="button"
              onClick={() => startEditTag(index)}
              className="ml-1 p-1 hover:opacity-80 rounded-full focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </button>
          )}
          
          {/* Delete button */}
          {!readOnly && allowDelete && (
            <button
              type="button"
              onClick={() => handleRemoveTag(index)}
              className="ml-1 p-1 hover:opacity-80 rounded-full focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      ))}
      
      {/* Add new tag button */}
      {!readOnly && allowCreate && !isAdding && !isEditing && (
        <button
          type="button"
          onClick={() => setIsAdding(true)}
          className="inline-flex items-center rounded-full border border-dashed border-gray-300 px-3 py-1 text-sm font-medium text-gray-600 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Add Tag
        </button>
      )}
      
      {/* Add tag form */}
      {!readOnly && isAdding && (
        <div className="flex flex-col gap-2 p-3 bg-white rounded-lg shadow border w-64">
          <div className="text-sm font-medium mb-1">Add New Tag</div>
          
          <input
            type="text"
            value={newTagName}
            onChange={(e) => setNewTagName(e.target.value)}
            placeholder="Tag name"
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            autoFocus
          />
          
          <div className="flex items-center gap-2">
            <input
              type="color"
              value={newTagColor}
              onChange={(e) => setNewTagColor(e.target.value)}
              className="w-8 h-8 p-0 border-0 rounded cursor-pointer"
            />
            <div className="flex flex-wrap gap-1">
              {colorOptions.map((color) => (
                <button
                  key={color.value}
                  type="button"
                  onClick={() => setNewTagColor(color.value)}
                  className="w-5 h-5 rounded-full cursor-pointer border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  style={{ backgroundColor: color.value }}
                  title={color.label}
                />
              ))}
            </div>
          </div>
          
          <div className="mt-2">
            <div className="text-sm mb-1">Preview:</div>
            <div 
              className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium"
              style={{ 
                backgroundColor: newTagColor,
                color: getTextColor(newTagColor)
              }}
            >
              {newTagName || 'Tag name'}
            </div>
          </div>
          
          {/* Available tags section */}
          {availableTags.length > 0 && (
            <div className="mt-2">
              <div className="text-sm mb-1">Available Tags:</div>
              <div className="flex flex-wrap gap-1 max-h-24 overflow-y-auto">
                {availableTags.map((tag, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => handleSelectTag(tag)}
                    className="rounded-full px-2 py-0.5 text-xs font-medium"
                    style={{ 
                      backgroundColor: tag.color,
                      color: getTextColor(tag.color)
                    }}
                  >
                    {tag.name}
                  </button>
                ))}
              </div>
            </div>
          )}
          
          <div className="flex justify-end mt-2 gap-2">
            <button
              type="button"
              onClick={() => {
                setIsAdding(false);
                setNewTagName('');
              }}
              className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleAddTag}
              disabled={!newTagName.trim()}
              className={`px-3 py-1 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 ${!newTagName.trim() ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Add
            </button>
          </div>
        </div>
      )}
      
      {/* Edit tag form */}
      {!readOnly && isEditing && (
        <div className="flex flex-col gap-2 p-3 bg-white rounded-lg shadow border w-64">
          <div className="text-sm font-medium mb-1">Edit Tag</div>
          
          <input
            type="text"
            value={newTagName}
            onChange={(e) => setNewTagName(e.target.value)}
            placeholder="Tag name"
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            autoFocus
          />
          
          <div className="flex items-center gap-2">
            <input
              type="color"
              value={newTagColor}
              onChange={(e) => setNewTagColor(e.target.value)}
              className="w-8 h-8 p-0 border-0 rounded cursor-pointer"
            />
            <div className="flex flex-wrap gap-1">
              {colorOptions.map((color) => (
                <button
                  key={color.value}
                  type="button"
                  onClick={() => setNewTagColor(color.value)}
                  className="w-5 h-5 rounded-full cursor-pointer border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  style={{ backgroundColor: color.value }}
                  title={color.label}
                />
              ))}
            </div>
          </div>
          
          <div className="mt-2">
            <div className="text-sm mb-1">Preview:</div>
            <div 
              className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium"
              style={{ 
                backgroundColor: newTagColor,
                color: getTextColor(newTagColor)
              }}
            >
              {newTagName || 'Tag name'}
            </div>
          </div>
          
          <div className="flex justify-end mt-2 gap-2">
            <button
              type="button"
              onClick={() => {
                setIsEditing(false);
                setEditingTagIndex(null);
                setNewTagName('');
              }}
              className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleEditTag}
              disabled={!newTagName.trim()}
              className={`px-3 py-1 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 ${!newTagName.trim() ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Save
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

Tags.propTypes = {
  initialTags: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      color: PropTypes.string.isRequired
    })
  ),
  onTagsChange: PropTypes.func,
  readOnly: PropTypes.bool,
  allowCreate: PropTypes.bool,
  allowEdit: PropTypes.bool,
  allowDelete: PropTypes.bool,
  availableTags: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      color: PropTypes.string.isRequired
    })
  ),
  onTagClick: PropTypes.func,
  className: PropTypes.string
};

export default Tags;