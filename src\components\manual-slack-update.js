// manual-slack-update.js
// Quick script to manually update a single team's Slack channel

require('dotenv').config();
const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID || process.env.REACT_APP_PROD_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    })
  });
}

const db = admin.firestore();

async function updateTeamSlackChannel(teamName, channelId) {
  try {
    // Find team by name
    const teamsSnapshot = await db.collection('teams')
      .where('name', '==', teamName)
      .get();
    
    if (teamsSnapshot.empty) {
      console.log(`❌ Team "${teamName}" not found`);
      return false;
    }

    const teamDoc = teamsSnapshot.docs[0];
    const teamId = teamDoc.id;

    // Update the team
    await db.collection('teams').doc(teamId).update({
      slackChannel: channelId,
      slackIntegration: {
        enabled: true,
        channelId: channelId,
        configuredAt: admin.firestore.FieldValue.serverTimestamp()
      }
    });

    console.log(`✅ Successfully updated team "${teamName}"`);
    console.log(`   Team ID: ${teamId}`);
    console.log(`   Slack Channel: ${channelId}`);
    
    return true;

  } catch (error) {
    console.error('❌ Error updating team:', error);
    return false;
  }
}

// Get command line arguments
const args = process.argv.slice(2);

if (args.length !== 2) {
  console.log('Usage: node manual-slack-update.js "Team Name" "ChannelID"');
  console.log('Example: node manual-slack-update.js "Team Alpha" "C1234567890"');
  process.exit(1);
}

const [teamName, channelId] = args;

// Validate channel ID format
if (!channelId.match(/^[CDG][A-Z0-9]+$/)) {
  console.log('⚠️  Warning: Channel ID format looks incorrect.');
  console.log('   Channel IDs usually start with C (public channel), D (DM), or G (private channel)');
}

// Run the update
updateTeamSlackChannel(teamName, channelId).then(() => {
  process.exit(0);
});