// MapStyles.js - Complete updated styles with night mode toggle fixes

export const mapStyles = `
  .vehicle-map-container {
    width: 100%;
    height: 100vh;
    position: relative;
    background-color: #f8f9fa;
    transition: all 0.5s ease;
  }

  /* Full screen map for all devices */
  .vehicle-map-container.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    height: 100vh;
    width: 100vw;
  }

  /* Responsive height adjustments */
  @media (max-width: 768px) {
    .vehicle-map-container {
      height: 100vh;
      min-height: 100vh;
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .vehicle-map-container {
      height: 100vh;
      min-height: 100vh;
    }
  }

  @media (min-width: 1025px) {
    .vehicle-map-container {
      height: 100vh;
      min-height: 100vh;
    }
  }

  /* FIXED: Enhanced night mode styling with better tile layer support */
  .vehicle-map-container.night-mode {
    background-color: #1a1a1a;
    border: 2px solid #4c1d95;
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
    transition: all 0.5s ease;
  }

  /* FIXED: Improved night mode tile layer styling */
  .leaflet-container.night-mode {
    background-color: #1a1a1a;
    transition: background-color 0.5s ease;
  }

  /* FIXED: Better tile layer transition effects */
  .leaflet-container.night-mode .leaflet-tile-pane {
    transition: filter 0.5s ease;
    /* Very gentle adjustments to dark tiles */
    filter: brightness(0.9) contrast(1.05);
  }

  /* Day mode ensures no filters */
  .leaflet-container:not(.night-mode) .leaflet-tile-pane {
    filter: none;
    transition: filter 0.5s ease;
  }

  /* FIXED: Enhanced tile loading transitions */
  .leaflet-tile {
    transition: opacity 0.2s ease-in-out !important;
  }

  /* Force tile layer updates */
  .leaflet-container.night-mode .leaflet-tile-container {
    transition: opacity 0.3s ease;
  }

  /* FIXED: Better control styling for night mode */
  .leaflet-container.night-mode .leaflet-control-zoom {
    background-color: rgba(31, 41, 55, 0.95);
    border: 1px solid #6366f1;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
  }

  .leaflet-container.night-mode .leaflet-control-zoom a {
    background-color: rgba(31, 41, 55, 0.95);
    color: #e5e7eb;
    border: 1px solid #4b5563;
    transition: all 0.3s ease;
  }

  .leaflet-container.night-mode .leaflet-control-zoom a:hover {
    background-color: rgba(55, 65, 81, 0.95);
    color: white;
    border-color: #6366f1;
  }

  /* Day mode control styling */
  .leaflet-container:not(.night-mode) .leaflet-control-zoom {
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid #e5e7eb;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
  }

  .leaflet-container:not(.night-mode) .leaflet-control-zoom a {
    background-color: rgba(255, 255, 255, 0.95);
    color: #374151;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;
  }

  .leaflet-container:not(.night-mode) .leaflet-control-zoom a:hover {
    background-color: rgba(249, 250, 251, 0.95);
    color: #111827;
    border-color: #9ca3af;
  }

  /* FIXED: Better transition for switching modes */
  .leaflet-container {
    transition: background-color 0.5s ease;
  }

  /* FIXED: Prevent tile flashing during transitions */
  .leaflet-tile-container {
    transition: none !important;
  }

  /* Override any conflicting transitions on tiles */
  .leaflet-layer,
  .leaflet-tile {
    transition: opacity 0.2s ease !important;
  }

  .current-location-marker {
    background: transparent !important;
    border: none !important;
  }

  .current-marker-inner {
    width: 20px;
    height: 20px;
    background-color: #3B82F6;
    border: 3px solid #FFFFFF;
    border-radius: 50%;
    position: relative;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    animation: pulse-blue 2s infinite;
  }

  @keyframes pulse-blue {
    0% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
  }
  
  .vehicle-marker {
    background: transparent !important;
    border: none !important;
  }
  
  .vehicle-marker-icon {
    width: 32px;
    height: 32px;
    background-color: #7C3AED;
    border: 2px solid #FFFFFF;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    transition: transform 0.2s ease, opacity 0.3s ease;
  }
  
  .vehicle-marker-icon:hover {
    transform: scale(1.1);
    cursor: pointer;
  }
  
  .vehicle-marker-icon.secured {
    background-color: #10B981;
  }
  
  .vehicle-marker-icon.found {
    background-color: #F59E0B;
  }
  
  .vehicle-marker-icon.pending {
    background-color: #3B82F6;
  }
  
  .vehicle-marker-icon.do-not-secure {
    background-color: #8B5CF6;
  }
  
  .vehicle-marker-icon.not-found {
    background-color: #EF4444;
  }
  
  .vehicle-marker-icon.bottom-status {
    background-color: #DC2626;
  }
  
  .vehicle-marker-icon.geocoded {
    opacity: 0.7;
    border-style: dashed;
  }
  
  .vehicle-marker-icon.gps-accurate {
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.5), 0 2px 6px rgba(0,0,0,0.3);
  }
  
  .vehicle-marker-icon.secured-fading {
    opacity: 0.3;
    filter: grayscale(50%);
    transform: scale(0.9);
  }
  
  .vehicle-marker-icon.newly-added {
    animation: pulse-new-vehicle 2s infinite;
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.8), 0 2px 6px rgba(0,0,0,0.3);
  }

  .vehicle-marker-icon.selected {
    box-shadow: 0 0 0 4px rgba(255, 255, 0, 0.8), 0 2px 6px rgba(0,0,0,0.3);
    animation: pulse-selected 1.5s infinite;
  }
  
  @keyframes pulse-new-vehicle {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.8), 0 2px 6px rgba(0,0,0,0.3);
    }
    50% {
      transform: scale(1.15);
      box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.4), 0 4px 12px rgba(0,0,0,0.4);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.8), 0 2px 6px rgba(0,0,0,0.3);
    }
  }

  @keyframes pulse-selected {
    0% {
      box-shadow: 0 0 0 4px rgba(255, 255, 0, 0.8), 0 2px 6px rgba(0,0,0,0.3);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(255, 255, 0, 0.4), 0 4px 12px rgba(0,0,0,0.4);
    }
    100% {
      box-shadow: 0 0 0 4px rgba(255, 255, 0, 0.8), 0 2px 6px rgba(0,0,0,0.3);
    }
  }

  /* Fixed vehicle marker icon styles */
  .vehicle-marker-icon-custom:hover {
    transform: scale(1.1) !important;
    cursor: pointer;
  }

  .vehicle-marker-icon-custom.secured-fading {
    opacity: 0.4 !important;
    filter: grayscale(50%) !important;
    transform: scale(0.9) !important;
  }

  .vehicle-marker-icon-custom.newly-added {
    animation: pulse-new-vehicle-custom 2s infinite !important;
  }

  .vehicle-marker-icon-custom.selected {
    box-shadow: 0 0 0 4px rgba(255, 255, 0, 0.8), 0 2px 6px rgba(0,0,0,0.4) !important;
    animation: pulse-selected-custom 1.5s infinite !important;
  }

  @keyframes pulse-new-vehicle-custom {
    0% {
      transform: scale(1);
      box-shadow: 0 2px 6px rgba(0,0,0,0.4), 0 0 0 0 rgba(59, 130, 246, 0.8);
    }
    50% {
      transform: scale(1.15);
      box-shadow: 0 4px 12px rgba(0,0,0,0.6), 0 0 0 8px rgba(59, 130, 246, 0.4);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 2px 6px rgba(0,0,0,0.4), 0 0 0 0 rgba(59, 130, 246, 0.8);
    }
  }

  @keyframes pulse-selected-custom {
    0% {
      box-shadow: 0 0 0 4px rgba(255, 255, 0, 0.8), 0 2px 6px rgba(0,0,0,0.4);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(255, 255, 0, 0.4), 0 4px 12px rgba(0,0,0,0.6);
    }
    100% {
      box-shadow: 0 0 0 4px rgba(255, 255, 0, 0.8), 0 2px 6px rgba(0,0,0,0.4);
    }
  }
  
  /* Order marker styles with vehicle icons */
  .order-marker {
    background: transparent !important;
    border: none !important;
  }
  
  .order-marker-icon {
    width: 38px;
    height: 38px;
    border: 3px solid #FFFFFF;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.5);
    transition: transform 0.2s ease, opacity 0.3s ease;
    position: relative;
    font-weight: bold;
    color: white;
  }
  
  .order-marker-icon:hover {
    transform: scale(1.1);
    cursor: pointer;
  }
  
  .order-marker-icon.order-open {
    background: linear-gradient(135deg, #3B82F6, #1E40AF);
  }
  
  .order-marker-icon.order-secure {
    background: linear-gradient(135deg, #10B981, #047857);
  }
  
  .order-marker-icon.order-pending {
    background: linear-gradient(135deg, #F59E0B, #D97706);
  }
  
  .order-marker-icon.order-claim {
    background: linear-gradient(135deg, #EC4899, #BE185D);
  }
  
  .order-marker-icon.order-restricted {
    background: linear-gradient(135deg, #EF4444, #DC2626);
  }
  
  .order-marker-icon.order-geocoded {
    opacity: 0.8;
    border-style: dashed;
  }
  
  .order-marker-icon.order-selected {
    box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.8), 0 4px 12px rgba(0,0,0,0.5);
    animation: pulse-order-selected 1.5s infinite;
  }
  
  .order-marker-icon.order-multi-address {
    border: 4px solid #FFD700;
    animation: pulse-multi-address 2s infinite;
  }
  
  @keyframes pulse-order-selected {
    0% {
      box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.8), 0 4px 12px rgba(0,0,0,0.5);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(255, 215, 0, 0.4), 0 8px 20px rgba(0,0,0,0.7);
    }
    100% {
      box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.8), 0 4px 12px rgba(0,0,0,0.5);
    }
  }
  
  @keyframes pulse-multi-address {
    0% {
      border-color: #FFD700;
      box-shadow: 0 4px 12px rgba(0,0,0,0.5);
    }
    50% {
      border-color: #FFA500;
      box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3), 0 4px 12px rgba(0,0,0,0.5);
    }
    100% {
      border-color: #FFD700;
      box-shadow: 0 4px 12px rgba(0,0,0,0.5);
    }
  }
  
  .order-priority-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 12px;
    height: 12px;
    background-color: #DC2626;
    border: 2px solid white;
    border-radius: 50%;
    font-size: 8px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .order-address-badge {
    position: absolute;
    top: -6px;
    left: -6px;
    width: 18px;
    height: 18px;
    background-color: #1E40AF;
    border: 2px solid white;
    border-radius: 50%;
    font-size: 10px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
  
  .vehicle-marker-icon.money-pulse {
    animation: money-pulse 3s infinite;
  }
  
  @keyframes money-pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.9), 0 2px 6px rgba(0,0,0,0.3);
    }
    25% {
      box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.6), 0 2px 6px rgba(0,0,0,0.3);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(34, 197, 94, 0.3), 0 4px 12px rgba(0,0,0,0.4);
    }
    75% {
      box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.6), 0 2px 6px rgba(0,0,0,0.3);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.9), 0 2px 6px rgba(0,0,0,0.3);
    }
  }
  
  .teammate-marker {
    background: transparent !important;
    border: none !important;
  }
  
  .teammate-marker-icon {
    width: 28px;
    height: 28px;
    border: 3px solid #FFFFFF;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.4);
    transition: transform 0.2s ease;
    font-size: 12px;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    position: relative;
    overflow: hidden;
  }
  
  .teammate-marker-icon svg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    filter: drop-shadow(1px 1px 1px rgba(0,0,0,0.5));
  }
  
  .teammate-marker-icon:hover {
    transform: scale(1.15);
  }
  
  .teammate-marker-icon.moving {
    animation: teammate-pulse 2s infinite;
  }
  
  @keyframes teammate-pulse {
    0% {
      box-shadow: 0 2px 8px rgba(0,0,0,0.4);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(255,255,255,0.3), 0 2px 8px rgba(0,0,0,0.4);
    }
    100% {
      box-shadow: 0 2px 8px rgba(0,0,0,0.4);
    }
  }
  
  .map-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 1000;
  }
  
  .map-control-button {
    width: 40px;
    height: 40px;
    background-color: white;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    /* Enhanced touch targets */
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    user-select: none;
  }
  
  .map-control-button:hover:not(:disabled) {
    background-color: #f3f4f6;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }
  
  .map-control-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .map-control-button.active {
    background-color: #3B82F6;
    border-color: #3B82F6;
  }
  
  .map-control-button.active svg {
    stroke: white;
  }

  /* FIXED: Enhanced night mode toggle styles with switching state */
  .map-control-button.night-mode-toggle {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .map-control-button.night-mode-toggle:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #7c3aed;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  }

  .map-control-button.night-mode-toggle svg {
    width: 24px;
    height: 24px;
    color: #374151;
    transition: all 0.3s ease;
  }

  .map-control-button.night-mode-toggle:hover:not(:disabled) svg {
    color: #7c3aed;
  }

  /* Night mode active state */
  .map-control-button.night-mode-toggle.active {
    background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);
    border-color: #8b5cf6;
    color: white;
  }

  .map-control-button.night-mode-toggle.active svg {
    color: #fbbf24;
  }

  .map-control-button.night-mode-toggle.active:hover:not(:disabled) {
    background: linear-gradient(135deg, #312e81 0%, #1e1b4b 100%);
    border-color: #a78bfa;
  }

  /* FIXED: Switching state styles */
  .map-control-button.night-mode-toggle.switching {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    border-color: #6366f1;
    color: white;
    cursor: wait;
    animation: pulse-switching 1.5s infinite;
  }

  .map-control-button.night-mode-toggle.switching svg {
    color: white;
  }

  @keyframes pulse-switching {
    0% {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 0 rgba(99, 102, 241, 0.7);
    }
    70% {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 0 rgba(99, 102, 241, 0);
    }
  }

  /* Location button enhanced styles */
  .map-control-button.location-btn {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .map-control-button.location-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  }

  .map-control-button.location-btn svg {
    width: 24px;
    height: 24px;
    color: #374151;
    transition: color 0.3s ease;
  }

  .map-control-button.location-btn:hover svg {
    color: #3b82f6;
  }

  .map-control-button.location-btn.active {
    animation: location-pulse 2s infinite;
  }

  @keyframes location-pulse {
    0% {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    70% {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 0 rgba(59, 130, 246, 0);
    }
  }

  /* Refresh button styles */
  .map-control-button.refresh-btn {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .map-control-button.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #10b981;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  }

  .map-control-button.refresh-btn svg {
    width: 24px;
    height: 24px;
    color: #374151;
    transition: all 0.3s ease;
  }

  .map-control-button.refresh-btn:hover svg {
    color: #10b981;
    transform: rotate(180deg);
  }

  .map-control-button.refresh-btn.refreshing svg {
    animation: spin-refresh 1s linear infinite;
    color: #10b981;
  }

  @keyframes spin-refresh {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  /* FIXED: Tile loading indicator for better user feedback */
  .leaflet-container .leaflet-control-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 10000;
    border: 2px solid #3b82f6;
    display: none;
  }

  .leaflet-container.tiles-loading .leaflet-control-loading {
    display: block;
    animation: fadeIn 0.3s ease;
  }

  /* FIXED: Map invalidation visual feedback */
  .map-container-invalidating {
    position: relative;
  }

  .map-container-invalidating::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(99, 102, 241, 0.1);
    z-index: 1000;
    animation: invalidation-flash 0.5s ease;
    pointer-events: none;
  }

  @keyframes invalidation-flash {
    0% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
  }

  /* FIXED: Status indicator for tile layer switching */
  .tile-switch-indicator {
    position: absolute;
    bottom: 60px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    z-index: 1000;
    border: 1px solid #3B82F6;
    animation: slideInRight 0.3s ease;
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .geocoding-status {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    z-index: 1000;
    border: 1px solid #3B82F6;
  }
  
  .trail-controls {
    background-color: rgba(31, 41, 55, 0.98);
    color: #f9fafb;
    padding: 12px;
    border-radius: 12px;
    border: 2px solid #3B82F6;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.5);
    height: 100%;
    overflow-y: auto;
  }
  
  .trail-controls h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: bold;
    color: #60A5FA;
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .trail-time-buttons {
    display: flex;
    gap: 4px;
    margin-bottom: 8px;
    flex-wrap: wrap;
  }
  
  .trail-time-button {
    background-color: rgba(31, 41, 55, 0.9);
    border: 1px solid #60A5FA;
    color: #F3F4F6;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    min-width: 32px;
    text-align: center;
  }
  
  .trail-time-button:hover {
    background-color: rgba(55, 65, 81, 1);
    transform: translateY(-1px);
  }
  
  .trail-time-button.active {
    background-color: #3B82F6;
    color: white;
    border-color: #2563EB;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.4);
  }
  
  .trail-toggle {
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid rgba(156, 163, 175, 0.3);
  }
  
  .trail-toggle label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    cursor: pointer;
    font-weight: 500;
    color: #E5E7EB;
  }
  
  .trail-toggle input[type="checkbox"] {
    margin: 0;
    width: 16px;
    height: 16px;
    accent-color: #3B82F6;
  }
  
  .trail-stats {
    font-size: 10px;
    color: #9CA3AF;
    margin-top: 8px;
    padding-top: 6px;
    border-top: 1px solid rgba(156, 163, 175, 0.2);
  }

  /* Compact trail controls without name list */
  .trail-compact-status {
    margin-top: 8px;
    padding: 6px;
    background-color: rgba(31, 41, 55, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(156, 163, 175, 0.3);
    text-align: center;
  }

  .trail-compact-counts {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #9CA3AF;
    margin-bottom: 4px;
  }

  .trail-compact-online {
    font-size: 11px;
    font-weight: bold;
    color: #10B981;
  }

  .trail-compact-offline {
    font-size: 11px;
    color: #EF4444;
  }

  /* Export button styling */
  .trail-export-button {
    width: 100%;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    margin-top: 8px;
  }

  .trail-export-button.enabled {
    background-color: #059669;
    color: white;
  }

  .trail-export-button.enabled:hover {
    background-color: #047857;
    transform: translateY(-1px);
  }

  .trail-export-button.disabled {
    background-color: #6B7280;
    color: #9CA3AF;
    cursor: not-allowed;
  }

  .chat-container {
    background-color: rgba(31, 41, 55, 0.98);
    border-radius: 12px;
    border: 2px solid #10B981;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.5);
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .chat-header {
    padding: 12px;
    border-bottom: 1px solid rgba(16, 185, 129, 0.3);
    background-color: rgba(55, 65, 81, 0.8);
    border-radius: 10px 10px 0 0;
    flex-shrink: 0;
  }

  .chat-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
    color: #34D399;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .chat-content {
    flex: 1;
    min-height: 0;
    background-color: #1F2937;
    border-radius: 0 0 10px 10px;
    display: flex;
    flex-direction: column;
  }

  .vehicle-selection-notification {
    position: absolute;
    top: 20px;
    left: 20px;
    background-color: rgba(255, 255, 0, 0.9);
    color: #000;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    z-index: 1000;
    border: 2px solid #FFA500;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    animation: slideInFromLeft 0.3s ease-out;
  }

  @keyframes slideInFromLeft {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  /* Order popup styles */
  .order-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
  }

  .order-popup {
    background-color: #1F2937;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    border: 2px solid #3B82F6;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    animation: slideUp 0.3s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      transform: translateY(30px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .order-popup-header {
    padding: 1rem;
    border-bottom: 1px solid #374151;
    background: linear-gradient(to right, #1F2937, #111827);
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .order-popup-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
  }

  .order-popup-close {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #4B5563;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s;
    flex-shrink: 0;
  }

  .order-popup-close:hover {
    background-color: #6B7280;
    transform: scale(1.1);
  }

  .order-status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.5rem;
  }

  .order-status-badge.open {
    background-color: #1E40AF;
    color: #DBEAFE;
  }

  .order-status-badge.secure {
    background-color: #065F46;
    color: #D1FAE5;
  }

  .order-status-badge.pending {
    background-color: #92400E;
    color: #FEF3C7;
  }

  .order-status-badge.claim {
    background-color: #9D174D;
    color: #FBCFE8;
  }

  .order-status-badge.restricted {
    background-color: #DC2626;
    color: #FEE2E2;
  }

  .order-popup-content {
    padding: 1rem;
  }

  .order-vehicle-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
    background-color: #111827;
    border: 1px solid #374151;
  }

  .order-vehicle-placeholder {
    width: 100%;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #111827;
    border: 1px solid #374151;
    border-radius: 8px;
    color: #9CA3AF;
    margin-bottom: 1rem;
  }

  .order-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .order-info-item {
    background-color: #111827;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid #374151;
  }

  .order-info-item.full-width {
    grid-column: span 2;
  }

  .order-info-label {
    font-size: 0.75rem;
    color: #9CA3AF;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
  }

  .order-info-value {
    font-weight: 600;
    color: white;
    word-break: break-word;
  }

  .order-info-value.vin {
    font-family: monospace;
    color: #60A5FA;
    font-size: 0.875rem;
  }

  .order-info-value.plate {
    color: #F59E0B;
    font-weight: 700;
  }

  .order-addresses-section {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #111827;
    border-radius: 8px;
    border: 1px solid #374151;
  }

  .order-addresses-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #60A5FA;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .order-address-item {
    background-color: #1F2937;
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    border-left: 3px solid #3B82F6;
  }

  .order-address-item:last-child {
    margin-bottom: 0;
  }

  .order-address-text {
    color: #E5E7EB;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .order-address-coords {
    font-family: monospace;
    font-size: 0.75rem;
    color: #9CA3AF;
  }

  .order-popup-actions {
    padding: 1rem;
    border-top: 1px solid #374151;
    background-color: #111827;
    border-radius: 0 0 10px 10px;
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .order-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s;
    cursor: pointer;
    border: none;
    flex: 1;
    min-width: 120px;
    position: relative;
  }

  .order-action-btn:hover {
    transform: translateY(-2px);
  }

  .order-action-btn.navigate {
    background: linear-gradient(135deg, #3B82F6, #1E40AF);
    color: white;
  }

  .order-action-btn.navigate:hover {
    background: linear-gradient(135deg, #2563EB, #1D4ED8);
  }

  .order-action-btn.secure {
    background: linear-gradient(135deg, #10B981, #047857);
    color: white;
  }

  .order-action-btn.secure:hover {
    background: linear-gradient(135deg, #059669, #065F46);
  }

  .order-action-btn.close {
    background-color: #4B5563;
    color: white;
  }

  .order-action-btn.close:hover {
    background-color: #374151;
  }

  /* Camera car driver buttons */
  .order-action-btn.camera-located {
    background: linear-gradient(135deg, #10B981, #047857);
    color: white;
  }

  .order-action-btn.camera-located:hover {
    background: linear-gradient(135deg, #059669, #065F46);
  }

  .order-action-btn.camera-not-present {
    background: linear-gradient(135deg, #F59E0B, #D97706);
    color: white;
  }

  .order-action-btn.camera-not-present:hover {
    background: linear-gradient(135deg, #D97706, #B45309);
  }

  .order-action-btn.camera-blocked {
    background: linear-gradient(135deg, #EF4444, #DC2626);
    color: white;
  }

  .order-action-btn.camera-blocked:hover {
    background: linear-gradient(135deg, #DC2626, #B91C1C);
  }

  .order-timestamp {
    font-size: 0.75rem;
    color: #9CA3AF;
    margin-top: 0.5rem;
  }

  .order-details-section {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #111827;
    border-radius: 8px;
    border: 1px solid #374151;
  }

  .order-details-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #60A5FA;
    margin-bottom: 0.75rem;
  }

  .order-details-text {
    color: #E5E7EB;
    font-size: 0.875rem;
    white-space: pre-wrap;
    line-height: 1.5;
  }

  /* Camera car action modal styles */
  .camera-action-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 15000;
    animation: fadeIn 0.3s ease-out;
  }

  .camera-action-content {
    background-color: #1F2937;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    border: 2px solid #3B82F6;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
    animation: slideUp 0.3s ease-out;
  }

  .camera-action-header {
    padding: 1rem;
    border-bottom: 1px solid #374151;
    background: linear-gradient(to right, #1F2937, #111827);
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .camera-action-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .camera-action-body {
    padding: 1.5rem;
  }

  .camera-form-group {
    margin-bottom: 1.5rem;
  }

  .camera-form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #E5E7EB;
    margin-bottom: 0.5rem;
  }

  .camera-form-textarea {
    width: 100%;
    min-height: 100px;
    padding: 0.75rem;
    border: 2px solid #374151;
    border-radius: 8px;
    background-color: #111827;
    color: white;
    font-size: 0.875rem;
    resize: vertical;
    transition: border-color 0.2s;
  }

  .camera-form-textarea:focus {
    outline: none;
    border-color: #3B82F6;
  }

  .camera-form-checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background-color: #111827;
    border-radius: 8px;
    border: 2px solid #374151;
    margin-bottom: 1rem;
  }

  .camera-form-checkbox {
    width: 20px;
    height: 20px;
    accent-color: #10B981;
  }

  .camera-photo-section {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #111827;
    border-radius: 8px;
    border: 2px solid #374151;
  }

  .camera-photo-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .camera-photo-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
  }

  .camera-photo-btn:hover {
    transform: translateY(-2px);
  }

  .camera-photo-btn.take-photo {
    background: linear-gradient(135deg, #3B82F6, #1E40AF);
    color: white;
  }

  .camera-photo-btn.upload-photo {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
  }

  .camera-photos-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .camera-photo-preview {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #374151;
  }

  .camera-photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .camera-photo-remove {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    background-color: rgba(239, 68, 68, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
  }

  .camera-action-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
  }

  .camera-submit-btn {
    flex: 1;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .camera-submit-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .camera-submit-btn.submit {
    background: linear-gradient(135deg, #10B981, #047857);
    color: white;
  }

  .camera-submit-btn.submit:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #065F46);
    transform: translateY(-2px);
  }

  .camera-cancel-btn {
    flex: 1;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    border: none;
    background-color: #4B5563;
    color: white;
    transition: all 0.2s;
  }

  .camera-cancel-btn:hover {
    background-color: #374151;
    transform: translateY(-2px);
  }

  /* Counter badges */
  .counter-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #DC2626;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid white;
  }

  /* Check-in history section */
  .checkin-history-section {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #111827;
    border-radius: 8px;
    border: 1px solid #374151;
  }

  .checkin-history-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #60A5FA;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .checkin-history-item {
    background-color: #1F2937;
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    border-left: 3px solid #3B82F6;
  }

  .checkin-history-item:last-child {
    margin-bottom: 0;
  }

  .checkin-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .checkin-history-action {
    font-weight: 600;
    color: #10B981;
  }

  .checkin-history-action.not_present {
    color: #F59E0B;
  }

  .checkin-history-action.blocked {
    color: #EF4444;
  }

  .checkin-history-time {
    font-size: 0.75rem;
    color: #9CA3AF;
  }

  .checkin-history-user {
    font-size: 0.75rem;
    color: #9CA3AF;
    margin-bottom: 0.5rem;
  }

  .checkin-history-notes {
    color: #E5E7EB;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  .checkin-history-vin {
    font-size: 0.75rem;
    color: #60A5FA;
    font-family: monospace;
  }

  /* Trail deletion styles with time range options */
  .trail-delete-section {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(239, 68, 68, 0.3);
  }

  .user-delete-warning {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 6px;
    padding: 8px;
    margin-bottom: 8px;
    font-size: 10px;
    color: #FCA5A5;
    text-align: center;
  }

  .user-delete-btn {
    width: 100%;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 11px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    background: linear-gradient(135deg, #DC2626, #B91C1C);
    color: white;
    margin-bottom: 8px;
  }

  .user-delete-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .user-delete-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #B91C1C, #991B1B);
    transform: translateY(-1px);
  }

  .admin-delete-section {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(239, 68, 68, 0.3);
  }

  .admin-delete-warning {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 6px;
    padding: 8px;
    margin-bottom: 8px;
    font-size: 10px;
    color: #FCA5A5;
    text-align: center;
  }

  .admin-delete-buttons {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .admin-delete-btn {
    width: 100%;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 11px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
  }

  .admin-delete-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .admin-delete-btn.delete-all {
    background: linear-gradient(135deg, #DC2626, #B91C1C);
    color: white;
  }

  .admin-delete-btn.delete-all:hover:not(:disabled) {
    background: linear-gradient(135deg, #B91C1C, #991B1B);
    transform: translateY(-1px);
  }

  .admin-delete-btn.delete-single {
    background: linear-gradient(135deg, #F59E0B, #D97706);
    color: white;
  }

  .admin-delete-btn.delete-single:hover:not(:disabled) {
    background: linear-gradient(135deg, #D97706, #B45309);
    transform: translateY(-1px);
  }

  .admin-delete-individual {
    margin-top: 8px;
    padding: 8px;
    background-color: rgba(31, 41, 55, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(156, 163, 175, 0.3);
  }

  .admin-delete-individual h5 {
    margin: 0 0 6px 0;
    font-size: 11px;
    color: #F59E0B;
    font-weight: bold;
  }

  .admin-delete-user-list {
    max-height: 100px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .admin-delete-user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 6px;
    background-color: rgba(55, 65, 81, 0.6);
    border-radius: 4px;
    font-size: 10px;
  }

  .admin-delete-user-info {
    flex: 1;
    color: #E5E7EB;
  }

  .admin-delete-user-btn {
    padding: 2px 6px;
    background-color: #EF4444;
    color: white;
    border: none;
    border-radius: 3px;
    font-size: 9px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .admin-delete-user-btn:hover {
    background-color: #DC2626;
    transform: scale(1.05);
  }

  /* Delete time range selection */
  .delete-time-range-selection {
    margin-bottom: 12px;
    padding: 8px;
    background-color: rgba(31, 41, 55, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(156, 163, 175, 0.3);
  }

  .delete-time-range-selection h5 {
    margin: 0 0 6px 0;
    font-size: 11px;
    color: #60A5FA;
    font-weight: bold;
  }

  .delete-time-options {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }

  .delete-time-option {
    background-color: rgba(31, 41, 55, 0.9);
    border: 1px solid #60A5FA;
    color: #F3F4F6;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    min-width: 32px;
    text-align: center;
  }

  .delete-time-option:hover {
    background-color: rgba(55, 65, 81, 1);
    transform: translateY(-1px);
  }

  .delete-time-option.active {
    background-color: #DC2626;
    color: white;
    border-color: #B91C1C;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.4);
  }

  .delete-time-option.all-time {
    background-color: #7F1D1D;
    border-color: #DC2626;
  }

  .delete-time-option.all-time.active {
    background-color: #991B1B;
    border-color: #7F1D1D;
  }

  /* Delete confirmation modal */
  .delete-confirmation-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 20000;
    animation: fadeIn 0.3s ease-out;
  }

  .delete-confirmation-content {
    background-color: #1F2937;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    border: 2px solid #DC2626;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
    animation: slideUp 0.3s ease-out;
  }

  .delete-confirmation-header {
    padding: 1rem;
    border-bottom: 1px solid #374151;
    background: linear-gradient(to right, #7F1D1D, #991B1B);
    border-radius: 10px 10px 0 0;
    text-align: center;
  }

  .delete-confirmation-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .delete-confirmation-body {
    padding: 1.5rem;
    text-align: center;
  }

  .delete-confirmation-message {
    color: #E5E7EB;
    margin-bottom: 1rem;
    line-height: 1.5;
  }

  .delete-confirmation-details {
    background-color: #111827;
    border: 1px solid #374151;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    text-align: left;
  }

  .delete-confirmation-buttons {
    display: flex;
    gap: 1rem;
  }

  .delete-confirmation-btn {
    flex: 1;
    padding: 0.75rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
  }

  .delete-confirmation-btn.confirm {
    background: linear-gradient(135deg, #DC2626, #B91C1C);
    color: white;
  }

  .delete-confirmation-btn.confirm:hover {
    background: linear-gradient(135deg, #B91C1C, #991B1B);
    transform: translateY(-2px);
  }

  .delete-confirmation-btn.cancel {
    background-color: #4B5563;
    color: white;
  }

  .delete-confirmation-btn.cancel:hover {
    background-color: #374151;
    transform: translateY(-2px);
  }

  /* Progress indicator */
  .delete-progress {
    margin-top: 1rem;
    padding: 0.75rem;
    background-color: #111827;
    border-radius: 8px;
    border: 1px solid #374151;
  }

  .delete-progress-bar {
    width: 100%;
    height: 8px;
    background-color: #374151;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }

  .delete-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #DC2626, #B91C1C);
    transition: width 0.3s ease;
  }

  .delete-progress-text {
    font-size: 0.75rem;
    color: #9CA3AF;
    text-align: center;
  }

  /* Alert integration compatibility styles */
  .alert-enabled-map {
    position: relative;
    padding-left: 20px;
    padding-right: 20px;
  }

  .map-container {
    position: relative;
    z-index: 1;
  }

  /* Mobile responsiveness */
  @media (max-width: 768px) {
    /* Full screen map adjustments */
    .vehicle-map-container {
      height: 100vh !important;
      min-height: 100vh !important;
    }

    .order-popup {
      width: 95%;
      margin: 1rem;
    }

    .order-info-grid {
      grid-template-columns: 1fr;
    }

    .order-info-item.full-width {
      grid-column: span 1;
    }

    .order-popup-actions {
      flex-direction: column;
    }

    .order-action-btn {
      min-width: auto;
    }

    .camera-action-content {
      width: 95%;
      margin: 1rem;
    }

    .camera-photo-buttons {
      grid-template-columns: 1fr;
    }

    .camera-action-buttons {
      flex-direction: column;
    }

    .delete-confirmation-content {
      width: 95%;
      margin: 1rem;
    }

    .delete-confirmation-buttons {
      flex-direction: column;
    }

    .delete-time-options {
      justify-content: center;
    }

    /* Enhanced mobile map controls - larger and more touch-friendly */
    .map-controls {
      bottom: 20px;
      right: 20px;
      gap: 12px;
    }

    .map-control-button {
      width: 56px;
      height: 56px;
      border-radius: 16px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.3);
    }

    .map-control-button svg {
      width: 28px;
      height: 28px;
    }

    /* Enhanced mobile night mode toggle */
    .map-control-button.night-mode-toggle {
      width: 60px;
      height: 60px;
      border-radius: 18px;
    }

    .map-control-button.night-mode-toggle svg {
      width: 30px;
      height: 30px;
    }

    /* Enhanced mobile location button */
    .map-control-button.location-btn {
      width: 58px;
      height: 58px;
      border-radius: 17px;
    }

    .map-control-button.location-btn svg {
      width: 29px;
      height: 29px;
    }

    /* Enhanced mobile refresh button */
    .map-control-button.refresh-btn {
      width: 58px;
      height: 58px;
      border-radius: 17px;
    }

    .map-control-button.refresh-btn svg {
      width: 29px;
      height: 29px;
    }
  }

  /* Tablet responsiveness */
  @media (min-width: 769px) and (max-width: 1024px) {
    /* Full screen map adjustments */
    .vehicle-map-container {
      height: 100vh !important;
      min-height: 100vh !important;
    }

    /* Medium-sized controls for tablets */
    .map-controls {
      bottom: 18px;
      right: 18px;
      gap: 10px;
    }

    .map-control-button {
      width: 50px;
      height: 50px;
      border-radius: 14px;
    }

    .map-control-button svg {
      width: 26px;
      height: 26px;
    }

    .map-control-button.night-mode-toggle {
      width: 52px;
      height: 52px;
      border-radius: 15px;
    }

    .map-control-button.night-mode-toggle svg {
      width: 27px;
      height: 27px;
    }
  }

  /* Desktop responsiveness */
  @media (min-width: 1025px) {
    /* Full screen map adjustments */
    .vehicle-map-container {
      height: 100vh !important;
      min-height: 100vh !important;
    }
  }

  /* Enhanced mobile touch interactions */
  @media (max-width: 768px) {
    /* Improve marker visibility and touch targets on mobile */
    .vehicle-marker-icon {
      width: 36px !important;
      height: 36px !important;
      border-width: 3px !important;
    }

    .order-marker-icon {
      width: 42px !important;
      height: 42px !important;
      border-width: 3px !important;
    }

    .teammate-marker-icon {
      width: 32px !important;
      height: 32px !important;
      border-width: 3px !important;
    }

    /* Enhanced popup styles for mobile */
    .leaflet-popup-content-wrapper {
      border-radius: 12px !important;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
    }

    .leaflet-popup-content {
      margin: 16px 20px !important;
      font-size: 16px !important;
      line-height: 1.5 !important;
    }

    /* Better zoom controls for mobile */
    .leaflet-control-zoom {
      margin-right: 80px !important;
      margin-bottom: 20px !important;
    }

    .leaflet-control-zoom a {
      width: 40px !important;
      height: 40px !important;
      line-height: 40px !important;
      font-size: 20px !important;
      border-radius: 8px !important;
    }

    /* Improve attribution positioning on mobile */
    .leaflet-control-attribution {
      font-size: 10px !important;
      background: rgba(255,255,255,0.8) !important;
      padding: 2px 6px !important;
      border-radius: 4px !important;
    }
  }

  /* Landscape mobile optimizations */
  @media (max-width: 768px) and (orientation: landscape) {
    .map-controls {
      bottom: 15px;
      right: 15px;
      gap: 8px;
    }

    .map-control-button {
      width: 48px;
      height: 48px;
    }

    .map-control-button svg {
      width: 24px;
      height: 24px;
    }
  }

  /* High DPI display optimizations */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .map-control-button {
      border-width: 1px;
    }

    .vehicle-marker-icon, .order-marker-icon, .teammate-marker-icon {
      border-width: 2px;
    }
  }
`;