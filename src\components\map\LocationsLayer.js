import React, { useContext, useEffect, useRef } from 'react';
import { collection, onSnapshot, doc, deleteDoc } from 'firebase/firestore';
import L from 'leaflet';
// In all components
import { MapContext } from '../MapContext';

const LocationsLayer = () => {
  // Get context from Map component
  const {
    mapRef,
    firestoreRef,
    markerClusterRef,
    currentUser,
    isAdmin,
    isTowTruckUser,
    locations,
    setLocations,
    selectedLocation,
    setSelectedLocation,
    setDetailsPanelLocation,
    setDetailsVisible,
    homeLocation,
    setHomeLocation,
    currentLocation,
    findAndUpdateClosestPendingLocation,
    setError
  } = useContext(MapContext);
  
  // Ref to store unsubscribe function for Firestore listener
  const locationsListenerRef = useRef(null);
  
  // Set up listeners for locations
  useEffect(() => {
    if (!firestoreRef.current) return;
    
    // Clean up any existing listener
    if (locationsListenerRef.current) {
      locationsListenerRef.current();
    }
    
    console.log("Setting up real-time listener for locations");
    
    // Setup real-time listener for locations
    locationsListenerRef.current = onSnapshot(
      collection(firestoreRef.current, 'locations'),
      (snapshot) => {
        const fetchedLocations = [];
        let foundHomeLocation = false;
        
        snapshot.forEach((doc) => {
          const data = doc.data();
          const location = {
            id: doc.id,
            name: data.name,
            position: {
              lat: data.position.lat,
              lng: data.position.lng
            },
            isAdminOnly: data.isAdminOnly || false,
            isPriority: data.isPriority || false,
            details: data.details || '',
            images: data.images || [],
            parkingSide: data.parkingSide || null,
            createdBy: data.createdBy || null,
            createdAt: data.createdAt || null,
            address: data.address || '',
            intersection: data.intersection || '',
            status: data.status || 'pending',
            pickedUpBy: data.pickedUpBy || null,
            pickedUpAt: data.pickedUpAt || null,
            isHome: data.isHome || false,
            // Vehicle-specific fields
            plateNumber: data.plateNumber || '',
            vin: data.vin || '',
            driveType: data.driveType || '',
            make: data.make || '',
            model: data.model || '',
            year: data.year || '',
            case: data.case || '',
            mileage: data.mileage || ''
          };
          
          fetchedLocations.push(location);
          
          // Check if this is the "Home" location
          if (location.name === "Home" || location.isHome) {
            setHomeLocation(location);
            foundHomeLocation = true;
          }
        });
        
        setLocations(fetchedLocations);
        console.log("Fetched locations:", fetchedLocations.length);
        
        // Update markers on map
        updateMapMarkers(fetchedLocations);
        
        // If tow truck user, update closest pending location
        if (isTowTruckUser && currentLocation) {
          findAndUpdateClosestPendingLocation(currentLocation);
        }
        
        // If we have no selected location, and we have locations, select the closest one
        if (!selectedLocation && fetchedLocations.length > 0) {
          selectClosestLocation(currentLocation);
        }
      },
      (error) => {
        console.error("Error in locations listener:", error);
        setError("Lost connection to location data. Please refresh the page.");
      }
    );
    
    // Cleanup listener on unmount
    return () => {
      if (locationsListenerRef.current) {
        locationsListenerRef.current();
        locationsListenerRef.current = null;
      }
    };
  }, [firestoreRef.current]);
  
  // Update markers when locations state or selectedLocation changes
  useEffect(() => {
    updateMapMarkers(locations);
  }, [locations, selectedLocation]);
  
  // Handle case where selected location is deleted
  useEffect(() => {
    if (selectedLocation) {
      const stillExists = locations.some(loc => loc.id === selectedLocation.id);
      if (!stillExists) {
        setSelectedLocation(null);
        setDetailsPanelLocation(null);
      } else {
        // Update selected location with latest data
        const updatedLocation = locations.find(loc => loc.id === selectedLocation.id);
        if (updatedLocation) {
          setSelectedLocation(updatedLocation);
          
          // Also update details panel if it's showing the same location
          if (detailsPanelLocation && detailsPanelLocation.id === updatedLocation.id) {
            setDetailsPanelLocation(updatedLocation);
          }
        }
      }
    }
  }, [locations, selectedLocation]);

  // Create or update a marker
  const createMarker = (location, isSelected) => {
    try {
      // Only proceed if map and cluster are available
      if (!mapRef.current || !markerClusterRef.current) return null;

      // Determine marker appearance based on type, status, and selected state
      const markerClass = isSelected ? 'selected' : (location.status === 'picked-up' ? 'picked-up' : (location.isAdminOnly ? 'admin' : 'regular'));
      const priorityClass = location.isPriority ? 'priority' : '';
      
      // Create HTML for marker based on whether it has images
      let markerHtml = '';
      
      if (location.images && location.images.length > 0) {
        // Use the first image as marker background
        markerHtml = `<div class="location-marker-image ${markerClass} ${priorityClass}" style="background-image: url('${location.images[0]}');">`;
        
        // Add status indicator if picked up
        if (location.status === 'picked-up') {
          markerHtml += `<div class="location-status">Picked Up</div>`;
        }
        
        markerHtml += `</div>`;
      } else {
        // Use fallback with first letter of location name
        const firstLetter = location.name.charAt(0).toUpperCase();
        markerHtml = `<div class="location-marker-fallback ${markerClass} ${priorityClass}">`;
        
        // Add status indicator if picked up
        if (location.status === 'picked-up') {
          markerHtml += `<div class="location-status">Picked Up</div>`;
        }
        
        markerHtml += `${firstLetter}</div>`;
      }
      
      // Create icon
      const icon = L.divIcon({
        className: 'location-marker',
        html: markerHtml,
        iconSize: [40, 40],
        iconAnchor: [20, 20]
      });
      
      // Create marker and add to layer
      const marker = L.marker([location.position.lat, location.position.lng], {
        icon: icon,
        title: location.name,
        id: location.id,
        zIndexOffset: isSelected ? 800 : (location.status === 'picked-up' ? 750 : (location.isAdminOnly ? 700 : 600))
      });
      
      // Add click handler to show in details panel
      marker.on('click', () => {
        selectLocation(location);
      });
      
      // Add marker to cluster
      markerClusterRef.current.addLayer(marker);
      
      return marker;
    } catch (err) {
      console.error("Error creating marker:", err);
      return null;
    }
  };

  // Update markers when locations change
  const updateMapMarkers = (locationsArray) => {
    if (!mapRef.current || !markerClusterRef.current) return;
    
    try {
      // Clear existing markers
      markerClusterRef.current.clearLayers();
      
      // Create new markers
      locationsArray.forEach(location => {
        // Skip admin-only markers if user is not admin
        if (location.isAdminOnly && !isAdmin) return;
        
        const isSelected = selectedLocation && selectedLocation.id === location.id;
        createMarker(location, isSelected);
      });
    } catch (err) {
      console.error("Error updating map markers:", err);
    }
  };

  // Select a location and show details
  const selectLocation = (location) => {
    setSelectedLocation(location);
    setDetailsPanelLocation(location);
    setDetailsVisible(true);
    
    // Ensure selected location is visible on the map
    if (mapRef.current) {
      try {
        // Get current bounds
        const bounds = mapRef.current.getBounds();
        
        // Check if location is visible
        if (!bounds.contains([location.position.lat, location.position.lng])) {
          // If not visible, pan to include it
          mapRef.current.panTo([location.position.lat, location.position.lng]);
        }
      } catch (err) {
        console.warn("Error ensuring location is visible:", err);
      }
    }
  };
  
  // Helper function to calculate distance between two points
  const calculateDistance = (point1, point2) => {
    const R = 3958.8; // Earth's radius in MILES
    const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
    const φ2 = point2.lat * Math.PI/180;
    const Δφ = (point2.lat-point1.lat) * Math.PI/180;
    const Δλ = (point2.lng-point1.lng) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    const d = R * c; // in MILES
    return d;
  };
  
  // Find closest location
  const findClosestLocation = (fromPosition, locationsList) => {
    if (!locationsList || locationsList.length === 0) return null;
    
    let closestLocation = null;
    let shortestDistance = Infinity;
    
    for (const location of locationsList) {
      const distance = calculateDistance(fromPosition, location.position);
      if (distance < shortestDistance) {
        shortestDistance = distance;
        closestLocation = location;
      }
    }
    
    return { location: closestLocation, distance: shortestDistance };
  };
  
  // Select the closest location
  const selectClosestLocation = (position) => {
    if (!position) return null;
    
    // Filter out admin-only locations if user is not admin
    const accessibleLocations = isAdmin 
      ? locations 
      : locations.filter(loc => !loc.isAdminOnly);
    
    if (accessibleLocations.length === 0) return null;
    
    const closest = findClosestLocation(position, accessibleLocations);
    if (closest && closest.location) {
      selectLocation(closest.location);
      return closest.location;
    }
    return null;
  };
  
  // Delete a marker from Firestore
  const deleteLocation = async (id) => {
    try {
      if (!firestoreRef.current) {
        console.error("Firestore reference not available");
        return;
      }
      
      // Delete from Firestore
      await deleteDoc(doc(firestoreRef.current, 'locations', id));
      
      console.log("Deleted location:", id);
      
      // If this was the selected location, clear selection
      if (selectedLocation && selectedLocation.id === id) {
        setSelectedLocation(null);
        setDetailsPanelLocation(null);
      }
    } catch (err) {
      console.error("Error deleting marker from Firestore:", err);
      setError("Failed to delete location. Please try again.");
    }
  };

  // This component doesn't render any visible UI directly
  return null;
};

export default LocationsLayer;