import React, { useState } from 'react';

const OpenOrders = ({
  openOrders = [],
  selectedLocation = null,
  showDetailsOnly,
  handleStartNavigation,
  geocodeLocation,
  currentLocation = { lat: 0, lng: 0 },
  calculateDistance,
  formatDistance,
  sectionHeight = 200,
  geocodingLocationId = null
}) => {
  // State for section expansion/collapse
  const [isExpanded, setIsExpanded] = useState(true);

  // Format distance for display
  const formatDistanceLocal = (distance) => {
    if (typeof formatDistance === 'function') {
      return formatDistance(distance);
    }
    
    if (distance < 0.1) {
      return `${Math.round(distance * 5280)} ft`;
    } else {
      return `${distance.toFixed(1)} mi`;
    }
  };

  // Get vehicle image URL
  const getVehicleImageUrl = (item) => {
    const make = encodeURIComponent(item.location?.make || item.make || '');
    const model = encodeURIComponent(item.location?.model || item.model || '');
    const year = item.location?.year || item.year || '';
    
    if (make && model) {
      return `https://cdn.imagin.studio/getimage?customer=img&make=${make}&modelFamily=${model}&year=${year}&angle=1`;
    }
    
    // Default car image as fallback
    return `data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="60" viewBox="0 0 100 60"><rect width="100" height="60" fill="%231A2642" /><path d="M20 40 L30 30 L70 30 L80 40 L80 45 L75 45 A5 5 0 0 1 65 45 L35 45 A5 5 0 0 1 25 45 L20 45 Z" fill="%23374151"/><path d="M30 30 L35 20 L65 20 L70 30" fill="%234B5563"/><circle cx="30" cy="45" r="5" fill="%231F2937"/><circle cx="70" cy="45" r="5" fill="%231F2937"/></svg>`;
  };

  // Helper function to get appropriate status label and style
  const getStatusStyle = (status) => {
    let displayStatus = status;
    
    if (status === 'pending') {
      displayStatus = 'open-order';
    }
    
    switch (displayStatus) {
      case 'picked-up':
        return { label: 'Picked Up', bg: 'bg-green-600', text: 'text-white' };
      case 'pending-pickup':
        return { label: 'Pending Pickup', bg: 'bg-yellow-600', text: 'text-white' };
      case 'awaiting-pickup':
        return { label: 'Awaiting Pickup', bg: 'bg-orange-600', text: 'text-white' };
      case 'open-order':
        return { label: 'Open Order', bg: 'bg-blue-600', text: 'text-white' };
      case 'completed':
        return { label: 'Completed', bg: 'bg-gray-600', text: 'text-white' };
      default:
        return { label: status, bg: 'bg-gray-600', text: 'text-white' };
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    try {
      const date = typeof dateString === 'object' && dateString.toDate 
        ? dateString.toDate() 
        : new Date(dateString);
      
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (error) {
      console.error("Error formatting date:", error);
      return 'Invalid date';
    }
  };

  // Check if location has valid GPS coordinates
  const hasValidCoordinates = (location) => {
    const pos = location?.position || location;
    return pos && 
           typeof pos.lat === 'number' && 
           typeof pos.lng === 'number' &&
           !isNaN(pos.lat) &&
           !isNaN(pos.lng);
  };

  const totalCount = openOrders.length;

  return (
    <div className="border-b border-gray-700 w-full mb-3 overflow-hidden">
      <div className="p-2 flex justify-between items-center bg-gray-800 w-full rounded-t">
        <div className="flex items-center min-w-0">
          <span className="text-xs font-medium text-gray-300 truncate">OPEN ORDERS</span>
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-2 text-gray-400 hover:text-white flex-shrink-0"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-4 w-4 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
        <span className="text-xs text-gray-400 truncate ml-2">
          {totalCount} orders
        </span>
      </div>
      
      {isExpanded && (
        <div 
          className="overflow-y-auto overflow-x-hidden w-full bg-gray-900 rounded-b"
          style={{ maxHeight: `${sectionHeight}px` }}
        >
          {totalCount > 0 ? (
            <div className="p-2 w-full">
              <ol className="list-decimal list-inside text-sm">
                {openOrders
                  .sort((a, b) => {
                    // Sort by distance
                    const posA = a.position || a;
                    const posB = b.position || b;
                    
                    if (!hasValidCoordinates(posA) && hasValidCoordinates(posB)) return 1;
                    if (hasValidCoordinates(posA) && !hasValidCoordinates(posB)) return -1;
                    if (!hasValidCoordinates(posA) && !hasValidCoordinates(posB)) return 0;
                    
                    const distA = calculateDistance(currentLocation, posA);
                    const distB = calculateDistance(currentLocation, posB);
                    return distA - distB;
                  })
                  .map((location, index) => {
                    const statusStyle = getStatusStyle(location.status);
                    const pos = location.position || location;
                    const distance = hasValidCoordinates(pos) ? calculateDistance(currentLocation, pos) : null;
                    const hasCoordinates = hasValidCoordinates(pos);
                    
                    return (
                      <li 
                        key={location.id} 
                        className={`py-2 ${selectedLocation && selectedLocation.id === location.id ? 'bg-gray-700' : ''} hover:bg-gray-700 border-b border-gray-700 last:border-0 rounded overflow-hidden`}
                      >
                        <div className="flex items-start justify-between gap-2">
                          {/* Clicking this area ONLY shows details - NEVER navigates */}
                          <div 
                            className="flex items-start gap-2 flex-1 cursor-pointer min-w-0"
                            onClick={() => showDetailsOnly(location)}
                          >
                            {/* Car thumbnail */}
                            <div className="w-14 h-14 rounded-md overflow-hidden flex-shrink-0 bg-gray-800 border border-gray-700">
                              <img
                                src={getVehicleImageUrl(location)}
                                alt={`${location.make || 'Vehicle'} ${location.model || ''}`}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  e.target.onerror = null;
                                  e.target.src = getVehicleImageUrl({});
                                }}
                              />
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-sm truncate pr-2">
                                <span className="inline-block max-w-full">{`${index + 1}. ${location.name}`}</span>
                              </div>
                              
                              <div className="flex items-center mt-1 flex-wrap gap-1">
                                {/* Status badge */}
                                <span className={`${statusStyle.bg} ${statusStyle.text} text-xs px-2 py-0.5 rounded-full flex items-center flex-shrink-0`}>
                                  <span className="truncate max-w-[100px] sm:max-w-none">{statusStyle.label}</span>
                                </span>
                                
                                {/* Distance */}
                                {distance !== null && (
                                  <span className="text-xs text-gray-400 flex-shrink-0">
                                    {formatDistanceLocal(distance)}
                                  </span>
                                )}
                              </div>
                              
                              {/* Vehicle details */}
                              {location.make && location.model && (
                                <div className="text-xs text-gray-400 mt-1 truncate">
                                  {location.year} {location.make} {location.model}
                                  {location.plateNumber && ` • ${location.plateNumber}`}
                                </div>
                              )}
                              
                              {/* Order created time if available */}
                              {location.createdAt && (
                                <div className="text-xs text-gray-500 mt-1 truncate">
                                  Added: {formatDate(location.createdAt)}
                                </div>
                              )}
                              
                              {/* GPS status indicator */}
                              <div className="flex items-center mt-1">
                                <span className={`w-2 h-2 rounded-full mr-1 flex-shrink-0 ${hasCoordinates ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                                <span className="text-xs text-gray-400 truncate">
                                  {hasCoordinates ? 'GPS Available' : location.address || 'No GPS'}
                                </span>
                              </div>
                            </div>
                          </div>
                          
                          {/* Navigation buttons with responsive layout */}
                          <div className="flex flex-col sm:flex-row items-end sm:items-center gap-1 flex-shrink-0">
                            {!hasCoordinates && location.address && (
                              <button 
                                className={`text-xs bg-yellow-600 hover:bg-yellow-700 text-white px-2 py-1 rounded whitespace-nowrap min-w-[65px] text-center flex items-center justify-center ${geocodingLocationId === location.id ? 'opacity-70 cursor-wait' : ''}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  geocodeLocation(location);
                                }}
                                disabled={geocodingLocationId === location.id}
                              >
                                {geocodingLocationId === location.id ? (
                                  <>
                                    <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span className="hidden sm:inline">Geocoding</span>
                                    <span className="sm:hidden">...</span>
                                  </>
                                ) : (
                                  <>Geocode</>
                                )}
                              </button>
                            )}
                            
                            {/* Navigate button */}
                            <button 
                              className={`text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded whitespace-nowrap min-w-[65px] text-center ${!hasCoordinates ? 'opacity-60' : ''}`}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStartNavigation(location);
                              }}
                              disabled={!hasCoordinates}
                            >
                              Navigate
                            </button>
                          </div>
                        </div>
                      </li>
                    );
                  })}
              </ol>
            </div>
          ) : (
            <div className="p-4 text-center text-gray-400">
              No open orders available.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default OpenOrders;