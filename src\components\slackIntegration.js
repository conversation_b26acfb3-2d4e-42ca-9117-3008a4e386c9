// src/components/slackIntegration.js
// Slack integration utilities for vehicle tracker

/**
 * Formats a vehicle object for Slack messages
 * @param {Object} vehicle - The vehicle data to format
 * @returns {Object} - Formatted vehicle data for Slack
 */
export const formatVehicleForSlack = (vehicle) => {
  // Create a clean copy of the vehicle data to send to Slack
  return {
    id: vehicle.id,
    vehicle: vehicle.vehicle,
    vin: vehicle.vin,
    vinVerified: vehicle.vinVerified || false,
    plateNumber: vehicle.plateNumber,
    accountNumber: vehicle.accountNumber,
    financier: vehicle.financier,
    address: vehicle.address,
    position: vehicle.position,
    date: vehicle.date,
    status: vehicle.status,
    doNotSecureReason: vehicle.doNotSecureReason, // Include DO NOT SECURE reason
    bottomStatus: vehicle.bottomStatus,
    bottomStatusCount: vehicle.bottomStatusCount,
    cantSecureReason: vehicle.cantSecureReason,
    notes: vehicle.notes,
    color: vehicle.color,
    driveType: vehicle.driveType,
    teamMemberId: vehicle.teamMemberId,
    teamMemberName: vehicle.teamMemberName,
    weekId: vehicle.weekId,
    weekRange: vehicle.weekRange,
    inRouteDriverId: vehicle.inRouteDriverId,
    inRouteDriverName: vehicle.inRouteDriverName,
    inRouteTimestamp: vehicle.inRouteTimestamp?.toDate?.() 
      ? vehicle.inRouteTimestamp.toDate().toISOString() 
      : vehicle.inRouteTimestamp,
    arrivedDriverId: vehicle.arrivedDriverId,
    arrivedDriverName: vehicle.arrivedDriverName,
    arrivedTimestamp: vehicle.arrivedTimestamp?.toDate?.() 
      ? vehicle.arrivedTimestamp.toDate().toISOString() 
      : vehicle.arrivedTimestamp,
    securedByTeammate: vehicle.securedByTeammate,
    securedByUserId: vehicle.securedByUserId,
    securedByUserName: vehicle.securedByUserName,
    securedDate: vehicle.securedDate,
    securedTimestamp: vehicle.securedTimestamp?.toDate?.() 
      ? vehicle.securedTimestamp.toDate().toISOString() 
      : vehicle.securedTimestamp,
    images: (vehicle.images || []).slice(0, 5).map(img => ({
      url: img.url,
      timestamp: img.timestamp
    }))
  };
};

/**
 * Sends a vehicle update to Slack via the webhook
 * @param {string} teamId - ID of the team (can be document ID or area ID)
 * @param {Object} vehicle - The vehicle data to send
 * @param {string} updateType - Type of update ('new', 'in_route', 'arrived', 'secured', 'bottom_status')
 * @returns {Promise<Object>} - Response from the webhook
 */
export const postVehicleToSlack = async (teamId, vehicle, updateType) => {
  // teamId is optional if user has personal channels
  if (!vehicle) {
    console.error("Missing vehicle data for Slack webhook");
    return { error: "Missing vehicle data" };
  }

  // Webhook endpoint URL - Update this to match your actual deployment
  const webhookUrl = process.env.REACT_APP_SLACK_WEBHOOK_URL || 
    'http://localhost:3001/api/slack/vehicle-update';

  try {
    console.log(`Posting ${updateType} update to Slack for vehicle ${vehicle.vehicle}`);
    
    // Include teamId in the request - the webhook will handle routing based on user preferences
    const requestBody = {
      teamId: teamId || null, // Can be null if user has personal channels
      vehicle: formatVehicleForSlack(vehicle), // Use the formatter to ensure all fields are included
      updateType
    };

    console.log('Sending to Slack webhook:', requestBody);

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    const responseText = await response.text();
    let responseData;
    
    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      // If response isn't JSON, treat it as an error
      responseData = { error: responseText };
    }

    if (!response.ok) {
      console.error(`Error posting to Slack webhook: ${response.status}`, responseData);
      
      // Check for specific error messages
      if (responseData.error && responseData.error.includes('No channels configured')) {
        console.log('⚠️  No Slack channels configured for updates');
        console.log('   This could mean:');
        console.log('   1. Team channel not linked: Run "/vehicle setup link [team-name]" in Slack');
        console.log('   2. Personal channel not linked: Run "/vehicle setup user" in your personal Slack channel');
        console.log('   3. Check your posting preference: Run "/vehicle posting" in Slack');
        
        return { 
          success: false, 
          status: response.status, 
          error: 'No Slack channels configured for updates',
          userMessage: 'Slack notifications are not configured. Either:\n' +
                      '1. Ask your admin to run "/vehicle setup link [team-name]" in a team channel\n' +
                      '2. Run "/vehicle setup user" in your personal Slack channel\n' +
                      '3. Check your posting preference with "/vehicle posting"'
        };
      }
      
      return { 
        success: false, 
        status: response.status, 
        error: responseData.error || 'Unknown error',
        details: responseData
      };
    }

    console.log("Successfully posted to Slack", responseData);
    return { 
      success: true,
      ...responseData
    };
  } catch (error) {
    console.error("Error sending to Slack webhook:", error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

/**
 * Check if the Slack integration is configured for a team or user
 * @param {string} teamId - ID of the team to check (optional)
 * @param {string} userId - ID of the user to check (optional)
 * @returns {Promise<Object>} - Status of the Slack integration
 */
export const checkSlackIntegration = async (teamId, userId) => {
  try {
    // For now, we'll assume integration exists if we have either teamId or userId
    // The actual check happens when posting
    if (teamId || userId) {
      return { 
        configured: true, 
        message: 'Slack integration available. Actual channel configuration will be checked on post.' 
      };
    }
    
    return { 
      configured: false, 
      error: "No team or user ID provided" 
    };
  } catch (error) {
    console.error("Error checking Slack integration:", error);
    return { 
      configured: false, 
      error: error.message 
    };
  }
};

/**
 * Send a test message to Slack for a team or user
 * @param {string} teamId - ID of the team (optional)
 * @param {string} userId - ID of the user (optional)
 * @param {string} message - Optional test message
 * @returns {Promise<Object>} - Response from the webhook
 */
export const sendTestMessageToSlack = async (teamId, userId, message = "Test message from Vehicle Tracker") => {
  // Create a test vehicle object
  const testVehicle = {
    id: 'test-' + Date.now(),
    vehicle: 'Test Vehicle',
    vin: 'TEST01',
    vinVerified: true,
    status: 'PENDING PICKUP',
    date: new Date().toISOString().split('T')[0],
    teamMemberName: 'Test User',
    teamMemberId: userId || 'test-user',
    notes: message
  };

  return postVehicleToSlack(teamId, testVehicle, 'test');
};

/**
 * Get user's Slack posting preference
 * @param {Object} userProfile - User profile data from Firebase
 * @returns {string} - 'team', 'personal', or 'both'
 */
export const getUserSlackPreference = (userProfile) => {
  return userProfile?.slackPostingPreference || 'team';
};

/**
 * Format a helpful message about Slack setup
 * @param {string} preference - User's current posting preference
 * @returns {string} - Helpful setup message
 */
export const getSlackSetupMessage = (preference = 'team') => {
  const messages = {
    team: 'Your updates will post to the team channel. Make sure your team has a channel linked with "/vehicle setup link [team-name]"',
    personal: 'Your updates will post to your personal channels. Link channels with "/vehicle setup user" in Slack',
    both: 'Your updates will post to both team and personal channels. Make sure both are configured.'
  };
  
  return messages[preference] || messages.team;
};

/**
 * List available teams (for debugging)
 * @returns {Promise<Object>} - List of available teams
 */
export const listAvailableTeams = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/teams/list');
    if (!response.ok) {
      throw new Error(`Server returned ${response.status}: ${await response.text()}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error listing teams:", error);
    return { error: error.message };
  }
};