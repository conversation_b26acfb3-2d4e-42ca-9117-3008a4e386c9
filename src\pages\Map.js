import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { 
  doc, 
  collection, 
  getDocs, 
  addDoc, 
  deleteDoc, 
  query, 
  where, 
  updateDoc,
  serverTimestamp,
  getDoc,
  orderBy,
  limit,
  onSnapshot,
  Timestamp, 
  getFirestore, 
  setDoc
} from 'firebase/firestore';
import { useNavigate } from 'react-router-dom';

// Import the AuthContext hook to share authentication state
import { useAuth } from '../contexts/AuthContext';

// Import our components
import MapDisplay from './MapDisplay';
import DetailsPanel from './DetailsPanel';
import ChatInterface from './ChatInterface';
import LocationsPanel from './LocationsPanel';
import Stats from './Stats';
import VehicleFinder from './VehicleFinder';
import TeamSelector from './TeamSelector';
import NavigationHUD from './NavigationHud';
import AlertComp from './AlertComp';
import CameraStatusIndicators from './CameraStatusIndicators';
import TeamZonesButton from './TeamZonesButton';
import ZoneDetection from './ZoneDetection';
import ActiveZoneStatus from './ActiveZoneStatus';
import './teamZones.css';

// UPDATED: Import everything from consolidated firebase.js
import { 
  db, 
  subscribeToTeamLocations,
  subscribeToTeamUserLocations,
  updateUserLocation,
  updateUserOnlineStatus,
  subscribeToTeamUserTraces,
  updateUserTrace,
  clearUserTrace,
  getUserProfile,
  getUserClockStatus,
  getTeamData,
  validateTeamAccess,
  getUserTeams,
  createTeam,
  addUserToTeam
} from './firebase';

// Default map center
const defaultPosition = { lat: 37.7749, lng: -122.4194 };

// ENHANCED: User trail color assignment system
const USER_TRAIL_COLORS = [
  '#FF6B6B', // Red
  '#4ECDC4', // Teal
  '#45B7D1', // Blue
  '#96CEB4', // Green
  '#FFEAA7', // Yellow
  '#DDA0DD', // Plum
  '#98D8C8', // Mint
  '#F7DC6F', // Light Yellow
  '#BB8FCE', // Light Purple
  '#85C1E9', // Light Blue
  '#F8C471', // Orange
  '#82E0AA', // Light Green
  '#F1948A', // Light Red
  '#85C0C7', // Light Teal
  '#D7BDE2', // Lavender
  '#A9CCE3', // Powder Blue
  '#F9E79F', // Cream
  '#A3E4D7', // Aqua
  '#FADBD8', // Pink
  '#D5DBDB'  // Light Gray
];

// Function to get consistent color for a user
const getUserTrailColor = (userId) => {
  if (!userId) return USER_TRAIL_COLORS[0];
  
  // Create a simple hash from userId to ensure consistent colors
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    const char = userId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  const colorIndex = Math.abs(hash) % USER_TRAIL_COLORS.length;
  return USER_TRAIL_COLORS[colorIndex];
};

// Utility functions - same as before, not changed
const calculateDistance = (point1, point2) => {
  if (!point1 || !point2) return 0;
  
  const R = 3958.8; // Earth's radius in MILES
  const φ1 = point1.lat * Math.PI/180;
  const φ2 = point2.lat * Math.PI/180;
  const Δφ = (point2.lat-point1.lat) * Math.PI/180;
  const Δλ = (point2.lng-point1.lng) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  const d = R * c; // in MILES
  return d;
};

const findClosestLocation = (fromPosition, locationsList) => {
  if (!locationsList || locationsList.length === 0) return null;
  
  let closestLocation = null;
  let shortestDistance = Infinity;
  
  for (const location of locationsList) {
    const distance = calculateDistance(fromPosition, location.position);
    if (distance < shortestDistance) {
      shortestDistance = distance;
      closestLocation = location;
    }
  }
  
  return { location: closestLocation, distance: shortestDistance };
};

const optimizeRoute = (startPosition, locationsList) => {
  if (!locationsList || locationsList.length === 0) return [];
  
  const unvisitedLocations = [...locationsList];
  const optimizedRoute = [];
  let currentPosition = startPosition;
  
  while (unvisitedLocations.length > 0) {
    const { location: closestLocation, distance } = findClosestLocation(currentPosition, unvisitedLocations);
    
    if (closestLocation) {
      optimizedRoute.push({
        location: closestLocation,
        distanceFromPrevious: distance
      });
      
      const index = unvisitedLocations.findIndex(loc => loc.id === closestLocation.id);
      if (index !== -1) {
        unvisitedLocations.splice(index, 1);
      }
      currentPosition = closestLocation.position;
    }
  }
  
  return optimizedRoute;
};

const formatDistance = (miles) => {
  if (miles === null || miles === undefined) return "Unknown distance";
  
  if (miles < 0.1) {
    return `${Math.round(miles * 5280)} feet`;
  } else {
    return `${miles.toFixed(1)} miles`;
  }
};

const formatTime = (seconds) => {
  if (seconds === null || seconds === undefined) return "Unknown time";
  
  if (seconds < 60) {
    return `${Math.round(seconds)} seconds`;
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)} minutes`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
};

const formatClockTime = (date) => {
  if (!date) return '';
  return date.toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: true
  });
};

const setupOrientationHandler = (mapRef) => {
  let lastOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
  
  const handleOrientationChange = () => {
    setTimeout(() => {
      const currentOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
      
      if (currentOrientation !== lastOrientation) {
        console.log(`Orientation changed to ${currentOrientation}`);
        lastOrientation = currentOrientation;
        
        if (mapRef?.current) {
          mapRef.current.invalidateSize({
            animate: false,
            pan: false,
            debounceMoveend: true
          });
          
          setTimeout(() => {
            if (mapRef.current) {
              mapRef.current.invalidateSize();
            }
          }, 500);
        }
        
        window.dispatchEvent(new CustomEvent('orientationChanged', {
          detail: { orientation: currentOrientation }
        }));
      }
    }, 100);
  };
  
  const handleResizeForOrientation = () => {
    const currentOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
    if (currentOrientation !== lastOrientation) {
      handleOrientationChange();
    }
  };
  
  window.addEventListener('orientationchange', handleOrientationChange);
  window.addEventListener('resize', handleResizeForOrientation);
  
  const isIPad = () => {
    return (
      /iPad/.test(navigator.userAgent) || 
      (/Macintosh/.test(navigator.userAgent) && 'ontouchend' in document)
    ) && window.innerWidth >= 768 && window.innerWidth <= 1024;
  };
  
  if (isIPad()) {
    document.body.classList.add('ipad');
    
    const setCustomVh = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };
    
    setCustomVh();
    window.addEventListener('resize', setCustomVh);
  }
  
  return () => {
    window.removeEventListener('orientationchange', handleOrientationChange);
    window.removeEventListener('resize', handleResizeForOrientation);
  };
};

const ensureUserInTeam = async (db, userId, teamId, roleName = 'member') => {
  try {
    console.log(`Ensuring user ${userId} is added to team ${teamId}...`);
    
    const membersCollection = collection(db, `teams/${teamId}/teamMembers`);
    const memberQuery = query(membersCollection, where("userId", "==", userId));
    const memberSnapshot = await getDocs(memberQuery);
    
    if (memberSnapshot.empty) {
      console.log(`Adding user ${userId} to teams/${teamId}/teamMembers collection...`);
      await addDoc(membersCollection, {
        userId: userId,
        addedAt: serverTimestamp(),
        addedBy: "system",
        role: roleName
      });
    } else {
      console.log(`User ${userId} already exists in teams/${teamId}/teamMembers collection`);
    }
    
    const rootMembersCollection = collection(db, 'teamMembers');
    const rootMemberQuery = query(
      rootMembersCollection, 
      where("userId", "==", userId),
      where("teamId", "==", teamId)
    );
    const rootMemberSnapshot = await getDocs(rootMemberQuery);
    
    if (rootMemberSnapshot.empty) {
      console.log(`Adding user ${userId} to root teamMembers collection...`);
      await addDoc(rootMembersCollection, {
        userId: userId,
        teamId: teamId,
        addedAt: serverTimestamp(),
        addedBy: "system",
        role: roleName
      });
    } else {
      console.log(`User ${userId} already exists in root teamMembers collection`);
    }
    
    const teamRef = doc(db, "teams", teamId);
    const teamDoc = await getDoc(teamRef);
    
    if (teamDoc.exists()) {
      if (memberSnapshot.empty) {
        const teamCount = teamDoc.data().memberCount || 0;
        await updateDoc(teamRef, {
          memberCount: teamCount + 1,
          updatedAt: serverTimestamp()
        });
      }
    }
    
    return true;
  } catch (error) {
    console.error(`Error ensuring user ${userId} in team ${teamId}:`, error);
    return false;
  }
};

const createBypassRecord = async (userId, options = {}) => {
  if (!userId || !db) return false;
  
  try {
    console.log(`Creating bypass record for user ${userId}...`);
    const bypassRef = doc(db, 'userBypass', userId);
    
    await setDoc(bypassRef, {
      userId,
      bypassTimeCheck: options.bypassTimeCheck || false,
      bypassInspectionCheck: options.bypassInspectionCheck || false,
      bypassTeamCheck: options.bypassTeamCheck || true,
      teams: options.teams || [],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy: options.createdBy || 'system-fix',
      notes: options.notes || 'Auto-created bypass record for team access'
    });
    
    console.log(`Successfully created bypass record for user ${userId}`);
    return true;
  } catch (error) {
    console.error("Error creating bypass record:", error);
    return false;
  }
};

// Create memoized ChatInterface component to prevent unnecessary re-renders
const MemoizedChatInterface = memo(ChatInterface);

function MapView({ teamIdProp = null }) {
  const navigate = useNavigate();
  const { currentUser, isAdmin, hasTeamAccess, debugTeamAccess, getUserTeams: authGetUserTeams } = useAuth();
  
  // State management
  const [redirectCheckCompleted, setRedirectCheckCompleted] = useState(false);
  const initialMountRef = useRef(false);
  const teamSetupCompletedRef = useRef(false);
  const isUpdatingTeamMembersRef = useRef(false);
  
  // Refs
  const mapRef = useRef(null);
  const mapContainerRef = useRef(null);
  const currentMarkerRef = useRef(null);
  const userLabelRef = useRef(null);
  const routingControlRef = useRef(null);
  const markersLayerRef = useRef(null);
  const userMarkersLayerRef = useRef(null);
  const userLabelsLayerRef = useRef(null);
  const userPathsLayerRef = useRef(null);
  const breadcrumbPathsRef = useRef({});
  const drivingPathRef = useRef(null);
  const firestoreRef = useRef(db);
  const mapInitializedRef = useRef(false);
  const routeAnimationRef = useRef(null);
  const navigationElementsRef = useRef([]);
  const userMarkersRef = useRef({});
  const chatMessagesRef = useRef(null);
  const lastKnownPositionRef = useRef(defaultPosition);
  const markerClusterRef = useRef(null);
  const userMarkerClusterRef = useRef(null);
  const locationsListenerRef = useRef(null);
  const userLocationsListenerRef = useRef(null);
  const userTracesListenerRef = useRef(null);
  const chatListenerRef = useRef(null);
  const appContainerRef = useRef(null);
  const timeCardListenerRef = useRef(null);
  
  // ENHANCED: Track user trail colors
  const userTrailColorsRef = useRef({});
  
  // Current date/time state for display in header
  const [currentDateTime, setCurrentDateTime] = useState(new Date());
  
  // TEAM-SPECIFIC STATE
  const [teamId, setTeamId] = useState(teamIdProp);
  const [teamData, setTeamData] = useState(null);
  const [teamName, setTeamName] = useState("Your Team");
  const [showTeamSelector, setShowTeamSelector] = useState(false);
  const [noTeamAccess, setNoTeamAccess] = useState(false);
  const [userTeams, setUserTeams] = useState([]);
  
  // ENHANCED: Team members with location data
  const [teamMembers, setTeamMembers] = useState([]);
  const [onlineUsers, setOnlineUsers] = useState([]);
  
  // NEW: Team zones
  const [zones, setZones] = useState([]);
  const [activeZones, setActiveZones] = useState([]);
  const [showZoneAssignments, setShowZoneAssignments] = useState(false);
  const [selectedZoneForEditing, setSelectedZoneForEditing] = useState(null);
  const [showZonesPanel, setShowZonesPanel] = useState(false);
  
  // NEW: Order locations state
  const [orderLocations, setOrderLocations] = useState([]); // Raw orders from Firestore
  const [expandedOrderLocations, setExpandedOrderLocations] = useState([]); // Expanded into individual address markers
  
  // NEW: Team vehicles state
  const [teamVehicles, setTeamVehicles] = useState([]);
  
  // UI and Map State
  const [currentLocation, setCurrentLocation] = useState(defaultPosition);
  const [locations, setLocations] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [newMarkerPosition, setNewMarkerPosition] = useState(null);
  const [newMarkerName, setNewMarkerName] = useState('');
  const [newMarkerDetails, setNewMarkerDetails] = useState('');
  const [newMarkerImages, setNewMarkerImages] = useState([]);
  const [newMarkerPriority, setNewMarkerPriority] = useState(false);
  const [newMarkerParkingSide, setNewMarkerParkingSide] = useState(null);
  
  // Vehicle-specific fields
  const [newMarkerPlateNumber, setNewMarkerPlateNumber] = useState('');
  const [newMarkerVIN, setNewMarkerVIN] = useState('');
  const [newMarkerDriveType, setNewMarkerDriveType] = useState('');
  const [newMarkerMake, setNewMarkerMake] = useState('');
  const [newMarkerModel, setNewMarkerModel] = useState('');
  const [newMarkerYear, setNewMarkerYear] = useState('');
  const [newMarkerCase, setNewMarkerCase] = useState('');
  const [newMarkerMileage, setNewMarkerMileage] = useState('');
  
  // Address selection and order details
  const [orderDetails, setOrderDetails] = useState({});
  const [showAddressSelector, setShowAddressSelector] = useState(false);
  const [addressOptions, setAddressOptions] = useState([]);
  const [selectedOrderForNavigation, setSelectedOrderForNavigation] = useState(null);
  
  // Search states
  const [vinSearch, setVinSearch] = useState('');
  
  // Control state
  const [isAddingMarker, setIsAddingMarker] = useState(false);
  const [isAddingAdminMarker, setIsAddingAdminMarker] = useState(false);
  const [isSettingLocation, setIsSettingLocation] = useState(false);
  
  // ENHANCED: Navigation state
  const [isNavigating, setIsNavigating] = useState(false);
  const [distanceToDestination, setDistanceToDestination] = useState(null);
  const [estimatedTime, setEstimatedTime] = useState(null);
  const [navigationDirection, setNavigationDirection] = useState("straight");
  const [arrivalTime, setArrivalTime] = useState(null);
  const [destinationAddress, setDestinationAddress] = useState("");
  const [nextInstruction, setNextInstruction] = useState(null);
  const [destinationLocation, setDestinationLocation] = useState(null);
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [locationPromptShown, setLocationPromptShown] = useState(false);
  
  // Location and routing state
  const [currentAddress, setCurrentAddress] = useState('');
  const [nearestIntersection, setNearestIntersection] = useState('');
  const [isClockedIn, setIsClockedIn] = useState(false);
  const [clockInTime, setClockInTime] = useState(null);
  const [totalMilesCovered, setTotalMilesCovered] = useState(0);
  const [optimizedRoute, setOptimizedRoute] = useState([]);
  const [isFollowingOptimizedRoute, setIsFollowingOptimizedRoute] = useState(false);
  const [hasMovedSinceClockIn, setHasMovedSinceClockIn] = useState(false);
  
  // User state
  const [allUsers, setAllUsers] = useState([]);
  const [userMarkers, setUserMarkers] = useState({});
  const [showOtherUsers, setShowOtherUsers] = useState(true);
  const [userDisplayNames, setUserDisplayNames] = useState({});
  const [userProfilePictures, setUserProfilePictures] = useState({});
  const [isTowTruckUser, setIsTowTruckUser] = useState(false);
  
  // ENHANCED: User trace paths with color assignments
  const [userTracePaths, setUserTracePaths] = useState({});
  
  // UI state
  const [activeTab, setActiveTab] = useState('users');
  const [chatMessages, setChatMessages] = useState([]);
  const [newChatMessage, setNewChatMessage] = useState('');
  const [detailsPanelLocation, setDetailsPanelLocation] = useState(null);
  const [closestPendingLocation, setClosestPendingLocation] = useState(null);
  const [isMapRefreshing, setIsMapRefreshing] = useState(false);
  const [mediaToUpload, setMediaToUpload] = useState(null);
  
  const [chatInitialized, setChatInitialized] = useState(false);
  const [mapContainerClass, setMapContainerClass] = useState('');
  
  // Team stats
  const [teamStats, setTeamStats] = useState({
    userOpenOrdersToday: 9,
    userOpenOrdersWeek: 24,
    userOpenOrdersMonth: 63,
    teamOpenOrdersToday: 38,
    teamOpenOrdersWeek: 143,
    teamOpenOrdersMonth: 421,
  });
  
  // Quick navigation links
  const [quickNavLinks, setQuickNavLinks] = useState([
    { name: "Home Base", position: { lat: 37.7749, lng: -122.4194 } },
    { name: "Downtown", position: { lat: 37.7939, lng: -122.3999 } },
    { name: "Marina District", position: { lat: 37.8030, lng: -122.4356 } },
    { name: "Mission District", position: { lat: 37.7599, lng: -122.4148 } }
  ]);

  // Screen configuration
  const [screenConfig, setScreenConfig] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
    isSmallScreen: typeof window !== 'undefined' ? window.innerWidth < 768 : false,
    isMediumScreen: typeof window !== 'undefined' ? window.innerWidth >= 768 && window.innerWidth <= 1024 : false
  });

  // NEW: Function to expand orders into individual address markers
  const expandOrdersIntoAddressMarkers = useCallback((orders) => {
    if (!orders || orders.length === 0) {
      console.log("📭 No orders to expand into address markers");
      return [];
    }

    const expanded = [];
    
    orders.forEach(order => {
      console.log("🔍 Processing order for address markers:", order.id, "with addresses:", order.addresses);
      
      // If order has multiple addresses, create a marker for each address
      if (order.addresses && Array.isArray(order.addresses) && order.addresses.length > 0) {
        order.addresses.forEach((address, index) => {
          // Only create marker if address has valid coordinates
          if (address.position && address.position.lat && address.position.lng) {
            const addressMarker = {
              id: `${order.id}-address-${index}`,
              name: `${order.year || ''} ${order.make || ''} ${order.model || ''} - ${order.licensePlate || ''} (Address ${index + 1})`,
              make: order.make,
              model: order.model,
              year: order.year,
              status: order.status || 'open-order', // This will determine the marker color
              position: address.position, // Use the address-specific coordinates
              plateNumber: order.licensePlate,
              caseNumber: order.caseNumber,
              priority: order.priority || false,
              // Include address details for the popup
              address: address,
              formattedAddress: address.street ? 
                `${address.street}${address.city ? ', ' + address.city : ''}${address.state ? ' ' + address.state : ''}${address.zip ? ' ' + address.zip : ''}` : 
                'Address details unavailable',
              // Reference back to the original order
              sourceOrder: order.id,
              sourceType: 'order-address',
              addressIndex: index,
              originalOrder: order, // Include full order data for reference
              // Additional fields that might be useful
              vin: order.vin,
              customerName: order.customerName,
              color: order.color,
              secure: order.secure,
              securedBy: order.securedBy,
              secureTimestamp: order.secureTimestamp,
              // Fields needed for LocationsPanel compatibility
              details: `${order.make} ${order.model} ${order.year} - ${order.licensePlate}`,
              images: order.vehicleImage ? [order.vehicleImage] : [],
              createdBy: order.createdBy,
              createdAt: order.createdAt,
              updatedAt: order.updatedAt,
              teamId: order.teamId || teamId
            };
            
            expanded.push(addressMarker);
            console.log(`✅ Created address marker ${index + 1} for order ${order.id}:`, {
              id: addressMarker.id,
              name: addressMarker.name,
              position: addressMarker.position,
              formattedAddress: addressMarker.formattedAddress
            });
          } else {
            console.warn(`⚠️ Skipping address ${index + 1} for order ${order.id} - no coordinates:`, address);
          }
        });
      } else if (order.position && order.position.lat && order.position.lng) {
        // Fallback: if no addresses array but has main position, use that
        expanded.push({
          id: order.id,
          name: `${order.year || ''} ${order.make || ''} ${order.model || ''} - ${order.licensePlate || ''}`,
          make: order.make,
          model: order.model,
          year: order.year,
          status: order.status || 'open-order',
          position: order.position,
          plateNumber: order.licensePlate,
          caseNumber: order.caseNumber,
          priority: order.priority || false,
          sourceOrder: order.id,
          sourceType: 'order',
          originalOrder: order,
          formattedAddress: 'Main order location',
          vin: order.vin,
          customerName: order.customerName,
          color: order.color,
          secure: order.secure,
          securedBy: order.securedBy,
          secureTimestamp: order.secureTimestamp,
          details: `${order.make} ${order.model} ${order.year} - ${order.licensePlate}`,
          images: order.vehicleImage ? [order.vehicleImage] : [],
          createdBy: order.createdBy,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
          teamId: order.teamId || teamId
        });
        console.log(`✅ Created fallback marker for order ${order.id} using main position`);
      } else {
        console.warn(`⚠️ Skipping order ${order.id} - no valid coordinates in addresses or main position`);
      }
    });
    
    console.log(`📊 Expanded ${orders.length} orders into ${expanded.length} address markers`);
    return expanded;
  }, [teamId]);

  // NEW: useEffect to expand orders when orderLocations changes
  useEffect(() => {
    const expanded = expandOrdersIntoAddressMarkers(orderLocations);
    setExpandedOrderLocations(expanded);
  }, [orderLocations, expandOrdersIntoAddressMarkers]);

  // NEW: Listener for orders from Firestore
  useEffect(() => {
    if (!db || !teamId) return;
    
    console.log(`Setting up orders listener for team: ${teamId}`);
    
    const ordersRef = collection(db, 'orders');
    const ordersQuery = query(
      ordersRef,
      where('teamId', '==', teamId),
      orderBy('createdAt', 'desc')
    );
    
    const unsubscribe = onSnapshot(ordersQuery, 
      (snapshot) => {
        const ordersData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        console.log(`📦 Loaded ${ordersData.length} orders for team ${teamId}`);
        setOrderLocations(ordersData);
      },
      (error) => {
        console.error(`Error in orders listener for team ${teamId}:`, error);
      }
    );
    
    return () => {
      unsubscribe();
    };
  }, [db, teamId]);

  // NEW: Listener for team vehicles from Firestore
// NEW: Listener for team vehicles from team members' collections
useEffect(() => {
  if (!db || !teamId || !currentUser) return;
  
  console.log(`Setting up team vehicles listener for team: ${teamId}`);
  
  let unsubscribes = [];
  
  const setupTeamVehicleListeners = async () => {
    try {
      // Clear any existing listeners
      unsubscribes.forEach(unsub => unsub());
      unsubscribes = [];
      
      // Get all team members
      const teamMembersSnapshot = await getDocs(
        query(collection(db, 'teams', teamId, 'teamMembers'))
      );
      
      const teamMemberIds = teamMembersSnapshot.docs
        .map(doc => doc.data().userId)
        .filter(Boolean);
      
      console.log(`Found ${teamMemberIds.length} team members`);
      
      const allVehicles = [];
      const now = new Date();
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(now.getMonth() - 3);
      
      // Set up listeners for each team member
      for (const memberId of teamMemberIds) {
        try {
          // Get member info
          const memberDoc = await getDoc(doc(db, 'users', memberId));
          const memberData = memberDoc.exists() ? memberDoc.data() : {};
          const memberName = memberData.displayName || memberData.email?.split('@')[0] || 'Team Member';
          
          // Get recent weeks
          const weeksSnapshot = await getDocs(
            query(
              collection(db, 'users', memberId, 'vehicleWeeks'),
              orderBy('startDate', 'desc')
            )
          );
          
          // Listen to vehicles in recent weeks
          for (const weekDoc of weeksSnapshot.docs) {
            const weekData = weekDoc.data();
            const weekStartDate = weekData.startDate?.toDate();
            
            if (weekStartDate && weekStartDate >= threeMonthsAgo) {
              const vehiclesRef = collection(db, 'users', memberId, 'vehicleWeeks', weekDoc.id, 'vehicles');
              
              const unsubscribe = onSnapshot(vehiclesRef, (snapshot) => {
                // Remove old vehicles from this member/week
                allVehicles.splice(0, allVehicles.length, 
                  ...allVehicles.filter(v => !(v.teamMemberId === memberId && v.weekId === weekDoc.id))
                );
                
                // Add current vehicles
                snapshot.docs.forEach(vehicleDoc => {
                  const vehicleData = vehicleDoc.data();
                  
                  if ((vehicleData.status === 'FOUND' || vehicleData.status === 'PENDING PICKUP') &&
                      vehicleData.vin &&
                      !vehicleData.securedByTeammate) {
                    
                    allVehicles.push({
                      id: vehicleDoc.id,
                      ...vehicleData,
                      teamMemberId: memberId,
                      teamMemberName: memberName,
                      weekId: weekDoc.id,
                      weekRange: weekData.displayRange || 'Unknown Week',
                      uniqueKey: `${memberId}_${weekDoc.id}_${vehicleDoc.id}`,
                      isOwnVehicle: memberId === currentUser.uid
                    });
                  }
                });
                
                // Deduplicate by VIN and update state
                const uniqueVehicles = Array.from(
                  new Map(allVehicles.map(v => [v.vin.toUpperCase().trim(), v])).values()
                );
                
                console.log(`📚 Updated team vehicles: ${uniqueVehicles.length} unique vehicles`);
                setTeamVehicles(uniqueVehicles);
              });
              
              unsubscribes.push(unsubscribe);
            }
          }
        } catch (error) {
          console.error(`Error setting up listeners for member ${memberId}:`, error);
        }
      }
    } catch (error) {
      console.error("Error setting up team vehicle listeners:", error);
      setTeamVehicles([]);
    }
  };
  
  setupTeamVehicleListeners();
  
  return () => {
    unsubscribes.forEach(unsub => unsub());
  };
}, [db, teamId, currentUser]);

  // NEW: useEffect to re-merge locations when expandedOrderLocations changes
  useEffect(() => {
    if (expandedOrderLocations.length === 0) return;
    
    console.log(`📍 Updating combined locations with ${expandedOrderLocations.length} order address markers`);
    
    setLocations(prevLocations => {
      // Separate manual locations from order locations
      const manualLocations = prevLocations.filter(loc => !loc.sourceType || loc.sourceType !== 'order-address');
      
      // Combine with new expanded order locations
      const combinedLocations = [
        ...manualLocations,
        ...expandedOrderLocations
      ];
      
      console.log(`📊 Location merge: ${manualLocations.length} manual + ${expandedOrderLocations.length} orders = ${combinedLocations.length} total`);
      
      return combinedLocations;
    });
  }, [expandedOrderLocations]);

  // ENHANCED: Effect to load team zones
  useEffect(() => {
    if (!db || !teamId) return;
    
    const zonesRef = collection(db, 'teams', teamId, 'zones');
    const unsubscribe = onSnapshot(zonesRef, (snapshot) => {
      const zonesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      console.log(`Loaded ${zonesData.length} zones for team ${teamId}`);
      setZones(zonesData);
    }, (error) => {
      console.error("Error loading zones:", error);
    });
    
    return () => unsubscribe();
  }, [db, teamId]);
  
  // Determine active zones when location changes
  useEffect(() => {
    if (!currentLocation || !zones.length || !isClockedIn) return;
    
    const userZones = zones.filter(zone => {
      if (!zone.bounds || !zone.bounds.northEast || !zone.bounds.southWest) return false;
      
      const isUserAssigned = !zone.assignedUsers || 
        zone.assignedUsers.length === 0 || 
        (currentUser && zone.assignedUsers.includes(currentUser.uid));
      
      if (!isUserAssigned) return false;
      
      return currentLocation.lat >= zone.bounds.southWest.lat && 
             currentLocation.lat <= zone.bounds.northEast.lat &&
             currentLocation.lng >= zone.bounds.southWest.lng &&
             currentLocation.lng <= zone.bounds.northEast.lng;
    });
    
    setActiveZones(userZones);
  }, [currentLocation, zones, isClockedIn, currentUser]);

  // ... [CONTINUE WITH REST OF THE FILE - ALL OTHER USEEFFECTS AND FUNCTIONS REMAIN THE SAME]
  // ... [I'll continue in the next part due to length]
  // ENHANCED: Find my location with immediate UI updates
  const findMyLocation = useCallback(() => {
    if (navigator.geolocation) {
      setIsLoading(true);
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude, accuracy, heading, speed } = position.coords;
          const userPos = { 
            lat: latitude, 
            lng: longitude,
            accuracy: accuracy,
            heading: heading || 0,
            speed: speed || 0,
            timestamp: Date.now()
          };
          
          // ENHANCED: Immediate state updates
          setCurrentLocation(userPos);
          lastKnownPositionRef.current = userPos;
          
          // Center map on user's location
          if (mapRef.current) {
            mapRef.current.setView([latitude, longitude], 16);
          }
          
          // ENHANCED: Immediate database update
          if (currentUser && teamId) {
            updateUserLocation(db, userPos, currentUser, teamId)
              .catch(err => console.error("Failed to update user location:", err));
          }
          
          setIsLoading(false);
          setLocationPromptShown(false);
        },
        (error) => {
          console.error("Geolocation error:", error);
          setError("Could not get your location. Please check your browser settings.");
          setIsLoading(false);
        },
        {
          enableHighAccuracy: true,
          timeout: 5000, // Faster timeout
          maximumAge: 1000 // Fresh location data
        }
      );
    } else {
      setError("Geolocation is not supported by this browser.");
    }
  }, [currentUser, teamId]);

  const skipLocationRequest = useCallback(() => {
    setLocationPromptShown(false);
    console.log("Location request skipped by user");
  }, []);

  // Load quick nav links from Firestore
  useEffect(() => {
    if (!db || !teamId) return;
    
    console.log("Loading quick nav links for team:", teamId);
    setIsLoading(true);
    
    const loadQuickNavLinks = async () => {
      try {
        const quickNavRef = collection(db, "teams", teamId, "quickNavLinks");
        const q = query(quickNavRef, orderBy("name", "asc"));
        const querySnapshot = await getDocs(q);
        
        if (!querySnapshot.empty) {
          const links = querySnapshot.docs.map(doc => ({
            id: doc.id,
            name: doc.data().name,
            position: doc.data().position || { lat: 0, lng: 0 }
          }));
          
          console.log(`Loaded ${links.length} custom quick nav links from database:`, links);
          setQuickNavLinks(links);
        } else {
          console.log("No quick nav links found in database, creating defaults");
          
          const defaultLinks = [
            { name: "Home Base", position: { lat: 37.7749, lng: -122.4194 } },
            { name: "Downtown", position: { lat: 37.7939, lng: -122.3999 } },
            { name: "Marina District", position: { lat: 37.8030, lng: -122.4356 } },
            { name: "Mission District", position: { lat: 37.7599, lng: -122.4148 } }
          ];
          
          setQuickNavLinks(defaultLinks);
          
          const createdLinks = [];
          for (const link of defaultLinks) {
            try {
              const docRef = await addDoc(quickNavRef, {
                name: link.name,
                position: link.position,
                createdAt: serverTimestamp(),
                updatedAt: serverTimestamp(),
                createdBy: currentUser?.uid || 'system'
              });
              
              createdLinks.push({
                id: docRef.id,
                ...link
              });
            } catch (error) {
              console.error(`Error creating default link "${link.name}":`, error);
            }
          }
          
          if (createdLinks.length > 0) {
            setQuickNavLinks(createdLinks);
          }
        }
      } catch (error) {
        console.error("Error loading quick nav links:", error);
        setQuickNavLinks([
          { name: "Home Base", position: { lat: 37.7749, lng: -122.4194 } },
          { name: "Downtown", position: { lat: 37.7939, lng: -122.3999 } },
          { name: "Marina District", position: { lat: 37.8030, lng: -122.4356 } },
          { name: "Mission District", position: { lat: 37.7599, lng: -122.4148 } }
        ]);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadQuickNavLinks();
    
    const unsubscribe = onSnapshot(
      collection(db, "teams", teamId, "quickNavLinks"),
      (snapshot) => {
        if (!snapshot.empty) {
          const updatedLinks = snapshot.docs.map(doc => ({
            id: doc.id,
            name: doc.data().name,
            position: doc.data().position || { lat: 0, lng: 0 }
          }));
          
          console.log("Real-time update to quick nav links received:", updatedLinks);
          setQuickNavLinks(updatedLinks);
        }
      },
      (error) => {
        console.error("Error in quick nav links listener:", error);
      }
    );
    
    return () => unsubscribe();
  }, [db, teamId, currentUser]);

  // Update quick nav link function
  const updateQuickNavLink = useCallback(async (updatedLink) => {
    try {
      console.log("Updating quick nav link:", updatedLink);
      
      if (!updatedLink || !updatedLink.name) {
        console.error("Invalid quick nav link data");
        return false;
      }
      
      if (!updatedLink.position || typeof updatedLink.position.lat !== 'number' || typeof updatedLink.position.lng !== 'number') {
        console.error("Invalid position data in quick nav link", updatedLink);
        return false;
      }
      
      if (db && teamId) {
        const quickNavRef = collection(db, "teams", teamId, "quickNavLinks");
        let docRef;
        
        if (updatedLink.id) {
          docRef = doc(db, "teams", teamId, "quickNavLinks", updatedLink.id);
          await updateDoc(docRef, {
            name: updatedLink.name,
            position: updatedLink.position,
            updatedAt: serverTimestamp(),
            updatedBy: currentUser?.uid || 'unknown'
          });
        } else {
          const q = query(quickNavRef, where("name", "==", updatedLink.name));
          const querySnapshot = await getDocs(q);
          
          if (!querySnapshot.empty) {
            docRef = querySnapshot.docs[0].ref;
            await updateDoc(docRef, {
              name: updatedLink.name,
              position: updatedLink.position,
              updatedAt: serverTimestamp(),
              updatedBy: currentUser?.uid || 'unknown'
            });
          } else {
            docRef = await addDoc(quickNavRef, {
              name: updatedLink.name,
              position: updatedLink.position,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp(),
              createdBy: currentUser?.uid || 'unknown',
              updatedBy: currentUser?.uid || 'unknown'
            });
          }
        }
        
        alert("Location updated successfully!");
        return true;
      } else {
        console.warn("Database or teamId not available, only updating local state");
        setQuickNavLinks(prevLinks => 
          prevLinks.map(link => 
            link.name === updatedLink.name ? {...link, ...updatedLink} : link
          )
        );
        return true;
      }
    } catch (error) {
      console.error("Error updating quick nav link:", error);
      alert("Error saving location: " + error.message);
      return false;
    }
  }, [db, teamId, currentUser]);

  // Setup orientation and resize handlers
  useEffect(() => {
    const handleResize = () => {
      setScreenConfig({
        width: window.innerWidth,
        height: window.innerHeight,
        isSmallScreen: window.innerWidth < 768,
        isMediumScreen: window.innerWidth >= 768 && window.innerWidth <= 1024
      });
    };
    
    window.addEventListener('resize', handleResize);
    const cleanupOrientationHandler = setupOrientationHandler(mapRef);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      cleanupOrientationHandler();
    };
  }, []);

  // ENHANCED: Team member count updates with color assignments
  const updateTeamMemberCounts = useCallback(async (teamId) => {
    if (!db || !teamId || isUpdatingTeamMembersRef.current) {
      return [];
    }

    isUpdatingTeamMembersRef.current = true;
    
    try {
      console.log(`Starting team member count update for team: ${teamId}`);
      const membersCollection = collection(db, `teams/${teamId}/teamMembers`);
      const allMembersSnapshot = await getDocs(membersCollection);
      
      const memberIds = [];
      const memberPromises = [];
      
      allMembersSnapshot.forEach(memberDoc => {
        const memberData = memberDoc.data();
        if (memberData.userId) {
          memberIds.push(memberData.userId);
          
          // ENHANCED: Assign trail colors during profile loading
          if (!userTrailColorsRef.current[memberData.userId]) {
            userTrailColorsRef.current[memberData.userId] = getUserTrailColor(memberData.userId);
            console.log(`Assigned trail color ${userTrailColorsRef.current[memberData.userId]} to user ${memberData.userId}`);
          }
          
          const profilePromise = getDoc(doc(db, "userProfiles", memberData.userId))
            .then(profileSnap => {
              return {
                userId: memberData.userId,
                role: memberData.role,
                profileData: profileSnap.exists() ? profileSnap.data() : null,
                trailColor: userTrailColorsRef.current[memberData.userId]
              };
            })
            .catch(error => {
              console.error(`Error fetching profile for ${memberData.userId}:`, error);
              return {
                userId: memberData.userId,
                role: memberData.role,
                profileData: null,
                trailColor: userTrailColorsRef.current[memberData.userId]
              };
            });
          
          memberPromises.push(profilePromise);
        }
      });
      
      const membersWithProfiles = await Promise.all(memberPromises);
      
      let spottersCount = 0;
      let towDriversCount = 0;
      
      membersWithProfiles.forEach(member => {
        if (member.role === 'towDriver' || member.role === 'tow') {
          towDriversCount++;
        } else if (member.profileData && 
                   member.profileData.tags && 
                   member.profileData.tags.includes("Tow Truck")) {
          towDriversCount++;
        } else {
          spottersCount++;
        }
      });
      
      if (spottersCount === 0 && towDriversCount === 0 && allMembersSnapshot.size > 0) {
        spottersCount = Math.ceil(allMembersSnapshot.size / 2);
        towDriversCount = allMembersSnapshot.size - spottersCount;
      }
      
      if (!teamData || 
          !teamData.teamSize || 
          teamData.teamSize.spotters !== spottersCount || 
          teamData.teamSize.towDrivers !== towDriversCount) {
        
        setTeamData(prev => {
          if (!prev) return { teamSize: { spotters: spottersCount, towDrivers: towDriversCount } };
          
          return {
            ...prev,
            teamSize: {
              ...(prev.teamSize || {}),
              spotters: spottersCount,
              towDrivers: towDriversCount
            }
          };
        });
        
        try {
          const teamRef = doc(db, "teams", teamId);
          const teamDocCheck = await getDoc(teamRef);
          if (teamDocCheck.exists()) {
            await updateDoc(teamRef, {
              teamSize: {
                spotters: spottersCount,
                towDrivers: towDriversCount
              },
              memberCount: allMembersSnapshot.size,
              updatedAt: serverTimestamp()
            });
          }
        } catch (updateError) {
          console.error("Error updating team document with member counts:", updateError);
        }
      }
      
      setTeamMembers(memberIds);
      return memberIds;
    } catch (error) {
      console.error("Error getting team member counts:", error);
      return [];
    } finally {
      isUpdatingTeamMembersRef.current = false;
    }
  }, [db, teamData]);

  // Requirements check for clock-in and inspection
  useEffect(() => {
    if (initialMountRef.current === true) {
      setRedirectCheckCompleted(true);
      return;
    }
    
    if (!currentUser || isAdmin) {
      setRedirectCheckCompleted(true);
      initialMountRef.current = true;
      return;
    }
    
    const checkRequirements = async () => {
      const redirectAttempted = sessionStorage.getItem('redirectAttempted');
      if (redirectAttempted === 'true') {
        setRedirectCheckCompleted(true);
        initialMountRef.current = true;
        return;
      }
      
      try {
        sessionStorage.setItem('redirectAttempted', 'true');
        
        let clockedIn = false;
        let inspectionCompleted = false;
        
        const timeCardRef = doc(db, 'timeCards', currentUser.uid);
        const timeCardDoc = await getDoc(timeCardRef);
        
        if (timeCardDoc.exists()) {
          const timeCardData = timeCardDoc.data();
          const today = new Date().toLocaleDateString();
          
          clockedIn = timeCardData.currentDay === today && 
                      timeCardData.clockedIn && 
                      !timeCardData.clockedOut;
        }
        
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        const startTimestamp = Timestamp.fromDate(today);
        const endTimestamp = Timestamp.fromDate(tomorrow);
        
        const q = query(
          collection(db, 'inspections'),
          where('userId', '==', currentUser.uid),
          where('timestamp', '>=', startTimestamp),
          where('timestamp', '<', endTimestamp)
        );
        
        const querySnapshot = await getDocs(q);
        inspectionCompleted = !querySnapshot.empty;
        
        if (!clockedIn) {
          navigate('/timecard');
          return;
        }
        
        if (!inspectionCompleted) {
          navigate('/inspection');
          return;
        }
        
        setIsClockedIn(clockedIn);
        setRedirectCheckCompleted(true);
        initialMountRef.current = true;
      } catch (error) {
        console.error("Error checking map requirements:", error);
        setRedirectCheckCompleted(true);
        initialMountRef.current = true;
      }
    };
    
    checkRequirements();
  }, [currentUser, isAdmin, navigate, db]);

  // Real-time clock status listener
  useEffect(() => {
    if (!currentUser || !db) return;
    
    const timeCardRef = doc(db, 'timeCards', currentUser.uid);
    
    const updateClockState = (timeCardData) => {
      const today = new Date().toLocaleDateString();
      const clockedIn = timeCardData.currentDay === today && 
                      timeCardData.clockedIn && 
                      !timeCardData.clockedOut;
      
      setIsClockedIn(clockedIn);
      setClockInTime(timeCardData.clockInTime?.toDate() || null);
    };
    
    getDoc(timeCardRef).then(docSnapshot => {
      if (docSnapshot.exists()) {
        updateClockState(docSnapshot.data());
      } else {
        setIsClockedIn(false);
        setClockInTime(null);
      }
    }).catch(error => {
      console.error("Error in direct timeCard check:", error);
    });
    
    const unsubscribe = onSnapshot(timeCardRef, (docSnapshot) => {
      if (docSnapshot.exists()) {
        updateClockState(docSnapshot.data());
      } else {
        setIsClockedIn(false);
        setClockInTime(null);
      }
    }, (error) => {
      console.error("Error in clock status listener:", error);
    });
    
    timeCardListenerRef.current = unsubscribe;
    
    return () => {
      if (timeCardListenerRef.current) {
        timeCardListenerRef.current();
        timeCardListenerRef.current = null;
      }
    };
  }, [currentUser, db]);

  // Load user profile
  useEffect(() => {
    if (!currentUser || !db) return;
    
    const loadUserProfile = async () => {
      try {
        const userProfileDoc = doc(db, "userProfiles", currentUser.uid);
        const profileSnapshot = await getDoc(userProfileDoc);
        
        if (profileSnapshot.exists()) {
          const profileData = profileSnapshot.data();
          
          if (profileData.photoBase64) {
            setUserProfilePictures(prev => ({
              ...prev,
              [currentUser.uid]: profileData.photoBase64
            }));
          }
          
          if (profileData.displayName) {
            setUserDisplayNames(prev => ({
              ...prev,
              [currentUser.uid]: profileData.displayName
            }));
          }
          
          if (profileData.tags && profileData.tags.some(tag => 
              typeof tag === 'object' ? tag.name === 'Tow Truck' : tag === 'Tow Truck')) {
            setIsTowTruckUser(true);
          }
        }
      } catch (error) {
        console.error("Error loading user profile directly:", error);
      }
    };
    
    loadUserProfile();
  }, [currentUser, db]);
  
  // Handle page refresh/unmount
  useEffect(() => {
    const handleBeforeUnload = () => {
      setChatInitialized(false);
      
      if (currentUser && teamId) {
        updateUserOnlineStatus(db, false, currentUser, teamId)
          .catch(err => console.error("Failed to set user offline on refresh:", err));
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      
      if (currentUser && teamId) {
        updateUserOnlineStatus(db, false, currentUser, teamId)
          .catch(err => console.error("Failed to set user offline on unmount:", err));
      }
    };
  }, [currentUser, teamId]);

  // Initialize user data and profile info
  useEffect(() => {
    if (!redirectCheckCompleted) {
      return;
    }
    
    let isMounted = true;
    
    const initializeUserData = async () => {
      if (!currentUser) return;
      
      try {
        const userProfile = await getUserProfile(db, currentUser.uid);
        if (userProfile && isMounted) {
          setIsTowTruckUser(userProfile.tags?.includes("Tow Truck") || false);
        }
        
        if (!teamId) {
          const teams = await getUserTeams(db, currentUser.uid);
          
          if (isMounted && teams) {
            setUserTeams(teams);
            
            if (teams.length > 0) {
              const storedTeamId = localStorage.getItem('lastUsedTeamId');
              
              if (storedTeamId) {
                const teamExists = teams.some(team => team.id === storedTeamId);
                
                if (teamExists) {
                  const hasAccess = await hasTeamAccess(storedTeamId);
                  
                  if (hasAccess) {
                    setTeamId(storedTeamId);
                    const teamData = teams.find(team => team.id === storedTeamId);
                    setTeamName(teamData?.name || 'Your Team');
                    setTeamData(teamData);
                    setShowTeamSelector(false);
                  } else {
                    setTeamId(teams[0].id);
                    setTeamName(teams[0].name);
                    setTeamData(teams[0]);
                    setShowTeamSelector(false);
                    localStorage.setItem('lastUsedTeamId', teams[0].id);
                  }
                } else {
                  setTeamId(teams[0].id);
                  setTeamName(teams[0].name);
                  setTeamData(teams[0]);
                  setShowTeamSelector(false);
                  localStorage.setItem('lastUsedTeamId', teams[0].id);
                }
              } else {
                setTeamId(teams[0].id);
                setTeamName(teams[0].name);
                setTeamData(teams[0]);
                setShowTeamSelector(false);
                localStorage.setItem('lastUsedTeamId', teams[0].id);
              }
            } else {
              setError("You don't have access to any teams. Please contact an administrator.");
              setNoTeamAccess(true);
              setShowTeamSelector(true);
            }
          }
        }
        
        if (isMounted) {
          setIsLoading(false);
          
          if (currentLocation === defaultPosition && !locationPromptShown) {
            setLocationPromptShown(true);
          }
        }
      } catch (error) {
        console.error("Error initializing user data:", error);
        if (isMounted) {
          setError("Error loading user data");
          setIsLoading(false);
        }
      }
    };
    
    initializeUserData();
    
    return () => {
      isMounted = false;
    };
  }, [currentUser, db, teamId, locationPromptShown, currentLocation, redirectCheckCompleted, hasTeamAccess]);

  // CRITICAL FIX: Enhanced location tracking with immediate updates
  useEffect(() => {
    if (!currentUser || !isClockedIn) return;
    
    let watchId = null;
    let isMounted = true;
    let lastUpdate = Date.now();
    
    if (navigator.geolocation) {
      watchId = navigator.geolocation.watchPosition(
        (position) => {
          if (!isMounted) return;
          
          const { latitude, longitude, accuracy, heading, speed } = position.coords;
          const newPos = { 
            lat: latitude, 
            lng: longitude,
            accuracy: accuracy,
            heading: heading || 0,
            speed: speed || 0,
            timestamp: Date.now()
          };
          
          // CRITICAL: Update state immediately for UI responsiveness
          setCurrentLocation(newPos);
          lastKnownPositionRef.current = newPos;
          
          // ENHANCED: Much more frequent database updates (every 2 seconds instead of 10)
          const now = Date.now();
          const timeSinceLastUpdate = now - lastUpdate;
          const distanceMoved = calculateDistance(lastKnownPositionRef.current, newPos);
          
          // Update every 2 seconds OR when moved 5+ meters (much more responsive)
          if (timeSinceLastUpdate > 2000 || distanceMoved > 0.003) { // ~5 meters
            console.log(`Live location update: ${teamId ? `team ${teamId}` : 'no team'}`);
            lastUpdate = now;
            
            // Update user location in Firestore with team ID
            if (db && currentUser && teamId) {
              updateUserLocation(db, newPos, currentUser, teamId)
                .catch(err => console.error("Failed to update user location:", err));
              
              // Update user trace (breadcrumb) with team ID
              updateUserTrace(db, newPos, currentUser, teamId)
                .catch(err => console.error("Failed to update user trace:", err));
            }
            
            setHasMovedSinceClockIn(true);
            
            // Update closest pending location when user moves
            if (locations.length > 0) {
              const pendingLocations = locations.filter(loc => loc.status !== 'picked-up');
              if (pendingLocations.length > 0) {
                const { location: closest } = findClosestLocation(newPos, pendingLocations);
                setClosestPendingLocation(closest);
              }
            }

            // Update navigation if active
            if (isNavigating && destinationLocation) {
              const distance = calculateDistance(newPos, destinationLocation.position);
              setDistanceToDestination(distance);
              
              if (distance) {
                const timeInSeconds = (distance / 20) * 3600;
                setEstimatedTime(timeInSeconds);
                
                const now = new Date();
                const arrivalTimeMs = now.getTime() + (timeInSeconds * 1000);
                setArrivalTime(new Date(arrivalTimeMs));
              }
            }
          }
        },
        (error) => {
          console.error("Geolocation watch error:", error);
        },
        {
          enableHighAccuracy: true,
          maximumAge: 1000,    // ENHANCED: Much fresher location data (1 second vs 10 seconds)
          timeout: 5000       // ENHANCED: Faster timeout (5 seconds vs 10 seconds)
        }
      );
    }
    
    return () => {
      isMounted = false;
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [currentUser, isClockedIn, db, locations, teamId, isNavigating, destinationLocation]);

  // ENHANCED: More frequent user online status updates
  useEffect(() => {
    if (!currentUser || !db || !teamId) return;
    
    let isMounted = true;
    let lastOnlineUpdate = Date.now();
    
    // Immediately update online status when teamId changes
    updateUserOnlineStatus(db, true, currentUser, teamId)
      .catch(err => console.error("Failed to update initial online status:", err));
    
    const interval = setInterval(() => {
      if (!isMounted || !currentUser || !currentUser.uid || !teamId) return;
      
      // ENHANCED: Update every 15 seconds instead of 30-60 seconds
      const now = Date.now();
      if (now - lastOnlineUpdate > 15000) {
        lastOnlineUpdate = now;
        updateUserOnlineStatus(db, true, currentUser, teamId)
          .catch(err => console.error("Failed to refresh online status:", err));
      }
    }, 15000); // Check every 15 seconds instead of 60
    
    return () => {
      isMounted = false;
      clearInterval(interval);
      
      if (currentUser && teamId) {
        updateUserOnlineStatus(db, false, currentUser, teamId)
          .catch(err => console.error("Failed to set offline status on cleanup:", err));
      }
    };
  }, [currentUser, db, teamId]);

  // Update current date/time every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentDateTime(new Date());
    }, 60000);
    
    return () => clearInterval(interval);
  }, []);

  // ENHANCED: Setup team listeners with better location data handling
  const setupTeamListeners = useCallback((teamId) => {
    if (!db || !currentUser || !teamId) return { cleanup: () => {} };
    
    console.log(`Setting up ENHANCED Firestore listeners for team: ${teamId}, user: ${currentUser.uid}`);
    
    let listeners = {
      locations: null,
      userLocations: null,
      userTraces: null
    };
    
    // 1. Team-specific locations listener (EXISTING manual locations)
    listeners.locations = subscribeToTeamLocations(
      db,
      teamId,
      (fetchedLocations) => {
        console.log(`Team ${teamId} manual locations updated:`, fetchedLocations.length);
        
        // CRITICAL: Merge manual locations with order address markers
        setLocations(prevLocations => {
          // Get current expanded order locations
          const currentOrderLocations = expandedOrderLocations || [];
          
          // Combine manual locations with order locations
          const combinedLocations = [
            ...fetchedLocations, // Manual locations from LocationsPanel
            ...currentOrderLocations // Order address markers
          ];
          
          console.log(`📍 Combined locations: ${fetchedLocations.length} manual + ${currentOrderLocations.length} order addresses = ${combinedLocations.length} total`);
          
          // Update closest pending location
          if (combinedLocations.length > 0 && currentLocation) {
            const pendingLocations = combinedLocations.filter(loc => 
              loc.status !== 'picked-up' && loc.status !== 'secure'
            );
            if (pendingLocations.length > 0) {
              const { location: closest } = findClosestLocation(currentLocation, pendingLocations);
              setClosestPendingLocation(closest);
            } else {
              setClosestPendingLocation(null);
            }
          }
          
          return combinedLocations;
        });
      },
      (error) => {
        console.error(`Team ${teamId} locations listener error:`, error);
        setError("Failed to load locations.");
      }
    );
    
    // 2. ENHANCED: Team-specific user locations listener with better data handling
    listeners.userLocations = subscribeToTeamUserLocations(
      db,
      teamId,
      (users) => {
        console.log(`Team ${teamId} user locations updated:`, users.length);
        
        // DEBUG: Log the structure of the first user to understand the data
        if (users.length > 0) {
          console.log('Sample user data structure:', users[0]);
        }
        
        // CRITICAL: Store full user data including locations for MapDisplay
        setAllUsers(users);
        
        // ENHANCED: Update team members with location data and trail colors
        const membersWithLocations = users.map(user => {
          // Assign trail color if not already assigned
          if (!userTrailColorsRef.current[user.uid]) {
            userTrailColorsRef.current[user.uid] = getUserTrailColor(user.uid);
          }
          
          // FIX: Handle the actual data structure from Firebase
          // The location might be nested differently or have different field names
          const locationData = user.currentLocation || 
                              user.location || 
                              user.lastKnownLocation ||
                              (user.latitude && user.longitude ? {
                                lat: user.latitude,
                                lng: user.longitude,
                                timestamp: user.timestamp || Date.now()
                              } : null);
          
          // DEBUG: Log if we found location data
          if (!locationData) {
            console.warn(`No location data found for user ${user.uid}:`, user);
          } else {
            console.log(`Found location for ${user.uid}:`, locationData);
          }
          
          return {
            uid: user.uid,  // ✅ FIXED: Primary identifier for TeamTrailSystem
            id: user.uid,   // Keep for backward compatibility
            location: locationData,
            currentLocation: locationData, // Add both fields for compatibility
            online: user.online !== false, // Default to true if not explicitly false
            displayName: user.displayName || userDisplayNames[user.uid] || user.email,
            profilePicture: user.photoBase64 || userProfilePictures[user.uid],
            trailColor: userTrailColorsRef.current[user.uid]
          };
        }); // REMOVED the filter that was excluding users without locations
        
        // CRITICAL: Update teamMembers state with ALL users, even if offline
        setTeamMembers(membersWithLocations);
        
        // FIXED: Update online users list with users that have location AND are online
        const onlineUsersWithLocation = users.filter(user => {
          const hasLocation = !!(user.currentLocation || user.location || 
                               (user.latitude && user.longitude));
          const isOnline = user.online !== false;
          return hasLocation && isOnline;
        });
        
        setOnlineUsers(onlineUsersWithLocation);
        
        console.log(`Updated team members: ${membersWithLocations.length} total, ${onlineUsersWithLocation.length} online with location`);
        
        // DEBUG: Log the final data being set
        console.log('Team members data:', membersWithLocations.map(m => ({
          uid: m.uid,
          hasLocation: !!m.location,
          locationCoords: m.location ? `${m.location.lat}, ${m.location.lng}` : 'none',
          online: m.online
        })));
      },
      (error) => {
        console.error(`Team ${teamId} user locations listener error:`, error);
      },
      {
        onlineOnly: false, // Include all users, not just online ones
        excludeCurrentUser: false,
        currentUserId: currentUser.uid
      }
    );
            
    // 3. ENHANCED: Team-specific user traces listener with color assignments
    listeners.userTraces = subscribeToTeamUserTraces(
      db,
      teamId,
      (traces) => {
        console.log(`Team ${teamId} user traces updated`);
        
        // ENHANCED: Add color information to traces
        const tracesWithColors = {};
        Object.keys(traces).forEach(userId => {
          if (!userTrailColorsRef.current[userId]) {
            userTrailColorsRef.current[userId] = getUserTrailColor(userId);
          }
          
          tracesWithColors[userId] = {
            ...traces[userId],
            color: userTrailColorsRef.current[userId]
          };
        });
        
        setUserTracePaths(tracesWithColors);
      },
      (error) => {
        console.error(`Team ${teamId} user traces listener error:`, error);
      }
    );
    
    // Load profile data for all team members
    updateTeamMemberCounts(teamId).then(memberIds => {
      const loadUserProfiles = async () => {
        try {
          const displayNames = {};
          const profilePics = {};
          
          for (const userId of memberIds) {
            const profile = await getUserProfile(db, userId);
            if (profile) {
              if (profile.displayName) {
                displayNames[userId] = profile.displayName;
              }
              if (profile.photoBase64) {
                profilePics[userId] = profile.photoBase64;
              }
            }
          }
          
          setUserDisplayNames(prev => ({...prev, ...displayNames}));
          setUserProfilePictures(prev => ({...prev, ...profilePics}));
        } catch (error) {
          console.error("Error loading team member profiles:", error);
        }
      };
      
      loadUserProfiles();
    });
    
    return {
      cleanup: () => {
        console.log(`Cleaning up team ${teamId} Firestore listeners...`);
        
        if (listeners.locations) {
          listeners.locations();
          listeners.locations = null;
        }
        
        if (listeners.userLocations) {
          listeners.userLocations();
          listeners.userLocations = null;
        }
        
        if (listeners.userTraces) {
          listeners.userTraces();
          listeners.userTraces = null;
        }
      }
    };
  }, [db, currentUser, currentLocation, updateTeamMemberCounts, expandedOrderLocations, userDisplayNames, userProfilePictures]); // ADD expandedOrderLocations to dependencies

  // Chat messages listener
  useEffect(() => {
    if (!db || !teamId || !currentUser) return;

    if (chatListenerRef.current) {
      chatListenerRef.current();
      chatListenerRef.current = null;
    }

    setChatMessages([]);
    setChatInitialized(false);
    
    console.log(`Setting up chat messages listener for team: ${teamId}`);
    
    try {
      const messagesCollection = collection(db, 'teams', teamId, 'chatMessages');
      const messagesQuery = query(
        messagesCollection,
        orderBy("timestamp", "asc"),
        limit(100)
      );
      
      const unsubscribe = onSnapshot(messagesQuery, 
        (snapshot) => {
          console.log(`Received ${snapshot.docs.length} chat messages for team ${teamId}`);
          
          if (snapshot.docs.length > 0) {
            const messages = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));
            
            setChatMessages(messages);
            setChatInitialized(true);
          } else {
            const rootCollection = collection(db, 'chatMessages');
            const rootQuery = query(
              rootCollection,
              where("teamId", "==", teamId),
              orderBy("timestamp", "asc"),
              limit(100)
            );
            
            getDocs(rootQuery)
              .then(rootSnapshot => {
                if (rootSnapshot.docs.length > 0) {
                  const rootMessages = rootSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                  }));
                  
                  setChatMessages(rootMessages);
                } else {
                  setChatMessages([]);
                }
                
                setChatInitialized(true);
              })
              .catch(rootError => {
                console.error("Error checking root messages:", rootError);
                setChatMessages([]);
                setChatInitialized(true);
              });
          }
        },
        (error) => {
          console.error(`Team ${teamId} chat messages listener error:`, error);
          setChatInitialized(true);
          setChatMessages([]);
        }
      );
      
      chatListenerRef.current = unsubscribe;
      
      return () => {
        if (chatListenerRef.current) {
          chatListenerRef.current();
          chatListenerRef.current = null;
        }
      };
    }
    catch (error) {
      console.error("Error setting up chat messages listener:", error);
      setChatInitialized(true);
      setChatMessages([]);
    }
  }, [db, teamId, currentUser]);

  // Setup Firestore listeners when team is selected
  useEffect(() => {
    if (!redirectCheckCompleted) {
      return;
    }
    
    if (!db || !currentUser || !teamId) {
      return;
    }
    
    if (teamSetupCompletedRef.current === teamId) {
      return;
    }
    
    let cleanupFunction = () => {};
    
    ensureUserInTeam(db, currentUser.uid, teamId)
      .then(() => {
        return hasTeamAccess(teamId);
      })
      .then(hasAccess => {
        if (!hasAccess && !isAdmin) {
          console.error("User does not have access to this team");
          debugTeamAccess(currentUser.uid, teamId);
          
          return ensureUserInTeam(db, currentUser.uid, teamId)
            .then(() => {
              return hasTeamAccess(teamId);
            })
            .then(accessFixed => {
              if (!accessFixed) {
                return createBypassRecord(currentUser.uid, {
                  bypassTeamCheck: true,
                  teams: [teamId],
                  notes: 'Auto-created for map access'
                }).then(() => {
                  const listenerHandlers = setupTeamListeners(teamId);
                  cleanupFunction = listenerHandlers.cleanup;
                  teamSetupCompletedRef.current = teamId;
                });
              } else {
                const listenerHandlers = setupTeamListeners(teamId);
                cleanupFunction = listenerHandlers.cleanup;
                teamSetupCompletedRef.current = teamId;
              }
            });
        } else if (!hasAccess && isAdmin) {
          addUserToTeam(db, currentUser.uid, teamId, 'admin', currentUser.uid)
            .then(() => {
              const listenerHandlers = setupTeamListeners(teamId);
              cleanupFunction = listenerHandlers.cleanup;
              teamSetupCompletedRef.current = teamId;
            })
            .catch(error => {
              console.error("Failed to add admin to team:", error);
              setError("Error adding admin to team");
              setNoTeamAccess(true);
              setShowTeamSelector(true);
            });
        } else {
          const listenerHandlers = setupTeamListeners(teamId);
          cleanupFunction = listenerHandlers.cleanup;
          teamSetupCompletedRef.current = teamId;
        }
      })
      .catch(error => {
        console.error("Error validating team access:", error);
        setError("Error validating team access");
        setNoTeamAccess(true);
        setShowTeamSelector(true);
        
        setLocations([]);
        setAllUsers([]);
        setUserTracePaths({});
        setChatMessages([]);
      });
    
    return () => {
      cleanupFunction();
      
      if (currentUser && currentUser.uid && teamId) {
        updateUserOnlineStatus(db, false, currentUser, teamId)
          .then(() => console.log("User set to offline on cleanup"))
          .catch(err => console.error("Failed to set user offline:", err));
      }
      
      if (teamSetupCompletedRef.current === teamId) {
        teamSetupCompletedRef.current = false;
      }
    };
  }, [db, currentUser, teamId, isAdmin, hasTeamAccess, debugTeamAccess, redirectCheckCompleted, setupTeamListeners]);

  // ENHANCED: Backup refresh mechanism for team member locations
  useEffect(() => {
    if (!teamId || !currentUser) return;
    
    const refreshInterval = setInterval(() => {
      console.log("Backup refresh of team member locations");
      
      if (Math.random() < 0.1) { // Only 10% of the time to avoid overload
        console.log("Performing full team listener refresh");
        setTeamMembers(prev => [...prev]);
      }
    }, 5000);
    
    return () => clearInterval(refreshInterval);
  }, [teamId, currentUser]);

  // ENHANCED: Force immediate update when currentLocation changes
  useEffect(() => {
    if (!currentLocation || (!currentLocation.lat && !currentLocation.lng)) {
      return;
    }

    console.log("Parent: Current location updated - forcing immediate team sync:", currentLocation);
    
    // CRITICAL: Immediately update database when location changes
    if (db && currentUser && teamId && isClockedIn) {
      updateUserLocation(db, currentLocation, currentUser, teamId)
        .then(() => {
          console.log("Immediate location update successful");
        })
        .catch(err => console.error("Immediate location update failed:", err));
      
      updateUserTrace(db, currentLocation, currentUser, teamId)
        .catch(err => console.error("Immediate trace update failed:", err));
    }
  }, [currentLocation, db, currentUser, teamId, isClockedIn]);

  // NEW: Debug logging for location counts
  useEffect(() => {
    console.log("🔍 DEBUG: Location counts:", {
      totalLocations: locations.length,
      orderLocations: orderLocations.length,
      expandedOrderLocations: expandedOrderLocations.length,
      manualLocations: locations.filter(loc => !loc.sourceType || loc.sourceType !== 'order-address').length,
      orderAddressMarkers: locations.filter(loc => loc.sourceType === 'order-address').length
    });
    
    if (locations.length > 0) {
      const sampleLocation = locations.find(loc => loc.sourceType === 'order-address');
      if (sampleLocation) {
        console.log("📋 Sample order address marker:", {
          id: sampleLocation.id,
          name: sampleLocation.name,
          sourceType: sampleLocation.sourceType,
          position: sampleLocation.position,
          formattedAddress: sampleLocation.formattedAddress
        });
      }
    }
  }, [locations, orderLocations, expandedOrderLocations]);

  // ENHANCED: Debugging functions
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.debugTeamMembers = () => {
        console.log("Team Members Debug Info:", {
          teamId,
          teamMembersCount: teamMembers.length,
          allUsersCount: allUsers.length,
          onlineUsersCount: onlineUsers.length,
          currentLocation,
          trailColors: userTrailColorsRef.current,
          teamMembers: teamMembers.map(m => ({
            id: m.id,
            hasLocation: !!m.location,
            location: m.location ? `${m.location.lat}, ${m.location.lng}` : 'none',
            online: m.online,
            trailColor: m.trailColor
          })),
          userTracePaths: Object.keys(userTracePaths).map(userId => ({
            userId,
            trailLength: userTracePaths[userId]?.length || 0,
            color: userTracePaths[userId]?.color || 'unknown'
          }))
        });
      };
      
      window.forceTeamRefresh = () => {
        console.log("Forcing team refresh...");
        setTeamMembers(prev => [...prev]);
        setAllUsers(prev => [...prev]);
      };
      
      window.getTrailColors = () => {
        console.log("Trail Colors:", userTrailColorsRef.current);
        return userTrailColorsRef.current;
      };

      // NEW: Debug functions for order markers
      window.debugOrderMarkers = () => {
        console.log("Order Markers Debug:", {
          rawOrders: orderLocations.length,
          expandedMarkers: expandedOrderLocations.length,
          totalLocations: locations.length,
          orderAddressMarkers: locations.filter(loc => loc.sourceType === 'order-address').length,
          sampleOrder: orderLocations[0],
          sampleExpandedMarker: expandedOrderLocations[0]
        });
        
        return {
          orderLocations,
          expandedOrderLocations,
          allLocations: locations
        };
      };
      
      window.testOrderExpansion = () => {
        console.log("Testing order expansion...");
        const expanded = expandOrdersIntoAddressMarkers(orderLocations);
        console.log(`Expansion test: ${orderLocations.length} orders -> ${expanded.length} markers`);
        return expanded;
      };
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        delete window.debugTeamMembers;
        delete window.forceTeamRefresh;
        delete window.getTrailColors;
        delete window.debugOrderMarkers;
        delete window.testOrderExpansion;
      }
    };
  }, [teamId, teamMembers, allUsers, onlineUsers, currentLocation, userTracePaths, orderLocations, expandedOrderLocations, locations, expandOrdersIntoAddressMarkers]);

  // Add map component styles
  useEffect(() => {
    const style = document.createElement('style');
    style.id = 'map-component-styles';
    
    if (document.getElementById('map-component-styles')) {
      return;
    }
    
// In MapView.js, update the style section for better responsive layout:

// Replace the existing style.textContent with this updated version:
style.textContent = `
  .w-full.h-screen.flex.flex-col {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .main-content-area {
    display: flex;
    width: 100%;
    height: calc(100vh - 130px);
    flex: 1;
    overflow: hidden;
    position: relative;
  }
  
  .panel-container {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    flex-shrink: 0;
    background: linear-gradient(to bottom, #1f2937, #111827);
    border-color: #374151;
  }
  
  /* Prevent horizontal scroll in panels */
  .panel-container > * {
    overflow-x: hidden !important;
    max-width: 100%;
  }
  
  .map-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    height: 100%;
  }
  
  .map-container {
    width: 100%;
    height: 60% !important;
    position: relative;
    overflow: hidden;
  }
  
  .chat-container {
    width: 100%;
    height: 40% !important;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border-top: 1px solid #374151;
    background: linear-gradient(to right, #1f2937, #111827, #1f2937);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
  }
  
  .leaflet-container {
    width: 100% !important;
    height: 100% !important;
  }
  
  /* Enhanced responsive panel widths */
  @media (min-width: 1536px) {
    /* 2XL screens */
    .left-panel {
      width: 22%;
      min-width: 350px;
      max-width: 400px;
    }
    
    .right-panel {
      width: 22%;
      min-width: 350px;
      max-width: 400px;
    }
  }
  
  @media (min-width: 1280px) and (max-width: 1535px) {
    /* XL screens */
    .left-panel {
      width: 24%;
      min-width: 320px;
      max-width: 380px;
    }
    
    .right-panel {
      width: 24%;
      min-width: 320px;
      max-width: 380px;
    }
  }
  
  @media (min-width: 1024px) and (max-width: 1279px) {
    /* LG screens */
    .left-panel {
      width: 26%;
      min-width: 280px;
      max-width: 350px;
    }
    
    .right-panel {
      width: 26%;
      min-width: 280px;
      max-width: 350px;
    }
  }
  
  @media (min-width: 768px) and (max-width: 1023px) {
    /* MD screens (tablets) */
    .left-panel {
      width: 30%;
      min-width: 240px;
      max-width: 300px;
    }
    
    .right-panel {
      width: 30%;
      min-width: 240px;
      max-width: 300px;
    }
  }
  
  @media (max-width: 767px) {
    /* Mobile screens - stack vertically */
    .main-content-area {
      flex-direction: column;
      height: auto;
      min-height: calc(100vh - 130px);
      overflow: auto;
    }
    
    .left-panel, .right-panel {
      width: 100%;
      height: 40vh;
      min-height: 300px;
      max-width: none;
      border-bottom: 1px solid #374151;
      border-left: none;
      border-right: none;
    }
    
    .map-wrapper {
      width: 100%;
      height: 60vh;
      min-height: 400px;
    }
  }
  
  /* Ensure content doesn't overflow */
  .overflow-y-auto, .overflow-auto {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Fix for content overflow in panels */
  .panel-container button {
    white-space: normal;
    word-break: break-word;
  }
  
  .panel-container .truncate {
    max-width: 100%;
  }
`;
    document.head.appendChild(style);

    return () => {
      const existingStyle = document.getElementById('map-component-styles');
      if (existingStyle) {
        document.head.removeChild(existingStyle);
      }
    };
  }, []);
  
  // Add debugging functions to window
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.debugMapSession = () => {
        console.log("Current session state:", {
          redirectAttempted: sessionStorage.getItem('redirectAttempted'),
          isClockedIn,
          clockInTime: clockInTime?.toISOString(),
          teamSetupCompleted: teamSetupCompletedRef.current
        });
      };
      
      window.clearMapSession = () => {
        sessionStorage.removeItem('redirectAttempted');
        console.log("Session storage cleared manually");
      };
      
      // Add window reference to access all users
      window.allUsers = allUsers;
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        delete window.debugMapSession;
        delete window.clearMapSession;
        delete window.allUsers;
      }
    };
  }, [isClockedIn, clockInTime, allUsers]);

  // Clear redirect flag function
  const clearRedirectFlag = useCallback(() => {
    sessionStorage.removeItem('redirectAttempted');
    console.log("Cleared redirect attempt flag");
  }, []);

  // ENHANCED: Handle selecting locations
  const handleSelectLocation = useCallback((location) => {
    if (!location) {
      console.error("Attempted to select undefined location");
      return;
    }
    
    console.log("Selected location:", location);
    setDetailsPanelLocation(location);
    setSelectedLocation(location);
    
    if (location.preventRouting) {
      console.log("Skipping map destination setting due to preventRouting flag");
      return;
    }
    
    if (location.position && typeof window.setMapDestination === 'function') {
      window.setMapDestination(location.position.lat, location.position.lng);
    }
  }, []);

  // Show user in details panel
  const showUserInDetailsPanel = useCallback((user) => {
    if (!user) {
      console.error("Cannot show details for undefined user");
      return;
    }
    
    console.log("Showing user in details panel:", user);
    const userForDetailsPanel = {
      ...user,
      type: 'user',
      name: userDisplayNames[user.uid] || user.displayName || user.email || "Unknown User",
      photoBase64: userProfilePictures[user.uid],
      hasTrail: userTracePaths[user.uid] && userTracePaths[user.uid].length > 0,
      trailColor: userTrailColorsRef.current[user.uid] || getUserTrailColor(user.uid)
    };
    
    setDetailsPanelLocation(userForDetailsPanel);
    
    if (user.currentLocation && typeof window.setMapDestination === 'function') {
      window.setMapDestination(user.currentLocation.lat, user.currentLocation.lng);
    }
  }, [userDisplayNames, userProfilePictures, userTracePaths]);

  // Create team function
  const handleCreateTeam = useCallback(async (teamName, teamArea) => {
    if (!currentUser) return;
    
    try {
      setIsLoading(true);
      
      const newTeamId = await createTeam(db, {
        name: teamName,
        area: teamArea || null,
        areaId: teamArea?.id || null,
        teamSize: { spotters: 0, towDrivers: 0 }
      }, currentUser.uid);
      
      await addUserToTeam(db, currentUser.uid, newTeamId, 'admin', currentUser.uid);
      await handleTeamSelect(newTeamId);
      
      setIsLoading(false);
    } catch (error) {
      console.error("Error creating team:", error);
      setError("Failed to create team. Please try again.");
      setIsLoading(false);
    }
  }, [currentUser, db]);

  // Refresh map function
  const refreshMap = useCallback(() => {
    console.log("Refreshing map");
    setIsMapRefreshing(true);
    
    findMyLocation();
    
    if (typeof window !== 'undefined') {
      if (window.zoomedOutMapInstance) {
        window.zoomedOutMapInstance.invalidateSize();
      }
      if (window.zoomedInMapInstance) {
        window.zoomedInMapInstance.invalidateSize();
      }
    }
    
    setTimeout(() => {
      setIsMapRefreshing(false);
      
      if (window.setMapDestination && selectedLocation?.position) {
        window.setMapDestination(
          selectedLocation.position.lat, 
          selectedLocation.position.lng
        );
      }
    }, 1000);
  }, [findMyLocation, selectedLocation]);

  // Clear trail function
  const clearTrail = useCallback(() => {
    console.log("Clearing trail");
    if (currentUser && db && teamId) {
      clearUserTrace(db, currentUser.uid, currentUser, teamId)
        .then(() => {
          console.log("User trail cleared");
          setHasMovedSinceClockIn(false);
        })
        .catch(err => console.error("Failed to clear user trail:", err));
    }
  }, [currentUser, db, teamId]);

  // Trail management functions
  const confirmDeleteTrail = useCallback((userId) => {
    console.log("Confirming delete trail for user:", userId);
  }, []);
  
  const deleteUserTrail = useCallback(async (userId) => {
    console.log("Deleting trail for user:", userId);
    if (db && currentUser && isAdmin && teamId) {
      try {
        await clearUserTrace(db, userId, currentUser, teamId);
        console.log("User trail deleted successfully");
      } catch (error) {
        console.error("Error deleting user trail:", error);
      }
    }
  }, [db, currentUser, isAdmin, teamId]);

  // Navigate to user profile
  const navigateToUserProfile = useCallback(() => {
    console.log("Navigating to user profile page");
    navigate(`/profile/${currentUser.uid}`);
  }, [navigate, currentUser]);

  // Handle map click
  const handleMapClick = useCallback((e) => {
    console.log("Map clicked at:", e.latlng);
    
    if (isNavigating && typeof window.setMapDestination === 'function') {
      window.setMapDestination(e.latlng.lat, e.latlng.lng);
    }
  }, [isNavigating]);

  // Direct messaging
  const handleStartDM = useCallback((user) => {
    if (!user) {
      console.error("Cannot start DM with undefined user");
      return;
    }
    
    console.log("Starting DM with user:", user);
  }, []);

  // Mark location as picked up
  const markLocationAsPickedUp = useCallback(async (location) => {
    if (!location) {
      console.error("Cannot mark undefined location as picked up");
      return;
    }
    
    console.log("Marking location as picked up:", location);
    
    if (location.position && typeof window.setMapDestination === 'function') {
      window.setMapDestination(location.position.lat, location.position.lng);
    }
  }, []);

  // Format address
  const formatAddress = useCallback((address) => {
    if (!address) return 'No address';
    
    if (typeof address === 'string') {
      return address;
    }
    
    try {
      let parts = [];
      if (address.street) parts.push(address.street);
      
      let cityStateZip = '';
      if (address.city) cityStateZip += address.city;
      if (address.state) {
        if (cityStateZip) cityStateZip += ', ';
        cityStateZip += address.state;
      }
      if (address.zip) {
        if (cityStateZip) cityStateZip += ' ';
        cityStateZip += address.zip;
      }
      
      if (cityStateZip) parts.push(cityStateZip);
      
      return parts.length > 0 ? parts.join(', ') : 'Address details unavailable';
    } catch (error) {
      console.error("Error formatting address:", error);
      return 'Error formatting address';
    }
  }, []);

  // Fetch order details
  const fetchOrderDetails = useCallback(async (orderId) => {
    if (!orderId || !db) return null;
    
    try {
      const orderRef = doc(db, "orders", orderId);
      const orderSnap = await getDoc(orderRef);
      
      if (orderSnap.exists()) {
        const orderData = orderSnap.data();
        console.log(`Fetched order details for ${orderId}:`, orderData);
        
        setOrderDetails(prev => ({
          ...prev,
          [orderId]: orderData
        }));
        
        return orderData;
      } else {
        console.log(`No order found with ID ${orderId}`);
        return null;
      }
    } catch (error) {
      console.error(`Error fetching order details for ${orderId}:`, error);
      return null;
    }
  }, [db]);

// Navigation functions
const startNavigationWithLocation = useCallback((location) => {
  if (!location) {
    console.error("Cannot start navigation: No location provided");
    return;
  }
  
  try {
    console.log("Starting navigation with location:", location);
    
    setDestinationLocation(location);
    setIsNavigating(true);
    
    const distance = calculateDistance(currentLocation, location.position);
    setDistanceToDestination(distance);
    
    const timeInSeconds = (distance / 20) * 3600;
    setEstimatedTime(timeInSeconds);
    
    const now = new Date();
    const arrivalTimeMs = now.getTime() + (timeInSeconds * 1000);
    setArrivalTime(new Date(arrivalTimeMs));
    
    const latDiff = location.position.lat - currentLocation.lat;
    const lngDiff = location.position.lng - currentLocation.lng;
    
    if (Math.abs(latDiff) > Math.abs(lngDiff)) {
      setNavigationDirection(latDiff > 0 ? "north" : "south");
    } else {
      setNavigationDirection(lngDiff > 0 ? "east" : "west");
    }
    
    const addressToDisplay = location.formattedAddress || 
                            location.address || 
                            location.name || 
                            "Selected Destination";
    setDestinationAddress(addressToDisplay);
    
    console.log("Navigation started successfully to:", location.position);
    
    if (typeof window.setMapDestination === 'function') {
      window.setMapDestination(location.position.lat, location.position.lng);
    }
  } catch (error) {
    console.error("Error starting navigation:", error);
    setIsNavigating(false);
    setDestinationLocation(null);
  }
}, [currentLocation]);

// Start navigation
const startNavigation = useCallback(() => {
  if (!selectedLocation) {
    console.error("Cannot start navigation: No location selected");
    return;
  }
  
  console.log("Starting navigation to selected location:", selectedLocation);
  startNavigationWithLocation(selectedLocation);
}, [selectedLocation, startNavigationWithLocation]);

// Stop navigation
const stopNavigation = useCallback(() => {
  console.log("Stopping navigation");
  
  setIsNavigating(false);
  setDestinationLocation(null);
  setDistanceToDestination(null);
  setEstimatedTime(null);
  setNavigationDirection("straight");
  setArrivalTime(null);
  setDestinationAddress("");
  setNextInstruction(null);
  
  if (typeof window.resetMapRotation === 'function') {
    window.resetMapRotation();
  }
  
  if (typeof window.clearAllRoutes === 'function') {
    window.clearAllRoutes();
  }
}, []);

// Quick navigation
const navigateToQuickLink = useCallback((link) => {
  if (!link || !link.position || typeof link.position.lat !== 'number' || typeof link.position.lng !== 'number') {
    console.error("Invalid quick link data:", link);
    return;
  }
  
  console.log("Navigating to:", link.name);
  if (mapRef.current) {
    mapRef.current.setView([link.position.lat, link.position.lng], 14);
  }
  
  if (typeof window.setMapDestination === 'function') {
    window.setMapDestination(link.position.lat, link.position.lng);
  }
  
  const quickLinkLocation = {
    name: link.name,
    position: link.position,
    id: `quicklink-${Date.now()}`,
    type: 'quicklink'
  };
  
  startNavigationWithLocation(quickLinkLocation);
}, [mapRef, startNavigationWithLocation]);

// Address selection functions
const prepareAddressSelection = useCallback(async (location) => {
  if (!location) {
    console.error("Location is undefined in prepareAddressSelection");
    return false;
  }
  
  if (location.orderReference || location.sourceType === 'order') {
    const orderId = location.orderReference || location.id;
    
    try {
      let orderData = orderDetails[orderId];
      
      if (!orderData) {
        orderData = await fetchOrderDetails(orderId);
        
        if (!orderData) {
          console.error(`No order data found for ID: ${orderId}`);
          return false;
        }
      }
      
      if (orderData.addresses && orderData.addresses.length > 0) {
        setSelectedOrderForNavigation({
          ...location,
          orderData
        });
        
        const options = orderData.addresses.map((addr, index) => ({
          id: index,
          address: addr,
          formattedAddress: formatAddress(addr)
        }));
        
        setAddressOptions(options);
        setShowAddressSelector(true);
        return true;
      } else {
        console.log(`Order ${orderId} has no addresses to select from`);
        return false;
      }
    } catch (error) {
      console.error(`Error in address selection for order ${orderId}:`, error);
      return false;
    }
  }
  
  return false;
}, [orderDetails, fetchOrderDetails, formatAddress]);

// Navigate to selected address
const navigateToSelectedAddress = useCallback((addressOption) => {
  if (!selectedOrderForNavigation || !addressOption) {
    console.error("Missing selectedOrderForNavigation or addressOption");
    setShowAddressSelector(false);
    return;
  }
  
  try {
    console.log("Navigating to selected address:", addressOption);
    
    const position = addressOption.address.position || selectedOrderForNavigation.position;
    
    if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {
      console.error("Invalid position for navigation:", position);
      alert("This address doesn't have valid coordinates. Please select another address.");
      return;
    }
    
    const navigationLocation = {
      ...selectedOrderForNavigation,
      name: `${selectedOrderForNavigation.name} - ${addressOption.formattedAddress}`,
      selectedAddress: addressOption.address,
      formattedAddress: addressOption.formattedAddress,
      position: position,
      originalPosition: selectedOrderForNavigation.position
    };
    
    setSelectedLocation(navigationLocation);
    handleSelectLocation(navigationLocation);
    startNavigationWithLocation(navigationLocation);
    
    setShowAddressSelector(false);
    setSelectedOrderForNavigation(null);
  } catch (error) {
    console.error("Error in navigateToSelectedAddress:", error);
    setShowAddressSelector(false);
    setSelectedOrderForNavigation(null);
  }
}, [selectedOrderForNavigation, handleSelectLocation, startNavigationWithLocation]);

// Handle start navigation
const handleStartNavigation = useCallback(async (location) => {
  if (!location) {
    console.error("Location is undefined in handleStartNavigation");
    return;
  }
  
  try {
    console.log("Handle start navigation called with location:", location);
    
    setSelectedLocation(location);
    
    const showingSelector = await prepareAddressSelection(location);
    
    if (!showingSelector) {
      console.log("No address selection needed, navigating directly");
      handleSelectLocation(location);
      startNavigationWithLocation(location);
    } else {
      console.log("Address selection dialog opened");
    }
  } catch (error) {
    console.error("Error in handleStartNavigation:", error);
  }
}, [prepareAddressSelection, handleSelectLocation, startNavigationWithLocation]);

// Open image viewer
const openImageViewer = useCallback((images, initialIndex) => {
  console.log("Opening image viewer with", images.length, "images");
}, []);

// Handle finding a car
const handleFoundACar = useCallback(() => {
  console.log("Car found!");
  setIsAddingMarker(true);
}, []);

// Handle VIN search
const handleVinSearch = useCallback(() => {
  console.log("Searching for VIN:", vinSearch);
}, [vinSearch]);

// Get user routes height
const getUserRoutesHeight = useCallback(() => {
  const { isSmallScreen, isMediumScreen, height } = screenConfig;
  
  if (isSmallScreen) {
    return Math.min(300, height - 200);
  } else if (isMediumScreen) {
    return Math.min(500, height - 200);
  } else {
    return Math.min(600, height - 200);
  }
}, [screenConfig]);

// Handle team selection
const handleTeamSelect = useCallback(async (selectedTeamId) => {
  setIsLoading(true);
  try {
    if (!currentUser) {
      console.error("Cannot select team: User is not authenticated");
      setError("Please log in to select a team");
      setIsLoading(false);
      return;
    }
    
    teamSetupCompletedRef.current = false;
    
    console.log(`Attempting to select team: ${selectedTeamId} for user: ${currentUser.uid}`);
    
    if (locationsListenerRef.current) {
      locationsListenerRef.current();
      locationsListenerRef.current = null;
    }
    
    if (userLocationsListenerRef.current) {
      userLocationsListenerRef.current();
      userLocationsListenerRef.current = null;
    }
    
    if (userTracesListenerRef.current) {
      userTracesListenerRef.current();
      userTracesListenerRef.current = null;
    }
    
    if (chatListenerRef.current) {
      chatListenerRef.current();
      chatListenerRef.current = null;
    }
    
    setChatMessages([]);
    setChatInitialized(false);
    
    await ensureUserInTeam(db, currentUser.uid, selectedTeamId);
    
    const hasAccess = await hasTeamAccess(selectedTeamId);
    
    if (!hasAccess && !isAdmin) {
      console.error(`Access denied: User ${currentUser.uid} is not a member of team ${selectedTeamId}`);
      
      await ensureUserInTeam(db, currentUser.uid, selectedTeamId);
      
      const secondCheck = await hasTeamAccess(selectedTeamId);
      if (!secondCheck) {
        console.log("Creating bypass record for team access...");
        await createBypassRecord(currentUser.uid, {
          bypassTeamCheck: true,
          teams: [selectedTeamId],
          notes: 'Auto-created during team selection'
        });
        
        const finalCheck = await hasTeamAccess(selectedTeamId);
        if (!finalCheck) {
          setError("You don't have permission to access this team");
          setNoTeamAccess(true);
          setShowTeamSelector(true);
          setIsLoading(false);
          return;
        }
      }
    }
    
    const teamData = await getTeamData(db, selectedTeamId);
    if (!teamData) {
      setError("Could not load team data");
      setIsLoading(false);
      return;
    }
    
    if (isAdmin) {
      console.log("Admin accessing team. Ensuring admin is a team member...");
      try {
        await ensureUserInTeam(db, currentUser.uid, selectedTeamId, 'admin');
      } catch (addError) {
        console.error("Error adding admin to team:", addError);
      }
    }
    
    setTeamId(selectedTeamId);
    setTeamName(teamData.name);
    setTeamData(teamData);
    setShowTeamSelector(false);
    setNoTeamAccess(false);
    
    localStorage.setItem('lastUsedTeamId', selectedTeamId);
    
    setLocations([]);
    setAllUsers([]);
    setUserTracePaths({});
    
    if (currentUser && lastKnownPositionRef.current) {
      await updateUserLocation(db, lastKnownPositionRef.current, currentUser, selectedTeamId)
        .catch(err => console.error("Failed to update user location with new team:", err));
    }
    
    setIsLoading(false);
  } catch (error) {
    console.error("Error selecting team:", error);
    setError("Error selecting team");
    setIsLoading(false);
  }
}, [currentUser, db, isAdmin, hasTeamAccess]);

// Callback to handle zooming to a specific zone
const handleZoomToZone = useCallback((zoneId) => {
  if (!mapRef.current || !zones) return;
  
  const zone = zones.find(z => z.id === zoneId);
  if (!zone || !zone.bounds) {
    console.error("Zone not found or has no bounds:", zoneId);
    return;
  }
  
  try {
    const center = [
      (zone.bounds.northEast.lat + zone.bounds.southWest.lat) / 2,
      (zone.bounds.northEast.lng + zone.bounds.southWest.lng) / 2
    ];
    
    mapRef.current.setView(center, 15);
    
    const bounds = [
      [zone.bounds.southWest.lat, zone.bounds.southWest.lng],
      [zone.bounds.northEast.lat, zone.bounds.northEast.lng]
    ];
    
    mapRef.current.fitBounds(bounds, { padding: [50, 50] });
    
    console.log(`Zoomed to zone: ${zone.name}`);
  } catch (error) {
    console.error("Error zooming to zone:", error);
  }
}, [mapRef, zones]);

// Address selector component
const AddressSelectorDialog = React.memo(() => {
  if (!showAddressSelector) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[2000]">
      <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg w-full max-w-md p-4">
        <h3 className="text-lg font-semibold text-white mb-2">Select Address for Navigation</h3>
        <p className="text-gray-300 text-sm mb-4">
          Choose which address you would like to navigate to:
        </p>
        
        <div className="max-h-60 overflow-y-auto mb-4">
          {addressOptions.map((option) => (
            <button
              key={option.id}
              onClick={() => navigateToSelectedAddress(option)}
              className="w-full text-left p-3 mb-2 bg-gray-700 hover:bg-gray-600 text-gray-200 rounded flex items-center transition-colors duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-400 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
              <span className="text-sm">{option.formattedAddress}</span>
            </button>
          ))}
        </div>
        
        <div className="flex justify-end">
          <button 
            onClick={() => setShowAddressSelector(false)}
            className="px-4 py-2 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors duration-200"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
});

// Enhanced Admin Team Selector Overlay
const AdminTeamSelector = React.memo(() => (
  <div className="team-selector-overlay">
    <div className="bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">
          {isAdmin ? "Admin Team Selection" : "Team Selection"}
        </h2>
        <button 
          onClick={() => setShowTeamSelector(false)}
          className="text-gray-400 hover:text-white"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      {isAdmin && (
        <div className="mb-4 p-3 bg-purple-900 bg-opacity-40 rounded-md border border-purple-700">
          <div className="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-400 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
            </svg>
            <p className="text-sm text-purple-200">
              Admin Mode: You can access all teams for monitoring purposes. When you join a team, you'll be automatically added as an admin if not already a member.
            </p>
          </div>
        </div>
      )}
      
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : userTeams.length > 0 ? (
        <div className="space-y-3 max-h-80 overflow-y-auto pr-1">
          {userTeams.map(team => (
            <button
              key={team.id}
              onClick={() => handleTeamSelect(team.id)}
              className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-blue-600 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors flex justify-between items-center"
            >
              <div className="flex items-center">
                <span>{team.name}</span>
                {team.id === teamId && (
                  <span className="ml-2 bg-green-600 text-xs px-2 py-0.5 rounded-full">Current</span>
                )}
              </div>
              <span className="text-xs text-gray-400">{team.area?.name || 'No Area'}</span>
            </button>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="mt-2 text-gray-400">No teams available.</p>
          {isAdmin && (
            <button
              onClick={() => {
                setShowTeamSelector(false);
              }}
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md"
            >
              Create New Team
            </button>
          )}
        </div>
      )}
      
      {isAdmin && userTeams.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-700">
          <button
            onClick={() => {
              setShowTeamSelector(false);
            }}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-medium py-2 px-4 rounded-md"
          >
            Team Management
          </button>
        </div>
      )}
      
      <div className="mt-4 flex justify-end">
        <button
          onClick={() => setShowTeamSelector(false)}
          className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
));

// Wait until redirect check is complete before rendering full UI
if (!redirectCheckCompleted && !isAdmin) {
  return (
    <div className="w-full h-screen flex items-center justify-center bg-gray-900 text-white">
      <div className="flex flex-col items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <p className="text-gray-300">Loading application...</p>
      </div>
    </div>
  );
}

// If team selector is showing and user has no team access or no teams, render access message
if (showTeamSelector && (noTeamAccess || userTeams.length === 0)) {
  if (!currentUser) {
    return (
      <div className="team-selector-overlay">
        <div className="bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
          <h2 className="text-xl font-bold mb-4 text-center">Authentication Required</h2>
          <p className="text-gray-300 mb-4">You must be logged in to access teams.</p>
          <p className="text-red-400 mb-4">Session expired or authentication failed. Please refresh the page and try again.</p>
          <button 
            onClick={() => window.location.reload()}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col h-screen w-full">
      {/* Header with user info and datetime */}
      <div className="flex-none bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white p-2 shadow-lg border-b border-gray-700">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">NWR</h1>
            <div className="hidden md:flex items-center ml-4 text-gray-300 text-sm">
              <span>{currentDateTime.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })}</span>
              <span className="mx-2">•</span>
              <span>{formatClockTime(currentDateTime)}</span>
            </div>
          </div>
          
          <div className="flex items-center">
            <div className="flex items-center">
              <span className="mr-3 text-sm hidden md:inline">{currentUser?.displayName || currentUser?.email || "Unknown User"}</span>
              <div className="w-10 h-10 bg-gray-700 rounded-full overflow-hidden cursor-pointer shadow-md border-2 border-gray-600 hover:border-blue-400 flex items-center justify-center">
                {userProfilePictures[currentUser?.uid] ? (
                  <img 
                    src={userProfilePictures[currentUser.uid]} 
                    alt="Your Profile" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-white bg-blue-600">
                    {currentUser?.displayName ? 
                      currentUser.displayName.charAt(0).toUpperCase() : 
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    }
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Show team access message */}
      <div className="flex-grow flex items-center justify-center bg-gray-900 bg-opacity-90">
        <div className="bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
          <h2 className="text-xl font-bold mb-4 text-center">Team Access</h2>
          {noTeamAccess ? (
            <div>
              <p className="text-red-400 mb-4">You don't have access to the requested team.</p>
              {userTeams.length > 0 ? (
                <div>
                  <p className="text-gray-300 mb-4">Please select one of your assigned teams:</p>
                  <div className="space-y-2">
                    {userTeams.map(team => (
                      <button
                        key={team.id}
                        onClick={() => handleTeamSelect(team.id)}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                      >
                        {team.name}
                      </button>
                    ))}
                  </div>
                </div>
              ) : (
                <p className="text-gray-300 mb-4">You don't have access to any teams. Please contact an administrator.</p>
              )}
            </div>
          ) : (
            <p className="text-gray-300 mb-4">You don't have access to any teams. Please contact an administrator.</p>
          )}
        </div>
      </div>
    </div>
  );
}

return (
  <div
    ref={appContainerRef}
    className="w-full h-screen flex flex-col bg-gray-900 text-white overflow-hidden"
  >
    {/* Location Permission Prompt Overlay */}
    {locationPromptShown && (
      <div className="location-prompt-overlay">
        <div className="location-prompt-card">
          <h2 className="text-xl font-bold mb-4 text-center">Location Access</h2>
          <p className="text-gray-300 mb-6">
            For the best experience, this app needs to access your location. This helps us show your position on the map and find nearby locations.
          </p>
          <div className="flex flex-col space-y-3">
            <button
              onClick={findMyLocation}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors"
            >
              Enable Location
            </button>
            <button
              onClick={skipLocationRequest}
              className="w-full bg-gray-700 hover:bg-gray-600 text-gray-300 font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-colors"
            >
              Skip for Now
            </button>
          </div>
        </div>
      </div>
    )}
    
    {/* Address Selector Dialog */}
    <AddressSelectorDialog />
    
    {/* Admin Team Selector Overlay */}
    {showTeamSelector && <AdminTeamSelector />}
    
    {/* Zone Detection Component */}
    <ZoneDetection 
      teamId={teamId}
      currentUser={currentUser}
      currentLocation={currentLocation}
      isClockedIn={isClockedIn}
    />
    
    {/* Header with Team Information */}
    <div className="flex-none bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white p-2 shadow-lg border-b border-gray-700">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <h1 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">NWR</h1>
          <div className="hidden md:flex items-center ml-4 text-gray-300 text-sm">
            <span>{currentDateTime.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })}</span>
            <span className="mx-2">•</span>
            <span>{formatClockTime(currentDateTime)}</span>
          </div>
          
          {/* Team Indicator */}
          {teamData && (
            <div className="ml-4 px-3 py-1 bg-gradient-to-r from-blue-800 to-indigo-800 rounded-lg shadow-inner">
              <span className="text-xs text-blue-300 uppercase">Team:</span>
              <span className="ml-1 text-white font-semibold">{teamName}</span>
            </div>
          )}

          {/* Admin-Only Team Switch Button */}
          {isAdmin && (
            <button
              onClick={() => setShowTeamSelector(true)}
              className="ml-2 flex items-center px-2 py-1 bg-gradient-to-r from-purple-800 to-blue-800 hover:from-purple-700 hover:to-blue-700 rounded-md text-xs text-white shadow-md border border-gray-700 transition-colors duration-200"
              title="Switch teams (Admin only)"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z" />
                <path d="M12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
              </svg>
              Switch
            </button>
          )}
          
          {/* Active Zone Status Indicator */}
          <div className="ml-2">
            <ActiveZoneStatus 
              teamId={teamId}
              currentUser={currentUser}
              currentLocation={currentLocation}
              isClockedIn={isClockedIn}
            />
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Camera Status Indicators */}
          <CameraStatusIndicators teamId={teamId} />
          
          {/* Team Zones Button */}
          <TeamZonesButton 
            teamId={teamId}
            isAdmin={isAdmin}
            currentUser={currentUser}
            mapRef={mapRef}
            userDisplayNames={userDisplayNames}
            userProfilePictures={userProfilePictures}
            teamMembers={teamMembers}
          />
          
          {/* Location button */}
          <button
            onClick={findMyLocation}
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-3 py-1 rounded-full shadow-md"
            title="Find my location"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
            </svg>
          </button>
          
          {/* Clock in/out status indicator */}
          <div className={`clock-status ml-2 rounded-lg px-3 py-1 shadow-md ${isClockedIn ? 'bg-gradient-to-r from-green-600 to-green-700' : 'bg-gradient-to-r from-red-600 to-red-700'}`}>
            {isClockedIn ? (
              <span className="flex items-center">
                <span className="inline-block w-2 h-2 bg-green-300 rounded-full animate-pulse mr-2"></span>
                Clocked In {clockInTime ? formatClockTime(clockInTime) : ''}
              </span>
            ) : (
              <span>Clocked Out</span>
            )}
          </div>

          {/* User profile picture */}
          <div 
            onClick={navigateToUserProfile}
            className="w-10 h-10 bg-gray-700 rounded-full overflow-hidden cursor-pointer ml-2 shadow-md border-2 border-gray-600 hover:border-blue-400 flex items-center justify-center"
            title="Your Profile"
          >
            {userProfilePictures[currentUser?.uid] ? (
              <img 
                src={userProfilePictures[currentUser.uid]} 
                alt="Your Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-white bg-blue-600">
                {currentUser?.displayName ? 
                  currentUser.displayName.charAt(0).toUpperCase() : 
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                }
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
    
    {/* Stats and Vehicle Finder */}
    <div className="flex-none">
      <Stats 
        teamName={teamName}
        teamStats={teamStats}
        isNavigating={false}
        navigationDirection={navigationDirection}
        distanceToDestination={distanceToDestination}
        estimatedTime={estimatedTime}
        arrivalTime={arrivalTime}
        destinationAddress={destinationAddress}
        stopNavigation={stopNavigation}
        formatDistance={formatDistance}
        formatTime={formatTime}
        formatClockTime={formatClockTime}
        currentUser={currentUser}
        isAdmin={isAdmin}
        userRole={teamData?.role || ''}
        teamSize={teamData?.teamSize || { spotters: 0, towDrivers: 0 }}
        initialMinimized={true}
      />

      <VehicleFinder 
        handleFoundACar={handleFoundACar}
        vinSearch={vinSearch}
        setVinSearch={setVinSearch}
        handleVinSearch={handleVinSearch}
        teamId={teamId}
        setDetailsPanelLocation={setDetailsPanelLocation}
        setDetailsVisible={(isVisible) => {
          console.log("Details visibility changed to:", isVisible);
        }}
        currentUser={currentUser}
      />
    </div>
    
    {/* Navigation HUD - shows above the map container when navigation is active */}
    <NavigationHUD
      isNavigating={isNavigating}
      navigationDirection={navigationDirection}
      distanceToDestination={distanceToDestination}
      estimatedTime={estimatedTime}
      arrivalTime={arrivalTime}
      destinationAddress={destinationAddress}
      stopNavigation={stopNavigation}
      formatDistance={formatDistance}
      formatTime={formatTime}
      formatClockTime={formatClockTime}
      nextInstruction={nextInstruction}
      destinationLocation={destinationLocation}
    />
    
    {/* Main content area - All sections visible simultaneously */}
    <div className="main-content-area">
      {/* Left Panel (Locations) */}
      <div className="panel-container left-panel border-r border-gray-700">
        <div className="flex-none bg-gradient-to-r from-gray-900 to-gray-800 p-2 border-b border-gray-700">
          <h2 className="font-semibold text-sm text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Locations</h2>
        </div>
        
        <div className="overflow-y-auto" style={{height: "calc(100% - 40px)"}}>
          <LocationsPanel 
            userRoutesHeight={getUserRoutesHeight()}
            allUsers={allUsers}
            locations={locations}
            selectedLocation={selectedLocation}
            handleSelectLocation={handleSelectLocation}
            userDisplayNames={userDisplayNames}
            userProfilePictures={userProfilePictures}
            handleStartDM={handleStartDM}
            confirmDeleteTrail={confirmDeleteTrail}
            isAdmin={isAdmin}
            currentUser={currentUser}
            calculateDistance={calculateDistance}
            formatDistance={formatDistance}
            quickNavLinks={quickNavLinks}
            navigateToQuickLink={navigateToQuickLink}
            updateQuickNavLink={updateQuickNavLink}
            screenConfig={screenConfig}
            optimizedRoute={optimizedRoute || []}
            userTracePaths={userTracePaths || {}}
            closestPendingLocation={closestPendingLocation}
            isTowTruckUser={isTowTruckUser}
            currentLocation={currentLocation}
            startNavigation={handleStartNavigation}
            markLocationAsPickedUp={markLocationAsPickedUp}
            setDetailsPanelLocation={setDetailsPanelLocation}
            setDetailsVisible={() => {}}
            teamId={teamId}
            orderDetails={orderDetails}
            setOrderDetails={setOrderDetails}
            showAddressSelector={showAddressSelector}
            setShowAddressSelector={setShowAddressSelector}
            addressOptions={addressOptions}
            setAddressOptions={setAddressOptions}
            selectedOrderForNavigation={selectedOrderForNavigation}
            setSelectedOrderForNavigation={setSelectedOrderForNavigation}
            formatAddress={formatAddress}
            fetchOrderDetails={fetchOrderDetails}
            prepareAddressSelection={prepareAddressSelection}
            navigateToSelectedAddress={navigateToSelectedAddress}
            deleteUserTrail={deleteUserTrail}
            teamMembers={teamMembers}
            zones={zones}
            activeZones={activeZones}
            teamVehicles={teamVehicles} // ADD THIS LINE
          />
        </div>
      </div>
      
      {/* Center (Map and Chat) */}
      <div className="map-wrapper">
        {/* Map Display with AlertComp positioned in map container */}
        <div className={`map-container ${mapContainerClass}`}>
          {/* Add AlertComp here, before MapDisplay */}
          <AlertComp 
            currentUser={currentUser} 
            isAdmin={isAdmin} 
            teamId={teamId}
            setMapContainerClass={setMapContainerClass} 
          />
          
          <MapDisplay 
            key={`map-display-${teamId || 'no-team'}`}
            mapRef={mapRef}
            mapContainerRef={mapContainerRef}
            currentMarkerRef={currentMarkerRef}
            userLabelRef={userLabelRef}
            markerClusterRef={markerClusterRef}
            userMarkerClusterRef={userMarkerClusterRef}
            routingControlRef={routingControlRef}
            navigationElementsRef={navigationElementsRef}
            breadcrumbPathsRef={breadcrumbPathsRef}
            currentLocation={currentLocation}
            handleMapClick={handleMapClick}
            isAddingMarker={isAddingMarker}
            isAddingAdminMarker={isAddingAdminMarker}
            isSettingLocation={isSettingLocation}
            newMarkerPosition={newMarkerPosition}
            setNewMarkerPosition={setNewMarkerPosition}
            lastKnownPositionRef={lastKnownPositionRef}
            error={error}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
            isMapRefreshing={isMapRefreshing}
            findMyLocation={findMyLocation}
            refreshMap={refreshMap}
            clearTrail={clearTrail}
            isAdmin={isAdmin}
            isClockedIn={isClockedIn}
            hasMovedSinceClockIn={hasMovedSinceClockIn}
            showUserActionPopup={false}
            userForAction={null}
            userActionPopupPosition={null}
            setShowUserActionPopup={() => {}}
            handleStartDM={handleStartDM}
            userDisplayNames={userDisplayNames}
            userProfilePictures={userProfilePictures}
            showUserInDetailsPanel={showUserInDetailsPanel}
            confirmDeleteTrail={confirmDeleteTrail}
            deleteUserTrail={deleteUserTrail}
            showDeleteConfirmation={false}
            setShowDeleteConfirmation={() => {}}
            userToDeleteTrail={null}
            screenConfig={screenConfig}
            teamId={teamId}
            setNavigationDirection={setNavigationDirection}
            setDistanceToDestination={setDistanceToDestination}
            setEstimatedTime={setEstimatedTime}
            setArrivalTime={setArrivalTime}
            setDestinationAddress={setDestinationAddress}
            onNavigationStatusChange={(status) => {
              if (status.nextInstruction) {
                setNextInstruction(status.nextInstruction);
              }
              
              if (status.isNavigating && !isNavigating) {
                setIsNavigating(true);
              }
            }}
            teamMembers={teamMembers}
            onlineUsers={onlineUsers}
            currentUser={currentUser}
            locations={locations}
            handleSelectLocation={handleSelectLocation}
            zones={zones}
            activeZones={activeZones}
            handleZoomToZone={handleZoomToZone}
            updateUserLocationInDatabase={(location) => {
              if (currentUser && teamId) {
                updateUserLocation(db, location, currentUser, teamId)
                  .catch(err => console.error("Failed to update user location in database:", err));
                
                updateUserTrace(db, location, currentUser, teamId)
                  .catch(err => console.error("Failed to update user trace in database:", err));
              }
            }}
          />
        </div>
        
        {/* Chat Interface */}
        <div className="chat-container">
          <div className="flex-none bg-gradient-to-r from-gray-900 to-gray-800 p-2 border-b border-gray-700 flex justify-between items-center">
            <h3 className="font-semibold text-sm text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">{teamName} Chat</h3>
            <span className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded-full shadow-inner">
              {chatMessages?.length || 0} messages{!chatInitialized ? ' (loading...)' : ''}
            </span>
          </div>
          
          <div className="h-[calc(100%-40px)] w-full relative overflow-hidden">
            <MemoizedChatInterface 
              chatMessages={chatMessages}
              setChatMessages={setChatMessages}
              newChatMessage={newChatMessage}
              setNewChatMessage={setNewChatMessage}
              currentUser={currentUser}
              mediaToUpload={mediaToUpload}
              setMediaToUpload={setMediaToUpload}
              userProfilePictures={userProfilePictures}
              openImageViewer={openImageViewer}
              teamId={teamId}
            />
          </div>
        </div>
      </div>
      
      {/* Right Panel (Details) */}
      <div className="panel-container right-panel border-l border-gray-700">
        <div className="flex-none bg-gradient-to-r from-gray-800 to-gray-900 p-2 border-b border-gray-700">
          <h2 className="font-semibold text-sm text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Details</h2>
        </div>
        
        <div className="overflow-y-auto" style={{height: "calc(100% - 40px)"}}>
          <DetailsPanel 
            detailsPanelLocation={detailsPanelLocation}
            detailsVisible={true}
            setDetailsVisible={(isVisible) => {
               console.log("Details visibility changed to:", isVisible);
            }}
            userDisplayNames={userDisplayNames}
            userProfilePictures={userProfilePictures}
            handleStartDM={handleStartDM}
            confirmDeleteTrail={confirmDeleteTrail}
            isAdmin={isAdmin}
            handleSelectLocation={handleSelectLocation}
            startNavigation={startNavigation}
            markLocationAsPickedUp={markLocationAsPickedUp}
            handleDeleteMarker={() => {}}
            isTowTruckUser={isTowTruckUser}
            currentUser={currentUser}
            currentLocation={currentLocation}
            calculateDistance={calculateDistance}
            mapRef={mapRef}
            openImageViewer={openImageViewer}
            screenConfig={screenConfig}
            teamId={teamId}
            zones={zones}
            setShowZoneAssignments={setShowZoneAssignments}
            handleZoomToZone={handleZoomToZone}
          />
        </div>
      </div>
    </div>
  </div>
);
}

export default MapView;