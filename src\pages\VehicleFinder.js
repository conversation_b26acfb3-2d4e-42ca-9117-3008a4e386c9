import React, { useState, useEffect, useRef } from 'react';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  addDoc, 
  serverTimestamp 
} from 'firebase/firestore';
import { db } from './firebase'; // Make sure this path is correct

const VehicleFinder = ({ 
  handleFoundACar,
  setDetailsVisible, 
  setDetailsPanelLocation,
  currentUser,
  vinSearch,
  setVinSearch,
  handleVinSearch,
  teamId
}) => {
  const [searchInput, setSearchInput] = useState('');
  const [searchType, setSearchType] = useState('vin');
  const [isLoading, setIsLoading] = useState(false);
  const [searchError, setSearchError] = useState(null);
  const [searchSuccess, setSearchSuccess] = useState(false);
  const [recentSearches, setRecentSearches] = useState([]);
  const [isLoadingRegistration, setIsLoadingRegistration] = useState(false);
  
  // Ref to store success message timeout
  const successTimeoutRef = useRef(null);

  // Clear search success after a timeout
  useEffect(() => {
    if (searchSuccess) {
      // Clear any existing timeout
      if (successTimeoutRef.current) {
        clearTimeout(successTimeoutRef.current);
      }
      
      // Set new timeout to clear success message after 3 seconds
      successTimeoutRef.current = setTimeout(() => {
        setSearchSuccess(false);
      }, 3000);
    }
    
    // Cleanup function
    return () => {
      if (successTimeoutRef.current) {
        clearTimeout(successTimeoutRef.current);
      }
    };
  }, [searchSuccess]);

  // Load recent searches from localStorage on component mount
  useEffect(() => {
    loadRecentSearches();
  }, []);

  // Debug log to confirm props are available
  useEffect(() => {
    console.log("VehicleFinder Props:", {
      hasSetDetailsVisible: typeof setDetailsVisible === 'function',
      hasSetDetailsPanelLocation: typeof setDetailsPanelLocation === 'function',
      hasCurrentUser: !!currentUser
    });
  }, [setDetailsVisible, setDetailsPanelLocation, currentUser]);

  // Load recent searches from localStorage
  const loadRecentSearches = () => {
    try {
      const storedSearches = localStorage.getItem('recentVehicleSearches');
      if (storedSearches) {
        const parsedSearches = JSON.parse(storedSearches);
        // Filter out searches older than a week (7 days)
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        
        const filteredSearches = parsedSearches.filter(search => {
          const searchDate = new Date(search.timestamp);
          return searchDate > oneWeekAgo;
        });
        
        setRecentSearches(filteredSearches);
        
        // If we filtered out old searches, update localStorage
        if (filteredSearches.length !== parsedSearches.length) {
          localStorage.setItem('recentVehicleSearches', JSON.stringify(filteredSearches));
        }
      }
    } catch (error) {
      console.error('Error loading recent searches:', error);
    }
  };

  // Save a search to recent searches
  const saveToRecentSearches = (query, type) => {
    try {
      // Create a new search object
      const newSearch = {
        query,
        type,
        timestamp: new Date().toISOString()
      };
      
      // Add to existing searches
      const updatedSearches = [newSearch, ...recentSearches.filter(s => 
        // Only keep searches that aren't the same as our new one
        !(s.query === query && s.type === type)
      )].slice(0, 10); // Keep only the 10 most recent searches
      
      // Update state and localStorage
      setRecentSearches(updatedSearches);
      localStorage.setItem('recentVehicleSearches', JSON.stringify(updatedSearches));
    } catch (error) {
      console.error('Error saving recent search:', error);
    }
  };

  // Apply a recent search
  const applyRecentSearch = (search) => {
    setSearchType(search.type);
    setSearchInput(search.query);
    // Optionally auto-search when clicking a recent search
    handleSearch(search.query, search.type);
  };

  // Clear all recent searches
  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('recentVehicleSearches');
  };

  // Dismiss success message manually
  const dismissSuccessMessage = () => {
    if (successTimeoutRef.current) {
      clearTimeout(successTimeoutRef.current);
      successTimeoutRef.current = null;
    }
    setSearchSuccess(false);
  };

  // Handle search
  const handleSearch = async (inputOverride, typeOverride) => {
    const input = inputOverride || searchInput;
    const type = typeOverride || searchType;
    
    // Reset states
    setSearchSuccess(false);
    
    if (!input.trim()) {
      setSearchError("Please enter a search term");
      return;
    }

    // Save this search to recent searches
    saveToRecentSearches(input.trim(), type);

    setIsLoading(true);
    setSearchError(null);

    try {
      // First check if the vehicle already exists in your Firestore database
      if (type === 'vin') {
        const vehiclesRef = collection(db, 'vehicles');
        const q = query(vehiclesRef, where('vin', '==', input.trim().toUpperCase()));
        const querySnapshot = await getDocs(q);
        
        if (!querySnapshot.empty) {
          // Vehicle exists in database
          const vehicleDoc = querySnapshot.docs[0];
          const vehicleData = vehicleDoc.data();
          
          const vehicleInfo = {
            ...vehicleData,
            id: vehicleDoc.id,
            fromDatabase: true
          };
          
          loadVehicleToDetailsPanel(vehicleInfo);
          setSearchSuccess(true);
          setIsLoading(false);
          return;
        }
      }

      // If not found in database, use appropriate API based on search type
      if (type === 'vin') {
        // Use NHTSA VIN decoder API
        const vin = input.trim();
        const response = await fetch(`https://vpic.nhtsa.dot.gov/api/vehicles/decodevin/${vin}?format=json`);
        
        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.Results && data.Results.length > 0) {
          // Process NHTSA results into a vehicle object
          const nhtsaData = data.Results;
          
          // Extract vehicle details from NHTSA data
          const getValueByVariable = (variable) => {
            const item = nhtsaData.find(item => item.Variable === variable);
            return item ? item.Value : null;
          };
          
          const vehicleInfo = {
            vin: vin.toUpperCase(),
            make: getValueByVariable("Make") || 'Unknown',
            model: getValueByVariable("Model") || 'Unknown',
            year: getValueByVariable("Model Year") || 'Unknown',
            vehicleType: getValueByVariable("Vehicle Type") || 'Unknown',
            bodyClass: getValueByVariable("Body Class") || 'Unknown',
            engine: getValueByVariable("Engine Model") || 'Unknown',
            fuelType: getValueByVariable("Fuel Type - Primary") || 'Unknown',
            transmission: getValueByVariable("Transmission Style") || 'Unknown',
            driveType: getValueByVariable("Drive Type") || 'Unknown',
            color: 'N/A', // NHTSA doesn't provide color info
            licensePlate: 'N/A', // NHTSA doesn't provide license plate info
            source: 'NHTSA VIN Decoder'
          };
          
          // Automatically fetch registration info when we get basic vehicle info
          fetchRegistrationInfo(vin, vehicleInfo);
        } else {
          setSearchError('No vehicle information found for this VIN');
          setIsLoading(false);
        }
      } else {
        // For non-VIN searches (license plate, title number), use vincheck.info
        // This would be the actual implementation when you have API access
        // For now, we'll use mock data
        const mockApiCall = () => {
          return new Promise((resolve) => {
            setTimeout(() => {
              if (input.length >= 4) {
                resolve({
                  success: true,
                  vehicle: {
                    vin: 'SAMPLE' + Math.random().toString(36).substring(2, 10).toUpperCase(),
                    make: 'Sample Make',
                    model: 'Sample Model',
                    year: '2023',
                    color: 'Blue',
                    licensePlate: type === 'licensePlate' ? input.toUpperCase() : 'ABC123',
                    titleNumber: type === 'titleNumber' ? input.toUpperCase() : '*********',
                    ownerName: 'Sample Owner',
                    titleStatus: 'CLEAN'
                  }
                });
              } else {
                resolve({
                  success: false,
                  message: 'No vehicle found with the provided information'
                });
              }
            }, 1500); // Simulate network delay
          });
        };

        const response = await mockApiCall();

        if (response.success) {
          // If we searched by license plate or title, also fetch registration info
          if (response.vehicle.vin) {
            fetchRegistrationInfo(response.vehicle.vin, response.vehicle);
          } else {
            loadVehicleToDetailsPanel(response.vehicle);
            setSearchSuccess(true);
            setIsLoading(false);
          }
        } else {
          setSearchError(response.message || 'No vehicle found');
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchError('Error performing search. Please try again.');
      setIsLoading(false);
    }
  };

  // Fetch vehicle registration information from vincheck.info
  const fetchRegistrationInfo = async (vin, vehicleData) => {
    setIsLoadingRegistration(true);
    
    try {
      // This would be the actual API call to vincheck.info
      // For demonstration purposes, we'll use mock data
      // When implementing the real API, replace this with actual API calls
      
      // Mock API call to simulate vincheck.info service
      const mockRegistrationCall = () => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              success: true,
              registrationData: {
                licensePlate: generateRandomPlate(),
                registrationState: "IL",
                registrationExpiry: getRandomFutureDate(),
                registeredOwner: "REGISTERED OWNER INFORMATION",
                registeredAddress: "123 MAIN ST, CHICAGO, IL 60601",
                previousRegistrations: [
                  {
                    state: "IL",
                    date: "2022-07-15",
                    plate: generateRandomPlate()
                  },
                  {
                    state: "IN",
                    date: "2020-05-22",
                    plate: generateRandomPlate()
                  }
                ],
                hasLiens: false,
                titleStatus: "CLEAN TITLE",
                lastOdometerReading: "45,879 miles",
                lastOdometerDate: "2023-09-18",
                source: "vincheck.info Vehicle Registration Services"
              }
            });
          }, 1500); // Simulate network delay
        });
      };

      const response = await mockRegistrationCall();

      if (response.success) {
        // Now we have both vehicle data and registration data, 
        // load it all to the details panel
        vehicleData.registrationInfo = response.registrationData;
        
        // Update plateNumber from registration if available
        if (response.registrationData.licensePlate) {
          vehicleData.plateNumber = response.registrationData.licensePlate;
        }
        
        // Show vehicle details in the details panel
        loadVehicleToDetailsPanel(vehicleData);
        
        // Show success message
        setSearchSuccess(true);
      } else {
        console.error('Error fetching registration info:', response.message);
        // Still load vehicle data even if reg info fails
        loadVehicleToDetailsPanel(vehicleData);
        setSearchSuccess(true);
      }
    } catch (error) {
      console.error('Registration info error:', error);
      // Still load vehicle data even if reg info fails
      loadVehicleToDetailsPanel(vehicleData);
      setSearchSuccess(true);
    } finally {
      setIsLoadingRegistration(false);
      setIsLoading(false);
    }
  };

  // Helper function to generate a random license plate for mock data
  const generateRandomPlate = () => {
    const letters = 'ABCDEFGHJKLMNPQRSTUVWXYZ';
    const numbers = '**********';
    let plate = '';
    
    // Generate 3 random letters
    for (let i = 0; i < 3; i++) {
      plate += letters.charAt(Math.floor(Math.random() * letters.length));
    }
    
    plate += ' ';
    
    // Generate 3 random numbers
    for (let i = 0; i < 3; i++) {
      plate += numbers.charAt(Math.floor(Math.random() * numbers.length));
    }
    
    return plate;
  };

  // Helper function to generate a random future date for mock data
  const getRandomFutureDate = () => {
    const today = new Date();
    const futureDate = new Date(today);
    // Add random number of months (1-24)
    futureDate.setMonth(today.getMonth() + Math.floor(Math.random() * 24) + 1);
    return futureDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  };

  // Format vehicle data for the details panel
  const loadVehicleToDetailsPanel = (vehicle) => {
    // Check if setDetailsPanelLocation and setDetailsVisible are functions
    if (typeof setDetailsPanelLocation !== 'function') {
      console.error('setDetailsPanelLocation is not a function');
      return;
    }
    
    if (typeof setDetailsVisible !== 'function') {
      console.error('setDetailsVisible is not a function');
      return;
    }

    const formattedVehicle = {
      id: vehicle.vin || vehicle.id || Date.now().toString(),
      type: 'vehicle',
      name: `${vehicle.year} ${vehicle.make} ${vehicle.model}`,
      position: vehicle.position || {
        lat: vehicle.location?.lat || 35.6895, // Default coordinates
        lng: vehicle.location?.lng || 139.6917,
      },
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      color: vehicle.color || 'Unknown',
      vin: vehicle.vin,
      plateNumber: vehicle.registrationInfo?.licensePlate || vehicle.licensePlate || vehicle.plateNumber || 'Unknown',
      status: 'pending',
      userSelected: true, // Mark as user-selected to bypass proximity check
      address: vehicle.registrationInfo?.registeredAddress || vehicle.address || 'Unknown location',
      addresses: vehicle.addresses || [],
      customerName: vehicle.registrationInfo?.registeredOwner || vehicle.ownerName || 'Unknown',
      driveType: vehicle.driveType || 'Unknown',
      createdAt: new Date(),
      // Fix for the undefined createdBy error
      createdBy: currentUser?.uid || 'unknown',
      source: vehicle.source || 'Manual Entry',
      titleStatus: vehicle.registrationInfo?.titleStatus || vehicle.titleStatus || 'Unknown',
      // Keep the registration info intact
      registrationInfo: vehicle.registrationInfo,
      // Additional fields from NHTSA if available
      vehicleType: vehicle.vehicleType,
      bodyClass: vehicle.bodyClass,
      engine: vehicle.engine,
      fuelType: vehicle.fuelType,
      transmission: vehicle.transmission
    };

    // Save the vehicle to Firestore if it's from a search and not already in the database
    if (!vehicle.fromDatabase) {
      saveVehicleToDatabase(formattedVehicle);
    }

    // Display in the details panel
    setDetailsPanelLocation(formattedVehicle);
    setDetailsVisible(true);
    
    // Log success
    console.log("Vehicle loaded to details panel:", formattedVehicle);
  };

  // Save new vehicle to database
  const saveVehicleToDatabase = async (vehicle) => {
    try {
      const vehiclesRef = collection(db, 'vehicles');
      
      // Make sure we don't save undefined values to Firestore
      const safeVehicle = { ...vehicle };
      
      // Remove any undefined values or convert them to null
      Object.keys(safeVehicle).forEach(key => {
        if (safeVehicle[key] === undefined) {
          safeVehicle[key] = null;
        }
      });
      
      await addDoc(vehiclesRef, {
        ...safeVehicle,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      
      console.log('Vehicle saved to database');
    } catch (error) {
      console.error('Error saving vehicle to database:', error);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    // Check if date is today
    if (date.toDateString() === today.toDateString()) {
      return `Today ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }
    
    // Check if date is yesterday
    if (date.toDateString() === yesterday.toDateString()) {
      return `Yesterday ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }
    
    // Otherwise return full date
    return date.toLocaleString([], { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className="flex-none bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 text-white border-b border-gray-700 p-3 shadow-lg">
      <div className="flex flex-wrap gap-2 items-center mb-2">
        <button 
          onClick={handleFoundACar}
          className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white px-4 py-2 rounded-lg font-semibold flex-none shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200"
        >
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
              <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-5h6a1 1 0 001-1v-2a1 1 0 00-.55-.9L14.2 4.5A4 4 0 0010.4 2H4a1 1 0 00-1 1v1zm1 7a1 1 0 011-1h11a1 1 0 110 2H5a1 1 0 01-1-1z" />
            </svg>
            Found A Car
          </div>
        </button>
        
        <div className="flex-grow">
          <div className="flex flex-wrap md:flex-nowrap gap-2">
            {/* Search Type Selector */}
            <select
              value={searchType}
              onChange={(e) => setSearchType(e.target.value)}
              className="bg-gray-700 text-white px-3 py-2 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="vin">VIN Search</option>
              <option value="licensePlate">License Plate</option>
              <option value="titleNumber">Title Number</option>
            </select>
            
            {/* Search Input */}
            <div className="flex-grow flex items-center gap-2 bg-gray-700 rounded-lg p-1 shadow-inner border border-gray-600">
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                placeholder={searchType === 'vin' ? "Enter VIN (17 characters)" : 
                             searchType === 'licensePlate' ? "Enter license plate" : "Enter title number"}
                className="bg-gray-700 text-white flex-grow border-none focus:outline-none focus:ring-2 focus:ring-blue-500 px-3 py-1 rounded"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch();
                  }
                }}
              />
              <button
                onClick={() => handleSearch()}
                disabled={isLoading || isLoadingRegistration}
                className={`bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-1 rounded shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 ${(isLoading || isLoadingRegistration) ? 'opacity-50' : ''}`}
              >
                {isLoading || isLoadingRegistration ? (
                  <div className="flex items-center">
                    <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {isLoadingRegistration ? 'Getting Registration...' : 'Searching...'}
                  </div>
                ) : (
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                    </svg>
                    Search
                  </div>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Recent Searches Section */}
      {recentSearches.length > 0 && (
        <div className="mt-2 mb-3">
          <div className="flex items-center justify-between mb-1">
            <h3 className="text-sm font-medium text-gray-400">Recent Searches</h3>
            <button 
              onClick={clearRecentSearches} 
              className="text-xs text-gray-400 hover:text-red-400 transition-colors"
            >
              Clear All
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {recentSearches.map((search, index) => (
              <button
                key={index}
                onClick={() => applyRecentSearch(search)}
                className="bg-gray-700 hover:bg-gray-600 text-white text-sm px-3 py-1 rounded-full flex items-center transition-all"
              >
                <span className={`mr-1 ${
                  search.type === 'vin' ? 'text-blue-400' : 
                  search.type === 'licensePlate' ? 'text-green-400' : 'text-purple-400'
                }`}>
                  {search.type === 'vin' ? '#' : 
                   search.type === 'licensePlate' ? '🚗' : '📄'}
                </span>
                <span className="truncate max-w-[150px]">{search.query}</span>
                <span className="ml-2 text-xs text-gray-400">{formatDate(search.timestamp)}</span>
              </button>
            ))}
          </div>
        </div>
      )}
      
      {/* Error Message */}
      {searchError && (
        <div className="bg-red-900 text-white p-3 rounded-lg mt-2 animate-fade-in">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {searchError}
            </div>
            <button 
              onClick={() => setSearchError(null)}
              className="text-red-300 hover:text-white"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}
      
      {/* Success Message - Automatically disappears after 3 seconds */}
      {searchSuccess && (
        <div className="bg-green-900 text-white p-3 rounded-lg mt-2 animate-fade-in">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Vehicle found! Details are now displayed in the details panel.
            </div>
            <button 
              onClick={dismissSuccessMessage}
              className="text-green-300 hover:text-white"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}
      
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
          animation: fadeIn 0.3s ease-out forwards;
        }
        
        @keyframes fadeOut {
          from { opacity: 1; transform: translateY(0); }
          to { opacity: 0; transform: translateY(-10px); }
        }
        .animate-fade-out {
          animation: fadeOut 0.3s ease-out forwards;
        }
      `}} />
    </div>
  );
};

export default VehicleFinder;