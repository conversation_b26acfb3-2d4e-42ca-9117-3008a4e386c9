/* Team member marker styles */
.team-member-marker {
    background: transparent !important;
    z-index: 800 !important;
}

.team-member-marker-inner {
    width: 32px;
    height: 32px;
    background: #4B5563;
    border: 2px solid white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: transform 0.2s ease;
}

.team-member-marker-inner:hover {
    transform: scale(1.1);
}

.team-member-marker-inner.current-user {
    background: #3B82F6;
    border-color: #FBBF24;
    box-shadow: 0 0 0 2px #FBBF24;
}

.user-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
}

.user-initials {
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.user-label {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    white-space: nowrap;
    text-align: center;
    z-index: 1000;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-popup {
    padding: 5px;
    text-align: center;
}

.user-popup h3 {
    margin: 0 0 8px 0;
    font-size: 14px;
}

.clear-trail-btn {
    background: #EF4444;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.clear-trail-btn:hover {
    background: #DC2626;
}

/* Trail styles */
.user-trail {
    /* Trail styles are set dynamically by color, but we can add pulse animation */
    animation: trailPulse 3s infinite;
}

@keyframes trailPulse {
    0% {
        opacity: 0.6;
    }

    50% {
        opacity: 0.9;
    }

    100% {
        opacity: 0.6;
    }
}