import React from 'react';

function UserList({ 
  users, 
  selectedTeam, 
  selectedUser, 
  loading, 
  isMobileMenuOpen, 
  handleUserSelect 
}) {
  return (
    <div 
      className={`${isMobileMenuOpen || !selectedUser ? 'block' : 'hidden md:block'} 
        w-full md:w-1/4 bg-gray-800 rounded shadow-sm p-3 border border-gray-700 mb-3 md:mb-0`}
      aria-label="Team members list"
    >
      <h2 className="text-sm font-semibold text-blue-300 mb-2">
        {selectedTeam ? `Team: ${selectedTeam.name}` : 'Team Members'}
      </h2>
      
      {loading ? (
        <div className="flex justify-center items-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500" aria-hidden="true"></div>
          <span className="sr-only">Loading...</span>
        </div>
      ) : (
        <div className="space-y-1 max-h-80 overflow-y-auto pr-1">
          {users.length === 0 ? (
            <p className="text-gray-400 text-center py-4 text-sm">
              {selectedTeam ? 'No members in this team' : 'Please select a team'}
            </p>
          ) : (
            users.map(user => (
              <div 
                key={user.id}
                onClick={() => handleUserSelect(user)}
                className={`p-2 rounded cursor-pointer ${
                  selectedUser?.id === user.id 
                    ? 'bg-blue-800 border border-blue-600' 
                    : 'bg-gray-750 hover:bg-gray-700 border border-gray-700'
                }`}
                role="button"
                tabIndex="0"
                aria-selected={selectedUser?.id === user.id}
              >
                <div className="flex items-center">
                  <div className="text-xl mr-2" aria-hidden="true">{user.avatar}</div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-white text-sm truncate">{user.displayName}</h3>
                    <div className="flex flex-col text-xs">
                      <span className="text-gray-400 truncate">{user.email}</span>
                      <span className="text-blue-300">{user.jobTitle || user.role}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
}

export default UserList;