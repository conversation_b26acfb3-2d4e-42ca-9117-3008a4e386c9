import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext.js';

function SuggestionsUpdates() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  // Form states
  const [suggestionTitle, setSuggestionTitle] = useState('');
  const [suggestionDescription, setSuggestionDescription] = useState('');
  const [submissionStatus, setSubmissionStatus] = useState(null);
  
  // Updates states (for admin)
  const [updateTitle, setUpdateTitle] = useState('');
  const [updateDescription, setUpdateDescription] = useState('');
  const [updateDate, setUpdateDate] = useState('');
  const [editingUpdateId, setEditingUpdateId] = useState(null);
  
  // To-Do List states (for admin)
  const [todos, setTodos] = useState([]);
  const [todoTitle, setTodoTitle] = useState('');
  const [todoDescription, setTodoDescription] = useState('');
  const [todoStatus, setTodoStatus] = useState('pending');
  const [editingTodoId, setEditingTodoId] = useState(null);
  
  // Loading state
  const [isLoading, setIsLoading] = useState(true);
  
  // Initialize state with empty arrays
  const [suggestions, setSuggestions] = useState([]);
  const [appUpdates, setAppUpdates] = useState([]);
  
  // Load data from localStorage when component mounts
  useEffect(() => {
    try {
      // Get data from localStorage
      const storedSuggestions = localStorage.getItem('nwrepo_suggestions');
      const storedAppUpdates = localStorage.getItem('nwrepo_updates');
      const storedTodos = localStorage.getItem('nwrepo_todos');
      
      // Parse stored data if it exists
      if (storedSuggestions) {
        setSuggestions(JSON.parse(storedSuggestions));
      }
      
      if (storedAppUpdates) {
        setAppUpdates(JSON.parse(storedAppUpdates));
      }
      
      if (storedTodos) {
        setTodos(JSON.parse(storedTodos));
      }
    } catch (error) {
      console.error("Error loading data from localStorage:", error);
      // Clear potentially corrupted data
      localStorage.removeItem('nwrepo_suggestions');
      localStorage.removeItem('nwrepo_updates');
      localStorage.removeItem('nwrepo_todos');
    } finally {
      setIsLoading(false);
    }
  }, []);

  async function handleLogout() {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error("Failed to log out", error);
    }
  }

  // Reset all data (admin only)
  const handleResetData = () => {
    if (window.confirm('Are you sure you want to reset all data? This cannot be undone.')) {
      localStorage.removeItem('nwrepo_suggestions');
      localStorage.removeItem('nwrepo_updates');
      localStorage.removeItem('nwrepo_todos');
      setSuggestions([]);
      setAppUpdates([]);
      setTodos([]);
    }
  };

  // Handle suggestion submission
  const handleSuggestionSubmit = (e) => {
    e.preventDefault();
    
    // Create new suggestion
    const newSuggestion = {
      id: Date.now(), // Use timestamp as unique ID
      title: suggestionTitle,
      description: suggestionDescription,
      user: currentUser.email,
      date: new Date().toISOString().split('T')[0],
      status: 'pending'
    };
    
    // Update state and localStorage
    const updatedSuggestions = [newSuggestion, ...suggestions];
    setSuggestions(updatedSuggestions);
    localStorage.setItem('nwrepo_suggestions', JSON.stringify(updatedSuggestions));
    
    setSubmissionStatus('success');
    
    // Reset form
    setSuggestionTitle('');
    setSuggestionDescription('');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSubmissionStatus(null);
    }, 3000);
  };
  
  // Generate a new version number for updates
  const generateNewVersionNumber = () => {
    if (appUpdates.length === 0) {
      return '1.0.0';
    }
    // Parse the most recent version number
    const latestVersion = appUpdates[0].version || '1.0.0';
    const parts = latestVersion.split('.');
    // Increment the minor version
    parts[1] = parseInt(parts[1]) + 1;
    return `${parts[0]}.${parts[1]}.0`;
  };
  
  // Handle update submission (admin only)
  const handleUpdateSubmit = (e) => {
    e.preventDefault();
    
    let updatedAppUpdates;
    
    if (editingUpdateId) {
      // Update existing
      updatedAppUpdates = appUpdates.map(update => 
        update.id === editingUpdateId ? 
        { ...update, title: updateTitle, description: updateDescription, date: updateDate } : 
        update
      );
    } else {
      // Add new
      const newUpdate = {
        id: Date.now(), // Use timestamp as unique ID
        title: updateTitle,
        description: updateDescription,
        date: updateDate,
        version: generateNewVersionNumber()
      };
      updatedAppUpdates = [newUpdate, ...appUpdates];
    }
    
    // Update state and localStorage
    setAppUpdates(updatedAppUpdates);
    localStorage.setItem('nwrepo_updates', JSON.stringify(updatedAppUpdates));
    
    // Reset form
    setUpdateTitle('');
    setUpdateDescription('');
    setUpdateDate('');
    setEditingUpdateId(null);
  };
  
  // Handle todo submission (admin only)
  const handleTodoSubmit = (e) => {
    e.preventDefault();
    
    let updatedTodos;
    
    if (editingTodoId) {
      // Update existing
      updatedTodos = todos.map(todo => 
        todo.id === editingTodoId ? 
        { 
          ...todo, 
          title: todoTitle, 
          description: todoDescription,
          status: todoStatus
        } : 
        todo
      );
    } else {
      // Add new
      const newTodo = {
        id: Date.now(), // Use timestamp as unique ID
        title: todoTitle,
        description: todoDescription,
        date: new Date().toISOString().split('T')[0],
        status: 'pending',
        completed: false
      };
      updatedTodos = [newTodo, ...todos];
    }
    
    // Update state and localStorage
    setTodos(updatedTodos);
    localStorage.setItem('nwrepo_todos', JSON.stringify(updatedTodos));
    
    // Reset form
    setTodoTitle('');
    setTodoDescription('');
    setTodoStatus('pending');
    setEditingTodoId(null);
  };
  
  // Toggle todo completion
  const handleToggleTodoCompletion = (id) => {
    const todoToToggle = todos.find(todo => todo.id === id);
    const isBeingCompleted = !todoToToggle.completed;
    
    const updatedTodos = todos.map(todo => 
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    );
    
    // Update todos state and localStorage
    setTodos(updatedTodos);
    localStorage.setItem('nwrepo_todos', JSON.stringify(updatedTodos));
    
    // If the todo is being marked as completed, add it to updates
    if (isBeingCompleted) {
      const today = new Date().toISOString().split('T')[0];
      const newUpdate = {
        id: Date.now(),
        title: `Completed: ${todoToToggle.title}`,
        description: todoToToggle.description 
          ? `Task completed: ${todoToToggle.description}` 
          : `Successfully completed task: ${todoToToggle.title}`,
        date: today,
        version: generateNewVersionNumber(),
        fromTodo: true
      };
      
      const updatedAppUpdates = [newUpdate, ...appUpdates];
      setAppUpdates(updatedAppUpdates);
      localStorage.setItem('nwrepo_updates', JSON.stringify(updatedAppUpdates));
    }
  };
  
  // Start editing a todo
  const handleEditTodo = (todo) => {
    setTodoTitle(todo.title);
    setTodoDescription(todo.description);
    setTodoStatus(todo.status || 'pending');
    setEditingTodoId(todo.id);
    
    // Scroll to form
    document.getElementById('todo-form').scrollIntoView({ behavior: 'smooth' });
  };
  
  // Delete a todo
  const handleDeleteTodo = (id) => {
    if (window.confirm('Are you sure you want to delete this to-do item?')) {
      const updatedTodos = todos.filter(todo => todo.id !== id);
      
      // Update state and localStorage
      setTodos(updatedTodos);
      localStorage.setItem('nwrepo_todos', JSON.stringify(updatedTodos));
    }
  };
  
  // Start editing an update
  const handleEditUpdate = (update) => {
    setUpdateTitle(update.title);
    setUpdateDescription(update.description);
    setUpdateDate(update.date);
    setEditingUpdateId(update.id);
    
    // Scroll to form
    document.getElementById('update-form').scrollIntoView({ behavior: 'smooth' });
  };
  
  // Delete an update
  const handleDeleteUpdate = (id) => {
    if (window.confirm('Are you sure you want to delete this update?')) {
      const updatedAppUpdates = appUpdates.filter(update => update.id !== id);
      
      // Update state and localStorage
      setAppUpdates(updatedAppUpdates);
      localStorage.setItem('nwrepo_updates', JSON.stringify(updatedAppUpdates));
    }
  };
  
  // Delete a suggestion (admin only)
  const handleDeleteSuggestion = (id) => {
    if (window.confirm('Are you sure you want to delete this suggestion?')) {
      const updatedSuggestions = suggestions.filter(suggestion => suggestion.id !== id);
      
      // Update state and localStorage
      setSuggestions(updatedSuggestions);
      localStorage.setItem('nwrepo_suggestions', JSON.stringify(updatedSuggestions));
    }
  };
  
  // Update suggestion status
  const handleStatusChange = (suggestionId, newStatus) => {
    const updatedSuggestions = suggestions.map(suggestion => 
      suggestion.id === suggestionId ? 
      { ...suggestion, status: newStatus } : 
      suggestion
    );
    
    // Update state and localStorage
    setSuggestions(updatedSuggestions);
    localStorage.setItem('nwrepo_suggestions', JSON.stringify(updatedSuggestions));
  };
  
  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch(status) {
      case 'pending':
        return 'bg-yellow-500';
      case 'reviewed':
        return 'bg-blue-500';
      case 'implemented':
        return 'bg-green-500';
      case 'rejected':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Show loading indicator
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-xl mb-2">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 z-50 bg-black bg-opacity-70 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <div 
            className="fixed right-0 top-0 bottom-0 w-64 bg-gray-800 shadow-lg border-l border-gray-700"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 border-b border-gray-700">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-white">Menu</h2>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="text-gray-400 hover:text-gray-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="p-4 flex flex-col space-y-4">
              <button
                onClick={() => {
                  navigate('/profile');
                  setIsMobileMenuOpen(false);
                }}
                className="flex items-center text-left px-3 py-2 rounded-md hover:bg-gray-700 text-gray-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Profile
              </button>
              <button
                onClick={() => {
                  navigate('/dashboard');
                  setIsMobileMenuOpen(false);
                }}
                className="flex items-center text-left px-3 py-2 rounded-md hover:bg-gray-700 text-gray-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Dashboard
              </button>
              <button
                onClick={() => {
                  handleLogout();
                  setIsMobileMenuOpen(false);
                }}
                className="flex items-center text-left px-3 py-2 rounded-md hover:bg-gray-700 text-gray-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Sign Out
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Top Navigation Bar */}
      <nav className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 shadow-md border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">NWRepo</h1>
              </div>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-4">
              <button
                onClick={() => navigate('/dashboard')}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md shadow-md"
              >
                Dashboard
              </button>
              <button
                onClick={() => navigate('/profile')}
                className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white px-4 py-2 rounded-md shadow-md"
              >
                Profile
              </button>
              <button
                onClick={handleLogout}
                className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-md shadow-md"
              >
                Sign Out
              </button>
            </div>
            
            {/* Mobile Menu Button */}
            <div className="flex md:hidden items-center">
              <button
                onClick={() => setIsMobileMenuOpen(true)}
                className="text-gray-300 hover:text-white focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="py-4 sm:py-6">
        <header className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto flex justify-between items-center">
          <h1 className="text-2xl sm:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Suggestions & Updates</h1>
          
          {isAdmin && (
            <button
              onClick={handleResetData}
              className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 text-sm rounded-md shadow-md"
            >
              Reset All Data
            </button>
          )}
        </header>
        
        <main>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4 sm:mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              
              {/* Left Column */}
              <div>
                {!isAdmin && (
                  <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg shadow-md p-4 sm:p-6 mb-6 border border-gray-700">
                    <h2 className="text-xl font-semibold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Submit a Suggestion</h2>
                    
                    {submissionStatus === 'success' && (
                      <div className="bg-green-900 border border-green-800 text-green-100 px-4 py-3 rounded mb-4">
                        <p>Your suggestion has been submitted successfully!</p>
                      </div>
                    )}
                    
                    <form onSubmit={handleSuggestionSubmit}>
                      <div className="mb-4">
                        <label htmlFor="suggestionTitle" className="block text-sm font-medium text-gray-300 mb-1">Title</label>
                        <input
                          type="text"
                          id="suggestionTitle"
                          value={suggestionTitle}
                          onChange={(e) => setSuggestionTitle(e.target.value)}
                          className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Brief title for your suggestion"
                          required
                        />
                      </div>
                      
                      <div className="mb-4">
                        <label htmlFor="suggestionDescription" className="block text-sm font-medium text-gray-300 mb-1">Description</label>
                        <textarea
                          id="suggestionDescription"
                          value={suggestionDescription}
                          onChange={(e) => setSuggestionDescription(e.target.value)}
                          rows="4"
                          className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Describe your suggestion in detail"
                          required
                        ></textarea>
                      </div>
                      
                      <div>
                        <button
                          type="submit"
                          className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md shadow-md w-full md:w-auto"
                        >
                          Submit Suggestion
                        </button>
                      </div>
                    </form>
                  </div>
                )}
                
                {/* App Updates Section */}
                <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg shadow-md p-4 sm:p-6 border border-gray-700">
                  <h2 className="text-xl font-semibold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">App Updates</h2>
                  
                  {appUpdates.length === 0 ? (
                    <p className="text-gray-400">No updates available yet.</p>
                  ) : (
                    <div className="space-y-4">
                      {appUpdates.map((update) => (
                        <div 
                          key={update.id} 
                          className={`bg-gray-800 rounded-md p-4 border ${update.fromTodo ? 'border-green-700' : 'border-gray-700'}`}
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="text-lg font-medium text-white">{update.title}</h3>
                              <p className="text-sm text-gray-400">Version {update.version} - {update.date}</p>
                            </div>
                            
                            {isAdmin && (
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => handleEditUpdate(update)}
                                  className="text-blue-400 hover:text-blue-300"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                  </svg>
                                </button>
                                <button
                                  onClick={() => handleDeleteUpdate(update.id)}
                                  className="text-red-400 hover:text-red-300"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                </button>
                              </div>
                            )}
                          </div>
                          <p className="mt-2 text-white">{update.description}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* Admin To-Do List */}
                {isAdmin && (
                  <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg shadow-md p-4 sm:p-6 mt-6 border border-gray-700">
                    <h2 className="text-xl font-semibold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Admin To-Do List</h2>
                    
                    <form id="todo-form" onSubmit={handleTodoSubmit} className="mb-6">
                      <div className="mb-4">
                        <label htmlFor="todoTitle" className="block text-sm font-medium text-gray-300 mb-1">Title</label>
                        <input
                          type="text"
                          id="todoTitle"
                          value={todoTitle}
                          onChange={(e) => setTodoTitle(e.target.value)}
                          className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="To-do task title"
                          required
                        />
                      </div>
                      
                      <div className="mb-4">
                        <label htmlFor="todoDescription" className="block text-sm font-medium text-gray-300 mb-1">Description (optional)</label>
                        <textarea
                          id="todoDescription"
                          value={todoDescription}
                          onChange={(e) => setTodoDescription(e.target.value)}
                          rows="2"
                          className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Additional details"
                        ></textarea>
                      </div>
                      
                      {editingTodoId && (
                        <div className="mb-4">
                          <label className="block text-sm font-medium text-gray-300 mb-1">Status</label>
                          <select
                            value={todoStatus}
                            onChange={(e) => setTodoStatus(e.target.value)}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="pending">Pending</option>
                            <option value="in-progress">In Progress</option>
                            <option value="completed">Completed</option>
                          </select>
                        </div>
                      )}
                      
                      <div className="flex space-x-3">
                        <button
                          type="submit"
                          className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white px-4 py-2 rounded-md shadow-md"
                        >
                          {editingTodoId ? 'Update Task' : 'Add Task'}
                        </button>
                        
                        {editingTodoId && (
                          <button
                            type="button"
                            onClick={() => {
                              setTodoTitle('');
                              setTodoDescription('');
                              setTodoStatus('pending');
                              setEditingTodoId(null);
                            }}
                            className="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white px-4 py-2 rounded-md shadow-md"
                          >
                            Cancel
                          </button>
                        )}
                      </div>
                    </form>
                    
                    {todos.length === 0 ? (
                      <p className="text-gray-400">No tasks added yet.</p>
                    ) : (
                      <div className="space-y-2">
                        <h3 className="font-medium text-gray-300 mb-2">To Do</h3>
                        {todos.filter(todo => !todo.completed).length === 0 ? (
                          <p className="text-gray-400">No pending tasks. Great job!</p>
                        ) : (
                          <div className="space-y-2">
                            {todos.filter(todo => !todo.completed).map((todo) => (
                              <div 
                                key={todo.id} 
                                className="bg-gray-800 rounded-md p-3 border border-gray-700"
                              >
                                <div className="flex items-start space-x-3">
                                  <div className="flex-shrink-0 mt-0.5">
                                    <input
                                      type="checkbox"
                                      checked={todo.completed}
                                      onChange={() => handleToggleTodoCompletion(todo.id)}
                                      className="h-4 w-4 rounded border-gray-600 text-blue-600 focus:ring-blue-500"
                                    />
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <h4 className="text-sm font-medium text-white">{todo.title}</h4>
                                    {todo.description && (
                                      <p className="text-xs text-gray-400 mt-1">{todo.description}</p>
                                    )}
                                    <div className="flex space-x-2 mt-2">
                                      <button
                                        onClick={() => handleEditTodo(todo)}
                                        className="text-xs text-blue-400 hover:text-blue-300"
                                      >
                                        Edit
                                      </button>
                                      <button
                                        onClick={() => handleDeleteTodo(todo.id)}
                                        className="text-xs text-red-400 hover:text-red-300"
                                      >
                                        Delete
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
              
              {/* Right Column */}
              <div>
                {isAdmin && (
                  <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg shadow-md p-4 sm:p-6 mb-6 border border-gray-700">
                    <h2 className="text-xl font-semibold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">
                      {editingUpdateId ? 'Edit Update' : 'Add New Update'}
                    </h2>
                    
                    <form id="update-form" onSubmit={handleUpdateSubmit}>
                      <div className="mb-4">
                        <label htmlFor="updateTitle" className="block text-sm font-medium text-gray-300 mb-1">Title</label>
                        <input
                          type="text"
                          id="updateTitle"
                          value={updateTitle}
                          onChange={(e) => setUpdateTitle(e.target.value)}
                          className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Update title"
                          required
                        />
                      </div>
                      
                      <div className="mb-4">
                        <label htmlFor="updateDescription" className="block text-sm font-medium text-gray-300 mb-1">Description</label>
                        <textarea
                          id="updateDescription"
                          value={updateDescription}
                          onChange={(e) => setUpdateDescription(e.target.value)}
                          rows="4"
                          className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Describe the update in detail"
                          required
                        ></textarea>
                      </div>
                      
                      <div className="mb-4">
                        <label htmlFor="updateDate" className="block text-sm font-medium text-gray-300 mb-1">Date</label>
                        <input
                          type="date"
                          id="updateDate"
                          value={updateDate}
                          onChange={(e) => setUpdateDate(e.target.value)}
                          className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        />
                      </div>
                      
                      <div className="flex space-x-3">
                        <button
                          type="submit"
                          className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white px-4 py-2 rounded-md shadow-md"
                        >
                          {editingUpdateId ? 'Update' : 'Add'}
                        </button>
                        
                        {editingUpdateId && (
                          <button
                            type="button"
                            onClick={() => {
                              setUpdateTitle('');
                              setUpdateDescription('');
                              setUpdateDate('');
                              setEditingUpdateId(null);
                            }}
                            className="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white px-4 py-2 rounded-md shadow-md"
                          >
                            Cancel
                          </button>
                        )}
                      </div>
                    </form>
                  </div>
                )}
                
                {isAdmin && (
                  <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg shadow-md p-4 sm:p-6 border border-gray-700">
                    <h2 className="text-xl font-semibold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">User Suggestions</h2>
                    
                    {suggestions.length === 0 ? (
                      <p className="text-gray-400">No suggestions submitted yet.</p>
                    ) : (
                      <div className="space-y-4">
                        {suggestions.map((suggestion) => (
                          <div key={suggestion.id} className="bg-gray-800 rounded-md p-4 border border-gray-700">
                            <div className="flex justify-between">
                              <h3 className="text-lg font-medium text-white">{suggestion.title}</h3>
                              <span className={`${getStatusBadgeColor(suggestion.status)} text-xs font-bold px-2 py-1 rounded-full uppercase`}>
                                {suggestion.status}
                              </span>
                            </div>
                            <p className="text-sm text-gray-400 mt-1">From: {suggestion.user} on {suggestion.date}</p>
                            <p className="mt-2 text-white">{suggestion.description}</p>
                            
                            {/* Admin actions */}
                            <div className="mt-3 flex space-x-2">
                              <select 
                                value={suggestion.status}
                                onChange={(e) => handleStatusChange(suggestion.id, e.target.value)}
                                className="bg-gray-700 border border-gray-600 rounded text-white text-sm py-1 px-2"
                              >
                                <option value="pending">Pending</option>
                                <option value="reviewed">Reviewed</option>
                                <option value="implemented">Implemented</option>
                                <option value="rejected">Rejected</option>
                              </select>
                              
                              <button
                                onClick={() => handleDeleteSuggestion(suggestion.id)}
                                className="bg-red-600 hover:bg-red-700 text-white text-sm py-1 px-2 rounded"
                              >
                                Delete
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default SuggestionsUpdates;