import React, { createContext, useRef, useState, useEffect } from 'react';

// Create the context
export const MapContext = createContext();

// Create a provider component
export const MapProvider = ({ children }) => {
  // Map reference
  const mapRef = useRef(null);
  
  // Location states
  const [currentLocation, setCurrentLocation] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [detailsPanelLocation, setDetailsPanelLocation] = useState(null);
  const [locations, setLocations] = useState([]);
  
  // Navigation states
  const [isNavigating, setIsNavigating] = useState(false);
  const [optimizedRoute, setOptimizedRoute] = useState(null);
  const [isFollowingOptimizedRoute, setIsFollowingOptimizedRoute] = useState(false);
  
  // User states (required by UserInfoBar)
  const [currentUser, setCurrentUser] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isTowTruckUser, setIsTowTruckUser] = useState(false);
  const [userDisplayNames, setUserDisplayNames] = useState({});
  const [allUsers, setAllUsers] = useState([]);
  
  // Effect to get current location when component mounts
  useEffect(() => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        setCurrentLocation({
          lat: position.coords.latitude,
          lng: position.coords.longitude
        });
      },
      (error) => {
        console.error("Error getting current location:", error);
        // Default location (you can set this to a sensible default for your application)
        setCurrentLocation({ lat: 37.7749, lng: -122.4194 }); // San Francisco as default
      }
    );
    
    // Setup location watching
    const watchId = navigator.geolocation.watchPosition(
      (position) => {
        setCurrentLocation({
          lat: position.coords.latitude,
          lng: position.coords.longitude
        });
      },
      (error) => {
        console.error("Error watching location:", error);
      }
    );
    
    // Cleanup function to stop watching location
    return () => {
      navigator.geolocation.clearWatch(watchId);
    };
  }, []);
  
  // Context value
  const contextValue = {
    // Map related
    mapRef,
    currentLocation,
    setCurrentLocation,
    selectedLocation,
    setSelectedLocation,
    detailsPanelLocation,
    setDetailsPanelLocation,
    locations,
    setLocations,
    
    // Navigation related
    isNavigating,
    setIsNavigating,
    optimizedRoute,
    setOptimizedRoute,
    isFollowingOptimizedRoute,
    setIsFollowingOptimizedRoute,
    
    // User related
    currentUser,
    setCurrentUser,
    isAdmin,
    setIsAdmin,
    isTowTruckUser,
    setIsTowTruckUser,
    userDisplayNames,
    setUserDisplayNames,
    allUsers,
    setAllUsers
  };
  
  return (
    <MapContext.Provider value={contextValue}>
      {children}
    </MapContext.Provider>
  );
};