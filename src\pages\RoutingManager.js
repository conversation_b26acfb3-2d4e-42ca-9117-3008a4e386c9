import L from 'leaflet';
import 'leaflet-routing-machine';
import 'leaflet-routing-machine/dist/leaflet-routing-machine.css';

// Constants for routing with all UI controls disabled
export const DEFAULT_ROUTING_OPTIONS = {
  // Explicitly don't specify position to avoid the topright error
  // show: false and other options already disable UI controls
  show: false, // Never show the control
  collapsible: true,
  showAlternatives: false,
  routeWhileDragging: false,
  fitSelectedRoutes: false,
  draggableWaypoints: false,
  autoRoute: true,
  addWaypoints: false, // Prevent users from adding waypoints
  useZoomParameter: false, // More stable path calculation
  // Configure how the route lines look
  lineOptions: {
    styles: [
      { color: '#4a89f3', opacity: 0.8, weight: 5 },
      { color: '#2a69d3', opacity: 0.9, weight: 3, className: 'routing-line-inner' }
    ],
    extendToWaypoints: true,
    missingRouteTolerance: 10,
    className: 'custom-route-path' // Add class for easier selection/debugging
  },
  // Use OSRM as the default routing service
  router: L.Routing.osrmv1({
    serviceUrl: 'https://router.project-osrm.org/route/v1',
    profile: 'driving',
    suppressDemoServerWarning: true,
    timeout: 30 * 1000, // 30 second timeout
    geometryOnly: false
  }),
  createMarker: function () { return null; } // Don't create markers, we'll handle that
};

/**
 * IMPROVED: Create a routing control for a map with better error prevention
 * @param {L.Map} map - The Leaflet map instance
 * @param {Object} options - Options to override the defaults
 * @returns {L.Routing.Control} The routing control instance
 */
export const createRoutingControl = (map, options = {}) => {
  if (!map) return null;

  try {
    // Check if there's an existing control to clean up first
    let existingControl = null;
    map.eachLayer((layer) => {
      if (layer && layer._plan && layer._routes) {
        existingControl = layer;
      }
    });

    if (existingControl) {
      // Safely remove the existing control first
      safelyDisposeRoutingControl(existingControl);
    }

    // Create the control with better error handling
    const routingOptions = {
      ...DEFAULT_ROUTING_OPTIONS,
      ...options
    };

    // IMPORTANT: Don't specify position to avoid topright error
    delete routingOptions.position;

    // Create the control but don't add it to map yet
    const routingControl = L.Routing.control(routingOptions);

    // Add the improved error handler
    routingControl.on('routingerror', (e) => {
      console.warn('Routing error encountered:', e.error);

      // Check for 429 error
      if (e.error && e.error.status === 429) {
        console.warn('Rate limit exceeded (429), implementing exponential backoff');
      }

      // Attempt recovery by clearing routes
      if (routingControl._routes) {
        try {
          safeClearRouteLines(routingControl);
        } catch (clearError) {
          console.warn('Error clearing routes after routing error:', clearError);
        }
      }
    });

    // Safely add to map after creation with a delay
    setTimeout(() => {
      try {
        routingControl.addTo(map);
      } catch (error) {
        console.warn("Error adding routing control to map:", error);
      }
    }, 500);

    return routingControl;
  } catch (error) {
    console.error("Error creating routing control:", error);
    // Return a mock routing control that does nothing to prevent errors
    return {
      setWaypoints: () => { },
      on: () => { },
      off: () => { },
      once: () => { },
      removeFrom: () => { },
      remove: () => { },
      _routes: [],
      _clearLines: () => { }
    };
  }
};

/**
 * IMPROVED: Safe utility function to safely clear route lines with enhanced error prevention
 * @param {L.Routing.Control} routingControl - The routing control to clear routes from
 * @returns {Promise<boolean>} - True if routes were cleared successfully, false otherwise
 */
export const safeClearRouteLines = (routingControl) => {
  if (!routingControl) return false;

  try {
    // Check if the routing control is valid and has routes
    if (!routingControl._plan ||
      !routingControl._routes ||
      !Array.isArray(routingControl._routes) ||
      routingControl._routes.length === 0) {
      return false;
    }

    // Check if _clearLines method exists
    if (typeof routingControl._clearLines !== 'function') {
      return false;
    }

    // Only attempt to clear routes with valid paths
    const hasValidPaths = routingControl._routes.some(route => {
      try {
        return route &&
          route.line &&
          route.line._path &&
          route.line._path.parentNode;
      } catch (e) {
        return false;
      }
    });

    if (hasValidPaths) {
      // Use a try/catch inside a promise with a timeout to prevent hanging
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          console.warn("Route clearing timed out");
          resolve(false);
        }, 300);

        try {
          // First try to remove routes one by one
          routingControl._routes.forEach(route => {
            try {
              if (route && route.line) {
                // Check if the line is a LayerGroup
                if (typeof route.line.eachLayer === 'function') {
                  // Safe remove each layer in the group
                  const layers = [];
                  route.line.eachLayer(layer => layers.push(layer));

                  layers.forEach(layer => {
                    try {
                      route.line.removeLayer(layer);
                    } catch (e) {
                      console.warn("Error removing layer from line:", e);
                    }
                  });
                }

                // Then try to remove from control
                if (routingControl._container && routingControl._map) {
                  try {
                    if (route.line._leaflet_id && routingControl._map._layers &&
                      routingControl._map._layers[route.line._leaflet_id]) {
                      routingControl._map.removeLayer(route.line);
                    }
                  } catch (e) {
                    console.warn("Error removing line from map:", e);
                  }
                }
              }
            } catch (e) {
              console.warn("Error in route cleanup:", e);
            }
          });

          // Now actually clear the routes array
          routingControl._routes = [];

          clearTimeout(timeout);
          resolve(true);
        } catch (error) {
          console.warn("Error in route clearing:", error);
          clearTimeout(timeout);
          resolve(false);
        }
      });
    } else {
      // No valid paths to clear
      routingControl._routes = [];
      return true;
    }
  } catch (error) {
    console.warn("Error in safeClearRouteLines:", error);
    return false;
  }
};

/**
 * New function to ensure routing controls are properly disposed before creating new ones
 * @param {L.Routing.Control} routingControl - The routing control to dispose
 * @returns {Promise<void>}
 */
export const safelyDisposeRoutingControl = (routingControl) => {
  if (!routingControl) return Promise.resolve();

  return new Promise((resolve) => {
    setTimeout(async () => {
      try {
        // First clear the lines
        await safeClearRouteLines(routingControl);

        // Then try to remove the control from the map
        try {
          if (routingControl._map) {
            routingControl.removeFrom(routingControl._map);
          }
        } catch (e) {
          console.warn("Error removing routing control from map:", e);
        }

        // Clear internal references
        try {
          routingControl._map = null;
          routingControl._container = null;
          routingControl._routes = [];
        } catch (e) {
          console.warn("Error clearing routing control references:", e);
        }

        resolve();
      } catch (e) {
        console.warn("Error disposing routing control:", e);
        resolve();
      }
    }, 100);
  });
};

export default {
  createRoutingControl,
  safeClearRouteLines,
  safelyDisposeRoutingControl,
  DEFAULT_ROUTING_OPTIONS
};