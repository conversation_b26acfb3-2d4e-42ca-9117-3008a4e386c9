import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { collection, getDocs, query, where, getDoc, doc, orderBy } from 'firebase/firestore'; // ✅ UPDATED: Added orderBy import
import { db } from './firebase'; // ✅ Import your Firebase db instance (adjust path as needed)
import { 
  COLORS_MAP, 
  determineDriveType, 
  geocodeAddress, 
  generateMultipleCarViews,
  formatUserDisplayName  // ✅ Added missing import
} from './utility-functions';
import { VehicleRender } from './ui-components';
import AddressComponent from './AddressComponent';

const OrderForm = ({ onSubmit, onCancel }) => {
  // ✅ Auto-save key for localStorage
  const AUTOSAVE_KEY = 'orderForm_autosave';
  
  // ✅ Load saved data from localStorage or use defaults with teamId
  const loadSavedData = () => {
    try {
      const saved = localStorage.getItem(AUTOSAVE_KEY);
      if (saved) {
        const parsedData = JSON.parse(saved);
        return {
          formData: parsedData.formData || {
            make: '',
            model: '',
            year: '',
            color: '',
            vin: '',
            licensePlate: '',
            caseNumber: '',
            customerName: '',
            dueDate: '',
            lienholder: '',
            accountNumber: '',
            notes: '',
            status: 'open',
            secure: false,
            teamId: '', // ✅ Team selection
          },
          addresses: parsedData.addresses || [],
          savedAt: parsedData.savedAt
        };
      }
    } catch (error) {
      console.error('Error loading saved form data:', error);
    }
    
    return {
      formData: {
        make: '',
        model: '',
        year: '',
        color: '',
        vin: '',
        licensePlate: '',
        caseNumber: '',
        customerName: '',
        dueDate: '',
        lienholder: '',
        accountNumber: '',
        notes: '',
        status: 'open',
        secure: false,
        teamId: '', // ✅ Team selection
      },
      addresses: [],
      savedAt: null
    };
  };
  
  const savedData = loadSavedData();
  
  const [formData, setFormData] = useState(savedData.formData);
  const [addresses, setAddresses] = useState(savedData.addresses);
  
  // ✅ Team management state
  const [availableTeams, setAvailableTeams] = useState([]);
  const [loadingTeams, setLoadingTeams] = useState(true);
  const [selectedTeam, setSelectedTeam] = useState(null);
  const [error, setError] = useState(null); // ✅ Added error state
  
  // Existing state...
  const [vehicleImage, setVehicleImage] = useState(null);
  const [isGeocodingInProgress, setIsGeocodingInProgress] = useState(false);
  const [lastSaved, setLastSaved] = useState(savedData.savedAt ? new Date(savedData.savedAt) : null); // ✅ Track last save time
  const { currentUser } = useAuth();
  
  // State for vehicle image rendering
  const [vehicleImageUrl, setVehicleImageUrl] = useState('');
  const [isLoadingImage, setIsLoadingImage] = useState(false);
  const [renderedViews, setRenderedViews] = useState([]);
  const [selectedViewIndex, setSelectedViewIndex] = useState(0);
  const [imageLoadError, setImageLoadError] = useState(null); // ✅ Track image loading errors
  
  // ✅ FIXED: Load available teams for the user with correct database structure
  useEffect(() => {
    const loadUserTeams = async () => {
      if (!currentUser || !db) {
        setLoadingTeams(false);
        return;
      }
      
      try {
        setLoadingTeams(true);
        setError(null);
        console.log('🏢 Loading teams for user:', currentUser.uid);
        
        const teams = [];
        
        // ✅ FIXED: Method 1 - Get all teams and check if user is a member of each
        try {
          console.log('📋 Fetching all teams and checking membership...');
          
          // First, get all teams
          const allTeamsQuery = query(
            collection(db, 'teams'),
            orderBy('name', 'asc')
          );
          
          const allTeamsSnapshot = await getDocs(allTeamsQuery);
          console.log(`Found ${allTeamsSnapshot.docs.length} total teams`);
          
          // For each team, check if current user is a member
          for (const teamDoc of allTeamsSnapshot.docs) {
            const teamData = teamDoc.data();
            const teamId = teamDoc.id;
            
            try {
              // Check if user is in this team's members subcollection
              const teamMembersQuery = query(
                collection(db, 'teams', teamId, 'teamMembers'),
                where('userId', '==', currentUser.uid)
              );
              
              const teamMembersSnapshot = await getDocs(teamMembersQuery);
              
              if (!teamMembersSnapshot.empty) {
                // User is a member of this team
                const memberData = teamMembersSnapshot.docs[0].data();
                
                teams.push({
                  id: teamId,
                  name: teamData.name || 'Unnamed Team',
                  description: teamData.description || '',
                  memberRole: memberData.role || 'member',
                  memberSince: memberData.joinedAt || null,
                  isUserMember: true,
                  ...teamData
                });
                
                console.log(`✅ User is member of team: ${teamData.name} (role: ${memberData.role || 'member'})`);
              }
            } catch (memberCheckError) {
              console.warn(`Could not check membership for team ${teamId}:`, memberCheckError);
            }
          }
          
        } catch (teamQueryError) {
          console.error('❌ Error querying teams:', teamQueryError);
        }
        
        // ✅ Method 2: Check user's profile for team assignment (fallback)
        if (teams.length === 0) {
          try {
            console.log('📝 No teams found via membership, checking user profile...');
            
            const userProfileDoc = await getDoc(doc(db, 'userProfiles', currentUser.uid));
            if (userProfileDoc.exists()) {
              const profile = userProfileDoc.data();
              if (profile.teamId) {
                const teamDoc = await getDoc(doc(db, 'teams', profile.teamId));
                if (teamDoc.exists()) {
                  teams.push({
                    id: teamDoc.id,
                    name: teamDoc.data().name || 'My Team',
                    description: 'From user profile',
                    memberRole: 'member',
                    isUserMember: true,
                    ...teamDoc.data()
                  });
                  console.log(`✅ Found team from user profile: ${teamDoc.data().name}`);
                }
              }
            }
          } catch (profileError) {
            console.log('📝 No user profile found or error:', profileError);
          }
        }
        
        // ✅ Method 3: Check localStorage for recent team (fallback)
        if (teams.length === 0) {
          try {
            console.log('🔄 No teams found, checking localStorage for recent team...');
            
            const lastTeamId = localStorage.getItem('lastUsedTeamId');
            if (lastTeamId) {
              const teamDoc = await getDoc(doc(db, 'teams', lastTeamId));
              if (teamDoc.exists()) {
                teams.push({
                  id: teamDoc.id,
                  name: teamDoc.data().name || 'Recent Team',
                  description: 'From recent usage',
                  memberRole: 'member',
                  isUserMember: false, // We don't know for sure
                  ...teamDoc.data()
                });
                console.log(`✅ Found recent team: ${teamDoc.data().name}`);
              }
            }
          } catch (teamError) {
            console.log('🏢 Recent team not found:', teamError);
          }
        }
        
        // ✅ Method 4: Admin override - load all teams if user might be admin
        if (teams.length === 0) {
          try {
            console.log('👑 Checking if user might be admin...');
            
            // Check if user email suggests admin privileges
            const userEmail = currentUser.email?.toLowerCase() || '';
            const isLikelyAdmin = userEmail.includes('admin') || 
                                userEmail.includes('manager') || 
                                userEmail.includes('supervisor');
            
            if (isLikelyAdmin) {
              console.log('👑 User appears to be admin, loading all teams...');
              
              const allTeamsQuery = query(
                collection(db, 'teams'),
                orderBy('name', 'asc')
              );
              
              const allTeamsSnapshot = await getDocs(allTeamsQuery);
              allTeamsSnapshot.docs.forEach(doc => {
                teams.push({
                  id: doc.id,
                  name: doc.data().name || 'Team',
                  description: 'Admin access',
                  memberRole: 'admin',
                  isUserMember: false,
                  isAdminAccess: true,
                  ...doc.data()
                });
              });
              
              console.log(`👑 Loaded ${teams.length} teams for admin user`);
            }
          } catch (adminError) {
            console.log('👑 Admin check failed:', adminError);
          }
        }
        
        console.log(`✅ Final result: Found ${teams.length} teams for user`);
        setAvailableTeams(teams);
        
        // ✅ Auto-select team logic
        if (formData.teamId) {
          // If form already has a teamId, try to find and select that team
          const savedTeam = teams.find(t => t.id === formData.teamId);
          if (savedTeam) {
            setSelectedTeam(savedTeam);
            console.log(`🎯 Restored saved team: ${savedTeam.name}`);
          }
        } else if (teams.length === 1) {
          // If user only belongs to one team, auto-select it
          setSelectedTeam(teams[0]);
          setFormData(prev => ({ ...prev, teamId: teams[0].id }));
          console.log(`🎯 Auto-selected single team: ${teams[0].name}`);
        } else {
          // Check localStorage for last used team
          const lastTeamId = localStorage.getItem('lastUsedTeamId');
          const lastTeam = teams.find(t => t.id === lastTeamId);
          if (lastTeam) {
            setSelectedTeam(lastTeam);
            setFormData(prev => ({ ...prev, teamId: lastTeam.id }));
            console.log(`🎯 Auto-selected recent team: ${lastTeam.name}`);
          }
        }
        
      } catch (error) {
        console.error('❌ Error loading teams:', error);
        setError('Failed to load teams. Please refresh the page.');
      } finally {
        setLoadingTeams(false);
      }
    };
    
    loadUserTeams();
  }, [currentUser, formData.teamId]);
  
  // ✅ Handle team selection
  const handleTeamChange = (e) => {
    const teamId = e.target.value;
    const team = availableTeams.find(t => t.id === teamId);
    
    setSelectedTeam(team);
    setFormData(prev => ({ ...prev, teamId: teamId }));
    
    // Save to localStorage for future use
    if (teamId) {
      localStorage.setItem('lastUsedTeamId', teamId);
    }
  };
  
  // ✅ ENHANCED: Team selection dropdown with better UI
  const renderTeamDropdown = () => {
    return (
      <div className="mb-4 p-3 bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg">
        <h4 className="font-semibold text-sm mb-3 text-blue-300 flex items-center">
          <span className="mr-2">🏢</span>
          Team Assignment
          {availableTeams.length > 0 && (
            <span className="ml-2 bg-blue-600 text-white px-2 py-1 rounded-full text-xs">
              {availableTeams.length} available
            </span>
          )}
        </h4>
        
        {loadingTeams ? (
          <div className="flex items-center text-blue-300">
            <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading teams...
          </div>
        ) : availableTeams.length === 0 ? (
          <div className="space-y-3">
            <div className="text-yellow-300 text-sm flex items-center">
              <span className="mr-2">⚠️</span>
              No teams found for your account
            </div>
            <div className="text-xs text-gray-400 bg-gray-800 p-3 rounded border border-gray-600">
              <p className="font-semibold mb-2">Possible solutions:</p>
              <ul className="space-y-1">
                <li>• Contact your administrator to add you to a team</li>
                <li>• Check if you're logged in with the correct account</li>
                <li>• Try refreshing the page</li>
              </ul>
            </div>
            <button 
              type="button"
              onClick={() => window.location.reload()}
              className="text-blue-400 hover:text-blue-300 text-sm underline"
            >
              🔄 Refresh page
            </button>
          </div>
        ) : (
          <div>
            <label className="block text-blue-200 text-xs mb-2">
              Select Team for this Order *
            </label>
            <select
              value={formData.teamId}
              onChange={handleTeamChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
              required
            >
              <option value="">-- Select Team --</option>
              
              {/* User's teams first */}
              {availableTeams.filter(team => team.isUserMember).length > 0 && (
                <optgroup label="Your Teams">
                  {availableTeams
                    .filter(team => team.isUserMember)
                    .map(team => (
                      <option key={team.id} value={team.id}>
                        ⭐ {team.name} 
                        {team.memberRole && team.memberRole !== 'member' ? ` (${team.memberRole})` : ''}
                      </option>
                    ))}
                </optgroup>
              )}
              
              {/* Other teams */}
              {availableTeams.filter(team => !team.isUserMember).length > 0 && (
                <optgroup label="Other Teams">
                  {availableTeams
                    .filter(team => !team.isUserMember)
                    .map(team => (
                      <option key={team.id} value={team.id}>
                        {team.name}
                        {team.isAdminAccess ? ' (Admin Access)' : ''}
                        {team.id === localStorage.getItem('lastUsedTeamId') ? ' (Recent)' : ''}
                      </option>
                    ))}
                </optgroup>
              )}
            </select>
            
            {/* Selected team info */}
            {selectedTeam && (
              <div className="mt-3 p-3 bg-green-900 bg-opacity-30 border border-green-600 rounded">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-green-200 font-semibold text-sm">
                        {selectedTeam.name}
                      </span>
                      {selectedTeam.isUserMember && (
                        <span className="ml-2 bg-green-600 text-white px-2 py-1 rounded-full text-xs">
                          Member
                        </span>
                      )}
                      {selectedTeam.isAdminAccess && (
                        <span className="ml-2 bg-purple-600 text-white px-2 py-1 rounded-full text-xs">
                          Admin
                        </span>
                      )}
                    </div>
                    
                    {selectedTeam.description && (
                      <p className="text-green-300 text-xs mt-1">
                        {selectedTeam.description}
                      </p>
                    )}
                    
                    {selectedTeam.memberRole && (
                      <p className="text-green-400 text-xs mt-1">
                        Your role: {selectedTeam.memberRole}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="mt-2 text-xs text-green-300">
                  ✅ Order will be assigned to this team and appear on their map
                </div>
              </div>
            )}
            
            {/* Debug info (remove in production) */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-2 text-xs text-gray-500">
                Debug: Found {availableTeams.length} teams for user {currentUser?.uid}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  
  // ✅ Auto-save function
  const saveFormData = () => {
    try {
      const dataToSave = {
        formData,
        addresses,
        savedAt: new Date().toISOString()
      };
      localStorage.setItem(AUTOSAVE_KEY, JSON.stringify(dataToSave));
      setLastSaved(new Date());
      console.log('📝 Form data auto-saved');
    } catch (error) {
      console.error('Error saving form data:', error);
    }
  };
  
  // ✅ Clear saved data
  const clearSavedData = () => {
    try {
      localStorage.removeItem(AUTOSAVE_KEY);
      setLastSaved(null);
      console.log('🗑️ Saved form data cleared');
    } catch (error) {
      console.error('Error clearing saved data:', error);
    }
  };
  
  // ✅ Auto-save whenever form data changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (formData.make || formData.model || formData.vin || formData.licensePlate || addresses.length > 0) {
        saveFormData();
      }
    }, 1000); // Save 1 second after user stops typing
    
    return () => clearTimeout(timeoutId);
  }, [formData, addresses]);
  
  // ✅ Load vehicle image from saved data if available
  useEffect(() => {
    if (savedData.formData.make && savedData.formData.model && !vehicleImageUrl) {
      searchForVehicleImage();
    }
  }, []); // Only run on mount
  
  // ✅ IMPROVED: Enhanced function with better error handling and rate limiting
  const searchForVehicleImage = async () => {
    if (!formData.make || !formData.model) return;
    
    setIsLoadingImage(true);
    setImageLoadError(null);
    
    try {
      console.log(`🔍 Generating vehicle images for ${formData.year} ${formData.make} ${formData.model}`);
      
      // Generate multiple angles for the vehicle using our function with retry logic
      const generatedViews = await generateMultipleCarViews(
        formData.make, 
        formData.model, 
        formData.year, 
        formData.color
      );
      
      if (generatedViews && generatedViews.length > 0) {
        setRenderedViews(generatedViews);
        
        // Set the first view as the main image
        setVehicleImageUrl(generatedViews[0].url);
        setVehicleImage(generatedViews[0].url);
        setSelectedViewIndex(0);
        
        console.log(`✅ Generated ${generatedViews.length} vehicle views successfully`);
        
        // Show user if we're using validated data or fallback
        if (generatedViews[0].carData) {
          console.log('✅ Using validated car data from API');
        } else if (generatedViews[0].isFallback) {
          console.log('⚠️ Using fallback rendering method');
        }
      } else {
        throw new Error('No views generated');
      }
    } catch (error) {
      console.error("❌ Error searching for vehicle image:", error);
      
      // Set error message for user
      if (error.message.includes('429')) {
        setImageLoadError('⏱️ Vehicle image service is temporarily busy. Using text placeholder.');
      } else if (error.message.includes('quota')) {
        setImageLoadError('📊 Daily image generation limit reached. Using text placeholder.');
      } else {
        setImageLoadError('🖼️ Could not generate vehicle image. Using text placeholder.');
      }
      
      // ✅ IMPROVED: Better fallback with vehicle info
      const fallbackSvgUrl = createVehicleFallbackSVG(
        formData.year, 
        formData.make, 
        formData.model, 
        formData.color
      );
      
      setVehicleImageUrl(fallbackSvgUrl);
      setVehicleImage(fallbackSvgUrl);
      setRenderedViews([{
        url: fallbackSvgUrl,
        angle: '1',
        colorName: formData.color || 'Unknown',
        colorCode: formData.color ? COLORS_MAP[formData.color] || formData.color.toLowerCase() : 'white',
        isFallback: true
      }]);
    }
    
    setIsLoadingImage(false);
  };
  
  // ✅ Create a better fallback SVG
  const createVehicleFallbackSVG = (year, make, model, color) => {
    const colorCode = color ? COLORS_MAP[color] || color.toLowerCase() : '#4A90E2';
    const vehicleText = `${year || ''} ${make || ''} ${model || ''}`.trim();
    
    // Create a simple car silhouette SVG with color
    const svgContent = `
      <svg xmlns="http://www.w3.org/2000/svg" width="400" height="225" viewBox="0 0 400 225">
        <defs>
          <linearGradient id="carGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:${colorCode};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${colorCode};stop-opacity:0.7" />
          </linearGradient>
        </defs>
        <rect width="400" height="225" fill="#1A2642" />
        
        <!-- Car body -->
        <path d="M50 120 L50 100 Q50 90 60 90 L140 90 Q150 80 170 80 L230 80 Q250 80 260 90 L340 90 Q350 90 350 100 L350 120 L350 140 Q350 150 340 150 L320 150 Q310 160 300 160 L280 160 Q270 160 260 150 L140 150 Q130 160 120 160 L100 160 Q90 160 80 150 L60 150 Q50 150 50 140 Z" 
              fill="url(#carGradient)" stroke="#333" stroke-width="2"/>
        
        <!-- Windows -->
        <path d="M70 100 Q70 95 75 95 L145 95 Q150 85 165 85 L235 85 Q245 85 250 95 L325 95 Q330 95 330 100 L330 115 L70 115 Z" 
              fill="#87CEEB" opacity="0.8"/>
        
        <!-- Wheels -->
        <circle cx="110" cy="150" r="15" fill="#2C2C2C" stroke="#555" stroke-width="2"/>
        <circle cx="110" cy="150" r="8" fill="#666"/>
        <circle cx="290" cy="150" r="15" fill="#2C2C2C" stroke="#555" stroke-width="2"/>
        <circle cx="290" cy="150" r="8" fill="#666"/>
        
        <!-- Headlights -->
        <ellipse cx="65" cy="105" rx="8" ry="12" fill="#FFF8DC" opacity="0.9"/>
        <ellipse cx="335" cy="105" rx="8" ry="12" fill="#FFF8DC" opacity="0.9"/>
        
        <!-- Vehicle text -->
        <text x="200" y="190" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white" text-anchor="middle">${vehicleText}</text>
        <text x="200" y="210" font-family="Arial, sans-serif" font-size="12" fill="#ccc" text-anchor="middle">${color || 'Vehicle Image'}</text>
      </svg>
    `;
    
    return `data:image/svg+xml;utf8,${encodeURIComponent(svgContent)}`;
  };
  
  // Handle switching between available vehicle views
  const handleSwitchView = (index) => {
    if (renderedViews[index]) {
      setVehicleImageUrl(renderedViews[index].url);
      setVehicleImage(renderedViews[index].url);
      setSelectedViewIndex(index);
    }
  };
  
  // ✅ IMPROVED: Debounced image search to prevent API spam
  useEffect(() => {
    if (formData.make && formData.model) {
      const timeoutId = setTimeout(() => {
        searchForVehicleImage();
      }, 1000); // Wait 1 second after user stops typing
      
      return () => clearTimeout(timeoutId);
    }
  }, [formData.make, formData.model, formData.year, formData.color]);
  
  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Auto-save is handled by useEffect above
  };
  
  // Auto-set drive type when make, model, or year changes
  useEffect(() => {
    if (formData.make && formData.model) {
      const suggestedDriveType = determineDriveType(formData.make, formData.model, formData.year);
      setFormData(prev => ({ ...prev, driveType: suggestedDriveType }));
    }
  }, [formData.make, formData.model, formData.year]);
  
  // Handle adding a new address
  const handleAddAddress = () => {
    setAddresses([...addresses, {
      street: '',
      city: '',
      state: '',
      zip: '',
      checkIns: []
    }]);
  };
  
  // Handle address change with geocoding
  const handleAddressChange = async (index, newAddress, forceGeocode = false) => {
    try {
      if (forceGeocode || !newAddress.position) {
        setIsGeocodingInProgress(true);
        
        // Geocode the address
        const geocodedAddress = await geocodeAddress(newAddress);
        
        // Update the address with geocoded data
        const updatedAddresses = [...addresses];
        updatedAddresses[index] = geocodedAddress;
        setAddresses(updatedAddresses);
        
        setIsGeocodingInProgress(false);
      } else {
        // Just update the address without geocoding
        const updatedAddresses = [...addresses];
        updatedAddresses[index] = newAddress;
        setAddresses(updatedAddresses);
      }
      // Auto-save is handled by useEffect above
    } catch (error) {
      console.error("Error updating address:", error);
      setIsGeocodingInProgress(false);
      
      // Still update the address even if geocoding fails
      const updatedAddresses = [...addresses];
      updatedAddresses[index] = newAddress;
      setAddresses(updatedAddresses);
    }
  };
  
  // Handle address deletion
  const handleDeleteAddress = (index) => {
    const updatedAddresses = [...addresses];
    updatedAddresses.splice(index, 1);
    setAddresses(updatedAddresses);
  };
  
  // Handle address check-in
  const handleAddressCheckIn = (index) => {
    const updatedAddresses = [...addresses];
    const address = updatedAddresses[index];
    
    if (!address.checkIns) {
      address.checkIns = [];
    }
    
    // Only allow up to 6 check-ins
    if (address.checkIns.length < 6) {
      const now = new Date();
      address.checkIns.push({
        timestamp: now,
        date: now.toISOString(),
        userId: currentUser?.uid,
        userName: formatUserDisplayName(currentUser) // ✅ FIXED: Now properly imported
      });
    }
    
    setAddresses(updatedAddresses);
  };
  
  // ✅ UPDATED: Handle form submission with team validation
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // ✅ Validate team selection
    if (!formData.teamId) {
      alert('Please select a team for this order');
      return;
    }
    
    try {
      // Geocode any addresses that don't have coordinates
      setIsGeocodingInProgress(true);
      const geocodedAddresses = await Promise.all(
        addresses.map(async (addr) => {
          if (addr.street && (!addr.position || !addr.position.lat || !addr.position.lng)) {
            try {
              return await geocodeAddress(addr);
            } catch (geoError) {
              console.warn('Geocoding failed for address:', addr, geoError);
              return addr; // Return original address if geocoding fails
            }
          }
          return addr;
        })
      );
      setIsGeocodingInProgress(false);
      
      // Due date handling
      let dueDate = null;
      if (formData.dueDate) {
        dueDate = new Date(formData.dueDate);
      }
      
      // Process addresses and filter out empty ones
      const validAddresses = geocodedAddresses.filter(addr => addr.street && addr.street.trim() !== '');
      
      // Get current timestamp
      const timestamp = new Date();
      
      // Determine position for the order based on first address with valid coordinates
      const firstValidAddress = validAddresses.find(addr => addr.position?.lat && addr.position?.lng);
      const position = firstValidAddress?.position || null; // Only use position from valid addresses
      
      // ✅ UPDATED: Create the order data with explicit team assignment
      const orderData = {
        ...formData,
        vehicleImage: vehicleImage,
        vehicleRenderViews: renderedViews,  // Store all rendered views
        addresses: validAddresses,
        dueDate: dueDate,
        position: position, // Only add position if we have valid coordinates from an address
        teamId: formData.teamId, // ✅ Explicit team assignment
        teamName: selectedTeam?.name || 'Unknown Team', // ✅ For debugging
        createdAt: timestamp,
        updatedAt: timestamp,
        createdBy: currentUser?.uid,
        createdByName: formatUserDisplayName(currentUser), // ✅ FIXED: Now properly imported
        name: `${formData.year} ${formData.make} ${formData.model} - ${formData.licensePlate}` // For locations panel integration
      };
      
      console.log("✅ Submitting order with team assignment:", {
        orderId: 'will-be-generated',
        teamId: orderData.teamId,
        teamName: orderData.teamName,
        vehicle: `${formData.year} ${formData.make} ${formData.model}`,
        hasPosition: !!orderData.position,
        addressCount: validAddresses.length
      });
      
      // ✅ Clear saved data on successful submission
      clearSavedData();
      
      onSubmit(orderData);
      
    } catch (error) {
      console.error("❌ Error submitting order:", error);
      setIsGeocodingInProgress(false);
      alert('Error creating order: ' + error.message);
    }
  };
  
  // ✅ Enhanced cancel function that clears saved data
  const handleCancel = () => {
    if (lastSaved) {
      const confirmClear = window.confirm(
        'You have unsaved changes. Are you sure you want to cancel? This will clear all your progress.'
      );
      if (!confirmClear) return;
    }
    clearSavedData();
    onCancel();
  };
  
  return (
    <form onSubmit={handleSubmit} className="bg-gray-800 rounded-lg shadow-lg p-4 border border-gray-700 animate-fadeIn">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">
            Create New Order
          </h3>
          {/* ✅ Auto-save status indicator */}
          {lastSaved && (
            <div className="flex items-center mt-1 text-xs text-green-400">
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              Auto-saved {lastSaved.toLocaleTimeString()}
            </div>
          )}
        </div>
        <div className="space-x-2">
          <button
            type="button"
            onClick={handleCancel}
            className="px-3 py-1 text-sm text-gray-300 border border-gray-700 rounded hover:bg-gray-700 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isGeocodingInProgress || !formData.teamId}
            className={`px-3 py-1 text-sm bg-gradient-to-r from-blue-600 to-indigo-600 ${(isGeocodingInProgress || !formData.teamId) ? 'opacity-70 cursor-not-allowed' : 'hover:from-blue-500 hover:to-indigo-500'} text-white rounded shadow transition-colors duration-200 flex items-center`}
          >
            {isGeocodingInProgress && (
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            Create Order
          </button>
        </div>
      </div>
      
      {/* ✅ Team Selection Section - UPDATED */}
      {renderTeamDropdown()}
      
      {/* Error message for team loading */}
      {error && (
        <div className="mb-4 p-3 bg-red-900 bg-opacity-30 border border-red-600 rounded-lg">
          <div className="text-red-200 text-sm flex items-center">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-semibold text-sm mb-3 text-blue-300 border-b border-gray-700 pb-2">Vehicle Details</h4>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">Make*</label>
              <input
                type="text"
                name="make"
                value={formData.make}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
                required
              />
            </div>
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">Model*</label>
              <input
                type="text"
                name="model"
                value={formData.model}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
                required
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">Year*</label>
              <input
                type="text"
                name="year"
                value={formData.year}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
                required
              />
            </div>
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">Color</label>
              <select
                name="color"
                value={formData.color}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
              >
                <option value="">Select color</option>
                <option value="Black">Black</option>
                <option value="White">White</option>
                <option value="Silver">Silver</option>
                <option value="Gray">Gray</option>
                <option value="Red">Red</option>
                <option value="Blue">Blue</option>
                <option value="Green">Green</option>
                <option value="Yellow">Yellow</option>
                <option value="Brown">Brown</option>
                <option value="Orange">Orange</option>
                <option value="Purple">Purple</option>
                <option value="Gold">Gold</option>
                <option value="Beige">Beige</option>
                <option value="Burgundy">Burgundy</option>
              </select>
            </div>
          </div>
          
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">VIN*</label>
            <input
              type="text"
              name="vin"
              value={formData.vin}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
              required
            />
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">License Plate*</label>
              <input
                type="text"
                name="licensePlate"
                value={formData.licensePlate}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
                required
              />
            </div>
            <div className="mb-3">
              <label className="block text-gray-300 text-xs mb-1">Case Number*</label>
              <input
                type="text"
                name="caseNumber"
                value={formData.caseNumber}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
                required
              />
            </div>
          </div>
          
          <div className="mb-3">
            <label className="flex justify-between text-gray-300 text-xs mb-1">
              <span>Drive Type</span> 
              <span className="text-blue-400">(Auto-detected)</span>
            </label>
            <div className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white">
              {formData.driveType || 'Will be auto-detected'}
            </div>
          </div>
          
          {/* Multiple Addresses with enhanced fields */}
          <div className="mb-3">
            <div className="flex justify-between items-center mb-1">
              <label className="block text-gray-300 text-xs">Vehicle Addresses</label>
            </div>
            
            <AddressComponent 
              addresses={addresses}
              onAddressChange={handleAddressChange}
              onAddressAdd={handleAddAddress}
              onAddressDelete={handleDeleteAddress}
              onAddressCheckIn={handleAddressCheckIn}
              isGeocodingInProgress={isGeocodingInProgress}
            />
          </div>
        </div>
        
        <div>
          <h4 className="font-semibold text-sm mb-3 text-blue-300 border-b border-gray-700 pb-2">Order Details</h4>
          
          {/* Customer Name */}
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Customer Name</label>
            <input
              type="text"
              name="customerName"
              value={formData.customerName}
              onChange={handleChange}
              placeholder="Enter customer's full name"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            />
          </div>
          
          {/* Due Date */}
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Due Date</label>
            <input
              type="date"
              name="dueDate"
              value={formData.dueDate}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            />
          </div>
          
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Lienholder</label>
            <input
              type="text"
              name="lienholder"
              value={formData.lienholder}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            />
          </div>
          
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Account Number</label>
            <input
              type="text"
              name="accountNumber"
              value={formData.accountNumber}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            />
          </div>
          
          {/* ✅ IMPROVED: Vehicle Image Preview with error handling */}
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Vehicle 3D Render</label>
            
            {/* Show error message if API failed */}
            {imageLoadError && (
              <div className="mb-2 p-2 bg-yellow-900 bg-opacity-50 border border-yellow-600 rounded text-yellow-200 text-xs">
                {imageLoadError}
              </div>
            )}
            
            <VehicleRender 
              vehicleImageUrl={vehicleImageUrl}
              renderedViews={renderedViews}
              make={formData.make}
              model={formData.model}
              year={formData.year}
              color={formData.color}
              selectedViewIndex={selectedViewIndex}
              onSwitchView={handleSwitchView}
              isLoading={isLoadingImage}
            />
            
            {/* Refresh render button with retry logic */}
            {formData.make && formData.model && (
              <button
                type="button"
                onClick={searchForVehicleImage}
                disabled={isLoadingImage}
                className="mt-1 text-sm text-blue-400 hover:text-blue-300 flex items-center disabled:opacity-50"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 mr-1 ${isLoadingImage ? 'animate-spin' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
                {isLoadingImage ? 'Generating...' : 'Refresh 3D render'}
              </button>
            )}
            
            {/* Color indicator */}
            {formData.color && (
              <div className="mt-1 flex items-center text-xs text-gray-400">
                <div className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: COLORS_MAP[formData.color] || formData.color.toLowerCase() }}></div>
                Color: {formData.color}
              </div>
            )}
          </div>
          
          {/* Enhanced Status Dropdown with new options */}
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Status</label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
            >
              <option value="open">Open</option>
              <option value="pending-pickup">Pending Pickup</option>
              <option value="secure">Secure</option>
              <option value="on-hold">On-Hold</option>
              <option value="claim">Claim</option>
              <option value="closed">Closed</option>
              <option value="restricted">Restricted</option>
            </select>
          </div>
          
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Security Status</label>
            <div className="flex mt-1">
              <button 
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, secure: false }))}
                className={`flex-1 py-2 flex items-center justify-center rounded-l ${!formData.secure ? 'bg-red-600' : 'bg-gray-700 hover:bg-red-600'} transition-colors duration-200`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                Not Secure
              </button>
              <button 
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, secure: true }))}
                className={`flex-1 py-2 flex items-center justify-center rounded-r ${formData.secure ? 'bg-green-600' : 'bg-gray-700 hover:bg-green-600'} transition-colors duration-200`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Secure
              </button>
            </div>
          </div>
          
          <div className="mb-3">
            <label className="block text-gray-300 text-xs mb-1">Notes</label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:border-blue-500"
              rows="4"
            />
          </div>
        </div>
      </div>
      
      {/* ✅ Team Assignment Validation */}
      {!formData.teamId && (
        <div className="mb-4 p-3 bg-red-900 bg-opacity-30 border border-red-600 rounded-lg">
          <div className="text-red-200 text-sm flex items-center">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            Please select a team before creating the order. This ensures the order appears on the correct team's map.
          </div>
        </div>
      )}
      
      {/* ✅ Auto-save info and controls */}
      {lastSaved && (
        <div className="mb-4 p-3 bg-green-900 bg-opacity-30 border border-green-600 rounded-lg">
          <div className="flex justify-between items-center">
            <div className="flex items-center text-sm text-green-200">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>Form auto-saved at {lastSaved.toLocaleTimeString()}</span>
              {selectedTeam && (
                <span className="ml-2 text-blue-300">• Team: {selectedTeam.name}</span>
              )}
            </div>
            <button
              type="button"
              onClick={() => {
                if (window.confirm('Are you sure you want to clear all saved data and start fresh?')) {
                  clearSavedData();
                  // Reset form to initial state
                  setFormData({
                    make: '', model: '', year: '', color: '', vin: '', licensePlate: '',
                    caseNumber: '', customerName: '', dueDate: '', lienholder: '',
                    accountNumber: '', notes: '', status: 'open', secure: false, teamId: ''
                  });
                  setAddresses([]);
                  setVehicleImage(null);
                  setVehicleImageUrl('');
                  setRenderedViews([]);
                  setSelectedTeam(null);
                }
              }}
              className="text-xs text-red-400 hover:text-red-300 underline"
            >
              Clear saved data
            </button>
          </div>
          <div className="text-xs text-green-300 mt-1">
            💾 Your progress is automatically saved. If you reload the page, your data will be restored.
          </div>
        </div>
      )}
    </form>
  );
};

export default OrderForm;