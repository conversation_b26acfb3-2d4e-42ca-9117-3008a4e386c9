import React, { useState, useEffect } from 'react';
import { getFirestore, collection, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc, serverTimestamp, query, where } from 'firebase/firestore';
import { useAuth } from '../contexts/AuthContext.js';
import { useNavigate } from 'react-router-dom';

function Team() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [db, setDb] = useState(null);
  
  // State for teams
  const [teams, setTeams] = useState([]);
  const [users, setUsers] = useState([]);
  const [areas, setAreas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // State for team creation/editing
  const [isCreatingTeam, setIsCreatingTeam] = useState(false);
  const [isEditingTeam, setIsEditingTeam] = useState(false);
  const [currentTeam, setCurrentTeam] = useState(null);
  const [teamName, setTeamName] = useState('');
  const [selectedArea, setSelectedArea] = useState('');
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentTab, setCurrentTab] = useState('teams');
  
  // Color options for team cards
  const teamColors = [
    { bg: "from-blue-600 to-blue-700", hover: "from-blue-500 to-blue-600", text: "text-blue-200" },
    { bg: "from-purple-600 to-purple-700", hover: "from-purple-500 to-purple-600", text: "text-purple-200" },
    { bg: "from-green-600 to-green-700", hover: "from-green-500 to-green-600", text: "text-green-200" },
    { bg: "from-red-600 to-red-700", hover: "from-red-500 to-red-600", text: "text-red-200" },
    { bg: "from-yellow-600 to-yellow-700", hover: "from-yellow-500 to-yellow-600", text: "text-yellow-200" },
    { bg: "from-indigo-600 to-indigo-700", hover: "from-indigo-500 to-indigo-600", text: "text-indigo-200" },
    { bg: "from-pink-600 to-pink-700", hover: "from-pink-500 to-pink-600", text: "text-pink-200" },
    { bg: "from-teal-600 to-teal-700", hover: "from-teal-500 to-teal-600", text: "text-teal-200" }
  ];
  
  // Assign a color to a team based on its index or area
  const getTeamColor = (team, index) => {
    // You can use different strategies to assign colors:
    // 1. Based on index (rotating through colors)
    // 2. Based on area (all teams in same area have same color)
    
    // Option 1: Based on index
    return teamColors[index % teamColors.length];
  };

  // Function to determine text color based on background color for tags
  const getTextColor = (hexColor) => {
    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    
    // Calculate brightness (YIQ formula)
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    
    return yiq >= 150 ? '#000000' : '#FFFFFF';
  };
  
  // Initialize Firestore
  useEffect(() => {
    try {
      const firestore = getFirestore();
      setDb(firestore);
    } catch (error) {
      console.error("Error initializing Firestore:", error);
      setError("Failed to connect to database.");
    }
  }, []);

  // Load data from Firestore
  useEffect(() => {
    if (!db) return;
    
    async function fetchData() {
      setLoading(true);
      try {
        // Fetch areas first to ensure they're available for team processing
        const areasCollection = collection(db, 'areas');
        const areasSnapshot = await getDocs(areasCollection);
        
        let areasData = [];
        
        // If no areas exist, create default ones
        if (areasSnapshot.empty) {
          const defaultAreas = [
            { name: 'North Region' },
            { name: 'South Region' },
            { name: 'East Region' },
            { name: 'West Region' },
            { name: 'Central Region' }
          ];
          
          const areaPromises = defaultAreas.map(area => addDoc(areasCollection, {
            name: area.name,
            createdAt: serverTimestamp()
          }));
          
          const areaRefs = await Promise.all(areaPromises);
          
          areasData = defaultAreas.map((area, index) => ({
            id: areaRefs[index].id,
            ...area
          }));
        } else {
          areasData = areasSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
        }
        
        setAreas(areasData);
        
        // Fetch teams
        const teamsCollection = collection(db, 'teams');
        const teamsSnapshot = await getDocs(teamsCollection);
        
        const teamsData = await Promise.all(teamsSnapshot.docs.map(async docRef => {
          const teamData = {
            id: docRef.id,
            ...docRef.data(),
            members: [] // Initialize members array
          };
          
          // Make sure team has area property, even if it's missing in the database
          if (!teamData.areaId && !teamData.area) {
            teamData.area = { id: null, name: 'No Area Assigned' };
          } else if (teamData.areaId && !teamData.area) {
            // Try to find matching area
            const matchingArea = areasData.find(area => area.id === teamData.areaId);
            teamData.area = matchingArea || { id: teamData.areaId, name: 'Unknown Area' };
          } else if (teamData.area && typeof teamData.area === 'object' && !teamData.area.name) {
            // Area object exists but doesn't have name property
            teamData.area.name = 'Unknown Area';
          }
          
          // Fetch team members if teamMembers collection exists
          try {
            const membersCollection = collection(db, `teams/${docRef.id}/teamMembers`);
            const membersSnapshot = await getDocs(membersCollection);
            
            if (!membersSnapshot.empty) {
              // Get member details for each member reference
              const memberIds = membersSnapshot.docs.map(doc => doc.data().userId);
              
              const memberPromises = memberIds.map(async userId => {
                const userDoc = await getDoc(doc(db, "users", userId));
                const userProfileDoc = await getDoc(doc(db, "userProfiles", userId));
                
                const userData = userDoc.exists() ? userDoc.data() : {};
                const profileData = userProfileDoc.exists() ? userProfileDoc.data() : {};
                
                return {
                  id: userId,
                  name: profileData.displayName || userData.email?.split('@')[0] || 'Unknown User',
                  email: userData.email || 'No email',
                  role: userData.role || 'user',
                  profileData: profileData
                };
              });
              
              teamData.members = await Promise.all(memberPromises);
            }
          } catch (err) {
            console.error(`Error fetching team members for team ${docRef.id}:`, err);
          }
          
          return teamData;
        }));
        
        setTeams(teamsData);
        
        // Fetch users
        const usersCollection = collection(db, 'users');
        const usersSnapshot = await getDocs(usersCollection);
        
        const usersData = await Promise.all(usersSnapshot.docs.map(async docRef => {
          const userData = {
            id: docRef.id,
            ...docRef.data()
          };
          
          // Try to get profile info for the user, including tags
          try {
            const profileDoc = await getDoc(doc(db, "userProfiles", docRef.id));
            if (profileDoc.exists()) {
              const profileData = profileDoc.data();
              return {
                ...userData,
                name: profileData.displayName || userData.email?.split('@')[0] || 'Unknown User',
                role: userData.role || 'user',
                tags: profileData.tags || [] // Get the user's tags from their profile
              };
            }
          } catch (profileErr) {
            console.error(`Error fetching profile for user ${docRef.id}:`, profileErr);
          }
          
          // Return with default values if no profile found
          return {
            ...userData,
            name: userData.email?.split('@')[0] || 'Unknown User',
            role: userData.role || 'user',
            tags: [] // Empty tags array if no profile found
          };
        }));
        
        setUsers(usersData);
        setLoading(false);
      } catch (err) {
        console.error("Error loading data:", err);
        setError("Failed to load data from the database");
        setLoading(false);
      }
    }
    
    fetchData();
  }, [db]);
  
  // Filtered users based on search term
  const filteredUsers = users.filter(user => 
    (user.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) || 
    (user.email?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (user.role?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );
  
  // Create new team in Firestore
  const handleCreateTeam = async () => {
    if (!teamName || !selectedArea) {
      alert('Please provide a team name and select an area');
      return;
    }
    
    setLoading(true);
    
    try {
      // Find selected area data
      const selectedAreaData = areas.find(a => a.id === selectedArea);
      
      if (!selectedAreaData) {
        throw new Error('Selected area not found');
      }
      
      // Create team in Firestore
      const teamRef = await addDoc(collection(db, "teams"), {
        name: teamName,
        areaId: selectedArea,
        areaData: { id: selectedArea, name: selectedAreaData.name },
        createdAt: serverTimestamp(),
        createdBy: currentUser.email,
        createdById: currentUser.uid,
        memberCount: selectedUsers.length
      });
      
      // Add creator to team as admin
      try {
        // Add to nested collection
        await addDoc(collection(db, `teams/${teamRef.id}/teamMembers`), {
          userId: currentUser.uid,
          addedAt: serverTimestamp(),
          addedBy: currentUser.uid,
          role: 'admin'
        });
        
        // Add to root collection
        await addDoc(collection(db, 'teamMembers'), {
          userId: currentUser.uid,
          teamId: teamRef.id,
          addedAt: serverTimestamp(),
          addedBy: currentUser.uid,
          role: 'admin'
        });
      } catch (error) {
        console.error("Error adding creator to team:", error);
      }
      
      // UPDATED: Add team members to both collections
      for (const user of selectedUsers) {
        try {
          // Add to nested collection
          await addDoc(collection(db, `teams/${teamRef.id}/teamMembers`), {
            userId: user.id,
            addedAt: serverTimestamp(),
            addedBy: currentUser.uid,
            role: 'member'
          });
          
          // Add to root collection
          await addDoc(collection(db, 'teamMembers'), {
            userId: user.id,
            teamId: teamRef.id,
            addedAt: serverTimestamp(),
            addedBy: currentUser.uid,
            role: 'member'
          });
          
          console.log(`Added user ${user.id} to team ${teamRef.id} in both collections`);
        } catch (error) {
          console.error(`Error adding user ${user.id} to team:`, error);
        }
      }
      
      // Get the created team with its ID
      const newTeam = {
        id: teamRef.id,
        name: teamName,
        area: selectedAreaData,
        members: selectedUsers,
        createdAt: new Date().toISOString(),
        createdBy: currentUser.email
      };
      
      // Update local state
      setTeams([...teams, newTeam]);
      resetForm();
      setLoading(false);
    } catch (error) {
      console.error("Error creating team:", error);
      setError("Failed to create team. Please try again.");
      setLoading(false);
    }
  };
  
  // Update existing team in Firestore
  const handleUpdateTeam = async () => {
    if (!teamName || !selectedArea) {
      alert('Please provide a team name and select an area');
      return;
    }
    
    setLoading(true);
    
    try {
      // Find selected area data
      const selectedAreaData = areas.find(a => a.id === selectedArea);
      
      if (!selectedAreaData) {
        throw new Error('Selected area not found');
      }
      
      // Update team document
      const teamRef = doc(db, "teams", currentTeam.id);
      await updateDoc(teamRef, {
        name: teamName,
        areaId: selectedArea,
        areaData: { id: selectedArea, name: selectedAreaData.name },
        updatedAt: serverTimestamp(),
        updatedBy: currentUser.email,
        updatedById: currentUser.uid,
        memberCount: selectedUsers.length
      });
      
      // Get current team members from nested collection
      const membersCollection = collection(db, `teams/${currentTeam.id}/teamMembers`);
      const membersSnapshot = await getDocs(membersCollection);
      
      // Map of existing member user IDs in nested collection
      const existingMemberIds = new Map();
      membersSnapshot.docs.forEach(doc => {
        existingMemberIds.set(doc.data().userId, doc.id);
      });
      
      // Get current team members from root collection
      const rootMembersCollection = collection(db, 'teamMembers');
      const rootQuery = query(rootMembersCollection, where('teamId', '==', currentTeam.id));
      const rootMembersSnapshot = await getDocs(rootQuery);
      
      // Map of existing member user IDs in root collection
      const existingRootMemberIds = new Map();
      rootMembersSnapshot.docs.forEach(doc => {
        existingRootMemberIds.set(doc.data().userId, doc.id);
      });
      
      // Identify members to add and remove
      const currentSelectedIds = selectedUsers.map(user => user.id);
      
      // Members to remove from nested collection
      for (const [userId, docId] of existingMemberIds.entries()) {
        if (!currentSelectedIds.includes(userId)) {
          // Remove member from nested collection
          await deleteDoc(doc(db, `teams/${currentTeam.id}/teamMembers`, docId));
          console.log(`Removed user ${userId} from nested collection`);
        }
      }
      
      // Members to remove from root collection
      for (const [userId, docId] of existingRootMemberIds.entries()) {
        if (!currentSelectedIds.includes(userId)) {
          // Remove member from root collection
          await deleteDoc(doc(db, 'teamMembers', docId));
          console.log(`Removed user ${userId} from root collection`);
        }
      }
      
      // Members to add
      for (const user of selectedUsers) {
        // Check if user is not in nested collection
        if (!existingMemberIds.has(user.id)) {
          // Add to nested collection
          await addDoc(collection(db, `teams/${currentTeam.id}/teamMembers`), {
            userId: user.id,
            addedAt: serverTimestamp(),
            addedBy: currentUser.uid,
            role: 'member'
          });
          console.log(`Added user ${user.id} to nested collection`);
        }
        
        // Check if user is not in root collection
        if (!existingRootMemberIds.has(user.id)) {
          // Add to root collection
          await addDoc(collection(db, 'teamMembers'), {
            userId: user.id,
            teamId: currentTeam.id,
            addedAt: serverTimestamp(),
            addedBy: currentUser.uid,
            role: 'member'
          });
          console.log(`Added user ${user.id} to root collection`);
        }
      }
      
      // Update local state
      const updatedTeams = teams.map(team => {
        if (team.id === currentTeam.id) {
          return {
            ...team,
            name: teamName,
            area: selectedAreaData,
            members: selectedUsers,
            updatedAt: new Date().toISOString(),
            updatedBy: currentUser.email
          };
        }
        return team;
      });
      
      setTeams(updatedTeams);
      resetForm();
      setLoading(false);
    } catch (error) {
      console.error("Error updating team:", error);
      setError("Failed to update team. Please try again.");
      setLoading(false);
    }
  };
  
  // Delete team from Firestore
  const handleDeleteTeam = async (teamId) => {
    if (window.confirm('Are you sure you want to delete this team?')) {
      setLoading(true);
      
      try {
        // Delete all team members from nested collection first
        const membersCollection = collection(db, `teams/${teamId}/teamMembers`);
        const membersSnapshot = await getDocs(membersCollection);
        
        const deletePromises = membersSnapshot.docs.map(doc => 
          deleteDoc(doc.ref)
        );
        
        await Promise.all(deletePromises);
        
        // Delete all team members from root collection
        const rootMembersCollection = collection(db, 'teamMembers');
        const rootQuery = query(rootMembersCollection, where('teamId', '==', teamId));
        const rootMembersSnapshot = await getDocs(rootQuery);
        
        const deleteRootPromises = rootMembersSnapshot.docs.map(doc => 
          deleteDoc(doc.ref)
        );
        
        await Promise.all(deleteRootPromises);
        
        // Delete the team document
        await deleteDoc(doc(db, "teams", teamId));
        
        // Update local state
        setTeams(teams.filter(team => team.id !== teamId));
        setLoading(false);
      } catch (error) {
        console.error("Error deleting team:", error);
        setError("Failed to delete team. Please try again.");
        setLoading(false);
      }
    }
  };
  
  // Edit team
  const handleEditTeam = (team) => {
    setCurrentTeam(team);
    setTeamName(team.name);
    setSelectedArea(team.area?.id || ''); // Add null check here
    setSelectedUsers(team.members);
    setIsEditingTeam(true);
    setIsCreatingTeam(false);
    setCurrentTab('editTeam');
  };
  
  // Reset form
  const resetForm = () => {
    setTeamName('');
    setSelectedArea('');
    setSelectedUsers([]);
    setIsCreatingTeam(false);
    setIsEditingTeam(false);
    setCurrentTeam(null);
    setCurrentTab('teams');
  };
  
  // Toggle user selection
  const toggleUserSelection = (user) => {
    if (selectedUsers.some(u => u.id === user.id)) {
      setSelectedUsers(selectedUsers.filter(u => u.id !== user.id));
    } else {
      setSelectedUsers([...selectedUsers, user]);
    }
  };
  
  // Check if user is already selected
  const isUserSelected = (userId) => {
    return selectedUsers.some(user => user.id === userId);
  };
  
  // Remove user from team
  const removeUserFromTeam = async (teamId, userId, userName) => {
    // Add confirmation dialog
    if (window.confirm(`Are you sure you want to remove ${userName} from this team?`)) {
      setLoading(true);
      
      try {
        // Find the team member document in nested collection
        const membersCollection = collection(db, `teams/${teamId}/teamMembers`);
        const memberQuery = query(membersCollection, where("userId", "==", userId));
        const memberSnapshot = await getDocs(memberQuery);
        
        if (!memberSnapshot.empty) {
          // Delete the team member document from nested collection
          await deleteDoc(memberSnapshot.docs[0].ref);
          console.log(`Removed user ${userId} from nested collection`);
        }
        
        // Find the team member document in root collection
        const rootMembersCollection = collection(db, 'teamMembers');
        const rootQuery = query(
          rootMembersCollection, 
          where("userId", "==", userId),
          where("teamId", "==", teamId)
        );
        const rootMemberSnapshot = await getDocs(rootQuery);
        
        if (!rootMemberSnapshot.empty) {
          // Delete the team member document from root collection
          await deleteDoc(rootMemberSnapshot.docs[0].ref);
          console.log(`Removed user ${userId} from root collection`);
        }
        
        // Update team member count
        const teamRef = doc(db, "teams", teamId);
        const teamDoc = await getDoc(teamRef);
        
        if (teamDoc.exists()) {
          const currentCount = teamDoc.data().memberCount || 0;
          await updateDoc(teamRef, {
            memberCount: Math.max(0, currentCount - 1),
            updatedAt: serverTimestamp(),
            updatedBy: currentUser.email
          });
        }
        
        // Update local state
        const updatedTeams = teams.map(team => {
          if (team.id === teamId) {
            return {
              ...team,
              members: team.members.filter(member => member.id !== userId)
            };
          }
          return team;
        });
        
        setTeams(updatedTeams);
        setLoading(false);
      } catch (error) {
        console.error("Error removing team member:", error);
        setError("Failed to remove team member. Please try again.");
        setLoading(false);
      }
    }
  };
  
  // If not admin, redirect to dashboard
  useEffect(() => {
    if (!isAdmin) {
      navigate('/dashboard');
    }
  }, [isAdmin, navigate]);
  
  if (!isAdmin) {
    return null;
  }
  
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Top Navigation Bar */}
      <nav className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 shadow-md border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">NWRepo</h1>
              </div>
              <div className="ml-6 flex space-x-4">
                <button onClick={() => navigate('/dashboard')} className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                  Dashboard
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>
      
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Team Management</h1>
            {!isCreatingTeam && !isEditingTeam && (
              <button
                onClick={() => {
                  setIsCreatingTeam(true);
                  setCurrentTab('createTeam');
                }}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md shadow-md"
                disabled={loading}
              >
                {loading ? 'Loading...' : 'Create New Team'}
              </button>
            )}
          </div>
          
          {error && (
            <div className="mt-4 bg-red-900 bg-opacity-75 text-red-100 px-4 py-3 rounded-md">
              <span>{error}</span>
              <button 
                className="float-right text-red-200 hover:text-white"
                onClick={() => setError(null)}
              >
                &times;
              </button>
            </div>
          )}
          
          {/* Tabs */}
          <div className="mt-4 border-b border-gray-700">
            <nav className="-mb-px flex space-x-6">
              <button
                onClick={() => setCurrentTab('teams')}
                className={`py-2 px-1 ${
                  currentTab === 'teams'
                    ? 'border-b-2 border-blue-400 text-blue-400'
                    : 'text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                Teams
              </button>
              {isCreatingTeam && (
                <button
                  onClick={() => setCurrentTab('createTeam')}
                  className={`py-2 px-1 ${
                    currentTab === 'createTeam'
                      ? 'border-b-2 border-blue-400 text-blue-400'
                      : 'text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  Create Team
                </button>
              )}
              {isEditingTeam && (
                <button
                  onClick={() => setCurrentTab('editTeam')}
                  className={`py-2 px-1 ${
                    currentTab === 'editTeam'
                      ? 'border-b-2 border-blue-400 text-blue-400'
                      : 'text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  Edit Team
                </button>
              )}
            </nav>
          </div>
          
          {/* Team List */}
          {currentTab === 'teams' && (
            <div className="mt-6">
              {loading ? (
                <div className="flex justify-center items-center py-12 bg-gray-800 rounded-lg border border-gray-700">
                  <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : teams.length === 0 ? (
                <div className="text-center py-12 bg-gray-800 rounded-lg border border-gray-700">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <p className="mt-2 text-sm text-gray-400">No teams created yet.</p>
                  <button
                    onClick={() => {
                      setIsCreatingTeam(true);
                      setCurrentTab('createTeam');
                    }}
                    className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md shadow-md"
                  >
                    Create Your First Team
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {teams.map((team, index) => {
                    // Get team color based on index
                    const teamColor = getTeamColor(team, index);
                    
                    return (
                      <div key={team.id} className="bg-gray-800 rounded-lg shadow-md border border-gray-700 overflow-hidden">
                        <div className={`bg-gradient-to-r ${teamColor.bg} px-4 py-3 flex justify-between items-center`}>
                          <h3 className="text-lg font-medium text-white">{team.name}</h3>
                          <div className="flex space-x-2">
                            <button 
                              onClick={() => handleEditTeam(team)}
                              className="text-white hover:text-gray-200"
                              disabled={loading}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                              </svg>
                            </button>
                            <button 
                              onClick={() => handleDeleteTeam(team.id)}
                              className="text-white hover:text-gray-200"
                              disabled={loading}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className={`px-4 py-3 bg-gray-750`}>
                          <div className="text-sm text-gray-300 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 mr-1 ${teamColor.text}`} viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                            </svg>
                            Area: {team.area?.name || 'No Area Assigned'}
                          </div>
                        </div>
                        <div className="px-4 py-3">
                          <h4 className="font-medium text-gray-200 mb-2">Team Members ({team.members.length})</h4>
                          {team.members.length === 0 ? (
                            <p className="text-sm text-gray-400">No members assigned yet</p>
                          ) : (
                            <ul className="space-y-2">
                              {team.members.map(member => (
                                <li key={member.id} className="flex justify-between items-center text-sm bg-gray-750 rounded p-2">
                                  <div>
                                    <span className="font-medium text-gray-200">{member.name}</span>
                                    <p className="text-xs text-gray-400">{member.role}</p>
                                  </div>
                                  <button 
                                    onClick={() => removeUserFromTeam(team.id, member.id, member.name)}
                                    className="text-gray-400 hover:text-red-400"
                                    disabled={loading}
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                  </button>
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                        <div className="px-4 py-2 bg-gray-750 text-xs text-gray-400">
                          Created: {new Date(team.createdAt).toLocaleDateString()} by {team.createdBy}
                          {team.updatedAt && (
                            <div>Updated: {new Date(team.updatedAt).toLocaleDateString()} by {team.updatedBy}</div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}
          
          {/* Create/Edit Team Form */}
          {(currentTab === 'createTeam' || currentTab === 'editTeam') && (
            <div className="mt-6 bg-gray-800 rounded-lg shadow-md border border-gray-700 p-6">
              <h2 className="text-xl font-semibold mb-4">
                {isEditingTeam ? 'Edit Team' : 'Create New Team'}
              </h2>
              
              <div className="space-y-4">
                {/* Team Basic Info */}
                <div>
                  <label htmlFor="teamName" className="block text-sm font-medium text-gray-300 mb-1">
                    Team Name*
                  </label>
                  <input
                    type="text"
                    id="teamName"
                    value={teamName}
                    onChange={(e) => setTeamName(e.target.value)}
                    className="w-full px-3 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter team name"
                    disabled={loading}
                  />
                </div>
                
                <div>
                  <label htmlFor="teamArea" className="block text-sm font-medium text-gray-300 mb-1">
                    Assigned Area*
                  </label>
                  <select
                    id="teamArea"
                    value={selectedArea}
                    onChange={(e) => setSelectedArea(e.target.value)}
                    className="w-full px-3 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={loading}
                  >
                    <option value="">Select an area</option>
                    {areas.map(area => (
                      <option key={area.id} value={area.id}>{area.name}</option>
                    ))}
                  </select>
                </div>
                
                {/* Team Members Selection */}
                <div className="mt-6">
                  <h3 className="text-lg font-medium text-gray-200 mb-2">Team Members</h3>
                  
                  <div className="mb-4">
                    <label htmlFor="searchUsers" className="block text-sm font-medium text-gray-300 mb-1">
                      Search Users
                    </label>
                    <input
                      type="text"
                      id="searchUsers"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full px-3 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Search users by name or email"
                      disabled={loading}
                    />
                  </div>
                  
                  <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 mb-6">
                    <div className="w-full md:w-1/2 bg-gray-750 rounded-lg border border-gray-700 p-3">
                      <h4 className="text-sm font-medium text-gray-300 mb-2">Available Users</h4>
                      {loading ? (
                        <div className="flex justify-center items-center py-8">
                          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                        </div>
                      ) : (
                        <div className="max-h-64 overflow-y-auto">
                          {filteredUsers.length > 0 ? (
                            <ul className="space-y-2">
                              {filteredUsers.map(user => (
                                <li 
                                  key={user.id}
                                  className={`flex items-center justify-between p-2 rounded ${
                                    isUserSelected(user.id) ? 'bg-blue-900 bg-opacity-50' : 'bg-gray-700'
                                  }`}
                                >
                                  <div className="w-full">
                                    <div className="flex justify-between items-center">
                                      <p className="font-medium text-gray-200">{user.name}</p>
                                      <button
                                        onClick={() => toggleUserSelection(user)}
                                        className={`text-sm px-2 py-1 rounded ${
                                          isUserSelected(user.id) 
                                            ? 'bg-blue-700 text-white' 
                                            : 'bg-gray-600 text-gray-200 hover:bg-gray-500'
                                        }`}
                                        disabled={loading}
                                      >
                                        {isUserSelected(user.id) ? 'Selected' : 'Select'}
                                      </button>
                                    </div>
                                    <p className="text-xs text-gray-400">{user.email}</p>
                                    
                                    {/* Display user tags */}
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {/* Role tag */}
                                      <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium
                                        ${user.role === 'admin' ? 'bg-purple-900 text-purple-200' : 
                                          user.role === 'tow' ? 'bg-yellow-800 text-yellow-200' : 
                                          'bg-blue-900 text-blue-200'}`}>
                                        {user.role}
                                      </span>
                                      
                                      {/* Display custom user tags */}
                                      {user.tags && user.tags.length > 0 && (
                                        user.tags.map((tag, tagIndex) => (
                                          <span
                                            key={tagIndex}
                                            className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium"
                                            style={{
                                              backgroundColor: tag.color,
                                              color: getTextColor(tag.color)
                                            }}
                                          >
                                            {tag.name}
                                          </span>
                                        ))
                                      )}
                                    </div>
                                  </div>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm text-gray-400 text-center py-4">No users found</p>
                          )}
                        </div>
                      )}
                    </div>
                    
                    <div className="w-full md:w-1/2 bg-gray-750 rounded-lg border border-gray-700 p-3">
                      <h4 className="text-sm font-medium text-gray-300 mb-2">Selected Users ({selectedUsers.length})</h4>
                      <div className="max-h-64 overflow-y-auto">
                        {selectedUsers.length > 0 ? (
                          <ul className="space-y-2">
                            {selectedUsers.map(user => (
                              <li 
                                key={user.id}
                                className="flex items-center justify-between p-2 rounded bg-blue-800"
                              >
                                <div className="w-full">
                                  <div className="flex justify-between items-center">
                                    <p className="font-medium text-white">{user.name}</p>
                                    <button
                                      onClick={() => {
                                        if (window.confirm(`Are you sure you want to remove ${user.name} from the selected users?`)) {
                                          toggleUserSelection(user);
                                        }
                                      }}
                                      className="text-sm bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded"
                                      disabled={loading}
                                    >
                                      Remove
                                    </button>
                                  </div>
                                  <p className="text-xs text-blue-200">{user.email}</p>
                                  
                                  {/* Display user tags */}
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {/* Role tag */}
                                    <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium
                                      ${user.role === 'admin' ? 'bg-purple-800 text-purple-100' : 
                                        user.role === 'tow' ? 'bg-yellow-700 text-yellow-100' : 
                                        'bg-blue-700 text-blue-100'}`}>
                                      {user.role}
                                    </span>
                                    
                                    {/* Display custom user tags */}
                                    {user.tags && user.tags.length > 0 && (
                                      user.tags.map((tag, tagIndex) => (
                                        <span
                                          key={tagIndex}
                                          className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium"
                                          style={{
                                            backgroundColor: tag.color,
                                            color: getTextColor(tag.color)
                                          }}
                                        >
                                          {tag.name}
                                        </span>
                                      ))
                                    )}
                                  </div>
                                </div>
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-gray-400 text-center py-4">No users selected</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={() => {
                      // Add confirmation before canceling if there are changes
                      if (teamName || selectedArea || selectedUsers.length > 0) {
                        if (window.confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                          resetForm();
                        }
                      } else {
                        resetForm();
                      }
                    }}
                    className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md shadow-md"
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={isEditingTeam ? handleUpdateTeam : handleCreateTeam}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md shadow-md"
                    disabled={loading}
                  >
                    {loading ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </span>
                    ) : (
                      isEditingTeam ? 'Update Team' : 'Create Team'
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Team;