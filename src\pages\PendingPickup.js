import React, { useState, useEffect } from 'react';

const PendingPickup = ({
  pendingPickups = [],
  teamVehicles = [],
  currentLocation = { lat: 0, lng: 0 },
  formatDistance,
  geocodeLocation,
  handleStartNavigation,
  showDetailsOnly,
  markLocationAsPickedUp,
  isTowTruckUser = false,
  geocodingLocationId = null,
  closestPendingLocation = null,
  calculateDistance,
  sectionHeight = 300
}) => {
  // State to track if user is viewing all pickups or just the closest
  const [viewAllPickups, setViewAllPickups] = useState(false);
  // State for expanded/collapsed view
  const [isExpanded, setIsExpanded] = useState(true);

  // Convert team vehicles to pending pickup format
  const teamVehiclePickups = teamVehicles.map(vehicle => {
    // Handle position data - team vehicles might have lat/lng directly on the object
    let position = null;
    if (vehicle.position) {
      position = vehicle.position;
    } else if (vehicle.lat !== undefined && vehicle.lng !== undefined && 
               vehicle.lat !== null && vehicle.lng !== null) {
      position = { lat: vehicle.lat, lng: vehicle.lng };
    }

    return {
      id: `team-vehicle-${vehicle.uniqueKey || vehicle.id}`,
      name: vehicle.vehicle,
      vehicle: vehicle.vehicle,
      vin: vehicle.vin,
      vinVerified: vehicle.vinVerified,
      plateNumber: vehicle.plateNumber,
      accountNumber: vehicle.accountNumber,
      financier: vehicle.financier,
      address: vehicle.address,
      position: position,
      status: 'pending-pickup',
      isTeamVehicle: true,
      teamMemberName: vehicle.teamMemberName,
      teamMemberId: vehicle.teamMemberId,
      notes: vehicle.notes,
      images: vehicle.images,
      bottomStatus: vehicle.bottomStatus,
      bottomStatusCount: vehicle.bottomStatusCount,
      inRouteDriverId: vehicle.inRouteDriverId,
      inRouteDriverName: vehicle.inRouteDriverName,
      inRouteTimestamp: vehicle.inRouteTimestamp,
      arrivedDriverId: vehicle.arrivedDriverId,
      arrivedDriverName: vehicle.arrivedDriverName,
      arrivedTimestamp: vehicle.arrivedTimestamp,
      createdAt: vehicle.createdAt,
      weekRange: vehicle.weekRange,
      date: vehicle.date,
      isOwnVehicle: vehicle.isOwnVehicle,
      // Parse make/model/year from vehicle name if possible
      make: vehicle.make || vehicle.vehicle?.split(' ')[1] || '',
      model: vehicle.model || vehicle.vehicle?.split(' ').slice(2).join(' ') || '',
      year: vehicle.year || vehicle.vehicle?.split(' ')[0] || ''
    };
  });

  // Debug logging to see what we're getting
  console.log('PendingPickup - teamVehicles received:', teamVehicles);
  console.log('PendingPickup - teamVehiclePickups converted:', teamVehiclePickups);
  console.log('PendingPickup - pendingPickups:', pendingPickups);

  // Combine regular pending pickups with team vehicles
  const allPickups = [...pendingPickups, ...teamVehiclePickups];
  console.log('PendingPickup - allPickups combined:', allPickups);

  // Check if location has valid GPS coordinates
  const hasValidCoordinates = (location) => {
    return location?.position && 
           typeof location.position.lat === 'number' && 
           typeof location.position.lng === 'number' &&
           !isNaN(location.position.lat) &&
           !isNaN(location.position.lng);
  };

  // Sort by priority: team vehicles with drivers first, then by distance
  const sortedPickups = [...allPickups].sort((a, b) => {
    // Team vehicles with drivers get priority
    const aHasDriver = a.inRouteDriverId || a.arrivedDriverId;
    const bHasDriver = b.inRouteDriverId || b.arrivedDriverId;
    
    if (aHasDriver && !bHasDriver) return -1;
    if (!aHasDriver && bHasDriver) return 1;
    
    // If current location is available and both have valid positions, sort by distance
    if (currentLocation && currentLocation.lat !== 0 && hasValidCoordinates(a) && hasValidCoordinates(b)) {
      const distA = calculateDistance(currentLocation, a.position);
      const distB = calculateDistance(currentLocation, b.position);
      return distA - distB;
    }
    
    // Put items without valid coordinates at the end
    if (hasValidCoordinates(a) && !hasValidCoordinates(b)) return -1;
    if (!hasValidCoordinates(a) && hasValidCoordinates(b)) return 1;
    
    // If neither has valid coordinates, maintain original order
    return 0;
  });

  // Update closest location to include team vehicles
  const closestLocation = sortedPickups.length > 0 && hasValidCoordinates(sortedPickups[0]) ? sortedPickups[0] : 
                          sortedPickups.length > 0 ? sortedPickups[0] : null;

  // Format distance for display
  const formatDistanceLocal = (distance) => {
    if (typeof formatDistance === 'function') {
      return formatDistance(distance);
    }
    
    if (distance < 0.1) {
      return `${Math.round(distance * 5280)} ft`;
    } else {
      return `${distance.toFixed(1)} mi`;
    }
  };

  // Get appropriate status label and style
  const getStatusStyle = (status) => {
    let displayStatus = status;
    
    if (status === 'pending') {
      displayStatus = 'open-order';
    }
    
    switch (displayStatus) {
      case 'picked-up':
        return { label: 'Picked Up', bg: 'bg-green-600', text: 'text-white' };
      case 'pending-pickup':
        return { label: 'Pending Pickup', bg: 'bg-yellow-600', text: 'text-white' };
      case 'awaiting-pickup':
        return { label: 'Awaiting Pickup', bg: 'bg-orange-600', text: 'text-white' };
      case 'open-order':
        return { label: 'Open Order', bg: 'bg-blue-600', text: 'text-white' };
      case 'completed':
        return { label: 'Completed', bg: 'bg-gray-600', text: 'text-white' };
      default:
        return { label: status, bg: 'bg-gray-600', text: 'text-white' };
    }
  };

  // Get image URL for a vehicle
  const getVehicleImageUrl = (item) => {
    // For team vehicles, check if they have images
    if (item.isTeamVehicle && item.images && item.images.length > 0) {
      const firstImage = item.images[0];
      return typeof firstImage === 'string' ? firstImage : firstImage.url;
    }
    
    // Generate image URL from make/model/year
    const make = encodeURIComponent(item.location?.make || item.make || '');
    const model = encodeURIComponent(item.location?.model || item.model || '');
    const year = item.location?.year || item.year || '';
    
    if (make && model) {
      return `https://cdn.imagin.studio/getimage?customer=img&make=${make}&modelFamily=${model}&year=${year}&angle=1`;
    }
    
    // Default car image as fallback
    return `data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="60" viewBox="0 0 100 60"><rect width="100" height="60" fill="%231A2642" /><path d="M20 40 L30 30 L70 30 L80 40 L80 45 L75 45 A5 5 0 0 1 65 45 L35 45 A5 5 0 0 1 25 45 L20 45 Z" fill="%23374151"/><path d="M30 30 L35 20 L65 20 L70 30" fill="%234B5563"/><circle cx="30" cy="45" r="5" fill="%231F2937"/><circle cx="70" cy="45" r="5" fill="%231F2937"/></svg>`;
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    try {
      const date = typeof dateString === 'object' && dateString.toDate 
        ? dateString.toDate() 
        : new Date(dateString);
      
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (error) {
      console.error("Error formatting date:", error);
      return 'Invalid date';
    }
  };

  // Format timestamp for team vehicles
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 60) {
      return `${diffMins} min ago`;
    } else if (diffMins < 1440) {
      return `${Math.floor(diffMins / 60)} hr ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Calculate ETA for team vehicles
  const calculateETA = (inRouteTimestamp) => {
    if (!inRouteTimestamp) return null;
    const inRouteTime = inRouteTimestamp.toDate ? inRouteTimestamp.toDate() : new Date(inRouteTimestamp);
    const eta = new Date(inRouteTime.getTime() + 15 * 60 * 1000); // 15 minutes
    return eta;
  };

  const getTimeToETA = (eta) => {
    if (!eta) return null;
    const now = new Date();
    const remaining = eta - now;
    if (remaining <= 0) return 'Arriving Now';
    const minutes = Math.ceil(remaining / 60000);
    return `${minutes} min`;
  };

  // Toggle between viewing all pickups and just the closest
  const toggleViewAll = () => {
    setViewAllPickups(!viewAllPickups);
  };

  // Total count including team vehicles
  const totalPickups = sortedPickups.length;
  const teamVehicleCount = teamVehiclePickups.length;
  const regularPickupCount = pendingPickups.length;

  return (
    <div className="w-full">
      <div className="p-2 flex justify-between items-center bg-gray-800 w-full rounded-t">
        <div className="flex items-center">
          <span className="text-xs font-medium text-gray-300">PENDING PICKUPS</span>
          {teamVehicleCount > 0 && (
            <span className="ml-2 bg-purple-600 text-white text-xs px-2 py-0.5 rounded-full">
              {teamVehicleCount} team
            </span>
          )}
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-2 text-gray-400 hover:text-white"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-4 w-4 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
        <span className="text-xs text-gray-400">
          {regularPickupCount} regular, {teamVehicleCount} team
        </span>
      </div>
      
      {isExpanded && (
        <div 
          className="overflow-y-auto w-full bg-gray-900 rounded-b"
          style={{ maxHeight: `${sectionHeight}px` }}
        >
          {sortedPickups.length > 0 ? (
            <div className="p-2 w-full">
              {/* Show the closest pickup (could be team vehicle or regular) */}
              {closestLocation && !viewAllPickups && (
                <div className={`${closestLocation.isTeamVehicle ? 'bg-purple-900 bg-opacity-30 border border-purple-600' : 'bg-gray-700'} p-3 rounded-lg mb-3`}>
                  <div className="flex justify-between items-start">
                    <div 
                      className="flex items-start space-x-3 cursor-pointer flex-1"
                      onClick={() => showDetailsOnly(closestLocation)}
                    >
                      {/* Car thumbnail */}
                      <div className="w-16 h-16 rounded-md overflow-hidden flex-shrink-0 bg-gray-800 border border-gray-700">
                        <img
                          src={getVehicleImageUrl(closestLocation)}
                          alt={closestLocation.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = getVehicleImageUrl({});
                          }}
                        />
                      </div>
                      
                      <div className="flex-1">
                        <div className="font-medium text-sm">
                          Closest: {closestLocation.name}
                          {closestLocation.isTeamVehicle && (
                            <span className="ml-2 text-xs bg-purple-600 text-white px-2 py-0.5 rounded">
                              Team Vehicle
                            </span>
                          )}
                        </div>
                        
                        {closestLocation.isTeamVehicle ? (
                          <>
                            <div className="text-xs text-gray-400 mt-1">
                              From: {closestLocation.teamMemberName} • VIN: {closestLocation.vin}
                              {closestLocation.vinVerified && (
                                <span className="ml-1 text-green-400">✓ Verified</span>
                              )}
                            </div>
                            {closestLocation.plateNumber && (
                              <div className="text-xs text-gray-400">
                                Plate: {closestLocation.plateNumber}
                              </div>
                            )}
                            {closestLocation.bottomStatus && (
                              <div className="text-xs text-red-400 mt-1">
                                Status: {closestLocation.bottomStatus} ({closestLocation.bottomStatusCount || 1}/3)
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-xs text-gray-400 mt-1">
                            {closestLocation.year} {closestLocation.make} {closestLocation.model}
                            {closestLocation.plateNumber && ` • ${closestLocation.plateNumber}`}
                          </div>
                        )}
                        
                        <div className="mt-1 text-sm text-blue-400">
                          {hasValidCoordinates(closestLocation) 
                            ? `${formatDistanceLocal(calculateDistance(currentLocation, closestLocation.position))} away`
                            : 'Distance unknown - No GPS'
                          }
                        </div>
                        
                        {/* Live tracking status for team vehicles */}
                        {closestLocation.isTeamVehicle && (closestLocation.inRouteDriverId || closestLocation.arrivedDriverId) && (
                          <div className={`mt-2 p-2 rounded text-xs ${
                            closestLocation.arrivedDriverId ? 'bg-green-900 bg-opacity-40 text-green-300' :
                            'bg-blue-900 bg-opacity-40 text-blue-300'
                          }`}>
                            {closestLocation.arrivedDriverId ? (
                              <>
                                📍 {closestLocation.arrivedDriverName} has arrived
                                {closestLocation.arrivedTimestamp && (
                                  <span className="ml-1">({formatTimestamp(closestLocation.arrivedTimestamp)})</span>
                                )}
                              </>
                            ) : (
                              <>
                                🚚 {closestLocation.inRouteDriverName} is in route
                                {closestLocation.inRouteTimestamp && (
                                  <span className="ml-1">
                                    (ETA: {getTimeToETA(calculateETA(closestLocation.inRouteTimestamp))})
                                  </span>
                                )}
                              </>
                            )}
                          </div>
                        )}
                        
                        {/* GPS indicator */}
                        <div className="flex items-center mt-1">
                          <span className={`w-2 h-2 rounded-full mr-1 ${hasValidCoordinates(closestLocation) ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                          <span className="text-xs text-gray-400">
                            {hasValidCoordinates(closestLocation) ? 'GPS Available' : 'No GPS'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2 mt-2">
                    {!hasValidCoordinates(closestLocation) && (
                      <button 
                        className={`bg-yellow-600 hover:bg-yellow-700 text-white text-xs px-3 py-1 rounded min-w-[80px] flex items-center justify-center ${geocodingLocationId === closestLocation.id ? 'opacity-70 cursor-wait' : ''}`}
                        onClick={() => geocodeLocation(closestLocation)}
                        disabled={geocodingLocationId === closestLocation.id}
                      >
                        {geocodingLocationId === closestLocation.id ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Geocoding
                          </>
                        ) : (
                          <>Geocode</>
                        )}
                      </button>
                    )}
                    
                    <button 
                      className={`bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded min-w-[80px] ${!hasValidCoordinates(closestLocation) ? 'opacity-60' : ''}`}
                      onClick={() => handleStartNavigation(closestLocation)}
                      disabled={!hasValidCoordinates(closestLocation)}
                    >
                      Navigate
                    </button>
                    
                    {/* Only tow truck users can pick up vehicles */}
                    {isTowTruckUser && !closestLocation.isTeamVehicle && (
                      <button 
                        className="bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-1 rounded min-w-[80px]"
                        onClick={() => markLocationAsPickedUp(closestLocation)}
                      >
                        Pick Up
                      </button>
                    )}
                    
                    <button
                      className="bg-gray-600 hover:bg-gray-500 text-white text-xs px-3 py-1 rounded min-w-[80px]"
                      onClick={() => showDetailsOnly(closestLocation)}
                    >
                      Details
                    </button>
                  </div>
                </div>
              )}

              <div className="mt-2 mb-2 flex justify-between items-center">
                <h4 className="text-xs font-medium text-gray-300">
                  {viewAllPickups ? "All Pending Pickups" : "Other Pending Pickups"}
                </h4>
                <button
                  className="text-xs text-blue-400 hover:text-blue-300 flex items-center"
                  onClick={toggleViewAll}
                >
                  {viewAllPickups ? (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M5 8l5 5 5-5H5z" />
                      </svg>
                      Show Closest
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M5 8l5-5 5 5H5z" />
                      </svg>
                      View All ({totalPickups})
                    </>
                  )}
                </button>
              </div>

              <div className="space-y-1 overflow-y-auto">
                {sortedPickups
                  .filter(loc => viewAllPickups || loc !== closestLocation)
                  .map(loc => {
                    const hasCoordinates = hasValidCoordinates(loc);
                    
                    return (
                      <div 
                        key={loc.id} 
                        className={`flex justify-between items-start p-2 border-b text-xs hover:bg-gray-800 rounded ${
                          loc.isTeamVehicle ? 'border-purple-700 bg-purple-900 bg-opacity-20' : 'border-gray-700'
                        }`}
                      >
                        <div 
                          className="flex items-start space-x-2 min-w-0 flex-1 cursor-pointer" 
                          onClick={() => showDetailsOnly(loc)}
                        >
                          {/* Car thumbnail */}
                          <div className="w-10 h-10 rounded-md overflow-hidden flex-shrink-0 bg-gray-800 border border-gray-700">
                            <img
                              src={getVehicleImageUrl(loc)}
                              alt={loc.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.target.onerror = null;
                                e.target.src = getVehicleImageUrl({});
                              }}
                            />
                          </div>
                          
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center">
                              <div className="truncate font-medium text-sm">{loc.name}</div>
                              {loc.isTeamVehicle && (
                                <span className="ml-2 bg-purple-600 text-white text-xs px-1.5 py-0.5 rounded">
                                  Team
                                </span>
                              )}
                              {loc.bottomStatus && (
                                <span className="ml-1 bg-red-600 text-white text-xs px-1.5 py-0.5 rounded">
                                  {loc.bottomStatus}
                                </span>
                              )}
                            </div>
                            
                            {loc.isTeamVehicle ? (
                              <>
                                <div className="text-gray-400 text-xs truncate mt-1">
                                  From: {loc.teamMemberName} • VIN: {loc.vin}
                                  {loc.vinVerified && (
                                    <span className="ml-1 text-green-400">✓</span>
                                  )}
                                </div>
                                {(loc.inRouteDriverId || loc.arrivedDriverId) && (
                                  <div className="text-xs mt-1">
                                    {loc.arrivedDriverId ? (
                                      <span className="text-green-400">📍 {loc.arrivedDriverName} arrived</span>
                                    ) : (
                                      <span className="text-blue-400">🚚 {loc.inRouteDriverName} in route</span>
                                    )}
                                  </div>
                                )}
                              </>
                            ) : (
                              <div className="text-gray-400 text-xs truncate mt-1">
                                {loc.year} {loc.make} {loc.model}
                                {loc.plateNumber && ` • ${loc.plateNumber}`}
                              </div>
                            )}
                            
                            {/* GPS indicator */}
                            <div className="flex items-center mt-1">
                              <span className={`w-2 h-2 rounded-full mr-1 ${hasCoordinates ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                              <span className="text-gray-400">
                                {hasCoordinates ? 'GPS' : 'No GPS'}
                              </span>
                            </div>
                            
                            {loc.createdAt && (
                              <div className="text-gray-500 text-xs mt-0.5">
                                Added: {formatDate(loc.createdAt)}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 flex-shrink-0 ml-2">
                          <span className="text-gray-400 whitespace-nowrap">
                            {hasCoordinates 
                              ? formatDistanceLocal(calculateDistance(currentLocation, loc.position))
                              : 'No GPS'
                            }
                          </span>
                          <div className="flex flex-col space-y-1">
                            {!hasCoordinates && (
                              <button 
                                className={`text-xs bg-yellow-600 hover:bg-yellow-700 text-white px-2 py-1 rounded whitespace-nowrap min-w-[70px] text-center flex items-center justify-center ${geocodingLocationId === loc.id ? 'opacity-70 cursor-wait' : ''}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  geocodeLocation(loc);
                                }}
                                disabled={geocodingLocationId === loc.id}
                              >
                                {geocodingLocationId === loc.id ? (
                                  <>
                                    <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Geocoding
                                  </>
                                ) : (
                                  <>Geocode</>
                                )}
                              </button>
                            )}
                            
                            <button 
                              className={`text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded whitespace-nowrap min-w-[70px] text-center ${!hasCoordinates ? 'opacity-60' : ''}`}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStartNavigation(loc);
                              }}
                              disabled={!hasCoordinates}
                            >
                              Navigate
                            </button>
                            
                            {/* Only show for tow truck users and not team vehicles */}
                            {isTowTruckUser && !loc.isTeamVehicle && (
                              <button 
                                className="text-xs bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded whitespace-nowrap min-w-[70px] text-center"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  markLocationAsPickedUp(loc);
                                }}
                              >
                                Pick Up
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          ) : (
            <div className="p-4 text-center">
              <div className="text-gray-400">No pending pickups available.</div>
              {/* Temporary debug display to diagnose the issue */}
              <div className="mt-4 text-left text-xs bg-gray-800 p-2 rounded">
                <div className="text-yellow-400 font-bold mb-2">Debug Info:</div>
                <div className="text-gray-300">
                  <div>Team Vehicles Received: {teamVehicles.length}</div>
                  <div>Regular Pickups: {pendingPickups.length}</div>
                  <div>Team Vehicle Pickups Converted: {teamVehiclePickups.length}</div>
                  <div>All Pickups Combined: {allPickups.length}</div>
                  <div>Sorted Pickups: {sortedPickups.length}</div>
                  {teamVehicles.length > 0 && (
                    <div className="mt-2">
                      <div className="text-yellow-400">First Team Vehicle Raw Data:</div>
                      <pre className="text-xs overflow-auto max-h-40 bg-gray-900 p-1 rounded mt-1">
                        {JSON.stringify(teamVehicles[0], null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Custom CSS for component */}
      <style>
      {`
        .truncate {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        /* Touch-friendly interactive elements */
        button {
          min-height: 30px;
          touch-action: manipulation;
        }
        
        @media (max-width: 767px) {
          button {
            min-height: 36px;
          }
        }
      `}
      </style>
    </div>
  );
};

export default PendingPickup;