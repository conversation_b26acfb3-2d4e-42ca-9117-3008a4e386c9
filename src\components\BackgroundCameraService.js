import React, { useState, useEffect, useRef } from 'react';
import { doc, setDoc, updateDoc, serverTimestamp, collection, getDocs, query, where } from 'firebase/firestore';
import { useAuth } from '../contexts/AuthContext';
// FIXED: Import db directly from your firebase.js
import { db } from '../pages/firebase.js';

/**
 * BackgroundCameraService
 * 
 * Background service that maintains camera connections while integrating with your AuthContext.
 * Handles the Periodic Sync permission error gracefully and uses alternative methods.
 */
const BackgroundCameraService = () => {
  const { currentUser, isAdmin } = useAuth(); // Use your existing AuthContext
  const [serviceId, setServiceId] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const heartbeatIntervalRef = useRef(null);
  const visibilityTimeoutRef = useRef(null);
  const broadcastChannelRef = useRef(null);
  
  useEffect(() => {
    if (!currentUser) return;
    
    // Generate a unique ID for this service instance
    const uniqueId = `bg-service-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    setServiceId(uniqueId);
    
    // Check if we have a persisted server session
    const persistedServer = localStorage.getItem('cameraServerSessionId');
    
    // Register the service in Firestore
    const registerService = async () => {
      try {
        const serviceRef = doc(db, 'backgroundServices', uniqueId);
        await setDoc(serviceRef, {
          userId: currentUser.uid,
          type: 'cameraService',
          status: 'active',
          startedAt: serverTimestamp(),
          lastHeartbeat: serverTimestamp(),
          persistedServer: persistedServer || null,
          userAgent: navigator.userAgent,
          isAdmin: isAdmin, // Include admin status for better tracking
          platform: navigator.platform || 'unknown',
          browser: getBrowserInfo(),
          timestamp: new Date().toISOString()
        });
        
        console.log('🔵 Background camera service registered:', uniqueId);
        setIsRunning(true);
        
        // Set up heartbeat to keep service active
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
        }
        
        heartbeatIntervalRef.current = setInterval(async () => {
          try {
            await updateDoc(serviceRef, {
              lastHeartbeat: serverTimestamp(),
              status: 'active'
            });
          } catch (error) {
            console.error('🔵 Heartbeat error:', error);
          }
        }, 30000);
        
        // Setup page visibility listeners for more aggressive heartbeats when hidden
        const handleVisibilityChange = () => {
          if (document.visibilityState === 'hidden') {
            console.log('🔵 Page hidden - using more aggressive heartbeat strategy');
            // When page is hidden, try to send one last heartbeat before potential termination
            if (visibilityTimeoutRef.current) {
              clearTimeout(visibilityTimeoutRef.current);
            }
            
            // Schedule multiple heartbeats when page is hidden
            const sendHiddenHeartbeat = async () => {
              try {
                await updateDoc(serviceRef, {
                  lastHeartbeat: serverTimestamp(),
                  status: 'active',
                  visibilityState: 'hidden'
                });
                
                // Use sendBeacon as a backup
                try {
                  const formData = new FormData();
                  formData.append('serviceId', uniqueId);
                  formData.append('userId', currentUser.uid);
                  formData.append('action', 'hidden_heartbeat');
                  
                  navigator.sendBeacon('/api/camera-service-heartbeat', formData);
                } catch (e) {
                  console.log('🔵 Beacon not supported:', e);
                }
                
                // Schedule next hidden heartbeat
                visibilityTimeoutRef.current = setTimeout(sendHiddenHeartbeat, 10000);
              } catch (error) {
                console.error('🔵 Hidden heartbeat error:', error);
              }
            };
            
            // Start hidden heartbeats
            sendHiddenHeartbeat();
            
            // Also create a ping for any camera pages before we go to background
            if (broadcastChannelRef.current) {
              broadcastChannelRef.current.postMessage({
                type: 'PAGE_HIDDEN',
                serviceId: uniqueId,
                timestamp: Date.now()
              });
            }
          } else {
            console.log('🔵 Page visible - returning to normal heartbeat pattern');
            // When page becomes visible again, clear the aggressive timeout
            if (visibilityTimeoutRef.current) {
              clearTimeout(visibilityTimeoutRef.current);
              visibilityTimeoutRef.current = null;
            }
            
            // Update visibility state
            updateDoc(serviceRef, {
              lastHeartbeat: serverTimestamp(),
              status: 'active',
              visibilityState: 'visible'
            }).catch(error => {
              console.error('🔵 Visibility update error:', error);
            });
            
            // Notify any camera pages that we're back
            if (broadcastChannelRef.current) {
              broadcastChannelRef.current.postMessage({
                type: 'PAGE_VISIBLE',
                serviceId: uniqueId,
                timestamp: Date.now()
              });
            }
          }
        };
        
        document.addEventListener('visibilitychange', handleVisibilityChange);
        
        // Check for camera sessions to keep alive
        const checkCameraSessions = async () => {
          try {
            // Look for any active camera sessions by this user
            const q = query(
              collection(db, 'clientSessions'),
              where('userId', '==', currentUser.uid),
              where('status', '==', 'active')
            );
            
            const querySnapshot = await getDocs(q);
            if (!querySnapshot.empty) {
              console.log(`🔵 Found ${querySnapshot.size} active camera sessions to monitor`);
            }
          } catch (error) {
            console.error('🔵 Error checking camera sessions:', error);
          }
        };
        
        // Check active sessions every 5 minutes
        const sessionCheckInterval = setInterval(checkCameraSessions, 5 * 60 * 1000);
        checkCameraSessions(); // Initial check
        
        // Create a ping channel to coordinate with any opened camera tabs
        try {
          const broadcastChannel = new BroadcastChannel('camera-system-channel');
          broadcastChannelRef.current = broadcastChannel;
          
          // Send periodic pings to keep any camera pages alive
          const pingInterval = setInterval(() => {
            broadcastChannel.postMessage({
              type: 'PING_REQUEST',
              serviceId: uniqueId,
              timestamp: Date.now()
            });
          }, 60000); // Every minute
          
          // Listen for responses
          broadcastChannel.onmessage = (event) => {
            if (event.data && event.data.type === 'PING_RESPONSE') {
              console.log('🔵 Received ping response from camera page:', event.data.clientId);
            } else if (event.data && event.data.type === 'SESSION_REGISTERED') {
              console.log('🔵 New camera session registered:', event.data.clientId);
            }
          };
          
          return () => {
            clearInterval(heartbeatIntervalRef.current);
            clearInterval(pingInterval);
            clearInterval(sessionCheckInterval);
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            if (visibilityTimeoutRef.current) {
              clearTimeout(visibilityTimeoutRef.current);
            }
            if (broadcastChannel) {
              broadcastChannel.close();
              broadcastChannelRef.current = null;
            }
          };
        } catch (e) {
          console.log('🔵 Broadcast Channel not supported, using fallback:', e);
          
          // Fallback: just the interval and visibility
          return () => {
            clearInterval(heartbeatIntervalRef.current);
            clearInterval(sessionCheckInterval);
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            if (visibilityTimeoutRef.current) {
              clearTimeout(visibilityTimeoutRef.current);
            }
          };
        }
      } catch (error) {
        console.error('🔵 Error registering background service:', error);
      }
    };
    
    registerService();
    
    // Register with service worker if available
    if ('serviceWorker' in navigator) {
      try {
        navigator.serviceWorker.register('/camera-service-worker.js')
          .then(registration => {
            console.log('🔵 Camera service worker registered by background service:', registration);
            
            // Notify service worker about our session
            if (navigator.serviceWorker.controller) {
              navigator.serviceWorker.controller.postMessage({
                type: 'BACKGROUND_SERVICE_STARTED',
                serviceId: uniqueId,
                userId: currentUser.uid,
                timestamp: Date.now()
              });
            }
            
            // Only attempt periodic sync if conditions are met
            const attemptPeriodicSync = async () => {
              try {
                // Check if periodic sync is supported
                if (!('periodicSync' in registration)) {
                  console.log('🔵 Periodic sync not supported by this browser');
                  return;
                }

                // Check if we're on HTTPS (required for periodic sync)
                if (!window.location.protocol.startsWith('https') && window.location.hostname !== 'localhost') {
                  console.log('🔵 Periodic sync requires HTTPS');
                  return;
                }

                // Check if web app is installed as PWA
                const isPWA = window.matchMedia('(display-mode: standalone)').matches || 
                              window.navigator.standalone || 
                              document.referrer.includes('android-app://');
                              
                if (!isPWA) {
                  console.log('🔵 Periodic sync works best when installed as a PWA');
                  // Still try, but lower expectations
                }

                // Check if permissions are likely to be granted
                if ('permissions' in navigator) {
                  try {
                    const permission = await navigator.permissions.query({ 
                      name: 'periodic-background-sync' 
                    }).catch(e => {
                      console.log('🔵 Permission query not supported:', e.message);
                      return { state: 'unknown' };
                    });
                    
                    if (permission.state === 'denied') {
                      console.log('🔵 Periodic sync permission denied - this is normal');
                      return;
                    }
                  } catch (e) {
                    console.log('🔵 Permission query not supported:', e.message);
                    // Continue anyway
                  }
                }

                // Attempt to register periodic sync with increased min interval
                await registration.periodicSync.register('camera-keepalive', {
                  minInterval: 60 * 60 * 1000 // 1 hour - more likely to be allowed than 1 minute
                });
                
                console.log('✅ Periodic sync registered successfully');
                
              } catch (err) {
                // Handle specific error types gracefully
                if (err.name === 'NotAllowedError') {
                  console.log('🔵 Periodic sync not allowed - this is normal and the app will work fine without it');
                } else if (err.name === 'NotSupportedError') {
                  console.log('🔵 Periodic sync not supported by this browser');
                } else {
                  console.log('🔵 Periodic sync registration failed:', err.message);
                }
                
                // IMPORTANT: This console message replaces the error in the console
                console.log('ℹ️ Using alternative background mechanisms instead of periodic sync');
              }
            };

            // Setup a very conservative attempt at periodic sync
            // Wait longer before trying to avoid errors during page load
            setTimeout(() => {
              if (document.hasFocus() || document.visibilityState === 'visible') {
                attemptPeriodicSync();
              } else {
                // Listen for user interaction before trying
                const handleInteraction = () => {
                  attemptPeriodicSync();
                  document.removeEventListener('click', handleInteraction);
                  document.removeEventListener('keydown', handleInteraction);
                };
                document.addEventListener('click', handleInteraction, { once: true });
                document.addEventListener('keydown', handleInteraction, { once: true });
              }
            }, 10000); // Wait 10 seconds before trying
            
            // Set up listener for service worker messages
            navigator.serviceWorker.addEventListener('message', (event) => {
              if (event.data && event.data.type === 'PING_FROM_SERVICE_WORKER') {
                console.log('🔵 Received ping from service worker');
                
                // Send a heartbeat when pinged by service worker
                try {
                  const serviceRef = doc(db, 'backgroundServices', uniqueId);
                  updateDoc(serviceRef, {
                    lastHeartbeat: serverTimestamp(),
                    status: 'active',
                    lastServiceWorkerPing: new Date().toISOString()
                  }).catch(error => {
                    console.error('🔵 Error updating from service worker ping:', error);
                  });
                } catch (e) {
                  console.error('🔵 Error processing service worker ping:', e);
                }
                
                // Respond to service worker
                if (navigator.serviceWorker.controller) {
                  navigator.serviceWorker.controller.postMessage({
                    type: 'PONG_TO_SERVICE_WORKER',
                    serviceId: uniqueId,
                    timestamp: Date.now()
                  });
                }
              }
            });
          })
          .catch(error => {
            console.log('🔵 Service worker registration failed - this is not critical:', error.message);
          });
      } catch (error) {
        console.log('🔵 Service worker not available - this is not critical:', error.message);
      }
    } else {
      console.log('🔵 Service workers not supported by this browser');
    }
    
    // Setup beforeunload to try to keep service alive
    const handleBeforeUnload = () => {
      // Try to send a final update before page closes
      try {
        const formData = new FormData();
        formData.append('serviceId', uniqueId);
        formData.append('userId', currentUser.uid);
        formData.append('action', 'page_closing');
        
        navigator.sendBeacon('/api/camera-service-close', formData);
      } catch (e) {
        console.error('🔵 Error sending beacon:', e);
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
      if (visibilityTimeoutRef.current) {
        clearTimeout(visibilityTimeoutRef.current);
      }
      console.log('🔵 Background service component unmounting - attempting to continue service');
      
      // Try to update status on unmount
      const serviceRef = doc(db, 'backgroundServices', uniqueId);
      updateDoc(serviceRef, {
        lastHeartbeat: serverTimestamp(),
        status: 'background',
        unmounted: true
      }).catch(error => {
        console.error('🔵 Error updating service on unmount:', error);
      });
    };
  }, [currentUser, isAdmin]);
  
  // Helper function to get browser info
  const getBrowserInfo = () => {
    const userAgent = navigator.userAgent;
    let browserName = "Unknown";
    
    if (userAgent.match(/chrome|chromium|crios/i)) {
      browserName = "Chrome";
    } else if (userAgent.match(/firefox|fxios/i)) {
      browserName = "Firefox";
    } else if (userAgent.match(/safari/i)) {
      browserName = "Safari";
    } else if (userAgent.match(/opr\//i)) {
      browserName = "Opera";
    } else if (userAgent.match(/edg/i)) {
      browserName = "Edge";
    }
    
    return browserName;
  };
  
  // The service doesn't render anything visible
  return null;
};

export default BackgroundCameraService;