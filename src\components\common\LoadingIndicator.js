import React from 'react';

/**
 * Reusable loading indicator component with different types and sizes
 */
const LoadingIndicator = ({
  // Display options
  type = 'spinner', // 'spinner', 'pulse', 'dots', 'overlay'
  size = 'medium', // 'small', 'medium', 'large'
  fullScreen = false, // Whether to cover the full screen
  
  // Text options
  text = 'Loading...',
  showText = true,
  
  // Color options (for custom styling)
  color = null // Custom color override
}) => {
  // Size mappings
  const sizeMap = {
    small: {
      spinner: 'h-4 w-4 border-2',
      container: 'text-xs',
      spacing: 'gap-2'
    },
    medium: {
      spinner: 'h-8 w-8 border-2',
      container: 'text-sm',
      spacing: 'gap-3'
    },
    large: {
      spinner: 'h-12 w-12 border-3',
      container: 'text-base',
      spacing: 'gap-4'
    }
  };
  
  // Get size classes
  const sizeClasses = sizeMap[size] || sizeMap.medium;
  
  // Base container classes
  let containerClasses = `flex items-center justify-center ${sizeClasses.spacing}`;
  
  // Classes for full-screen overlay
  if (fullScreen || type === 'overlay') {
    containerClasses = `fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 ${sizeClasses.spacing}`;
  }
  
  // Render different loading indicator types
  const renderLoadingIndicator = () => {
    switch (type) {
      case 'spinner':
        return (
          <div 
            className={`animate-spin rounded-full border-t-transparent ${sizeClasses.spinner}`}
            style={{ 
              borderColor: color ? `${color} transparent transparent transparent` : 'currentColor transparent transparent transparent'
            }}
            role="status"
            aria-label="Loading"
          ></div>
        );
        
      case 'pulse':
        return (
          <div className="flex space-x-2">
            {[0, 1, 2].map(i => (
              <div 
                key={i}
                className={`animate-pulse rounded-full ${size === 'small' ? 'h-2 w-2' : size === 'large' ? 'h-4 w-4' : 'h-3 w-3'}`}
                style={{ 
                  backgroundColor: color || 'currentColor',
                  animationDelay: `${i * 0.15}s`
                }}
              ></div>
            ))}
          </div>
        );
        
      case 'dots':
        return (
          <div className="flex space-x-1 items-center">
            <span>Loading</span>
            {[0, 1, 2].map(i => (
              <span 
                key={i} 
                className="animate-bounce"
                style={{ 
                  animationDelay: `${i * 0.15}s`,
                  display: 'inline-block'
                }}
              >.</span>
            ))}
          </div>
        );
        
      case 'overlay':
        return (
          <>
            <div 
              className={`animate-spin rounded-full border-t-transparent ${sizeClasses.spinner}`}
              style={{ 
                borderColor: color ? `${color} transparent transparent transparent` : '#60A5FA transparent transparent transparent'
              }}
            ></div>
            {showText && <p className="text-white">{text}</p>}
          </>
        );
        
      default:
        return (
          <div 
            className={`animate-spin rounded-full border-t-transparent ${sizeClasses.spinner}`}
            style={{ borderColor: color ? `${color} transparent transparent transparent` : 'currentColor transparent transparent transparent' }}
          ></div>
        );
    }
  };
  
  return (
    <div className={containerClasses}>
      {renderLoadingIndicator()}
      {showText && type !== 'dots' && type !== 'overlay' && <p className={sizeClasses.container}>{text}</p>}
    </div>
  );
};

export default LoadingIndicator;