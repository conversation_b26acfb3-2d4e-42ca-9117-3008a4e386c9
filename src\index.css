@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles for full visibility of all content */
html,
body,
#root {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Fix for iOS Safari viewport height issues */
@supports (-webkit-touch-callout: none) {

    html,
    body,
    #root {
        height: -webkit-fill-available;
    }

    /* This makes the vh unit work correctly on iOS */
    :root {
        --vh: 1vh;
    }
}

/* Settings page specific styles */
.settings-wrapper {
    min-height: 100vh;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #111827;
}

@supports (-webkit-touch-callout: none) {
    .settings-wrapper {
        min-height: -webkit-fill-available;
    }
}

/* Ensure all scrollable areas have momentum scrolling on iOS */
.overflow-y-auto,
.overflow-auto,
.settings-wrapper {
    -webkit-overflow-scrolling: touch;
}

/* Fix for iPad Safari issues */
.ipad body,
.ipad #root,
.ipad .settings-wrapper {
    height: 100%;
    position: relative;
    overflow: auto;
}

/* Landscape adjustments for iPad */
@media (orientation: landscape) and (max-height: 834px) {
    .ipad .settings-container {
        padding-top: 0.5rem;
        padding-bottom: 1.5rem;
    }

    .ipad .grid {
        grid-gap: 0.75rem;
    }
}

/* Portrait adjustments for iPad */
@media (orientation: portrait) and (min-width: 768px) and (max-width: 1024px) {
    .ipad .settings-container {
        max-width: 90vw;
    }
}

/* Prevent zoom on input fields on iOS */
input,
select,
textarea {
    font-size: 16px !important;
}

/* Fix for Chrome/Safari padding issues */
* {
    box-sizing: border-box;
}

/* Better touch targets for mobile */
button,
input[type="checkbox"],
.toggle,
a {
    touch-action: manipulation;
}

/* Improve scrollbar appearance */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1f2937;
}

::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Main content area layout (from your existing CSS) */
.main-content-area {
    display: flex;
    width: 100%;
    height: calc(100vh - 130px);
    overflow: hidden;
    position: relative;
}

/* Panel sizing for different screen sizes */
.panel-container {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    flex-shrink: 0;
    background: linear-gradient(to bottom, #1f2937, #111827);
    border-color: #374151;
    transition: width 0.3s ease;
}

/* Map container */
.map-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    height: 100%;
}

.map-container {
    width: 100%;
    flex: 1;
    position: relative;
    overflow: hidden;
}

/* Chat container */
.chat-container {
    width: 100%;
    height: auto;
    max-height: 30vh;
    min-height: 200px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Add these styles to your existing CSS or in a new <style> tag in the head */
.shadow-glow-green {
    box-shadow: 0 0 5px 2px rgba(74, 222, 128, 0.6);
}

/* Animation for signal activity */
@keyframes pulse-green {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.animate-pulse-green {
    animation: pulse-green 2s infinite;
}

/* Fix for checkboxes - restore native appearance after Tailwind base reset */
input[type="checkbox"] {
    appearance: auto !important;
    -webkit-appearance: checkbox !important;
    -moz-appearance: checkbox !important;
    accent-color: #10b981 !important;
    /* green-500 */
    width: 1.25rem !important;
    height: 1.25rem !important;
}

/* Alternative fix for custom styled checkboxes */
input[type="checkbox"]:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e") !important;
    background-size: 100% 100% !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

/* Ensure checkboxes work on dark backgrounds */
input[type="checkbox"]:not(:checked) {
    background-color: #374151 !important;
    /* gray-700 */
    border-color: #6b7280 !important;
    /* gray-500 */
}

input[type="checkbox"]:checked {
    background-color: #10b981 !important;
    /* green-500 */
    border-color: #10b981 !important;
    /* green-500 */
}

/* ===================================== */
/* UPDATED: MUCH LARGER OVERLAY FEATURES */
/* ===================================== */

/* Features overlay modal background */
.features-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* Features selector modal - LARGER */
.features-selector {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    border: 2px solid #4f46e5;
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Features grid layout - LARGER CARDS */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-top: 25px;
}

/* Individual feature cards - MUCH LARGER */
.feature-card {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    border-radius: 20px;
    padding: 30px 25px;
    border: 3px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 160px;
}

.feature-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px -5px rgba(59, 130, 246, 0.5);
    border-color: #3b82f6;
}

.feature-card.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-color: #60a5fa;
    box-shadow: 0 15px 35px -5px rgba(59, 130, 246, 0.7);
}

.feature-card.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.feature-icon {
    font-size: 3.5rem;
    margin-bottom: 15px;
    display: block;
}

.feature-title {
    font-weight: bold;
    color: white;
    margin-bottom: 8px;
    font-size: 1.3rem;
}

.feature-description {
    font-size: 1rem;
    color: #d1d5db;
    line-height: 1.5;
}

.feature-card.active .feature-description {
    color: #e0e7ff;
}

/* MASSIVELY UPDATED: Much larger overlay panels */
.overlay-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(17, 24, 39, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 2px solid rgba(75, 85, 99, 0.5);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    z-index: 500;
    width: 95vw;
    height: 92vh;
    max-width: 1400px;
    max-height: none;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
    pointer-events: none;
}

.overlay-panel.visible {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    pointer-events: all;
}

.overlay-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 2px solid rgba(75, 85, 99, 0.3);
    background: rgba(55, 65, 81, 0.7);
    border-radius: 18px 18px 0 0;
    flex-shrink: 0;
}

.overlay-panel-title {
    font-weight: bold;
    color: white;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 12px;
}

.overlay-panel-close {
    background: rgba(239, 68, 68, 0.8);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
    transition: all 0.2s ease;
}

.overlay-panel-close:hover {
    background: rgba(239, 68, 68, 1);
    transform: scale(1.1);
}

.overlay-panel-content {
    padding: 30px;
    overflow-y: auto;
    height: calc(100% - 90px);
}

/* Specific panel positioning - REMOVED since we're using centered approach */

/* Mobile responsive adjustments - MUCH LARGER ON MOBILE */
@media (max-width: 768px) {
    .overlay-panel {
        width: 98vw;
        height: 95vh;
        max-width: none;
        max-height: none;
        border-radius: 15px;
    }

    .overlay-panel-header {
        padding: 20px 25px;
        border-radius: 13px 13px 0 0;
    }

    .overlay-panel-title {
        font-size: 1.3rem;
    }

    .overlay-panel-content {
        padding: 25px;
        height: calc(100% - 80px);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .feature-card {
        padding: 25px 20px;
        min-height: 140px;
    }

    .feature-icon {
        font-size: 3rem;
    }

    .feature-title {
        font-size: 1.2rem;
    }

    .feature-description {
        font-size: 0.9rem;
    }

    .features-selector {
        margin: 15px;
        padding: 30px 25px;
        max-height: 95vh;
        max-width: none;
    }
}

/* Tablet adjustments - LARGER */
@media (min-width: 769px) and (max-width: 1024px) {
    .overlay-panel {
        width: 92vw;
        height: 88vh;
        max-width: 1200px;
    }

    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Large screen adjustments - MAXIMUM SIZE */
@media (min-width: 1400px) {
    .overlay-panel {
        width: 90vw;
        height: 90vh;
        max-width: 1600px;
    }
}

/* UPDATED: Chat specific styles - LARGER */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: rgba(31, 41, 55, 0.5);
    border-radius: 12px;
    margin-bottom: 20px;
    max-height: none;
}

.chat-message {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 12px;
    background: rgba(55, 65, 81, 0.3);
}

.chat-message.own {
    background: rgba(59, 130, 246, 0.2);
    margin-left: 30px;
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #4B5563;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.chat-content {
    flex: 1;
}

.chat-sender {
    font-weight: bold;
    color: #60A5FA;
    font-size: 1rem;
    margin-bottom: 4px;
}

.chat-text {
    color: #E5E7EB;
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 6px;
}

.chat-timestamp {
    color: #9CA3AF;
    font-size: 0.85rem;
}

.chat-input-container {
    display: flex;
    gap: 15px;
    align-items: end;
}

.chat-input {
    flex: 1;
    background: rgba(31, 41, 55, 0.8);
    border: 2px solid #4B5563;
    border-radius: 12px;
    padding: 15px 20px;
    color: white;
    resize: none;
    min-height: 50px;
    max-height: 120px;
    font-size: 1rem;
}

.chat-input:focus {
    outline: none;
    border-color: #60A5FA;
}

.chat-send-btn {
    background: #3B82F6;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-send-btn:hover {
    background: #2563EB;
}

.chat-send-btn:disabled {
    background: #4B5563;
    cursor: not-allowed;
}

/* UPDATED: Trails specific styles - LARGER */
.trails-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.trails-controls {
    background: rgba(31, 41, 55, 0.5);
    border-radius: 12px;
    padding: 20px;
}

.trails-stats {
    background: rgba(31, 41, 55, 0.5);
    border-radius: 12px;
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.trails-time-selector {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.trails-time-btn {
    background: rgba(55, 65, 81, 0.8);
    border: 2px solid #4B5563;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    font-weight: 500;
}

.trails-time-btn:hover {
    background: rgba(75, 85, 99, 0.8);
}

.trails-time-btn.active {
    background: #3B82F6;
    border-color: #60A5FA;
}

.trails-member-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.trails-member-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: rgba(55, 65, 81, 0.3);
    border-radius: 10px;
}

.trails-member-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.trails-member-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid white;
}

.trails-member-name {
    color: white;
    font-weight: 500;
    font-size: 1rem;
}

.trails-member-status {
    font-size: 0.85rem;
    color: #9CA3AF;
}

.trails-member-actions {
    display: flex;
    gap: 8px;
}

.trails-action-btn {
    background: transparent;
    border: 2px solid;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.2s ease;
    font-weight: 500;
}

.trails-toggle-btn {
    border-color: #10B981;
    color: #10B981;
    padding: 12px 20px;
    font-size: 1rem;
}

.trails-toggle-btn:hover {
    background: #10B981;
    color: white;
}

.trails-delete-btn {
    border-color: #EF4444;
    color: #EF4444;
}

.trails-delete-btn:hover {
    background: #EF4444;
    color: white;
}

/* Smooth scrolling for panels */
.overlay-panel {
    scroll-behavior: smooth;
}

.overlay-panel::-webkit-scrollbar {
    width: 8px;
}

.overlay-panel::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
    border-radius: 4px;
}

.overlay-panel::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.8);
    border-radius: 4px;
}

.overlay-panel::-webkit-scrollbar-thumb:hover {
    background: rgba(107, 114, 128, 0.9);
}

/* Prevent body scroll when overlays are open */
body.overlay-open {
    overflow: hidden;
}

/* Enhanced animations for feature cards */
.feature-card {
    position: relative;
}

.feature-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.feature-card:hover::after {
    transform: translateX(100%);
}

/* Additional utility classes for overlays */
.overlay-fade-in {
    animation: overlayFadeIn 0.3s ease;
}

.overlay-fade-out {
    animation: overlayFadeOut 0.3s ease;
}

@keyframes overlayFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes overlayFadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }

    to {
        opacity: 0;
        transform: scale(0.95);
    }
}

/* UPDATED: Vehicle Detail Card Styles - MUCH LARGER */
.vehicle-detail-card {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    border: 3px solid #4f46e5;
    max-width: 900px;
    width: 92vw;
    max-height: 90vh;
    overflow-y: auto;
    z-index: 600;
}

.vehicle-detail-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 500;
}

@media (max-width: 768px) {
    .vehicle-detail-card {
        width: 96vw;
        padding: 30px 25px;
        max-height: 92vh;
    }
}