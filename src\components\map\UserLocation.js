import React, { useContext, useEffect, useRef, useState } from 'react';
import { doc, updateDoc, getDoc, setDoc, serverTimestamp, collection, addDoc } from 'firebase/firestore';
import L from 'leaflet';
// In all components
import { MapContext } from '../MapContext';
import VehicleInspectionModal from './VehicleInspectionModal';
import { playNotificationSound } from '../../utils/mapUtils';

const UserLocation = () => {
  // Get context from Map component
  const {
    mapRef,
    firestoreRef,
    currentUser,
    currentLocation,
    setCurrentLocation,
    isClockedIn,
    setIsClockedIn,
    clockInTime,
    setClockInTime,
    totalMilesCovered,
    setTotalMilesCovered,
    debouncedUpdateUserLocation,
    getCurrentPosition,
    userProfilePictures,
    userDisplayNames,
    homeLocation,
    lastKnownPositionRef,
    setError
  } = useContext(MapContext);

  // Local refs for this component
  const currentMarkerRef = useRef(null);
  const userLabelRef = useRef(null);
  const locationWatchId = useRef(null);
  const breadcrumbPathRef = useRef(null);
  const hasMovedSinceClockInRef = useRef(false);

  // Local state
  const [showVehicleInspection, setShowVehicleInspection] = useState(false);
  const [isStartInspection, setIsStartInspection] = useState(false);
  const [locationsCreated, setLocationsCreated] = useState(0);
  const [locationsPickedUp, setLocationsPickedUp] = useState(0);
  const [routeTrace, setRouteTrace] = useState([]);

  // Update current marker when location changes
  useEffect(() => {
    if (!mapRef.current || !currentLocation) return;
    
    updateCurrentPositionOnMap(currentLocation, false);
    
    // For clocked in users, update trace
    if (isClockedIn) {
      updateRouteTrace(currentLocation);
    }
    
    // Update Firestore when user moves - use debounce to prevent too many writes
    if (currentUser) {
      debouncedUpdateUserLocation(currentLocation);
    }
  }, [currentLocation, currentUser, isClockedIn]);

  // Clean up location watch and marker on unmount
  useEffect(() => {
    return () => {
      // Clear location watch
      if (locationWatchId.current !== null && navigator.geolocation) {
        try {
          navigator.geolocation.clearWatch(locationWatchId.current);
        } catch (err) {
          console.log("Error clearing location watch on unmount:", err);
        }
        locationWatchId.current = null;
      }
      
      // Clean up marker
      if (currentMarkerRef.current && mapRef.current) {
        try {
          mapRef.current.removeLayer(currentMarkerRef.current);
          currentMarkerRef.current = null;
        } catch (err) {
          console.warn("Error removing user marker on unmount:", err);
        }
      }
      
      // Clean up label
      if (userLabelRef.current && mapRef.current) {
        try {
          mapRef.current.removeLayer(userLabelRef.current);
          userLabelRef.current = null;
        } catch (err) {
          console.warn("Error removing user label on unmount:", err);
        }
      }
      
      // Clean up breadcrumb path
      if (breadcrumbPathRef.current && mapRef.current) {
        try {
          mapRef.current.removeLayer(breadcrumbPathRef.current);
          breadcrumbPathRef.current = null;
        } catch (err) {
          console.warn("Error removing breadcrumb path on unmount:", err);
        }
      }
    };
  }, []);

  /**
   * Updates the current marker position without refreshing the map view
   * @param {Object} position - The new position with lat/lng properties
   * @param {Boolean} centerMap - Whether to center the map on the new position
   */
  const updateCurrentPositionOnMap = async (position, centerMap = false) => {
    // Verify we have a valid position
    if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {
      console.error("Invalid position:", position);
      return;
    }
    
    // Update last known position
    lastKnownPositionRef.current = position;
    
    if (!mapRef.current) {
      console.warn("Map reference is not available, cannot update marker position");
      return;
    }
    
    try {
      // Get user profile picture
      let profilePicUrl = null;
      let displayName = null;
      
      if (currentUser) {
        if (userProfilePictures[currentUser.uid]) {
          profilePicUrl = userProfilePictures[currentUser.uid];
        }
        
        // Get display name
        if (userDisplayNames[currentUser.uid]) {
          displayName = userDisplayNames[currentUser.uid];
        } else {
          displayName = currentUser.email || 'Me';
        }
      }
      
      // Update marker position if it exists
      if (currentMarkerRef.current) {
        try {
          currentMarkerRef.current.setLatLng([position.lat, position.lng]);
        } catch (markerErr) {
          console.warn("Error updating marker position, recreating marker:", markerErr);
          currentMarkerRef.current = null; // Reset so we can recreate it
        }
      }
      
      // If marker doesn't exist or failed to update, recreate it
      if (!currentMarkerRef.current) {
        try {
          // Create a new marker with profile picture if available
          const userIcon = L.divIcon({
            className: 'current-location-marker',
            html: profilePicUrl 
              ? `<div class="user-profile-marker" style="background-image: url('${profilePicUrl}');"></div>`
              : `<div class="current-location-pin"></div>`,
            iconSize: [40, 40],
            iconAnchor: [20, 20]
          });
          
          currentMarkerRef.current = L.marker([position.lat, position.lng], {
            icon: userIcon,
            title: displayName || 'You',
            zIndexOffset: 1000
          }).addTo(mapRef.current);
        } catch (createErr) {
          console.error("Failed to create marker:", createErr);
        }
      }
      
      // Update or create user label
      if (displayName) {
        if (userLabelRef.current) {
          try {
            userLabelRef.current.setLatLng([position.lat, position.lng]);
          } catch (labelErr) {
            console.warn("Error updating label position, recreating label:", labelErr);
            userLabelRef.current = null; // Reset so we can recreate it
          }
        }
        
        if (!userLabelRef.current && mapRef.current) {
          try {
            // Create a label for the current user
            userLabelRef.current = L.marker([position.lat, position.lng], {
              icon: L.divIcon({
                className: 'user-label',
                html: `<div class="user-label-content current-user">${displayName}</div>`,
                iconSize: [100, 20],
                iconAnchor: [50, 0] // Position it at the top of the marker
              }),
              zIndexOffset: 1001
            }).addTo(mapRef.current);
          } catch (createLabelErr) {
            console.error("Failed to create user label:", createLabelErr);
          }
        }
      }
      
      // Center map if requested
      if (centerMap && mapRef.current) {
        try {
          mapRef.current.setView([position.lat, position.lng], mapRef.current.getZoom());
        } catch (viewErr) {
          console.warn("Error centering map:", viewErr);
        }
      }
    } catch (err) {
      console.error("Error updating marker position:", err);
    }
  };

  // Helper function to calculate distance between two points in miles
  const calculateDistance = (point1, point2) => {
    const R = 3958.8; // Earth's radius in MILES
    const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
    const φ2 = point2.lat * Math.PI/180;
    const Δφ = (point2.lat-point1.lat) * Math.PI/180;
    const Δλ = (point2.lng-point1.lng) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    const d = R * c; // in MILES
    return d;
  };

  // Update route trace for clocked-in users
  const updateRouteTrace = (newPoint) => {
    setRouteTrace(prevTrace => {
      // If this is the first point or we've moved enough distance
      if (prevTrace.length === 0) {
        return [newPoint]; // Add first point
      }
      
      try {
        // Check if we've moved enough to add another point
        const lastPoint = prevTrace[prevTrace.length - 1];
        const distance = calculateDistance(lastPoint, newPoint);
        
        if (distance > 0.006) { // ~30 feet (0.006 miles)
          hasMovedSinceClockInRef.current = true;
          
          // Update total miles covered
          setTotalMilesCovered(prev => prev + distance);
          
          // Update breadcrumb path on the map
          updateBreadcrumbPath([...prevTrace, newPoint]);
          
          // Update trace in Firestore
          updateTraceInFirestore([...prevTrace, newPoint]);
          
          return [...prevTrace, newPoint];
        }
      } catch (err) {
        console.log("Error processing location update:", err);
      }
      
      return prevTrace;
    });
  };

  // Update breadcrumb path on map
  const updateBreadcrumbPath = (points) => {
    if (!mapRef.current || points.length < 2) return;
    
    try {
      // Remove existing path
      if (breadcrumbPathRef.current) {
        mapRef.current.removeLayer(breadcrumbPathRef.current);
      }
      
      // Create new path
      const pathCoordinates = points.map(point => [point.lat, point.lng]);
      breadcrumbPathRef.current = L.polyline(pathCoordinates, {
        color: '#3B82F6',
        weight: 5,
        opacity: 0.8
      }).addTo(mapRef.current);
    } catch (err) {
      console.error("Error updating breadcrumb path:", err);
    }
  };

  // Update trace in Firestore
  const updateTraceInFirestore = async (trace) => {
    if (!currentUser || !firestoreRef.current) return;
    
    try {
      const userTraceRef = doc(firestoreRef.current, 'userTraces', currentUser.uid);
      
      const userTraceDoc = await getDoc(userTraceRef);
      
      if (userTraceDoc.exists()) {
        // Update existing trace
        await updateDoc(userTraceRef, {
          trace: trace.map(point => ({
            lat: point.lat,
            lng: point.lng,
            timestamp: serverTimestamp()
          })),
          uid: currentUser.uid
        });
      } else {
        // Create new trace
        await setDoc(userTraceRef, {
          uid: currentUser.uid,
          trace: trace.map(point => ({
            lat: point.lat,
            lng: point.lng,
            timestamp: serverTimestamp()
          }))
        });
      }
    } catch (err) {
      console.error("Error updating user trace in Firestore:", err);
    }
  };

  // Watch user position for clocked in users
  const watchUserPosition = () => {
    if (!navigator.geolocation) {
      setError("Geolocation is not supported by your browser.");
      return;
    }
    
    try {
      // Start watching position with improved error handling
      const watchId = navigator.geolocation.watchPosition(
        (position) => {
          const newPoint = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          
          // Save as last known position
          lastKnownPositionRef.current = newPoint;
          
          // Update current location state
          setCurrentLocation(newPoint);
        },
        (error) => {
          console.error("Error watching position:", error);
          let errorMessage = "Lost track of your location. Please try again.";
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = "Location access denied. Please enable location services.";
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = "Your position is unavailable. Try again later.";
              break;
            case error.TIMEOUT:
              errorMessage = "Location update timed out. Please try again.";
              break;
          }
          setError(errorMessage);
        },
        {
          enableHighAccuracy: true,
          maximumAge: 30000,
          timeout: 10000
        }
      );
      
      locationWatchId.current = watchId;
    } catch (err) {
      console.log("Error setting up location watching:", err);
      setError("Could not track your location. Please refresh and try again.");
    }
  };

  // Clear user trail
  const clearTrail = async () => {
    if (!currentUser || !firestoreRef.current) return;
    
    try {
      // Remove the trail from the map
      if (breadcrumbPathRef.current && mapRef.current) {
        mapRef.current.removeLayer(breadcrumbPathRef.current);
        breadcrumbPathRef.current = null;
      }
      
      // Remove from Firestore
      const userTraceRef = doc(firestoreRef.current, 'userTraces', currentUser.uid);
      await updateDoc(userTraceRef, {
        trace: []
      });
      
      console.log(`Cleared breadcrumb trail for current user`);
      
      // Update local state
      setRouteTrace([]);
      hasMovedSinceClockInRef.current = false;
      setTotalMilesCovered(0);
      
      // Play notification sound
      playNotificationSound();
    } catch (err) {
      console.error("Error clearing user trail:", err);
      setError("Failed to clear trail. Please try again.");
    }
  };

  // Check if user is close enough to home location for clock in/out
  const isNearHomeLocation = () => {
    try {
      if (!homeLocation || !currentLocation) return false;
      
      const distance = calculateDistance(currentLocation, homeLocation.position);
      const MAX_DISTANCE = 1000 / 5280; // 1000 feet in miles
      
      return distance <= MAX_DISTANCE;
    } catch (err) {
      console.error("Error checking proximity to home:", err);
      return false;
    }
  };

  // Toggle clock in/out status with vehicle inspection
  const toggleClockStatus = () => {
    // Check if near Home location for clock in/out
    if (!isNearHomeLocation()) {
      setError(`You must be within 1000 feet of the Home location to clock ${isClockedIn ? 'out' : 'in'}.`);
      return;
    }
    
    // Show vehicle inspection form
    setShowVehicleInspection(true);
    setIsStartInspection(!isClockedIn);
  };

  // Complete vehicle inspection and proceed with clock in/out
  const completeVehicleInspection = async (inspectionImages) => {
    try {
      if (!currentUser || !firestoreRef.current) {
        setError("User must be logged in to complete inspection.");
        return;
      }
      
      // Create inspection record
      const inspectionData = {
        userId: currentUser.uid,
        userName: userDisplayNames[currentUser.uid] || currentUser.email,
        isStartInspection: isStartInspection,
        timestamp: serverTimestamp(),
        location: currentLocation,
        images: inspectionImages
      };
      
      // Save inspection to Firestore
      await addDoc(collection(firestoreRef.current, 'vehicleInspections'), inspectionData);
      
      // Close the inspection modal
      setShowVehicleInspection(false);
      
      // Now proceed with actual clock in/out operation
      if (isStartInspection) {
        // Clock in
        setIsClockedIn(true);
        const now = new Date();
        setClockInTime(now);
        setRouteTrace([currentLocation]); // Start with current position
        hasMovedSinceClockInRef.current = false;
        setTotalMilesCovered(0); // Reset miles counter
        setLocationsCreated(0); // Reset locations counter
        setLocationsPickedUp(0); // Reset pickup counter
        
        // Make sure the user marker is visible when clocking in
        updateCurrentPositionOnMap(currentLocation, true);
        
        // Update user status in Firestore
        if (currentUser && firestoreRef.current) {
          try {
            const userStatusRef = doc(firestoreRef.current, 'userStatus', currentUser.uid);
            setDoc(userStatusRef, {
              clockedIn: true,
              clockInTime: serverTimestamp(),
              lastPosition: {
                lat: currentLocation.lat,
                lng: currentLocation.lng
              }
            }, { merge: true });
          } catch (err) {
            console.error("Error updating clock in status:", err);
          }
        }
        
        // Start watching location
        watchUserPosition();
      } else {
        // Clock out
        if (locationWatchId.current !== null && navigator.geolocation) {
          try {
            navigator.geolocation.clearWatch(locationWatchId.current);
          } catch (err) {
            console.log("Error clearing location watch:", err);
          }
          locationWatchId.current = null;
        }
        
        // Update user status in Firestore
        if (currentUser && firestoreRef.current) {
          try {
            const userStatusRef = doc(firestoreRef.current, 'userStatus', currentUser.uid);
            
            // Create shift summary
            const shiftSummary = {
              clockedIn: false,
              clockOutTime: serverTimestamp(),
              lastPosition: {
                lat: currentLocation.lat,
                lng: currentLocation.lng
              },
              shiftSummary: {
                totalMilesCovered: totalMilesCovered.toFixed(2),
                locationsCreated: locationsCreated,
                locationsPickedUp: locationsPickedUp,
                shiftDuration: clockInTime ? ((new Date() - clockInTime) / (1000 * 60 * 60)).toFixed(2) + ' hours' : 'Unknown'
              }
            };
            
            await setDoc(userStatusRef, shiftSummary, { merge: true });
          } catch (err) {
            console.error("Error updating clock out status:", err);
          }
        }
        
        hasMovedSinceClockInRef.current = false;
        setIsClockedIn(false);
        setClockInTime(null);
      }
      
      // Play success sound
      playNotificationSound();
    } catch (err) {
      console.error("Error completing vehicle inspection:", err);
      setError("Failed to save inspection. Please try again.");
    }
  };

  // Render admin clear trail button if needed
  const renderAdminClearTrailButton = () => {
    if (isClockedIn && hasMovedSinceClockInRef.current) {
      return (
        <div 
          className="user-actions-button"
          onClick={clearTrail}
          title="Delete your trail"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
      );
    }
    return null;
  };

  // UI for clock in/out button
  const renderClockInOutButton = () => {
    return (
      <button
        onClick={toggleClockStatus}
        disabled={!isNearHomeLocation()}
        className={`px-2 sm:px-3 py-1 rounded flex items-center ${isClockedIn ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'} text-white text-xs sm:text-sm ${!isNearHomeLocation() ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
        </svg>
        {isClockedIn ? 'Clock Out' : 'Clock In'}
      </button>
    );
  };

  return (
    <>
      {/* Clear trail button */}
      {renderAdminClearTrailButton()}
      
      {/* Clock in/out button is rendered in ActionToolbar component */}
      
      {/* Vehicle inspection modal */}
      {showVehicleInspection && (
        <VehicleInspectionModal
          isStartInspection={isStartInspection}
          onComplete={completeVehicleInspection}
          onCancel={() => setShowVehicleInspection(false)}
        />
      )}
    </>
  );
};

export default UserLocation;