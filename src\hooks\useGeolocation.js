import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Custom hook for handling geolocation functionality
 * 
 * @param {Object} options - Geolocation options
 * @param {boolean} options.enableHighAccuracy - Whether to enable high accuracy mode
 * @param {number} options.timeout - Timeout for position requests (ms)
 * @param {number} options.maximumAge - Maximum age of cached positions (ms)
 * @param {boolean} options.watchPosition - Whether to watch for position changes
 * @returns {Object} Geolocation state and control functions
 */
const useGeolocation = ({
  enableHighAccuracy = true,
  timeout = 10000,
  maximumAge = 30000,
  watchPosition = false
} = {}) => {
  // Current position state
  const [position, setPosition] = useState(null);
  
  // Status states
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [permission, setPermission] = useState('prompt'); // 'granted', 'denied', 'prompt'
  
  // Tracking states
  const [isWatching, setIsWatching] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  
  // Refs to keep track of watch IDs
  const watchIdRef = useRef(null);
  
  // Store the geolocation options
  const geoOptions = {
    enableHighAccuracy,
    timeout,
    maximumAge
  };
  
  /**
   * Success handler for geolocation requests
   */
  const handleSuccess = useCallback((geolocationPosition) => {
    const { latitude, longitude, accuracy, altitude, altitudeAccuracy, heading, speed } = geolocationPosition.coords;
    const timestamp = geolocationPosition.timestamp;
    
    const formattedPosition = {
      lat: latitude,
      lng: longitude,
      accuracy,
      altitude,
      altitudeAccuracy,
      heading,
      speed,
      timestamp
    };
    
    setPosition(formattedPosition);
    setError(null);
    setLoading(false);
    setLastUpdated(new Date());
    
    // Set permission as granted if we got a position
    setPermission('granted');
    
    return formattedPosition;
  }, []);
  
  /**
   * Error handler for geolocation requests
   */
  const handleError = useCallback((geolocationError) => {
    setLoading(false);
    
    // Format error based on the error code
    let errorMessage = "Unknown error occurred while getting location.";
    let permissionStatus = 'prompt';
    
    switch (geolocationError.code) {
      case 1: // PERMISSION_DENIED
        errorMessage = "Location access denied. Please enable location services in your browser settings.";
        permissionStatus = 'denied';
        break;
      case 2: // POSITION_UNAVAILABLE
        errorMessage = "Your current position is unavailable. Try again later.";
        break;
      case 3: // TIMEOUT
        errorMessage = "Location request timed out. Please try again.";
        break;
    }
    
    setError({ code: geolocationError.code, message: errorMessage });
    setPermission(permissionStatus);
    
    return { code: geolocationError.code, message: errorMessage };
  }, []);
  
  /**
   * Get the current position once
   */
  const getCurrentPosition = useCallback(() => {
    if (!navigator.geolocation) {
      setError({ code: -1, message: "Geolocation is not supported by your browser." });
      return Promise.reject(new Error("Geolocation is not supported by your browser."));
    }
    
    setLoading(true);
    
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const formattedPosition = handleSuccess(position);
          resolve(formattedPosition);
        },
        (error) => {
          const formattedError = handleError(error);
          reject(formattedError);
        },
        geoOptions
      );
    });
  }, [handleSuccess, handleError, geoOptions]);
  
  /**
   * Start watching position changes
   */
  const startWatching = useCallback(() => {
    if (!navigator.geolocation) {
      setError({ code: -1, message: "Geolocation is not supported by your browser." });
      return;
    }
    
    // Clear any existing watch
    stopWatching();
    
    setLoading(true);
    setIsWatching(true);
    
    watchIdRef.current = navigator.geolocation.watchPosition(
      handleSuccess,
      handleError,
      geoOptions
    );
    
    return watchIdRef.current;
  }, [handleSuccess, handleError, geoOptions]);
  
  /**
   * Stop watching position changes
   */
  const stopWatching = useCallback(() => {
    if (watchIdRef.current !== null && navigator.geolocation) {
      navigator.geolocation.clearWatch(watchIdRef.current);
      watchIdRef.current = null;
      setIsWatching(false);
    }
  }, []);
  
  /**
   * Request permission explicitly (useful for mobile)
   */
  const requestPermission = useCallback(() => {
    return getCurrentPosition()
      .then(() => true)
      .catch(() => false);
  }, [getCurrentPosition]);
  
  // Start watching position if watchPosition is true
  useEffect(() => {
    if (watchPosition && !isWatching && permission !== 'denied') {
      startWatching();
    }
    
    return () => {
      if (isWatching) {
        stopWatching();
      }
    };
  }, [watchPosition, isWatching, permission, startWatching, stopWatching]);
  
  return {
    // Current state
    position,
    error,
    loading,
    lastUpdated,
    permission,
    isWatching,
    
    // Actions
    getCurrentPosition,
    startWatching,
    stopWatching,
    requestPermission
  };
};

export default useGeolocation;