// pages/Bot.js
import React, { useEffect, useState } from 'react';
import { FiCheckCircle, FiAlertCircle } from 'react-icons/fi';

const Bot = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [processingOAuth, setProcessingOAuth] = useState(false);

  // Get OAuth parameters from URL
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const errorParam = urlParams.get('error');
  const state = urlParams.get('state');

  // Your Slack app credentials
  const CLIENT_ID = '9100366040791.9112393886501';
  const REDIRECT_URI = 'https://recoveriqs.net/bot';
  const SCOPES = [
    'app_mentions:read',
    'channels:history',
    'channels:join',
    'channels:read',
    'chat:write',
    'commands',
    'files:write',
    'groups:history',
    'groups:read',
    'im:history',
    'im:read',
    'im:write',
    'team:read',
    'users:read',
    'users:read.email'
  ].join(',');

  useEffect(() => {
    // Handle OAuth callback
    if (code && !processingOAuth) {
      handleOAuthCallback();
    } else if (errorParam) {
      // Enhanced error handling
      if (errorParam === 'access_denied') {
        setError('Installation cancelled by user');
      } else if (errorParam === 'invalid_team_for_non_distributed_app') {
        setError(
          <div>
            <p className="mb-2">❌ This app hasn't been configured for distribution yet.</p>
            <p className="text-sm mb-2">It can only be installed to the development workspace.</p>
            <div className="bg-gray-800/50 rounded-lg p-3 mt-3">
              <p className="text-xs font-semibold mb-1">To fix this:</p>
              <ol className="text-xs space-y-1 list-decimal list-inside">
                <li>Go to api.slack.com/apps</li>
                <li>Click "Manage Distribution"</li>
                <li>Complete all requirements</li>
                <li>Enable public distribution</li>
              </ol>
            </div>
          </div>
        );
      } else {
        setError(`Error: ${errorParam}`);
      }
    }
  }, [code, errorParam]);

  const handleOAuthCallback = async () => {
    setProcessingOAuth(true);
    setLoading(true);
    
    try {
      // Try to call your backend to exchange the code
      // Update this URL to match your actual backend
      const backendUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      
      const response = await fetch(`${backendUrl}/api/slack-oauth`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: code,
          redirectUri: REDIRECT_URI
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'OAuth exchange failed');
      }

      const data = await response.json();
      
      // Clear URL parameters
      window.history.replaceState({}, document.title, '/bot');
      
      setSuccess(true);
      setLoading(false);
    } catch (err) {
      console.error('OAuth error:', err);
      
      // If backend is not available, show manual instructions
      if (err.message.includes('fetch')) {
        setError(
          <div>
            <p className="mb-2">⚠️ Backend server not running</p>
            <p className="text-sm">Make sure your slack-vehicle-bot.js is running on port 3001</p>
            <code className="block mt-2 bg-gray-800 p-2 rounded text-xs">
              node src/components/slack-vehicle-bot.js
            </code>
          </div>
        );
      } else {
        setError(err.message || 'Failed to complete installation. Please try again.');
      }
      setLoading(false);
    }
  };

  const handleAddToSlack = () => {
    // Generate state for security
    const oauthState = Math.random().toString(36).substring(7);
    sessionStorage.setItem('slack_oauth_state', oauthState);
    
    // Construct Slack OAuth URL
    const slackAuthUrl = `https://slack.com/oauth/v2/authorize?` +
      `client_id=${CLIENT_ID}&` +
      `scope=${SCOPES}&` +
      `redirect_uri=${encodeURIComponent(REDIRECT_URI)}&` +
      `state=${oauthState}`;
    
    // Redirect to Slack
    window.location.href = slackAuthUrl;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      <div className="container mx-auto px-4 py-12 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl mb-6">
            <span className="text-4xl">🚗</span>
          </div>
          <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
            Tracker Bot
          </h1>
          <p className="text-xl text-gray-300">
            Streamline your vehicle tracking workflow directly in Slack
          </p>
        </div>

        {/* Status Messages */}
        {error && (
          <div className="mb-8 p-4 bg-red-500/20 border border-red-500/50 rounded-lg">
            <div className="flex items-start">
              <FiAlertCircle className="mr-3 text-red-500 text-xl flex-shrink-0 mt-1" />
              <div className="flex-1">
                {typeof error === 'string' ? <span>{error}</span> : error}
              </div>
            </div>
          </div>
        )}

        {success && (
          <div className="mb-8 p-6 bg-green-500/20 border border-green-500/50 rounded-lg">
            <div className="flex items-center mb-4">
              <FiCheckCircle className="mr-3 text-green-500 text-2xl" />
              <span className="text-xl font-semibold">Success! Bot installed.</span>
            </div>
            <div className="space-y-2 text-gray-300">
              <p><strong>Next steps:</strong></p>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>Go to your Slack workspace</li>
                <li>Invite the bot: <code className="bg-gray-700 px-2 py-1 rounded">/invite @Vehicle Tracker</code></li>
                <li>Set up your channel: <code className="bg-gray-700 px-2 py-1 rounded">/vehicle setup link</code></li>
                <li>Start tracking: <code className="bg-gray-700 px-2 py-1 rounded">/vehicle list</code></li>
              </ol>
            </div>
          </div>
        )}

        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-300">Connecting to Slack...</p>
          </div>
        )}

        {!success && !loading && (
          <>
            {/* Features Grid */}
            <div className="grid md:grid-cols-2 gap-6 mb-12">
              <div className="bg-gray-800/50 backdrop-blur border border-gray-700 rounded-xl p-6">
                <div className="flex items-start">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <span>✅</span>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Real-time Updates</h3>
                    <p className="text-gray-400 text-sm">Get instant vehicle status notifications in your Slack channels</p>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800/50 backdrop-blur border border-gray-700 rounded-xl p-6">
                <div className="flex items-start">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <span>🚚</span>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Driver Tracking</h3>
                    <p className="text-gray-400 text-sm">Monitor driver routes and arrival times in real-time</p>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800/50 backdrop-blur border border-gray-700 rounded-xl p-6">
                <div className="flex items-start">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <span>📊</span>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Multi-team Support</h3>
                    <p className="text-gray-400 text-sm">Unified dashboard for all teams in one place</p>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800/50 backdrop-blur border border-gray-700 rounded-xl p-6">
                <div className="flex items-start">
                  <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <span>🔔</span>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Smart Reminders</h3>
                    <p className="text-gray-400 text-sm">Automated notifications for pending vehicles</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Add to Slack Button */}
            <div className="text-center mb-12">
              {/* Official Slack Button */}
              <a 
                href="https://slack.com/oauth/v2/authorize?client_id=9100366040791.9112393886501&scope=calls:write,channels:history,channels:read,chat:write,chat:write.public,commands,groups:history,im:read,im:write,reactions:write,team:read,users.profile:read,users:read,users:read.email,groups:read&user_scope="
                className="inline-block transform hover:scale-105 transition-all"
              >
                <img 
                  alt="Add to Slack" 
                  height="40" 
                  width="139" 
                  src="https://platform.slack-edge.com/img/add_to_slack.png" 
                  srcSet="https://platform.slack-edge.com/img/add_to_slack.png 1x, https://platform.slack-edge.com/img/<EMAIL> 2x" 
                />
              </a>
              
              {/* Alternative Custom Button */}
              <div className="mt-4">
                <p className="text-gray-400 text-xs mb-2">Or use custom flow:</p>
                <button
                  onClick={handleAddToSlack}
                  className="inline-flex items-center px-6 py-2 bg-gray-700 hover:bg-gray-600 text-white font-semibold rounded-lg transition-all text-sm"
                >
                  <img 
                    src="https://cdn.brandfolder.io/5H442O3W/at/pl546j-7le8zk-6gwiyo/Slack_Mark_Web.png" 
                    alt="Slack" 
                    className="w-5 h-5 mr-2"
                  />
                  Custom Install
                </button>
              </div>
              
              {errorParam === 'invalid_team_for_non_distributed_app' && (
                <p className="mt-3 text-yellow-400 text-sm animate-pulse">
                  ⚠️ App distribution needs to be enabled first (see below)
                </p>
              )}
            </div>

            {/* Instructions */}
            <div className="bg-gray-800/50 backdrop-blur border border-gray-700 rounded-xl p-6">
              <h3 className="text-lg font-semibold mb-4 text-blue-400">📋 Installation Instructions</h3>
              <ol className="space-y-3 text-gray-300">
                <li className="flex">
                  <span className="font-semibold mr-2">1.</span>
                  <span>Click "Add to Slack" above</span>
                </li>
                <li className="flex">
                  <span className="font-semibold mr-2">2.</span>
                  <span>Choose your Slack workspace</span>
                </li>
                <li className="flex">
                  <span className="font-semibold mr-2">3.</span>
                  <span>Review and authorize permissions</span>
                </li>
                <li className="flex">
                  <span className="font-semibold mr-2">4.</span>
                  <span>Start using <code className="bg-gray-700 px-2 py-1 rounded">/vehicle</code> commands</span>
                </li>
              </ol>
            </div>

            {/* Direct Install Option */}
            <div className="mt-8 bg-blue-900/20 border border-blue-600/50 rounded-xl p-6">
              <h3 className="text-lg font-semibold mb-3 text-blue-400">🚀 Quick Start for Development</h3>
              <p className="text-gray-300 mb-4">
                If you're testing in your development workspace, you can install directly:
              </p>
              <ol className="space-y-2 text-gray-300 text-sm">
                <li>1. Go to <a href="https://api.slack.com/apps/A093ABKS2ER" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">your app settings</a></li>
                <li>2. Click "Install App" → "Install to Workspace"</li>
                <li>3. Return here after installation</li>
              </ol>
            </div>
          </>
        )}

        {/* Footer */}
        <div className="text-center mt-12 text-sm text-gray-500">
          <p className="mb-2">
            By installing, you agree to our{' '}
            <a href="/privacy" className="text-blue-400 hover:underline">Privacy Policy</a>
            {' '}and{' '}
            <a href="/terms" className="text-blue-400 hover:underline">Terms of Service</a>
          </p>
          <p className="mb-4">&copy; 2024 RecoverIQ All rights reserved.</p>
          <p className="text-xs">
            Need help? Contact{' '}
            <a href="mailto:<EMAIL>" className="text-blue-400 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Bot;