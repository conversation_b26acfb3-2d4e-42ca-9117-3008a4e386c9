import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext.js';
import { 
  getFirestore, collection, query, where, orderBy, getDocs, doc, getDoc, 
  addDoc, deleteDoc, updateDoc, serverTimestamp, Timestamp, onSnapshot, limit 
} from 'firebase/firestore';
import { getStorage, ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { db } from '../pages/firebase.js';

function EmployeeDirectory() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const storage = getStorage();
  
  // State for employee directory
  const [employees, setEmployees] = useState([]);
  const [filteredEmployees, setFilteredEmployees] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [activeView, setActiveView] = useState('directory'); // 'directory', 'chat', 'profile'
  
  // State for chat
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [mediaToUpload, setMediaToUpload] = useState(null);
  const [isUploadingMedia, setIsUploadingMedia] = useState(false);
  const [selectedParticipants, setSelectedParticipants] = useState([]);
  const [showSelectParticipants, setShowSelectParticipants] = useState(false);
  const [userProfilePictures, setUserProfilePictures] = useState({});
  
  // Refs
  const chatMessagesRef = useRef(null);
  const inputRef = useRef(null);
  const unsubscribeRef = useRef(null);
  
  // Fetch all employees
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setIsLoading(true);
        
        // Fetch users from Firestore collection (same as admin panel)
        const usersCollection = collection(db, 'users');
        const userSnapshot = await getDocs(usersCollection);
        let employeesList = userSnapshot.docs.map(docSnapshot => ({
          id: docSnapshot.id,
          ...docSnapshot.data(),
          tags: [] // Initialize tags array
        }));
        
        const profilePictures = {};
        
        // Fetch user profiles to get additional data for each user
        for (let employee of employeesList) {
          const profileRef = doc(db, "userProfiles", employee.id);
          const profileDoc = await getDoc(profileRef);
          
          if (profileDoc.exists()) {
            const profileData = profileDoc.data();
            
            // Add profile data to employee
            employee.displayName = profileData.displayName || '';
            employee.jobTitle = profileData.jobTitle || '';
            employee.location = profileData.location || '';
            employee.vehicle = profileData.vehicle || '';
            employee.phoneNumber = profileData.phoneNumber || '';
            employee.tags = profileData.tags || [];
            
            // Store profile pictures for chat
            if (profileData.photoBase64) {
              profilePictures[employee.id] = profileData.photoBase64;
              employee.photoBase64 = profileData.photoBase64;
            }
          }
        }
        
        // Store profile pictures for chat component
        setUserProfilePictures(profilePictures);
        
        // Sort by display name
        employeesList.sort((a, b) => {
          const nameA = a.displayName || a.email || '';
          const nameB = b.displayName || b.email || '';
          return nameA.localeCompare(nameB);
        });
        
        setEmployees(employeesList);
        setFilteredEmployees(employeesList);
      } catch (error) {
        console.error("Error fetching employees:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchEmployees();
  }, []);
  
  // Filter employees based on search term
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredEmployees(employees);
    } else {
      const filtered = employees.filter(employee => {
        const searchLower = searchTerm.toLowerCase();
        const displayName = (employee.displayName || '').toLowerCase();
        const jobTitle = (employee.jobTitle || '').toLowerCase();
        const location = (employee.location || '').toLowerCase();
        const email = (employee.email || '').toLowerCase();
        
        return displayName.includes(searchLower) || 
               jobTitle.includes(searchLower) || 
               location.includes(searchLower) ||
               email.includes(searchLower);
      });
      setFilteredEmployees(filtered);
    }
  }, [searchTerm, employees]);
  
  // Fetch user conversations
  useEffect(() => {
    if (!currentUser) return;
    
    const fetchConversations = async () => {
      try {
        // Query conversations where the current user is a participant
        const q = query(
          collection(db, "directMessages"),
          where("participants", "array-contains", currentUser.uid),
          orderBy("lastMessageTime", "desc")
        );
        
        const querySnapshot = await getDocs(q);
        const conversationsData = [];
        
        for (const docSnapshot of querySnapshot.docs) {
          const data = docSnapshot.data();
          
          // Get participant names and photos
          const participantDetails = await Promise.all(
            data.participants
              .filter(uid => uid !== currentUser.uid)
              .map(async (uid) => {
                // Fixed: Renamed the variable to avoid naming collision with imported 'doc' function
                const userDocRef = doc(db, "userProfiles", uid);
                const userDoc = await getDoc(userDocRef);
                if (userDoc.exists()) {
                  const userData = userDoc.data();
                  return {
                    id: uid,
                    name: userData.displayName || "User",
                    photo: userData.photoBase64 || null
                  };
                }
                return { id: uid, name: "Unknown User", photo: null };
              })
          );
          
          conversationsData.push({
            id: docSnapshot.id,
            ...data,
            participantDetails: participantDetails,
            conversationName: data.name || participantDetails.map(p => p.name).join(', ')
          });
        }
        
        setConversations(conversationsData);
      } catch (error) {
        console.error("Error fetching conversations:", error);
      }
    };
    
    fetchConversations();
  }, [currentUser]);
  
  // Subscribe to messages for active conversation
  useEffect(() => {
    if (!activeConversation) {
      setChatMessages([]);
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
      return;
    }
    
    const messagesQuery = query(
      collection(db, "directMessages", activeConversation.id, "messages"),
      orderBy("timestamp", "asc")
    );
    
    const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
      const messages = snapshot.docs.map(docSnapshot => ({
        id: docSnapshot.id,
        ...docSnapshot.data()
      }));
      setChatMessages(messages);
      
      // Scroll to bottom after messages load
      setTimeout(() => {
        if (chatMessagesRef.current) {
          chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
        }
      }, 100);
    });
    
    unsubscribeRef.current = unsubscribe;
    
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [activeConversation]);
  
  // Handle employee search
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };
  
  // Handle employee selection
  const handleEmployeeClick = (employee) => {
    setSelectedEmployee(employee);
    setActiveView('profile');
  };
  
  // Start or open a direct message conversation
  const startConversation = async (employee) => {
    if (!currentUser || !employee) return;
    
    try {
      // Check if a one-on-one conversation already exists
      const isOneOnOne = !selectedParticipants.length;
      
      if (isOneOnOne) {
        const q = query(
          collection(db, "directMessages"),
          where("participants", "array-contains", currentUser.uid)
        );
        
        const querySnapshot = await getDocs(q);
        let existingConversation = null;
        
        querySnapshot.forEach(docSnapshot => {
          const data = docSnapshot.data();
          if (data.participants.length === 2 && data.participants.includes(employee.id)) {
            existingConversation = { id: docSnapshot.id, ...data };
          }
        });
        
        if (existingConversation) {
          setActiveConversation(existingConversation);
          setActiveView('chat');
          return;
        }
      }
      
      // Create new conversation
      const participants = [currentUser.uid, employee.id, ...selectedParticipants];
      
      // Get group name if multiple participants
      let conversationName = "";
      if (participants.length > 2) {
        // Fetch names for all participants excluding current user
        const participantProfiles = await Promise.all(
          participants
            .filter(uid => uid !== currentUser.uid)
            .map(async (uid) => {
              const userDocRef = doc(db, "userProfiles", uid);
              const userDoc = await getDoc(userDocRef);
              return userDoc.exists() ? 
                (userDoc.data().displayName || "User") : "Unknown User";
            })
        );
        
        conversationName = participantProfiles.join(', ');
      }
      
      const newConversationRef = await addDoc(collection(db, "directMessages"), {
        participants: participants,
        name: conversationName || null,
        createdAt: serverTimestamp(),
        lastMessageTime: serverTimestamp(),
        expirationDate: Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) // 30 days from now
      });
      
      // Get participant details
      const participantDetails = await Promise.all(
        participants
          .filter(uid => uid !== currentUser.uid)
          .map(async (uid) => {
            const userDocRef = doc(db, "userProfiles", uid);
            const userDoc = await getDoc(userDocRef);
            if (userDoc.exists()) {
              const userData = userDoc.data();
              return {
                id: uid,
                name: userData.displayName || "User",
                photo: userData.photoBase64 || null
              };
            }
            return { id: uid, name: "Unknown User", photo: null };
          })
      );
      
      const newConversation = {
        id: newConversationRef.id,
        participants: participants,
        name: conversationName || null,
        participantDetails: participantDetails,
        conversationName: conversationName || participantDetails.map(p => p.name).join(', '),
        createdAt: new Date(),
        lastMessageTime: new Date()
      };
      
      setConversations(prev => [newConversation, ...prev]);
      setActiveConversation(newConversation);
      setActiveView('chat');
      setSelectedParticipants([]);
      setShowSelectParticipants(false);
      
      // Add system message about conversation creation
      const systemMessage = {
        text: `Conversation ${participants.length > 2 ? 'group' : ''} created by ${currentUser.displayName || 'User'}`,
        sender: {
          uid: 'system',
          name: 'System',
          photo: null
        },
        timestamp: serverTimestamp(),
        isSystem: true
      };
      
      await addDoc(collection(db, "directMessages", newConversationRef.id, "messages"), systemMessage);
      
    } catch (error) {
      console.error("Error starting conversation:", error);
    }
  };
  
  // Open existing conversation
  const openConversation = (conversation) => {
    setActiveConversation(conversation);
    setActiveView('chat');
  };
  
  // Send a message
  const sendMessage = async (e) => {
    e.preventDefault();
    
    if ((!newMessage.trim() && !mediaToUpload) || !activeConversation) return;
    
    try {
      const messageData = {
        text: newMessage.trim(),
        sender: {
          uid: currentUser.uid,
          name: currentUser.displayName || "User",
          photo: null // Will be populated from user profile
        },
        timestamp: serverTimestamp(),
        expirationDate: Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) // 30 days from now
      };
      
      // Get sender photo from user profile
      try {
        const userDocRef = doc(db, "userProfiles", currentUser.uid);
        const userDoc = await getDoc(userDocRef);
        if (userDoc.exists() && userDoc.data().photoBase64) {
          messageData.sender.photo = userDoc.data().photoBase64;
        }
      } catch (error) {
        console.error("Error fetching user photo:", error);
      }
      
      // Handle media upload
      if (mediaToUpload) {
        setIsUploadingMedia(true);
        
        try {
          // Upload to Firebase Storage
          const storageRef = ref(storage, `directMessages/${activeConversation.id}/${Date.now()}_${mediaToUpload.file.name}`);
          const uploadTask = uploadBytesResumable(storageRef, mediaToUpload.file);
          
          // Wait for upload to complete
          await new Promise((resolve, reject) => {
            uploadTask.on(
              'state_changed',
              (snapshot) => {
                // Optional: Track upload progress
                const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
                console.log(`Upload is ${progress}% done`);
              },
              (error) => {
                console.error("Upload error:", error);
                reject(error);
              },
              () => {
                resolve();
              }
            );
          });
          
          // Get download URL
          const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
          
          // Add media details to message
          messageData.mediaUrl = downloadURL;
          messageData.mediaType = mediaToUpload.type;
          messageData.mediaName = mediaToUpload.file.name;
          
        } catch (error) {
          console.error("Error uploading media:", error);
          alert("Failed to upload media. Please try again.");
          setIsUploadingMedia(false);
          return;
        } finally {
          setIsUploadingMedia(false);
        }
      }
      
      // Add message to conversation
      await addDoc(
        collection(db, "directMessages", activeConversation.id, "messages"),
        messageData
      );
      
      // Update conversation last message time
      await updateDoc(doc(db, "directMessages", activeConversation.id), {
        lastMessageTime: serverTimestamp(),
        lastMessage: {
          text: newMessage.trim() || (mediaToUpload ? `Sent a ${mediaToUpload.type}` : ''),
          sender: currentUser.uid,
          timestamp: serverTimestamp()
        }
      });
      
      // Clear input
      setNewMessage('');
      setMediaToUpload(null);
      
      // Focus back on input
      if (inputRef.current) {
        inputRef.current.focus();
      }
      
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };
  
  // Delete a message
  const deleteMessage = async (messageId) => {
    if (!activeConversation) return;
    
    try {
      await deleteDoc(doc(db, "directMessages", activeConversation.id, "messages", messageId));
    } catch (error) {
      console.error("Error deleting message:", error);
    }
  };
  
  // Add participants to conversation
  const toggleParticipantSelection = (employeeId) => {
    setSelectedParticipants(prevSelected => {
      if (prevSelected.includes(employeeId)) {
        return prevSelected.filter(id => id !== employeeId);
      } else {
        return [...prevSelected, employeeId];
      }
    });
  };
  
  // Handle media selection
  const handleMediaUpload = (mediaType) => {
    const input = document.createElement('input');
    input.type = 'file';
    
    if (mediaType === 'image') {
      input.accept = 'image/*';
    } else if (mediaType === 'video') {
      input.accept = 'video/*';
    }
    
    input.onchange = (e) => {
      if (e.target.files && e.target.files[0]) {
        const file = e.target.files[0];
        
        // Check file size (10MB limit for now)
        if (file.size > 10 * 1024 * 1024) {
          alert("File is too large. Maximum size is 10MB.");
          return;
        }
        
        const reader = new FileReader();
        reader.onload = (event) => {
          setMediaToUpload({
            type: mediaType,
            file: file,
            preview: event.target.result
          });
        };
        reader.readAsDataURL(file);
      }
    };
    
    input.click();
  };
  
  // Function to determine text color based on background color (for tags)
  const getTextColor = (hexColor) => {
    if (!hexColor) return '#FFFFFF';
    
    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    
    // Calculate brightness (YIQ formula)
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    
    return yiq >= 150 ? '#000000' : '#FFFFFF';
  };

  // Render functions
  const renderDirectoryHeader = () => (
    <div className="flex items-center justify-between mb-4">
      <h2 className="text-xl font-semibold text-white">Employee Directory</h2>
      <button
        onClick={() => navigate('/dashboard')}
        className="bg-gray-700 hover:bg-gray-600 text-gray-200 px-3 py-1 rounded text-sm flex items-center"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back
      </button>
    </div>
  );
  
  const renderSearchBar = () => (
    <div className="relative mb-4">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <input
        type="text"
        className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
        placeholder="Search employees..."
        value={searchTerm}
        onChange={handleSearchChange}
      />
    </div>
  );
  
  const renderEmployeeList = () => (
    <div className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 240px)' }}>
      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredEmployees.length === 0 ? (
        <div className="text-center py-8 text-gray-400">
          {searchTerm ? 'No employees found matching your search.' : 'No employees found.'}
        </div>
      ) : (
        <div className="space-y-2">
          {filteredEmployees.map(employee => {
            // Skip current user in list
            if (employee.id === currentUser?.uid) return null;
            
            return (
              <div 
                key={employee.id} 
                className={`p-3 rounded-md border border-gray-700 hover:bg-gray-700 transition-colors duration-150 flex items-center ${
                  showSelectParticipants && selectedParticipants.includes(employee.id) ? 'bg-blue-900 border-blue-700' : 'bg-gray-800'
                }`}
              >
                {/* Profile Picture */}
                <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-600 flex-shrink-0 mr-3 border border-gray-600">
                  {employee.photoBase64 ? (
                    <img 
                      src={employee.photoBase64} 
                      alt={employee.displayName || employee.email || "User"} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-300">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  )}
                </div>
                
                {/* Employee Info */}
                <div className="flex-grow">
                  <h3 className="font-medium text-white">{employee.displayName || employee.email || "Unknown User"}</h3>
                  <div className="flex flex-wrap text-xs text-gray-400 gap-x-2">
                    {employee.jobTitle && <span>{employee.jobTitle}</span>}
                    {employee.location && <span>{employee.location}</span>}
                    
                    {/* Display role badge */}
                    {employee.role && (
                      <span className={`px-1.5 py-0.5 rounded-full text-xs ${
                        employee.role === 'admin' ? 'bg-red-900 text-red-200' : 
                        employee.role === 'tow' ? 'bg-yellow-900 text-yellow-200' : 
                        'bg-blue-900 text-blue-200'
                      }`}>
                        {employee.role}
                      </span>
                    )}
                    
                    {/* Display tags */}
                    {employee.tags && employee.tags.map((tag, tagIndex) => (
                      <span 
                        key={tagIndex}
                        className="rounded-full px-1.5 py-0.5 text-xs font-medium"
                        style={{
                          backgroundColor: tag.color,
                          color: getTextColor(tag.color)
                        }}
                      >
                        {tag.name}
                      </span>
                    ))}
                  </div>
                </div>
                
                {/* Actions */}
                <div className="flex space-x-2">
                  {showSelectParticipants ? (
                    <button
                      onClick={() => toggleParticipantSelection(employee.id)}
                      className={`p-2 rounded-full ${
                        selectedParticipants.includes(employee.id) ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300'
                      }`}
                    >
                      {selectedParticipants.includes(employee.id) ? (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      )}
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={() => handleEmployeeClick(employee)}
                        className="p-2 rounded-full bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
                        title="View Profile"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => startConversation(employee)}
                        className="p-2 rounded-full bg-blue-600 text-white hover:bg-blue-500"
                        title="Message"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </button>
                    </>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
  
  const renderRecentConversations = () => (
    <div className="mt-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-200">Recent Conversations</h3>
        <div className="flex">
          <button
            onClick={() => setShowSelectParticipants(!showSelectParticipants)}
            className="text-blue-500 hover:text-blue-400 text-sm flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            {showSelectParticipants ? 'Cancel' : 'New Group'}
          </button>
        </div>
      </div>
      
      {showSelectParticipants && selectedParticipants.length > 0 && (
        <div className="mb-4 p-3 bg-gray-700 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Selected: {selectedParticipants.length}</span>
            <button
              onClick={() => {
                // Create group chat with first employee + selected participants
                if (filteredEmployees.length > 0) {
                  // Find first non-current-user employee
                  const firstEmployee = filteredEmployees.find(e => e.id !== currentUser?.uid);
                  if (firstEmployee) {
                    startConversation(firstEmployee);
                  }
                }
              }}
              className="bg-blue-600 hover:bg-blue-500 text-white px-3 py-1 rounded text-sm"
              disabled={selectedParticipants.length === 0}
            >
              Create Group
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedParticipants.map(participantId => {
              const employee = employees.find(e => e.id === participantId);
              return employee ? (
                <div key={participantId} className="flex items-center bg-blue-800 rounded-full px-2 py-1 text-xs text-white">
                  <span>{employee.displayName || employee.email || "User"}</span>
                  <button 
                    onClick={() => toggleParticipantSelection(participantId)}
                    className="ml-1 text-blue-200 hover:text-white"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              ) : null;
            })}
          </div>
        </div>
      )}
      
      {conversations.length === 0 ? (
        <div className="text-center py-4 text-gray-400 bg-gray-800 rounded-md">
          No conversations yet. Start a new message!
        </div>
      ) : (
        <div className="space-y-2">
          {conversations.map(conversation => (
            <div 
              key={conversation.id} 
              className="p-3 rounded-md bg-gray-800 border border-gray-700 hover:bg-gray-700 transition-colors duration-150 cursor-pointer"
              onClick={() => openConversation(conversation)}
            >
              <div className="flex items-center">
                {/* Conversation Avatar */}
                {conversation.participantDetails.length > 1 ? (
                  <div className="relative w-10 h-10 flex-shrink-0 mr-3">
                    <div className="absolute top-0 left-0 w-7 h-7 rounded-full overflow-hidden bg-blue-600 border border-gray-800">
                      {conversation.participantDetails[0]?.photo ? (
                        <img 
                          src={conversation.participantDetails[0].photo} 
                          alt={conversation.participantDetails[0].name} 
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-white text-xs">
                          {conversation.participantDetails[0]?.name?.charAt(0) || "?"}
                        </div>
                      )}
                    </div>
                    <div className="absolute bottom-0 right-0 w-7 h-7 rounded-full overflow-hidden bg-green-600 border border-gray-800">
                      {conversation.participantDetails[1]?.photo ? (
                        <img 
                          src={conversation.participantDetails[1].photo} 
                          alt={conversation.participantDetails[1].name} 
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-white text-xs">
                          {conversation.participantDetails[1]?.name?.charAt(0) || "?"}
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-600 flex-shrink-0 mr-3 border border-gray-600">
                    {conversation.participantDetails[0]?.photo ? (
                      <img 
                        src={conversation.participantDetails[0].photo} 
                        alt={conversation.participantDetails[0].name} 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-300">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    )}
                  </div>
                )}
                
                {/* Conversation Info */}
                <div className="flex-grow overflow-hidden">
                  <div className="flex justify-between items-center">
                    <h3 className="font-medium text-white truncate">
                      {conversation.conversationName}
                    </h3>
                    {conversation.lastMessageTime && (
                      <span className="text-xs text-gray-400">
                        {typeof conversation.lastMessageTime.toDate === 'function'
                          ? conversation.lastMessageTime.toDate().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
                          : new Date(conversation.lastMessageTime).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-400 truncate">
                    {conversation.lastMessage?.text || 'No messages yet'}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
  
  const renderProfileView = () => {
    if (!selectedEmployee) return null;
    
    return (
      <div className="bg-gray-800 rounded-lg p-4 h-full flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white">Employee Profile</h2>
          <div className="flex space-x-2">
            <button
              onClick={() => startConversation(selectedEmployee)}
              className="bg-blue-600 hover:bg-blue-500 text-white px-3 py-1 rounded text-sm flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Message
            </button>
            <button
              onClick={() => {
                // Go to full profile page
                navigate(`/profile/${selectedEmployee.id}`);
              }}
              className="bg-purple-600 hover:bg-purple-500 text-white px-3 py-1 rounded text-sm flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              Full Profile
            </button>
            <button
              onClick={() => setActiveView('directory')}
              className="bg-gray-700 hover:bg-gray-600 text-gray-300 px-2 py-1 rounded"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
        
        <div className="flex flex-col items-center mb-6">
          <div className="w-24 h-24 rounded-full overflow-hidden bg-gray-700 mb-4 border-2 border-gray-600">
            {selectedEmployee.photoBase64 ? (
              <img 
                src={selectedEmployee.photoBase64} 
                alt={selectedEmployee.displayName || "User"} 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            )}
          </div>
          
          <h3 className="text-xl font-semibold text-white">{selectedEmployee.displayName || selectedEmployee.email || "Unknown User"}</h3>
          
          <div className="flex items-center mt-1 space-x-2">
            {selectedEmployee.jobTitle && <span className="text-gray-400">{selectedEmployee.jobTitle}</span>}
            
            {/* Role badge */}
            {selectedEmployee.role && (
              <span className={`px-2 py-0.5 rounded-full text-xs ${
                selectedEmployee.role === 'admin' ? 'bg-red-900 text-red-200' : 
                selectedEmployee.role === 'tow' ? 'bg-yellow-900 text-yellow-200' : 
                'bg-blue-900 text-blue-200'
              }`}>
                {selectedEmployee.role}
              </span>
            )}
          </div>
          
          {/* Tags */}
          {selectedEmployee.tags && selectedEmployee.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2 justify-center">
              {selectedEmployee.tags.map((tag, tagIndex) => (
                <span 
                  key={tagIndex}
                  className="rounded-full px-2 py-0.5 text-xs font-medium"
                  style={{
                    backgroundColor: tag.color,
                    color: getTextColor(tag.color)
                  }}
                >
                  {tag.name}
                </span>
              ))}
            </div>
          )}
        </div>
        
        <div className="bg-gray-700 rounded-lg p-4 mb-4 flex-grow">
          <h4 className="font-medium text-gray-300 mb-2">Contact Information</h4>
          <div className="space-y-3">
            {selectedEmployee.email && (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span className="text-white">{selectedEmployee.email}</span>
              </div>
            )}
            {selectedEmployee.phoneNumber && (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                <span className="text-white">{selectedEmployee.phoneNumber}</span>
              </div>
            )}
            {selectedEmployee.location && (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span className="text-white">{selectedEmployee.location}</span>
              </div>
            )}
            {selectedEmployee.vehicle && (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                </svg>
                <span className="text-white">Vehicle #: {selectedEmployee.vehicle}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };
  
  const renderChatView = () => {
    if (!activeConversation) return null;
    
    return (
      <div className="bg-gray-800 rounded-lg flex flex-col h-full">
        {/* Chat Header */}
        <div className="p-3 border-b border-gray-700 flex items-center justify-between">
          <div className="flex items-center">
            {activeConversation.participantDetails.length > 1 ? (
              <div className="relative w-10 h-10 flex-shrink-0 mr-3">
                <div className="absolute top-0 left-0 w-7 h-7 rounded-full overflow-hidden bg-blue-600 border border-gray-800">
                  {activeConversation.participantDetails[0]?.photo ? (
                    <img 
                      src={activeConversation.participantDetails[0].photo} 
                      alt={activeConversation.participantDetails[0].name} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-white text-xs">
                      {activeConversation.participantDetails[0]?.name?.charAt(0) || "?"}
                    </div>
                  )}
                </div>
                <div className="absolute bottom-0 right-0 w-7 h-7 rounded-full overflow-hidden bg-green-600 border border-gray-800">
                  {activeConversation.participantDetails[1]?.photo ? (
                    <img 
                      src={activeConversation.participantDetails[1].photo} 
                      alt={activeConversation.participantDetails[1].name} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-white text-xs">
                      {activeConversation.participantDetails[1]?.name?.charAt(0) || "?"}
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-600 flex-shrink-0 mr-3 border border-gray-700">
                {activeConversation.participantDetails[0]?.photo ? (
                  <img 
                    src={activeConversation.participantDetails[0].photo} 
                    alt={activeConversation.participantDetails[0].name} 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                )}
              </div>
            )}
            
            <div>
              <h3 className="font-medium text-white">{activeConversation.conversationName}</h3>
              <p className="text-xs text-gray-400">
                {activeConversation.participants.length} participants • Messages auto-delete after 30 days
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setActiveView('directory')}
              className="bg-gray-700 hover:bg-gray-600 text-gray-300 p-2 rounded"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
        
        {/* Chat Messages */}
        <div 
          ref={chatMessagesRef} 
          className="flex-grow overflow-y-auto p-4 space-y-4"
          style={{ maxHeight: 'calc(100vh - 220px)' }}
        >
          {chatMessages.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              No messages yet. Start the conversation!
            </div>
          ) : (
            chatMessages.map(message => {
              const isCurrentUser = message.sender.uid === currentUser?.uid;
              const isSystem = message.isSystem;
              
              if (isSystem) {
                return (
                  <div key={message.id} className="flex justify-center">
                    <div className="bg-gray-700 text-gray-300 text-xs px-3 py-1 rounded-full">
                      {message.text}
                    </div>
                  </div>
                );
              }
              
              return (
                <div 
                  key={message.id} 
                  className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-[75%] ${isCurrentUser ? 'bg-blue-600' : 'bg-gray-700'} rounded-lg p-3`}>
                    <div className="flex items-center mb-1">
                      {!isCurrentUser && (
                        <div className="w-6 h-6 rounded-full overflow-hidden bg-gray-600 mr-2">
                          {message.sender.photo ? (
                            <img src={message.sender.photo} alt={message.sender.name} className="w-full h-full object-cover" />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-300 text-xs">
                              {message.sender.name.charAt(0)}
                            </div>
                          )}
                        </div>
                      )}
                      <div className="text-sm font-medium text-gray-200">{message.sender.name}</div>
                      <div className="ml-auto flex items-center">
                        <span className="text-xs text-gray-300 mr-2">
                          {message.timestamp ? 
                            (typeof message.timestamp.toDate === 'function' 
                              ? message.timestamp.toDate().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) 
                              : new Date(message.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}))
                            : ''}
                        </span>
                        
                        {/* Delete button - visible to all participants in DMs */}
                        <button 
                          onClick={() => deleteMessage(message.id)}
                          className="text-gray-300 hover:text-red-400"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    
                    {message.text && (
                      <div className="text-white break-words mb-2">{message.text}</div>
                    )}
                    
                    {/* Display media */}
                    {message.mediaUrl && message.mediaType === 'image' && (
                      <div className="mt-2 rounded overflow-hidden">
                        <img 
                          src={message.mediaUrl} 
                          alt="Shared"
                          className="max-w-full h-auto" 
                        />
                      </div>
                    )}
                    
                    {message.mediaUrl && message.mediaType === 'video' && (
                      <div className="mt-2 rounded overflow-hidden">
                        <video 
                          src={message.mediaUrl} 
                          controls
                          className="max-w-full h-auto"
                        />
                      </div>
                    )}
                  </div>
                </div>
              );
            })
          )}
        </div>
        
        {/* Chat Input */}
        <div className="p-3 border-t border-gray-700">
          <form onSubmit={sendMessage} className="flex items-center">
            {/* Preview of media to upload */}
            {mediaToUpload && (
              <div className="relative mr-2">
                <div className="w-12 h-12 rounded overflow-hidden border border-gray-600">
                  {mediaToUpload.type === 'image' && (
                    <img 
                      src={mediaToUpload.preview} 
                      alt="Upload preview" 
                      className="w-full h-full object-cover"
                    />
                  )}
                  {mediaToUpload.type === 'video' && (
                    <video 
                      src={mediaToUpload.preview}
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>
                <button
                  type="button"
                  onClick={() => setMediaToUpload(null)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </div>
            )}
            
            <input
              ref={inputRef}
              type="text"
              className="flex-grow bg-gray-700 border border-gray-600 rounded-l-md px-3 py-2 text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Type your message..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              disabled={isUploadingMedia}
            />
            
            <button
              type="button"
              onClick={() => handleMediaUpload('image')}
              className="bg-gray-700 border-t border-b border-gray-600 px-2 py-2 text-gray-300 hover:text-white"
              disabled={isUploadingMedia}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </button>
            
            <button
              type="button"
              onClick={() => handleMediaUpload('video')}
              className="bg-gray-700 border-t border-b border-r border-gray-600 px-2 py-2 text-gray-300 hover:text-white"
              disabled={isUploadingMedia}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </button>
            
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-r-md"
              disabled={(!newMessage.trim() && !mediaToUpload) || isUploadingMedia}
            >
              {isUploadingMedia ? (
                <div className="flex items-center">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                  <span>Uploading...</span>
                </div>
              ) : (
                <span>Send</span>
              )}
            </button>
          </form>
        </div>
      </div>
    );
  };
  
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Top Navigation Bar */}
      <nav className="bg-gray-800 shadow-lg border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold" style={{
                  backgroundImage: 'linear-gradient(to right, #4f46e5, #a855f7, #4f46e5)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundSize: '200% auto',
                }}>NWRepo</h1>
              </div>
              <div className="ml-6 flex space-x-4">
                <button 
                  onClick={() => navigate('/dashboard')}
                  className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                >
                  Dashboard
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>
      
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Two-column layout */}
    <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-6">
          {/* Directory Column */}
          <div className="w-full md:w-1/3 bg-gray-800 rounded-lg p-4 shadow-lg">
            {renderDirectoryHeader()}
            {renderSearchBar()}
            {renderEmployeeList()}
            {renderRecentConversations()}
          </div>
          
          {/* Detail Column (Profile/Chat) */}
          <div className="w-full md:w-2/3 bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            {activeView === 'profile' && renderProfileView()}
            {activeView === 'chat' && renderChatView()}
            {activeView === 'directory' && (
              <div className="h-full flex items-center justify-center p-8 text-gray-400">
                <div className="text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <h3 className="text-xl font-medium mb-2">Employee Directory</h3>
                  <p className="max-w-md">
                    Select an employee to view their profile or start a conversation.
                    All messages automatically delete after 30 days.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default EmployeeDirectory;