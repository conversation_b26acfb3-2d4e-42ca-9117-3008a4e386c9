// src/pages/SplashScreen.js
import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

function SplashScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const [fadeOut, setFadeOut] = useState(false);
  
  // Get the destination from location state, default to login-direct
  const redirectTo = location.state?.redirectTo || "/login-direct";
  
  useEffect(() => {
    // Start fade out animation after 5.5 seconds
    const fadeTimer = setTimeout(() => {
      setFadeOut(true);
    }, 5500);
    
    // Redirect to the destination after 7 seconds
    const redirectTimer = setTimeout(() => {
      navigate(redirectTo);
    }, 7000);
    
    // Clean up timers on unmount
    return () => {
      clearTimeout(fadeTimer);
      clearTimeout(redirectTimer);
    };
  }, [navigate, redirectTo]);
  
  return (
    <div className={`min-h-screen flex items-center justify-center bg-gray-900 transition-opacity duration-1500 ${fadeOut ? 'opacity-0' : 'opacity-100'}`}>
      <div className="text-center">
        {/* Solid text version for better visibility */}
        <h1 className="text-7xl font-bold">
          <span className="text-white">
            <span className="inline-block animate-bounce delay-100">R</span>
            <span className="inline-block animate-bounce delay-200">e</span>
            <span className="inline-block animate-bounce delay-300">c</span>
            <span className="inline-block animate-bounce delay-400">o</span>
            <span className="inline-block animate-bounce delay-500">v</span>
            <span className="inline-block animate-bounce delay-600">e</span>
            <span className="inline-block animate-bounce delay-700">r</span>
            <span className="inline-block animate-bounce delay-800">I</span>
            <span className="inline-block animate-bounce delay-900">Q</span>
          </span>
        </h1>
        
        {/* Gradient version underneath (in case the issue is with the gradient) */}
        <h1 className="text-7xl font-bold mt-4">
          <span className="bg-gradient-to-r from-blue-500 to-purple-600 text-transparent bg-clip-text">RecoverIQ</span>
        </h1>
        
        <div className="mt-8">
          <div className="animate-pulse inline-block w-16 h-1 bg-blue-500 rounded-full"></div>
        </div>
      </div>
    </div>
  );
}

export default SplashScreen;