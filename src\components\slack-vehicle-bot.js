// src/components/slack-vehicle-bot.js
// Complete Slack Bot Integration for Vehicle Tracker System with Multi-Team Support
// UPDATED VERSION - Fixed Recovery Completion Support and Full Photo Display

require('dotenv').config();
const { App } = require('@slack/bolt');
const { WebClient } = require('@slack/web-api');
const admin = require('firebase-admin');
const express = require('express');
const bodyParser = require('body-parser');
const axios = require('axios'); // Added for OAuth

// Canvas is optional - only needed for image export
let createCanvas, registerFont;
try {
  const canvasModule = require('canvas');
  createCanvas = canvasModule.createCanvas;
  registerFont = canvasModule.registerFont;
  console.log('✅ Canvas loaded - image export enabled');
} catch (error) {
  console.log('⚠️  Canvas not installed - image export disabled');
  console.log('   Bot will work normally without image export feature');
}
const cron = require('node-cron');
const path = require('path');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID || process.env.REACT_APP_PROD_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    })
  });
}

const db = admin.firestore();

// Socket Mode debugging
process.env.DEBUG = process.env.DEBUG || 'socket-mode:*';

// Initialize Slack App
const app = new App({
  token: process.env.SLACK_BOT_TOKEN,
  signingSecret: process.env.SLACK_SIGNING_SECRET,
  socketMode: true,
  appToken: process.env.SLACK_APP_TOKEN,
  logLevel: process.env.LOG_LEVEL || 'debug',
  processBeforeResponse: true,
  clientOptions: {
    slackApiUrl: 'https://slack.com/api/'
  }
});

// Create Express app for webhook
const webhookApp = express();
webhookApp.use(bodyParser.json());

// Add CORS middleware to allow requests from React app
webhookApp.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  
  next();
});

// Add global error handler for unhandled command errors
process.on('unhandledRejection', (error) => {
  console.error('Unhandled promise rejection:', error);
});

// Store for active vehicle timers and reminders
const vehicleTimers = new Map();
const reminderStates = new Map();
const teamChannels = new Map();
const channelTeams = new Map(); // Maps channel to team
const userLinkedChannels = new Map(); // Maps user to their linked channels
const userPostingPreferences = new Map(); // Store user posting preferences

// Store reminder intervals per team/channel
const reminderIntervals = new Map();

// Store for reminder preferences
const reminderPreferences = new Map(); // Stores channel reminder preferences

// Get color based on vehicle status
const getStatusColor = (vehicle) => {
  if (vehicle.bottomStatus) return '#FF6B6B'; // Orange-red for bottom status
  if (vehicle.status === 'SECURED') return '#51CF66'; // Green for secured
  if (vehicle.status === 'DO NOT SECURE') return '#9333EA'; // Purple for do not secure
  if (vehicle.arrivedDriverId) return '#339AF0'; // Blue for arrived
  if (vehicle.inRouteDriverId) return '#74C0FC'; // Light blue for in route
  if (vehicle.status === 'PENDING PICKUP') return '#FFD43B'; // Yellow for pending
  return '#E03131'; // Red for new/found
};

// Utility Functions
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);

  const timeStr = date.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  });

  if (date.toDateString() === now.toDateString()) {
    return `Today ${timeStr}`;
  } else if (diffDays === 1) {
    return `Yesterday ${timeStr}`;
  } else if (diffDays < 7) {
    return `${diffDays}d ago ${timeStr}`;
  } else {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }
};

const calculateElapsedTime = (vehicle) => {
  if (!vehicle.createdAt) return null;
  const createdTime = vehicle.createdAt.toMillis ? vehicle.createdAt.toMillis() : new Date(vehicle.createdAt).getTime();
  const elapsed = Date.now() - createdTime;
  return {
    elapsed,
    hours: Math.floor(elapsed / 3600000),
    minutes: Math.floor((elapsed % 3600000) / 60000),
    days: Math.floor(elapsed / 86400000)
  };
};

const getTimerDisplay = (vehicle) => {
  const timeData = calculateElapsedTime(vehicle);
  if (!timeData) return null;
  
  const { days, hours, minutes } = timeData;
  if (days > 0) {
    return `${days}d ${hours}h`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
};

// ENHANCED: Format recovery data for Slack with better structure
const formatRecoveryForSlack = (recoveryData) => {
  console.log('[RECOVERY] Formatting recovery data:', recoveryData);
  
  if (!recoveryData) return 'No recovery details available';
  
  let formatted = '';
  
  // Vehicle Condition Report
  if (recoveryData.vehicleCondition) {
    const condition = recoveryData.vehicleCondition;
    formatted += `*🔍 Vehicle Condition:*\n`;
    formatted += `• Exterior: ${condition.exterior || 'Not assessed'}\n`;
    formatted += `• Interior: ${condition.interior || 'Not assessed'}\n`;
    formatted += `• Engine: ${condition.engine || 'Unknown'}\n`;
    formatted += `• Transmission: ${condition.transmission || 'Unknown'}\n`;
    formatted += `• Tires: ${condition.tires || 'Good'}\n`;
    formatted += `• Mileage: ${condition.mileage || 'Not recorded'}\n`;
    formatted += `• Keys Present: ${condition.keys ? 'Yes' : 'No'}\n`;
    
    if (condition.damage) {
      formatted += `• Damage Notes: ${condition.damage}\n`;
    }
    formatted += '\n';
  }
  
  // Personal Property
  if (recoveryData.personalPropertyNotes || recoveryData.vehicleCondition?.personalItems) {
    const personalItems = recoveryData.personalPropertyNotes || recoveryData.vehicleCondition?.personalItems;
    formatted += `*📋 Personal Property:*\n`;
    if (personalItems && personalItems.trim() !== '') {
      formatted += `• Items Found: ${personalItems}\n`;
    } else {
      formatted += `• No personal items found\n`;
    }
    formatted += '\n';
  }
  
  // Recovery Checklist
  if (recoveryData.checklistCompleted || recoveryData.checklist) {
    const checklist = recoveryData.checklistCompleted || recoveryData.checklist;
    formatted += `*✅ Recovery Checklist:*\n`;
    formatted += `• Vehicle Secured: ${checklist.vehicleSecured ? '✅' : '❌'}\n`;
    formatted += `• VIN Verified: ${checklist.verifyVinMatch ? '✅' : '❌'}\n`;
    formatted += `• On Hook: ${checklist.confirmOnHook ? '✅' : '❌'}\n`;
    formatted += `• Condition Report: ${checklist.conditionReportComplete ? '✅' : '❌'}\n`;
    formatted += `• Personal Property: ${checklist.personalPropertyInventory ? '✅' : '❌'}\n`;
    formatted += `• Photos Taken: ${checklist.photosTaken ? '✅' : '❌'}\n`;
    formatted += `• Forms Completed: ${checklist.formsCompleted ? '✅' : '❌'}\n`;
    formatted += `• System Updated: ${checklist.systemUpdated ? '✅' : '❌'}\n`;
    formatted += `• Parties Notified: ${checklist.partiesNotified ? '✅' : '❌'}\n`;
    formatted += '\n';
  }
  
  // VIN Mismatch Warning
  if (recoveryData.vinMatchWarning) {
    formatted += `*⚠️ VIN Warning:*\n`;
    formatted += `• VIN may not match exactly - discrepancy noted\n\n`;
  }
  
  // Location Information
  if (recoveryData.recoveryLocation) {
    formatted += `*📍 Recovery Location:*\n`;
    formatted += `• ${recoveryData.recoveryLocation}\n\n`;
  }
  
  // Recovery Photos Count
  if (recoveryData.photoURLs) {
    const photoCount = Object.values(recoveryData.photoURLs).flat().filter(url => url && url !== '').length;
    if (photoCount > 0) {
      formatted += `*📸 Recovery Photos: ${photoCount} images captured*\n\n`;
    }
  }
  
  // Recovery Time
  if (recoveryData.timestamp) {
    formatted += `*⏰ Completed:* ${formatTimestamp(recoveryData.timestamp)}\n`;
  }
  
  // Additional Notes
  if (recoveryData.notes || recoveryData.vehicleCondition?.notes) {
    const notes = recoveryData.notes || recoveryData.vehicleCondition?.notes;
    if (notes && notes.trim() !== '') {
      formatted += `*📝 Additional Notes:*\n${notes}\n`;
    }
  }
  
  console.log('[RECOVERY] Formatted recovery text length:', formatted.length);
  return formatted.trim();
};

// ENHANCED: Post detailed recovery report with ALL photos
const postDetailedRecoveryReport = async (channel, vehicle, recoveryData) => {
  try {
    console.log('[RECOVERY] Posting detailed recovery report to channel:', channel);
    console.log('[RECOVERY] Recovery data structure:', JSON.stringify(recoveryData, null, 2));
    
    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📋 Detailed Recovery Report',
          emoji: true
        }
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Vehicle:* ${vehicle.vehicle}\n*VIN:* ${vehicle.vin || 'No VIN'}\n*Recovered by:* ${vehicle.securedByUserName}`
        }
      },
      {
        type: 'divider'
      }
    ];
    
    // Add formatted recovery details
    const formattedDetails = formatRecoveryForSlack(recoveryData);
    if (formattedDetails && formattedDetails !== 'No recovery details available') {
      // Split long text into chunks to avoid Slack limits
      const maxChunkLength = 2800; // Leave room for formatting
      const chunks = [];
      let currentChunk = '';
      
      formattedDetails.split('\n').forEach(line => {
        if (currentChunk.length + line.length + 1 > maxChunkLength) {
          chunks.push(currentChunk);
          currentChunk = line;
        } else {
          currentChunk += (currentChunk ? '\n' : '') + line;
        }
      });
      
      if (currentChunk) {
        chunks.push(currentChunk);
      }
      
      chunks.forEach(chunk => {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: chunk
          }
        });
      });
    }
    
    // Add recovery photos if available - SHOW ALL PHOTOS
    const allPhotos = [];
    
    if (recoveryData.photoURLs) {
      const photoData = recoveryData.photoURLs;
      console.log('[RECOVERY] Photo URLs structure:', JSON.stringify(photoData, null, 2));
      
      // Collect all photo URLs
      Object.entries(photoData).forEach(([photoType, urls]) => {
        if (photoType === 'damage' && Array.isArray(urls)) {
          // Handle damage photos array
          urls.forEach((url, index) => {
            if (url && url.trim() !== '') {
              allPhotos.push({
                url: url,
                title: `Damage Photo ${index + 1}`,
                type: 'damage'
              });
            }
          });
        } else if (urls && typeof urls === 'string' && urls.trim() !== '') {
          // Handle single photo URLs
          const photoTitle = photoType.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
          allPhotos.push({
            url: urls,
            title: photoTitle,
            type: photoType
          });
        }
      });
    }
    
    // Also check for photos array (backup)
    if (recoveryData.photos && Array.isArray(recoveryData.photos)) {
      recoveryData.photos.forEach((photo, index) => {
        if (photo && photo.url) {
          allPhotos.push({
            url: photo.url,
            title: photo.title || `Recovery Photo ${index + 1}`,
            type: 'general'
          });
        }
      });
    }
    
    console.log('[RECOVERY] Total photos found:', allPhotos.length);
    
    if (allPhotos.length > 0) {
      blocks.push({
        type: 'divider'
      });
      
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*📸 Recovery Photos (${allPhotos.length} total):*`
        }
      });
      
      // Show ALL photos, but group them to avoid message limits
      // Slack has a limit of ~50 blocks per message, so we'll batch photos
      const photosPerMessage = 15; // Conservative limit
      
      for (let i = 0; i < allPhotos.length; i += photosPerMessage) {
        const photoBatch = allPhotos.slice(i, i + photosPerMessage);
        
        if (i > 0) {
          // If this isn't the first batch, send as a separate message
          const photoBlocks = [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*📸 Recovery Photos (continued - batch ${Math.floor(i / photosPerMessage) + 1}):*`
              }
            }
          ];
          
          photoBatch.forEach(photo => {
            photoBlocks.push({
              type: 'image',
              image_url: photo.url,
              alt_text: `${photo.title} - ${vehicle.vehicle}`,
              title: {
                type: 'plain_text',
                text: photo.title
              }
            });
          });
          
          await app.client.chat.postMessage({
            channel: channel,
            text: 'Recovery Photos (continued)',
            blocks: photoBlocks
          });
        } else {
          // Add to main message
          photoBatch.forEach(photo => {
            blocks.push({
              type: 'image',
              image_url: photo.url,
              alt_text: `${photo.title} - ${vehicle.vehicle}`,
              title: {
                type: 'plain_text',
                text: photo.title
              }
            });
          });
        }
      }
    }
    
    // Add final summary
    blocks.push({
      type: 'divider'
    });
    
    blocks.push({
      type: 'context',
      elements: [
        {
          type: 'mrkdwn',
          text: `*Recovery completed at:* ${new Date().toLocaleString()} | *Total photos:* ${allPhotos.length}`
        }
      ]
    });
    
    await app.client.chat.postMessage({
      channel: channel,
      text: 'Detailed Recovery Report',
      blocks: blocks
    });
    
    console.log('[RECOVERY] Detailed recovery report posted successfully');
    
  } catch (error) {
    console.error('[RECOVERY] Error posting detailed recovery report:', error);
    console.error('[RECOVERY] Error stack:', error.stack);
    
    // Send a fallback message
    try {
      await app.client.chat.postMessage({
        channel: channel,
        text: `❌ Error posting detailed recovery report for ${vehicle.vehicle}. Please check the logs.`,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `❌ *Error posting detailed recovery report*\n\nVehicle: ${vehicle.vehicle}\nRecovered by: ${vehicle.securedByUserName}\n\nPlease check the bot logs for more details.`
            }
          }
        ]
      });
    } catch (fallbackError) {
      console.error('[RECOVERY] Failed to send fallback message:', fallbackError);
    }
  }
};

// Helper function to get complete vehicle data with metadata
async function getCompleteVehicleData(teamMemberId, weekId, vehicleId) {
  // Get vehicle document
  const vehicleDoc = await db.collection('users').doc(teamMemberId)
    .collection('vehicleWeeks').doc(weekId)
    .collection('vehicles').doc(vehicleId)
    .get();

  if (!vehicleDoc.exists) {
    throw new Error('Vehicle not found');
  }

  // Get team member info
  const memberDoc = await db.collection('users').doc(teamMemberId).get();
  const memberData = memberDoc.exists ? memberDoc.data() : {};
  const memberName = memberData.displayName || memberData.email?.split('@')[0] || 'Team Member';

  // Get week info
  const weekDoc = await db.collection('users').doc(teamMemberId)
    .collection('vehicleWeeks').doc(weekId).get();
  const weekData = weekDoc.exists ? weekDoc.data() : {};

  // Find team name
  let teamName = 'Unknown Team';
  let teamId = null;
  const teamsSnapshot = await db.collection('teams').get();
  for (const teamDoc of teamsSnapshot.docs) {
    const teamMembersSnapshot = await db.collection('teams')
      .doc(teamDoc.id)
      .collection('teamMembers')
      .where('userId', '==', teamMemberId)
      .limit(1)
      .get();
    
    if (!teamMembersSnapshot.empty) {
      teamName = teamDoc.data().name || 'Unknown Team';
      teamId = teamDoc.id;
      break;
    }
  }

  // Build complete vehicle object with ALL metadata
  return { 
    id: vehicleDoc.id, 
    ...vehicleDoc.data(),
    teamMemberId: teamMemberId,
    teamMemberName: memberName,
    weekId: weekId,
    weekRange: weekData.displayRange || 'Unknown Week',
    teamName: teamName,
    teamId: teamId,
    uniqueKey: `${teamMemberId}_${weekId}_${vehicleDoc.id}`
  };
}

// NEW: Get user from linked account
async function getLinkedUser(slackUserId) {
  const linkQuery = await db.collection('slack_user_links')
    .where('slackUserId', '==', slackUserId)
    .limit(1)
    .get();
  
  if (linkQuery.empty) {
    return null;
  }
  
  const linkData = linkQuery.docs[0].data();
  const userDoc = await db.collection('users').doc(linkData.firebaseUserId).get();
  
  if (!userDoc.exists) {
    return null;
  }
  
  return {
    id: userDoc.id,
    ...userDoc.data(),
    isLinked: true
  };
}

// UPDATED: Get or create user - now checks for linked accounts first
async function getOrCreateUserAndTeam(userId, teamId, teamDomain, client) {
  // First check if user has a linked account
  const linkedUser = await getLinkedUser(userId);
  if (linkedUser) {
    console.log(`[DEBUG] Using linked account for Slack user ${userId}: ${linkedUser.displayName}`);
    
    // Find user's team
    let team = null;
    const teamsSnapshot = await db.collection('teams').get();
    for (const teamDoc of teamsSnapshot.docs) {
      const teamMembersSnapshot = await db.collection('teams')
        .doc(teamDoc.id)
        .collection('teamMembers')
        .where('userId', '==', linkedUser.id)
        .limit(1)
        .get();
      
      if (!teamMembersSnapshot.empty) {
        team = { id: teamDoc.id, ...teamDoc.data() };
        break;
      }
    }
    
    return { user: linkedUser, team };
  }

  // Original email-based logic as fallback
  const userInfo = await client.users.info({ user: userId });
  const userEmail = userInfo.user.profile.email;
  
  // IMPORTANT: Use real_name as primary source
  const slackDisplayName = userInfo.user.profile.real_name || 
                          userInfo.user.profile.display_name || 
                          userInfo.user.name || 
                          userEmail.split('@')[0];
  
  // Get or create user
  const usersRef = db.collection('users');
  const userQuery = await usersRef.where('email', '==', userEmail).get();
  
  let user;
  if (userQuery.empty) {
    const newUserRef = await usersRef.add({
      email: userEmail,
      displayName: slackDisplayName, // Use Slack's real name
      slackUserId: userId,
      slackTeamId: teamId,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    user = { 
      id: newUserRef.id, 
      email: userEmail, 
      displayName: slackDisplayName,
      slackUserId: userId,
      slackTeamId: teamId
    };
  } else {
    user = { id: userQuery.docs[0].id, ...userQuery.docs[0].data() };
    
    // Update Slack info but DON'T overwrite display name if it exists
    const updates = {
      slackUserId: userId,
      slackTeamId: teamId
    };
    
    // Only update display name if it's missing or is just the email prefix
    if (!user.displayName || user.displayName === userEmail.split('@')[0]) {
      updates.displayName = slackDisplayName;
    }
    
    await userQuery.docs[0].ref.update(updates);
    
    // Keep the existing display name if available
    user.slackUserId = userId;
    user.slackTeamId = teamId;
    if (!user.displayName || user.displayName === userEmail.split('@')[0]) {
      user.displayName = slackDisplayName;
    }
  }
  
  // Get or create team for Slack workspace
  const teamsRef = db.collection('teams');
  const teamQuery = await teamsRef.where('slackTeamId', '==', teamId).get();
  
  let team;
  if (teamQuery.empty) {
    const workspaceInfo = await client.team.info({ team: teamId });
    const teamName = workspaceInfo.team.name || 'Slack Team';
    
    const newTeamRef = await teamsRef.add({
      name: teamName,
      slackTeamId: teamId,
      slackTeamDomain: teamDomain,
      teamMembers: [user.id],
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    team = { id: newTeamRef.id, name: teamName, teamMembers: [user.id] };
  } else {
    team = { id: teamQuery.docs[0].id, ...teamQuery.docs[0].data() };
    
    if (!team.teamMembers || !team.teamMembers.includes(user.id)) {
      await teamQuery.docs[0].ref.update({
        teamMembers: admin.firestore.FieldValue.arrayUnion(user.id)
      });
      team.teamMembers = [...(team.teamMembers || []), user.id];
    }
  }
  
  return { user, team };
}

// Get user posting preference
async function getUserPostingPreference(firebaseUserId) {
  try {
    const userDoc = await db.collection('users').doc(firebaseUserId).get();
    if (userDoc.exists) {
      const userData = userDoc.data();
      // Default to 'team' if not set
      return userData.slackPostingPreference || 'team';
    }
    return 'team';
  } catch (error) {
    console.error('Error getting user posting preference:', error);
    return 'team';
  }
}

// Set user posting preference
async function setUserPostingPreference(firebaseUserId, preference) {
  try {
    await db.collection('users').doc(firebaseUserId).update({
      slackPostingPreference: preference,
      slackPostingPreferenceUpdatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    // Update cache
    userPostingPreferences.set(firebaseUserId, preference);
    
    return true;
  } catch (error) {
    console.error('Error setting user posting preference:', error);
    return false;
  }
}

// Enhanced Vehicle Card Builder for Slack with color coding and notes
const buildVehicleCard = (vehicle, options = {}) => {
  const statusEmoji = vehicle.bottomStatus ? '🔴' :
                     vehicle.status === 'SECURED' ? '✅' :
                     vehicle.status === 'DO NOT SECURE' ? '🚫' :
                     vehicle.status === 'FOUND' ? '🟡' :
                     vehicle.status === 'PENDING PICKUP' ? '🔵' : '❌';

  const blocks = [
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: `${statusEmoji} ${vehicle.vehicle || 'Unknown Vehicle'}`,
        emoji: true
      }
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*VIN:*\n${vehicle.vin || 'No VIN'} ${vehicle.vinVerified ? '✓' : '❌'}`
        },
        {
          type: 'mrkdwn',
          text: `*Status:*\n${vehicle.status || 'UNKNOWN'}`
        },
        {
          type: 'mrkdwn',
          text: `*From:*\n${vehicle.teamMemberName || 'Team Member'}`
        },
        {
          type: 'mrkdwn',
          text: `*Date:*\n${vehicle.date || 'Unknown'}`
        }
      ]
    }
  ];

  // Add team name if provided
  if (vehicle.teamName) {
    blocks[1].fields.push({
      type: 'mrkdwn',
      text: `*Team:*\n${vehicle.teamName}`
    });
  }

  // FIXED: Add DO NOT SECURE reason if present
  if (vehicle.status === 'DO NOT SECURE' && vehicle.doNotSecureReason) {
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `🚫 *DO NOT SECURE - Reason:*\n${vehicle.doNotSecureReason}`
      }
    });
  }

  // Add color and drive type if available
  if (vehicle.color || vehicle.driveType) {
    blocks.push({
      type: 'section',
      fields: [
        vehicle.color ? {
          type: 'mrkdwn',
          text: `*Color:*\n${vehicle.color}`
        } : null,
        vehicle.driveType ? {
          type: 'mrkdwn',
          text: `*Drive Type:*\n${vehicle.driveType}`
        } : null
      ].filter(Boolean)
    });
  }

  // Add account details if available
  if (vehicle.plateNumber || vehicle.accountNumber || vehicle.financier) {
    const fields = [];
    if (vehicle.plateNumber) {
      fields.push({
        type: 'mrkdwn',
        text: `*Plate #:*\n${vehicle.plateNumber}`
      });
    }
    if (vehicle.accountNumber) {
      fields.push({
        type: 'mrkdwn',
        text: `*Account #:*\n${vehicle.accountNumber}`
      });
    }
    if (vehicle.financier) {
      fields.push({
        type: 'mrkdwn',
        text: `*Financier:*\n${vehicle.financier}`
      });
    }
    
    blocks.push({
      type: 'section',
      fields: fields.slice(0, 2) // Slack limits to 2 fields per section
    });
    
    if (fields.length > 2) {
      blocks.push({
        type: 'section',
        fields: fields.slice(2)
      });
    }
  }

  // Location section
  if (vehicle.address) {
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `📍 *Location:*\n${vehicle.address}`
      },
      accessory: vehicle.position ? {
        type: 'button',
        text: {
          type: 'plain_text',
          text: 'Open in Maps',
          emoji: true
        },
        url: `https://maps.google.com/?q=${vehicle.position.lat},${vehicle.position.lng}`,
        action_id: 'open_maps'
      } : undefined
    });
  }

  // IMPORTANT: Show notes section prominently
  if (vehicle.notes && vehicle.notes.trim() !== '') {
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `📝 *Notes:*\n${vehicle.notes}`
      }
    });
  }

  // Bottom status warning
  if (vehicle.bottomStatus) {
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `⚠️ *Bottom Status:* ${vehicle.bottomStatus} (Attempt ${vehicle.bottomStatusCount || 1}/3)`
      }
    });
    
    if (vehicle.cantSecureReason) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `📋 *Reason:* ${vehicle.cantSecureReason}`
        }
      });
    }
  }

  // Driver status - SHOW WHO CLAIMED IT
  if (vehicle.inRouteDriverId || vehicle.arrivedDriverId) {
    const driverStatus = vehicle.arrivedDriverId ? 
      `📍 *Driver Arrived:* ${vehicle.arrivedDriverName || 'Driver'}` :
      `🚚 *In Route:* ${vehicle.inRouteDriverName || 'Driver'}`;
    
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: driverStatus
      }
    });
    
    // Add driver assignment time
    if (vehicle.inRouteTimestamp || vehicle.arrivedTimestamp) {
      const timestamp = vehicle.arrivedTimestamp || vehicle.inRouteTimestamp;
      blocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `Driver assigned: ${formatTimestamp(timestamp)}`
          }
        ]
      });
    }
  }

  // Timer display
  if (!vehicle.inRouteDriverId && !vehicle.arrivedDriverId && !vehicle.bottomStatus && !options.noTimer) {
    const timerDisplay = getTimerDisplay(vehicle);
    if (timerDisplay) {
      blocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `⏱️ Time elapsed: ${timerDisplay}`
          }
        ]
      });
    }
  }

  // FIXED: Images section - Show ALL images inline instead of just one
  if (vehicle.images && vehicle.images.length > 0 && !options.noImages) {
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `📷 *${vehicle.images.length} Photos Available*`
      }
    });

    // Show ALL images inline (up to Slack's limit)
    vehicle.images.forEach((image, index) => {
      if (image && image.url && index < 10) { // Slack has limits on message size
        blocks.push({
          type: 'image',
          image_url: image.url,
          alt_text: `${vehicle.vehicle} - Photo ${index + 1}`
        });
      }
    });

    if (vehicle.images.length > 10) {
      blocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `_${vehicle.images.length - 10} more photos not shown due to message size limits_`
          }
        ]
      });
    }
  }

  // Add divider before actions
  if (!options.noActions) {
    blocks.push({ type: 'divider' });

    // Action buttons
    const actions = {
      type: 'actions',
      elements: []
    };

    // Dynamic action buttons based on status - DISABLE IF SOMEONE ELSE HAS CLAIMED
    const isVehicleClaimed = vehicle.inRouteDriverId || vehicle.arrivedDriverId;
    
    if (!isVehicleClaimed && vehicle.status !== 'SECURED' && vehicle.status !== 'DO NOT SECURE') {
      // Vehicle is available - show Mark In Route button
      actions.elements.push({
        type: 'button',
        text: {
          type: 'plain_text',
          text: '🚚 Mark In Route',
          emoji: true
        },
        style: 'primary',
        action_id: 'mark_in_route',
        value: JSON.stringify({
          vehicleId: vehicle.id,
          teamMemberId: vehicle.teamMemberId,
          weekId: vehicle.weekId
        })
      });
    } else if (vehicle.inRouteDriverId && !vehicle.arrivedDriverId && vehicle.status !== 'SECURED') {
      // Show Mark Arrived only for the driver who claimed it
      actions.elements.push({
        type: 'button',
        text: {
          type: 'plain_text',
          text: '📍 Mark Arrived',
          emoji: true
        },
        style: 'primary',
        action_id: 'mark_arrived',
        value: JSON.stringify({
          vehicleId: vehicle.id,
          teamMemberId: vehicle.teamMemberId,
          weekId: vehicle.weekId,
          driverId: vehicle.inRouteDriverId // Include driver ID for verification
        })
      });
    } else if (vehicle.arrivedDriverId && vehicle.status !== 'SECURED') {
      // Show Secure Vehicle only for the driver who arrived
      actions.elements.push({
        type: 'button',
        text: {
          type: 'plain_text',
          text: '✅ Secure Vehicle',
          emoji: true
        },
        style: 'primary',
        action_id: 'secure_vehicle',
        value: JSON.stringify({
          vehicleId: vehicle.id,
          teamMemberId: vehicle.teamMemberId,
          weekId: vehicle.weekId,
          driverId: vehicle.arrivedDriverId // Include driver ID for verification
        })
      });
    }

    // If vehicle is claimed by someone, show who has it
    if (isVehicleClaimed && vehicle.status !== 'SECURED') {
      const claimedByText = vehicle.arrivedDriverId ? 
        `Claimed by: ${vehicle.arrivedDriverName}` : 
        `Claimed by: ${vehicle.inRouteDriverName}`;
      
      blocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `🔒 ${claimedByText}`
          }
        ]
      });
    }

    // Always show these options
    actions.elements.push(
      {
        type: 'button',
        text: {
          type: 'plain_text',
          text: '📋 View Order',
          emoji: true
        },
        action_id: 'view_order',
        value: JSON.stringify({
          vehicleId: vehicle.id,
          teamMemberId: vehicle.teamMemberId,
          weekId: vehicle.weekId
        })
      },
      {
        type: 'button',
        text: {
          type: 'plain_text',
          text: '📸 Export Card',
          emoji: true
        },
        action_id: 'export_card',
        value: JSON.stringify({
          vehicleId: vehicle.id,
          teamMemberId: vehicle.teamMemberId,
          weekId: vehicle.weekId
        })
      },
      {
        type: 'button',
        text: {
          type: 'plain_text',
          text: '🔄 Refresh',
          emoji: true
        },
        action_id: 'refresh_vehicle',
        value: JSON.stringify({
          vehicleId: vehicle.id,
          teamMemberId: vehicle.teamMemberId,
          weekId: vehicle.weekId
        })
      }
    );

    // Bottom status overflow menu - ONLY SHOW FOR DRIVER WHO ARRIVED
    if (!vehicle.bottomStatus && vehicle.arrivedDriverId && vehicle.status !== 'SECURED' && vehicle.status !== 'DO NOT SECURE') {
      actions.elements.push({
        type: 'overflow',
        options: [
          {
            text: {
              type: 'plain_text',
              text: 'Mark as Gone'
            },
            value: JSON.stringify({
              a: 'bs', // action: bottom_status
              s: 'GONE', // status
              v: vehicle.id, // vehicleId
              m: vehicle.teamMemberId, // teamMemberId
              w: vehicle.weekId, // weekId
              d: vehicle.arrivedDriverId // driverId for verification
            })
          },
          {
            text: {
              type: 'plain_text',
              text: 'Mark as Blocked In'
            },
            value: JSON.stringify({
              a: 'bs',
              s: 'BLOCKED IN',
              v: vehicle.id,
              m: vehicle.teamMemberId,
              w: vehicle.weekId,
              d: vehicle.arrivedDriverId
            })
          },
          {
            text: {
              type: 'plain_text',
              text: 'Mark as Can\'t Secure'
            },
            value: JSON.stringify({
              a: 'bs',
              s: 'CANT SECURE',
              v: vehicle.id,
              m: vehicle.teamMemberId,
              w: vehicle.weekId,
              d: vehicle.arrivedDriverId
            })
          },
          {
            text: {
              type: 'plain_text',
              text: 'Mark as Debtor Interference'
            },
            value: JSON.stringify({
              a: 'bs',
              s: 'DEBTOR',
              v: vehicle.id,
              m: vehicle.teamMemberId,
              w: vehicle.weekId,
              d: vehicle.arrivedDriverId
            })
          }
        ],
        action_id: 'vehicle_overflow'
      });
    }

    if (actions.elements.length > 0) {
      blocks.push(actions);
    }
  }

  // Return blocks with color attachment if specified
  if (options.useColor !== false) {
    return [{
      color: getStatusColor(vehicle),
      blocks: blocks
    }];
  }

  return blocks;
};

// ENHANCED WEBHOOK ENDPOINT - Now handles personal posting without requiring teamId AND recovery completion
webhookApp.post('/api/slack/vehicle-update', async (req, res) => {
  console.log('[WEBHOOK] Received vehicle update');
  console.log('[WEBHOOK] Request body:', JSON.stringify(req.body, null, 2));
  
  try {
    const { teamId, vehicle, updateType, recoveryData } = req.body;
    
    // Allow webhook to work without teamId when vehicle has teamMemberId
    if (!vehicle) {
      console.error('[WEBHOOK] Missing vehicle data');
      return res.status(400).json({ error: 'Missing vehicle data' });
    }
    
    console.log(`[WEBHOOK] Vehicle teamMemberId: ${vehicle.teamMemberId}`);
    console.log(`[WEBHOOK] Update type: ${updateType}`);
    console.log(`[WEBHOOK] DO NOT SECURE reason: ${vehicle.doNotSecureReason}`);
    console.log(`[WEBHOOK] Recovery data present: ${!!recoveryData}`);
    
    if (recoveryData) {
      console.log(`[WEBHOOK] Recovery data keys:`, Object.keys(recoveryData));
      console.log(`[WEBHOOK] Recovery data structure:`, JSON.stringify(recoveryData, null, 2));
    }
    
    const channelsToPost = new Set();
    let teamDoc = null;
    let teamData = null;
    let teamName = 'Unknown Team';
    
    // Get user's posting preference
    let postToTeamChannel = true;
    let postToUserChannels = true;
    
    if (vehicle.teamMemberId) {
      const postingPreference = await getUserPostingPreference(vehicle.teamMemberId);
      console.log(`[WEBHOOK] User ${vehicle.teamMemberId} posting preference: ${postingPreference}`);
      
      // Apply preference
      if (postingPreference === 'personal') {
        postToTeamChannel = false;
        postToUserChannels = true;
        console.log('[WEBHOOK] Will post to personal channels only');
      } else if (postingPreference === 'team') {
        postToTeamChannel = true;
        postToUserChannels = false;
        console.log('[WEBHOOK] Will post to team channel only');
      } else {
        console.log('[WEBHOOK] Will post to both team and personal channels');
      }
    }
    
    // Try to find the team if teamId is provided
    if (teamId && postToTeamChannel) {
      // Find the specific team and their channel
      teamDoc = await db.collection('teams').doc(teamId).get();
      
      // If not found by ID, try searching by areaId
      if (!teamDoc.exists) {
        console.log(`[WEBHOOK] Team ${teamId} not found by ID, trying as areaId...`);
        const teamsQuery = await db.collection('teams')
          .where('areaData.id', '==', teamId)
          .limit(1)
          .get();
          
        if (!teamsQuery.empty) {
          teamDoc = teamsQuery.docs[0];
          console.log(`[WEBHOOK] Found team by areaId: ${teamDoc.id}`);
        }
      }
      
      if (teamDoc && teamDoc.exists) {
        teamData = teamDoc.data();
        teamName = teamData.name || 'Unknown Team';
        const slackChannel = teamData.slackChannel;
        
        // Add team channel if configured
        if (slackChannel) {
          channelsToPost.add(slackChannel);
        }
      }
    }
    
    // If no team found but we have teamMemberId, try to find user's team
    if (!teamDoc && vehicle.teamMemberId) {
      console.log(`[WEBHOOK] No team provided, finding team for user ${vehicle.teamMemberId}`);
      const teamsSnapshot = await db.collection('teams').get();
      for (const doc of teamsSnapshot.docs) {
        const teamMembersSnapshot = await db.collection('teams')
          .doc(doc.id)
          .collection('teamMembers')
          .where('userId', '==', vehicle.teamMemberId)
          .limit(1)
          .get();
        
        if (!teamMembersSnapshot.empty) {
          teamDoc = doc;
          teamData = doc.data();
          teamName = teamData.name || 'Unknown Team';
          
          // Add team channel if preference allows
          if (teamData.slackChannel && postToTeamChannel) {
            channelsToPost.add(teamData.slackChannel);
          }
          break;
        }
      }
    }
    
    // Find all user-linked channels for this user if preference allows
    if (vehicle.teamMemberId && postToUserChannels) {
      console.log(`[WEBHOOK] Looking for user-linked channels for user: ${vehicle.teamMemberId}`);
      const userDoc = await db.collection('users').doc(vehicle.teamMemberId).get();
      if (userDoc.exists) {
        const userData = userDoc.data();
        console.log(`[WEBHOOK] User data found. SlackUserId: ${userData.slackUserId}`);
        if (userData.slackUserId) {
          // Get all channels this user has linked
          const linkedChannelsSnapshot = await db.collection('user_linked_channels')
            .where('slackUserId', '==', userData.slackUserId)
            .get();
          
          console.log(`[WEBHOOK] Found ${linkedChannelsSnapshot.size} linked channels for user`);
          
          linkedChannelsSnapshot.forEach(doc => {
            const channelData = doc.data();
            console.log(`[WEBHOOK] Channel data:`, channelData);
            if (channelData.channelId) {
              channelsToPost.add(channelData.channelId);
              console.log(`[WEBHOOK] Adding user-linked channel: ${channelData.channelId}`);
            }
          });
        } else {
          console.log('[WEBHOOK] User does not have slackUserId field');
        }
      } else {
        console.log(`[WEBHOOK] User document not found for ID: ${vehicle.teamMemberId}`);
      }
    }
    
    if (channelsToPost.size === 0) {
      console.log(`[WEBHOOK] No channels configured for updates`);
      console.log(`[WEBHOOK] Debug: postToTeamChannel=${postToTeamChannel}, postToUserChannels=${postToUserChannels}`);
      console.log(`[WEBHOOK] Debug: teamId=${teamId}, vehicle.teamMemberId=${vehicle.teamMemberId}`);
      return res.status(404).json({ error: 'No channels configured for updates' });
    }
    
    // Add team name to vehicle data
    vehicle.teamName = teamName;
    if (teamDoc) {
      vehicle.teamId = teamDoc.id;
    }
    
    console.log(`[WEBHOOK] Posting to ${channelsToPost.size} channel(s)`);
    
    // Build appropriate message based on update type
    let message = '';
    let attachments = [];
    
    switch (updateType) {
      case 'new':
        message = `🚗 New vehicle added: ${vehicle.vehicle}`;
        
        // Use the full buildVehicleCard to match the web UI
        attachments = buildVehicleCard(vehicle, { useColor: true });
        
        // Add a header to indicate it's a new vehicle
        attachments[0].blocks.unshift(
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `🚗 *New Vehicle Added*\nAdded by: *${vehicle.teamMemberName || 'Team Member'}*`
            }
          },
          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: `Added at ${new Date().toLocaleString()}`
              }
            ]
          },
          {
            type: 'divider'
          }
        );
        break;
        
      case 'in_route':
        message = `🚚 ${vehicle.inRouteDriverName} is in route to ${vehicle.vehicle}`;
        
        // Send full updated vehicle card
        attachments = buildVehicleCard(vehicle, { useColor: true });
        attachments[0].blocks.unshift(
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `🚚 *Driver Update*\n${vehicle.inRouteDriverName} is now IN ROUTE to ${vehicle.vehicle}`
            }
          },
          {
            type: 'divider'
          }
        );
        break;
        
      case 'arrived':
        message = `📍 ${vehicle.arrivedDriverName} has arrived at ${vehicle.vehicle}`;
        
        // Send full updated vehicle card
        attachments = buildVehicleCard(vehicle, { useColor: true });
        attachments[0].blocks.unshift(
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `📍 *Driver Update*\n${vehicle.arrivedDriverName} has ARRIVED at ${vehicle.vehicle}`
            }
          },
          {
            type: 'divider'
          }
        );
        break;
        
      case 'secured':
        message = `✅ ${vehicle.vehicle} secured by ${vehicle.securedByUserName}`;
        attachments = [{
          color: getStatusColor({ status: 'SECURED' }),
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `✅ *Vehicle Secured!*\n${vehicle.vehicle} (VIN: ${vehicle.vin || 'No VIN'}) secured by ${vehicle.securedByUserName}`
              }
            },
            {
              type: 'context',
              elements: [
                {
                  type: 'mrkdwn',
                  text: `Secured at ${new Date().toLocaleString()}`
                }
              ]
            }
          ]
        }];
        break;
        
      // ENHANCED: Recovery completed case with detailed reporting
      case 'recovery_completed':
        console.log('[WEBHOOK] Processing recovery completion...');
        message = `🏆 ${vehicle.vehicle} recovery completed by ${vehicle.securedByUserName}`;
        
        // Create comprehensive recovery completion message
        const recoveryBlocks = [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: '🏆 Vehicle Recovery Completed!',
              emoji: true
            }
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*${vehicle.vehicle}* (VIN: ${vehicle.vin || 'No VIN'})\nRecovered by: *${vehicle.securedByUserName}*`
            }
          }
        ];
        
        // Add recovery summary if available
        if (recoveryData) {
          console.log('[WEBHOOK] Adding recovery summary to message...');
          recoveryBlocks.push({
            type: 'divider'
          });
          
          // Quick recovery summary
          let summaryText = '*🔍 Recovery Summary:*\n';
          
          if (recoveryData.vehicleCondition) {
            summaryText += `• Exterior: ${recoveryData.vehicleCondition.exterior || 'Not assessed'} | Interior: ${recoveryData.vehicleCondition.interior || 'Not assessed'}\n`;
          }
          
          if (recoveryData.personalPropertyNotes || recoveryData.vehicleCondition?.personalItems) {
            const personalItems = recoveryData.personalPropertyNotes || recoveryData.vehicleCondition?.personalItems;
            summaryText += `• Personal Property: ${personalItems && personalItems.trim() !== '' ? 'Items found and documented' : 'No items found'}\n`;
          }
          
          // Count total photos
          let totalPhotos = 0;
          if (recoveryData.photoURLs) {
            Object.values(recoveryData.photoURLs).forEach(urls => {
              if (Array.isArray(urls)) {
                totalPhotos += urls.filter(url => url && url.trim() !== '').length;
              } else if (urls && typeof urls === 'string' && urls.trim() !== '') {
                totalPhotos += 1;
              }
            });
          }
          
          if (totalPhotos > 0) {
            summaryText += `• Photos Captured: ${totalPhotos} images\n`;
          }
          
          if (recoveryData.checklistCompleted || recoveryData.checklist) {
            const checklist = recoveryData.checklistCompleted || recoveryData.checklist;
            const completedItems = Object.values(checklist).filter(item => item === true).length;
            const totalItems = Object.keys(checklist).length;
            summaryText += `• Checklist: ${completedItems}/${totalItems} items completed\n`;
          }
          
          recoveryBlocks.push({
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: summaryText
            }
          });
        }
        
        recoveryBlocks.push({
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Recovery completed at ${new Date().toLocaleString()}`
            }
          ]
        });
        
        attachments = [{
          color: getStatusColor({ status: 'SECURED' }),
          blocks: recoveryBlocks
        }];
        break;
        
      case 'bottom_status':
        message = `⚠️ ${vehicle.vehicle} marked as ${vehicle.bottomStatus}`;
        
        // Send full updated vehicle card
        attachments = buildVehicleCard(vehicle, { useColor: true });
        attachments[0].blocks.unshift(
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `⚠️ *Bottom Status Update*\n${vehicle.vehicle} marked as ${vehicle.bottomStatus} (Attempt ${vehicle.bottomStatusCount || 1}/3)`
            }
          }
        );
        if (vehicle.cantSecureReason) {
          attachments[0].blocks.splice(2, 0, {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `📋 *Reason:* ${vehicle.cantSecureReason}`
            }
          });
        }
        attachments[0].blocks.splice(vehicle.cantSecureReason ? 3 : 2, 0, {
          type: 'divider'
        });
        break;
        
      default:
        message = `Vehicle update: ${vehicle.vehicle}`;
        attachments = buildVehicleCard(vehicle, { useColor: true });
    }
    
    // Post to all channels
    const results = [];
    for (const channel of channelsToPost) {
      try {
        console.log(`[WEBHOOK] Posting to Slack channel ${channel}...`);
        
        const result = await app.client.chat.postMessage({
          channel: channel,
          text: message,
          attachments: attachments
        });
        
        results.push({ 
          channel, 
          success: true, 
          timestamp: result.ts 
        });
        
        console.log(`[WEBHOOK] Successfully posted to channel: ${channel}, message ts: ${result.ts}`);
        
        // ENHANCED: For recovery completion, also post detailed report with ALL PHOTOS
        if (updateType === 'recovery_completed' && recoveryData) {
          console.log(`[WEBHOOK] Posting detailed recovery report to channel: ${channel}`);
          try {
            await postDetailedRecoveryReport(channel, vehicle, recoveryData);
            console.log(`[WEBHOOK] Detailed recovery report posted successfully to channel: ${channel}`);
          } catch (detailError) {
            console.error(`[WEBHOOK] Failed to post detailed recovery report to channel ${channel}:`, detailError);
            // Don't fail the main webhook, just log the error
          }
        }
        
      } catch (error) {
        console.error(`[WEBHOOK] Failed to post to channel ${channel}:`, error);
        results.push({ 
          channel, 
          success: false, 
          error: error.message 
        });
      }
    }
    
    res.json({ 
      success: true, 
      message: 'Posted to Slack', 
      results: results,
      team: teamName,
      recoveryDataProcessed: !!recoveryData,
      detailedReportPosted: updateType === 'recovery_completed' && !!recoveryData
    });
    
  } catch (error) {
    console.error('[WEBHOOK] Error processing update:', error);
    console.error('[WEBHOOK] Error stack:', error.stack);
    res.status(500).json({ error: 'Failed to process update', details: error.message });
  }
});

// NEW: OAuth endpoint for the React app /bot page
webhookApp.post('/api/slack-oauth', async (req, res) => {
  console.log('[OAUTH] Received OAuth exchange request');
  
  const { code, redirectUri } = req.body;

  if (!code) {
    return res.status(400).json({ error: 'Missing OAuth code' });
  }

  try {
    // Exchange code for access token
    const response = await axios.post('https://slack.com/api/oauth.v2.access', null, {
      params: {
        client_id: process.env.SLACK_CLIENT_ID,
        client_secret: process.env.SLACK_CLIENT_SECRET,
        code: code,
        redirect_uri: redirectUri || 'https://recoveriqs.net/bot'
      }
    });

    const { ok, access_token, team, authed_user, bot_user_id, app_id } = response.data;

    if (!ok) {
      console.error('[OAUTH] Slack OAuth error:', response.data);
      return res.status(400).json({ error: 'OAuth failed', details: response.data.error });
    }

    console.log(`[OAUTH] Successfully authenticated for team: ${team.name}`);

    // Store installation data
    await db.collection('slack_installations').doc(team.id).set({
      teamId: team.id,
      teamName: team.name,
      accessToken: access_token,
      botUserId: bot_user_id,
      appId: app_id,
      installedBy: authed_user.id,
      installedAt: admin.firestore.FieldValue.serverTimestamp(),
      isActive: true
    });

    // Check/create team
    const teamQuery = await db.collection('teams')
      .where('slackTeamId', '==', team.id)
      .limit(1)
      .get();

    if (teamQuery.empty) {
      await db.collection('teams').add({
        name: team.name,
        slackTeamId: team.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log(`[OAUTH] Created new team: ${team.name}`);
    } else {
      // Update existing team
      await teamQuery.docs[0].ref.update({
        slackInstallation: {
          installed: true,
          installedAt: admin.firestore.FieldValue.serverTimestamp(),
          botUserId: bot_user_id
        }
      });
      console.log(`[OAUTH] Updated existing team: ${team.name}`);
    }

    // Send welcome message
    try {
      const slackClient = new WebClient(access_token);
      await slackClient.chat.postMessage({
        channel: authed_user.id,
        text: 'Welcome to Vehicle Tracker Bot! 🚗',
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `👋 *Welcome to Vehicle Tracker Bot!*\n\nSuccessfully installed in *${team.name}*!`
            }
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '*Getting Started:*\n1. Add me to a channel: `/invite @Vehicle Tracker`\n2. Link teams: `/vehicle setup link`\n3. View vehicles: `/vehicle list`\n4. Get help: `/vehicle help`'
            }
          },
          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: 'Need help? Contact <EMAIL>'
              }
            ]
          }
        ]
      });
      console.log('[OAUTH] Welcome message sent');
    } catch (msgError) {
      console.error('[OAUTH] Failed to send welcome message:', msgError);
      // Don't fail the installation if message fails
    }

    res.json({
      success: true,
      team: {
        id: team.id,
        name: team.name
      },
      message: 'Bot successfully installed!'
    });

  } catch (error) {
    console.error('[OAUTH] Error:', error);
    res.status(500).json({ 
      error: 'Failed to complete installation',
      details: error.message 
    });
  }
});

// Health check endpoint
webhookApp.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Debug endpoint to list all teams
webhookApp.get('/api/teams/list', async (req, res) => {
  try {
    const teamsSnapshot = await db.collection('teams').get();
    const teams = teamsSnapshot.docs.map(doc => ({
      id: doc.id,
      name: doc.data().name || 'Unnamed Team',
      hasSlackChannel: !!doc.data().slackChannel,
      slackChannel: doc.data().slackChannel || 'None',
      slackTeamId: doc.data().slackTeamId || 'None',
      members: doc.data().teamMembers?.length || 0,
      areaData: doc.data().areaData || null
    }));
    
    res.json({ teams });
  } catch (error) {
    console.error('Error listing teams:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test slash command to verify Socket Mode is working
app.command('/vehicle-test', async ({ command, ack, say }) => {
  console.log('[TEST] Received vehicle-test command');
  await ack();
  await say('✅ Bot is responding to slash commands!');
});

// Generate vehicle card image with DO NOT SECURE status
const generateVehicleCardImage = async (vehicle, team) => {
  if (!createCanvas) {
    console.log('Canvas not available for image generation');
    return null;
  }
  
  const canvas = createCanvas(500, 1000);
  const ctx = canvas.getContext('2d');
  
  // Background
  ctx.fillStyle = '#1a1a1a';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // Header background
  const statusColor = vehicle.bottomStatus ? '#dc2626' :
                     vehicle.status === 'SECURED' ? '#059669' :
                     vehicle.status === 'DO NOT SECURE' ? '#9333ea' :
                     vehicle.status === 'FOUND' ? '#d97706' :
                     vehicle.status === 'PENDING PICKUP' ? '#2563eb' : '#dc2626';
  
  ctx.fillStyle = statusColor;
  ctx.fillRect(0, 0, canvas.width, 70);
  
  // Status text
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 24px Arial';
  ctx.fillText(vehicle.status || 'UNKNOWN', 20, 45);
  
  // Time in top right
  ctx.font = '14px Arial';
  ctx.fillText(new Date().toLocaleString(), canvas.width - 180, 30);
  ctx.font = '12px Arial';
  ctx.fillText('Export Time', canvas.width - 180, 50);
  
  // Vehicle name
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 28px Arial';
  ctx.fillText(vehicle.vehicle || 'Unknown Vehicle', 20, 110);
  
  // VIN Section
  ctx.fillStyle = '#dc2626';
  ctx.fillRect(20, 125, 200, 40);
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 20px Arial';
  ctx.fillText(`VIN: ${vehicle.vin || 'N/A'}`, 30, 150);
  
  // VIN Verified badge
  if (vehicle.vinVerified) {
    ctx.fillStyle = '#059669';
    ctx.fillRect(230, 125, 120, 40);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Arial';
    ctx.fillText('✓ VERIFIED', 245, 150);
  } else {
    ctx.fillStyle = '#6b7280';
    ctx.fillRect(230, 125, 130, 40);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Arial';
    ctx.fillText('NOT VERIFIED', 240, 150);
  }
  
  // Information fields
  let yPos = 190;
  ctx.font = '14px Arial';
  
  // Two column layout for info
  const leftFields = [
    { label: 'From:', value: vehicle.teamMemberName },
    { label: 'Date:', value: vehicle.date },
    { label: 'Created:', value: vehicle.createdAt ? formatTimestamp(vehicle.createdAt) : 'Unknown' },
    { label: 'Week:', value: vehicle.weekRange },
    { label: 'Color:', value: vehicle.color || 'Not provided' },
    { label: 'Drive Type:', value: vehicle.driveType || 'Not provided' }
  ];
  
  const rightFields = [
    { label: 'Plate #:', value: vehicle.plateNumber || 'Not provided' },
    { label: 'Account #:', value: vehicle.accountNumber || 'Not provided' },
    { label: 'Financier:', value: vehicle.financier || 'Not provided' }
  ];
  
  // Draw left column
  leftFields.forEach(field => {
    ctx.fillStyle = '#9ca3af';
    ctx.font = 'bold 12px Arial';
    ctx.fillText(field.label, 20, yPos);
    ctx.fillStyle = '#ffffff';
    ctx.font = '14px Arial';
    ctx.fillText(field.value, 20, yPos + 18);
    yPos += 40;
  });
  
  // Draw right column
  yPos = 190;
  rightFields.forEach(field => {
    ctx.fillStyle = '#9ca3af';
    ctx.font = 'bold 12px Arial';
    ctx.fillText(field.label, 260, yPos);
    ctx.fillStyle = '#ffffff';
    ctx.font = '14px Arial';
    ctx.fillText(field.value, 260, yPos + 18);
    yPos += 40;
  });
  
  yPos = 430;
  
  // DO NOT SECURE reason if present
  if (vehicle.status === 'DO NOT SECURE' && vehicle.doNotSecureReason) {
    ctx.fillStyle = '#9333ea';
    ctx.fillRect(20, yPos, canvas.width - 40, 60);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Arial';
    ctx.fillText('🚫 DO NOT SECURE REASON:', 30, yPos + 25);
    ctx.font = '14px Arial';
    ctx.fillText(vehicle.doNotSecureReason, 30, yPos + 45);
    yPos += 70;
  }
  
  // Address Section
  if (vehicle.address) {
    ctx.fillStyle = '#2563eb';
    ctx.fillRect(20, yPos, canvas.width - 40, 80);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 14px Arial';
    ctx.fillText('📍 VEHICLE LOCATION:', 30, yPos + 20);
    ctx.font = '16px Arial';
    
    // Word wrap for address
    const words = vehicle.address.split(' ');
    let line = '';
    let lineY = yPos + 45;
    
    words.forEach((word, i) => {
      const testLine = line + word + ' ';
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if (testWidth > canvas.width - 60 && i > 0) {
        ctx.fillText(line, 30, lineY);
        line = word + ' ';
        lineY += 22;
      } else {
        line = testLine;
      }
    });
    ctx.fillText(line, 30, lineY);
    yPos += 90;
  }
  
  // GPS Coordinates
  if (vehicle.position) {
    ctx.fillStyle = '#059669';
    ctx.fillRect(20, yPos, canvas.width - 40, 50);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 14px Arial';
    ctx.fillText('GPS COORDINATES:', 30, yPos + 20);
    ctx.font = '16px Arial';
    ctx.fillText(`Lat: ${vehicle.position.lat.toFixed(6)}, Lng: ${vehicle.position.lng.toFixed(6)}`, 30, yPos + 40);
    yPos += 60;
  }
  
  // Bottom status if present
  if (vehicle.bottomStatus) {
    ctx.fillStyle = '#dc2626';
    ctx.fillRect(20, yPos, canvas.width - 40, 50);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 18px Arial';
    ctx.fillText(`⚠️ ${vehicle.bottomStatus}`, 30, yPos + 30);
    ctx.font = '14px Arial';
    ctx.fillText(`Attempt ${vehicle.bottomStatusCount || 1}/3`, canvas.width - 120, yPos + 30);
    yPos += 60;
  }
  
  // Notes section
  if (vehicle.notes) {
    ctx.fillStyle = '#374151';
    ctx.fillRect(20, yPos, canvas.width - 40, 80);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 14px Arial';
    ctx.fillText('📝 NOTES:', 30, yPos + 20);
    ctx.font = '12px Arial';
    
    // Word wrap for notes
    const noteWords = vehicle.notes.split(' ');
    let noteLine = '';
    let noteY = yPos + 40;
    let lineCount = 0;
    
    noteWords.forEach((word, i) => {
      const testLine = noteLine + word + ' ';
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if ((testWidth > canvas.width - 60 && i > 0) || lineCount >= 3) {
        if (lineCount < 3) {
          ctx.fillText(noteLine, 30, noteY);
          noteLine = word + ' ';
          noteY += 18;
          lineCount++;
        }
      } else {
        noteLine = testLine;
      }
    });
    if (lineCount < 3) {
      ctx.fillText(noteLine, 30, noteY);
    }
    yPos += 90;
  }
  
  // Footer
  ctx.fillStyle = '#4b5563';
  ctx.font = '10px Arial';
  ctx.fillText(`Vehicle ID: ${vehicle.id || 'N/A'}`, 20, canvas.height - 40);
  ctx.fillText(`Generated: ${new Date().toLocaleString()}`, 20, canvas.height - 25);
  ctx.fillText(`Team: ${team?.name || vehicle.teamName || 'N/A'}`, 20, canvas.height - 10);
  ctx.fillText('NWRepo Vehicle Tracker', canvas.width - 150, canvas.height - 10);
  
  return canvas.toBuffer('image/jpeg', { quality: 0.9 });
};

// Helper to load vehicles for a specific team
const loadTeamVehicles = async (teamId) => {
  const vehicles = [];
  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

  try {
    // Get team document
    const teamDoc = await db.collection('teams').doc(teamId).get();
    if (!teamDoc.exists) {
      return [];
    }
    
    const teamData = teamDoc.data();
    const teamName = teamData.name || 'Unknown Team';
    
    // Get team members from subcollection
    const teamMembersSnapshot = await db.collection('teams')
      .doc(teamId)
      .collection('teamMembers')
      .get();
    
    const teamMemberIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);
    
    // Load vehicles for each team member
    for (const memberId of teamMemberIds) {
      const memberDoc = await db.collection('users').doc(memberId).get();
      const memberData = memberDoc.exists ? memberDoc.data() : {};
      const memberName = memberData.displayName || memberData.email?.split('@')[0] || 'Team Member';

      const weeksSnapshot = await db
        .collection('users')
        .doc(memberId)
        .collection('vehicleWeeks')
        .orderBy('startDate', 'desc')
        .get();

      for (const weekDoc of weeksSnapshot.docs) {
        const weekData = weekDoc.data();
        const weekStartDate = weekData.startDate?.toDate();

        if (weekStartDate && weekStartDate >= threeMonthsAgo) {
          const vehiclesSnapshot = await db
            .collection('users')
            .doc(memberId)
            .collection('vehicleWeeks')
            .doc(weekDoc.id)
            .collection('vehicles')
            .get();

          vehiclesSnapshot.docs.forEach(vehicleDoc => {
            const vehicleData = vehicleDoc.data();
            
            if ((vehicleData.status === 'FOUND' || 
                 vehicleData.status === 'PENDING PICKUP' || 
                 vehicleData.status === 'DO NOT SECURE' ||
                 vehicleData.status === 'NOT FOUND') &&
                vehicleData.status !== 'SECURED' &&
                !vehicleData.securedByTeammate && 
                !vehicleData.isNeverSecured) {
              
              vehicles.push({
                id: vehicleDoc.id,
                ...vehicleData,
                teamId: teamId,
                teamName: teamName,
                teamMemberId: memberId,
                teamMemberName: memberName,
                weekId: weekDoc.id,
                weekRange: weekData.displayRange || 'Unknown Week',
                uniqueKey: `${memberId}_${weekDoc.id}_${vehicleDoc.id}`
              });
            }
          });
        }
      }
    }

    // Sort vehicles
    vehicles.sort((a, b) => {
      // Prioritize PENDING PICKUP vehicles at the top
      if (a.status === 'PENDING PICKUP' && b.status !== 'PENDING PICKUP') return -1;
      if (a.status !== 'PENDING PICKUP' && b.status === 'PENDING PICKUP') return 1;
      
      // Then vehicles with drivers
      if ((a.inRouteDriverId || a.arrivedDriverId) && !(b.inRouteDriverId || b.arrivedDriverId)) return -1;
      if (!(a.inRouteDriverId || a.arrivedDriverId) && (b.inRouteDriverId || b.arrivedDriverId)) return 1;
      
      // Then non-bottom status before bottom status
      if (!a.bottomStatus && b.bottomStatus) return -1;
      if (a.bottomStatus && !b.bottomStatus) return 1;
      
      // Finally by date
      const dateA = new Date(a.date || '1970-01-01');
      const dateB = new Date(b.date || '1970-01-01');
      return dateB.getTime() - dateA.getTime();
    });

    return vehicles;
  } catch (error) {
    console.error('Error loading team vehicles:', error);
    return [];
  }
};

// Load vehicles for a specific user
const loadUserVehicles = async (userId) => {
  const vehicles = [];
  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

  try {
    // First check if user has a linked account
    const linkedUser = await getLinkedUser(userId);
    
    if (!linkedUser) {
      console.log(`[DEBUG] No linked account for Slack user: ${userId}`);
      return [];
    }
    
    const firebaseUserId = linkedUser.id;
    const memberName = linkedUser.displayName || linkedUser.email?.split('@')[0] || 'You';

    console.log(`[DEBUG] Found linked user ${memberName} with Firebase ID: ${firebaseUserId}`);

    // Find user's team
    let teamName = 'No Team';
    let teamId = null;
    const teamsSnapshot = await db.collection('teams').get();
    for (const teamDoc of teamsSnapshot.docs) {
      const teamMembersSnapshot = await db.collection('teams')
        .doc(teamDoc.id)
        .collection('teamMembers')
        .where('userId', '==', firebaseUserId)
        .limit(1)
        .get();
      
      if (!teamMembersSnapshot.empty) {
        teamName = teamDoc.data().name || 'Unknown Team';
        teamId = teamDoc.id;
        break;
      }
    }

    const weeksSnapshot = await db
      .collection('users')
      .doc(firebaseUserId)
      .collection('vehicleWeeks')
      .orderBy('startDate', 'desc')
      .get();

    for (const weekDoc of weeksSnapshot.docs) {
      const weekData = weekDoc.data();
      const weekStartDate = weekData.startDate?.toDate();

      if (weekStartDate && weekStartDate >= threeMonthsAgo) {
        const vehiclesSnapshot = await db
          .collection('users')
          .doc(firebaseUserId)
          .collection('vehicleWeeks')
          .doc(weekDoc.id)
          .collection('vehicles')
          .get();

        vehiclesSnapshot.docs.forEach(vehicleDoc => {
          const vehicleData = vehicleDoc.data();
          
          if ((vehicleData.status === 'FOUND' || 
               vehicleData.status === 'PENDING PICKUP' || 
               vehicleData.status === 'DO NOT SECURE' ||
               vehicleData.status === 'NOT FOUND') &&
              vehicleData.status !== 'SECURED' &&
              !vehicleData.securedByTeammate && 
              !vehicleData.isNeverSecured) {
            
            vehicles.push({
              id: vehicleDoc.id,
              ...vehicleData,
              teamId: teamId,
              teamName: teamName,
              teamMemberId: firebaseUserId,
              teamMemberName: memberName,
              weekId: weekDoc.id,
              weekRange: weekData.displayRange || 'Unknown Week',
              uniqueKey: `${firebaseUserId}_${weekDoc.id}_${vehicleDoc.id}`,
              isOwnVehicle: true
            });
          }
        });
      }
    }

    console.log(`[DEBUG] Found ${vehicles.length} vehicles for user ${memberName}`);
    return vehicles;
  } catch (error) {
    console.error('Error loading user vehicles:', error);
    return [];
  }
};

// Load vehicles from ALL teams with proper subcollection structure
const loadAllTeamsVehicles = async (currentUserId = null) => {
  const allVehicles = [];
  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

  try {
    // Get ALL teams
    const teamsSnapshot = await db.collection('teams').get();
    
    console.log(`[DEBUG] Loading vehicles from ${teamsSnapshot.size} teams`);

    for (const teamDoc of teamsSnapshot.docs) {
      const teamData = teamDoc.data();
      const teamName = teamData.name || 'Unknown Team';
      
      // Get team members from the subcollection 'teamMembers'
      const teamMembersSnapshot = await db.collection('teams')
        .doc(teamDoc.id)
        .collection('teamMembers')
        .get();
      
      // Extract userIds from the teamMembers subcollection
      const teamMemberIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);
      
      console.log(`[DEBUG] Processing team: ${teamName} with ${teamMemberIds.length} members`);

      for (const memberId of teamMemberIds) {
        const memberDoc = await db.collection('users').doc(memberId).get();
        const memberData = memberDoc.exists ? memberDoc.data() : {};
        const memberName = memberData.displayName || memberData.email?.split('@')[0] || 'Team Member';
        const isCurrentUser = memberData.slackUserId === currentUserId;

        const weeksSnapshot = await db
          .collection('users')
          .doc(memberId)
          .collection('vehicleWeeks')
          .orderBy('startDate', 'desc')
          .get();

        for (const weekDoc of weeksSnapshot.docs) {
          const weekData = weekDoc.data();
          const weekStartDate = weekData.startDate?.toDate();

          if (weekStartDate && weekStartDate >= threeMonthsAgo) {
            const vehiclesSnapshot = await db
              .collection('users')
              .doc(memberId)
              .collection('vehicleWeeks')
              .doc(weekDoc.id)
              .collection('vehicles')
              .get();

            vehiclesSnapshot.docs.forEach(vehicleDoc => {
              const vehicleData = vehicleDoc.data();
              
              if ((vehicleData.status === 'FOUND' || 
                   vehicleData.status === 'PENDING PICKUP' || 
                   vehicleData.status === 'DO NOT SECURE' ||
                   vehicleData.status === 'NOT FOUND') &&
                  vehicleData.status !== 'SECURED' &&
                  !vehicleData.securedByTeammate && 
                  !vehicleData.isNeverSecured) {
                
                allVehicles.push({
                  id: vehicleDoc.id,
                  ...vehicleData,
                  teamId: teamDoc.id,
                  teamName: teamName,
                  teamMemberId: memberId,
                  teamMemberName: memberName,
                  weekId: weekDoc.id,
                  weekRange: weekData.displayRange || 'Unknown Week',
                  uniqueKey: `${memberId}_${weekDoc.id}_${vehicleDoc.id}`,
                  isOwnVehicle: isCurrentUser
                });
              }
            });
          }
        }
      }
    }

    console.log(`[DEBUG] Total vehicles found across all teams: ${allVehicles.length}`);

    // Sort vehicles
    allVehicles.sort((a, b) => {
      // Prioritize PENDING PICKUP vehicles at the top
      if (a.status === 'PENDING PICKUP' && b.status !== 'PENDING PICKUP') return -1;
      if (a.status !== 'PENDING PICKUP' && b.status === 'PENDING PICKUP') return 1;
      
      // Then vehicles with drivers
      if ((a.inRouteDriverId || a.arrivedDriverId) && !(b.inRouteDriverId || b.arrivedDriverId)) return -1;
      if (!(a.inRouteDriverId || a.arrivedDriverId) && (b.inRouteDriverId || b.arrivedDriverId)) return 1;
      
      // Then non-bottom status before bottom status
      if (!a.bottomStatus && b.bottomStatus) return -1;
      if (a.bottomStatus && !b.bottomStatus) return 1;
      
      // Finally by date
      const dateA = new Date(a.date || '1970-01-01');
      const dateB = new Date(b.date || '1970-01-01');
      return dateB.getTime() - dateA.getTime();
    });

    return allVehicles;

  } catch (error) {
    console.error('Error loading all teams vehicles:', error);
    return [];
  }
};

// Get team linked to current channel
async function getChannelTeam(channelId) {
  const teamsSnapshot = await db.collection('teams')
    .where('slackChannel', '==', channelId)
    .limit(1)
    .get();
  
  if (!teamsSnapshot.empty) {
    const teamDoc = teamsSnapshot.docs[0];
    return { id: teamDoc.id, ...teamDoc.data() };
  }
  
  return null;
}

// Global leaderboard function with proper stats calculations
async function showGlobalLeaderboard(say, client) {
  try {
    await say('🏆 Loading global leaderboard...');
    
    const allUsers = [];
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    // Get all users
    const usersSnapshot = await db.collection('users').get();
    
    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();
      const userId = userDoc.id;
      
      // Find user's team
      let teamName = 'No Team';
      const teamsSnapshot = await db.collection('teams').get();
      for (const teamDoc of teamsSnapshot.docs) {
        const teamMembersSnapshot = await db.collection('teams')
          .doc(teamDoc.id)
          .collection('teamMembers')
          .where('userId', '==', userId)
          .limit(1)
          .get();
        
        if (!teamMembersSnapshot.empty) {
          teamName = teamDoc.data().name || 'Unknown Team';
          break;
        }
      }
      
      // Get user's current stats
      let totalSecured = 0;
      let monthlySecured = 0;
      let totalScans = 0;
      let monthlyScans = 0;
      let totalUploaded = 0;
      let monthlyUploaded = 0;
      
      // Load all weeks for this user
      const weeksSnapshot = await db
        .collection('users')
        .doc(userId)
        .collection('vehicleWeeks')
        .get();
      
      for (const weekDoc of weeksSnapshot.docs) {
        const weekData = weekDoc.data();
        const weekStartDate = weekData.startDate?.toDate();
        
        if (weekStartDate) {
          const weekMonth = weekStartDate.getMonth();
          const weekYear = weekStartDate.getFullYear();
          const isCurrentMonth = weekMonth === currentMonth && weekYear === currentYear;
          
          // Count vehicles in this week
          const vehiclesSnapshot = await db
            .collection('users')
            .doc(userId)
            .collection('vehicleWeeks')
            .doc(weekDoc.id)
            .collection('vehicles')
            .get();
          
          let weekSecured = 0;
          let weekUploaded = 0;
          
          vehiclesSnapshot.docs.forEach(vehicleDoc => {
            const vehicleData = vehicleDoc.data();
            
            // Count all non-carryover vehicles as uploaded
            if (!vehicleData.carriedOver) {
              weekUploaded++;
              totalUploaded++;
              if (isCurrentMonth) {
                monthlyUploaded++;
              }
            }
            
            // Count secured vehicles
            if (vehicleData.status === 'SECURED') {
              weekSecured++;
              totalSecured++;
              if (isCurrentMonth) {
                monthlySecured++;
              }
            }
          });
          
          // Add week stats
          const weekScans = weekData.totalScans || 0;
          totalScans += weekScans;
          
          if (isCurrentMonth) {
            monthlyScans += weekScans;
          }
        }
      }
      
      allUsers.push({
        id: userId,
        displayName: userData.displayName || userData.email?.split('@')[0] || 'Unknown',
        teamName: teamName,
        totalSecured: totalSecured,
        monthlySecured: monthlySecured,
        totalScans: totalScans,
        monthlyScans: monthlyScans,
        totalUploaded: totalUploaded,
        monthlyUploaded: monthlyUploaded,
        recoveryRate: totalUploaded > 0 ? Math.round((totalSecured / totalUploaded) * 100) : 0
      });
    }
    
    // Sort by total secured (descending)
    allUsers.sort((a, b) => b.totalSecured - a.totalSecured);
    
    // Build leaderboard blocks
    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '🏆 Global Vehicle Recovery Leaderboard',
          emoji: true
        }
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*All-Time Leaders* | ${new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`
        }
      },
      {
        type: 'divider'
      }
    ];
    
    // Top 10 users
    const top10 = allUsers.slice(0, 10);
    
    top10.forEach((user, index) => {
      const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
      
      blocks.push({
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `${medal} *${user.displayName}*\n_${user.teamName}_`
          },
          {
            type: 'mrkdwn',
            text: `*Secured:* ${user.totalSecured} (${user.monthlySecured} this month)\n*Uploaded:* ${user.totalUploaded} | *Rate:* ${user.recoveryRate}%`
          }
        ]
      });
    });
    
    // Monthly leaders section
    const monthlyLeaders = [...allUsers].sort((a, b) => b.monthlySecured - a.monthlySecured).slice(0, 3);
    
    blocks.push(
      {
        type: 'divider'
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*🌟 This Month\'s Top Performers*'
        }
      }
    );
    
    monthlyLeaders.forEach((user, index) => {
      const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉';
      blocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `${medal} ${user.displayName} (${user.teamName}) - ${user.monthlySecured} secured, ${user.monthlyUploaded} uploaded`
          }
        ]
      });
    });
    
    await say({
      text: 'Global Leaderboard',
      blocks: blocks
    });
    
  } catch (error) {
    console.error('Error showing global leaderboard:', error);
    await say('❌ Error loading leaderboard. Please try again.');
  }
}

// ENHANCED: Employee of the Month nomination handler
async function handleNominationCommand(say, client, command, args) {
  const nominatorId = command.user_id;
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  
  try {
    // Get nominator info
    const nominator = await getLinkedUser(nominatorId);
    if (!nominator) {
      await say('❌ Please link your VehicleTracker account first using `/vehicle link [url]`');
      return;
    }
    
    // Check if we're in nomination period (20th-27th of month)
    const currentDay = new Date().getDate();
    const isNominationPeriod = currentDay >= 20 && currentDay <= 27;
    
    if (!isNominationPeriod) {
      await say({
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `❌ *Nominations are closed*\n\nNominations are only open from the 20th to 27th of each month.\nCurrent date: ${new Date().toLocaleDateString()}`
            }
          }
        ]
      });
      return;
    }
    
    // Parse command
    const userMatch = args[1]?.match(/<@([A-Z0-9]+)\|?.*?>/);
    if (!userMatch) {
      await say({
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '*How to nominate someone for Employee of the Month:*\n\n`/vehicle nominate @username [reason]`\n\nExample:\n`/vehicle nominate @john Great teamwork and secured 50 vehicles this month!`'
            }
          },
          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: `📅 Nomination period: 20th-27th of each month (Currently ${isNominationPeriod ? 'OPEN' : 'CLOSED'})`
              }
            ]
          }
        ]
      });
      return;
    }
    
    const nomineeSlackId = userMatch[1];
    const reason = args.slice(2).join(' ').trim();
    
    if (!reason) {
      await say('❌ Please provide a reason for your nomination.');
      return;
    }
    
    // Get nominee info
    const nomineeInfo = await client.users.info({ user: nomineeSlackId });
    const nomineeUser = await getLinkedUser(nomineeSlackId);
    
    if (!nomineeUser) {
      await say('❌ The nominated user must have their VehicleTracker account linked.');
      return;
    }
    
    // Check if nominator already nominated this month
    const existingNomination = await db.collection('employee_nominations')
      .where('nominatorId', '==', nominator.id)
      .where('month', '==', currentMonth)
      .where('year', '==', currentYear)
      .limit(1)
      .get();
    
    if (!existingNomination.empty) {
      await say('❌ You have already made a nomination this month. You can only nominate once per month.');
      return;
    }
    
    // Check if nominating themselves
    if (nominator.id === nomineeUser.id) {
      await say('❌ You cannot nominate yourself for Employee of the Month.');
      return;
    }
    
    // Create nomination
    await db.collection('employee_nominations').add({
      nominatorId: nominator.id,
      nominatorName: nominator.displayName,
      nomineeId: nomineeUser.id,
      nomineeName: nomineeUser.displayName,
      nomineeSlackId: nomineeSlackId,
      reason: reason,
      month: currentMonth,
      year: currentYear,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      votes: 0,
      voters: []
    });
    
    await say({
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `✅ *Nomination Successful!*\n\nYou have nominated *${nomineeUser.displayName}* for Employee of the Month!\n\n*Reason:* ${reason}`
          }
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: 'Voting will open on the 1st of next month. Use `/vehicle nominations` to see all nominations.'
            }
          ]
        }
      ]
    });
    
  } catch (error) {
    console.error('Error handling nomination:', error);
    await say('❌ Error processing nomination. Please try again.');
  }
}

// ENHANCED: Show nominations with better messaging and voting
async function showNominations(say, client, command) {
  const voterId = command.user_id;
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const currentDay = new Date().getDate();
  
  try {
    // Get voter info
    const voter = await getLinkedUser(voterId);
    if (!voter) {
      await say('❌ Please link your VehicleTracker account first using `/vehicle link [url]`');
      return;
    }
    
    // Determine current phase
    let phase = '';
    let viewingMonth = currentMonth;
    let viewingYear = currentYear;
    
    if (currentDay >= 1 && currentDay <= 7) {
      // Voting period - show last month's nominations
      phase = 'voting';
      viewingMonth = currentMonth - 1;
      if (viewingMonth < 0) {
        viewingMonth = 11;
        viewingYear = currentYear - 1;
      }
    } else if (currentDay >= 20 && currentDay <= 27) {
      // Nomination period
      phase = 'nominating';
    } else {
      phase = 'closed';
    }
    
    // Get nominations for the viewing month
    const nominationsSnapshot = await db.collection('employee_nominations')
      .where('month', '==', viewingMonth)
      .where('year', '==', viewingYear)
      .orderBy('votes', 'desc')
      .get();
    
    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '🌟 Employee of the Month',
          emoji: true
        }
      }
    ];
    
    // Add phase-specific header
    if (phase === 'voting') {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Voting for ${new Date(viewingYear, viewingMonth).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}*\n🗳️ Voting is OPEN until the 7th!`
        }
      });
    } else if (phase === 'nominating') {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Nominations for ${new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}*\n📝 Accepting nominations until the 27th!`
        }
      });
    } else {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*${new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}*\n🔒 Voting and nominations are currently closed`
        }
      });
    }
    
    // Add informative message about the process
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '*📅 Employee of the Month Schedule:*\n• *20th-27th:* Nomination period - Nominate outstanding colleagues\n• *1st-7th:* Voting period - Vote for your choice\n• *8th:* Winner announcement\n\n*How to participate:*\n• To nominate: `/vehicle nominate @username [reason]`\n• To vote: Click the vote button below during voting period (1st-7th)\n• Each person gets one nomination and one vote per month'
      }
    });
    
    blocks.push({
      type: 'divider'
    });
    
    if (nominationsSnapshot.empty) {
      if (phase === 'nominating') {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '📝 *No nominations yet this month!*\n\nBe the first to recognize a colleague\'s outstanding performance!\n\nUse: `/vehicle nominate @username [reason]`'
          }
        });
      } else {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '📋 *No nominations for this period*\n\nNominations will open on the 20th of this month.'
          }
        });
      }
    } else {
      // Show nomination count
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*${nominationsSnapshot.size} Nomination${nominationsSnapshot.size > 1 ? 's' : ''} for ${new Date(viewingYear, viewingMonth).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}:*`
        }
      });
      
      // Check if user has voted
      let userVotedFor = null;
      
      nominationsSnapshot.docs.forEach((doc, index) => {
        const nomination = doc.data();
        const hasVoted = nomination.voters?.includes(voter.id);
        
        if (hasVoted) {
          userVotedFor = nomination.nomineeName;
        }
        
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*${nomination.nomineeName}*\n_Nominated by: ${nomination.nominatorName}_\n"${nomination.reason}"\n\n*Votes: ${nomination.votes || 0}* ${hasVoted ? '(You voted for this person ✓)' : ''}`
          },
          accessory: phase === 'voting' && !userVotedFor ? {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'Vote',
              emoji: true
            },
            style: 'primary',
            action_id: 'vote_employee',
            value: doc.id
          } : undefined
        });
        
        if (index < nominationsSnapshot.docs.length - 1) {
          blocks.push({ type: 'divider' });
        }
      });
      
      if (userVotedFor && phase === 'voting') {
        blocks.push({
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `✅ You have voted for *${userVotedFor}* this month`
            }
          ]
        });
      } else if (phase === 'voting') {
        blocks.push({
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: '👆 Click the "Vote" button above to cast your vote'
            }
          ]
        });
      }
    }
    
    // Add reminder about next phase
    if (phase === 'closed') {
      const daysUntilNominations = 20 - currentDay;
      const daysUntilVoting = currentDay > 7 ? (30 - currentDay + 1) : (1 - currentDay);
      
      blocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: currentDay < 20 ? 
              `⏰ Nominations open in ${daysUntilNominations} days` :
              `⏰ Voting opens in ${Math.abs(daysUntilVoting)} days`
          }
        ]
      });
    }
    
    await say({
      text: 'Employee of the Month',
      blocks: blocks
    });
    
  } catch (error) {
    console.error('Error showing nominations:', error);
    await say('❌ Error loading nominations. Please try again.');
  }
}

// Handle employee vote
app.action('vote_employee', async ({ body, ack, say, client }) => {
  await ack();
  
  const voterId = body.user.id;
  const nominationId = body.actions[0].value;
  
  try {
    // Get voter info
    const voter = await getLinkedUser(voterId);
    if (!voter) {
      await say('❌ Please link your VehicleTracker account first using `/vehicle link [url]`');
      return;
    }
    
    // Get nomination
    const nominationDoc = await db.collection('employee_nominations').doc(nominationId).get();
    if (!nominationDoc.exists) {
      await say('❌ Nomination not found.');
      return;
    }
    
    const nomination = nominationDoc.data();
    
    // Check if already voted
    if (nomination.voters?.includes(voter.id)) {
      await say('❌ You have already voted for this person.');
      return;
    }
    
    // Check if voting for any other nominee this month
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    // For voting period, we're looking at last month's nominations
    let checkMonth = currentMonth - 1;
    let checkYear = currentYear;
    if (checkMonth < 0) {
      checkMonth = 11;
      checkYear = currentYear - 1;
    }
    
    const allNominationsSnapshot = await db.collection('employee_nominations')
      .where('month', '==', checkMonth)
      .where('year', '==', checkYear)
      .get();
    
    let hasVotedElsewhere = false;
    allNominationsSnapshot.docs.forEach(doc => {
      if (doc.id !== nominationId && doc.data().voters?.includes(voter.id)) {
        hasVotedElsewhere = true;
      }
    });
    
    if (hasVotedElsewhere) {
      await say('❌ You have already voted for someone else this month.');
      return;
    }
    
    // Add vote
    await nominationDoc.ref.update({
      votes: admin.firestore.FieldValue.increment(1),
      voters: admin.firestore.FieldValue.arrayUnion(voter.id)
    });
    
    await say(`✅ Your vote for *${nomination.nomineeName}* has been recorded!`);
    
  } catch (error) {
    console.error('Error processing vote:', error);
    await say('❌ Error processing your vote. Please try again.');
  }
});

// NEW: View Order action handler
app.action('view_order', async ({ body, ack, say }) => {
  await ack();
  
  try {
    const value = JSON.parse(body.actions[0].value);
    
    // Get complete vehicle data
    const vehicle = await getCompleteVehicleData(value.teamMemberId, value.weekId, value.vehicleId);
    
    // Build detailed order view
    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📋 Complete Order Details',
          emoji: true
        }
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Vehicle:* ${vehicle.vehicle}\n*VIN:* ${vehicle.vin} ${vehicle.vinVerified ? '✓ Verified' : '❌ Not Verified'}\n*Status:* ${vehicle.status}`
        }
      },
      {
        type: 'divider'
      }
    ];
    
    // Vehicle Details
    const details = [];
    if (vehicle.date) details.push(`*Date:* ${vehicle.date}`);
    if (vehicle.color) details.push(`*Color:* ${vehicle.color}`);
    if (vehicle.driveType) details.push(`*Drive Type:* ${vehicle.driveType}`);
    if (vehicle.plateNumber) details.push(`*Plate #:* ${vehicle.plateNumber}`);
    if (vehicle.accountNumber) details.push(`*Account #:* ${vehicle.accountNumber}`);
    if (vehicle.financier) details.push(`*Financier:* ${vehicle.financier}`);
    
    if (details.length > 0) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: details.join('\n')
        }
      });
    }
    
    // Location
    if (vehicle.address || vehicle.position) {
      blocks.push({
        type: 'divider'
      });
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `📍 *Location Information*\n${vehicle.address || 'No address provided'}`
        }
      });
      
      if (vehicle.position) {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*GPS Coordinates:*\nLatitude: ${vehicle.position.lat}\nLongitude: ${vehicle.position.lng}`
          },
          accessory: {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'Open in Maps',
              emoji: true
            },
            url: `https://maps.google.com/?q=${vehicle.position.lat},${vehicle.position.lng}`,
            action_id: 'open_maps'
          }
        });
      }
    }
    
    // DO NOT SECURE Reason
    if (vehicle.status === 'DO NOT SECURE' && vehicle.doNotSecureReason) {
      blocks.push({
        type: 'divider'
      });
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `🚫 *DO NOT SECURE Reason:*\n${vehicle.doNotSecureReason}`
        }
      });
    }
    
    // Notes
    if (vehicle.notes) {
      blocks.push({
        type: 'divider'
      });
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `📝 *Notes:*\n${vehicle.notes}`
        }
      });
    }
    
    // Metadata
    blocks.push({
      type: 'divider'
    });
    blocks.push({
      type: 'context',
      elements: [
        {
          type: 'mrkdwn',
          text: `*Added by:* ${vehicle.teamMemberName} | *Team:* ${vehicle.teamName} | *Week:* ${vehicle.weekRange}`
        }
      ]
    });
    
    if (vehicle.createdAt) {
      blocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `*Created:* ${formatTimestamp(vehicle.createdAt)}`
          }
        ]
      });
    }
    
    await say({
      text: 'Order Details',
      blocks: blocks
    });
    
  } catch (error) {
    console.error('Error viewing order:', error);
    await say('❌ Error loading order details. Please try again.');
  }
});

// NEW: Reminder preferences command
async function handleReminderPreferences(say, command, args) {
  const channelId = command.channel_id;
  const action = args[1];
  
  try {
    if (!action || !['enable', 'disable', 'status'].includes(action)) {
      // Show current status and options
      const prefs = await db.collection('reminder_preferences').doc(channelId).get();
      const currentPrefs = prefs.exists ? prefs.data() : { enabled: true };
      
      await say({
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*Reminder Preferences for this channel*\n\nCurrent status: *${currentPrefs.enabled !== false ? 'Enabled' : 'Disabled'}*`
            }
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '*Available commands:*\n• `/vehicle reminders enable` - Enable all reminders\n• `/vehicle reminders disable` - Disable all reminders\n• `/vehicle reminders status` - Check current status\n• `/vehicle reminders [minutes]` - Set reminder interval'
            }
          }
        ]
      });
      return;
    }
    
    switch (action) {
      case 'enable':
        await db.collection('reminder_preferences').doc(channelId).set({
          enabled: true,
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedBy: command.user_id
        }, { merge: true });
        
        reminderPreferences.set(channelId, { enabled: true });
        
        await say('✅ Reminders have been *enabled* for this channel. You will receive vehicle reminders and Employee of the Month notifications.');
        break;
        
      case 'disable':
        await db.collection('reminder_preferences').doc(channelId).set({
          enabled: false,
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedBy: command.user_id
        }, { merge: true });
        
        reminderPreferences.set(channelId, { enabled: false });
        
        await say('🔕 Reminders have been *disabled* for this channel. You will not receive any automated notifications.');
        break;
        
      case 'status':
        const prefs = await db.collection('reminder_preferences').doc(channelId).get();
        const currentPrefs = prefs.exists ? prefs.data() : { enabled: true };
        
        await say({
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*Reminder Status:* ${currentPrefs.enabled !== false ? '✅ Enabled' : '🔕 Disabled'}`
              }
            }
          ]
        });
        break;
    }
    
  } catch (error) {
    console.error('Error handling reminder preferences:', error);
    await say('❌ Error updating reminder preferences. Please try again.');
  }
}

// Channel setup command
app.command('/vehicle-setup', async ({ command, ack, say, client }) => {
  await ack();

  try {
    const channelId = command.channel_id;
    const channelName = command.channel_name;

    // Get or create user and team automatically
    const { user } = await getOrCreateUserAndTeam(
      command.user_id,
      command.team_id,
      command.team_domain,
      client
    );

    const args = command.text.split(' ');
    const subcommand = args[0] || 'help';

    switch (subcommand) {
      case 'link':
        const teamArg = args.slice(1).join(' ');
        await linkChannelToTeam(say, client, channelId, channelName, user, teamArg);
        break;

      case 'discover':
        await discoverChannels(say, client, user);
        break;

      case 'status':
        await showChannelStatus(say, client, channelId);
        break;

      case 'unlink':
        await unlinkChannel(say, client, channelId, user);
        break;

      case 'user':
        // Link channel to receive user's vehicle updates
        await linkUserChannel(say, client, channelId, channelName, command.user_id);
        break;

      default:
        await say({
          blocks: [
            {
              type: 'header',
              text: {
                type: 'plain_text',
                text: '⚙️ Vehicle Bot Channel Setup',
                emoji: true
              }
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: '*Available commands:*\n' +
                      '• `/vehicle-setup link [team]` - Link specific team to this channel\n' +
                      '• `/vehicle-setup user` - Link your vehicles to this channel\n' +
                      '• `/vehicle-setup discover` - Show all channels bot is in\n' +
                      '• `/vehicle-setup status` - Check this channel\'s setup\n' +
                      '• `/vehicle-setup unlink` - Remove team from this channel'
              }
            },
            {
              type: 'context',
              elements: [
                {
                  type: 'mrkdwn',
                  text: `Current channel: #${channelName} (${channelId})`
                }
              ]
            }
          ]
        });
    }
  } catch (error) {
    console.error('Error in vehicle-setup:', error);
    await say('❌ An error occurred. Please try again.');
  }
});

// Link user's vehicles to channel
async function linkUserChannel(say, client, channelId, channelName, slackUserId) {
  try {
    // Get linked user
    const user = await getLinkedUser(slackUserId);
    if (!user) {
      await say('❌ Please link your VehicleTracker account first using `/vehicle link [url]`');
      return;
    }
    
    // Store user-channel link
    await db.collection('user_linked_channels').doc(`${slackUserId}_${channelId}`).set({
      slackUserId: slackUserId,
      firebaseUserId: user.id,
      channelId: channelId,
      channelName: channelName,
      linkedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    // Update cache
    if (!userLinkedChannels.has(slackUserId)) {
      userLinkedChannels.set(slackUserId, new Set());
    }
    userLinkedChannels.get(slackUserId).add(channelId);
    
    await say({
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `✅ *Personal Updates Linked!*\n\nChannel <#${channelId}> will now receive updates for vehicles you add.`
          }
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: 'Your vehicle updates will be posted here automatically.'
            }
          ]
        }
      ]
    });
    
  } catch (error) {
    console.error('Error linking user channel:', error);
    await say('❌ Error linking personal updates to this channel.');
  }
}

// Handle setup command under /vehicle
async function handleSetupCommand(say, client, command, user) {
  const channelId = command.channel_id;
  const channelName = command.channel_name;
  const args = command.text.split(' ');
  const setupAction = args[1] || 'help';
  const teamArg = args.slice(2).join(' ');

  switch (setupAction) {
    case 'link':
      await linkChannelToTeam(say, client, channelId, channelName, user, teamArg);
      break;

    case 'user':
      await linkUserChannel(say, client, channelId, channelName, command.user_id);
      break;

    case 'discover':
      await discoverChannels(say, client, user);
      break;

    case 'status':
      await showChannelStatus(say, client, channelId);
      break;

    case 'unlink':
      await unlinkChannel(say, client, channelId, user);
      break;

    default:
      await say({
        blocks: [
          {
            type: 'header',
            text: {
                type: 'plain_text',
              text: '⚙️ Vehicle Bot Channel Setup',
              emoji: true
            }
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
// Continuation of slack-vehicle-bot.js from where it was cut off

text: '*Available setup commands:*\n' +
'• `/vehicle setup link [team]` - Link specific team to this channel\n' +
'• `/vehicle setup user` - Link your vehicles to this channel\n' +
'• `/vehicle setup discover` - Show all channels bot is in\n' +
'• `/vehicle setup status` - Check this channel\'s setup\n' +
'• `/vehicle setup unlink` - Remove team from this channel'
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: `Current channel: #${channelName} (${channelId})`
}
]
}
]
});
}
}

// Handle link command to connect Slack account to VehicleTracker account via URL
async function handleLinkCommand(say, client, command, args) {
const slackUserId = command.user_id;
const urlArg = args[1]; // Get the URL argument

if (!urlArg) {
await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: '*Link your Slack account to VehicleTracker*\n\nPaste your VehicleTracker URL to link your accounts.'
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '*How to get your URL:*\n1. Open VehicleTracker in your browser\n2. Copy the URL from the address bar\n3. Use: `/vehicle link [your-url]`\n\n*Example:*\n`/vehicle link https://recoveriqs.net/vehicles?user=*************`'
}
}
]
});
return;
}

try {
// Extract user ID from URL
const urlMatch = urlArg.match(/user=([a-zA-Z0-9]+)/);
if (!urlMatch) {
await say('❌ Invalid URL format. Please copy the full URL from your VehicleTracker page.');
return;
}

const firebaseUserId = urlMatch[1];
console.log(`[LINK] Attempting to link Slack user ${slackUserId} to Firebase user ${firebaseUserId}`);

// Check if Firebase user exists
const userDoc = await db.collection('users').doc(firebaseUserId).get();
if (!userDoc.exists) {
await say('❌ User not found. Please make sure you copied the correct URL from VehicleTracker.');
return;
}

const userData = userDoc.data();
const displayName = userData.displayName || userData.email?.split('@')[0] || 'User';

// Check if this Slack user is already linked
const existingLinkQuery = await db.collection('slack_user_links')
.where('slackUserId', '==', slackUserId)
.limit(1)
.get();

if (!existingLinkQuery.empty) {
const existingLink = existingLinkQuery.docs[0].data();
if (existingLink.firebaseUserId === firebaseUserId) {
await say(`✅ Your Slack account is already linked to **${displayName}**`);
return;
} else {
// Update existing link
await existingLinkQuery.docs[0].ref.update({
firebaseUserId: firebaseUserId,
linkedAt: admin.firestore.FieldValue.serverTimestamp()
});

// IMPORTANT: Also update the user document with slackUserId
await db.collection('users').doc(firebaseUserId).update({
slackUserId: slackUserId,
slackLinkedAt: admin.firestore.FieldValue.serverTimestamp()
});

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `✅ *Account Re-linked Successfully!*\n\nYour Slack account is now linked to:\n*${displayName}*`
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: 'Your vehicle actions will now use your VehicleTracker display name.'
}
]
}
]
});
return;
}
}

// Check if this Firebase user is already linked to another Slack account
const firebaseUserLinkQuery = await db.collection('slack_user_links')
.where('firebaseUserId', '==', firebaseUserId)
.limit(1)
.get();

if (!firebaseUserLinkQuery.empty) {
await say('⚠️ This VehicleTracker account is already linked to another Slack user. Please contact an admin if you need to change this.');
return;
}

// Create new link
await db.collection('slack_user_links').add({
slackUserId: slackUserId,
firebaseUserId: firebaseUserId,
linkedAt: admin.firestore.FieldValue.serverTimestamp()
});

// IMPORTANT: Also update the user document with slackUserId
await db.collection('users').doc(firebaseUserId).update({
slackUserId: slackUserId,
slackLinkedAt: admin.firestore.FieldValue.serverTimestamp()
});

console.log(`[LINK] Successfully linked Slack user ${slackUserId} to Firebase user ${firebaseUserId} (${displayName})`);
console.log(`[LINK] Updated user document with slackUserId`);

// Get user's team info
let teamName = 'No Team';
const teamsSnapshot = await db.collection('teams').get();
for (const teamDoc of teamsSnapshot.docs) {
const teamMembersSnapshot = await db.collection('teams')
.doc(teamDoc.id)
.collection('teamMembers')
.where('userId', '==', firebaseUserId)
.limit(1)
.get();

if (!teamMembersSnapshot.empty) {
teamName = teamDoc.data().name || 'Unknown Team';
break;
}
}

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `✅ *Account Linked Successfully!*\n\nYour Slack account is now linked to:\n*${displayName}*\nTeam: *${teamName}*`
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '*What this means:*\n• Your vehicle actions will use your correct display name\n• You can use `/vehicle my` to see your vehicles\n• All vehicle updates will show your proper name\n• Use `/vehicle setup user` in any channel to receive your vehicle updates there'
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: 'Use `/vehicle unlink` if you need to disconnect this link.'
}
]
}
]
});

} catch (error) {
console.error('[LINK] Error linking account:', error);
await say('❌ Error linking account. Please try again or contact support.');
}
}

// Handle unlink command
async function handleUnlinkCommand(say, command) {
const slackUserId = command.user_id;

try {
const linkQuery = await db.collection('slack_user_links')
.where('slackUserId', '==', slackUserId)
.limit(1)
.get();

if (linkQuery.empty) {
await say('❌ Your Slack account is not linked to any VehicleTracker account.');
return;
}

// Delete the link
await linkQuery.docs[0].ref.delete();

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: '✅ *Account Unlinked*\n\nYour Slack account has been disconnected from VehicleTracker.'
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: 'Use `/vehicle link [url]` to link a different account.'
}
]
}
]
});

} catch (error) {
console.error('[UNLINK] Error unlinking account:', error);
await say('❌ Error unlinking account. Please try again.');
}
}

// Add posting preference command
async function handlePostingPreferenceCommand(say, command, args) {
const slackUserId = command.user_id;
const preference = args[1];

try {
// Get linked user
const user = await getLinkedUser(slackUserId);
if (!user) {
await say('❌ Please link your VehicleTracker account first using `/vehicle link [url]`');
return;
}

if (!preference || !['team', 'personal', 'both'].includes(preference)) {
// Show current preference
const currentPref = await getUserPostingPreference(user.id);

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Vehicle Update Posting Preferences*\n\nCurrent setting: *${currentPref}*\n\nTo change where your vehicle updates are posted:\n\`/vehicle posting team\` - Post only to team channel\n\`/vehicle posting personal\` - Post only to your linked channels\n\`/vehicle posting both\` - Post to both (not recommended - causes duplicates)`
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: '💡 Tip: Use "personal" if you have your own channel for updates'
}
]
}
]
});
return;
}

// Update preference
await setUserPostingPreference(user.id, preference);

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `✅ *Posting preference updated!*\n\nYour vehicle updates will now be posted to: *${preference}*`
}
}
]
});

} catch (error) {
console.error('Error setting posting preference:', error);
await say('❌ Error updating preference. Please try again.');
}
}

// Link specific team to channel
async function linkChannelToTeam(say, client, channelId, channelName, user, teamName) {
try {
if (!teamName) {
// Show available teams to link
const teamsSnapshot = await db.collection('teams').get();

if (teamsSnapshot.empty) {
await say('❌ No teams found in the system.');
return;
}

const teamOptions = teamsSnapshot.docs.map(doc => {
const team = doc.data();
const linkedStatus = team.slackChannel ? `(linked to <#${team.slackChannel}>)` : '(not linked)';
return `• *${team.name}* ${linkedStatus}`;
}).join('\n');

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Select a team to link to this channel:*\n${teamOptions}\n\nUse: \`/vehicle setup link [team name]\`\nExample: \`/vehicle setup link rockford\``
}
}
]
});
return;
}

// Find the team
const teamsSnapshot = await db.collection('teams').get();
let matchedTeam = null;

for (const doc of teamsSnapshot.docs) {
const team = doc.data();
if (team.name?.toLowerCase() === teamName.toLowerCase() ||
team.name?.toLowerCase().includes(teamName.toLowerCase())) {
matchedTeam = { id: doc.id, ...team };
break;
}
}

if (!matchedTeam) {
await say(`❌ Team "${teamName}" not found. Use \`/vehicle teams\` to see all teams.`);
return;
}

// Check if team is already linked to another channel
if (matchedTeam.slackChannel && matchedTeam.slackChannel !== channelId) {
await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `⚠️ *${matchedTeam.name}* is already linked to <#${matchedTeam.slackChannel}>.\n\nLinking to this channel will remove the previous link. Continue?`
}
},
{
type: 'actions',
elements: [
{
type: 'button',
text: {
type: 'plain_text',
text: 'Yes, Link Here',
emoji: true
},
style: 'primary',
action_id: 'confirm_link',
value: JSON.stringify({
teamId: matchedTeam.id,
teamName: matchedTeam.name,
channelId: channelId,
channelName: channelName
})
},
{
type: 'button',
text: {
type: 'plain_text',
text: 'Cancel',
emoji: true
},
action_id: 'cancel_link'
}
]
}
]
});
return;
}

// Link the team to this channel
await db.collection('teams').doc(matchedTeam.id).update({
slackChannel: channelId,
slackChannelName: channelName,
slackIntegration: {
enabled: true,
channelId: channelId,
channelName: channelName,
linkedBy: user.id,
linkedByName: user.displayName || user.email,
linkedAt: admin.firestore.FieldValue.serverTimestamp()
}
});

// Update local caches
teamChannels.set(matchedTeam.id, channelId);
channelTeams.set(channelId, matchedTeam.id);

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `✅ *Channel Linked Successfully!*\n\nChannel <#${channelId}> is now linked to *${matchedTeam.name}*`
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '*What happens now:*\n' +
`• Vehicle updates from ${matchedTeam.name} will be posted here\n` +
`• \`/vehicle list\` will show only ${matchedTeam.name} vehicles\n` +
`• Team members can use all vehicle commands in this channel`
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: '💡 *Tip:* Use `/vehicle list` to see current vehicles'
}
]
}
]
});

console.log(`✅ Linked ${matchedTeam.name} to channel ${channelName}`);

} catch (error) {
console.error('Error linking team to channel:', error);
await say('❌ Error linking team to channel. Please try again.');
}
}

// Handle link confirmation
app.action('confirm_link', async ({ body, ack, client }) => {
await ack();

try {
const value = JSON.parse(body.actions[0].value);

// Update team with new channel
await db.collection('teams').doc(value.teamId).update({
slackChannel: value.channelId,
slackChannelName: value.channelName,
slackIntegration: {
enabled: true,
channelId: value.channelId,
channelName: value.channelName,
linkedAt: admin.firestore.FieldValue.serverTimestamp()
}
});

// Update caches
teamChannels.set(value.teamId, value.channelId);
channelTeams.set(value.channelId, value.teamId);

await client.chat.update({
channel: body.channel.id,
ts: body.message.ts,
text: 'Channel linked successfully',
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `✅ *${value.teamName}* is now linked to <#${value.channelId}>!`
}
}
]
});
} catch (error) {
console.error('Error confirming link:', error);
}
});

app.action('cancel_link', async ({ body, ack, client }) => {
await ack();

await client.chat.update({
channel: body.channel.id,
ts: body.message.ts,
text: 'Link cancelled',
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: '❌ Link cancelled. No changes were made.'
}
}
]
});
});

async function discoverChannels(say, client, user) {
try {
// Get all channels the bot is in
const result = await client.conversations.list({
types: 'public_channel,private_channel',
exclude_archived: true
});

if (!result.channels || result.channels.length === 0) {
await say('❌ Bot is not in any channels yet.');
return;
}

// Get all teams with existing channel links
const teamsSnapshot = await db.collection('teams').get();
const channelToTeams = new Map();

teamsSnapshot.forEach(doc => {
const data = doc.data();
if (data.slackChannel) {
channelToTeams.set(data.slackChannel, {
teamId: doc.id,
teamName: data.name
});
}
});

const channelBlocks = result.channels.map(channel => {
const linkedTeam = channelToTeams.get(channel.id);
const statusText = linkedTeam 
? `✅ Linked to: ${linkedTeam.teamName}`
: '❌ Not linked to any team';

return {
type: 'section',
text: {
type: 'mrkdwn',
text: `*#${channel.name}*\n${statusText}\nID: \`${channel.id}\``
}
};
});

await say({
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: '🔍 Bot Channel Discovery',
emoji: true
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: `Found ${result.channels.length} channels where the bot is present:`
}
},
{ type: 'divider' },
...channelBlocks
]
});

} catch (error) {
console.error('Error discovering channels:', error);
await say('❌ Error discovering channels. Make sure the bot has the necessary permissions.');
}
}

async function showChannelStatus(say, client, channelId) {
try {
// Get channel info
const channelInfo = await client.conversations.info({
channel: channelId
});

// Get team linked to this channel
const team = await getChannelTeam(channelId);

// Check for user-linked channels
const userLinkedSnapshot = await db.collection('user_linked_channels')
.where('channelId', '==', channelId)
.get();

const linkedUsers = [];
for (const doc of userLinkedSnapshot.docs) {
const linkData = doc.data();
const userDoc = await db.collection('users').doc(linkData.firebaseUserId).get();
if (userDoc.exists) {
linkedUsers.push(userDoc.data().displayName || 'Unknown');
}
}

if (!team && linkedUsers.length === 0) {
await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Channel Status: #${channelInfo.channel.name}*\n\n❌ This channel is not linked to any team or user.`
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: 'Use `/vehicle-setup link [team]` to connect a team\nUse `/vehicle-setup user` to link your personal updates'
}
}
]
});
} else {
let statusText = '';

if (team) {
// Get team member count
const membersSnapshot = await db.collection('teams')
.doc(team.id)
.collection('teamMembers')
.get();

statusText += `✅ *Team Link:* ${team.name} (${membersSnapshot.size} members)\n`;
}

if (linkedUsers.length > 0) {
statusText += `✅ *User Links:* ${linkedUsers.join(', ')}`;
}

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Channel Status: #${channelInfo.channel.name}*\n\n${statusText}`
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: `Channel ID: \`${channelId}\``
}
]
}
]
});
}
} catch (error) {
console.error('Error checking channel status:', error);
await say('❌ Error checking channel status.');
}
}

async function unlinkChannel(say, client, channelId, user) {
try {
// Find team linked to this channel
const team = await getChannelTeam(channelId);

if (team) {
// Unlink the team from this channel
await db.collection('teams').doc(team.id).update({
slackChannel: admin.firestore.FieldValue.delete(),
slackIntegration: {
enabled: false,
previousChannelId: channelId,
unlinkedBy: user.id,
unlinkedByName: user.displayName || user.email,
unlinkedAt: admin.firestore.FieldValue.serverTimestamp()
}
});

// Remove from local caches
teamChannels.delete(team.id);
channelTeams.delete(channelId);
}

// Also remove any user-linked channels
const userLinkedSnapshot = await db.collection('user_linked_channels')
.where('channelId', '==', channelId)
.get();

const batch = db.batch();
userLinkedSnapshot.docs.forEach(doc => {
batch.delete(doc.ref);
});
await batch.commit();

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `✅ *Channel Unlinked*\n\nThis channel is no longer linked to any team or user updates.`
}
}
]
});

} catch (error) {
console.error('Error unlinking channel:', error);
await say('❌ Error unlinking channel.');
}
}

// Slash command handler - /vehicle
app.command('/vehicle', async ({ command, ack, say, client }) => {
console.log('[DEBUG] Received /vehicle command:', command.text);

try {
// Acknowledge command immediately
await ack();
console.log('[DEBUG] Command acknowledged');
const userId = command.user_id;
const userInfo = await client.users.info({ user: userId });
const userEmail = userInfo.user.profile.email;
const displayName = userInfo.user.profile.real_name || userInfo.user.name || userEmail.split('@')[0];
console.log('[DEBUG] User email:', userEmail, 'Display name:', displayName);

// Get or create user from Slack info
const usersRef = db.collection('users');
const userQuery = await usersRef.where('email', '==', userEmail).get();

let user;
if (userQuery.empty) {
console.log('[DEBUG] First time user, adding to system...');

// Create user from Slack data
const newUserRef = await usersRef.add({
email: userEmail,
displayName: displayName,
slackUserId: userId,
slackTeamId: command.team_id,
createdAt: admin.firestore.FieldValue.serverTimestamp(),
lastActive: admin.firestore.FieldValue.serverTimestamp()
});

user = { 
id: newUserRef.id, 
email: userEmail, 
displayName: displayName,
slackUserId: userId
};
console.log('[DEBUG] Added user:', user.displayName);
} else {
const userData = userQuery.docs[0];
user = { id: userData.id, ...userData.data() };

// Update display name and slack user ID if needed (DON'T OVERWRITE EXISTING DISPLAY NAME)
const updates = {
slackUserId: userId,
slackTeamId: command.team_id
};

// Only update display name if it's missing or is just the email prefix
if (!user.displayName || user.displayName === userEmail.split('@')[0]) {
updates.displayName = displayName;
user.displayName = displayName;
}

await userData.ref.update(updates);
console.log('[DEBUG] User found:', user.id);
}

// Parse command text
const args = command.text.split(' ');
const subcommand = args[0] || 'list';

switch (subcommand) {
case 'list':
// Check if team name is provided
const teamNameArg = args.slice(1).join(' ');
if (teamNameArg) {
await listTeamVehicles(say, client, user, teamNameArg);
} else {
// Show vehicles for the team linked to this channel
const channelTeam = await getChannelTeam(command.channel_id);
if (channelTeam) {
await listChannelTeamVehicles(say, client, user, channelTeam);
} else {
await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: '❌ *This channel is not linked to any team.*\n\nUse `/vehicle setup link [team]` to link a team, or specify a team:\n`/vehicle list rockford`'
}
}
]
});
}
}
break;

case 'my':
case 'mine':
await listMyVehicles(say, client, user, userId);
break;

case 'teams':
await listAllTeams(say, client, user);
break;

case 'team':
const teamArg = args.slice(1).join(' ');
if (teamArg) {
await showTeamInfo(say, client, user, teamArg);
} else {
await listAllTeams(say, client, user);
}
break;

case 'search':
const searchTerm = args.slice(1).join(' ');
await searchVehicles(say, client, user, searchTerm);
break;

case 'stats':
await showStats(say, client, user, userId);
break;

case 'leaderboard':
await showGlobalLeaderboard(say, client);
break;

case 'setup':
await handleSetupCommand(say, client, command, user);
break;

case 'reminders':
// Check if it's a preference command
if (['enable', 'disable', 'status'].includes(args[1])) {
await handleReminderPreferences(say, command, args);
} else {
await handleRemindersCommand(say, client, command, user);
}
break;

case 'link':
await handleLinkCommand(say, client, command, args);
break;

case 'unlink':
await handleUnlinkCommand(say, command);
break;

case 'nominate':
await handleNominationCommand(say, client, command, args);
break;

case 'nominations':
await showNominations(say, client, command);
break;

case 'posting':
await handlePostingPreferenceCommand(say, command, args);
break;

case 'help':
default:
await say({
text: 'Vehicle Tracker Commands',
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: '🚗 Vehicle Tracker Commands',
emoji: true
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '*Vehicle Commands:*\n' +
  '• `/vehicle list` - Show vehicles for channel\'s team\n' +
  '• `/vehicle list [team]` - Show vehicles for specific team\n' +
  '• `/vehicle my` or `/vehicle mine` - Show only your vehicles\n' +
  '• `/vehicle search [term]` - Search vehicles by VIN or name\n' +
  '• `/vehicle stats` - Show statistics for ALL teams\n' +
  '• `/vehicle leaderboard` - Global user leaderboard\n\n' +
  '*Team Commands:*\n' +
  '• `/vehicle teams` - Show all teams\n' +
  '• `/vehicle team [name]` - Show team members\n\n' +
  '*Account Linking:*\n' +
  '• `/vehicle link [url]` - Link your VehicleTracker account\n' +
  '• `/vehicle unlink` - Unlink your account\n\n' +
  '*Posting Preferences:*\n' +
  '• `/vehicle posting` - View/set where your updates post\n' +
  '• `/vehicle posting team` - Post to team channel only\n' +
  '• `/vehicle posting personal` - Post to personal channels only\n\n' +
  '*Employee Recognition:*\n' +
  '• `/vehicle nominate @user [reason]` - Nominate for Employee of the Month\n' +
  '• `/vehicle nominations` - View nominations and vote\n\n' +
  '*Configuration:*\n' +
  '• `/vehicle setup link [team]` - Link team to this channel\n' +
  '• `/vehicle setup user` - Link your updates to this channel\n' +
  '• `/vehicle setup discover` - Show all bot channels\n' +
  '• `/vehicle setup status` - Check channel setup\n' +
  '• `/vehicle setup unlink` - Unlink this channel\n' +
  '• `/vehicle reminders [minutes]` - Set reminder interval\n' +
  '• `/vehicle reminders enable/disable` - Control all reminders\n\n' +
  '• `/vehicle help` - Show this help message'
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: `User: ${user.displayName || user.email}`
}
]
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '💡 *Tip:* Use `/vehicle link` to connect your VehicleTracker account for proper name display'
}
}
]
});
}
} catch (error) {
console.error('[ERROR] Error handling vehicle command:', error);
console.error('[ERROR] Stack trace:', error.stack);
try {
await say('❌ An error occurred while processing your request. Please check the bot logs.');
} catch (sayError) {
console.error('[ERROR] Failed to send error message:', sayError);
}
}
});

// List vehicles for channel's linked team
async function listChannelTeamVehicles(say, client, user, team) {
try {
await say(`🔄 Loading vehicles for ${team.name}...`);

const vehicles = await loadTeamVehicles(team.id);

if (vehicles.length === 0) {
await say({
text: `No pending vehicles found for ${team.name}`,
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `📋 *No pending vehicles found for ${team.name}*\n\nPossible reasons:\n• All vehicles are secured\n• No vehicles added in the last 3 months\n• Team has no active members`
}
}
]
});
return;
}

// Send summary
await say({
text: `Found ${vehicles.length} vehicles for ${team.name}`,
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: `📊 ${team.name}: ${vehicles.length} Active Vehicles`,
emoji: true
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Showing:* All unsecured vehicles from the last 3 months`
}
},
{
type: 'divider'
}
]
});

// Send vehicles
const maxVehicles = 10;
for (let i = 0; i < Math.min(vehicles.length, maxVehicles); i++) {
await say({
text: `Vehicle: ${vehicles[i].vehicle}`,
attachments: buildVehicleCard(vehicles[i], { useColor: true })
});

// Small delay to avoid rate limits
if ((i + 1) % 3 === 0) {
await new Promise(resolve => setTimeout(resolve, 1000));
}
}

if (vehicles.length > maxVehicles) {
await say(`📋 Showing first ${maxVehicles} of ${vehicles.length} vehicles.`);
}

} catch (error) {
console.error('Error listing channel team vehicles:', error);
await say('❌ Error loading vehicles.');
}
}

// List vehicles for a specific team
async function listTeamVehicles(say, client, user, teamName) {
try {
await say(`🔄 Loading vehicles for team: ${teamName}...`);

// Find team by name (case insensitive)
const teamsSnapshot = await db.collection('teams').get();
let matchedTeam = null;

for (const doc of teamsSnapshot.docs) {
const team = doc.data();
if (team.name?.toLowerCase() === teamName.toLowerCase()) {
matchedTeam = { id: doc.id, ...team };
break;
}
}

if (!matchedTeam) {
// Try partial match
for (const doc of teamsSnapshot.docs) {
const team = doc.data();
if (team.name?.toLowerCase().includes(teamName.toLowerCase())) {
matchedTeam = { id: doc.id, ...team };
break;
}
}
}

if (!matchedTeam) {
await say(`❌ Team "${teamName}" not found. Use \`/vehicle teams\` to see all teams.`);
return;
}

// Load vehicles for this team
const vehicles = await loadTeamVehicles(matchedTeam.id);

if (vehicles.length === 0) {
await say({
text: `No pending vehicles found for ${matchedTeam.name}`,
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `📋 *No pending vehicles found for ${matchedTeam.name}*\n\nPossible reasons:\n• All vehicles are secured\n• No vehicles added in the last 3 months\n• Team has no active members`
}
}
]
});
return;
}

// Send summary
await say({
text: `Found ${vehicles.length} vehicles for ${matchedTeam.name}`,
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: `📊 ${matchedTeam.name}: ${vehicles.length} Active Vehicles`,
emoji: true
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Showing:* All unsecured vehicles from the last 3 months`
}
},
{
type: 'divider'
}
]
});

// Send vehicles
const maxVehicles = 10;
for (let i = 0; i < Math.min(vehicles.length, maxVehicles); i++) {
await say({
text: `Vehicle: ${vehicles[i].vehicle}`,
attachments: buildVehicleCard(vehicles[i], { useColor: true })
});

// Small delay to avoid rate limits
if ((i + 1) % 3 === 0) {
await new Promise(resolve => setTimeout(resolve, 1000));
}
}

if (vehicles.length > maxVehicles) {
await say(`📋 Showing first ${maxVehicles} of ${vehicles.length} vehicles.`);
}

} catch (error) {
console.error('Error listing team vehicles:', error);
await say('❌ Error loading team vehicles.');
}
}

// List user's own vehicles - now uses linked accounts
async function listMyVehicles(say, client, user, slackUserId) {
try {
// Check if user has linked account
const linkedUser = await getLinkedUser(slackUserId);
if (!linkedUser) {
await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: '❌ *Account Not Linked*\n\nYou need to link your VehicleTracker account first.'
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '*How to link:*\n1. Open VehicleTracker in your browser\n2. Copy the URL from the address bar\n3. Use: `/vehicle link [your-url]`\n\n*Example:*\n`/vehicle link https://recoveriqs.net/vehicles?user=*************`'
}
}
]
});
return;
}

await say('🔄 Loading your vehicles...');

const vehicles = await loadUserVehicles(slackUserId);

if (vehicles.length === 0) {
await say({
text: 'No pending vehicles found',
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: '📋 *You have no pending vehicles*\n\nAll your vehicles are either secured or moved to Never Secured.'
}
}
]
});
return;
}

// Send summary
await say({
text: `Found ${vehicles.length} of your vehicles`,
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: `📊 Your Vehicles: ${vehicles.length} Active`,
emoji: true
}
},
{
type: 'divider'
}
]
});

// Send vehicles
for (const vehicle of vehicles) {
await say({
text: `Vehicle: ${vehicle.vehicle}`,
attachments: buildVehicleCard(vehicle, { useColor: true })
});
}

} catch (error) {
console.error('Error listing user vehicles:', error);
await say('❌ Error loading your vehicles.');
}
}

// List all teams
async function listAllTeams(say, client, user) {
try {
const teamsSnapshot = await db.collection('teams').get();

if (teamsSnapshot.empty) {
await say('❌ No teams found in the system.');
return;
}

const teamBlocks = [];

for (const doc of teamsSnapshot.docs) {
const team = doc.data();

// Get member count from subcollection
const membersSnapshot = await db.collection('teams')
.doc(doc.id)
.collection('teamMembers')
.get();

// Check if team has Slack channel
const channelInfo = team.slackChannel ? `<#${team.slackChannel}>` : 'No channel linked';

teamBlocks.push({
type: 'section',
text: {
type: 'mrkdwn',
text: `*${team.name}*\n` +
`Members: ${membersSnapshot.size}\n` +
`Channel: ${channelInfo}\n` +
`_Use \`/vehicle list ${team.name}\` to see vehicles_`
}
});
}

await say({
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: '👥 All Teams',
emoji: true
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: `Found ${teamsSnapshot.size} teams in the system:`
}
},
{
type: 'divider'
},
...teamBlocks
]
});

} catch (error) {
console.error('Error listing teams:', error);
await say('❌ Error loading teams.');
}
}

// Show team information
async function showTeamInfo(say, client, user, teamName) {
try {
// Find team
const teamsSnapshot = await db.collection('teams').get();
let matchedTeam = null;

for (const doc of teamsSnapshot.docs) {
const team = doc.data();
if (team.name?.toLowerCase() === teamName.toLowerCase() ||
team.name?.toLowerCase().includes(teamName.toLowerCase())) {
matchedTeam = { id: doc.id, ...team };
break;
}
}

if (!matchedTeam) {
await say(`❌ Team "${teamName}" not found.`);
return;
}

// Get team members
const membersSnapshot = await db.collection('teams')
.doc(matchedTeam.id)
.collection('teamMembers')
.get();

const memberBlocks = [];

for (const memberDoc of membersSnapshot.docs) {
const memberData = memberDoc.data();
const userId = memberData.userId;

// Get user details
const userDoc = await db.collection('users').doc(userId).get();
if (userDoc.exists) {
const userData = userDoc.data();
memberBlocks.push({
type: 'section',
text: {
type: 'mrkdwn',
text: `• *${userData.displayName || userData.email}*\n  ${userData.email}`
}
});
}
}

const channelInfo = matchedTeam.slackChannel ? 
`Linked to: <#${matchedTeam.slackChannel}>` : 
'No Slack channel linked';

await say({
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: `👥 ${matchedTeam.name}`,
emoji: true
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Total Members:* ${membersSnapshot.size}\n*${channelInfo}*\n*Team ID:* \`${matchedTeam.id}\``
}
},
{
type: 'divider'
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '*Team Members:*'
}
},
...memberBlocks
]
});

} catch (error) {
console.error('Error showing team info:', error);
await say('❌ Error loading team information.');
}
}

// Handle reminders command
async function handleRemindersCommand(say, client, command, user) {
const args = command.text.split(' ');
const minutes = parseInt(args[1]);

if (!minutes || minutes < 1) {
// Show current settings
const currentInterval = reminderIntervals.get(command.channel_id) || 30;
await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `⏰ *Current reminder interval:* ${currentInterval} minutes\n\nTo change: \`/vehicle reminders [minutes]\`\nExample: \`/vehicle reminders 60\` (for hourly reminders)`
}
}
]
});
return;
}

// Update reminder interval
reminderIntervals.set(command.channel_id, minutes);

// Save to database
await db.collection('reminder_settings').doc(command.channel_id).set({
channelId: command.channel_id,
intervalMinutes: minutes,
updatedBy: user.id,
updatedByName: user.displayName || user.email,
updatedAt: admin.firestore.FieldValue.serverTimestamp()
});

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `✅ *Reminder interval updated!*\n\nVehicle reminders will now be sent every ${minutes} minutes for this channel.`
}
}
]
});
}

// Search vehicles - searches only in teams the user has access to
async function searchVehicles(say, client, user, searchTerm) {
if (!searchTerm) {
await say('❌ Please provide a search term. Example: `/vehicle search camry`');
return;
}

try {
await say(`🔍 Searching for "${searchTerm}"...`);

// Find which teams the user belongs to
const userTeams = [];
const teamsSnapshot = await db.collection('teams').get();

for (const teamDoc of teamsSnapshot.docs) {
const teamMembersSnapshot = await db.collection('teams')
.doc(teamDoc.id)
.collection('teamMembers')
.where('userId', '==', user.id)
.limit(1)
.get();

if (!teamMembersSnapshot.empty) {
userTeams.push({ id: teamDoc.id, ...teamDoc.data() });
}
}

if (userTeams.length === 0) {
await say('❌ You are not a member of any team.');
return;
}

// Search vehicles in user's teams
const allVehicles = [];
const searchLower = searchTerm.toLowerCase();

for (const team of userTeams) {
const teamVehicles = await loadTeamVehicles(team.id);
allVehicles.push(...teamVehicles);
}

const matches = allVehicles.filter(vehicle => 
vehicle.vehicle?.toLowerCase().includes(searchLower) ||
vehicle.vin?.toLowerCase().includes(searchLower) ||
vehicle.plateNumber?.toLowerCase().includes(searchLower) ||
vehicle.teamMemberName?.toLowerCase().includes(searchLower)
);

if (matches.length === 0) {
await say(`❌ No vehicles found matching "${searchTerm}" in your teams`);
return;
}

await say(`Found ${matches.length} vehicle(s) matching "${searchTerm}"`);

// Show matches
for (let i = 0; i < matches.length && i < 10; i++) {
await say({
text: `Vehicle: ${matches[i].vehicle} (${matches[i].teamName})`,
attachments: buildVehicleCard(matches[i], { useColor: true })
});
}

if (matches.length > 10) {
await say(`Showing first 10 of ${matches.length} matches. Try a more specific search.`);
}

} catch (error) {
console.error('Error searching vehicles:', error);
await say('❌ Error searching vehicles. Please try again.');
}
}

// Show statistics for ALL teams
async function showStats(say, client, user, slackUserId) {
try {
// Load vehicles from ALL teams
const allVehicles = await loadAllTeamsVehicles(slackUserId);

// Group by team
const vehiclesByTeam = {};
allVehicles.forEach(v => {
if (!vehiclesByTeam[v.teamName]) {
vehiclesByTeam[v.teamName] = {
total: 0,
withDrivers: 0,
bottomStatus: 0,
pending: 0,
doNotSecure: 0
};
}

vehiclesByTeam[v.teamName].total++;
if (v.inRouteDriverId || v.arrivedDriverId) vehiclesByTeam[v.teamName].withDrivers++;
if (v.bottomStatus) vehiclesByTeam[v.teamName].bottomStatus++;
if (v.status === 'DO NOT SECURE') vehiclesByTeam[v.teamName].doNotSecure++;
if (!v.inRouteDriverId && !v.arrivedDriverId && !v.bottomStatus && v.status !== 'DO NOT SECURE') vehiclesByTeam[v.teamName].pending++;
});

// Calculate totals
const stats = {
total: allVehicles.length,
withDrivers: allVehicles.filter(v => v.inRouteDriverId || v.arrivedDriverId).length,
bottomStatus: allVehicles.filter(v => v.bottomStatus).length,
doNotSecure: allVehicles.filter(v => v.status === 'DO NOT SECURE').length,
pending: allVehicles.filter(v => !v.inRouteDriverId && !v.arrivedDriverId && !v.bottomStatus && v.status !== 'DO NOT SECURE').length,
teams: Object.keys(vehiclesByTeam).length
};

// Build team breakdown
let teamBreakdown = '';
Object.entries(vehiclesByTeam).forEach(([teamName, teamStats]) => {
teamBreakdown += `\n*${teamName}:* ${teamStats.total} vehicles (${teamStats.withDrivers} with drivers, ${teamStats.pending} pending, ${teamStats.doNotSecure} do not secure)`;
});

await say({
text: 'All Teams Statistics',
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: '📊 All Teams Vehicle Statistics',
emoji: true
}
},
{
type: 'section',
fields: [
{
type: 'mrkdwn',
text: `*Total Teams:*\n${stats.teams}`
},
{
type: 'mrkdwn',
text: `*Total Vehicles:*\n${stats.total}`
}
]
},
{
type: 'divider'
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '*Overall Status Breakdown:*'
}
},
{
type: 'section',
fields: [
{
type: 'mrkdwn',
text: `*With Drivers:*\n${stats.withDrivers} vehicles`
},
{
type: 'mrkdwn',
text: `*Bottom Status:*\n${stats.bottomStatus} vehicles`
},
{
type: 'mrkdwn',
text: `*Do Not Secure:*\n${stats.doNotSecure} vehicles`
},
{
type: 'mrkdwn',
text: `*Pending Action:*\n${stats.pending} vehicles`
}
]
},
{
type: 'divider'
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Team Breakdown:*${teamBreakdown}`
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: `_Last updated: ${new Date().toLocaleString()}_`
}
]
}
]
});

} catch (error) {
console.error('Error showing stats:', error);
await say('❌ Error loading statistics. Please try again.');
}
}

// Button handlers with linked account support
app.action('mark_in_route', async ({ body, ack, client, say }) => {
await ack();

try {
const value = JSON.parse(body.actions[0].value);
const slackUserId = body.user.id;

// Get linked user
const user = await getLinkedUser(slackUserId);
if (!user) {
await say('❌ Please link your VehicleTracker account first using `/vehicle link [url]`');
return;
}

const finalDisplayName = user.displayName || 'Team Member';

// Check if vehicle is already claimed
const vehicleDoc = await db.collection('users').doc(value.teamMemberId)
.collection('vehicleWeeks').doc(value.weekId)
.collection('vehicles').doc(value.vehicleId)
.get();

const vehicleData = vehicleDoc.data();
if (vehicleData.inRouteDriverId || vehicleData.arrivedDriverId) {
await say(`❌ This vehicle has already been claimed by ${vehicleData.inRouteDriverName || vehicleData.arrivedDriverName}`);
return;
}

// Update vehicle in Firebase with PROPER display name
await db.collection('users').doc(value.teamMemberId)
.collection('vehicleWeeks').doc(value.weekId)
.collection('vehicles').doc(value.vehicleId)
.update({
inRouteDriverId: user.id, // Use Firebase user ID, not Slack ID
inRouteDriverName: finalDisplayName, // Use proper display name
inRouteTimestamp: admin.firestore.FieldValue.serverTimestamp(),
updatedAt: admin.firestore.FieldValue.serverTimestamp()
});

// Get complete vehicle data with all metadata
const vehicle = await getCompleteVehicleData(value.teamMemberId, value.weekId, value.vehicleId);

// Update Slack message
await client.chat.update({
channel: body.channel.id,
ts: body.message.ts,
text: `Vehicle: ${vehicle.vehicle}`,
attachments: buildVehicleCard(vehicle, { useColor: true })
});

// Send notification with proper name
await say({
text: `🚚 ${finalDisplayName} is now in route`,
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `🚚 *${finalDisplayName}* is now in route to *${vehicle.vehicle}*\nETA: ~15 minutes`
}
}
]
});

} catch (error) {
console.error('Error marking in route:', error);
await say('❌ Error updating vehicle status. Please try again.');
}
});

app.action('mark_arrived', async ({ body, ack, client, say }) => {
await ack();

try {
const value = JSON.parse(body.actions[0].value);
const slackUserId = body.user.id;

// Get linked user
const user = await getLinkedUser(slackUserId);
if (!user) {
await say('❌ Please link your VehicleTracker account first using `/vehicle link [url]`');
return;
}

const finalDisplayName = user.displayName || 'Team Member';

// Verify this is the same driver who marked in route
if (value.driverId && value.driverId !== user.id) {
await say('❌ Only the driver who marked "In Route" can mark as arrived.');
return;
}

// Update vehicle with proper display name
await db.collection('users').doc(value.teamMemberId)
.collection('vehicleWeeks').doc(value.weekId)
.collection('vehicles').doc(value.vehicleId)
.update({
arrivedDriverId: user.id,
arrivedDriverName: finalDisplayName,
arrivedTimestamp: admin.firestore.FieldValue.serverTimestamp(),
updatedAt: admin.firestore.FieldValue.serverTimestamp()
});

// Get complete vehicle data
const vehicle = await getCompleteVehicleData(value.teamMemberId, value.weekId, value.vehicleId);

// Update message
await client.chat.update({
channel: body.channel.id,
ts: body.message.ts,
text: `Vehicle: ${vehicle.vehicle}`,
attachments: buildVehicleCard(vehicle, { useColor: true })
});

await say(`📍 ${finalDisplayName} has arrived at ${vehicle.vehicle}`);

} catch (error) {
console.error('Error marking arrived:', error);
await say('❌ Error updating arrival status.');
}
});

app.action('secure_vehicle', async ({ body, ack, client, say }) => {
await ack();

try {
const value = JSON.parse(body.actions[0].value);
const slackUserId = body.user.id;

// Get linked user
const user = await getLinkedUser(slackUserId);
if (!user) {
await say('❌ Please link your VehicleTracker account first using `/vehicle link [url]`');
return;
}

const finalDisplayName = user.displayName || 'Team Member';

// Verify this is the same driver who arrived
if (value.driverId && value.driverId !== user.id) {
await say('❌ Only the driver who arrived can secure the vehicle.');
return;
}

// Get vehicle data first
const vehicleDoc = await db.collection('users').doc(value.teamMemberId)
.collection('vehicleWeeks').doc(value.weekId)
.collection('vehicles').doc(value.vehicleId)
.get();

const vehicle = { id: vehicleDoc.id, ...vehicleDoc.data() };

// Update original vehicle as secured with proper name
await db.collection('users').doc(value.teamMemberId)
.collection('vehicleWeeks').doc(value.weekId)
.collection('vehicles').doc(value.vehicleId)
.update({
status: 'SECURED',
securedDate: new Date().toISOString().split('T')[0],
securedTimestamp: admin.firestore.FieldValue.serverTimestamp(),
securedByTeammate: true,
securedByUserId: user.id,
securedByUserName: finalDisplayName,
updatedAt: admin.firestore.FieldValue.serverTimestamp()
});

// Update Slack message
await client.chat.update({
channel: body.channel.id,
ts: body.message.ts,
text: '✅ Vehicle Secured!',
attachments: [{
color: getStatusColor({ status: 'SECURED' }),
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `✅ *Vehicle Secured!*\n*${vehicle.vehicle}* (VIN: ${vehicle.vin || 'No VIN'})\nSecured by ${finalDisplayName}`
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: `Secured at ${new Date().toLocaleString()}`
}
]
}
]
}]
});

// Send success notification
await say(`💰 ${finalDisplayName} secured ${vehicle.vehicle}! Great work! 🎉`);

} catch (error) {
console.error('Error securing vehicle:', error);
await say('❌ Error securing vehicle. Please try again.');
}
});

// Export card handler with proper error handling
app.action('export_card', async ({ body, ack, say }) => {
await ack();

try {
const value = JSON.parse(body.actions[0].value);

// Validate required fields
if (!value.teamMemberId || !value.weekId || !value.vehicleId) {
console.error('[ERROR] Export card missing required fields:', value);
await say('❌ Missing vehicle information. Please refresh and try again.');
return;
}

// Get vehicle data
const vehicleDoc = await db.collection('users').doc(value.teamMemberId)
.collection('vehicleWeeks').doc(value.weekId)
.collection('vehicles').doc(value.vehicleId)
.get();

if (!vehicleDoc.exists) {
await say('❌ Vehicle not found.');
return;
}

const vehicle = { id: vehicleDoc.id, ...vehicleDoc.data() };

// Check if canvas is available
if (!createCanvas) {
// Export as formatted text instead
const exportText = `
🚗 **${vehicle.vehicle}**
VIN: ${vehicle.vin || 'No VIN'} ${vehicle.vinVerified ? '✓' : ''}
Status: ${vehicle.status}
From: ${vehicle.teamMemberName}
Date: ${vehicle.date}

📍 Location: ${vehicle.address || 'Not provided'}
${vehicle.position ? `GPS: ${vehicle.position.lat}, ${vehicle.position.lng}` : ''}

Color: ${vehicle.color || 'Not provided'}
Drive Type: ${vehicle.driveType || 'Not provided'}
Plate #: ${vehicle.plateNumber || 'Not provided'}
Account #: ${vehicle.accountNumber || 'Not provided'}
Financier: ${vehicle.financier || 'Not provided'}

${vehicle.notes ? `📝 Notes: ${vehicle.notes}` : ''}
${vehicle.bottomStatus ? `⚠️ Bottom Status: ${vehicle.bottomStatus} (${vehicle.bottomStatusCount}/3)` : ''}
${vehicle.status === 'DO NOT SECURE' && vehicle.doNotSecureReason ? `🚫 DO NOT SECURE Reason: ${vehicle.doNotSecureReason}` : ''}

Generated: ${new Date().toLocaleString()}
`.trim();

await say({
text: 'Vehicle card exported',
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: '📋 *Vehicle Card Export*'
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '```\n' + exportText + '\n```'
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: '_Image export not available - canvas module not installed_'
}
]
}
]
});

return;
}

// Get team data
const teamsRef = db.collection('teams');
const teamMemberQuery = await teamsRef
.where('teamMembers', 'array-contains', value.teamMemberId)
.get();

const team = teamMemberQuery.empty ? null : teamMemberQuery.docs[0].data();

// Generate image
const imageBuffer = await generateVehicleCardImage(vehicle, team);

if (!imageBuffer) {
await say('❌ Error generating vehicle card image.');
return;
}

// Upload to Slack
await app.client.files.upload({
channels: body.channel.id,
file: imageBuffer,
filename: `vehicle_${vehicle.vehicle.replace(/\s+/g, '_')}_${vehicle.vin || 'NOVIN'}_${Date.now()}.jpg`,
title: `${vehicle.vehicle} - VIN: ${vehicle.vin || 'No VIN'}`,
initial_comment: `📸 Vehicle card for ${vehicle.vehicle}`
});

await say('📸 Vehicle card exported successfully!');

} catch (error) {
console.error('Error exporting card:', error);
await say('❌ Error exporting vehicle card.');
}
});

app.action('refresh_vehicle', async ({ body, ack, client }) => {
await ack();

try {
const value = JSON.parse(body.actions[0].value);

// Get fresh vehicle data
const vehicleDoc = await db.collection('users').doc(value.teamMemberId)
.collection('vehicleWeeks').doc(value.weekId)
.collection('vehicles').doc(value.vehicleId)
.get();

if (!vehicleDoc.exists) {
await client.chat.update({
channel: body.channel.id,
ts: body.message.ts,
text: '❌ Vehicle no longer exists',
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: '❌ *Vehicle no longer exists or has been secured*'
}
}
]
});
return;
}

// Get complete vehicle data
const vehicle = await getCompleteVehicleData(value.teamMemberId, value.weekId, value.vehicleId);

// Update the message with fresh data AND COLOR
await client.chat.update({
channel: body.channel.id,
ts: body.message.ts,
text: `Vehicle: ${vehicle.vehicle}`,
attachments: buildVehicleCard(vehicle, { useColor: true })
});

} catch (error) {
console.error('Error refreshing vehicle:', error);
}
});

// Handle overflow menu selections with linked account support
app.action('vehicle_overflow', async ({ body, ack, say }) => {
await ack();

try {
const selectedOption = JSON.parse(body.actions[0].selected_option.value);

// Handle abbreviated keys
const action = selectedOption.a || selectedOption.action;
const status = selectedOption.s || selectedOption.status;
const vehicleId = selectedOption.v || selectedOption.vehicleId;
const teamMemberId = selectedOption.m || selectedOption.teamMemberId;
const weekId = selectedOption.w || selectedOption.weekId;
const driverId = selectedOption.d || selectedOption.driverId;

// Expand status abbreviations
const fullStatus = status === 'DEBTOR' ? 'DEBTOR INTERFERENCE' : status;

if (action === 'bs' || action === 'bottom_status') {
// Get user info
const slackUserId = body.user.id;

// Get linked user
const user = await getLinkedUser(slackUserId);
if (!user) {
await say('❌ Please link your VehicleTracker account first using `/vehicle link [url]`');
return;
}

const finalDisplayName = user.displayName || 'Team Member';

// Verify this is the driver who arrived
if (driverId && driverId !== user.id) {
await say('❌ Only the driver who arrived can set bottom status.');
return;
}

// Get current vehicle
const currentVehicle = await db.collection('users').doc(teamMemberId)
.collection('vehicleWeeks').doc(weekId)
.collection('vehicles').doc(vehicleId)
.get();

const vehicleData = currentVehicle.data();
const currentCount = vehicleData.bottomStatusCount || 0;
const newCount = currentCount + 1;

// Update vehicle with proper display name
await db.collection('users').doc(teamMemberId)
.collection('vehicleWeeks').doc(weekId)
.collection('vehicles').doc(vehicleId)
.update({
bottomStatus: fullStatus,
bottomStatusCount: newCount,
bottomStatusDate: new Date().toISOString(),
bottomStatusByUserId: user.id,
bottomStatusByUserName: finalDisplayName,
updatedAt: admin.firestore.FieldValue.serverTimestamp()
});

await say({
text: `Vehicle marked as ${fullStatus}`,
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `📍 *Vehicle marked as ${fullStatus}*\n${vehicleData.vehicle} (VIN: ${vehicleData.vin || 'No VIN'})\nAttempt ${newCount}/3`
}
}
]
});

// If it's the 3rd attempt, notify about moving to never secured
if (newCount >= 3) {
await say({
text: '⚠️ Vehicle will be moved to Never Secured list',
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `⚠️ *3rd Attempt Reached*\nThis vehicle will be automatically moved to the Never Secured list.`
}
}
]
});
}
}

} catch (error) {
console.error('Error handling overflow action:', error);
await say('❌ Error updating vehicle status.');
}
});

// Scheduled reminders for teams
const sendScheduledReminders = async () => {
console.log('Running scheduled vehicle reminders...');

try {
// Get all teams with Slack channels configured
const teamsSnapshot = await db.collection('teams')
.where('slackChannel', '!=', null)
.get();

if (teamsSnapshot.empty) {
console.log('No teams have Slack configured yet');
return;
}

// Check each team's vehicles
for (const teamDoc of teamsSnapshot.docs) {
const teamData = teamDoc.data();
const slackChannel = teamData.slackChannel;
const teamName = teamData.name;

// Check if reminders are enabled for this channel
const prefs = reminderPreferences.get(slackChannel) || { enabled: true };
if (prefs.enabled === false) {
console.log(`Reminders disabled for channel ${slackChannel} (${teamName})`);
continue;
}

// Check reminder interval for this channel
const reminderInterval = reminderIntervals.get(slackChannel) || 30;

// Load vehicles for this team
const teamVehicles = await loadTeamVehicles(teamDoc.id);

// Check each vehicle for reminders
for (const vehicle of teamVehicles) {
const shouldRemind = checkIfReminderNeeded(vehicle, reminderInterval);

if (shouldRemind) {
await app.client.chat.postMessage({
channel: slackChannel,
text: `Reminder: ${vehicle.vehicle} needs attention`,
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: vehicle.bottomStatus ?
`⚠️ *DAILY REMINDER*\nRecheck ${vehicle.vehicle} - Status: ${vehicle.bottomStatus}` :
`⏰ *REMINDER*\n${vehicle.vehicle} - ${getTimerDisplay(vehicle)} elapsed`
}
},
{
type: 'section',
fields: [
{
type: 'mrkdwn',
text: `*VIN:*\n${vehicle.vin || 'No VIN'}`
},
{
type: 'mrkdwn',
text: `*From:*\n${vehicle.teamMemberName}`
}
]
}
]
});
}
}
}
} catch (error) {
console.error('Error sending scheduled reminders:', error);
}
};

const checkIfReminderNeeded = (vehicle, intervalMinutes = 30) => {
// Skip if driver assigned
if (vehicle.inRouteDriverId || vehicle.arrivedDriverId) {
return false;
}

// Always remind for bottom status vehicles once per day
if (vehicle.bottomStatus) {
const reminderKey = `${vehicle.uniqueKey}_daily`;
const lastReminder = reminderStates.get(reminderKey);
const today = new Date().toDateString();

if (!lastReminder || lastReminder !== today) {
reminderStates.set(reminderKey, today);
return true;
}
return false;
}

// Calculate time elapsed
const timeData = calculateElapsedTime(vehicle);
if (!timeData) return false;

const { minutes } = timeData;
const reminderKey = vehicle.uniqueKey;
const lastReminderMinutes = reminderStates.get(reminderKey) || 0;

// Send reminders based on interval
if (minutes >= intervalMinutes && minutes - lastReminderMinutes >= intervalMinutes) {
reminderStates.set(reminderKey, minutes);
return true;
}

return false;
};

// Load reminder intervals from database on startup
const loadReminderIntervals = async () => {
try {
const settingsSnapshot = await db.collection('reminder_settings').get();
settingsSnapshot.forEach(doc => {
const data = doc.data();
reminderIntervals.set(data.channelId, data.intervalMinutes);
});
console.log(`Loaded ${settingsSnapshot.size} reminder interval settings`);
} catch (error) {
console.error('Error loading reminder intervals:', error);
}
};

// Load user-linked channels on startup
const loadUserLinkedChannels = async () => {
try {
const snapshot = await db.collection('user_linked_channels').get();
snapshot.forEach(doc => {
const data = doc.data();
if (!userLinkedChannels.has(data.slackUserId)) {
userLinkedChannels.set(data.slackUserId, new Set());
}
userLinkedChannels.get(data.slackUserId).add(data.channelId);
});
console.log(`Loaded ${snapshot.size} user-linked channel settings`);
} catch (error) {
console.error('Error loading user-linked channels:', error);
}
};

// Load user posting preferences on startup
const loadUserPostingPreferences = async () => {
try {
const usersSnapshot = await db.collection('users')
.where('slackPostingPreference', '!=', null)
.get();

usersSnapshot.forEach(doc => {
const data = doc.data();
if (data.slackPostingPreference) {
userPostingPreferences.set(doc.id, data.slackPostingPreference);
}
});
console.log(`Loaded ${usersSnapshot.size} user posting preferences`);
} catch (error) {
console.error('Error loading user posting preferences:', error);
}
};

// Load reminder preferences on startup
const loadReminderPreferences = async () => {
try {
const prefsSnapshot = await db.collection('reminder_preferences').get();
prefsSnapshot.forEach(doc => {
const data = doc.data();
reminderPreferences.set(doc.id, data);
});
console.log(`Loaded ${prefsSnapshot.size} reminder preferences`);
} catch (error) {
console.error('Error loading reminder preferences:', error);
}
};

// Schedule reminder checks every 30 minutes
if (process.env.ENABLE_SCHEDULED_REMINDERS === 'true') {
const interval = process.env.REMINDER_INTERVAL_MINUTES || 30;
cron.schedule(`*/${interval} * * * *`, sendScheduledReminders);
console.log(`Scheduled reminders enabled - running every ${interval} minutes`);
}

// Schedule Employee of the Month reminders
// Daily reminder during nomination period (20th-27th)
cron.schedule('0 10 * * *', async () => {
try {
const currentDay = new Date().getDate();

if (currentDay >= 20 && currentDay <= 27) {
console.log('Sending Employee of the Month nomination reminders...');

// Get all teams with Slack channels
const teamsSnapshot = await db.collection('teams')
.where('slackChannel', '!=', null)
.get();

for (const teamDoc of teamsSnapshot.docs) {
const teamData = teamDoc.data();
const channel = teamData.slackChannel;

// Check if reminders are enabled for this channel
const prefs = reminderPreferences.get(channel) || { enabled: true };
if (prefs.enabled === false) {
console.log(`Reminders disabled for channel ${channel}, skipping Employee of the Month reminder`);
continue;
}

try {
await app.client.chat.postMessage({
channel: channel,
text: '🌟 Employee of the Month - Nominations Open!',
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: '🌟 Employee of the Month - Nominations Open!',
emoji: true
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Nomination period ends in ${27 - currentDay} days!*\n\nRecognize your outstanding colleagues for their hard work this month.`
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '*How to nominate:*\n`/vehicle nominate @username [reason]`\n\n*Example:*\n`/vehicle nominate @sarah Excellent teamwork and secured 45 vehicles!`'
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: 'Remember: You can only nominate once per month and cannot nominate yourself.'
}
]
}
]
});
} catch (error) {
console.error(`Failed to send nomination reminder to channel ${channel}:`, error);
}
}
}

// Voting period reminders (1st-7th)
if (currentDay >= 1 && currentDay <= 7) {
console.log('Sending Employee of the Month voting reminders...');

const teamsSnapshot = await db.collection('teams')
.where('slackChannel', '!=', null)
.get();

for (const teamDoc of teamsSnapshot.docs) {
const teamData = teamDoc.data();
const channel = teamData.slackChannel;

// Check if reminders are enabled for this channel
const prefs = reminderPreferences.get(channel) || { enabled: true };
if (prefs.enabled === false) {
console.log(`Reminders disabled for channel ${channel}, skipping voting reminder`);
continue;
}

try {
await app.client.chat.postMessage({
channel: channel,
text: '🗳️ Employee of the Month - Voting Open!',
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: '🗳️ Employee of the Month - Voting Open!',
emoji: true
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Voting closes in ${7 - currentDay} days!*\n\nVote for last month's Employee of the Month.`
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: '*How to vote:*\nUse `/vehicle nominations` to see nominees and cast your vote.\n\nRemember: You can only vote once!'
}
}
]
});
} catch (error) {
console.error(`Failed to send voting reminder to channel ${channel}:`, error);
}
}
}

} catch (error) {
console.error('Error sending Employee of the Month reminders:', error);
}
});

// Winner announcement on the 8th of each month
cron.schedule('0 10 8 * *', async () => {
try {
console.log('Running Employee of the Month winner announcement...');

// Get last month's nominations
const now = new Date();
let checkMonth = now.getMonth() - 1;
let checkYear = now.getFullYear();

if (checkMonth < 0) {
checkMonth = 11;
checkYear = checkYear - 1;
}

const nominationsSnapshot = await db.collection('employee_nominations')
.where('month', '==', checkMonth)
.where('year', '==', checkYear)
.orderBy('votes', 'desc')
.limit(1)
.get();

if (nominationsSnapshot.empty) {
console.log('No nominations found for last month');
return;
}

const winner = nominationsSnapshot.docs[0].data();

// Post to all team channels
const teamsSnapshot = await db.collection('teams')
.where('slackChannel', '!=', null)
.get();

for (const teamDoc of teamsSnapshot.docs) {
const teamData = teamDoc.data();
const channel = teamData.slackChannel;

// Check if reminders are enabled for this channel
const prefs = reminderPreferences.get(channel) || { enabled: true };
if (prefs.enabled === false) {
console.log(`Reminders disabled for channel ${channel}, skipping winner announcement`);
continue;
}

try {
await app.client.chat.postMessage({
channel: channel,
text: `🏆 Employee of the Month Winner!`,
blocks: [
{
type: 'header',
text: {
type: 'plain_text',
text: '🏆 Employee of the Month Winner!',
emoji: true
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Congratulations to ${winner.nomineeName}!*\n\n${new Date(checkYear, checkMonth).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })} Employee of the Month`
}
},
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Nominated by:* ${winner.nominatorName}\n*Reason:* "${winner.reason}"\n*Total Votes:* ${winner.votes || 0}`
}
},
{
type: 'context',
elements: [
{
type: 'mrkdwn',
text: '🎉 Thank you to everyone who participated in nominations and voting!'
}
]
}
]
});
} catch (error) {
console.error(`Failed to post winner to channel ${channel}:`, error);
}
}

// Store winner record
await db.collection('employee_of_month_winners').add({
...winner,
announcedAt: admin.firestore.FieldValue.serverTimestamp()
});

} catch (error) {
console.error('Error announcing Employee of the Month:', error);
}
});

// Handle bot mentions for easy setup
app.event('app_mention', async ({ event, say }) => {
const text = event.text.toLowerCase();

if (text.includes('setup') || text.includes('link') || text.includes('connect')) {
const userId = event.user;
const channelId = event.channel;

try {
const userInfo = await app.client.users.info({ user: userId });
const userEmail = userInfo.user.profile.email;
const displayName = userInfo.user.profile.real_name || 
     userInfo.user.profile.display_name || 
     userInfo.user.name || 
     userEmail.split('@')[0];

const usersRef = db.collection('users');
const userQuery = await usersRef.where('email', '==', userEmail).get();

if (userQuery.empty) {
await say('❌ You must be a registered user to set up channels.');
return;
}

const userData = userQuery.docs[0];
const user = { id: userData.id, ...userData.data() };

// Show team selection
const teamsSnapshot = await db.collection('teams').get();

if (teamsSnapshot.empty) {
await say('❌ No teams found in the system.');
return;
}

const teamOptions = teamsSnapshot.docs.map(doc => {
const team = doc.data();
const linkedStatus = team.slackChannel ? `(linked to <#${team.slackChannel}>)` : '(not linked)';
return `• *${team.name}* ${linkedStatus}`;
}).join('\n');

await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: `*Select a team to link to this channel:*\n${teamOptions}\n\nUse: \`/vehicle setup link [team name]\`\nExample: \`/vehicle setup link rockford\`\n\nOr link your personal updates:\n\`/vehicle setup user\``
}
}
]
});
} catch (error) {
console.error('Error in app mention setup:', error);
await say('❌ Error setting up channel. Try using `/vehicle setup link [team]` instead.');
}
} else if (text.includes('help')) {
await say({
blocks: [
{
type: 'section',
text: {
type: 'mrkdwn',
text: 'Hi! I\'m the Vehicle Tracker Bot. Here\'s how to get started:\n\n' +
'*Quick Setup:*\n' +
'• Use `/vehicle setup link [team]` to link a team to this channel\n' +
'• Use `/vehicle setup user` to link your personal updates\n' +
'• Example: `/vehicle setup link rockford`\n\n' +
'*Account Linking:*\n' +
'• Use `/vehicle link [url]` to link your VehicleTracker account\n' +
'• This ensures your correct name appears on all actions\n\n' +
'*Available Commands:*\n' +
'• `/vehicle list` - Show vehicles for channel\'s team\n' +
'• `/vehicle list [team]` - Show vehicles for specific team\n' +
'• `/vehicle my` or `/vehicle mine` - Show only your vehicles\n' +
'• `/vehicle search [term]` - Search vehicles\n' +
'• `/vehicle stats` - Team statistics\n' +
'• `/vehicle leaderboard` - Global user rankings\n' +
'• `/vehicle teams` - Show all teams\n' +
'• `/vehicle setup` - Channel configuration\n' +
'• `/vehicle reminders [minutes]` - Set reminder interval\n' +
'• `/vehicle reminders enable/disable` - Control reminders\n' +
'• `/vehicle link [url]` - Link your VehicleTracker account\n' +
'• `/vehicle unlink` - Unlink your account\n' +
'• `/vehicle posting` - Set where your updates post\n' +
'• `/vehicle nominate @user [reason]` - Nominate for Employee of the Month\n' +
'• `/vehicle nominations` - View and vote on nominations\n\n' +
'• `/vehicle help` - Show this help message'
}
}
]
});
} else {
await say('👋 Hi! Need help? Try "@Vehicle Tracker help" or use `/vehicle help`');
}
});

// Handle open_maps action (no action needed, just acknowledge)
app.action('open_maps', async ({ ack }) => {
await ack();
});

// Error handlers
app.error(async (error) => {
console.error('Slack app error:', error);
console.error('Error details:', {
message: error.message,
code: error.code,
data: error.data
});

// If it's a rate limit error, log it specifically
if (error.code === 'slack_webapi_rate_limited_error') {
console.error('Rate limited! Retry after:', error.data?.retryAfter);
}
});

// Start the app
const startApp = async () => {
try {
// Load reminder intervals from database
await loadReminderIntervals();

// Load user-linked channels
await loadUserLinkedChannels();

// Load user posting preferences
await loadUserPostingPreferences();

// Load reminder preferences
await loadReminderPreferences();

// Start the Slack app
await app.start();
console.log('⚡️ Vehicle Tracker Slack Bot is running!');
console.log(`   Socket Mode: Enabled`);
console.log(`   Log Level: ${process.env.LOG_LEVEL || 'debug'}`);

// Start the webhook server
const webhookPort = process.env.WEBHOOK_PORT || 3001;
webhookApp.listen(webhookPort, () => {
console.log(`🌐 Webhook server running on port ${webhookPort}`);
console.log(`   Vehicle Update: http://localhost:${webhookPort}/api/slack/vehicle-update`);
console.log(`   OAuth endpoint: http://localhost:${webhookPort}/api/slack-oauth`);
console.log(`   Health check: http://localhost:${webhookPort}/health`);
console.log(`   Teams list: http://localhost:${webhookPort}/api/teams/list`);
});

// Test authentication
try {
const auth = await app.client.auth.test({
token: process.env.SLACK_BOT_TOKEN
});

console.log('✅ Bot authenticated:', {
workspace: auth.team,
botUser: auth.user,
botId: auth.user_id
});
} catch (authError) {
console.error('❌ Authentication failed:', authError);
throw authError;
}

// Test slash command registration
console.log('📝 Listening for slash commands:');
console.log('   - /vehicle');
console.log('   - /vehicle-setup');
console.log('   - /vehicle-test');

// Load team channel mappings
const teamsSnapshot = await db.collection('teams').get();
let teamsWithSlack = 0;
let teamChannelList = [];

teamsSnapshot.forEach(doc => {
const teamData = doc.data();
if (teamData.slackChannel) {
teamChannels.set(doc.id, teamData.slackChannel);
channelTeams.set(teamData.slackChannel, doc.id);
teamsWithSlack++;
teamChannelList.push({
team: teamData.name,
channel: teamData.slackChannelName || 'Unknown'
});
}
});

console.log(`📊 Loaded ${teamsWithSlack} teams with Slack integration`);

if (teamChannelList.length > 0) {
console.log('🔗 Team-Channel Mappings:');
teamChannelList.forEach(({ team, channel }) => {
console.log(`   - ${team} → #${channel}`);
});
} else {
console.log('ℹ️  No teams have Slack integration yet');
console.log('   Use `/vehicle setup link [team]` in a channel to get started');
}

// Show environment status
console.log('\n📋 Environment Status:');
console.log(`   Canvas module: ${createCanvas ? '✅ Enabled' : '❌ Disabled (image export unavailable)'}`);
console.log(`   Scheduled reminders: ${process.env.ENABLE_SCHEDULED_REMINDERS === 'true' ? '✅ Enabled' : '❌ Disabled'}`);
console.log(`   Debug mode: ${process.env.DEBUG_SLACK_EVENTS === 'true' ? '✅ Enabled' : '❌ Disabled'}`);

// Log all incoming events for debugging if enabled
if (process.env.DEBUG_SLACK_EVENTS === 'true') {
app.use(async ({ payload, next }) => {
const eventType = payload.type || payload.command || 'unknown';
console.log(`[DEBUG] Incoming event: ${eventType}`);
if (payload.text) {
console.log(`[DEBUG] Text: ${payload.text}`);
}
await next();
});
}

console.log('\n✅ Vehicle Tracker Bot is ready!');
console.log('=====================================\n');

} catch (error) {
console.error('❌ Failed to start app:', error);
console.error('Stack trace:', error.stack);

// Provide specific error guidance
if (error.message?.includes('invalid_auth')) {
console.error('\n⚠️  Authentication Error:');
console.error('   Check your SLACK_BOT_TOKEN environment variable');
} else if (error.message?.includes('account_inactive')) {
console.error('\n⚠️  Account Inactive:');
console.error('   Your Slack workspace may be inactive or the bot was removed');
} else if (error.message?.includes('not_in_channel')) {
console.error('\n⚠️  Channel Access Error:');
console.error('   Make sure to invite the bot to channels with: /invite @Vehicle Tracker');
}

process.exit(1);
}
};

// Graceful shutdown
process.on('SIGTERM', async () => {
console.log('SIGTERM received, shutting down gracefully...');

// Clear all intervals
if (global.reminderInterval) {
clearInterval(global.reminderInterval);
}

// Save any pending state
console.log('Saving reminder states...');
// Could save reminderStates to database here if needed

console.log('Shutdown complete');
process.exit(0);
});

process.on('SIGINT', async () => {
console.log('\nSIGINT received, shutting down gracefully...');
process.exit(0);
});

// Export for use in other modules
module.exports = {
app,
webhookApp,
startApp,
buildVehicleCard,
generateVehicleCardImage,
loadAllTeamsVehicles,
loadTeamVehicles,
loadUserVehicles,
sendScheduledReminders,
getCompleteVehicleData,
formatTimestamp,
calculateElapsedTime,
getTimerDisplay,
getStatusColor,
showGlobalLeaderboard,
handleNominationCommand,
showNominations,
formatRecoveryForSlack,        // ENHANCED
postDetailedRecoveryReport     // ENHANCED
};

// Start if run directly
if (require.main === module) {
console.log('=====================================');
console.log('🚗 Vehicle Tracker Slack Bot v2.0');
console.log('=====================================');
console.log('Starting up...\n');

// Check required environment variables
const requiredEnvVars = [
'SLACK_BOT_TOKEN',
'SLACK_SIGNING_SECRET',
'SLACK_APP_TOKEN',
'FIREBASE_PROJECT_ID',
'FIREBASE_CLIENT_EMAIL',
'FIREBASE_PRIVATE_KEY'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
console.error('❌ Missing required environment variables:');
missingVars.forEach(varName => {
console.error(`   - ${varName}`);
});
console.error('\nPlease set these in your .env file or environment');
process.exit(1);
}

// Optional environment variables
const optionalEnvVars = {
'SLACK_CLIENT_ID': 'Required for OAuth flow',
'SLACK_CLIENT_SECRET': 'Required for OAuth flow',
'WEBHOOK_PORT': 'Defaults to 3001',
'LOG_LEVEL': 'Defaults to debug',
'ENABLE_SCHEDULED_REMINDERS': 'Defaults to false',
'REMINDER_INTERVAL_MINUTES': 'Defaults to 30',
'DEBUG_SLACK_EVENTS': 'Defaults to false'
};

console.log('📋 Optional environment variables:');
Object.entries(optionalEnvVars).forEach(([varName, description]) => {
const value = process.env[varName];
const status = value ? '✅' : '⚠️';
console.log(`   ${status} ${varName}: ${value || `Not set (${description})`}`);
});

console.log('\n');

// Start the application
startApp();
}