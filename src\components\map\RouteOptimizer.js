import React, { useContext } from 'react';
import { MapContext } from '../MapContext';
import { calculateDistance } from '../../utils/mapUtils';

const RouteOptimizer = () => {
  // Replace useMap() with useContext(MapContext)
  const {
    mapRef,
    currentLocation,
    locations,
    isAdmin,
    optimizedRoute,
    setOptimizedRoute,
    activeTab,
    selectedLocation,
    setSelectedLocation,
    setDetailsPanelLocation,
    setDetailsVisible
  } = useContext(MapContext);
  
  // Your component logic
  
  // Example of rendering the optimized route
  if (activeTab !== 'route') return null;
  
  // Calculate total distance
  const totalDistance = optimizedRoute ? optimizedRoute.reduce((sum, item) => sum + item.distanceFromPrevious, 0) : 0;
  
  return (
    <div className="location-section border-t border-gray-700 bg-gray-800" style={{ maxHeight: '200px', overflowY: 'auto' }}>
      <div className="p-2 bg-gray-800">
        <h3 className="text-sm font-semibold text-gray-300 mb-2">Optimized Route</h3>
        
        {!optimizedRoute || optimizedRoute.length === 0 ? (
          <div className="text-center text-gray-400 py-4">
            No pending locations available for routing.
          </div>
        ) : (
          <>
            <ol className="list-decimal list-inside text-xs space-y-1">
              {optimizedRoute.map((item) => (
                <li 
                  key={item.location.id} 
                  className={`py-1 truncate hover:bg-gray-700 cursor-pointer ${selectedLocation && selectedLocation.id === item.location.id ? 'bg-gray-700 font-semibold text-blue-400' : ''}`}
                  onClick={() => {
                    setSelectedLocation(item.location);
                    setDetailsPanelLocation(item.location);
                    setDetailsVisible(true);
                  }}
                >
                  <span>
                    {item.location.name}
                  </span>
                  {/* Additional location details */}
                </li>
              ))}
            </ol>
            
            <div className="mt-2 text-xs text-gray-400 border-t border-gray-700 pt-2 flex justify-between">
              <span>Total: {totalDistance.toFixed(1)} miles</span>
              <span>Approximate drive time: {formatDriveTime(totalDistance)}</span>
            </div>
            
            <div className="mt-2 text-xs text-gray-400 italic">
              This route is optimized based on distance from your current location.
            </div>
          </>
        )}
      </div>
    </div>
  );
};

// Helper function to format drive time
function formatDriveTime(distance) {
  // Assume average speed of 30 mph in urban areas
  const timeInHours = distance / 30;
  
  if (timeInHours < 1/60) {
    return "Less than 1 minute";
  } else if (timeInHours < 1) {
    return `${Math.round(timeInHours * 60)} minutes`;
  } else {
    const hours = Math.floor(timeInHours);
    const minutes = Math.round((timeInHours - hours) * 60);
    return `${hours} hour${hours !== 1 ? 's' : ''}${minutes > 0 ? ` ${minutes} min` : ''}`;
  }
}

export default RouteOptimizer;