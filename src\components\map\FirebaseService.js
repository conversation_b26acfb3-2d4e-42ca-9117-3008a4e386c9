import { 
  getFirestore, 
  doc, 
  collection, 
  getDoc, 
  getDocs, 
  setDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  onSnapshot, 
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  signOut, 
  createUserWithEmailAndPassword, 
  onAuthStateChanged 
} from 'firebase/auth';

// Initialize Firebase services
const db = getFirestore();
const auth = getAuth();

// ===============================================
// Authentication Services
// ===============================================

/**
 * Sign in with email and password
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise} - Auth user object
 */
export const signIn = async (email, password) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    console.error("Error signing in:", error);
    throw error;
  }
};

/**
 * Sign out the current user
 * @returns {Promise}
 */
export const logOut = async () => {
  try {
    await signOut(auth);
    return true;
  } catch (error) {
    console.error("Error signing out:", error);
    throw error;
  }
};

/**
 * Create a new user account
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise} - Auth user object
 */
export const createAccount = async (email, password) => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    console.error("Error creating account:", error);
    throw error;
  }
};

/**
 * Set up auth state listener
 * @param {Function} callback - Function to call with user object
 * @returns {Function} - Unsubscribe function
 */
export const listenToAuthState = (callback) => {
  return onAuthStateChanged(auth, (user) => {
    callback(user);
  });
};

/**
 * Get the current authenticated user
 * @returns {Object|null} - Current user or null
 */
export const getCurrentUser = () => {
  return auth.currentUser;
};

// ===============================================
// User Services
// ===============================================

/**
 * Create or update a user profile
 * @param {string} uid - User ID
 * @param {Object} profileData - User profile data
 * @returns {Promise}
 */
export const updateUserProfile = async (uid, profileData) => {
  try {
    const userDocRef = doc(db, 'userProfiles', uid);
    await setDoc(userDocRef, {
      ...profileData,
      updatedAt: serverTimestamp()
    }, { merge: true });
    return true;
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw error;
  }
};

/**
 * Get a user profile
 * @param {string} uid - User ID
 * @returns {Promise} - User profile data
 */
export const getUserProfile = async (uid) => {
  try {
    const userDoc = await getDoc(doc(db, 'userProfiles', uid));
    if (userDoc.exists()) {
      return userDoc.data();
    }
    return null;
  } catch (error) {
    console.error("Error getting user profile:", error);
    throw error;
  }
};

/**
 * Check if user has a specific tag
 * @param {string} uid - User ID
 * @param {string} tagName - Tag to check for
 * @returns {Promise<boolean>} - True if user has tag
 */
export const checkUserHasTag = async (uid, tagName) => {
  try {
    const userDoc = await getDoc(doc(db, 'userProfiles', uid));
    if (userDoc.exists() && userDoc.data().tags) {
      const tags = userDoc.data().tags || [];
      return tags.includes(tagName);
    }
    return false;
  } catch (error) {
    console.error("Error checking user tag:", error);
    return false;
  }
};

/**
 * Check if user is an admin
 * @param {string} uid - User ID
 * @returns {Promise<boolean>} - True if user is admin
 */
export const checkIfUserIsAdmin = async (uid) => {
  try {
    const userDoc = await getDoc(doc(db, 'userProfiles', uid));
    if (userDoc.exists() && userDoc.data().isAdmin) {
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error checking admin status:", error);
    return false;
  }
};

/**
 * Get user profile picture
 * @param {string} uid - User ID
 * @returns {Promise<string|null>} - Profile picture URL or null
 */
export const getUserProfilePicture = async (uid) => {
  try {
    const userDoc = await getDoc(doc(db, 'userProfiles', uid));
    if (userDoc.exists() && userDoc.data().photoBase64) {
      return userDoc.data().photoBase64;
    }
    return null;
  } catch (error) {
    console.error("Error getting user profile picture:", error);
    return null;
  }
};

/**
 * Get user display name
 * @param {string} uid - User ID
 * @returns {Promise<string|null>} - Display name or null
 */
export const getUserDisplayName = async (uid) => {
  try {
    const userDoc = await getDoc(doc(db, 'userProfiles', uid));
    if (userDoc.exists() && userDoc.data().displayName) {
      return userDoc.data().displayName;
    }
    return null;
  } catch (error) {
    console.error("Error getting user display name:", error);
    return null;
  }
};

// ===============================================
// User Location Services
// ===============================================

/**
 * Update user location
 * @param {string} uid - User ID
 * @param {Object} position - Position object with lat/lng
 * @returns {Promise}
 */
export const updateUserLocation = async (uid, position) => {
  try {
    const userLocRef = doc(db, 'userLocations', uid);
    
    // Check if document exists
    const userDoc = await getDoc(userLocRef);
    
    if (userDoc.exists()) {
      return updateDoc(userLocRef, {
        position: position,
        lastUpdated: serverTimestamp(),
        online: true
      });
    } else {
      // Get user info from profile if available
      let displayName = null;
      let email = null;
      
      try {
        const profileDoc = await getDoc(doc(db, 'userProfiles', uid));
        if (profileDoc.exists()) {
          displayName = profileDoc.data().displayName;
          email = profileDoc.data().email;
        }
      } catch (err) {
        console.warn("Couldn't fetch user profile:", err);
      }
      
      return setDoc(userLocRef, {
        uid: uid,
        displayName: displayName || '',
        email: email || '',
        position: position,
        lastUpdated: serverTimestamp(),
        online: true
      });
    }
  } catch (error) {
    console.error("Error updating user location:", error);
    throw error;
  }
};

/**
 * Get all online users
 * @returns {Promise<Array>} - Array of user objects
 */
export const getOnlineUsers = async () => {
  try {
    const snapshot = await getDocs(collection(db, 'userLocations'));
    const users = [];
    
    snapshot.forEach(doc => {
      users.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return users;
  } catch (error) {
    console.error("Error getting online users:", error);
    throw error;
  }
};

/**
 * Listen to all user locations
 * @param {Function} callback - Function to call with users array
 * @returns {Function} - Unsubscribe function
 */
export const listenToUserLocations = (callback) => {
  const unsubscribe = onSnapshot(
    collection(db, 'userLocations'),
    (snapshot) => {
      const users = [];
      
      snapshot.forEach(doc => {
        const userData = doc.data();
        users.push({
          ...userData,
          id: doc.id
        });
      });
      
      callback(users);
    },
    (error) => {
      console.error("Error in user locations listener:", error);
    }
  );
  
  return unsubscribe;
};

/**
 * Update user online status
 * @param {string} uid - User ID
 * @param {boolean} isOnline - Online status
 * @returns {Promise}
 */
export const updateUserOnlineStatus = async (uid, isOnline) => {
  try {
    const userLocRef = doc(db, 'userLocations', uid);
    
    // Check if document exists
    const userDoc = await getDoc(userLocRef);
    
    if (userDoc.exists()) {
      return updateDoc(userLocRef, {
        online: isOnline,
        lastUpdated: serverTimestamp()
      });
    }
    
    // If not existing and going offline, no need to create
    if (!isOnline) return null;
    
    // Otherwise create a new document for online user
    // Get user info from profile if available
    let displayName = null;
    let email = null;
    let position = { lat: 0, lng: 0 };
    
    try {
      const profileDoc = await getDoc(doc(db, 'userProfiles', uid));
      if (profileDoc.exists()) {
        displayName = profileDoc.data().displayName;
        email = profileDoc.data().email;
      }
    } catch (err) {
      console.warn("Couldn't fetch user profile:", err);
    }
    
    return setDoc(userLocRef, {
      uid: uid,
      displayName: displayName || '',
      email: email || '',
      position: position,
      lastUpdated: serverTimestamp(),
      online: true
    });
  } catch (error) {
    console.error("Error updating user online status:", error);
    throw error;
  }
};

// ===============================================
// User Trace Services
// ===============================================

/**
 * Add point to user trace
 * @param {string} uid - User ID
 * @param {Object} point - Point with lat/lng
 * @returns {Promise}
 */
export const addPointToUserTrace = async (uid, point) => {
  try {
    const userTraceRef = doc(db, 'userTraces', uid);
    
    // Check if document exists
    const userTraceDoc = await getDoc(userTraceRef);
    
    if (userTraceDoc.exists()) {
      const existingTrace = userTraceDoc.data().trace || [];
      
      // Only add if we've moved enough from the last point
      if (existingTrace.length > 0) {
        const lastPoint = existingTrace[existingTrace.length - 1];
        const lastPosition = {
          lat: lastPoint.lat,
          lng: lastPoint.lng
        };
        
        // Calculate distance function - this is simplified, in reality you'd import a utility
        const distance = calculateDistance(lastPosition, point);
        
        if (distance > 0.006) { // ~30 feet (0.006 miles)
          return updateDoc(userTraceRef, {
            trace: [...existingTrace, {
              lat: point.lat,
              lng: point.lng,
              timestamp: serverTimestamp()
            }]
          });
        }
        
        return null; // No update needed
      } else {
        // First point in trace
        return updateDoc(userTraceRef, {
          trace: [{
            lat: point.lat,
            lng: point.lng,
            timestamp: serverTimestamp()
          }]
        });
      }
    } else {
      // Create new trace
      return setDoc(userTraceRef, {
        uid: uid,
        trace: [{
          lat: point.lat,
          lng: point.lng,
          timestamp: serverTimestamp()
        }]
      });
    }
  } catch (error) {
    console.error("Error adding point to user trace:", error);
    throw error;
  }
};

/**
 * Listen to all user traces
 * @param {Function} callback - Function to call with traces object
 * @returns {Function} - Unsubscribe function
 */
export const listenToUserTraces = (callback) => {
  const unsubscribe = onSnapshot(
    collection(db, 'userTraces'),
    (snapshot) => {
      const traces = {};
      
      snapshot.forEach(doc => {
        const traceData = doc.data();
        traces[traceData.uid] = traceData.trace || [];
      });
      
      callback(traces);
    },
    (error) => {
      console.error("Error in user traces listener:", error);
    }
  );
  
  return unsubscribe;
};

/**
 * Clear user trace
 * @param {string} uid - User ID
 * @returns {Promise}
 */
export const clearUserTrace = async (uid) => {
  try {
    const userTraceRef = doc(db, 'userTraces', uid);
    return updateDoc(userTraceRef, {
      trace: []
    });
  } catch (error) {
    console.error("Error clearing user trace:", error);
    throw error;
  }
};

// ===============================================
// Location Services
// ===============================================

/**
 * Get all locations
 * @returns {Promise<Array>} - Array of location objects
 */
export const getAllLocations = async () => {
  try {
    const snapshot = await getDocs(collection(db, 'locations'));
    const locations = [];
    
    snapshot.forEach(doc => {
      locations.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return locations;
  } catch (error) {
    console.error("Error getting locations:", error);
    throw error;
  }
};

/**
 * Listen to all locations
 * @param {Function} callback - Function to call with locations array
 * @returns {Function} - Unsubscribe function
 */
export const listenToLocations = (callback) => {
  const unsubscribe = onSnapshot(
    collection(db, 'locations'),
    (snapshot) => {
      const fetchedLocations = [];
      let foundHomeLocation = false;
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        const location = {
          id: doc.id,
          name: data.name,
          position: {
            lat: data.position.lat,
            lng: data.position.lng
          },
          isAdminOnly: data.isAdminOnly || false,
          isPriority: data.isPriority || false,
          details: data.details || '',
          images: data.images || [],
          parkingSide: data.parkingSide || null,
          createdBy: data.createdBy || null,
          createdAt: data.createdAt || null,
          address: data.address || '',
          intersection: data.intersection || '',
          status: data.status || 'pending',
          pickedUpBy: data.pickedUpBy || null,
          pickedUpAt: data.pickedUpAt || null,
          isHome: data.isHome || false,
          // Vehicle-specific fields
          plateNumber: data.plateNumber || '',
          vin: data.vin || '',
          driveType: data.driveType || '',
          make: data.make || '',
          model: data.model || '',
          year: data.year || '',
          case: data.case || '',
          mileage: data.mileage || ''
        };
        
        fetchedLocations.push(location);
      });
      
      callback(fetchedLocations);
    },
    (error) => {
      console.error("Error in locations listener:", error);
    }
  );
  
  return unsubscribe;
};

/**
 * Add a new location
 * @param {Object} locationData - Location data
 * @returns {Promise<string>} - New location ID
 */
export const addLocation = async (locationData) => {
  try {
    // Make sure timestamp fields are proper Firebase timestamps
    const dataWithTimestamp = {
      ...locationData,
      createdAt: serverTimestamp()
    };
    
    const docRef = await addDoc(collection(db, 'locations'), dataWithTimestamp);
    return docRef.id;
  } catch (error) {
    console.error("Error adding location:", error);
    throw error;
  }
};

/**
 * Update a location
 * @param {string} locationId - Location ID
 * @param {Object} locationData - Updated location data
 * @returns {Promise}
 */
export const updateLocation = async (locationId, locationData) => {
  try {
    return updateDoc(doc(db, 'locations', locationId), locationData);
  } catch (error) {
    console.error("Error updating location:", error);
    throw error;
  }
};

/**
 * Delete a location
 * @param {string} locationId - Location ID
 * @returns {Promise}
 */
export const deleteLocation = async (locationId) => {
  try {
    return deleteDoc(doc(db, 'locations', locationId));
  } catch (error) {
    console.error("Error deleting location:", error);
    throw error;
  }
};

/**
 * Mark location as picked up
 * @param {string} locationId - Location ID
 * @param {string} userId - User ID who picked up
 * @param {Object} additionalData - Additional data to update
 * @returns {Promise}
 */
export const markLocationAsPickedUp = async (locationId, userId, additionalData = {}) => {
  try {
    return updateDoc(doc(db, 'locations', locationId), {
      status: 'picked-up',
      pickedUpBy: userId,
      pickedUpAt: serverTimestamp(),
      ...additionalData
    });
  } catch (error) {
    console.error("Error marking location as picked up:", error);
    throw error;
  }
};

// ===============================================
// Chat Services
// ===============================================

/**
 * Add a chat message
 * @param {Object} messageData - Message data
 * @returns {Promise<string>} - New message ID
 */
export const addChatMessage = async (messageData) => {
  try {
    // Make sure timestamp fields are proper Firebase timestamps
    const dataWithTimestamp = {
      ...messageData,
      timestamp: serverTimestamp()
    };
    
    const docRef = await addDoc(collection(db, 'chatMessages'), dataWithTimestamp);
    return docRef.id;
  } catch (error) {
    console.error("Error adding chat message:", error);
    throw error;
  }
};

/**
 * Listen to chat messages
 * @param {Function} callback - Function to call with messages array
 * @returns {Function} - Unsubscribe function
 */
export const listenToChatMessages = (callback) => {
  const unsubscribe = onSnapshot(
    collection(db, 'chatMessages'),
    (snapshot) => {
      const messages = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        
        // Convert Firestore timestamp to JS Date
        const timestamp = data.timestamp ? new Date(data.timestamp.toDate()) : new Date();
        
        messages.push({
          id: doc.id,
          text: data.text || '',
          sender: {
            uid: data.sender?.uid || 'unknown',
            name: data.sender?.name || 'Unknown User',
            photo: data.sender?.photo || null
          },
          timestamp: timestamp,
          mediaUrl: data.mediaUrl || null,
          mediaType: data.mediaType || null // 'image' or 'video'
        });
      });
      
      // Sort by timestamp
      messages.sort((a, b) => a.timestamp - b.timestamp);
      
      callback(messages);
    },
    (error) => {
      console.error("Error in chat messages listener:", error);
    }
  );
  
  return unsubscribe;
};

/**
 * Add a direct message
 * @param {string} senderId - Sender user ID
 * @param {string} recipientId - Recipient user ID
 * @param {Object} messageData - Message data
 * @returns {Promise<string>} - New message ID
 */
export const addDirectMessage = async (senderId, recipientId, messageData) => {
  try {
    // Create a unique chat ID sorted by user IDs to ensure consistency
    const chatId = [senderId, recipientId].sort().join('_');
    
    // Add to Firestore in the directMessages collection
    const messagesRef = collection(db, 'directMessages', chatId, 'messages');
    const docRef = await addDoc(messagesRef, {
      ...messageData,
      timestamp: serverTimestamp()
    });
    
    // Also update the DM metadata document
    await setDoc(doc(db, 'directMessages', chatId), {
      participants: [senderId, recipientId],
      lastMessage: messageData.text,
      lastMessageTime: serverTimestamp(),
      lastMessageSender: senderId,
      [recipientId + '_unread']: true // Flag as unread for recipient
    }, { merge: true });
    
    return docRef.id;
  } catch (error) {
    console.error("Error adding direct message:", error);
    throw error;
  }
};

/**
 * Listen to direct messages between two users
 * @param {string} userId1 - First user ID
 * @param {string} userId2 - Second user ID
 * @param {Function} callback - Function to call with messages array
 * @returns {Function} - Unsubscribe function
 */
export const listenToDirectMessages = (userId1, userId2, callback) => {
  // Create a unique chat ID sorted by user IDs to ensure consistency
  const chatId = [userId1, userId2].sort().join('_');
  
  const unsubscribe = onSnapshot(
    collection(db, 'directMessages', chatId, 'messages'),
    (snapshot) => {
      const messages = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        
        // Convert Firestore timestamp to JS Date
        const timestamp = data.timestamp ? new Date(data.timestamp.toDate()) : new Date();
        
        messages.push({
          id: doc.id,
          text: data.text || '',
          senderId: data.senderId,
          senderName: data.senderName || 'Unknown',
          senderPhoto: data.senderPhoto || null,
          timestamp: timestamp,
          mediaUrl: data.mediaUrl || null,
          mediaType: data.mediaType || null
        });
      });
      
      // Sort by timestamp
      messages.sort((a, b) => a.timestamp - b.timestamp);
      
      callback(messages);
    },
    (error) => {
      console.error("Error in direct messages listener:", error);
    }
  );
  
  return unsubscribe;
};

// ===============================================
// Clock In/Out Services
// ===============================================

/**
 * Clock in a user
 * @param {string} uid - User ID
 * @param {Object} position - Current position
 * @returns {Promise}
 */
export const clockInUser = async (uid, position) => {
  try {
    const userStatusRef = doc(db, 'userStatus', uid);
    return setDoc(userStatusRef, {
      clockedIn: true,
      clockInTime: serverTimestamp(),
      lastPosition: position
    }, { merge: true });
  } catch (error) {
    console.error("Error clocking in user:", error);
    throw error;
  }
};

/**
 * Clock out a user
 * @param {string} uid - User ID
 * @param {Object} position - Current position
 * @param {Object} shiftSummary - Summary of the shift
 * @returns {Promise}
 */
export const clockOutUser = async (uid, position, shiftSummary) => {
  try {
    const userStatusRef = doc(db, 'userStatus', uid);
    return setDoc(userStatusRef, {
      clockedIn: false,
      clockOutTime: serverTimestamp(),
      lastPosition: position,
      shiftSummary: shiftSummary
    }, { merge: true });
  } catch (error) {
    console.error("Error clocking out user:", error);
    throw error;
  }
};

/**
 * Get user clock status
 * @param {string} uid - User ID
 * @returns {Promise<Object>} - Clock status object
 */
export const getUserClockStatus = async (uid) => {
  try {
    const userDoc = await getDoc(doc(db, 'userStatus', uid));
    if (userDoc.exists()) {
      const data = userDoc.data();
      
      // Convert timestamp to Date if exists
      if (data.clockInTime) {
        data.clockInTime = new Date(data.clockInTime.toDate());
      }
      
      return data;
    }
    return { clockedIn: false };
  } catch (error) {
    console.error("Error getting user clock status:", error);
    return { clockedIn: false };
  }
};

// ===============================================
// Vehicle Inspection Services
// ===============================================

/**
 * Save vehicle inspection
 * @param {string} uid - User ID
 * @param {boolean} isStartInspection - Whether it's start or end inspection
 * @param {Object} inspectionData - Inspection data
 * @returns {Promise<string>} - New inspection ID
 */
export const saveVehicleInspection = async (uid, isStartInspection, inspectionData) => {
  try {
    const docRef = await addDoc(collection(db, 'vehicleInspections'), {
      userId: uid,
      isStartInspection: isStartInspection,
      timestamp: serverTimestamp(),
      ...inspectionData
    });
    return docRef.id;
  } catch (error) {
    console.error("Error saving vehicle inspection:", error);
    throw error;
  }
};

/**
 * Get user's vehicle inspections
 * @param {string} uid - User ID
 * @returns {Promise<Array>} - Inspections array
 */
export const getUserVehicleInspections = async (uid) => {
  try {
    const q = query(collection(db, 'vehicleInspections'), where('userId', '==', uid));
    const snapshot = await getDocs(q);
    
    const inspections = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Convert timestamp to Date if exists
      if (data.timestamp) {
        data.timestamp = new Date(data.timestamp.toDate());
      }
      
      inspections.push({
        id: doc.id,
        ...data
      });
    });
    
    return inspections;
  } catch (error) {
    console.error("Error getting user inspections:", error);
    throw error;
  }
};

// ===============================================
// Screenshot Services
// ===============================================

/**
 * Save map screenshot
 * @param {string} uid - User ID
 * @param {string} name - Screenshot name
 * @param {string} imageBase64 - Base64 encoded image
 * @param {Object} mapInfo - Map center and zoom level
 * @returns {Promise<string>} - New screenshot ID
 */
export const saveMapScreenshot = async (uid, name, imageBase64, mapInfo) => {
  try {
    const docRef = await addDoc(collection(db, 'mapScreenshots'), {
      name,
      imageBase64,
      createdBy: uid,
      createdAt: serverTimestamp(),
      mapCenter: mapInfo.center,
      zoomLevel: mapInfo.zoom,
      visibleLocations: mapInfo.visibleLocationIds || []
    });
    return docRef.id;
  } catch (error) {
    console.error("Error saving map screenshot:", error);
    throw error;
  }
};

/**
 * Get user's map screenshots
 * @param {string} uid - User ID
 * @returns {Promise<Array>} - Screenshots array
 */
export const getUserMapScreenshots = async (uid) => {
  try {
    const q = query(collection(db, 'mapScreenshots'), where('createdBy', '==', uid));
    const snapshot = await getDocs(q);
    
    const screenshots = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Convert timestamp to Date if exists
      if (data.createdAt) {
        data.createdAt = new Date(data.createdAt.toDate());
      }
      
      screenshots.push({
        id: doc.id,
        ...data
      });
    });
    
    return screenshots;
  } catch (error) {
    console.error("Error getting user screenshots:", error);
    throw error;
  }
};

// ===============================================
// Utility Functions
// ===============================================

/**
 * Calculate distance between two points in miles
 * @param {Object} point1 - Point with lat/lng
 * @param {Object} point2 - Point with lat/lng
 * @returns {number} - Distance in miles
 */
function calculateDistance(point1, point2) {
  const R = 3958.8; // Earth's radius in MILES
  const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
  const φ2 = point2.lat * Math.PI/180;
  const Δφ = (point2.lat-point1.lat) * Math.PI/180;
  const Δλ = (point2.lng-point1.lng) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  const d = R * c; // in MILES
  return d;
}

// Export the Firestore and Auth references for advanced usage
export { db, auth };