import React, { useState, useEffect } from 'react';

/**
 * Component for displaying error messages with different styles and severity levels
 */
const ErrorDisplay = ({
  // Error content
  message,
  
  // Display options
  type = 'error', // 'error', 'warning', 'info'
  style = 'inline', // 'inline', 'toast', 'banner'
  
  // Behavior options
  dismissible = true,
  autoDismiss = false,
  autoDismissTimeout = 5000, // Time in ms before auto-dismissing
  
  // Event handlers
  onDismiss = () => {}
}) => {
  const [isVisible, setIsVisible] = useState(true);
  
  // Auto-dismiss functionality
  useEffect(() => {
    if (autoDismiss && isVisible) {
      const timeout = setTimeout(() => {
        handleDismiss();
      }, autoDismissTimeout);
      
      return () => clearTimeout(timeout);
    }
  }, [autoDismiss, isVisible, autoDismissTimeout]);
  
  // Reset visibility when message changes
  useEffect(() => {
    setIsVisible(true);
  }, [message]);
  
  // <PERSON>le dismiss action
  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss();
  };
  
  // Don't render if message is empty or component is not visible
  if (!message || !isVisible) return null;
  
  // Get icon based on type
  const getIcon = () => {
    switch (type) {
      case 'error':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'info':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };
  
  // Get color based on type
  const getColorClasses = () => {
    switch (type) {
      case 'error':
        return 'bg-red-500 bg-opacity-20 text-red-500 border-red-500';
      case 'warning':
        return 'bg-yellow-500 bg-opacity-20 text-yellow-500 border-yellow-500';
      case 'info':
        return 'bg-blue-500 bg-opacity-20 text-blue-500 border-blue-500';
      default:
        return 'bg-gray-500 bg-opacity-20 text-gray-500 border-gray-500';
    }
  };
  
  // Get container classes based on style
  const getContainerClasses = () => {
    const baseClasses = `flex items-center ${getColorClasses()} rounded-md border p-3`;
    
    switch (style) {
      case 'toast':
        return `${baseClasses} fixed top-4 right-4 z-50 shadow-lg max-w-sm`;
      case 'banner':
        return `${baseClasses} w-full rounded-none px-4 py-2`;
      case 'inline':
      default:
        return baseClasses;
    }
  };
  
  return (
    <div 
      className={getContainerClasses()}
      role="alert"
      aria-live="assertive"
    >
      <div className="flex-shrink-0 mr-2">
        {getIcon()}
      </div>
      
      <div className="flex-1 text-sm">
        {message}
      </div>
      
      {dismissible && (
        <button 
          className="flex-shrink-0 ml-2 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current rounded-md"
          onClick={handleDismiss}
          aria-label="Dismiss"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default ErrorDisplay;