import React, { useState, useEffect, useCallback } from 'react';
import { 
  doc, 
  collection,
  query, 
  where, 
  getDocs,
  updateDoc,
  serverTimestamp,
  onSnapshot,
  getFirestore 
} from 'firebase/firestore';

function ZoneAssignmentManager({ 
  teamId, 
  userId = null, // If provided, only show zones for this user
  isAdmin,
  currentUser,
  userDisplayNames,
  userProfilePictures,
  onClose,
  onZoomToZone
}) {
  const db = getFirestore();
  const [zones, setZones] = useState([]);
  const [assignments, setAssignments] = useState({});
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(userId || null);
  const [selectedZone, setSelectedZone] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [viewMode, setViewMode] = useState(userId ? 'userZones' : 'zoneUsers');

  // Load zones and assignments when component mounts
  useEffect(() => {
    if (!db || !teamId) return;

    setIsLoading(true);
    
    // Load zones
    const zonesRef = collection(db, 'teams', teamId, 'zones');
    const unsubscribeZones = onSnapshot(zonesRef, (snapshot) => {
      const zonesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      console.log(`Loaded ${zonesData.length} zones for team ${teamId}`);
      setZones(zonesData);
      
      // Extract assignments
      const allAssignments = {};
      zonesData.forEach(zone => {
        if (zone.assignedUsers && Array.isArray(zone.assignedUsers)) {
          zone.assignedUsers.forEach(userId => {
            if (!allAssignments[userId]) {
              allAssignments[userId] = [];
            }
            allAssignments[userId].push(zone.id);
          });
        }
      });
      setAssignments(allAssignments);
      setIsLoading(false);
    }, (error) => {
      console.error("Error loading zones:", error);
      setError("Failed to load zones");
      setIsLoading(false);
    });
    
    // Load team members
    const loadTeamMembers = async () => {
      try {
        const membersCollection = collection(db, `teams/${teamId}/teamMembers`);
        const membersSnapshot = await getDocs(membersCollection);
        
        const memberPromises = membersSnapshot.docs.map(async (memberDoc) => {
          const memberData = memberDoc.data();
          const userId = memberData.userId;
          
          return {
            id: userId,
            role: memberData.role || 'member',
            displayName: userDisplayNames[userId] || userId.substring(0, 8),
            profilePicture: userProfilePictures[userId] || null,
          };
        });
        
        const members = await Promise.all(memberPromises);
        setUsers(members);
        
        // If selectedUser is not set and we're not in user-specific mode, select the first user
        if (!selectedUser && !userId && members.length > 0) {
          setSelectedUser(members[0].id);
        }
      } catch (error) {
        console.error("Error loading team members:", error);
        setError("Failed to load team members");
      }
    };
    
    loadTeamMembers();
    
    return () => {
      unsubscribeZones();
    };
  }, [db, teamId, userId, userDisplayNames, userProfilePictures, selectedUser]);
  
  // Toggle zone assignment for a user
  const toggleZoneAssignment = useCallback(async (zoneId, userId) => {
    if (!zoneId || !userId) return;
    
    setIsLoading(true);
    
    try {
      // Find the zone
      const zone = zones.find(z => z.id === zoneId);
      if (!zone) {
        throw new Error("Zone not found");
      }
      
      // Clone assigned users array or create if doesn't exist
      const assignedUsers = zone.assignedUsers ? [...zone.assignedUsers] : [];
      
      // Check if user is already assigned
      const userIndex = assignedUsers.indexOf(userId);
      
      if (userIndex >= 0) {
        // Remove user
        assignedUsers.splice(userIndex, 1);
      } else {
        // Add user
        assignedUsers.push(userId);
      }
      
      // Update zone in Firestore
      await updateDoc(doc(db, 'teams', teamId, 'zones', zoneId), {
        assignedUsers,
        updatedAt: serverTimestamp(),
        updatedBy: currentUser.uid
      });
      
      // Update local assignments state for immediate UI feedback
      setAssignments(prev => {
        const newAssignments = { ...prev };
        
        if (!newAssignments[userId]) {
          newAssignments[userId] = [];
        }
        
        if (userIndex >= 0) {
          // Remove zone from user's assignments
          newAssignments[userId] = newAssignments[userId].filter(id => id !== zoneId);
        } else {
          // Add zone to user's assignments
          newAssignments[userId] = [...newAssignments[userId], zoneId];
        }
        
        return newAssignments;
      });
    } catch (error) {
      console.error("Error toggling zone assignment:", error);
      setError("Failed to update zone assignment");
    } finally {
      setIsLoading(false);
    }
  }, [zones, db, teamId, currentUser]);
  
  // Check if a user is assigned to a zone
  const isUserAssignedToZone = useCallback((userId, zoneId) => {
    if (!assignments[userId]) return false;
    return assignments[userId].includes(zoneId);
  }, [assignments]);
  
  // Handle user selection change
  const handleUserChange = (e) => {
    setSelectedUser(e.target.value);
  };
  
  // Get zones assigned to a specific user
  const getUserZones = useCallback((userId) => {
    if (!userId || !assignments[userId]) return [];
    
    return zones.filter(zone => 
      zone.assignedUsers && zone.assignedUsers.includes(userId)
    );
  }, [zones, assignments]);
  
  // Get users assigned to a specific zone
  const getZoneUsers = useCallback((zoneId) => {
    if (!zoneId) return [];
    
    const zone = zones.find(z => z.id === zoneId);
    if (!zone || !zone.assignedUsers) return [];
    
    return users.filter(user => zone.assignedUsers.includes(user.id));
  }, [zones, users]);
  
  // Handle zoom to zone
  const handleZoomToZone = (zoneId) => {
    if (onZoomToZone) onZoomToZone(zoneId);
  };
  
  // Render the user zone view
  const renderUserZonesView = () => {
    if (!selectedUser) {
      return (
        <div className="text-center py-8 text-gray-400">
          <p>Select a user to view their assigned zones</p>
        </div>
      );
    }
    
    const userZones = getUserZones(selectedUser);
    const user = users.find(u => u.id === selectedUser);
    
    return (
      <div>
        <div className="bg-gray-800 p-3 rounded-lg mb-4 flex items-center">
          {user?.profilePicture ? (
            <img
              src={user.profilePicture}
              alt={user.displayName}
              className="w-10 h-10 rounded-full mr-3"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gray-600 mr-3 flex items-center justify-center text-white">
              {user?.displayName?.charAt(0) || '?'}
            </div>
          )}
          <div>
            <h3 className="font-medium text-white">{user?.displayName || 'Unknown User'}</h3>
            <p className="text-xs text-gray-400">{userZones.length} assigned zones</p>
          </div>
        </div>
        
        {userZones.length === 0 ? (
          <div className="bg-gray-800 bg-opacity-50 rounded-lg p-4 text-center">
            <p className="text-gray-400">No zones assigned</p>
          </div>
        ) : (
          <div className="space-y-2">
            {userZones.map(zone => (
              <div 
                key={zone.id}
                className="bg-gray-800 rounded-lg p-3 hover:bg-gray-750 transition-colors"
              >
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-white flex items-center">
                    <span className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: zone.color || '#3b82f6' }}></span>
                    {zone.name}
                  </h4>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleZoomToZone(zone.id)}
                      className="p-1 text-gray-400 hover:text-white"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                    </button>
                    
                    {isAdmin && (
                      <button
                        onClick={() => toggleZoneAssignment(zone.id, selectedUser)}
                        className="p-1 text-gray-400 hover:text-red-500"
                        title="Remove assignment"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
                
                {zone.description && (
                  <p className="text-sm text-gray-400 mt-1">{zone.description}</p>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };
  
  // Render the zone users view
  const renderZoneUsersView = () => {
    if (!selectedZone) {
      return (
        <div className="text-center py-8 text-gray-400">
          <p>Select a zone to view assigned users</p>
        </div>
      );
    }
    
    const zoneUsers = getZoneUsers(selectedZone);
    const zone = zones.find(z => z.id === selectedZone);
    
    return (
      <div>
        <div className="bg-gray-800 p-3 rounded-lg mb-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-white flex items-center">
              <span className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: zone?.color || '#3b82f6' }}></span>
              {zone?.name || 'Unknown Zone'}
            </h3>
            
            <button
              onClick={() => handleZoomToZone(selectedZone)}
              className="p-1 text-gray-400 hover:text-white"
              title="View on map"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
          
          {zone?.description && (
            <p className="text-sm text-gray-400 mt-1">{zone.description}</p>
          )}
          
          <p className="text-xs text-gray-400 mt-2">{zoneUsers.length} assigned users</p>
        </div>
        
        {zoneUsers.length === 0 ? (
          <div className="bg-gray-800 bg-opacity-50 rounded-lg p-4 text-center">
            <p className="text-gray-400">No users assigned</p>
          </div>
        ) : (
          <div className="space-y-2">
            {zoneUsers.map(user => (
              <div 
                key={user.id}
                className="bg-gray-800 rounded-lg p-3 hover:bg-gray-750 transition-colors"
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    {user.profilePicture ? (
                      <img
                        src={user.profilePicture}
                        alt={user.displayName}
                        className="w-8 h-8 rounded-full mr-2"
                      />
                    ) : (
                      <div className="w-8 h-8 rounded-full bg-gray-600 mr-2 flex items-center justify-center text-white">
                        {user.displayName?.charAt(0) || '?'}
                      </div>
                    )}
                    
                    <div>
                      <h4 className="font-medium text-white">{user.displayName}</h4>
                      <p className="text-xs text-gray-400">{user.role}</p>
                    </div>
                  </div>
                  
                  {isAdmin && (
                    <button
                      onClick={() => toggleZoneAssignment(selectedZone, user.id)}
                      className="p-1 text-gray-400 hover:text-red-500"
                      title="Remove assignment"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };
  
  // Render user assignment matrix
  const renderAssignmentMatrix = () => {
    if (users.length === 0 || zones.length === 0) {
      return (
        <div className="text-center py-8 text-gray-400">
          <p>No users or zones available</p>
        </div>
      );
    }
    
    return (
      <div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-gray-800">
              <tr>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  User
                </th>
                {zones.map(zone => (
                  <th key={zone.id} className="px-3 py-3 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">
                    <div className="flex flex-col items-center" title={zone.name}>
                      <span className="w-3 h-3 rounded-full mb-1" style={{ backgroundColor: zone.color || '#3b82f6' }}></span>
                      <span className="truncate max-w-[80px]">{zone.name}</span>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-gray-900 divide-y divide-gray-800">
              {users.map(user => (
                <tr key={user.id} className="hover:bg-gray-800">
                  <td className="px-3 py-2 whitespace-nowrap">
                    <div className="flex items-center">
                      {user.profilePicture ? (
                        <img
                          src={user.profilePicture}
                          alt={user.displayName}
                          className="w-8 h-8 rounded-full mr-2"
                        />
                      ) : (
                        <div className="w-8 h-8 rounded-full bg-gray-600 mr-2 flex items-center justify-center text-white">
                          {user.displayName?.charAt(0) || '?'}
                        </div>
                      )}
                      <span className="text-sm text-white">{user.displayName}</span>
                    </div>
                  </td>
                  
                  {zones.map(zone => (
                    <td key={`${user.id}-${zone.id}`} className="px-3 py-2 whitespace-nowrap text-center">
                      <input
                        type="checkbox"
                        checked={isUserAssignedToZone(user.id, zone.id)}
                        onChange={() => toggleZoneAssignment(zone.id, user.id)}
                        disabled={!isAdmin || isLoading}
                        className="w-4 h-4 text-blue-600 bg-gray-700 rounded border-gray-600 focus:ring-blue-500 focus:ring-2"
                      />
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-lg overflow-hidden max-w-screen-lg mx-auto">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-900 border-b border-gray-700 px-4 py-3 flex justify-between items-center">
        <h2 className="font-semibold text-white">Zone Assignments</h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="bg-red-900 bg-opacity-75 text-red-100 px-4 py-3">
          <div className="flex">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span>{error}</span>
          </div>
        </div>
      )}
      
      {/* View Selector */}
      {!userId && (
        <div className="bg-gray-800 px-4 py-2 border-b border-gray-700">
          <div className="flex">
            <button
              onClick={() => setViewMode('userZones')}
              className={`px-3 py-1 text-sm font-medium rounded-md mr-2 ${
                viewMode === 'userZones'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              User's Zones
            </button>
            <button
              onClick={() => setViewMode('zoneUsers')}
              className={`px-3 py-1 text-sm font-medium rounded-md mr-2 ${
                viewMode === 'zoneUsers'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Zone's Users
            </button>
            <button
              onClick={() => setViewMode('matrix')}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                viewMode === 'matrix'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Assignment Matrix
            </button>
          </div>
        </div>
      )}
      
      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}
      
      {/* Content Area */}
      <div className="p-4 max-h-[calc(80vh-7rem)] overflow-y-auto">
        {/* Selectors and Content */}
        {viewMode === 'userZones' && !userId && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Select User
            </label>
            <select
              value={selectedUser || ''}
              onChange={handleUserChange}
              className="w-full px-3 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="" disabled>Choose a user</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>{user.displayName}</option>
              ))}
            </select>
          </div>
        )}
        
        {viewMode === 'zoneUsers' && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Select Zone
            </label>
            <select
              value={selectedZone || ''}
              onChange={e => setSelectedZone(e.target.value)}
              className="w-full px-3 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="" disabled>Choose a zone</option>
              {zones.map(zone => (
                <option key={zone.id} value={zone.id}>{zone.name}</option>
              ))}
            </select>
          </div>
        )}
        
        {!isLoading && (
          <>
            {viewMode === 'userZones' && renderUserZonesView()}
            {viewMode === 'zoneUsers' && renderZoneUsersView()}
            {viewMode === 'matrix' && renderAssignmentMatrix()}
          </>
        )}
      </div>
    </div>
  );
}

export default ZoneAssignmentManager;