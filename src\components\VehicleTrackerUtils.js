// VehicleTrackerUtils.js - Complete Utility Functions and Constants
// No imports needed - this file contains only utility functions and constants

// Firebase configuration
export const firebaseConfig = {
    apiKey: "AIzaSyDsheDQQhNmVfYl0JQxuR64bl-ciR1rMe8",
    authDomain: "nwrepo-bf088.firebaseapp.com",
    databaseURL: "https://nwrepo-bf088-default-rtdb.firebaseio.com",
    projectId: "nwrepo-bf088",
    storageBucket: "nwrepo-bf088.firebasestorage.app",
    messagingSenderId: "499202829104",
    appId: "1:499202829104:web:c5a451b5ccc68f07dbb6f5",
    measurementId: "G-H3YSTEJ27Q"
  };
  
  // Initialize viewport settings
  export const initializeViewport = () => {
    let viewportMeta = document.querySelector('meta[name="viewport"]');
    if (!viewportMeta) {
      viewportMeta = document.createElement('meta');
      viewportMeta.name = "viewport";
      document.head.appendChild(viewportMeta);
    }
    viewportMeta.content = "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no";
  
    // Force layout refresh on mobile
    document.documentElement.style.display = 'none';
    setTimeout(() => document.documentElement.style.display = '', 10);
  };
  
  // Load PDF libraries dynamically
  export const loadPDFLibraries = () => {
    // Check if libraries are already loaded
    if (document.querySelector('script[src*="jspdf.umd.min.js"]')) {
      return; // Already loading or loaded
    }
  
    // Load jsPDF library
    const jsPdfScript = document.createElement('script');
    jsPdfScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
    jsPdfScript.async = true;
    document.head.appendChild(jsPdfScript);
  
    // Load autoTable plugin after jsPDF loads
    jsPdfScript.onload = function () {
      if (!document.querySelector('script[src*="jspdf.plugin.autotable.min.js"]')) {
        const autoTableScript = document.createElement('script');
        autoTableScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js';
        autoTableScript.async = true;
        document.head.appendChild(autoTableScript);
      }
    };
  };
  
  // User ID extraction from URL
  export const getUserIdFromUrl = () => {
    try {
      console.log('🔍 Current URL:', window.location.href);
      console.log('🔍 Pathname:', window.location.pathname);
      console.log('🔍 Search:', window.location.search);
  
      // Method 1: Query parameters (?user=USER_ID)
      const urlParams = new URLSearchParams(window.location.search);
      const userFromQuery = urlParams.get('user');
      if (userFromQuery && userFromQuery.trim()) {
        console.log('✅ Found user ID in query params:', userFromQuery);
        return userFromQuery.trim();
      }
  
      // Method 2: Path segments (/vehicles/USER_ID)
      const pathSegments = window.location.pathname.split('/').filter(segment => segment);
      console.log('🔍 Path segments:', pathSegments);
  
      // Look for 'vehicles' in path and get the next segment
      const vehiclesIndex = pathSegments.findIndex(segment => segment === 'vehicles');
      if (vehiclesIndex !== -1 && pathSegments[vehiclesIndex + 1]) {
        const userId = pathSegments[vehiclesIndex + 1];
        console.log('✅ Found user ID after vehicles:', userId);
        return userId;
      }
  
      // Method 3: Last segment if it looks like a user ID (length > 10)
      const lastSegment = pathSegments[pathSegments.length - 1];
      if (lastSegment && lastSegment.length > 10) {
        console.log('✅ Using last segment as user ID:', lastSegment);
        return lastSegment;
      }
  
      // Method 4: Hash routing fallback
      if (window.location.hash) {
        const hashSegments = window.location.hash.substring(1).split('/').filter(segment => segment);
        console.log('🔍 Hash segments:', hashSegments);
  
        const vehiclesHashIndex = hashSegments.findIndex(segment => segment === 'vehicles');
        if (vehiclesHashIndex !== -1 && hashSegments[vehiclesHashIndex + 1]) {
          const userId = hashSegments[vehiclesHashIndex + 1];
          console.log('✅ Found user ID in hash:', userId);
          return userId;
        }
      }
  
      console.log('❌ No user ID found in URL');
      return null;
    } catch (error) {
      console.error('❌ Error parsing URL for user ID:', error);
      return null;
    }
  };
  
  // User avatar based on role
  export const getUserAvatar = (role) => {
    const avatars = {
      admin: '👨‍💼',
      manager: '👩‍💼',
      tow: '🚚',
      technician: '👨‍🔧',
      support: '👩‍💻',
      driver: '🚗',
      operator: '📱',
      supervisor: '👨‍🏫'
    };
    return avatars[role] || '👤';
  };
  
  // Date and time utilities
  export const getCurrentWeekId = () => {
    const now = new Date();
    const startOfWeek = new Date(now);
    const dayOfWeek = startOfWeek.getDay();
    const diff = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Monday as start of week
    startOfWeek.setDate(startOfWeek.getDate() - diff);
    startOfWeek.setHours(0, 0, 0, 0);
    return `week_${startOfWeek.getTime()}`;
  };
  
  export const formatDateRange = (startDate, endDate) => {
    const formatDate = (date) => {
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${month}/${day}`;
    };
    return `${formatDate(startDate)} - ${formatDate(endDate)}`;
  };
  
  export const formatNumber = (num) => {
    if (num === undefined || num === null) return '0';
    return new Intl.NumberFormat('en-US').format(Math.round(num * 100) / 100);
  };
  
  // Status utilities
  export const getStatusColor = (status) => {
    switch (status) {
      case 'FOUND': return 'text-yellow-500';
      case 'SECURED': return 'text-green-500';
      case 'PENDING PICKUP': return 'text-blue-500';
      default: return 'text-gray-400';
    }
  };
  
  export const getStatusEmoji = (status) => {
    switch (status) {
      case 'FOUND': return '🔍';
      case 'SECURED': return '🔒';
      case 'PENDING PICKUP': return '🚚';
      default: return '❓';
    }
  };
  
  export const getNotificationColor = (type) => {
    switch (type) {
      case 'team_sync': return 'bg-purple-900 border-purple-600 text-purple-200';
      case 'team_sync_success': return 'bg-green-900 border-green-600 text-green-200';
      case 'team_sync_error': return 'bg-red-900 border-red-600 text-red-200';
      case 'reminder': return 'bg-orange-900 border-orange-600 text-orange-200';
      default: return 'bg-blue-900 border-blue-600 text-blue-200';
    }
  };
  
  // Sound utilities
  export const playNotificationSound = (soundEnabled) => {
    if (!soundEnabled) return;
  
    try {
      // Try the Web Audio API approach first
      if (window.AudioContext || window.webkitAudioContext) {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        const audioCtx = new AudioContext();
  
        // Create notification sound
        const oscillator = audioCtx.createOscillator();
        const gainNode = audioCtx.createGain();
  
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(587.33, audioCtx.currentTime); // D5
        oscillator.connect(gainNode);
        gainNode.connect(audioCtx.destination);
  
        // Fade in and out for pleasant sound
        gainNode.gain.setValueAtTime(0, audioCtx.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.3, audioCtx.currentTime + 0.05);
        gainNode.gain.linearRampToValueAtTime(0, audioCtx.currentTime + 0.5);
  
        oscillator.start();
        oscillator.stop(audioCtx.currentTime + 0.5);
  
        // Play a second tone for the "ding" effect
        setTimeout(() => {
          const oscillator2 = audioCtx.createOscillator();
          const gainNode2 = audioCtx.createGain();
  
          oscillator2.type = 'sine';
          oscillator2.frequency.setValueAtTime(880, audioCtx.currentTime); // A5
          oscillator2.connect(gainNode2);
          gainNode2.connect(audioCtx.destination);
  
          gainNode2.gain.setValueAtTime(0, audioCtx.currentTime);
          gainNode2.gain.linearRampToValueAtTime(0.3, audioCtx.currentTime + 0.05);
          gainNode2.gain.linearRampToValueAtTime(0, audioCtx.currentTime + 0.3);
  
          oscillator2.start();
          oscillator2.stop(audioCtx.currentTime + 0.3);
        }, 150);
  
        return; // We successfully played the sound, so return
      }
  
      // Fallback to Audio element if Web Audio API is not available
      const audio = new Audio("https://assets.mixkit.co/sfx/preview/mixkit-positive-notification-951.mp3");
      audio.volume = 0.5;
      audio.play().catch(e => console.error("Error playing notification:", e));
  
    } catch (error) {
      console.error("Error playing notification sound:", error);
  
      // Final fallback - try to use a simple beep with a data URI
      try {
        const beep = new Audio("data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU0FAACBgIF/gn6Df4B+gn+BfoGAgX6CfoGAgX6Bf4GAgICBf4GAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgYCBgIGAgA==");
        beep.volume = 0.2;
        beep.play().catch(() => console.log("Unable to play notification sound"));
      } catch {
        console.log("All sound methods failed");
      }
    }
  };
  
  export const playMoneySound = (soundEnabled) => {
    if (!soundEnabled) return;
  
    try {
      if (window.AudioContext || window.webkitAudioContext) {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        const audioCtx = new AudioContext();
  
        // Create a "cha-ching" sound effect with two tones
        // First "cha" sound
        setTimeout(() => {
          const osc1 = audioCtx.createOscillator();
          const gain1 = audioCtx.createGain();
  
          osc1.type = 'sine';
          osc1.frequency.setValueAtTime(800, audioCtx.currentTime);
          osc1.frequency.linearRampToValueAtTime(1200, audioCtx.currentTime + 0.1);
          osc1.connect(gain1);
          gain1.connect(audioCtx.destination);
  
          gain1.gain.setValueAtTime(0, audioCtx.currentTime);
          gain1.gain.linearRampToValueAtTime(0.4, audioCtx.currentTime + 0.02);
          gain1.gain.linearRampToValueAtTime(0, audioCtx.currentTime + 0.1);
  
          osc1.start();
          osc1.stop(audioCtx.currentTime + 0.1);
        }, 0);
  
        // Second "ching" sound
        setTimeout(() => {
          const osc2 = audioCtx.createOscillator();
          const gain2 = audioCtx.createGain();
  
          osc2.type = 'sine';
          osc2.frequency.setValueAtTime(1200, audioCtx.currentTime);
          osc2.frequency.linearRampToValueAtTime(1600, audioCtx.currentTime + 0.2);
          osc2.connect(gain2);
          gain2.connect(audioCtx.destination);
  
          gain2.gain.setValueAtTime(0, audioCtx.currentTime);
          gain2.gain.linearRampToValueAtTime(0.5, audioCtx.currentTime + 0.02);
          gain2.gain.linearRampToValueAtTime(0, audioCtx.currentTime + 0.3);
  
          osc2.start();
          osc2.stop(audioCtx.currentTime + 0.3);
        }, 100);
  
      } else {
        // Fallback to pre-loaded cash register sound
        const audio = new Audio("https://assets.mixkit.co/sfx/preview/mixkit-cash-register-kaching-1926.mp3");
        audio.volume = 0.6;
        audio.play().catch(e => console.error("Error playing money sound:", e));
      }
    } catch (error) {
      console.error("Error playing money sound:", error);
      // Fall back to notification sound
      playNotificationSound(soundEnabled);
    }
  };
  
  export const playReminderSound = (soundEnabled) => {
    if (!soundEnabled) return;
  
    try {
      if (window.AudioContext || window.webkitAudioContext) {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        const audioCtx = new AudioContext();
  
        // Create a distinct reminder sound - three quick beeps
        for (let i = 0; i < 3; i++) {
          setTimeout(() => {
            const oscillator = audioCtx.createOscillator();
            const gainNode = audioCtx.createGain();
  
            oscillator.type = 'square';
            oscillator.frequency.setValueAtTime(700, audioCtx.currentTime);
            oscillator.connect(gainNode);
            gainNode.connect(audioCtx.destination);
  
            gainNode.gain.setValueAtTime(0, audioCtx.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.2, audioCtx.currentTime + 0.01);
            gainNode.gain.linearRampToValueAtTime(0, audioCtx.currentTime + 0.1);
  
            oscillator.start();
            oscillator.stop(audioCtx.currentTime + 0.1);
          }, i * 150);
        }
      }
    } catch (error) {
      console.error("Error playing reminder sound:", error);
    }
  };
  
  // Image handling utilities
  export const compressImage = async (file, maxWidth = 1200, maxHeight = 1200, quality = 0.8) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');
          let width = img.width;
          let height = img.height;
  
          // Calculate new dimensions
          if (width > height) {
            if (width > maxWidth) {
              height = height * (maxWidth / width);
              width = maxWidth;
            }
          } else {
            if (height > maxHeight) {
              width = width * (maxHeight / height);
              height = maxHeight;
            }
          }
  
          canvas.width = width;
          canvas.height = height;
  
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);
  
          canvas.toBlob((blob) => {
            resolve(blob);
          }, 'image/jpeg', quality);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    });
  };
  
  export const getGeolocation = () => {
    return new Promise((resolve) => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            resolve({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy
            });
          },
          (error) => {
            console.error("Error getting location:", error);
            resolve(null);
          },
          {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0
          }
        );
      } else {
        resolve(null);
      }
    });
  };
  
  // Navigation utility
  export const navigateToAddress = (address) => {
    if (!address) return;
    
    // Try to use native app navigation first (for mobile devices)
    const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (isMobileDevice) {
      // Try different navigation app URL schemes
      const encodedAddress = encodeURIComponent(address);
      
      // Try Google Maps app first
      window.location.href = `comgooglemaps://?q=${encodedAddress}`;
      
      // Fallback to web-based Google Maps after a short delay
      setTimeout(() => {
        window.open(`https://www.google.com/maps/search/?api=1&query=${encodedAddress}`, '_blank');
      }, 500);
    } else {
      // For desktop, open in new tab
      window.open(`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`, '_blank');
    }
  };
  
  // Timer utilities
  export const calculateElapsedTime = (vehicle) => {
    let startTime;
  
    // Try different timestamp fields
    if (vehicle.createdAt) {
      startTime = vehicle.createdAt.toDate ? vehicle.createdAt.toDate() : new Date(vehicle.createdAt);
    } else if (vehicle.timestamp) {
      startTime = vehicle.timestamp.toDate ? vehicle.timestamp.toDate() : new Date(vehicle.timestamp);
    } else if (vehicle.date) {
      startTime = new Date(vehicle.date);
    } else {
      return null;
    }
  
    const now = Date.now();
    const elapsed = now - startTime.getTime();
  
    return {
      elapsed,
      startTime: startTime.getTime()
    };
  };
  
  export const getTimerDisplay = (vehicle) => {
    const timeData = calculateElapsedTime(vehicle);
    if (!timeData) return null;
    
    const { elapsed } = timeData;
    const minutes = Math.floor(elapsed / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const displayMinutes = minutes % 60;
    const displayHours = hours % 24;
    
    if (days > 0) {
      return `${days}d ${displayHours}h`;
    } else if (hours > 0) {
      return `${hours}h ${displayMinutes}m`;
    }
    return `${minutes}m`;
  };
  
  // Permissions check
  export const checkUserPermissions = (user, userProfile) => {
    if (!user || !userProfile) return false;
  
    // Check if admin
    if (user.role === 'admin') return true;
  
    // Check if has tow truck tag - more flexible matching
    if (userProfile.tags && userProfile.tags.length > 0) {
      return userProfile.tags.some(tag => {
        if (!tag.name) return false;
        const tagName = tag.name.toLowerCase();
        return tagName.includes('tow') || tagName.includes('truck') || tagName.includes('driver') || tagName.includes('secure');
      });
    }
  
    return false;
  };
  
  // Suppress Google API errors on mobile
  export const suppressGoogleAPIErrors = () => {
    window.addEventListener('error', function (e) {
      if (e.message && e.message.includes('Cannot read properties of null (reading \'parent\')') &&
        e.filename && e.filename.includes('googleapis')) {
        console.log('Suppressed Google API error');
        e.stopPropagation();
        return true; // Prevent the error from showing in console
      }
    }, true);
  };