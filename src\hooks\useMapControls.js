import { useState, useEffect, useRef, useCallback } from 'react';
import L from 'leaflet';
import 'leaflet-routing-machine';

/**
 * Custom hook for managing Leaflet map controls and interactions
 * 
 * @param {Object} options - Map configuration options
 * @param {Object} options.defaultPosition - Default map center position
 * @param {number} options.defaultZoom - Default zoom level 
 * @param {boolean} options.darkMode - Whether to use dark mode styling
 * @returns {Object} Map control functions and state
 */
const useMapControls = ({
  defaultPosition = { lat: 37.7749, lng: -122.4194 },
  defaultZoom = 14,
  darkMode = true
} = {}) => {
  // Map references
  const mapRef = useRef(null);
  const mapContainerRef = useRef(null);
  const currentMarkerRef = useRef(null);
  const userLabelRef = useRef(null);
  const routingControlRef = useRef(null);
  const markerClusterRef = useRef(null);
  const userMarkerClusterRef = useRef(null);
  const userLabelsLayerRef = useRef(null);
  const userPathsLayerRef = useRef(null);
  const breadcrumbPathsRef = useRef({});
  const drivingPathRef = useRef(null);
  const navigationElementsRef = useRef([]);
  const userMarkersRef = useRef({});
  const routeAnimationRef = useRef(null);
  
  // Map state
  const [isMapInitialized, setIsMapInitialized] = useState(false);
  const [isMapRefreshing, setIsMapRefreshing] = useState(false);
  const [currentZoom, setCurrentZoom] = useState(defaultZoom);
  const [mapCenter, setMapCenter] = useState(defaultPosition);
  const [mapBounds, setMapBounds] = useState(null);
  
  /**
   * Initialize the map with all required layers and controls
   */
  const initializeMap = useCallback((container) => {
    if (!container || mapRef.current) return false;
    
    try {
      console.log("Initializing OpenStreetMap with Leaflet");
      
      // Create the map
      const map = L.map(container, {
        center: [defaultPosition.lat, defaultPosition.lng],
        zoom: defaultZoom,
        layers: [
          L.tileLayer(
            darkMode 
              ? 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png'
              : 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            {
              attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors' + 
                           (darkMode ? ' &copy; <a href="https://carto.com/attributions">CARTO</a>' : ''),
              maxZoom: 19,
              updateWhenIdle: true,
              updateWhenZooming: false,
              updateInterval: 500
            }
          )
        ],
        zoomControl: true,
        attributionControl: true,
        preferCanvas: true, // Better performance with many markers
        renderer: L.canvas()
      });
      
      mapRef.current = map;
      
      // Create marker clusters for better performance with many markers
      markerClusterRef.current = L.markerClusterGroup({
        maxClusterRadius: 30,
        disableClusteringAtZoom: 15,
        spiderfyOnMaxZoom: true
      }).addTo(map);
      
      userMarkerClusterRef.current = L.markerClusterGroup({
        maxClusterRadius: 50,
        disableClusteringAtZoom: 13,
        spiderfyOnMaxZoom: true
      }).addTo(map);
      
      // Create layers for organization
      userPathsLayerRef.current = L.layerGroup().addTo(map);
      userLabelsLayerRef.current = L.layerGroup().addTo(map);
      drivingPathRef.current = L.layerGroup().addTo(map);
      
      // Track map changes
      map.on('zoom', () => {
        setCurrentZoom(map.getZoom());
      });
      
      map.on('moveend', () => {
        setMapCenter(map.getCenter());
        setMapBounds(map.getBounds());
      });
      
      setIsMapInitialized(true);
      return true;
    } catch (err) {
      console.error("Error initializing map:", err);
      return false;
    }
  }, [defaultPosition, defaultZoom, darkMode]);
  
  /**
   * Add a click handler to the map
   */
  const addMapClickHandler = useCallback((handler) => {
    if (!mapRef.current) return;
    
    // First remove any existing handler
    mapRef.current.off('click');
    
    // Add the new handler
    mapRef.current.on('click', handler);
  }, []);
  
  /**
   * Add a context menu handler to the map
   */
  const addMapContextMenuHandler = useCallback((handler) => {
    if (!mapRef.current) return;
    
    // First remove any existing handler
    mapRef.current.off('contextmenu');
    
    // Add the new handler
    mapRef.current.on('contextmenu', handler);
  }, []);
  
  /**
   * Create and add a user marker to the map
   */
  const createUserMarker = useCallback((position, options = {}) => {
    if (!mapRef.current || !position) return null;
    
    try {
      // Default options
      const {
        profilePicture = null,
        displayName = 'You',
        isCurrentUser = true,
        zIndexOffset = 1000
      } = options;
      
      // Remove existing marker if any
      if (currentMarkerRef.current && isCurrentUser) {
        mapRef.current.removeLayer(currentMarkerRef.current);
        currentMarkerRef.current = null;
      }
      
      // Create icon based on whether there's a profile picture
      const userIcon = L.divIcon({
        className: isCurrentUser ? 'current-location-marker' : 'other-user-marker',
        html: profilePicture 
          ? `<div class="user-profile-marker ${!isCurrentUser ? 'other-user' : ''}" style="background-image: url('${profilePicture}');"></div>`
          : `<div class="${isCurrentUser ? 'current-location-pin' : 'other-user-pin'}"></div>`,
        iconSize: isCurrentUser ? [40, 40] : [36, 36],
        iconAnchor: isCurrentUser ? [20, 20] : [18, 18]
      });
      
      // Create marker
      const marker = L.marker([position.lat, position.lng], {
        icon: userIcon,
        title: displayName,
        zIndexOffset: zIndexOffset
      }).addTo(mapRef.current);
      
      // Add user label if display name is provided
      if (displayName && isCurrentUser) {
        // Remove existing label if any
        if (userLabelRef.current) {
          mapRef.current.removeLayer(userLabelRef.current);
          userLabelRef.current = null;
        }
        
        userLabelRef.current = L.marker([position.lat, position.lng], {
          icon: L.divIcon({
            className: 'user-label',
            html: `<div class="user-label-content current-user">${displayName}</div>`,
            iconSize: [100, 20],
            iconAnchor: [50, 0] // Position it at the top of the marker
          }),
          zIndexOffset: zIndexOffset + 1
        }).addTo(mapRef.current);
      }
      
      // If it's the current user, store the reference
      if (isCurrentUser) {
        currentMarkerRef.current = marker;
      }
      
      return marker;
    } catch (err) {
      console.error("Error creating user marker:", err);
      return null;
    }
  }, []);
  
  /**
   * Update a user marker's position
   */
  const updateUserMarker = useCallback((position, options = {}) => {
    if (!mapRef.current || !position) return false;
    
    try {
      const {
        isCurrentUser = true,
        centerMap = false
      } = options;
      
      const marker = isCurrentUser ? currentMarkerRef.current : options.marker;
      const label = isCurrentUser ? userLabelRef.current : options.label;
      
      // Update marker position if it exists
      if (marker) {
        marker.setLatLng([position.lat, position.lng]);
      } else if (isCurrentUser) {
        // Create new marker if it doesn't exist
        createUserMarker(position, options);
        return true;
      }
      
      // Update label position if it exists
      if (label) {
        label.setLatLng([position.lat, position.lng]);
      }
      
      // Center map if requested
      if (centerMap) {
        mapRef.current.setView([position.lat, position.lng], mapRef.current.getZoom());
      }
      
      return true;
    } catch (err) {
      console.error("Error updating user marker:", err);
      return false;
    }
  }, [createUserMarker]);
  
  /**
   * Create a location marker
   */
  const createLocationMarker = useCallback((location, onClick) => {
    if (!mapRef.current || !markerClusterRef.current || !location) return null;
    
    try {
      // Determine marker appearance
      const isSelected = location.isSelected || false;
      const markerClass = isSelected ? 'selected' : (location.status === 'picked-up' ? 'picked-up' : (location.isAdminOnly ? 'admin' : 'regular'));
      const priorityClass = location.isPriority ? 'priority' : '';
      
      // Create HTML for marker
      let markerHtml = '';
      
      if (location.images && location.images.length > 0) {
        // Use the first image as marker background
        markerHtml = `<div class="location-marker-image ${markerClass} ${priorityClass}" style="background-image: url('${location.images[0]}');">`;
        
        // Add status indicator if picked up
        if (location.status === 'picked-up') {
          markerHtml += `<div class="location-status">Picked Up</div>`;
        }
        
        markerHtml += `</div>`;
      } else {
        // Use fallback with first letter of location name
        const firstLetter = location.name.charAt(0).toUpperCase();
        markerHtml = `<div class="location-marker-fallback ${markerClass} ${priorityClass}">`;
        
        // Add status indicator if picked up
        if (location.status === 'picked-up') {
          markerHtml += `<div class="location-status">Picked Up</div>`;
        }
        
        markerHtml += `${firstLetter}</div>`;
      }
      
      // Create icon
      const icon = L.divIcon({
        className: 'location-marker',
        html: markerHtml,
        iconSize: [40, 40],
        iconAnchor: [20, 20]
      });
      
      // Create marker
      const marker = L.marker([location.position.lat, location.position.lng], {
        icon: icon,
        title: location.name,
        id: location.id,
        zIndexOffset: isSelected ? 800 : (location.status === 'picked-up' ? 750 : (location.isAdminOnly ? 700 : 600))
      });
      
      // Add to marker cluster
      markerClusterRef.current.addLayer(marker);
      
      // Add click handler if provided
      if (onClick) {
        marker.on('click', () => onClick(location, marker));
      }
      
      return marker;
    } catch (err) {
      console.error("Error creating location marker:", err);
      return null;
    }
  }, []);
  
  /**
   * Create a navigation route between two points
   */
  const createRoute = useCallback((from, to, options = {}) => {
    if (!mapRef.current || !from || !to) return null;
    
    try {
      // Clean up any existing navigation
      clearRoute();
      
      // Config options
      const {
        fitRoute = true,
        showStartMarker = false,
        showEndMarker = false,
        lineColor = '#3B82F6',
        lineWeight = 5,
        onRouteFound = null,
        onRoutingError = null
      } = options;
      
      // Create routing control
      const routingControl = L.Routing.control({
        waypoints: [
          L.latLng(from.lat, from.lng),
          L.latLng(to.lat, to.lng)
        ],
        routeWhileDragging: false,
        router: L.Routing.osrmv1({
          serviceUrl: 'https://router.project-osrm.org/route/v1',
          profile: 'driving'
        }),
        lineOptions: {
          styles: [
            {color: lineColor, opacity: 0.8, weight: lineWeight}
          ],
          addWaypoints: false,
          extendToWaypoints: true,
          missingRouteTolerance: 0
        },
        createMarker: function(i, waypoint, n) {
          // First waypoint (start)
          if (i === 0) {
            return showStartMarker ? L.marker(waypoint.latLng) : null;
          }
          // Last waypoint (end)
          if (i === n - 1) {
            return showEndMarker ? L.marker(waypoint.latLng) : null;
          }
          // No intermediate markers
          return null;
        },
        fitSelectedRoutes: fitRoute,
        showAlternatives: false,
        plan: new L.Routing.Plan([
          L.latLng(from.lat, from.lng),
          L.latLng(to.lat, to.lng)
        ], {
          createMarker: function(i, waypoint, n) {
            // Same logic as above
            if (i === 0) {
              return showStartMarker ? L.marker(waypoint.latLng) : null;
            }
            if (i === n - 1) {
              return showEndMarker ? L.marker(waypoint.latLng) : null;
            }
            return null;
          },
          draggableWaypoints: false,
          routeWhileDragging: false
        }),
        collapsible: true,
        show: false // Don't show the instructions panel
      });
      
      // Add routing control to map
      routingControl.addTo(mapRef.current);
      
      // Store reference
      routingControlRef.current = routingControl;
      
      // Setup event handlers
      if (onRouteFound) {
        routingControl.on('routesfound', onRouteFound);
      }
      
      if (onRoutingError) {
        routingControl.on('routingerror', onRoutingError);
      }
      
      return routingControl;
    } catch (err) {
      console.error("Error creating route:", err);
      return null;
    }
  }, [clearRoute]);
  
  /**
   * Create a simple straight line between two points
   */
  const createDirectLine = useCallback((from, to, options = {}) => {
    if (!mapRef.current || !from || !to) return null;
    
    try {
      // Options
      const {
        color = '#3B82F6',
        weight = 5,
        opacity = 0.8,
        dashArray = '10, 10',
        fitRoute = true
      } = options;
      
      // Create line
      const line = L.polyline([
        [from.lat, from.lng],
        [to.lat, to.lng]
      ], {
        color: color,
        weight: weight,
        opacity: opacity,
        dashArray: dashArray,
        lineCap: 'round'
      }).addTo(mapRef.current);
      
      // Add to navigation elements for cleanup
      navigationElementsRef.current.push({
        remove: () => {
          try {
            if (line && mapRef.current) {
              mapRef.current.removeLayer(line);
            }
          } catch (err) {
            console.warn("Error removing line:", err);
          }
        }
      });
      
      // Fit bounds if requested
      if (fitRoute) {
        mapRef.current.fitBounds([
          [from.lat, from.lng],
          [to.lat, to.lng]
        ], { padding: [50, 50] });
      }
      
      return line;
    } catch (err) {
      console.error("Error creating direct line:", err);
      return null;
    }
  }, []);
  
  /**
   * Clear current route and navigation elements
   */
  const clearRoute = useCallback(() => {
    try {
      // Clean up navigation elements
      navigationElementsRef.current.forEach(element => {
        if (element && element.remove) {
          try {
            element.remove();
          } catch (err) {
            console.warn("Error removing navigation element:", err);
          }
        }
      });
      navigationElementsRef.current = [];
      
      // Clean up routing control
      if (routingControlRef.current && mapRef.current) {
        try {
          // Make sure the plan is cleared first
          if (routingControlRef.current._router && routingControlRef.current._router._plan) {
            routingControlRef.current._router._plan.setWaypoints([]);
          }
          
          mapRef.current.removeControl(routingControlRef.current);
        } catch (err) {
          console.warn("Error removing routing control:", err);
        }
        routingControlRef.current = null;
      }
      
      // Clear animation
      if (routeAnimationRef.current) {
        clearInterval(routeAnimationRef.current);
        routeAnimationRef.current = null;
      }
      
      return true;
    } catch (err) {
      console.error("Error clearing route:", err);
      return false;
    }
  }, []);
  
  /**
   * Add a path for tracking user movement
   */
  const addUserPath = useCallback((userId, coordinates, options = {}) => {
    if (!mapRef.current || !coordinates || coordinates.length < 2) return null;
    
    try {
      // Options
      const {
        color = '#4285F4',
        weight = 5,
        opacity = 0.8,
        dashArray = null,
        isCurrentUser = false
      } = options;
      
      // Remove existing path if any
      if (breadcrumbPathsRef.current[userId] && mapRef.current) {
        try {
          mapRef.current.removeLayer(breadcrumbPathsRef.current[userId]);
        } catch (err) {
          console.warn("Error removing existing breadcrumb path:", err);
        }
      }
      
      // Determine line style based on whether it's current user
      const lineStyle = isCurrentUser 
        ? { color, weight, opacity } // Current user: solid, thicker
        : { color, weight: Math.max(2, weight - 2), opacity: Math.max(0.5, opacity - 0.2), dashArray: dashArray || '5, 10' }; // Other users: dashed, thinner
      
      // Create polyline
      const userPath = L.polyline(coordinates, lineStyle).addTo(mapRef.current);
      
      // Store reference
      breadcrumbPathsRef.current[userId] = userPath;
      
      return userPath;
    } catch (err) {
      console.error("Error adding user path:", err);
      return null;
    }
  }, []);
  
  /**
   * Remove a user's path
   */
  const removeUserPath = useCallback((userId) => {
    if (!mapRef.current || !breadcrumbPathsRef.current[userId]) return false;
    
    try {
      mapRef.current.removeLayer(breadcrumbPathsRef.current[userId]);
      delete breadcrumbPathsRef.current[userId];
      return true;
    } catch (err) {
      console.error("Error removing user path:", err);
      return false;
    }
  }, []);
  
  /**
   * Add a navigation panel element to the map
   */
  const addNavigationPanel = useCallback((content, options = {}) => {
    if (!mapRef.current || !mapContainerRef.current) return null;
    
    try {
      // Create panel element
      const navPanel = document.createElement('div');
      navPanel.className = 'navigation-panel dark-mode';
      
      // Set content
      navPanel.innerHTML = content;
      
      // Add to container
      mapContainerRef.current.appendChild(navPanel);
      
      // Add to navigation elements for cleanup
      navigationElementsRef.current.push({
        remove: () => {
          if (navPanel.parentNode) {
            try {
              navPanel.parentNode.removeChild(navPanel);
            } catch (err) {
              console.warn("Error removing navigation panel:", err);
            }
          }
        }
      });
      
      return navPanel;
    } catch (err) {
      console.error("Error adding navigation panel:", err);
      return null;
    }
  }, []);
  
  /**
   * Refresh the map (clears and reinitializes layers)
   */
  const refreshMap = useCallback(() => {
    if (!mapRef.current) return false;
    
    try {
      setIsMapRefreshing(true);
      
      // Store current view
      const currentCenter = mapRef.current.getCenter();
      const currentZoom = mapRef.current.getZoom();
      
      // Clear existing navigation
      clearRoute();
      
      // Recreate marker clusters
      if (markerClusterRef.current) {
        try {
          mapRef.current.removeLayer(markerClusterRef.current);
        } catch (err) {
          console.warn("Error removing marker cluster:", err);
        }
      }
      
      if (userMarkerClusterRef.current) {
        try {
          mapRef.current.removeLayer(userMarkerClusterRef.current);
        } catch (err) {
          console.warn("Error removing user marker cluster:", err);
        }
      }
      
      // Create new marker clusters
      markerClusterRef.current = L.markerClusterGroup({
        maxClusterRadius: 30,
        disableClusteringAtZoom: 15,
        spiderfyOnMaxZoom: true
      }).addTo(mapRef.current);
      
      userMarkerClusterRef.current = L.markerClusterGroup({
        maxClusterRadius: 50,
        disableClusteringAtZoom: 13,
        spiderfyOnMaxZoom: true
      }).addTo(mapRef.current);
      
      // Clear user paths
      Object.keys(breadcrumbPathsRef.current).forEach(userId => {
        removeUserPath(userId);
      });
      
      // Clear layer references
      currentMarkerRef.current = null;
      userLabelRef.current = null;
      
      // Refresh tile layers
      mapRef.current.eachLayer(layer => {
        if (layer instanceof L.TileLayer) {
          // Remove old tile layer
          try {
            mapRef.current.removeLayer(layer);
          } catch (err) {
            console.warn("Error removing tile layer:", err);
          }
        }
      });
      
      // Add fresh tile layer based on current mode
      L.tileLayer(
        darkMode 
          ? 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png'
          : 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors' + 
                      (darkMode ? ' &copy; <a href="https://carto.com/attributions">CARTO</a>' : ''),
          maxZoom: 19,
          updateWhenIdle: true,
          updateWhenZooming: false,
          updateInterval: 500
        }
      ).addTo(mapRef.current);
      
      // Restore previous view
      mapRef.current.setView(currentCenter, currentZoom);
      
      setIsMapRefreshing(false);
      return true;
    } catch (err) {
      console.error("Error refreshing map:", err);
      setIsMapRefreshing(false);
      return false;
    }
  }, [clearRoute, darkMode, removeUserPath]);
  
  /**
   * Calculate distance between two points (in miles)
   */
  const calculateDistance = useCallback((point1, point2) => {
    const R = 3958.8; // Earth's radius in MILES
    const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
    const φ2 = point2.lat * Math.PI/180;
    const Δφ = (point2.lat-point1.lat) * Math.PI/180;
    const Δλ = (point2.lng-point1.lng) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    const d = R * c; // in MILES
    return d;
  }, []);
  
  /**
   * Calculate bearing between two points (in degrees)
   */
  const calculateBearing = useCallback((start, end) => {
    const startLat = start.lat * Math.PI / 180;
    const startLng = start.lng * Math.PI / 180;
    const endLat = end.lat * Math.PI / 180;
    const endLng = end.lng * Math.PI / 180;

    const y = Math.sin(endLng - startLng) * Math.cos(endLat);
    const x = Math.cos(startLat) * Math.sin(endLat) -
              Math.sin(startLat) * Math.cos(endLat) * Math.cos(endLng - startLng);
    
    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360; // Normalize to 0-360
  }, []);
  
  /**
   * Format distance for display
   */
  const formatDistance = useCallback((miles) => {
    if (miles < 0.1) {
      return `${Math.round(miles * 5280)} feet`;
    } else {
      return `${miles.toFixed(1)} miles`;
    }
  }, []);
  
  /**
   * Format time for display
   */
  const formatTime = useCallback((seconds) => {
    if (seconds < 60) {
      return `${Math.round(seconds)} seconds`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)} minutes`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.round((seconds % 3600) / 60);
      return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }
  }, []);
  
  /**
   * Get direction text from bearing
   */
  const getDirectionText = useCallback((bearing) => {
    const directions = ['North', 'Northeast', 'East', 'Southeast', 'South', 'Southwest', 'West', 'Northwest'];
    const index = Math.round(bearing / 45) % 8;
    return directions[index];
  }, []);
  
  /**
   * Get direction arrow from bearing
   */
  const getDirectionArrow = useCallback((bearing) => {
    // Map bearing to one of 8 cardinal directions with corresponding arrows
    const directions = [
      { min: 337.5, max: 360, arrow: '⬆️' }, // North
      { min: 0, max: 22.5, arrow: '⬆️' },    // North
      { min: 22.5, max: 67.5, arrow: '↗️' },  // Northeast
      { min: 67.5, max: 112.5, arrow: '➡️' }, // East
      { min: 112.5, max: 157.5, arrow: '↘️' }, // Southeast
      { min: 157.5, max: 202.5, arrow: '⬇️' }, // South
      { min: 202.5, max: 247.5, arrow: '↙️' }, // Southwest
      { min: 247.5, max: 292.5, arrow: '⬅️' }, // West
      { min: 292.5, max: 337.5, arrow: '↖️' }  // Northwest
    ];
    
    // Find the direction that matches the bearing
    for (const dir of directions) {
      if ((bearing >= dir.min && bearing < dir.max) || 
          (dir.min > dir.max && (bearing >= dir.min || bearing < dir.max))) {
        return dir.arrow;
      }
    }
    
    return '⬆️'; // Default to North if something goes wrong
  }, []);
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      // Clear any navigation
      clearRoute();
      
      // Remove all breadcrumb paths
      Object.keys(breadcrumbPathsRef.current).forEach(userId => {
        if (removeUserPath) {
          removeUserPath(userId);
        }
      });
      
      // Destroy map if needed
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, [clearRoute, removeUserPath]);
  
  return {
    // Map references
    mapRef,
    mapContainerRef,
    markerClusterRef,
    userMarkerClusterRef,
    
    // Map state
    isMapInitialized,
    isMapRefreshing,
    currentZoom,
    mapCenter,
    mapBounds,
    
    // Initialization
    initializeMap,
    refreshMap,
    
    // Event handlers
    addMapClickHandler,
    addMapContextMenuHandler,
    
    // Marker management
    createUserMarker,
    updateUserMarker,
    createLocationMarker,
    
    // Routing
    createRoute,
    createDirectLine,
    clearRoute,
    
    // Path management
    addUserPath,
    removeUserPath,
    
    // UI elements
    addNavigationPanel,
    
    // Utility functions
    calculateDistance,
    calculateBearing,
    formatDistance,
    formatTime,
    getDirectionText,
    getDirectionArrow
  };
};

export default useMapControls;