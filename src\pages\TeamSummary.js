import React from 'react';

function TeamSummary({ 
  selectedTeam, 
  teamStats, 
  topPerformers, 
  loading, 
  selectedUser, 
  userStats, 
  timeCardData,
  selectedWeek,
  vehicleStats,
  aggregatedVehicleData
}) {
  // Format numbers for display - preserving small decimal values
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0';
    if (typeof num === 'string') {
      const cleanValue = num.replace(/,/g, '');
      return !isNaN(parseFloat(cleanValue)) 
        ? new Intl.NumberFormat('en-US').format(Math.round(parseFloat(cleanValue) * 100) / 100)
        : '0';
    }
    // Ensure small decimal values (like 0.01) aren't rounded to 0
    return new Intl.NumberFormat('en-US', { 
      minimumFractionDigits: num < 0.1 ? 2 : 0,
      maximumFractionDigits: 2 
    }).format(num);
  };

  // Format efficiency values (per hour metrics) with safety measures
  const formatEfficiency = (value, hours) => {
    if (value === undefined || value === null) return '0';
    // Use a minimum divisor for calculation purposes only
    const hoursForCalculation = hours < 0.25 ? 0.25 : hours;
    // Cap at reasonable maximum - limit to 100 cars/scans per hour
    const efficiency = Math.min(value / hoursForCalculation, 100);
    // Format the result
    return formatNumber(efficiency);
  };

  // Format percentages
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0%';
    return `${Math.round(value)}%`;
  };

  // Check if the current user is a top performer
  const isCurrentUserTopPerformer = (performerObj) => {
    return selectedUser && performerObj && performerObj.userId === selectedUser.id;
  };

  // Ensure numeric values for stats
  const ensureNumber = (value) => {
    if (value === undefined || value === null) return 0;
    if (typeof value === 'string') {
      // Remove any commas and convert to number
      const cleanValue = value.replace(/,/g, '');
      return !isNaN(parseFloat(cleanValue)) ? parseFloat(cleanValue) : 0;
    }
    return typeof value === 'number' ? value : 0;
  };

  // Function to calculate user contribution as percentage of team
  const calculateContribution = (userValue, teamValue) => {
    if (!userValue || !teamValue) return 0;
    const user = ensureNumber(userValue);
    const team = ensureNumber(teamValue);
    if (team === 0) return 0;
    return Math.min(100, (user / team) * 100);
  };

  // Get user vehicles stats from vehicleStats when available (for selected week)
  const getUserVehicleStats = () => {
    if (!vehicleStats) return { found: 0, secured: 0, scans: 0 };
    
    return {
      found: ensureNumber(vehicleStats.totalFound),
      secured: ensureNumber(vehicleStats.totalSecured),
      scans: ensureNumber(vehicleStats.totalScans)
    };
  };

  if (!selectedTeam) {
    return null;
  }

  // Get user-specific stats from UserDashboard and VehicleTracker
  const userVehicleStats = getUserVehicleStats();
  const userCarsFound = selectedWeek ? userVehicleStats.found : (userStats?.carsFoundWeek || 0);
  const userCarsRecovered = selectedWeek ? userVehicleStats.secured : (userStats?.carsRecoveredWeek || 0);
  const userScans = selectedWeek ? userVehicleStats.scans : (userStats?.userScansWeek || 0);
  const userHoursWorked = timeCardData?.weeklyHours || userStats?.userHoursWorkedWeek || 0;

  // Get team totals from teamStats - use the selected week data if available
  const teamCarsFound = teamStats?.carsFoundThisWeek || 0;
  const teamCarsRecovered = teamStats?.carsRecoveredThisWeek || 0;
  const teamScans = teamStats?.teamScansWeek || 0;
  const teamHoursWorked = teamStats?.teamHoursWorkedWeek || 0;
  const teamRecoveryRate = teamStats?.weeklyRecoveryRate || teamStats?.recoveryRate || 0;

  // Use weekly data as fallback if the monthly/YTD data is missing or zero
  const monthlyScans = aggregatedVehicleData?.month?.totalScans > 0 ? 
                      aggregatedVehicleData.month.totalScans : 
                      teamScans;
  const ytdScans = aggregatedVehicleData?.ytd?.totalScans > 0 ? 
                   aggregatedVehicleData.ytd.totalScans : 
                   teamScans;
  
  // Also use weekly data for monthly and YTD found/secured if missing
  const monthlyFound = aggregatedVehicleData?.month?.totalFound > 0 ? 
                      aggregatedVehicleData.month.totalFound : 
                      teamCarsFound;
  const monthlySecured = aggregatedVehicleData?.month?.totalSecured > 0 ? 
                        aggregatedVehicleData.month.totalSecured : 
                        teamCarsRecovered;
  const ytdFound = aggregatedVehicleData?.ytd?.totalFound > 0 ? 
                   aggregatedVehicleData.ytd.totalFound : 
                   teamCarsFound;
  const ytdSecured = aggregatedVehicleData?.ytd?.totalSecured > 0 ? 
                     aggregatedVehicleData.ytd.totalSecured : 
                     teamCarsRecovered;

  // Use weekly hours for monthly and YTD if those stats are missing or zero
  const teamMonthlyHours = teamStats?.teamHoursWorkedMonth > 0 ? 
                           teamStats.teamHoursWorkedMonth : 
                           teamHoursWorked;
  const teamYTDHours = teamStats?.teamHoursWorkedYTD > 0 ? 
                       teamStats.teamHoursWorkedYTD : 
                       teamHoursWorked;

  // Calculate user contribution percentages
  const userCarsFoundPercentage = calculateContribution(userCarsFound, teamCarsFound);
  const userCarsRecoveredPercentage = calculateContribution(userCarsRecovered, teamCarsRecovered);
  const userScansPercentage = calculateContribution(userScans, teamScans);
  const userHoursPercentage = calculateContribution(userHoursWorked, teamHoursWorked);
  
  // Calculate user recovery rate
  const userRecoveryRate = userCarsFound > 0 ? (userCarsRecovered / userCarsFound) * 100 : 0;

  return (
    <div className="bg-gray-800 rounded shadow-sm p-3 border border-gray-700 mt-4">
      <h2 className="text-lg font-semibold text-white mb-3">
        {selectedTeam.name} Team Analytics
        {selectedWeek && vehicleStats?.dateRange && (
          <span className="text-sm font-normal text-gray-400 ml-2">
            {vehicleStats.dateRange.start} - {vehicleStats.dateRange.end}
          </span>
        )}
      </h2>
      
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500" aria-hidden="true"></div>
          <span className="sr-only">Loading team data...</span>
        </div>
      ) : (
        <>
          {/* Team Stats Overview */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
            {/* Recovery Rate */}
            <div className="bg-gray-700 p-3 rounded border border-gray-600">
              <div className="flex flex-col h-full justify-between">
                <div className="text-xs text-gray-400 mb-1">Recovery Rate</div>
                <div className="text-xl font-bold text-green-400 mb-1">
                  {formatPercent(teamRecoveryRate)}
                </div>
                <div className="w-full bg-gray-800 rounded-full h-1.5">
                  <div 
                    className="bg-gradient-to-r from-yellow-500 via-green-500 to-green-600 h-1.5 rounded-full" 
                    style={{ width: `${Math.min(100, teamRecoveryRate || 0)}%` }}
                    aria-hidden="true"
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-1">Team average</div>
              </div>
            </div>
            
            {/* Team Hours Worked - Added from TimeCard data */}
            <div className="bg-gray-700 p-3 rounded border border-gray-600">
              <div className="flex flex-col h-full justify-between">
                <div className="text-xs text-gray-400 mb-1">Hours Worked</div>
                <div className="text-xl font-bold text-blue-400 mb-1">
                  {formatNumber(teamHoursWorked)}
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-xs text-gray-400">Daily</div>
                  <div className="text-xs text-white">{formatNumber(teamStats?.teamHoursWorkedToday || teamHoursWorked / 5)}</div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-xs text-gray-400">Monthly</div>
                  <div className="text-xs text-white">{formatNumber(teamMonthlyHours)}</div>
                </div>
              </div>
            </div>
            
            {/* Team Cars Found - Week specific */}
            <div className="bg-gray-700 p-3 rounded border border-gray-600">
              <div className="flex flex-col h-full justify-between">
                <div className="text-xs text-gray-400 mb-1">Cars Found</div>
                <div className="text-xl font-bold text-yellow-400 mb-1">
                  {formatNumber(teamCarsFound)}
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-xs text-gray-400">Secured</div>
                  <div className="text-xs text-white">{formatNumber(teamCarsRecovered)}</div>
                </div>
                <div className="text-xs text-gray-400">this week</div>
              </div>
            </div>
            
            {/* Team Scans - Week specific */}
            <div className="bg-gray-700 p-3 rounded border border-gray-600">
              <div className="flex flex-col h-full justify-between">
                <div className="text-xs text-gray-400 mb-1">Team Scans</div>
                <div className="text-xl font-bold text-purple-400 mb-1">
                  {formatNumber(teamScans)}
                </div>
                <div className="text-xs text-gray-400">this week</div>
              </div>
            </div>
          </div>
          
          {/* Team Efficiency Score */}
          <div className="mb-4 bg-gray-700 p-3 rounded border border-gray-600">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-semibold text-blue-300">Team Efficiency Score</h3>
              <div className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-500">
                {formatNumber(teamStats?.efficiencyScore || 0)}%
              </div>
            </div>
            
            <div className="w-full bg-gray-800 rounded-full h-2.5">
              <div 
                className="bg-gradient-to-r from-blue-500 via-purple-500 to-green-500 h-2.5 rounded-full" 
                style={{ width: `${Math.min(100, teamStats?.efficiencyScore || 0)}%` }}
                aria-hidden="true"
              ></div>
            </div>
          </div>
          
          {/* Top Performers */}
          <h3 className="text-sm font-semibold text-blue-300 mb-3">Top Performers</h3>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-4">
            {/* Most Cars Found */}
            <div className={`bg-gray-700 rounded-lg p-3 border ${isCurrentUserTopPerformer(topPerformers.mostCars) ? 'border-green-500' : 'border-gray-600'}`}>
              <div className="flex flex-col items-center">
                <div className="h-10 w-10 flex items-center justify-center bg-blue-900 rounded-full mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div className="text-xs text-gray-400 mb-1">Most Cars Found</div>
                {topPerformers.mostCars ? (
                  <>
                    <div className="text-sm font-semibold text-white mb-1">
                      {topPerformers.mostCars.name}
                      {isCurrentUserTopPerformer(topPerformers.mostCars) && (
                        <span className="ml-1 text-green-400">(You)</span>
                      )}
                    </div>
                    <div className="text-lg font-bold text-blue-400">{formatNumber(topPerformers.mostCars.value)}</div>
                  </>
                ) : (
                  <div className="text-sm text-gray-500">No data available</div>
                )}
              </div>
            </div>
            
            {/* Most Efficient */}
            <div className={`bg-gray-700 rounded-lg p-3 border ${isCurrentUserTopPerformer(topPerformers.mostEfficient) ? 'border-green-500' : 'border-gray-600'}`}>
              <div className="flex flex-col items-center">
                <div className="h-10 w-10 flex items-center justify-center bg-green-900 rounded-full mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div className="text-xs text-gray-400 mb-1">Most Efficient</div>
                {topPerformers.mostEfficient ? (
                  <>
                    <div className="text-sm font-semibold text-white mb-1">
                      {topPerformers.mostEfficient.name}
                      {isCurrentUserTopPerformer(topPerformers.mostEfficient) && (
                        <span className="ml-1 text-green-400">(You)</span>
                      )}
                    </div>
                    <div className="text-lg font-bold text-green-400">
                      {formatNumber(topPerformers.mostEfficient.value)} per hour
                    </div>
                    {/* Show hours worked for context */}
                    <div className="flex justify-between w-full text-xs text-gray-400 mt-1">
                      <span>{formatNumber(topPerformers.mostEfficient.cars)} cars</span>
                      <span>{formatNumber(topPerformers.mostEfficient.hours)} hrs</span>
                    </div>
                  </>
                ) : (
                  <div className="text-sm text-gray-500">No data available</div>
                  )}
          </div>
            </div>
            
            {/* Most Improved */}
            <div className={`bg-gray-700 rounded-lg p-3 border ${isCurrentUserTopPerformer(topPerformers.mostImproved) ? 'border-green-500' : 'border-gray-600'}`}>
              <div className="flex flex-col items-center">
                <div className="h-10 w-10 flex items-center justify-center bg-purple-900 rounded-full mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 11l3-3m0 0l3 3m-3-3v8m0-13a9 9 0 110 18 9 9 0 010-18z" />
                  </svg>
                </div>
                <div className="text-xs text-gray-400 mb-1">Most Improved</div>
                {topPerformers.mostImproved ? (
                  <>
                    <div className="text-sm font-semibold text-white mb-1">
                      {topPerformers.mostImproved.name}
                      {isCurrentUserTopPerformer(topPerformers.mostImproved) && (
                        <span className="ml-1 text-green-400">(You)</span>
                      )}
                    </div>
                    <div className="text-lg font-bold text-purple-400">+{formatNumber(topPerformers.mostImproved.value)}%</div>
                  </>
                ) : (
                  <div className="text-sm text-gray-500">No data available</div>
                )}
              </div>
            </div>
          </div>
          
          {/* Your Performance Highlight - Enhanced section with user data */}
          {selectedUser && (
            <div className="bg-blue-900 bg-opacity-30 p-3 rounded border border-blue-800 mb-4">
              <h3 className="text-sm font-semibold text-blue-300 mb-2">Your Performance</h3>
              
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                {/* Hours Worked */}
                <div className="bg-gray-800 p-2 rounded text-center">
                  <div className="text-xs text-gray-400">Your Hours</div>
                  <div className="text-lg font-bold text-blue-400">
                    {formatNumber(userHoursWorked)}
                  </div>
                  {teamHoursWorked > 0 && (
                    <div className="text-xs text-gray-400">
                      {formatNumber(userHoursPercentage)}% of team
                    </div>
                  )}
                </div>
                
                {/* Cars Found */}
                <div className="bg-gray-800 p-2 rounded text-center">
                  <div className="text-xs text-gray-400">Your Cars</div>
                  <div className="text-lg font-bold text-green-400">
                    {formatNumber(userCarsFound)}
                  </div>
                  {teamCarsFound > 0 && (
                    <div className="text-xs text-gray-400">
                      {formatNumber(userCarsFoundPercentage)}% of team
                    </div>
                  )}
                </div>
                
                {/* Scans */}
                <div className="bg-gray-800 p-2 rounded text-center">
                  <div className="text-xs text-gray-400">Your Scans</div>
                  <div className="text-lg font-bold text-yellow-400">
                    {formatNumber(userScans)}
                  </div>
                  {teamScans > 0 && (
                    <div className="text-xs text-gray-400">
                      {formatNumber(userScansPercentage)}% of team
                    </div>
                  )}
                </div>
                
                {/* Efficiency */}
                <div className="bg-gray-800 p-2 rounded text-center">
                  <div className="text-xs text-gray-400">Your Efficiency</div>
                  <div className="text-lg font-bold text-purple-400">
                    {userHoursWorked > 0 ? 
                      formatNumber(Math.min(userCarsFound / Math.max(userHoursWorked, 0.1), 100)) : 
                      '0'} / hr
                  </div>
                  <div className="text-xs text-gray-400">
                    {formatPercent(userRecoveryRate)} recovery
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Team Contribution Analysis - New section when user is selected */}
          {selectedUser && (
            <div className="bg-gray-700 p-3 rounded border border-gray-600 mb-4">
              <h3 className="text-sm font-semibold text-blue-300 mb-2">
                Your Team Contribution
              </h3>
              
              <div className="space-y-3">
                {/* Cars Found Contribution */}
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-300">Cars Found</span>
                    <div className="text-xs">
                      <span className="font-medium text-green-400">{formatNumber(userCarsFound)}</span>
                      <span className="text-gray-400"> / {formatNumber(teamCarsFound)} team total</span>
                    </div>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-1.5">
                    <div 
                      className="bg-green-500 h-1.5 rounded-full" 
                      style={{ width: `${userCarsFoundPercentage}%` }}
                      aria-hidden="true"
                    ></div>
                  </div>
                  <div className="text-xs text-right text-gray-400 mt-0.5">{formatNumber(userCarsFoundPercentage)}%</div>
                </div>
                
                {/* Cars Recovered Contribution */}
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-300">Cars Recovered</span>
                    <div className="text-xs">
                      <span className="font-medium text-blue-400">{formatNumber(userCarsRecovered)}</span>
                      <span className="text-gray-400"> / {formatNumber(teamCarsRecovered)} team total</span>
                    </div>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-1.5">
                    <div 
                      className="bg-blue-500 h-1.5 rounded-full" 
                      style={{ width: `${userCarsRecoveredPercentage}%` }}
                      aria-hidden="true"
                    ></div>
                  </div>
                  <div className="text-xs text-right text-gray-400 mt-0.5">{formatNumber(userCarsRecoveredPercentage)}%</div>
                </div>
                
                {/* Scans Contribution */}
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-300">Scans</span>
                    <div className="text-xs">
                      <span className="font-medium text-yellow-400">{formatNumber(userScans)}</span>
                      <span className="text-gray-400"> / {formatNumber(teamScans)} team total</span>
                    </div>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-1.5">
                    <div 
                      className="bg-yellow-500 h-1.5 rounded-full" 
                      style={{ width: `${userScansPercentage}%` }}
                      aria-hidden="true"
                    ></div>
                  </div>
                  <div className="text-xs text-right text-gray-400 mt-0.5">{formatNumber(userScansPercentage)}%</div>
                </div>
                
                {/* Hours Worked Contribution */}
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-300">Hours Worked</span>
                    <div className="text-xs">
                      <span className="font-medium text-purple-400">{formatNumber(userHoursWorked)}</span>
                      <span className="text-gray-400"> / {formatNumber(teamHoursWorked)} team total</span>
                    </div>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-1.5">
                    <div 
                      className="bg-purple-500 h-1.5 rounded-full" 
                      style={{ width: `${userHoursPercentage}%` }}
                      aria-hidden="true"
                    ></div>
                  </div>
                  <div className="text-xs text-right text-gray-400 mt-0.5">{formatNumber(userHoursPercentage)}%</div>
                </div>
              </div>
            </div>
          )}
          
          {/* User Comparison Stats - Fixed to prevent unrealistic values */}
          {selectedUser && (
            <div className="bg-gray-700 p-3 rounded border border-gray-600 mb-4">
              <h3 className="text-sm font-semibold text-blue-300 mb-2">Comparison to Team Average</h3>
              
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {/* Cars Per Hour Comparison - With caps to prevent unrealistic values */}
                <div className="bg-gray-800 p-2 rounded">
                  <div className="text-xs text-gray-400">Cars Per Hour</div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-white">You:</span>
                    <span className="text-sm font-medium text-green-400">
                      {userHoursWorked > 0 ? 
                        formatNumber(Math.min(userCarsFound / Math.max(userHoursWorked, 0.1), 100)) : 
                        '0'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-white">Team Avg:</span>
                    <span className="text-sm font-medium text-blue-400">
                      {teamHoursWorked > 0 ? 
                        formatNumber(Math.min(teamCarsFound / Math.max(teamHoursWorked, 0.1), 100)) : 
                        '0'}
                    </span>
                  </div>
                </div>
                
                {/* Scans Per Hour Comparison - With caps to prevent unrealistic values */}
                <div className="bg-gray-800 p-2 rounded">
                  <div className="text-xs text-gray-400">Scans Per Hour</div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-white">You:</span>
                    <span className="text-sm font-medium text-green-400">
                      {userHoursWorked > 0 ? 
                        formatNumber(Math.min(userScans / Math.max(userHoursWorked, 0.1), 1000)) : 
                        '0'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-white">Team Avg:</span>
                    <span className="text-sm font-medium text-blue-400">
                      {teamHoursWorked > 0 ? 
                        formatNumber(Math.min(teamScans / Math.max(teamHoursWorked, 0.1), 1000)) : 
                        '0'}
                    </span>
                  </div>
                </div>
                
                {/* Recovery Rate Comparison */}
                <div className="bg-gray-800 p-2 rounded">
                  <div className="text-xs text-gray-400">Recovery Rate</div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-white">You:</span>
                    <span className="text-sm font-medium text-green-400">
                      {formatPercent(userRecoveryRate)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-white">Team Avg:</span>
                    <span className="text-sm font-medium text-blue-400">
                      {formatPercent(teamRecoveryRate)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Team Time & Scan Tracking Section */}
          <div className="bg-gray-700 p-3 rounded border border-gray-600 mt-4">
            <h3 className="text-sm font-semibold text-blue-300 mb-3">Team Performance Metrics</h3>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 mb-3">
              {/* Time tracking cards */}
              <div className="bg-gray-800 p-2 rounded text-center">
                <div className="text-xs text-gray-400">Hours Worked</div>
                <div className="grid grid-cols-3 gap-1 mt-2">
                  <div>
                    <div className="text-xs text-gray-500">Day</div>
                    <div className="text-sm font-bold text-blue-400">{formatNumber(teamStats?.teamHoursWorkedToday || teamHoursWorked / 5)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Week</div>
                    <div className="text-sm font-bold text-blue-400">{formatNumber(teamHoursWorked)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Month</div>
                    <div className="text-sm font-bold text-blue-400">{formatNumber(teamMonthlyHours)}</div>
                  </div>
                </div>
              </div>
              
              {/* Scans tracking - Uses direct weeklyScans for all periods if aggregated data is unavailable */}
              <div className="bg-gray-800 p-2 rounded text-center">
                <div className="text-xs text-gray-400">Team Scans</div>
                <div className="grid grid-cols-3 gap-1 mt-2">
                  <div>
                    <div className="text-xs text-gray-500">Week</div>
                    <div className="text-sm font-bold text-green-400">{formatNumber(teamScans)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Month</div>
                    <div className="text-sm font-bold text-green-400">{formatNumber(monthlyScans)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">YTD</div>
                    <div className="text-sm font-bold text-green-400">{formatNumber(ytdScans)}</div>
                  </div>
                </div>
              </div>
              
              {/* Cars found tracking - Uses weekly values for all periods if aggregated data is unavailable */}
              <div className="bg-gray-800 p-2 rounded text-center">
                <div className="text-xs text-gray-400">Cars Found</div>
                <div className="grid grid-cols-3 gap-1 mt-2">
                  <div>
                    <div className="text-xs text-gray-500">Week</div>
                    <div className="text-sm font-bold text-yellow-400">{formatNumber(teamCarsFound)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Month</div>
                    <div className="text-sm font-bold text-yellow-400">{formatNumber(monthlyFound)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">YTD</div>
                    <div className="text-sm font-bold text-yellow-400">{formatNumber(ytdFound)}</div>
                  </div>
                </div>
              </div>
              
              {/* Recovery rates */}
              <div className="bg-gray-800 p-2 rounded text-center">
                <div className="text-xs text-gray-400">Recovery Rate</div>
                <div className="mt-2">
                  <div className="text-lg font-bold text-purple-400">{formatPercent(teamRecoveryRate)}</div>
                  <div className="w-full bg-gray-700 rounded-full h-1.5 mt-1">
                    <div 
                      className="bg-gradient-to-r from-yellow-500 via-green-500 to-green-600 h-1.5 rounded-full" 
                      style={{ width: `${Math.min(100, teamRecoveryRate || 0)}%` }}
                      aria-hidden="true"
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">Weekly average</div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Team Efficiency Stats */}
          <div className="bg-gray-700 p-3 rounded border border-gray-600 mt-4">
            <h3 className="text-sm font-semibold text-blue-300 mb-3">Team Efficiency Metrics</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
              <div className="bg-gray-800 p-2 rounded text-center">
                <div className="text-xs text-gray-400">Scans per Hour</div>
                <div className="text-lg font-bold text-blue-400">
                  {teamHoursWorked > 0 ? 
                    formatNumber(Math.min(teamScans / Math.max(teamHoursWorked, 0.1), 1000)) : 
                    '0'}
                </div>
              </div>
              <div className="bg-gray-800 p-2 rounded text-center">
                <div className="text-xs text-gray-400">Cars per Hour</div>
                <div className="text-lg font-bold text-green-400">
                  {teamHoursWorked > 0 ? 
                    formatNumber(Math.min(teamCarsFound / Math.max(teamHoursWorked, 0.1), 100)) : 
                    '0'}
                </div>
              </div>
              <div className="bg-gray-800 p-2 rounded text-center">
                <div className="text-xs text-gray-400">Recovery Rate</div>
                <div className="text-lg font-bold text-purple-400">
                  {formatPercent(teamRecoveryRate)}
                </div>
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-400 text-center">
              Weekly efficiency stats based on tracked hours
            </div>
          </div>
          
          {/* Member Performance Comparison - When user is selected */}
          {selectedUser && (
            <div className="bg-gray-700 p-3 rounded border border-gray-600 mt-4">
              <h3 className="text-sm font-semibold text-blue-300 mb-3">Your Weekly Performance</h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {/* Weekly Time & Efficiency */}
                <div className="bg-gray-800 p-3 rounded">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-xs text-white">Weekly Hours</span>
                    <span className="text-lg font-semibold text-blue-400">{formatNumber(userHoursWorked)}</span>
                  </div>
                  
                  <div className="flex items-center mb-2">
                    <div className="w-full h-2 bg-gray-700 rounded-full mr-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full" 
                        style={{ width: `${Math.min(100, (userHoursWorked / 40) * 100)}%` }}
                        aria-hidden="true"
                      ></div>
                    </div>
                    <span className="text-xs text-gray-400 whitespace-nowrap">{formatPercent((userHoursWorked / 40) * 100)} of 40h</span>
                  </div>
                  
                  <div className="flex justify-between items-center mt-4 text-xs text-gray-400">
                    <span>Efficiency Score:</span>
                    <div className="flex items-center">
                      <div className="w-16 h-1.5 bg-gray-700 rounded-full mr-2">
                        <div 
                          className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-1.5 rounded-full" 
                          style={{ 
                            width: `${Math.min(100, ((userCarsFound / Math.max(0.1, userHoursWorked)) / 
                                    (teamCarsFound / Math.max(0.1, teamHoursWorked))) * 50)}%` 
                          }}
                          aria-hidden="true"
                        ></div>
                      </div>
                      <span className={`
                        ${((userCarsFound / Math.max(0.1, userHoursWorked)) > 
                           (teamCarsFound / Math.max(0.1, teamHoursWorked))) ? 'text-green-400' : 'text-gray-400'}
                      `}>
                        {formatNumber(((userCarsFound / Math.max(0.1, userHoursWorked)) / 
                                    (teamCarsFound / Math.max(0.1, teamHoursWorked))) * 100)}%
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Weekly Cars & Recovery */}
                <div className="bg-gray-800 p-3 rounded">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <div className="text-xs text-gray-400">Found</div>
                      <div className="text-lg font-bold text-green-400">
                        {formatNumber(userCarsFound)}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-400">Recovered</div>
                      <div className="text-lg font-bold text-blue-400">
                        {formatNumber(userCarsRecovered)}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-400">Scans</div>
                      <div className="text-lg font-bold text-yellow-400">
                        {formatNumber(userScans)}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-400">Recovery Rate</div>
                      <div className="text-lg font-bold text-purple-400">
                        {formatPercent(userRecoveryRate)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}

export default TeamSummary;