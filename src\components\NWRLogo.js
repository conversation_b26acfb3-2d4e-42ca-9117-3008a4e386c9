import React, { useEffect, useRef } from 'react';
import './NWRLogo.css';

const NWRLogo = () => {
  const videoRef = useRef(null);

  useEffect(() => {
    const videoElement = videoRef.current;
    
    if (videoElement) {
      videoElement.defaultMuted = true;
      videoElement.muted = true;
      
      const playPromise = videoElement.play();
      
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            videoElement.loop = true;
          })
          .catch(error => {
            console.error("Error attempting to autoplay:", error);
          });
      }
    }
  }, []);

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 shadow-xl relative overflow-hidden">
        {/* Video Background */}
        <video 
          ref={videoRef}
          className="absolute inset-0 w-full h-full object-cover opacity-30 z-0"
          autoPlay
          muted
          loop
          playsInline
        >
          <source src="/New project.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* SVG Logo Overlay */}
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 400 200" 
          className="w-64 h-auto relative z-10"
        >
          {/* Animated Letters with Spacing and Color Transition */}
          <text 
            x="30%" 
            y="40%" 
            textAnchor="middle" 
            className="font-bold text-6xl n-letter"
          >
            N
          </text>
          <text 
            x="50%" 
            y="40%" 
            textAnchor="middle" 
            className="font-bold text-6xl w-letter"
          >
            W
          </text>
          <text 
            x="70%" 
            y="40%" 
            textAnchor="middle" 
            className="font-bold text-6xl r-letter"
          >
            R
          </text>

          {/* Animated Underline */}
          <line 
            x1="30%" 
            y1="50%" 
            x2="70%" 
            y2="50%" 
            stroke="currentColor" 
            strokeWidth="3" 
            className="underline-animation text-gray-500"
          />
        </svg>

        {/* Subtitle - Moved much closer to letters */}
        <div className="text-gray-400 text-center -mt-2 text-lg subtitle-animation relative z-10">
          North West Repo
        </div>
      </div>
    </div>
  );
};

export default NWRLogo;