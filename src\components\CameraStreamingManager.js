import React, { useState, useEffect, useRef } from 'react';
import { getFirestore, doc, setDoc, updateDoc, onSnapshot, collection, query, getDocs, where, serverTimestamp } from 'firebase/firestore';
import { useAuth } from '../contexts/AuthContext';

// This manager component enables a client-server architecture for continuous camera streaming
const CameraStreamingManager = ({ children }) => {
  const { currentUser } = useAuth();
  const [isServer, setIsServer] = useState(false);
  const [clientId, setClientId] = useState(null);
  const [serverInfo, setServerInfo] = useState(null);
  const [clientConnected, setClientConnected] = useState(false);
  const [streamingSessions, setStreamingSessions] = useState([]);
  const heartbeatIntervalRef = useRef(null);
  const persistedSessionRef = useRef(null); // To track persisted sessions

  // Generate unique ID for this session or recover the existing one
  useEffect(() => {
    // First check if there's a persisted session in localStorage
    const persistedSession = localStorage.getItem('activeCameraSession');
    let sessionId;
    
    if (persistedSession) {
      try {
        const sessionData = JSON.parse(persistedSession);
        if (sessionData && sessionData.clientId && Date.now() - sessionData.timestamp < 86400000) { // 24hr validity
          sessionId = sessionData.clientId;
          persistedSessionRef.current = sessionData;
          console.log('Recovered persisted camera session:', sessionId);
        } else {
          sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        }
      } catch (e) {
        console.error('Error parsing persisted session:', e);
        sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
      }
    } else {
      sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    }
    
    setClientId(sessionId);
    
    // Check if this was previously a server session
    const serverSession = localStorage.getItem('cameraServerMode');
    const serverSessionId = localStorage.getItem('cameraServerSessionId');
    
    if (serverSession === 'active' && serverSessionId === sessionId) {
      console.log('Automatically restoring server mode from previous session');
      setIsServer(true);
    }
    
    // Listen for streaming sessions
    const db = getFirestore();
    const unsubscribe = onSnapshot(
      collection(db, 'streamingSessions'),
      (snapshot) => {
        const sessions = [];
        snapshot.forEach(doc => {
          sessions.push({
            id: doc.id,
            ...doc.data()
          });
        });
        setStreamingSessions(sessions);
      }
    );
    
    return () => unsubscribe();
  }, []);

  // Process messages from service worker
  useEffect(() => {
    if (!navigator.serviceWorker) return;
    
    const handleServiceWorkerMessage = (event) => {
      if (event.data && event.data.type === 'CAMERA_KEEPALIVE') {
        console.log('Received keepalive from service worker');
        sendHeartbeat();
      }
    };
    
    navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
    
    return () => {
      navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
    };
  }, [clientId]);

  // Function to send a heartbeat to Firestore
  const sendHeartbeat = async () => {
    if (!clientId || !currentUser) return;
    
    try {
      const db = getFirestore();
      const sessionRef = doc(db, 'clientSessions', clientId);
      
      await updateDoc(sessionRef, {
        lastActive: serverTimestamp(),
        status: 'active'
      });
      
      // If this is a server, also update the server session
      if (isServer) {
        const serverRef = doc(db, 'streamingSessions', clientId);
        await updateDoc(serverRef, {
          lastActive: serverTimestamp()
        });
      }
      
      console.log('Heartbeat sent successfully');
    } catch (error) {
      console.error('Error sending heartbeat:', error);
      
      // If document doesn't exist, try to recreate it
      if (error.code === 'not-found') {
        try {
          await registerSession();
        } catch (registerError) {
          console.error('Error re-registering session:', registerError);
        }
      }
    }
  };

  // Register session when component mounts
  useEffect(() => {
    if (!clientId || !currentUser) return;
    
    const registerSession = async () => {
      const db = getFirestore();
      const sessionRef = doc(db, 'clientSessions', clientId);
      
      await setDoc(sessionRef, {
        userId: currentUser.uid,
        userEmail: currentUser.email,
        status: 'active',
        lastActive: serverTimestamp(),
        isServer: isServer,
        userAgent: navigator.userAgent,
        createdAt: serverTimestamp(),
        persistentMode: true // Mark as a persistent session
      });

      // Store session info in localStorage to persist across page navigations
      localStorage.setItem('activeCameraSession', JSON.stringify({
        clientId,
        userId: currentUser.uid,
        timestamp: Date.now(),
        isServer: isServer
      }));

      // Register with service worker if available
      if ('serviceWorker' in navigator) {
        try {
          navigator.serviceWorker.register('/camera-service-worker.js')
            .then(registration => {
              console.log('Camera service worker registered:', registration);
              
              // Send session info to service worker
              if (navigator.serviceWorker.controller) {
                navigator.serviceWorker.controller.postMessage({
                  type: 'REGISTER_CAMERA_SESSION',
                  sessionId: clientId,
                  userId: currentUser.uid,
                  isServer: isServer
                });
              }
            });
        } catch (error) {
          console.error('Service worker registration failed:', error);
        }
      }

      // Set up interval for heartbeat
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
      
      heartbeatIntervalRef.current = setInterval(sendHeartbeat, 30000);
      
      // Also set up a more aggressive background ping using broadcast channel
      // This allows different tabs to coordinate
      try {
        const broadcastChannel = new BroadcastChannel('camera-system-channel');
        broadcastChannel.postMessage({
          type: 'SESSION_REGISTERED',
          clientId,
          timestamp: Date.now()
        });
        
        broadcastChannel.onmessage = (event) => {
          if (event.data && event.data.type === 'PING_REQUEST') {
            sendHeartbeat();
            broadcastChannel.postMessage({
              type: 'PING_RESPONSE',
              clientId,
              timestamp: Date.now()
            });
          }
        };
      } catch (e) {
        console.log('Broadcast Channel not supported:', e);
      }
      
      // Clean up function for component unmount
      return () => {
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
        }
        
        // Critical: Don't actually delete the session document on component unmount,
        // just mark it as inactive but still running. This allows background operation.
        updateDoc(sessionRef, {
          lastActive: serverTimestamp(),
          backgroundRunning: true, // Indicate it's still running in background
          lastVisibleTimestamp: serverTimestamp()
        }).catch(error => {
          console.error('Error updating session status on unmount:', error);
        });
      };
    };

    registerSession();
    
    // Setup visibility change listener to detect when tab goes to background
    const handleVisibilityChange = () => {
      if (document.hidden) {
        console.log('Page hidden - ensuring background operation');
        // When page goes to background, make sure we keep running
        // Update the session to indicate background status
        const db = getFirestore();
        const sessionRef = doc(db, 'clientSessions', clientId);
        updateDoc(sessionRef, {
          backgroundRunning: true,
          visibilityState: 'hidden',
          lastVisibilityChange: serverTimestamp()
        }).catch(error => {
          console.error('Error updating background status:', error);
        });
      } else {
        console.log('Page visible again - resuming normal operation');
        // When page comes back to foreground
        const db = getFirestore();
        const sessionRef = doc(db, 'clientSessions', clientId);
        updateDoc(sessionRef, {
          backgroundRunning: false,
          visibilityState: 'visible',
          lastVisibilityChange: serverTimestamp()
        }).catch(error => {
          console.error('Error updating foreground status:', error);
        });
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Also set up beforeunload event to notify about page close
    const handleBeforeUnload = () => {
      // Send a synchronous fetch to update status before page unloads
      try {
        const formData = new FormData();
        formData.append('sessionId', clientId);
        formData.append('userId', currentUser.uid);
        formData.append('action', 'page_closing');
        
        // Sync request to ensure it completes before page closes
        navigator.sendBeacon('/api/camera-session-update', formData);
      } catch (e) {
        console.error('Error sending beacon:', e);
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [clientId, currentUser, isServer]);

  // Start a server session
  const startServerSession = async () => {
    if (!clientId || !currentUser) return;
    
    try {
      const db = getFirestore();
      // Check if there's already an active server
      const serversQuery = query(
        collection(db, 'streamingSessions'), 
        where('status', '==', 'active')
      );
      
      const snapshot = await getDocs(serversQuery);
      let existingServer = null;
      
      snapshot.forEach(doc => {
        existingServer = {
          id: doc.id,
          ...doc.data()
        };
      });
      
      if (existingServer) {
        if (window.confirm('There is already an active streaming server. Do you want to take over?')) {
          // Update the existing server to inactive
          await updateDoc(doc(db, 'streamingSessions', existingServer.id), {
            status: 'inactive',
            lastActive: serverTimestamp(),
            endedAt: serverTimestamp(),
            endReason: 'manual-takeover'
          });
        } else {
          return false; // User canceled
        }
      }
      
      // Create a new streaming session
      const sessionRef = doc(db, 'streamingSessions', clientId);
      await setDoc(sessionRef, {
        userId: currentUser.uid,
        userEmail: currentUser.email,
        status: 'active',
        startedAt: serverTimestamp(),
        lastActive: serverTimestamp(),
        deviceInfo: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          vendor: navigator.vendor
        },
        cameraCount: 0, // Will be updated by the CameraSys component
        activeCameras: 0, // Will be updated by the CameraSys component
        persistentMode: true, // This server will keep running in background
        backgroundMode: true // Server is meant to run in background
      });
      
      // Save server session to localStorage for persistence
      localStorage.setItem('cameraServerMode', 'active');
      localStorage.setItem('cameraServerSessionId', clientId);
      
      // Register with service worker as a server
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'SERVER_MODE_STARTED',
          sessionId: clientId,
          userId: currentUser.uid
        });
      }
      
      setIsServer(true);
      
      // Add event listener for page unload to properly mark the server as inactive
      const handleBeforeUnload = async (event) => {
        // This won't actually work right before page unload due to how the
        // beforeunload event works, but it's worth trying
        try {
          // Use navigator.sendBeacon for more reliable background updates
          const formData = new FormData();
          formData.append('sessionId', clientId);
          formData.append('userId', currentUser.uid);
          formData.append('action', 'server_still_running');
          
          navigator.sendBeacon('/api/camera-server-update', formData);
        } catch (error) {
          console.error('Error updating session on unload:', error);
        }
        
        // Don't show confirmation message - let it close silently and continue in background
        // event.returnValue = 'The camera server is still running. Are you sure you want to leave?';
        // return event.returnValue;
      };
      
      window.addEventListener('beforeunload', handleBeforeUnload);
      
      // Setup a heartbeat to keep the server marked as active
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
      
      heartbeatIntervalRef.current = setInterval(async () => {
        try {
          await updateDoc(sessionRef, {
            lastActive: serverTimestamp()
          });
        } catch (error) {
          console.error('Error updating server heartbeat:', error);
        }
      }, 15000); // Every 15 seconds
      
      return true;
    } catch (error) {
      console.error('Error starting server session:', error);
      return false;
    }
  };

  // Stop a server session
  const stopServerSession = async () => {
    if (!clientId || !isServer) return;
    
    try {
      const db = getFirestore();
      const sessionRef = doc(db, 'streamingSessions', clientId);
      await updateDoc(sessionRef, {
        status: 'inactive',
        lastActive: serverTimestamp(),
        endedAt: serverTimestamp(),
        endReason: 'manual-stop'
      });
      
      // Remove server session from localStorage
      localStorage.removeItem('cameraServerMode');
      localStorage.removeItem('cameraServerSessionId');
      
      // Notify service worker
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'SERVER_MODE_STOPPED',
          sessionId: clientId
        });
      }
      
      setIsServer(false);
      return true;
    } catch (error) {
      console.error('Error stopping server session:', error);
      return false;
    }
  };

  // Connect to a server as a client
  const connectToServer = async (serverId) => {
    if (!clientId || !currentUser) return;
    
    try {
      const db = getFirestore();
      // Check if server is active
      const serverRef = doc(db, 'streamingSessions', serverId);
      const unsubscribe = onSnapshot(serverRef, (snapshot) => {
        if (snapshot.exists()) {
          const data = snapshot.data();
          if (data.status === 'active') {
            setServerInfo(data);
            setClientConnected(true);
          } else {
            setServerInfo(null);
            setClientConnected(false);
          }
        } else {
          setServerInfo(null);
          setClientConnected(false);
        }
      });
      
      // Register as a connected client
      await setDoc(doc(db, 'connectedClients', clientId), {
        userId: currentUser.uid,
        userEmail: currentUser.email,
        connectedTo: serverId,
        connectedAt: serverTimestamp(),
        lastActive: serverTimestamp(),
        deviceInfo: {
          userAgent: navigator.userAgent,
          platform: navigator.platform
        }
      });
      
      // Heartbeat for client
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
      
      heartbeatIntervalRef.current = setInterval(async () => {
        try {
          await updateDoc(doc(db, 'connectedClients', clientId), {
            lastActive: serverTimestamp()
          });
        } catch (error) {
          console.error('Error updating client heartbeat:', error);
        }
      }, 30000); // Every 30 seconds
      
      return () => {
        clearInterval(heartbeatIntervalRef.current);
        unsubscribe();
      };
    } catch (error) {
      console.error('Error connecting to server:', error);
      return null;
    }
  };

  // Disconnect from server
  const disconnectFromServer = async () => {
    if (!clientId || !clientConnected) return;
    
    try {
      const db = getFirestore();
      await updateDoc(doc(db, 'connectedClients', clientId), {
        disconnectedAt: serverTimestamp(),
        status: 'disconnected'
      });
      
      setServerInfo(null);
      setClientConnected(false);
      return true;
    } catch (error) {
      console.error('Error disconnecting from server:', error);
      return false;
    }
  };

  // Provide all the streaming functions and state to children
  const contextValue = {
    isServer,
    clientId,
    serverInfo,
    clientConnected,
    streamingSessions,
    startServerSession,
    stopServerSession,
    connectToServer,
    disconnectFromServer
  };

  return (
    <div className="streaming-manager">
      {/* Pass all props to children with additional streaming context */}
      {React.Children.map(children, child => 
        React.cloneElement(child, { streamingContext: contextValue })
      )}
    </div>
  );
};

export default CameraStreamingManager;