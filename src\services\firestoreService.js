/**
 * Firestore service for managing database operations
 */
import { 
  doc, 
  collection, 
  getDocs, 
  getDoc, 
  setDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  onSnapshot, 
  serverTimestamp, 
  limit, 
  orderBy,
  documentId
} from 'firebase/firestore';

/**
 * Location Collection Services
 */

/**
 * Set up a real-time listener for locations
 * 
 * @param {Object} db - Firestore database instance
 * @param {Function} onLocationsUpdate - Callback for location updates
 * @param {Function} onError - Error callback
 * @returns {Function} Unsubscribe function
 */
export const subscribeToLocations = (db, onLocationsUpdate, onError) => {
  try {
    return onSnapshot(
      collection(db, 'locations'),
      (snapshot) => {
        const fetchedLocations = [];
        
        snapshot.forEach((doc) => {
          const data = doc.data();
          const location = {
            id: doc.id,
            name: data.name,
            position: {
              lat: data.position.lat,
              lng: data.position.lng
            },
            isAdminOnly: data.isAdminOnly || false,
            isPriority: data.isPriority || false,
            details: data.details || '',
            images: data.images || [],
            parkingSide: data.parkingSide || null,
            createdBy: data.createdBy || null,
            createdAt: data.createdAt || null,
            address: data.address || '',
            intersection: data.intersection || '',
            status: data.status || 'pending',
            pickedUpBy: data.pickedUpBy || null,
            pickedUpAt: data.pickedUpAt || null,
            plateNumber: data.plateNumber || '',
            vin: data.vin || '',
            driveType: data.driveType || '',
            make: data.make || '',
            model: data.model || ''
          };
          
          fetchedLocations.push(location);
        });
        
        onLocationsUpdate(fetchedLocations);
      },
      (error) => {
        console.error("Error in locations listener:", error);
        if (onError) onError(error);
      }
    );
  } catch (error) {
    console.error("Error setting up locations listener:", error);
    if (onError) onError(error);
    return () => {}; // Return empty function as fallback
  }
};

/**
 * Add a new location
 * 
 * @param {Object} db - Firestore database instance
 * @param {Object} locationData - Location data to add
 * @param {Object} user - Current user
 * @returns {Promise<string>} Created location ID
 */
export const addLocation = async (db, locationData, user) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    // Add user data and server timestamp
    const newLocationData = {
      ...locationData,
      createdBy: user.uid,
      createdAt: serverTimestamp()
    };
    
    const docRef = await addDoc(collection(db, 'locations'), newLocationData);
    return docRef.id;
  } catch (error) {
    console.error("Error adding location:", error);
    throw error;
  }
};

/**
 * Update an existing location
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} locationId - Location ID to update
 * @param {Object} locationData - Updated location data
 * @param {Object} user - Current user
 * @returns {Promise<boolean>} Success status
 */
export const updateLocation = async (db, locationId, locationData, user) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    // Check permissions
    const locationRef = doc(db, 'locations', locationId);
    const locationDoc = await getDoc(locationRef);
    
    if (!locationDoc.exists()) {
      throw new Error("Location does not exist");
    }
    
    const currentData = locationDoc.data();
    
    // Check if user has permission (admin or owner)
    const isAdmin = await checkIfUserIsAdmin(db, user.uid);
    const isOwner = currentData.createdBy === user.uid;
    
    if (!isAdmin && (!isOwner || currentData.isAdminOnly)) {
      throw new Error("You don't have permission to update this location");
    }
    
    // Add update timestamp and preserve original creation data
    const updatedData = {
      ...locationData,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(locationRef, updatedData);
    return true;
  } catch (error) {
    console.error("Error updating location:", error);
    throw error;
  }
};

/**
 * Delete a location
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} locationId - Location ID to delete
 * @param {Object} user - Current user
 * @returns {Promise<boolean>} Success status
 */
export const deleteLocation = async (db, locationId, user) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    // Check permissions
    const locationRef = doc(db, 'locations', locationId);
    const locationDoc = await getDoc(locationRef);
    
    if (!locationDoc.exists()) {
      throw new Error("Location does not exist");
    }
    
    const locationData = locationDoc.data();
    
    // Check if user has permission (admin or owner)
    const isAdmin = await checkIfUserIsAdmin(db, user.uid);
    const isOwner = locationData.createdBy === user.uid;
    
    if (!isAdmin && (!isOwner || locationData.isAdminOnly)) {
      throw new Error("You don't have permission to delete this location");
    }
    
    await deleteDoc(locationRef);
    return true;
  } catch (error) {
    console.error("Error deleting location:", error);
    throw error;
  }
};

/**
 * Mark a location as picked up
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} locationId - Location ID
 * @param {Object} pickupData - Pickup details
 * @param {Object} user - Current user
 * @returns {Promise<boolean>} Success status
 */
export const markLocationAsPickedUp = async (db, locationId, pickupData, user) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    // Check if user has tow truck role
    const hasTowTruckRole = await checkUserHasTag(db, user.uid, "Tow Truck");
    if (!hasTowTruckRole) {
      throw new Error("Only tow truck drivers can mark locations as picked up");
    }
    
    // Get location data
    const locationRef = doc(db, 'locations', locationId);
    const locationDoc = await getDoc(locationRef);
    
    if (!locationDoc.exists()) {
      throw new Error("Location does not exist");
    }
    
    const locationData = locationDoc.data();
    
    // Check if already picked up
    if (locationData.status === 'picked-up') {
      throw new Error("This location has already been picked up");
    }
    
    // Update with pickup details
    const updateData = {
      status: 'picked-up',
      pickedUpBy: user.uid,
      pickedUpAt: serverTimestamp(),
      ...pickupData
    };
    
    await updateDoc(locationRef, updateData);
    return true;
  } catch (error) {
    console.error("Error marking location as picked up:", error);
    throw error;
  }
};

/**
 * User Location Services
 */

/**
 * Subscribe to user locations
 * 
 * @param {Object} db - Firestore database instance
 * @param {Function} onUsersUpdate - Callback for user updates
 * @param {Function} onError - Error callback
 * @param {Object} options - Subscription options
 * @returns {Function} Unsubscribe function
 */
export const subscribeToUserLocations = (db, onUsersUpdate, onError, options = {}) => {
  const {
    onlineOnly = true,
    excludeCurrentUser = true,
    currentUserId = null
  } = options;
  
  try {
    // Build query
    let userQuery = collection(db, 'userLocations');
    
    if (onlineOnly) {
      userQuery = query(userQuery, where('online', '==', true));
    }
    
    return onSnapshot(
      userQuery,
      (snapshot) => {
        const users = [];
        
        snapshot.forEach(doc => {
          const userData = doc.data();
          
          // Skip current user if excludeCurrentUser is true
          if (excludeCurrentUser && currentUserId && userData.uid === currentUserId) {
            return;
          }
          
          users.push({
            ...userData,
            id: doc.id
          });
        });
        
        onUsersUpdate(users);
      },
      (error) => {
        console.error("Error in user locations listener:", error);
        if (onError) onError(error);
      }
    );
  } catch (error) {
    console.error("Error setting up user locations listener:", error);
    if (onError) onError(error);
    return () => {}; // Return empty function as fallback
  }
};

/**
 * Update user's location
 * 
 * @param {Object} db - Firestore database instance
 * @param {Object} position - Position coordinates
 * @param {Object} user - Current user
 * @returns {Promise<boolean>} Success status
 */
export const updateUserLocation = async (db, position, user) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    const userDocRef = doc(db, 'userLocations', user.uid);
    
    // Check if document exists
    const userDoc = await getDoc(userDocRef);
    
    const userData = {
      uid: user.uid,
      email: user.email || '',
      displayName: user.displayName || '',
      position: {
        lat: position.lat,
        lng: position.lng
      },
      lastUpdated: serverTimestamp(),
      online: true
    };
    
    if (userDoc.exists()) {
      // Update existing document
      await updateDoc(userDocRef, userData);
    } else {
      // Create new document with specific ID
      await setDoc(userDocRef, userData);
    }
    
    return true;
  } catch (error) {
    console.error("Error updating user location:", error);
    throw error;
  }
};

/**
 * Update user's online status
 * 
 * @param {Object} db - Firestore database instance
 * @param {boolean} isOnline - Online status
 * @param {Object} user - Current user
 * @returns {Promise<boolean>} Success status
 */
export const updateUserOnlineStatus = async (db, isOnline, user) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    const userDocRef = doc(db, 'userLocations', user.uid);
    
    // Check if document exists
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      // Just update the online status and timestamp
      await updateDoc(userDocRef, {
        online: isOnline,
        lastUpdated: serverTimestamp()
      });
    } else if (isOnline) {
      // Only create document if setting to online
      const userData = {
        uid: user.uid,
        email: user.email || '',
        displayName: user.displayName || '',
        position: null, // Will be set by location updates
        lastUpdated: serverTimestamp(),
        online: isOnline
      };
      
      await setDoc(userDocRef, userData);
    }
    
    return true;
  } catch (error) {
    console.error("Error updating user online status:", error);
    throw error;
  }
};

/**
 * User Traces/Trails Services
 */

/**
 * Subscribe to user traces (breadcrumb trails)
 * 
 * @param {Object} db - Firestore database instance
 * @param {Function} onTracesUpdate - Callback for trace updates
 * @param {Function} onError - Error callback
 * @returns {Function} Unsubscribe function
 */
export const subscribeToUserTraces = (db, onTracesUpdate, onError) => {
  try {
    return onSnapshot(
      collection(db, 'userTraces'),
      (snapshot) => {
        const traces = {};
        
        snapshot.forEach(doc => {
          const traceData = doc.data();
          traces[traceData.uid] = traceData.trace || [];
        });
        
        onTracesUpdate(traces);
      },
      (error) => {
        console.error("Error in user traces listener:", error);
        if (onError) onError(error);
      }
    );
  } catch (error) {
    console.error("Error setting up user traces listener:", error);
    if (onError) onError(error);
    return () => {}; // Return empty function as fallback
  }
};

/**
 * Update user trace (add new position)
 * 
 * @param {Object} db - Firestore database instance
 * @param {Object} position - Position coordinates
 * @param {Object} user - Current user 
 * @param {number} minDistanceMeters - Minimum distance to add point (default: 10 meters)
 * @returns {Promise<boolean>} Success status
 */
export const updateUserTrace = async (db, position, user, minDistanceMeters = 10) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    const userTraceRef = doc(db, 'userTraces', user.uid);
    
    // Check if document exists
    const userTraceDoc = await getDoc(userTraceRef);
    
    if (userTraceDoc.exists()) {
      // Update existing trace
      const existingTrace = userTraceDoc.data().trace || [];
      
      // Only add if we've moved enough from the last point
      if (existingTrace.length > 0) {
        const lastPoint = existingTrace[existingTrace.length - 1];
        const lastPosition = {
          lat: lastPoint.lat,
          lng: lastPoint.lng
        };
        
        // Calculate distance
        const distance = calculateDistanceInMeters(lastPosition, position);
        
        if (distance > minDistanceMeters) {
          await updateDoc(userTraceRef, {
            trace: [...existingTrace, {
              lat: position.lat,
              lng: position.lng,
              timestamp: serverTimestamp()
            }]
          });
        }
      } else {
        // First point in trace
        await updateDoc(userTraceRef, {
          trace: [{
            lat: position.lat,
            lng: position.lng,
            timestamp: serverTimestamp()
          }]
        });
      }
    } else {
      // Create new trace
      await setDoc(userTraceRef, {
        uid: user.uid,
        trace: [{
          lat: position.lat,
          lng: position.lng,
          timestamp: serverTimestamp()
        }]
      });
    }
    
    return true;
  } catch (error) {
    console.error("Error updating user trace:", error);
    throw error;
  }
};

/**
 * Clear user trace (remove all points)
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} userId - User ID to clear
 * @param {Object} currentUser - Current user (for permission check)
 * @returns {Promise<boolean>} Success status
 */
export const clearUserTrace = async (db, userId, currentUser) => {
  try {
    if (!currentUser || !currentUser.uid) {
      throw new Error("User authentication required");
    }
    
    // Check permissions (admin or self)
    if (userId !== currentUser.uid) {
      const isAdmin = await checkIfUserIsAdmin(db, currentUser.uid);
      if (!isAdmin) {
        throw new Error("Only admins can clear other users' traces");
      }
    }
    
    // Clear trace
    const userTraceRef = doc(db, 'userTraces', userId);
    const traceDoc = await getDoc(userTraceRef);
    
    if (traceDoc.exists()) {
      await updateDoc(userTraceRef, {
        trace: []
      });
    } else {
      // Create empty trace if it doesn't exist
      await setDoc(userTraceRef, {
        uid: userId,
        trace: []
      });
    }
    
    return true;
  } catch (error) {
    console.error("Error clearing user trace:", error);
    throw error;
  }
};

/**
 * Chat Services
 */

/**
 * Subscribe to chat messages
 * 
 * @param {Object} db - Firestore database instance
 * @param {Function} onMessagesUpdate - Callback for messages updates
 * @param {Function} onError - Error callback
 * @param {number} messagesLimit - Maximum number of messages to load
 * @returns {Function} Unsubscribe function
 */
export const subscribeToChatMessages = (db, onMessagesUpdate, onError, messagesLimit = 100) => {
  try {
    const messagesQuery = query(
      collection(db, 'chatMessages'),
      orderBy('timestamp', 'asc'),
      limit(messagesLimit)
    );
    
    return onSnapshot(
      messagesQuery,
      (snapshot) => {
        const messages = [];
        
        snapshot.forEach((doc) => {
          const data = doc.data();
          
          // Convert Firestore timestamp to JS Date
          const timestamp = data.timestamp ? new Date(data.timestamp.toDate()) : new Date();
          
          messages.push({
            id: doc.id,
            text: data.text || '',
            sender: {
              uid: data.sender?.uid || 'unknown',
              name: data.sender?.name || 'Unknown User',
              photo: data.sender?.photo || null
            },
            timestamp: timestamp
          });
        });
        
        // Sort by timestamp
        messages.sort((a, b) => a.timestamp - b.timestamp);
        
        onMessagesUpdate(messages);
      },
      (error) => {
        console.error("Error in chat messages listener:", error);
        if (onError) onError(error);
      }
    );
  } catch (error) {
    console.error("Error setting up chat messages listener:", error);
    if (onError) onError(error);
    return () => {}; // Return empty function as fallback
  }
};

/**
 * Send a chat message
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} messageText - Message content
 * @param {Object} user - Current user
 * @returns {Promise<string>} Message ID
 */
export const sendChatMessage = async (db, messageText, user) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    if (!messageText.trim()) {
      throw new Error("Message cannot be empty");
    }
    
    // Get user's display name and photo
    let displayName = user.displayName || user.email || 'User';
    let userPhoto = null;
    
    try {
      const profileDoc = await getDoc(doc(db, 'userProfiles', user.uid));
      if (profileDoc.exists()) {
        // Use profile data if available
        displayName = profileDoc.data().displayName || displayName;
        userPhoto = profileDoc.data().photoBase64 || null;
      }
    } catch (err) {
      console.warn("Error fetching user profile for chat:", err);
    }
    
    // Create message data
    const messageData = {
      text: messageText,
      sender: {
        uid: user.uid,
        name: displayName,
        photo: userPhoto
      },
      timestamp: serverTimestamp()
    };
    
    // Add to Firestore
    const docRef = await addDoc(collection(db, 'chatMessages'), messageData);
    return docRef.id;
  } catch (error) {
    console.error("Error sending chat message:", error);
    throw error;
  }
};

/**
 * User Profile Services
 */

/**
 * Get user profile data
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User profile data
 */
export const getUserProfile = async (db, userId) => {
  try {
    if (!userId) {
      throw new Error("User ID is required");
    }
    
    const profileRef = doc(db, "userProfiles", userId);
    const profileDoc = await getDoc(profileRef);
    
    if (profileDoc.exists()) {
      return {
        ...profileDoc.data(),
        id: profileDoc.id
      };
    }
    
    return null;
  } catch (error) {
    console.error("Error getting user profile:", error);
    throw error;
  }
};

/**
 * Get user profile picture
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} userId - User ID
 * @returns {Promise<string|null>} Profile picture as base64
 */
export const getUserProfilePicture = async (db, userId) => {
  try {
    if (!userId) {
      throw new Error("User ID is required");
    }
    
    const profileRef = doc(db, "userProfiles", userId);
    const profileDoc = await getDoc(profileRef);
    
    if (profileDoc.exists() && profileDoc.data().photoBase64) {
      return profileDoc.data().photoBase64;
    }
    
    return null;
  } catch (error) {
    console.error("Error fetching user profile picture:", error);
    throw error;
  }
};

/**
 * Get user display name
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} userId - User ID
 * @returns {Promise<string|null>} User display name
 */
export const getUserDisplayName = async (db, userId) => {
  try {
    if (!userId) {
      throw new Error("User ID is required");
    }
    
    const profileRef = doc(db, "userProfiles", userId);
    const profileDoc = await getDoc(profileRef);
    
    if (profileDoc.exists() && profileDoc.data().displayName) {
      return profileDoc.data().displayName;
    }
    
    return null;
  } catch (error) {
    console.error("Error fetching user display name:", error);
    throw error;
  }
};

/**
 * Update user profile
 * 
 * @param {Object} db - Firestore database instance
 * @param {Object} profileData - Profile data to update
 * @param {Object} user - Current user
 * @returns {Promise<boolean>} Success status
 */
export const updateUserProfile = async (db, profileData, user) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    const profileRef = doc(db, "userProfiles", user.uid);
    
    // Check if document exists
    const profileDoc = await getDoc(profileRef);
    
    // Include timestamp
    const updatedData = {
      ...profileData,
      updatedAt: serverTimestamp()
    };
    
    if (profileDoc.exists()) {
      // Update existing document
      await updateDoc(profileRef, updatedData);
    } else {
      // Create new document with defaults
      const newProfileData = {
        uid: user.uid,
        email: user.email || '',
        displayName: user.displayName || '',
        createdAt: serverTimestamp(),
        ...updatedData
      };
      
      await setDoc(profileRef, newProfileData);
    }
    
    // Update displayName in userLocations if needed
    if (profileData.displayName) {
      try {
        const userLocationRef = doc(db, 'userLocations', user.uid);
        const locationDoc = await getDoc(userLocationRef);
        
        if (locationDoc.exists()) {
          await updateDoc(userLocationRef, {
            displayName: profileData.displayName
          });
        }
      } catch (err) {
        console.warn("Error updating display name in userLocations:", err);
      }
    }
    
    return true;
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw error;
  }
};

/**
 * Clock Status Services
 */

/**
 * Update user's clock in/out status
 * 
 * @param {Object} db - Firestore database instance
 * @param {boolean} isClockedIn - Whether user is clocked in
 * @param {Object} user - Current user
 * @returns {Promise<boolean>} Success status
 */
export const updateClockStatus = async (db, isClockedIn, user) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    const userStatusRef = doc(db, 'userStatus', user.uid);
    
    await setDoc(userStatusRef, {
      clockedIn: isClockedIn,
      [isClockedIn ? 'clockInTime' : 'clockOutTime']: serverTimestamp(),
      uid: user.uid
    }, { merge: true });
    
    return true;
  } catch (error) {
    console.error(`Error updating clock ${isClockedIn ? 'in' : 'out'} status:`, error);
    throw error;
  }
};

/**
 * Get user's current clock status
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Clock status information
 */
export const getUserClockStatus = async (db, userId) => {
  try {
    if (!userId) {
      throw new Error("User ID is required");
    }
    
    const userStatusRef = doc(db, 'userStatus', userId);
    const statusDoc = await getDoc(userStatusRef);
    
    if (statusDoc.exists()) {
      const data = statusDoc.data();
      return {
        clockedIn: data.clockedIn || false,
        clockInTime: data.clockInTime ? new Date(data.clockInTime.toDate()) : null,
        clockOutTime: data.clockOutTime ? new Date(data.clockOutTime.toDate()) : null
      };
    }
    
    return {
      clockedIn: false,
      clockInTime: null,
      clockOutTime: null
    };
  } catch (error) {
    console.error("Error getting user clock status:", error);
    throw error;
  }
};

/**
 * Screenshots and Other Services
 */

/**
 * Save a map screenshot
 * 
 * @param {Object} db - Firestore database instance
 * @param {Object} screenshotData - Screenshot data
 * @param {Object} user - Current user
 * @returns {Promise<string>} Screenshot ID
 */
export const saveMapScreenshot = async (db, screenshotData, user) => {
  try {
    if (!user || !user.uid) {
      throw new Error("User authentication required");
    }
    
    // Check if user is admin
    const isAdmin = await checkIfUserIsAdmin(db, user.uid);
    if (!isAdmin) {
      throw new Error("Only admins can save map screenshots");
    }
    
    // Add metadata
    const screenshotWithMeta = {
      ...screenshotData,
      createdBy: user.uid,
      createdAt: serverTimestamp()
    };
    
    // Save to Firestore
    const docRef = await addDoc(collection(db, 'mapScreenshots'), screenshotWithMeta);
    return docRef.id;
  } catch (error) {
    console.error("Error saving map screenshot:", error);
    throw error;
  }
};

/**
 * Get saved screenshots
 * 
 * @param {Object} db - Firestore database instance
 * @param {Object} options - Query options
 * @returns {Promise<Array>} List of screenshots
 */
export const getMapScreenshots = async (db, options = {}) => {
  const { limit: queryLimit = 20, userId = null } = options;
  
  try {
    let screenshotsQuery;
    
    if (userId) {
      // Get screenshots by specific user
      screenshotsQuery = query(
        collection(db, 'mapScreenshots'),
        where('createdBy', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(queryLimit)
      );
    } else {
      // Get all screenshots
      screenshotsQuery = query(
        collection(db, 'mapScreenshots'),
        orderBy('createdAt', 'desc'),
        limit(queryLimit)
      );
    }
    
    const snapshot = await getDocs(screenshotsQuery);
    const screenshots = [];
    
    snapshot.forEach(doc => {
      const data = doc.data();
      screenshots.push({
        id: doc.id,
        name: data.name,
        imageBase64: data.imageBase64,
        createdBy: data.createdBy,
        createdAt: data.createdAt ? new Date(data.createdAt.toDate()) : null,
        mapCenter: data.mapCenter,
        zoomLevel: data.zoomLevel,
        visibleLocations: data.visibleLocations
      });
    });
    
    return screenshots;
  } catch (error) {
    console.error("Error getting map screenshots:", error);
    throw error;
  }
};

/**
 * Permission Checking Services
 */

/**
 * Check if user is an admin
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} userId - User ID to check
 * @returns {Promise<boolean>} Admin status
 */
export const checkIfUserIsAdmin = async (db, userId) => {
  try {
    if (!userId) return false;
    
    const userDoc = await getDoc(doc(db, "userProfiles", userId));
    
    if (userDoc.exists() && userDoc.data().isAdmin) {
      return true;
    }
    
    return false;
  } catch (error) {
    console.error("Error checking admin status:", error);
    return false;
  }
};

/**
 * Check if user has a specific tag/role
 * 
 * @param {Object} db - Firestore database instance
 * @param {string} userId - User ID to check
 * @param {string} tagName - Tag name to check for
 * @returns {Promise<boolean>} Whether user has the tag
 */
export const checkUserHasTag = async (db, userId, tagName) => {
  try {
    if (!userId || !tagName) return false;
    
    const profileRef = doc(db, "userProfiles", userId);
    const profileDoc = await getDoc(profileRef);
    
    if (profileDoc.exists() && profileDoc.data().tags) {
      const tags = profileDoc.data().tags || [];
      return tags.includes(tagName);
    }
    
    return false;
  } catch (error) {
    console.error("Error checking user tag:", error);
    return false;
  }
};

/**
 * Utility Functions
 */

/**
 * Calculate distance between two points in meters
 * 
 * @param {Object} point1 - First point with lat/lng
 * @param {Object} point2 - Second point with lat/lng
 * @returns {number} Distance in meters
 */
export const calculateDistanceInMeters = (point1, point2) => {
  if (!point1 || !point2) return Infinity;
  
  const R = 6371000; // Earth's radius in meters
  const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
  const φ2 = point2.lat * Math.PI/180;
  const Δφ = (point2.lat-point1.lat) * Math.PI/180;
  const Δλ = (point2.lng-point1.lng) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c; // Distance in meters
};

/**
 * Calculate distance between two points in miles
 * 
 * @param {Object} point1 - First point with lat/lng
 * @param {Object} point2 - Second point with lat/lng
 * @returns {number} Distance in miles
 */
export const calculateDistanceInMiles = (point1, point2) => {
  const meters = calculateDistanceInMeters(point1, point2);
  return meters / 1609.34; // Convert meters to miles
};