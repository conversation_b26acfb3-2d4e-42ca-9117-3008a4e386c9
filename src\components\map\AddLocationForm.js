import React, { useContext, useState } from 'react';
import { MapContext } from '../MapContext';

const AddLocationForm = () => {
  const {
    currentUser,
    isAdmin,
    newMarkerPosition,
    setNewMarkerPosition,
    isAddingMarker,
    setIsAddingMarker,
    isAddingAdminMarker,
    setIsAddingAdminMarker,
    currentAddress,
    nearestIntersection,
    handleAddMarker,
    
    // Form field states
    newMarkerName,
    setNewMarkerName,
    newMarkerDetails,
    setNewMarkerDetails,
    newMarkerImages,
    setNewMarkerImages,
    newMarkerPriority,
    setNewMarkerPriority,
    newMarkerParkingSide,
    setNewMarkerParkingSide,
    newMarkerPlateNumber,
    setNewMarkerPlateNumber,
    newMarkerVIN,
    setNewMarkerVIN,
    newMarkerDriveType,
    setNewMarkerDriveType,
    newMarkerMake,
    setNewMarkerMake,
    newMarkerModel,
    setNewMarkerModel,
    newMarkerYear,
    setNewMarkerYear,
    newMarkerCase,
    setNewMarkerCase,
    newMarkerMileage,
    setNewMarkerMileage,
    setError
  } = useContext(MapContext);

  // Handle file inputs for marker images
  const handleImageUpload = async (e) => {
    const files = Array.from(e.target.files);
    
    if (files.length > 0) {
      try {
        // Limit to max 3 images
        const filesToProcess = files.slice(0, 3 - newMarkerImages.length);
        
        // Process each file - you'll need to implement compressImageToBase64
        // or import it from your utils
        const processedImages = await Promise.all(
          filesToProcess.map(file => compressImageToBase64(file))
        );
        
        // Update state with new images
        setNewMarkerImages(prev => [...prev, ...processedImages].slice(0, 3));
      } catch (err) {
        console.error("Error processing images:", err);
        setError("Failed to process images. Please try again with smaller images.");
      }
    }
  };

  // Remove image from new marker
  const removeImage = (index) => {
    setNewMarkerImages(prev => prev.filter((_, i) => i !== index));
  };

  // Take a photo using the device camera
  const takePhoto = async () => {
    try {
      // Create a file input element programmatically
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.capture = 'environment'; // Use the rear camera on mobile devices
      
      // Add change event listener
      input.addEventListener('change', handleImageUpload);
      
      // Trigger the file picker
      input.click();
    } catch (err) {
      console.error("Error taking photo:", err);
      setError("Could not access camera. Please check permissions.");
    }
  };

  // Utility function placeholder - should be imported from utils
  const compressImageToBase64 = async (file) => {
    // Placeholder for image compression function
    // In a real implementation, this would compress the image to base64
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };

  // Function to cancel adding a marker
  const cancelAddMarker = () => {
    setIsAddingMarker(false);
    setIsAddingAdminMarker(false);
    setNewMarkerPosition(null);
    setNewMarkerName('');
    setNewMarkerDetails('');
    setNewMarkerImages([]);
    setNewMarkerPriority(false);
    setNewMarkerParkingSide(null);
    setNewMarkerPlateNumber('');
    setNewMarkerVIN('');
    setNewMarkerDriveType('');
    setNewMarkerMake('');
    setNewMarkerModel('');
    setNewMarkerYear('');
    setNewMarkerCase('');
    setNewMarkerMileage('');
  };

  // Whether this is an admin marker
  const isAdminMarkerMode = isAddingAdminMarker;

  return (
    <div className="absolute bottom-4 right-4 z-[1000] bg-gray-800 text-gray-200 rounded-lg shadow-lg p-3 sm:p-4 w-full max-w-[320px]">
      <div className="mb-2 text-xs text-gray-400 flex items-center">
        {isAdminMarkerMode && (
          <span className="bg-red-900 text-red-200 text-xs px-2 py-0.5 rounded mr-2">Admin</span>
        )}
        <span className="truncate">Add location</span>
      </div>
      
      {/* Image upload section */}
      <div className="mb-3">
        <div className="text-xs text-gray-400 mb-1">Images (max 3)</div>
        <div className="flex gap-2 mb-2">
          {newMarkerImages.length > 0 ? (
            newMarkerImages.map((img, index) => (
              <div key={index} className="relative w-16 h-16">
                <img src={img} alt={`Image ${index+1}`} className="w-full h-full object-cover rounded" />
                <button 
                  onClick={() => removeImage(index)}
                  className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs"
                >×</button>
              </div>
            ))
          ) : (
            <div className="w-16 h-16 bg-gray-700 rounded flex items-center justify-center text-gray-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          )}
          
          {newMarkerImages.length < 3 && (
            <div className="flex flex-col gap-1">
              <label className="cursor-pointer w-16 h-8 bg-blue-500 text-white rounded flex items-center justify-center text-xs">
                Upload
                <input 
                  type="file" 
                  accept="image/*" 
                  onChange={handleImageUpload} 
                  className="hidden" 
                  multiple={newMarkerImages.length < 2}
                />
              </label>
              <button 
                onClick={takePhoto}
                className="w-16 h-8 bg-green-500 text-white rounded flex items-center justify-center text-xs"
              >
                Camera
              </button>
            </div>
          )}
        </div>
      </div>
      
      {/* Location name */}
      <input
        type="text"
        value={newMarkerName}
        onChange={(e) => setNewMarkerName(e.target.value)}
        placeholder="Location name *"
        className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded mb-2 text-sm"
        autoFocus
      />
      
      {/* Vehicle Information Fields */}
      <div className="grid grid-cols-2 gap-2 mb-2">
        <input
          type="text"
          value={newMarkerPlateNumber}
          onChange={(e) => setNewMarkerPlateNumber(e.target.value)}
          placeholder="License Plate # *"
          className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
        />
        
        <input
          type="text"
          value={newMarkerVIN}
          onChange={(e) => setNewMarkerVIN(e.target.value)}
          placeholder="VIN # *"
          className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
        />
      </div>
      
      <div className="grid grid-cols-3 gap-2 mb-2">
        <input
          type="text"
          value={newMarkerMake}
          onChange={(e) => setNewMarkerMake(e.target.value)}
          placeholder="Make *"
          className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
        />
        
        <input
          type="text"
          value={newMarkerModel}
          onChange={(e) => setNewMarkerModel(e.target.value)}
          placeholder="Model *"
          className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
        />
        
        <input
          type="text"
          value={newMarkerYear}
          onChange={(e) => setNewMarkerYear(e.target.value)}
          placeholder="Year *"
          className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
        />
      </div>
      
      <div className="grid grid-cols-2 gap-2 mb-2">
        <input
          type="text"
          value={newMarkerDriveType}
          onChange={(e) => setNewMarkerDriveType(e.target.value)}
          placeholder="Drive Type *"
          className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
        />
        
        <input
          type="text"
          value={newMarkerCase}
          onChange={(e) => setNewMarkerCase(e.target.value)}
          placeholder="Case # (optional)"
          className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded text-sm"
        />
      </div>
      
      <textarea
        value={newMarkerDetails}
        onChange={(e) => setNewMarkerDetails(e.target.value)}
        placeholder="Additional details (optional)"
        className="w-full px-3 py-2 border border-gray-600 bg-gray-700 text-gray-200 rounded mb-2 text-sm h-20"
      ></textarea>
      
      {/* Parking side selection */}
      <div className="mb-2">
        <div className="text-xs text-gray-400 mb-1">Vehicle Parking Side</div>
        <div className="flex gap-2">
          <button
            onClick={() => setNewMarkerParkingSide('left')}
            className={`px-2 py-1 rounded text-xs flex items-center gap-1 ${newMarkerParkingSide === 'left' ? 'bg-red-500 text-white' : 'bg-gray-700'}`}
          >
            ← Left Side
          </button>
          <button
            onClick={() => setNewMarkerParkingSide(null)}
            className={`px-2 py-1 rounded text-xs ${newMarkerParkingSide === null ? 'bg-blue-500 text-white' : 'bg-gray-700'}`}
          >
            None
          </button>
          <button
            onClick={() => setNewMarkerParkingSide('right')}
            className={`px-2 py-1 rounded text-xs flex items-center gap-1 ${newMarkerParkingSide === 'right' ? 'bg-red-500 text-white' : 'bg-gray-700'}`}
          >
            Right Side →
          </button>
        </div>
      </div>
      
      {/* Priority checkbox */}
      <div className="flex items-center mb-3">
        <input
          type="checkbox"
          id="priority-checkbox"
          checked={newMarkerPriority}
          onChange={(e) => setNewMarkerPriority(e.target.checked)}
          className="mr-2"
        />
        <label htmlFor="priority-checkbox" className="text-xs text-gray-300">
          Mark as priority (animated)
        </label>
      </div>
      
      <div className="flex space-x-2">
        <button
          onClick={handleAddMarker}
          disabled={
            !newMarkerName.trim() ||
            !newMarkerPlateNumber.trim() ||
            !newMarkerVIN.trim() ||
            !newMarkerMake.trim() ||
            !newMarkerModel.trim() ||
            !newMarkerDriveType.trim() ||
            !newMarkerYear.trim() ||
            !currentUser
          }
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50 flex-1 text-sm"
        >
          Save
        </button>
        <button
          onClick={cancelAddMarker}
          className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded flex-1 text-sm"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default AddLocationForm;