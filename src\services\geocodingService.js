/**
 * Geocoding service for converting between coordinates and addresses
 * Uses OpenStreetMap's Nominatim service for geocoding with fallback options
 */

// Rate limiting - recommended by Nominatim is 1 request per second
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 1000; // 1 second in ms

// Request timeout
const REQUEST_TIMEOUT = 5000; // 5 seconds

// Cache for geocoding results to reduce API calls
const geocodingCache = new Map();
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 hours

/**
 * Fetch with timeout to prevent long-hanging requests
 * 
 * @param {string} url - URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise} Promise that resolves to fetch response or rejects with timeout
 */
const fetchWithTimeout = async (url, options = {}) => {
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    clearTimeout(id);
    return response;
  } catch (error) {
    clearTimeout(id);
    throw error;
  }
};

/**
 * Generate a cache key for the position
 * 
 * @param {Object} position - Position object with lat/lng properties
 * @returns {string} Cache key
 */
const getCacheKey = (position) => {
  // Round coordinates to 4 decimal places for caching (approximately 11 meters precision)
  const lat = parseFloat(position.lat).toFixed(4);
  const lng = parseFloat(position.lng).toFixed(4);
  return `${lat},${lng}`;
};

/**
 * Get address information from coordinates using Nominatim
 * 
 * @param {Object} position - Position object with lat/lng properties
 * @returns {Promise} Promise that resolves to address data
 */
export const getAddressFromCoordinates = async (position) => {
  try {
    // Check cache first
    const cacheKey = getCacheKey(position);
    if (geocodingCache.has(cacheKey)) {
      const cacheEntry = geocodingCache.get(cacheKey);
      
      // Check if the cache entry is still valid
      if (Date.now() - cacheEntry.timestamp < CACHE_EXPIRATION) {
        console.log('Using cached geocoding result');
        return cacheEntry.data;
      } else {
        // Remove expired cache entry
        geocodingCache.delete(cacheKey);
      }
    }
    
    // Implement basic rate limiting
    const now = Date.now();
    const timeToWait = Math.max(0, MIN_REQUEST_INTERVAL - (now - lastRequestTime));
    
    if (timeToWait > 0) {
      await new Promise(resolve => setTimeout(resolve, timeToWait));
    }
    
    lastRequestTime = Date.now();
    
    // Try to make the request to Nominatim with timeout
    try {
      const response = await fetchWithTimeout(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.lat}&lon=${position.lng}&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'MyMapApp/1.0' // Nominatim requires a user agent
          }
        }
      );
      
      if (!response.ok) {
        throw new Error(`Failed to fetch address: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Extract streets for intersection calculation
      const streets = getStreetsFromNominatimResponse(data);
      
      const result = {
        formatted_address: data.display_name || '',
        raw: data,
        streets: streets,
        addressComponents: data.address || {}
      };
      
      // Cache the result
      geocodingCache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });
      
      return result;
    } catch (networkError) {
      console.warn('Nominatim service unavailable, using fallback:', networkError);
      // Use fallback method (local calculation)
      return useFallbackGeocoding(position);
    }
  } catch (error) {
    console.error('Error fetching address:', error);
    return useFallbackGeocoding(position);
  }
};

/**
 * Fallback geocoding when online services are unavailable
 * 
 * @param {Object} position - Position object with lat/lng properties
 * @returns {Object} Basic address info with coordinates
 */
const useFallbackGeocoding = (position) => {
  // Format coordinates nicely
  const lat = parseFloat(position.lat).toFixed(6);
  const lng = parseFloat(position.lng).toFixed(6);
  
  return { 
    formatted_address: `Location: ${lat}, ${lng}`, 
    streets: [],
    addressComponents: {},
    isOfflineMode: true,
    coordinates: {
      lat: parseFloat(lat),
      lng: parseFloat(lng)
    }
  };
};

/**
 * Extract street names from Nominatim response
 * 
 * @param {Object} data - Nominatim response data
 * @returns {Array} Array of street names
 */
export const getStreetsFromNominatimResponse = (data) => {
  const streets = [];
  
  if (!data || !data.address) return streets;
  
  // Try to get road
  if (data.address.road) {
    streets.push(data.address.road);
  }
  
  // Try to get other road types
  const possibleRoads = [
    'pedestrian', 'footway', 'path', 'street', 'residential',
    'avenue', 'boulevard', 'drive', 'highway', 'lane'
  ];
  
  for (const roadType of possibleRoads) {
    if (data.address[roadType] && !streets.includes(data.address[roadType])) {
      streets.push(data.address[roadType]);
    }
  }
  
  return streets;
};

/**
 * Get full address and intersection information for a position
 * 
 * @param {Object} position - Position object with lat/lng properties
 * @returns {Promise} Promise that resolves to object with address and intersection
 */
export const getAddressAndIntersection = async (position) => {
  try {
    const addressData = await getAddressFromCoordinates(position);
    
    // Generate intersection text if we have multiple streets
    let intersectionText = '';
    if (addressData.streets && addressData.streets.length >= 2) {
      intersectionText = `${addressData.streets[0]} & ${addressData.streets[1]}`;
    } else if (addressData.streets && addressData.streets.length === 1) {
      intersectionText = `Near ${addressData.streets[0]}`;
    } else if (addressData.isOfflineMode) {
      // In offline mode, we don't have street data
      intersectionText = 'Street data unavailable';
    }
    
    return {
      address: addressData.formatted_address,
      intersection: intersectionText,
      streets: addressData.streets || [],
      addressComponents: addressData.addressComponents || {},
      isOfflineMode: addressData.isOfflineMode || false
    };
  } catch (error) {
    console.error('Error getting address and intersection:', error);
    return {
      address: `Location: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`,
      intersection: 'Unable to determine',
      streets: [],
      addressComponents: {},
      error: error.message,
      isOfflineMode: true
    };
  }
};

/**
 * Search for addresses by text query with fallback
 * 
 * @param {string} query - Address or location to search for
 * @param {Object} options - Optional parameters like limit, countrycodes, etc.
 * @returns {Promise} Promise that resolves to search results
 */
export const searchAddressByText = async (query, options = {}) => {
  if (!query || query.trim() === '') {
    return [];
  }
  
  try {
    // Implement basic rate limiting
    const now = Date.now();
    const timeToWait = Math.max(0, MIN_REQUEST_INTERVAL - (now - lastRequestTime));
    
    if (timeToWait > 0) {
      await new Promise(resolve => setTimeout(resolve, timeToWait));
    }
    
    lastRequestTime = Date.now();
    
    // Build query parameters
    const params = new URLSearchParams({
      format: 'json',
      q: query,
      limit: options.limit || 5,
      addressdetails: 1
    });
    
    if (options.countrycodes) {
      params.append('countrycodes', options.countrycodes);
    }
    
    if (options.viewbox) {
      params.append('viewbox', options.viewbox);
      params.append('bounded', options.bounded ? 1 : 0);
    }
    
    // Make the request to Nominatim with timeout
    try {
      const response = await fetchWithTimeout(
        `https://nominatim.openstreetmap.org/search?${params.toString()}`,
        {
          headers: {
            'User-Agent': 'MyMapApp/1.0' // Nominatim requires a user agent
          }
        }
      );
      
      if (!response.ok) {
        throw new Error(`Failed to search address: ${response.status} ${response.statusText}`);
      }
      
      const results = await response.json();
      
      // Transform results to a more usable format
      return results.map(item => ({
        display_name: item.display_name,
        lat: parseFloat(item.lat),
        lng: parseFloat(item.lon),
        type: item.type,
        importance: item.importance,
        addressComponents: item.address || {}
      }));
    } catch (networkError) {
      console.warn('Nominatim search service unavailable:', networkError);
      // Return empty result if can't access the service
      return [];
    }
  } catch (error) {
    console.error('Error searching address:', error);
    return [];
  }
};

/**
 * Get a simplified readable location description
 * 
 * @param {Object} position - Position object with lat/lng properties
 * @returns {Promise} Promise that resolves to a simplified location description
 */
export const getSimplifiedLocation = async (position) => {
  try {
    const addressData = await getAddressFromCoordinates(position);
    
    if (addressData.isOfflineMode) {
      return `Location at ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`;
    }
    
    if (!addressData.addressComponents) {
      return "Unknown location";
    }
    
    const components = addressData.addressComponents;
    
    // Try to create a simplified description like "Main St, New York"
    let locationParts = [];
    
    // Add road name if available
    if (components.road) {
      locationParts.push(components.road);
    } else if (components.pedestrian) {
      locationParts.push(components.pedestrian);
    }
    
    // Add city or town
    if (components.city) {
      locationParts.push(components.city);
    } else if (components.town) {
      locationParts.push(components.town);
    } else if (components.village) {
      locationParts.push(components.village);
    } else if (components.suburb) {
      locationParts.push(components.suburb);
    }
    
    // If we have nothing, add county and state as fallback
    if (locationParts.length === 0) {
      if (components.county) {
        locationParts.push(components.county);
      }
      
      if (components.state) {
        locationParts.push(components.state);
      }
    }
    
    return locationParts.length > 0 
      ? locationParts.join(', ')
      : addressData.formatted_address || "Unknown location";
    
  } catch (error) {
    console.error('Error getting simplified location:', error);
    return `Location at ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`;
  }
};

export default {
  getAddressFromCoordinates,
  getStreetsFromNominatimResponse,
  getAddressAndIntersection,
  searchAddressByText,
  getSimplifiedLocation
};