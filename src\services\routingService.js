import L from 'leaflet';
import 'leaflet-routing-machine';

/**
 * Routing service that handles route calculations, navigation, direction calculations
 * and fallback routing when the main service fails.
 */

/**
 * Calculate distance between two points in miles
 * 
 * @param {Object} point1 - First point with lat/lng properties
 * @param {Object} point2 - Second point with lat/lng properties
 * @returns {number} Distance in miles
 */
export const calculateDistance = (point1, point2) => {
  const R = 3958.8; // Earth's radius in MILES
  const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
  const φ2 = point2.lat * Math.PI/180;
  const Δφ = (point2.lat-point1.lat) * Math.PI/180;
  const Δλ = (point2.lng-point1.lng) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  const d = R * c; // in MILES
  return d;
};

/**
 * Format distance in human-readable form
 * 
 * @param {number} miles - Distance in miles
 * @returns {string} Formatted distance string
 */
export const formatDistance = (miles) => {
  if (miles < 0.1) {
    return `${Math.round(miles * 5280)} feet`;
  } else {
    return `${miles.toFixed(1)} miles`;
  }
};

/**
 * Format time in human-readable form
 * 
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time string
 */
export const formatTime = (seconds) => {
  if (seconds < 60) {
    return `${Math.round(seconds)} seconds`;
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)} minutes`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
};

/**
 * Calculate bearing between two points (in degrees)
 * 
 * @param {Object} start - Start point with lat/lng properties
 * @param {Object} end - End point with lat/lng properties
 * @returns {number} Bearing in degrees (0-360)
 */
export const calculateBearing = (start, end) => {
  const startLat = start.lat * Math.PI / 180;
  const startLng = start.lng * Math.PI / 180;
  const endLat = end.lat * Math.PI / 180;
  const endLng = end.lng * Math.PI / 180;

  const y = Math.sin(endLng - startLng) * Math.cos(endLat);
  const x = Math.cos(startLat) * Math.sin(endLat) -
            Math.sin(startLat) * Math.cos(endLat) * Math.cos(endLng - startLng);
  
  const bearing = Math.atan2(y, x) * 180 / Math.PI;
  return (bearing + 360) % 360; // Normalize to 0-360
};

/**
 * Get direction text from bearing
 * 
 * @param {number} bearing - Bearing in degrees
 * @returns {string} Cardinal direction name
 */
export const getDirectionText = (bearing) => {
  const directions = ['North', 'Northeast', 'East', 'Southeast', 'South', 'Southwest', 'West', 'Northwest'];
  const index = Math.round(bearing / 45) % 8;
  return directions[index];
};

/**
 * Get direction arrow emoji based on bearing
 * 
 * @param {number} bearing - Bearing in degrees
 * @returns {string} Arrow emoji representing the direction
 */
export const getDirectionArrow = (bearing) => {
  // Map bearing to one of 8 cardinal directions with corresponding arrows
  const directions = [
    { min: 337.5, max: 360, arrow: '⬆️' }, // North
    { min: 0, max: 22.5, arrow: '⬆️' },    // North
    { min: 22.5, max: 67.5, arrow: '↗️' },  // Northeast
    { min: 67.5, max: 112.5, arrow: '➡️' }, // East
    { min: 112.5, max: 157.5, arrow: '↘️' }, // Southeast
    { min: 157.5, max: 202.5, arrow: '⬇️' }, // South
    { min: 202.5, max: 247.5, arrow: '↙️' }, // Southwest
    { min: 247.5, max: 292.5, arrow: '⬅️' }, // West
    { min: 292.5, max: 337.5, arrow: '↖️' }  // Northwest
  ];
  
  // Find the direction that matches the bearing
  for (const dir of directions) {
    if ((bearing >= dir.min && bearing < dir.max) || 
        (dir.min > dir.max && (bearing >= dir.min || bearing < dir.max))) {
      return dir.arrow;
    }
  }
  
  return '⬆️'; // Default to North if something goes wrong
};

/**
 * Find the closest location from a list of locations to a specific point
 * 
 * @param {Object} fromPosition - Reference position with lat/lng properties
 * @param {Array} locationsList - List of locations to check
 * @returns {Object|null} Object containing closest location and distance, or null if list is empty
 */
export const findClosestLocation = (fromPosition, locationsList) => {
  if (!locationsList || locationsList.length === 0) return null;
  
  let closestLocation = null;
  let shortestDistance = Infinity;
  
  for (const location of locationsList) {
    const distance = calculateDistance(fromPosition, location.position);
    if (distance < shortestDistance) {
      shortestDistance = distance;
      closestLocation = location;
    }
  }
  
  return { location: closestLocation, distance: shortestDistance };
};

/**
 * Optimize route by finding the shortest path through all locations
 * 
 * @param {Object} startPosition - Starting position with lat/lng properties
 * @param {Array} locationsList - List of locations to visit
 * @returns {Array} Optimized list of locations with distance from previous point
 */
export const optimizeRoute = (startPosition, locationsList) => {
  if (!locationsList || locationsList.length === 0) return [];
  
  const unvisitedLocations = [...locationsList];
  const optimizedRoute = [];
  let currentPosition = startPosition;
  
  while (unvisitedLocations.length > 0) {
    const { location: closestLocation, distance } = findClosestLocation(currentPosition, unvisitedLocations);
    
    if (closestLocation) {
      optimizedRoute.push({
        location: closestLocation,
        distanceFromPrevious: distance
      });
      
      const index = unvisitedLocations.findIndex(loc => loc.id === closestLocation.id);
      if (index !== -1) {
        unvisitedLocations.splice(index, 1);
      }
      currentPosition = closestLocation.position;
    }
  }
  
  return optimizedRoute;
};

/**
 * Calculate a route using OSRM routing service
 * 
 * @param {Object} origin - Starting point with lat/lng properties
 * @param {Object} destination - End point with lat/lng properties
 * @param {Object} options - Additional options
 * @returns {Promise} Promise that resolves with route information
 */
export const calculateRoute = (origin, destination, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      // Create temporary routing control that won't be added to map
      const routingControl = L.Routing.control({
        waypoints: [
          L.latLng(origin.lat, origin.lng),
          L.latLng(destination.lat, destination.lng)
        ],
        routeWhileDragging: false,
        router: L.Routing.osrmv1({
          serviceUrl: 'https://router.project-osrm.org/route/v1',
          profile: 'driving'
        }),
        createMarker: function() { return null; },
        addWaypoints: false,
        fitSelectedRoutes: false,
        show: false
      });
      
      // Set up listeners
      let resolved = false;
      
      // Handle route found
      routingControl.on('routesfound', function(e) {
        if (resolved) return;
        resolved = true;
        
        const routes = e.routes;
        const summary = routes[0].summary;
        const coordinates = routes[0].coordinates;
        const instructions = routes[0].instructions || [];
        
        resolve({
          success: true,
          route: {
            summary: {
              totalDistance: summary.totalDistance,
              totalTime: summary.totalTime
            },
            coordinates: coordinates.map(coord => ({ lat: coord.lat, lng: coord.lng })),
            instructions: instructions.map(instruction => ({
              text: instruction.text,
              distance: instruction.distance,
              time: instruction.time,
              type: instruction.type,
              direction: instruction.direction
            }))
          }
        });
      });
      
      // Handle errors
      routingControl.on('routingerror', function(e) {
        if (resolved) return;
        resolved = true;
        reject(new Error('Routing failed: ' + e.error.message));
      });
      
      // Force route calculation
      routingControl.route();
      
      // Set timeout for routing
      setTimeout(() => {
        if (!resolved) {
          resolved = true;
          reject(new Error('Routing timed out after 10 seconds'));
        }
      }, 10000);
      
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Get a direct route when routing service fails
 * 
 * @param {Object} origin - Starting point with lat/lng properties
 * @param {Object} destination - End point with lat/lng properties
 * @returns {Object} Direct route information
 */
export const getDirectRoute = (origin, destination) => {
  const distance = calculateDistance(origin, destination);
  const bearing = calculateBearing(origin, destination);
  const direction = getDirectionText(bearing);
  const directionArrow = getDirectionArrow(bearing);
  
  // Estimate travel time based on average speed of 40 mph
  const estimatedTimeSeconds = (distance / 40) * 3600;
  
  return {
    success: true,
    direct: true,
    route: {
      summary: {
        totalDistance: distance * 1609.34, // Convert to meters for consistency
        totalTime: estimatedTimeSeconds
      },
      coordinates: [
        { lat: origin.lat, lng: origin.lng },
        { lat: destination.lat, lng: destination.lng }
      ],
      bearing: bearing,
      direction: direction,
      directionArrow: directionArrow
    }
  };
};

/**
 * Try to calculate route with fallback to direct route
 * 
 * @param {Object} origin - Starting point with lat/lng properties
 * @param {Object} destination - End point with lat/lng properties
 * @param {Object} options - Additional options
 * @returns {Promise} Promise that resolves with route information
 */
export const calculateRouteWithFallback = async (origin, destination, options = {}) => {
  try {
    const routeResult = await calculateRoute(origin, destination, options);
    return routeResult;
  } catch (error) {
    console.warn('Routing service failed, falling back to direct route:', error);
    return getDirectRoute(origin, destination);
  }
};

export default {
  calculateDistance,
  formatDistance,
  formatTime,
  calculateBearing,
  getDirectionText,
  getDirectionArrow,
  findClosestLocation,
  optimizeRoute,
  calculateRoute,
  getDirectRoute,
  calculateRouteWithFallback
};