import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext.js';
import { 
  getFirestore, 
  collection, 
  addDoc, 
  getDocs, 
  getDoc,
  doc, 
  query, 
  orderBy, 
  where,
  serverTimestamp, 
  Timestamp,
  deleteDoc,
  setDoc
} from 'firebase/firestore';
import { 
  getStorage, 
  ref, 
  uploadBytes, 
  getDownloadURL,
  uploadBytesResumable
} from 'firebase/storage';

const Inspection = () => {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [viewMode, setViewMode] = useState('form'); // 'form' or 'list'
  const [inspections, setInspections] = useState([]);
  const [userProfile, setUserProfile] = useState(null);
  const [profilePhotoLoading, setProfilePhotoLoading] = useState(true);
  
  // Time tracking states
  const [isClockedIn, setIsClockedIn] = useState(false);
  const [clockInTime, setClockInTime] = useState(null);
  const [clockOutTime, setClockOutTime] = useState(null);
  const [timeCardLoading, setTimeCardLoading] = useState(true);
  
  // Inspection completion tracking for Map access
  const [hasCompletedInspection, setHasCompletedInspection] = useState(false);
  
  // Selected inspection for detailed view
  const [selectedInspection, setSelectedInspection] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  
  // Delete confirmation modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [inspectionToDelete, setInspectionToDelete] = useState(null);
  
  // Date filtering
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [filterActive, setFilterActive] = useState(false);

  // Form state
  const [carNumber, setCarNumber] = useState('');
  const [mileage, setMileage] = useState('');
  const [notes, setNotes] = useState('');
  const [needsService, setNeedsService] = useState(false);
  
  // Photo states with previews
  const [frontTirePhoto, setFrontTirePhoto] = useState(null);
  const [rightTirePhoto, setRightTirePhoto] = useState(null);
  const [leftTirePhoto, setLeftTirePhoto] = useState(null);
  const [backTirePhoto, setBackTirePhoto] = useState(null);
  const [interiorPhoto, setInteriorPhoto] = useState(null);
  const [frontExteriorPhoto, setFrontExteriorPhoto] = useState(null);
  const [backExteriorPhoto, setBackExteriorPhoto] = useState(null);
  const [leftExteriorPhoto, setLeftExteriorPhoto] = useState(null);
  const [rightExteriorPhoto, setRightExteriorPhoto] = useState(null);

  // Image preview states
  const [frontTirePreview, setFrontTirePreview] = useState(null);
  const [rightTirePreview, setRightTirePreview] = useState(null);
  const [leftTirePreview, setLeftTirePreview] = useState(null);
  const [backTirePreview, setBackTirePreview] = useState(null);
  const [interiorPreview, setInteriorPreview] = useState(null);
  const [frontExteriorPreview, setFrontExteriorPreview] = useState(null);
  const [backExteriorPreview, setBackExteriorPreview] = useState(null);
  const [leftExteriorPreview, setLeftExteriorPreview] = useState(null);
  const [rightExteriorPreview, setRightExteriorPreview] = useState(null);

  // File input refs for camera trigger
  const frontTireRef = useRef(null);
  const rightTireRef = useRef(null);
  const leftTireRef = useRef(null);
  const backTireRef = useRef(null);
  const interiorRef = useRef(null);
  const frontExteriorRef = useRef(null);
  const backExteriorRef = useRef(null);
  const leftExteriorRef = useRef(null);
  const rightExteriorRef = useRef(null);

  // Animation references for form sections
  const formSectionRefs = {
    vehicleInfo: useRef(null),
    tirePhotos: useRef(null),
    interiorPhoto: useRef(null),
    exteriorPhotos: useRef(null),
    notes: useRef(null)
  };

  // Update clock
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Check if user has completed an inspection today
  useEffect(() => {
    async function checkInspectionStatus() {
      if (!currentUser) return;
      
      try {
        const db = getFirestore();
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        const startTimestamp = Timestamp.fromDate(today);
        const endTimestamp = Timestamp.fromDate(tomorrow);
        
        const q = query(
          collection(db, 'inspections'),
          where('userId', '==', currentUser.uid),
          where('timestamp', '>=', startTimestamp),
          where('timestamp', '<', endTimestamp)
        );
        
        const querySnapshot = await getDocs(q);
        setHasCompletedInspection(!querySnapshot.empty);
        
        // Store the inspection status in localStorage for page refreshes
        localStorage.setItem('hasCompletedInspection', !querySnapshot.empty);
      } catch (error) {
        console.error("Error checking inspection status:", error);
      }
    }
    
    // Check from localStorage first for better UX
    const cachedStatus = localStorage.getItem('hasCompletedInspection');
    if (cachedStatus === 'true') {
      setHasCompletedInspection(true);
    } else {
      checkInspectionStatus();
    }
  }, [currentUser]);

  // Check if user is clocked in
  useEffect(() => {
    async function checkClockStatus() {
      if (!currentUser) return;
      setTimeCardLoading(true);
      
      try {
        const db = getFirestore();
        const timeCardRef = doc(db, 'timeCards', currentUser.uid);
        const timeCardDoc = await getDoc(timeCardRef);
        
        if (timeCardDoc.exists()) {
          const timeCardData = timeCardDoc.data();
          const today = new Date().toLocaleDateString();
          
          if (timeCardData.currentDay === today && timeCardData.clockedIn && !timeCardData.clockedOut) {
            // User is clocked in
            setIsClockedIn(true);
            setClockInTime(timeCardData.clockInTime.toDate());
          } else {
            setIsClockedIn(false);
          }
        } else {
          setIsClockedIn(false);
        }
      } catch (error) {
        console.error("Error checking clock status:", error);
        setIsClockedIn(false);
      } finally {
        setTimeCardLoading(false);
      }
    }
    
    checkClockStatus();
  }, [currentUser]);

  // Fetch user profile to get profile picture
  useEffect(() => {
    async function fetchUserProfile() {
      if (!currentUser) return;
      
      setProfilePhotoLoading(true);
      try {
        const db = getFirestore();
        const userProfileRef = doc(db, "userProfiles", currentUser.uid);
        const userProfileDoc = await getDoc(userProfileRef);
        
        if (userProfileDoc.exists()) {
          setUserProfile(userProfileDoc.data());
        }
      } catch (error) {
        console.error("Error fetching user profile:", error);
      } finally {
        setProfilePhotoLoading(false);
      }
    }
    
    fetchUserProfile();
  }, [currentUser]);

  // Load inspections for admin
  useEffect(() => {
    if (isAdmin && viewMode === 'list') {
      fetchInspections();
    }
  }, [isAdmin, viewMode, filterActive, startDate, endDate]);

  // Handle clock in/out
  const handleClockInOut = async () => {
    if (!currentUser) return;
    
    try {
      setTimeCardLoading(true);
      const db = getFirestore();
      const timeCardRef = doc(db, 'timeCards', currentUser.uid);
      const currentDate = new Date();
      const today = currentDate.toLocaleDateString();
      
      if (!isClockedIn) {
        // Clock in
        await setDoc(timeCardRef, {
          userId: currentUser.uid,
          userName: userProfile?.displayName || currentUser.displayName || currentUser.email,
          userPhotoBase64: userProfile?.photoBase64 || null,
          currentDay: today,
          clockedIn: true,
          clockedOut: false,
          clockInTime: Timestamp.fromDate(currentDate),
          clockOutTime: null,
          updatedAt: serverTimestamp()
        }, { merge: true });
        
        // Also add to history collection
        await addDoc(collection(db, 'timeCardHistory'), {
          userId: currentUser.uid,
          userName: userProfile?.displayName || currentUser.displayName || currentUser.email,
          userPhotoBase64: userProfile?.photoBase64 || null,
          date: today,
          type: 'clockIn',
          timestamp: Timestamp.fromDate(currentDate)
        });
        
        setIsClockedIn(true);
        setClockInTime(currentDate);
        setClockOutTime(null);
      } else {
        // Clock out
        await setDoc(timeCardRef, {
          clockedOut: true,
          clockOutTime: Timestamp.fromDate(currentDate),
          updatedAt: serverTimestamp()
        }, { merge: true });
        
        // Add to history collection
        await addDoc(collection(db, 'timeCardHistory'), {
          userId: currentUser.uid,
          userName: userProfile?.displayName || currentUser.displayName || currentUser.email,
          userPhotoBase64: userProfile?.photoBase64 || null,
          date: today,
          type: 'clockOut',
          timestamp: Timestamp.fromDate(currentDate)
        });
        
        setIsClockedIn(false);
        setClockOutTime(currentDate);
      }
    } catch (error) {
      console.error("Error updating time card:", error);
      setError("Failed to update time card. Please try again.");
    } finally {
      setTimeCardLoading(false);
    }
  };

  // Image compression function
  const compressImage = (file, maxWidth = 1200, maxHeight = 1200, quality = 0.8) => {
    return new Promise((resolve, reject) => {
      if (!file || !/^image\//.test(file.type)) {
        reject(new Error('Not a valid image file'));
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          // Calculate new dimensions
          let width = img.width;
          let height = img.height;
          
          if (width > maxWidth) {
            height = Math.round((height * maxWidth) / width);
            width = maxWidth;
          }
          
          if (height > maxHeight) {
            width = Math.round((width * maxHeight) / height);
            height = maxHeight;
          }
          
          // Create canvas and draw resized image
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);
          
          // Convert to blob with quality setting
          canvas.toBlob((blob) => {
            if (blob) {
              // Create a new file with the same name but compressed
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              });
              resolve(compressedFile);
            } else {
              reject(new Error('Canvas to Blob conversion failed'));
            }
          }, 'image/jpeg', quality);
        };
        
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = e.target.result;
      };
      
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    });
  };

  // Improved upload function with retry and progress
  const uploadImageWithRetry = async (file, path, maxRetries = 3) => {
    const storage = getStorage();
    const filename = `${Date.now()}_${path}`;
    const storageRef = ref(storage, `inspections/${currentUser.uid}/${filename}`);
    
    try {
      // Compress the image before upload
      const compressedFile = await compressImage(file);
      
      // Upload with progress tracking
      return new Promise((resolve, reject) => {
        const uploadTask = uploadBytesResumable(storageRef, compressedFile, {
          contentType: 'image/jpeg', // Force image/jpeg for all uploads
          customMetadata: {
            'uploaded-by': currentUser.uid,
            'upload-timestamp': new Date().toISOString()
          }
        });
        
        uploadTask.on('state_changed', 
          (snapshot) => {
            // Track progress
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            setUploadProgress(prev => ({
              ...prev,
              [path]: Math.round(progress)
            }));
          }, 
          (error) => {
            console.error(`Error uploading ${path}:`, error);
            reject(error);
          }, 
          async () => {
            // Upload completed successfully, get download URL
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              resolve(downloadURL);
            } catch (urlError) {
              console.error(`Error getting download URL for ${path}:`, urlError);
              reject(urlError);
            }
          }
        );
      });
    } catch (error) {
      console.error(`Error preparing ${path} for upload:`, error);
      
      // Implement retry logic
      if (maxRetries > 0) {
        console.log(`Retrying upload for ${path}, ${maxRetries} attempts left`);
        return uploadImageWithRetry(file, path, maxRetries - 1);
      }
      
      throw error;
    }
  };

  // Format date for display
  const formatDate = (date) => {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  };

  // Format time for display
  const formatTime = (date) => {
    const options = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };
    return date.toLocaleTimeString('en-US', options);
  };

  // Calculate elapsed time
  const getElapsedTime = (startTime) => {
    if (!startTime) return '--:--:--';
    
    const elapsed = new Date() - startTime;
    const hours = Math.floor(elapsed / (1000 * 60 * 60));
    const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Handle file change and create preview with animation
  const handlePhotoChange = (e, setPhoto, setPreview) => {
    const file = e.target.files[0];
    if (file) {
      setPhoto(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Fetch inspections with optional date filtering
  const fetchInspections = async () => {
    try {
      setLoading(true);
      const db = getFirestore();
      
      let q;
      
      if (filterActive && startDate && endDate) {
        // Convert string dates to Timestamp objects for Firestore query
        const startTimestamp = Timestamp.fromDate(new Date(startDate));
        const endTimestamp = Timestamp.fromDate(new Date(endDate + 'T23:59:59')); // End of the selected day
        
        q = query(
          collection(db, 'inspections'),
          where('timestamp', '>=', startTimestamp),
          where('timestamp', '<=', endTimestamp),
          orderBy('timestamp', 'desc')
        );
      } else {
        q = query(collection(db, 'inspections'), orderBy('timestamp', 'desc'));
      }
      
      const querySnapshot = await getDocs(q);
      
      const inspectionsData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate()
      }));
      
      setInspections(inspectionsData);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching inspections:", error);
      setError("Failed to load inspection data");
      setLoading(false);
    }
  };

  // Delete inspection
  const deleteInspection = async () => {
    if (!inspectionToDelete) return;
    
    try {
      setLoading(true);
      const db = getFirestore();
      await deleteDoc(doc(db, 'inspections', inspectionToDelete.id));
      
      // Update the local state to remove the deleted inspection
      setInspections(prevInspections => 
        prevInspections.filter(inspection => inspection.id !== inspectionToDelete.id)
      );
      
      setShowDeleteModal(false);
      setInspectionToDelete(null);
      setLoading(false);
    } catch (error) {
      console.error("Error deleting inspection:", error);
      setError("Failed to delete inspection");
      setLoading(false);
    }
  };

  // Show delete confirmation modal
  const handleDeleteClick = (e, inspection) => {
    e.stopPropagation();
    setInspectionToDelete(inspection);
    setShowDeleteModal(true);
  };

  // Close delete confirmation modal
  const closeDeleteModal = () => {
    setShowDeleteModal(false);
    setInspectionToDelete(null);
  };

  // Apply date filter
  const applyDateFilter = () => {
    if (startDate && endDate) {
      setFilterActive(true);
    } else {
      setError("Please select both start and end dates");
    }
  };

  // Clear date filter
  const clearDateFilter = () => {
    setStartDate('');
    setEndDate('');
    setFilterActive(false);
  };

  // View inspection details
  const viewInspectionDetails = (inspection) => {
    setSelectedInspection(inspection);
    setShowDetailsModal(true);
  };

  // Close inspection details modal
  const closeDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedInspection(null);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate clock in status
    if (!isClockedIn) {
      setError("You must be clocked in to submit an inspection");
      return;
    }
    
    // Validate form
    if (!carNumber || !mileage || !frontTirePhoto || !rightTirePhoto || 
        !leftTirePhoto || !backTirePhoto || !interiorPhoto || 
        !frontExteriorPhoto || !backExteriorPhoto || 
        !leftExteriorPhoto || !rightExteriorPhoto) {
      setError("Please fill all fields and upload all required photos");
      return;
    }
    
    setLoading(true);
    setError('');
    setUploadProgress({});
    
    try {
      const db = getFirestore();
      
      // Upload all photos with progress indicators and better error handling
      const photoUploads = [
        uploadImageWithRetry(frontTirePhoto, 'front_tire'),
        uploadImageWithRetry(rightTirePhoto, 'right_tire'),
        uploadImageWithRetry(leftTirePhoto, 'left_tire'),
        uploadImageWithRetry(backTirePhoto, 'back_tire'),
        uploadImageWithRetry(interiorPhoto, 'interior'),
        uploadImageWithRetry(frontExteriorPhoto, 'front_exterior'),
        uploadImageWithRetry(backExteriorPhoto, 'back_exterior'),
        uploadImageWithRetry(leftExteriorPhoto, 'left_exterior'),
        uploadImageWithRetry(rightExteriorPhoto, 'right_exterior')
      ];
      
      // Wait for all uploads to complete
      const [
        frontTireURL,
        rightTireURL,
        leftTireURL,
        backTireURL,
        interiorURL,
        frontExteriorURL,
        backExteriorURL,
        leftExteriorURL,
        rightExteriorURL
      ] = await Promise.all(photoUploads);
      
      // Add inspection document
      await addDoc(collection(db, 'inspections'), {
        carNumber,
        mileage: parseInt(mileage),
        notes,
        needsService,
        photos: {
          frontTire: frontTireURL,
          rightTire: rightTireURL,
          leftTire: leftTireURL,
          backTire: backTireURL,
          interior: interiorURL,
          frontExterior: frontExteriorURL,
          backExterior: backExteriorURL,
          leftExterior: leftExteriorURL,
          rightExterior: rightExteriorURL
        },
        userId: currentUser.uid,
        userName: userProfile?.displayName || currentUser.displayName || currentUser.email,
        userPhotoBase64: userProfile?.photoBase64 || null, // Include user's profile photo
        timestamp: serverTimestamp(),
        inspectionDate: new Date().toISOString(),
        userJobTitle: userProfile?.jobTitle || '',
        userVehicle: userProfile?.vehicle || ''
      });
      
      // Mark that user has completed inspection today
      setHasCompletedInspection(true);
      localStorage.setItem('hasCompletedInspection', 'true');
      
      setSuccess(true);
      // Reset form
      resetForm();
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (error) {
      console.error("Error submitting inspection:", error);
      setError("Failed to submit inspection: " + (error.message || "Please try again."));
    } finally {
      setLoading(false);
      setUploadProgress({});
    }
  };
  
  // Reset form fields and previews
  const resetForm = () => {
    setCarNumber('');
    setMileage('');
    setNotes('');
    setNeedsService(false);
    setFrontTirePhoto(null);
    setRightTirePhoto(null);
    setLeftTirePhoto(null);
    setBackTirePhoto(null);
    setInteriorPhoto(null);
    setFrontExteriorPhoto(null);
    setBackExteriorPhoto(null);
    setLeftExteriorPhoto(null);
    setRightExteriorPhoto(null);
    setFrontTirePreview(null);
    setRightTirePreview(null);
    setLeftTirePreview(null);
    setBackTirePreview(null);
    setInteriorPreview(null);
    setFrontExteriorPreview(null);
    setBackExteriorPreview(null);
    setLeftExteriorPreview(null);
    setRightExteriorPreview(null);
  };

  // Format inspection date for display
  const formatInspectionDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric', 
      hour: '2-digit', 
      minute: '2-digit'
    });
  };

  // Create upload progress indicator
  const UploadProgressIndicator = ({ progress }) => {
    if (!progress || progress === 100) return null;
    
    return (
      <div className="w-full bg-gray-700 rounded-full h-2.5 my-2">
        <div 
          className="bg-green-600 h-2.5 rounded-full" 
          style={{ width: `${progress}%` }}
        ></div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 shadow-md border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">
              Vehicle Inspection
            </h1>
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
      
      {/* Time Tracking Banner */}
      <div className="bg-gradient-to-r from-gray-900 to-gray-800 shadow-md border-b border-gray-700 py-3">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <div className="flex items-center mb-3 sm:mb-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-300">{formatDate(currentTime)} {formatTime(currentTime)}</span>
            </div>
            
            <div className="flex items-center space-x-4">
              {isClockedIn && (
                <div className="bg-gray-800 rounded-lg px-3 py-1 flex items-center border border-blue-900">
                  <span className="text-blue-400 text-sm mr-2">Clocked in for:</span>
                  <span className="text-blue-300 font-mono">{getElapsedTime(clockInTime)}</span>
                </div>
              )}
              
              <button
                onClick={handleClockInOut}
                disabled={timeCardLoading}
                className={`px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 flex items-center ${
                  isClockedIn
                    ? 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600'
                    : 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600'
                }`}
              >
                {timeCardLoading ? (
                  <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    {isClockedIn ? (
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                    ) : (
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clipRule="evenodd" />
                    )}
                  </svg>
                )}
                {isClockedIn ? 'Clock Out' : 'Clock In'}
              </button>
              
              <button
                onClick={() => navigate('/timecard')}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105"
              >
                View Time Cards
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* User Info Banner */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-700 shadow-md mb-6 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div className="flex items-center mb-2 sm:mb-0">
              {profilePhotoLoading ? (
                <div className="h-8 w-8 bg-gray-700 rounded-full animate-pulse mr-2"></div>
              ) : userProfile?.photoBase64 ? (
                <div className="h-8 w-8 rounded-full overflow-hidden mr-2 border border-blue-400 shadow-lg transform transition-all hover:scale-110">
                  <img 
                    src={userProfile.photoBase64} 
                    alt="Profile" 
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              )}
              <span className="text-gray-300 text-sm sm:text-base truncate">
                {userProfile?.displayName || currentUser?.displayName || currentUser?.email || 'Not logged in'}
                {userProfile?.jobTitle && <span className="ml-2 text-xs text-blue-300">({userProfile.jobTitle})</span>}
              </span>
            </div>
            
            {hasCompletedInspection && (
              <div className="px-3 py-1 bg-green-800 bg-opacity-30 rounded-full border border-green-700 text-green-300 flex items-center text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Inspection Completed Today
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Admin toggle between form and list view */}
        {isAdmin && (
          <div className="mb-6 flex justify-center">
            <div className="bg-gray-800 rounded-lg p-1 inline-flex shadow-lg">
              <button
                onClick={() => setViewMode('form')}
                className={`px-4 py-2 rounded-md transition-all duration-300 ${
                  viewMode === 'form'
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-inner'
                    : 'text-gray-400 hover:bg-gray-700 hover:text-white'
                }`}
              >
                Submit Inspection
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-4 py-2 rounded-md transition-all duration-300 ${
                  viewMode === 'list'
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-inner'
                    : 'text-gray-400 hover:bg-gray-700 hover:text-white'
                }`}
              >
                View All Inspections
              </button>
            </div>
          </div>
        )}
        
        {/* Clock In Required Warning */}
        {!isClockedIn && viewMode === 'form' && (
          <div className="bg-yellow-900 bg-opacity-50 text-yellow-200 p-4 rounded-lg mb-6 border border-yellow-700 animate-pulse">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <p className="font-medium">You must be clocked in to submit an inspection</p>
            </div>
          </div>
        )}
        
        {/* Form View */}
        {viewMode === 'form' && (
          <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700 transform transition-all duration-500 hover:shadow-xl">
            <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-6">
              Vehicle Inspection Form
            </h2>
            
            {error && (
              <div className="bg-red-900 bg-opacity-50 text-red-200 p-4 rounded-lg mb-6 border border-red-700 animate-pulse">
                <p>{error}</p>
              </div>
            )}
            
            {success && (
              <div className="bg-green-900 bg-opacity-50 text-green-200 p-4 rounded-lg mb-6 border border-green-700 animate-bounce">
                <p>Inspection submitted successfully!</p>
              </div>
            )}

            {loading && Object.keys(uploadProgress).length > 0 && (
              <div className="bg-blue-900 bg-opacity-50 text-blue-200 p-4 rounded-lg mb-6 border border-blue-700">
                <p className="mb-2">Uploading images... Please wait.</p>
                <div className="space-y-2">
                  {Object.entries(uploadProgress).map(([key, progress]) => (
                    <div key={key} className="flex items-center">
                      <span className="w-24 text-xs">{key.replace('_', ' ')}:</span>
                      <div className="flex-grow h-2 mx-2 bg-gray-700 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-blue-500 rounded-full" 
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                      <span className="text-xs w-10 text-right">{progress}%</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Info Section */}
              <div 
                ref={formSectionRefs.vehicleInfo}
                className="bg-gray-900 bg-opacity-60 p-5 rounded-lg border border-gray-700 transform transition-all duration-300 hover:border-blue-500 hover:shadow-md hover:shadow-blue-900/20"
              >
                <h3 className="text-lg font-semibold text-blue-300 mb-4">Vehicle Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-400 mb-2">Vehicle Number</label>
                    <input
                      type="text"
                      className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                      placeholder="Enter vehicle number"
                      value={carNumber}
                      onChange={(e) => setCarNumber(e.target.value)}
                      required
                      disabled={!isClockedIn || loading}
                    />
                  </div>
                  <div>
                    <label className="block text-gray-400 mb-2">Mileage (miles)</label>
                    <input
                      type="number"
                      className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                      placeholder="Enter current mileage"
                      value={mileage}
                      onChange={(e) => setMileage(e.target.value)}
                      required
                      disabled={!isClockedIn || loading}
                    />
                  </div>
                </div>
                
                {/* Needs Immediate Service Checkbox */}
                <div className="mt-4">
                  <div className="flex items-center">
                    <input
                      id="needs-service"
                      type="checkbox"
                      className="h-5 w-5 text-red-600 rounded border-gray-700 focus:ring-red-500 bg-gray-800"
                      checked={needsService}
                      onChange={(e) => setNeedsService(e.target.checked)}
                      disabled={!isClockedIn || loading}
                    />
                    <label htmlFor="needs-service" className="ml-2 text-gray-300 flex items-center">
                      <span className="mr-2">Needs Immediate Service</span>
                      <span className="inline-flex h-3 w-3">
                        <span className={`animate-ping absolute inline-flex h-3 w-3 rounded-full ${needsService ? 'bg-red-400' : 'bg-gray-500'} opacity-75`}></span>
                        <span className={`relative inline-flex rounded-full h-3 w-3 ${needsService ? 'bg-red-500' : 'bg-gray-600'}`}></span>
                      </span>
                    </label>
                  </div>
                  {needsService && (
                    <p className="text-red-400 text-sm mt-1">
                      Flag this vehicle for immediate maintenance attention
                    </p>
                  )}
                </div>
              </div>
              
              {/* Tire Photos Section */}
              <div 
                ref={formSectionRefs.tirePhotos}
                className="bg-gray-900 bg-opacity-60 p-5 rounded-lg border border-gray-700 transform transition-all duration-300 hover:border-blue-500 hover:shadow-md hover:shadow-blue-900/20"
              >
                <h3 className="text-lg font-semibold text-blue-300 mb-4">Tire Photos</h3>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                  {/* Front Tire */}
                  <div className="flex flex-col items-center transform transition duration-300 hover:scale-105">
                    <div className="w-full h-44 bg-gray-800 rounded-lg mb-2 flex items-center justify-center overflow-hidden border border-gray-700 transition-all hover:border-blue-400">
                      {frontTirePreview ? (
                        <img src={frontTirePreview} alt="Front Tire" className="w-full h-full object-cover" />
                      ) : (
                        <div className="text-center p-4">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <p className="text-gray-500 text-sm">Front Tire</p>
                        </div>
                      )}
                    </div>
                    <input
                      ref={frontTireRef}
                      type="file"
                      accept="image/*"
                      capture="environment"
                      className="hidden"
                      onChange={(e) => handlePhotoChange(e, setFrontTirePhoto, setFrontTirePreview)}
                      required
                      disabled={!isClockedIn}
                    />
                    <button
                      type="button"
                      onClick={() => frontTireRef.current.click()}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-3 py-1 rounded-md text-sm shadow-md transition-all duration-300"
                      disabled={loading || !isClockedIn}
                    >
                      {frontTirePreview ? 'Change Photo' : 'Take Photo'}
                    </button>
                    <UploadProgressIndicator progress={uploadProgress['front_tire']} />
                  </div>
                  
                  {/* Right Tire */}
                  <div className="flex flex-col items-center transform transition duration-300 hover:scale-105">
                    <div className="w-full h-44 bg-gray-800 rounded-lg mb-2 flex items-center justify-center overflow-hidden border border-gray-700 transition-all hover:border-blue-400">
                      {rightTirePreview ? (
                        <img src={rightTirePreview} alt="Right Tire" className="w-full h-full object-cover" />
                      ) : (
                        <div className="text-center p-4">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <p className="text-gray-500 text-sm">Right Tire</p>
                        </div>
                      )}
                    </div>
                    <input
                      ref={rightTireRef}
                      type="file"
                      accept="image/*"
                      capture="environment"
                      className="hidden"
                      onChange={(e) => handlePhotoChange(e, setRightTirePhoto, setRightTirePreview)}
                      required
                      disabled={!isClockedIn}
                    />
                    <button
                      type="button"
                      onClick={() => rightTireRef.current.click()}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-3 py-1 rounded-md text-sm shadow-md transition-all duration-300"
                      disabled={loading || !isClockedIn}
                    >
                      {rightTirePreview ? 'Change Photo' : 'Take Photo'}
                    </button>
                    <UploadProgressIndicator progress={uploadProgress['right_tire']} />
                  </div>
                  
                  {/* Left Tire */}
                  <div className="flex flex-col items-center transform transition duration-300 hover:scale-105">
                    <div className="w-full h-44 bg-gray-800 rounded-lg mb-2 flex items-center justify-center overflow-hidden border border-gray-700 transition-all hover:border-blue-400">
                      {leftTirePreview ? (
                        <img src={leftTirePreview} alt="Left Tire" className="w-full h-full object-cover" />
                      ) : (
                        <div className="text-center p-4">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <p className="text-gray-500 text-sm">Left Tire</p>
                        </div>
                      )}
                    </div>
                    <input
                      ref={leftTireRef}
                      type="file"
                      accept="image/*"
                      capture="environment"
                      className="hidden"
                      onChange={(e) => handlePhotoChange(e, setLeftTirePhoto, setLeftTirePreview)}
                      required
                      disabled={!isClockedIn}
                    />
                    <button
                      type="button"
                      onClick={() => leftTireRef.current.click()}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-3 py-1 rounded-md text-sm shadow-md transition-all duration-300"
                      disabled={loading || !isClockedIn}
                    >
                      {leftTirePreview ? 'Change Photo' : 'Take Photo'}
                    </button>
                    <UploadProgressIndicator progress={uploadProgress['left_tire']} />
                  </div>
                  
                  {/* Back Tire */}
                  <div className="flex flex-col items-center transform transition duration-300 hover:scale-105">
                    <div className="w-full h-44 bg-gray-800 rounded-lg mb-2 flex items-center justify-center overflow-hidden border border-gray-700 transition-all hover:border-blue-400">
                      {backTirePreview ? (
                        <img src={backTirePreview} alt="Back Tire" className="w-full h-full object-cover" />
                      ) : (
                        <div className="text-center p-4">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <p className="text-gray-500 text-sm">Back Tire</p>
                        </div>
                      )}
                    </div>
                    <input
                      ref={backTireRef}
                      type="file"
                      accept="image/*"
                      capture="environment"
                      className="hidden"
                      onChange={(e) => handlePhotoChange(e, setBackTirePhoto, setBackTirePreview)}
                      required
                      disabled={!isClockedIn}
                    />
                    <button
                      type="button"
                      onClick={() => backTireRef.current.click()}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-3 py-1 rounded-md text-sm shadow-md transition-all duration-300"
                      disabled={loading || !isClockedIn}
                    >
                      {backTirePreview ? 'Change Photo' : 'Take Photo'}
                    </button>
                    <UploadProgressIndicator progress={uploadProgress['back_tire']} />
                  </div>
                </div>
              </div>
              
              {/* Interior Photo Section */}
              <div 
                ref={formSectionRefs.interiorPhoto}
                className="bg-gray-900 bg-opacity-60 p-5 rounded-lg border border-gray-700 transform transition-all duration-300 hover:border-blue-500 hover:shadow-md hover:shadow-blue-900/20"
              >
                <h3 className="text-lg font-semibold text-blue-300 mb-4">Interior Photo</h3>
                
                <div className="flex flex-col items-center">
                  <div className="w-full h-64 bg-gray-800 rounded-lg mb-3 flex items-center justify-center overflow-hidden border border-gray-700 transition-all hover:border-blue-400">
                    {interiorPreview ? (
                      <img src={interiorPreview} alt="Interior" className="w-full h-full object-cover transition-all duration-700 hover:scale-105" />
                    ) : (
                      <div className="text-center p-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <p className="text-gray-500">Take a photo of the interior</p>
                      </div>
                    )}
                  </div>
                  <input
                    ref={interiorRef}
                    type="file"
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={(e) => handlePhotoChange(e, setInteriorPhoto, setInteriorPreview)}
                    required
                    disabled={!isClockedIn}
                  />
                  <button
                    type="button"
                    onClick={() => interiorRef.current.click()}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105"
                    disabled={loading || !isClockedIn}
                  >
                    {interiorPreview ? 'Change Interior Photo' : 'Take Interior Photo'}
                  </button>
                  <UploadProgressIndicator progress={uploadProgress['interior']} />
                </div>
              </div>
              
              {/* Exterior Photos Section */}
              <div 
                ref={formSectionRefs.exteriorPhotos}
                className="bg-gray-900 bg-opacity-60 p-5 rounded-lg border border-gray-700 transform transition-all duration-300 hover:border-blue-500 hover:shadow-md hover:shadow-blue-900/20"
              >
                <h3 className="text-lg font-semibold text-blue-300 mb-4">Exterior Photos</h3>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                  {/* Front Exterior */}
                  <div className="flex flex-col items-center transform transition duration-300 hover:scale-105">
                    <div className="w-full h-48 bg-gray-800 rounded-lg mb-2 flex items-center justify-center overflow-hidden border border-gray-700 transition-all hover:border-blue-400">
                      {frontExteriorPreview ? (
                        <img src={frontExteriorPreview} alt="Front Exterior" className="w-full h-full object-cover" />
                      ) : (
                        <div className="text-center p-4">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <p className="text-gray-500">Front of Vehicle</p>
                        </div>
                      )}
                    </div>
                    <input
                      ref={frontExteriorRef}
                      type="file"
                      accept="image/*"
                      capture="environment"
                      className="hidden"
                      onChange={(e) => handlePhotoChange(e, setFrontExteriorPhoto, setFrontExteriorPreview)}
                      required
                      disabled={!isClockedIn}
                    />
                    <button
                      type="button"
                      onClick={() => frontExteriorRef.current.click()}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-3 py-1 rounded-md text-sm shadow-md transition-all duration-300"
                      disabled={loading || !isClockedIn}
                    >
                      {frontExteriorPreview ? 'Change Photo' : 'Take Photo'}
                    </button>
                    <UploadProgressIndicator progress={uploadProgress['front_exterior']} />
                  </div>
                  
                  {/* Back Exterior */}
                  <div className="flex flex-col items-center transform transition duration-300 hover:scale-105">
                    <div className="w-full h-48 bg-gray-800 rounded-lg mb-2 flex items-center justify-center overflow-hidden border border-gray-700 transition-all hover:border-blue-400">
                      {backExteriorPreview ? (
                        <img src={backExteriorPreview} alt="Back Exterior" className="w-full h-full object-cover" />
                      ) : (
                        <div className="text-center p-4">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <p className="text-gray-500">Back of Vehicle</p>
                        </div>
                      )}
                    </div>
                    <input
                      ref={backExteriorRef}
                      type="file"
                      accept="image/*"
                      capture="environment"
                      className="hidden"
                      onChange={(e) => handlePhotoChange(e, setBackExteriorPhoto, setBackExteriorPreview)}
                      required
                      disabled={!isClockedIn}
                    />
                    <button
                      type="button"
                      onClick={() => backExteriorRef.current.click()}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-3 py-1 rounded-md text-sm shadow-md transition-all duration-300"
                      disabled={loading || !isClockedIn}
                    >
                      {backExteriorPreview ? 'Change Photo' : 'Take Photo'}
                    </button>
                    <UploadProgressIndicator progress={uploadProgress['back_exterior']} />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {/* Left Side Exterior */}
                  <div className="flex flex-col items-center transform transition duration-300 hover:scale-105">
                    <div className="w-full h-48 bg-gray-800 rounded-lg mb-2 flex items-center justify-center overflow-hidden border border-gray-700 transition-all hover:border-blue-400">
                      {leftExteriorPreview ? (
                        <img src={leftExteriorPreview} alt="Left Exterior" className="w-full h-full object-cover" />
                      ) : (
                        <div className="text-center p-4">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <p className="text-gray-500">Left Side of Vehicle</p>
                        </div>
                      )}
                    </div>
                    <input
                      ref={leftExteriorRef}
                      type="file"
                      accept="image/*"
                      capture="environment"
                      className="hidden"
                      onChange={(e) => handlePhotoChange(e, setLeftExteriorPhoto, setLeftExteriorPreview)}
                      required
                      disabled={!isClockedIn}
                    />
                    <button
                      type="button"
                      onClick={() => leftExteriorRef.current.click()}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-3 py-1 rounded-md text-sm shadow-md transition-all duration-300"
                      disabled={loading || !isClockedIn}
                    >
                      {leftExteriorPreview ? 'Change Photo' : 'Take Photo'}
                    </button>
                    <UploadProgressIndicator progress={uploadProgress['left_exterior']} />
                  </div>
                  
                  {/* Right Side Exterior */}
                  <div className="flex flex-col items-center transform transition duration-300 hover:scale-105">
                    <div className="w-full h-48 bg-gray-800 rounded-lg mb-2 flex items-center justify-center overflow-hidden border border-gray-700 transition-all hover:border-blue-400">
                      {rightExteriorPreview ? (
                        <img src={rightExteriorPreview} alt="Right Exterior" className="w-full h-full object-cover" />
                      ) : (
                        <div className="text-center p-4">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <p className="text-gray-500">Right Side of Vehicle</p>
                        </div>
                      )}
                    </div>
                    <input
                      ref={rightExteriorRef}
                      type="file"
                      accept="image/*"
                      capture="environment"
                      className="hidden"
                      onChange={(e) => handlePhotoChange(e, setRightExteriorPhoto, setRightExteriorPreview)}
                      required
                      disabled={!isClockedIn}
                    />
                    <button
                      type="button"
                      onClick={() => rightExteriorRef.current.click()}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-3 py-1 rounded-md text-sm shadow-md transition-all duration-300"
                      disabled={loading || !isClockedIn}
                    >
                      {rightExteriorPreview ? 'Change Photo' : 'Take Photo'}
                    </button>
                    <UploadProgressIndicator progress={uploadProgress['right_exterior']} />
                  </div>
                </div>
              </div>
              
              {/* Notes Section */}
              <div 
                ref={formSectionRefs.notes}
                className="bg-gray-900 bg-opacity-60 p-5 rounded-lg border border-gray-700 transform transition-all duration-300 hover:border-blue-500 hover:shadow-md hover:shadow-blue-900/20"
              >
                <h3 className="text-lg font-semibold text-blue-300 mb-4">Notes & Defects</h3>
                <div>
                  <textarea
                    className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[150px] transition-colors"
                    placeholder="Record any defects, damages, or other vehicle condition details here..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    disabled={loading || !isClockedIn}
                  />
                </div>
              </div>
              
              {/* Submit Button */}
              <div className="flex justify-center">
                <button
                  type="submit"
                  disabled={loading || !isClockedIn}
                  className={`px-8 py-3 rounded-md text-white font-medium text-lg shadow-lg transform transition-all duration-300 ${
                    loading || !isClockedIn
                      ? 'bg-gray-600 cursor-not-allowed' 
                      : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 hover:scale-105 hover:shadow-blue-900/50'
                  }`}
                >
                  {loading ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Submitting...
                    </div>
                  ) : !isClockedIn ? 'Clock In to Submit' : 'Submit Inspection'}
                </button>
              </div>
            </form>
          </div>
        )}
        
        {/* Admin List View */}
        {viewMode === 'list' && isAdmin && (
          <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700 transform transition-all duration-500 hover:shadow-xl">
            <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-6">
              All Vehicle Inspections
            </h2>
            
            {/* Date Filter Controls */}
            <div className="mb-6 bg-gray-900 p-4 rounded-lg border border-gray-700">
              <h3 className="text-lg font-medium text-blue-300 mb-3">Filter by Date</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">Start Date</label>
                  <input
                    type="date"
                    className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">End Date</label>
                  <input
                    type="date"
                    className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={applyDateFilter}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 flex-grow"
                  >
                    Apply Filter
                  </button>
                  {filterActive && (
                    <button
                      onClick={clearDateFilter}
                      className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300"
                    >
                      Clear
                    </button>
                  )}
                </div>
              </div>
              
              {filterActive && (
                <div className="mt-2 bg-blue-900 bg-opacity-30 p-2 rounded border border-blue-800 text-blue-200 text-sm">
                  <span>Filtered: </span>
                  <span>{new Date(startDate).toLocaleDateString()} to {new Date(endDate).toLocaleDateString()}</span>
                </div>
              )}
            </div>
            
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin h-12 w-12 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
              </div>
            ) : inspections.length === 0 ? (
              <div className="bg-gray-900 p-8 rounded-lg text-center border border-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p className="text-gray-400">No inspection records found</p>
                {filterActive && (
                  <p className="text-gray-500 mt-2 text-sm">Try adjusting your date filter or clear it to see all inspections</p>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-gray-900 rounded-lg overflow-hidden">
                  <thead className="bg-gradient-to-r from-gray-800 to-gray-700 text-gray-300">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Date/Time</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Vehicle</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">User</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Mileage</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Notes</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-800">
                    {inspections.map((inspection) => (
                      <tr 
                        key={inspection.id} 
                        className={`hover:bg-gray-800 transition-colors cursor-pointer ${
                          inspection.needsService ? 'bg-red-900 bg-opacity-30 border-l-4 border-red-600' : ''
                        }`}
                        onClick={() => viewInspectionDetails(inspection)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          {inspection.needsService && (
                            <div className="flex items-center">
                              <div className="relative flex-shrink-0">
                                <span className="absolute inset-0 rounded-full animate-ping bg-red-600 opacity-25"></span>
                                <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                              </div>
                              <span className="ml-2 text-red-400 text-sm font-medium">Needs Service</span>
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {inspection.timestamp ? (
                            <div>
                              <div>{new Date(inspection.timestamp).toLocaleDateString()}</div>
                              <div className="text-gray-500">{new Date(inspection.timestamp).toLocaleTimeString()}</div>
                            </div>
                          ) : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {inspection.carNumber}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          <div className="flex items-center">
                            {inspection.userPhotoBase64 ? (
                              <div className="h-8 w-8 rounded-full overflow-hidden mr-2 border border-gray-600">
                                <img 
                                  src={inspection.userPhotoBase64} 
                                  alt="User" 
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            ) : (
                              <div className="h-8 w-8 rounded-full bg-gray-700 flex items-center justify-center mr-2">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                              </div>
                            )}
                            <div>
                              <div className="font-medium">{inspection.userName}</div>
                              {inspection.userJobTitle && (
                                <div className="text-xs text-gray-500">{inspection.userJobTitle}</div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {inspection.mileage?.toLocaleString() || 'N/A'} mi
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {inspection.notes ? (
                            <div className="flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                              </svg>
                              <span>Yes</span>
                            </div>
                          ) : (
                            <span className="text-gray-500">None</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <div className="flex space-x-2">
                            <button 
                              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white px-3 py-1 rounded shadow-md transition-all duration-300 transform hover:scale-105"
                              onClick={(e) => {
                                e.stopPropagation();
                                viewInspectionDetails(inspection);
                              }}
                            >
                              View
                            </button>
                            <button 
                              className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-3 py-1 rounded shadow-md transition-all duration-300 transform hover:scale-105"
                              onClick={(e) => handleDeleteClick(e, inspection)}
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Inspection Details Modal */}
      {showDetailsModal && selectedInspection && (
        <div className="fixed inset-0 overflow-y-auto z-50 flex items-center justify-center px-4">
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" onClick={closeDetailsModal}></div>
          
          <div className="relative bg-gray-900 rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto shadow-xl border border-gray-700 transform transition-all">
            <div className="sticky top-0 bg-gradient-to-r from-gray-900 to-gray-800 p-4 border-b border-gray-700 flex justify-between items-center z-10">
              <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">
                Inspection Details - Vehicle #{selectedInspection.carNumber}
              </h3>
              <button
                onClick={closeDetailsModal}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-6">
              {/* Service Status */}
              {selectedInspection.needsService && (
                <div className="bg-red-900 bg-opacity-30 p-4 rounded-lg mb-6 border border-red-600 animate-pulse">
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <span className="text-red-400 font-medium">This vehicle needs immediate service</span>
                  </div>
                </div>
              )}
              
              {/* User and Basic Info */}
              <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                  <div className="flex items-center mb-4 md:mb-0">
                    {selectedInspection.userPhotoBase64 ? (
                      <div className="h-12 w-12 rounded-full overflow-hidden mr-4 border-2 border-blue-500 shadow-md">
                        <img 
                          src={selectedInspection.userPhotoBase64} 
                          alt="User" 
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-12 w-12 rounded-full bg-gray-700 flex items-center justify-center mr-4 border border-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    )}
                    <div>
                      <h4 className="text-lg font-medium text-white">{selectedInspection.userName}</h4>
                      {selectedInspection.userJobTitle && (
                        <p className="text-sm text-gray-400">{selectedInspection.userJobTitle}</p>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-gray-300">
                      <span className="font-medium">Inspection Date:</span>{' '}
                      {selectedInspection.timestamp?.toLocaleDateString('en-US', { 
                        weekday: 'long', 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}
                    </div>
                    <div className="text-gray-300">
                      <span className="font-medium">Time:</span>{' '}
                      {selectedInspection.timestamp?.toLocaleTimeString('en-US', { 
                        hour: '2-digit', 
                        minute: '2-digit', 
                        hour12: true 
                      })}
                    </div>
                  </div>
                </div>
                
<div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-3 bg-gray-850 rounded-lg">
                  <div>
                    <span className="text-gray-400">Vehicle Number:</span>
                    <span className="ml-2 text-gray-200 font-medium">{selectedInspection.carNumber}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Mileage:</span>
                    <span className="ml-2 text-gray-200 font-medium">{selectedInspection.mileage?.toLocaleString()} miles</span>
                  </div>
                  {selectedInspection.userVehicle && (
                    <div>
                      <span className="text-gray-400">User's Vehicle:</span>
                      <span className="ml-2 text-gray-200 font-medium">{selectedInspection.userVehicle}</span>
                    </div>
                  )}
                  <div>
                    <span className="text-gray-400">Service Status:</span>
                    <span className={`ml-2 font-medium ${selectedInspection.needsService ? 'text-red-400' : 'text-green-400'}`}>
                      {selectedInspection.needsService ? 'Needs Service' : 'No Service Required'}
                    </span>
                  </div>
                </div>
              </div>
              
              {/* Notes Section */}
              {selectedInspection.notes && (
                <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
                  <h4 className="text-lg font-medium text-blue-300 mb-2">Notes & Defects</h4>
                  <div className="bg-gray-900 p-3 rounded border border-gray-700 text-gray-300 whitespace-pre-line">
                    {selectedInspection.notes}
                  </div>
                </div>
              )}
              
              {/* Tire Photos */}
              <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
                <h4 className="text-lg font-medium text-blue-300 mb-4">Tire Photos</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700 h-48 transition-transform hover:scale-105">
                      <img 
                        src={selectedInspection.photos?.frontTire || ''} 
                        alt="Front Tire" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = "https://via.placeholder.com/400x400?text=Image+Not+Available";
                        }}
                      />
                    </div>
                    <p className="text-center text-gray-400">Front Tire</p>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700 h-48 transition-transform hover:scale-105">
                      <img 
                        src={selectedInspection.photos?.rightTire || ''} 
                        alt="Right Tire" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = "https://via.placeholder.com/400x400?text=Image+Not+Available";
                        }}
                      />
                    </div>
                    <p className="text-center text-gray-400">Right Tire</p>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700 h-48 transition-transform hover:scale-105">
                      <img 
                        src={selectedInspection.photos?.leftTire || ''} 
                        alt="Left Tire" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = "https://via.placeholder.com/400x400?text=Image+Not+Available";
                        }}
                      />
                    </div>
                    <p className="text-center text-gray-400">Left Tire</p>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700 h-48 transition-transform hover:scale-105">
                      <img 
                        src={selectedInspection.photos?.backTire || ''} 
                        alt="Back Tire" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = "https://via.placeholder.com/400x400?text=Image+Not+Available";
                        }}
                      />
                    </div>
                    <p className="text-center text-gray-400">Back Tire</p>
                  </div>
                </div>
              </div>
              
              {/* Interior Photo */}
              <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
                <h4 className="text-lg font-medium text-blue-300 mb-4">Interior Photo</h4>
                <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700 transition-transform hover:scale-105">
                  <img 
                    src={selectedInspection.photos?.interior || ''} 
                    alt="Interior" 
                    className="w-full h-full object-cover max-h-96 mx-auto"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = "https://via.placeholder.com/800x400?text=Image+Not+Available";
                    }}
                  />
                </div>
              </div>
              
              {/* Exterior Photos */}
              <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
                <h4 className="text-lg font-medium text-blue-300 mb-4">Exterior Photos</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="space-y-2">
                    <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700 h-48 transition-transform hover:scale-105">
                      <img 
                        src={selectedInspection.photos?.frontExterior || ''} 
                        alt="Front Exterior" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = "https://via.placeholder.com/400x300?text=Image+Not+Available";
                        }}
                      />
                    </div>
                    <p className="text-center text-gray-400">Front of Vehicle</p>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700 h-48 transition-transform hover:scale-105">
                      <img 
                        src={selectedInspection.photos?.backExterior || ''} 
                        alt="Back Exterior" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = "https://via.placeholder.com/400x300?text=Image+Not+Available";
                        }}
                      />
                    </div>
                    <p className="text-center text-gray-400">Back of Vehicle</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700 h-48 transition-transform hover:scale-105">
                      <img 
                        src={selectedInspection.photos?.leftExterior || ''} 
                        alt="Left Exterior" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = "https://via.placeholder.com/400x300?text=Image+Not+Available";
                        }}
                      />
                    </div>
                    <p className="text-center text-gray-400">Left Side of Vehicle</p>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700 h-48 transition-transform hover:scale-105">
                      <img 
                        src={selectedInspection.photos?.rightExterior || ''} 
                        alt="Right Exterior" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = "https://via.placeholder.com/400x300?text=Image+Not+Available";
                        }}
                      />
                    </div>
                    <p className="text-center text-gray-400">Right Side of Vehicle</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Footer with buttons */}
            <div className="sticky bottom-0 bg-gradient-to-r from-gray-900 to-gray-800 p-4 border-t border-gray-700 flex justify-between">
              <button
                onClick={() => {
                  closeDetailsModal();
                  handleDeleteClick(new Event('click'), selectedInspection);
                }}
                className="bg-gradient-to-r from-red-700 to-red-600 hover:from-red-600 hover:to-red-500 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105"
              >
                Delete Inspection
              </button>
              <button
                onClick={closeDetailsModal}
                className="bg-gradient-to-r from-gray-700 to-gray-600 hover:from-gray-600 hover:to-gray-500 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 overflow-y-auto z-50 flex items-center justify-center px-4">
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" onClick={closeDeleteModal}></div>
          
          <div className="relative bg-gray-900 rounded-lg max-w-md w-full overflow-hidden shadow-xl border border-gray-700 transform transition-all">
            <div className="bg-gradient-to-r from-red-900 to-gray-800 p-4 border-b border-gray-700">
              <h3 className="text-lg font-medium text-white">Confirm Deletion</h3>
            </div>
            
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="bg-red-900 rounded-full p-2 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </div>
                <div>
                  <p className="text-white font-medium">Delete this inspection record?</p>
                  <p className="text-gray-400 text-sm mt-1">
                    This will permanently remove inspection data for Vehicle #{inspectionToDelete?.carNumber}.
                    This action cannot be undone.
                  </p>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={closeDeleteModal}
                  className="px-4 py-2 bg-gray-800 text-gray-300 rounded-md hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={deleteInspection}
                  disabled={loading}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : (
                    'Delete Inspection'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Inspection;