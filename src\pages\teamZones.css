/* Zone styling - Core elements */
.zone-label-container {
    display: none !important;
    /* Hide all zone labels */
}

.zone-rectangle {
    opacity: 0.3;
    transition: opacity 0.3s ease, stroke-width 0.3s ease;
}

.zone-rectangle:hover {
    opacity: 0.5;
    cursor: pointer;
}

.zone-rectangle.active {
    opacity: 0.4;
    stroke-width: 3px;
}

/* Highlight animation for selected zone */
@keyframes zone-pulse {
    0% {
        opacity: 0.6;
        stroke-width: 2;
    }

    50% {
        opacity: 1;
        stroke-width: 3;
    }

    100% {
        opacity: 0.6;
        stroke-width: 2;
    }
}

.zone-highlight-pulse path {
    animation: zone-pulse 1.5s infinite;
}

/* Animation for zone highlighting on map */
@keyframes highlight-zone {
    0% {
        opacity: 0.3;
        stroke-width: 2px;
    }

    50% {
        opacity: 0.6;
        stroke-width: 3px;
    }

    100% {
        opacity: 0.3;
        stroke-width: 2px;
    }
}

.highlighted-zone {
    animation: highlight-zone 1.5s infinite;
}