import { initializeApp, getApps, getApp } from 'firebase/app';
import { 
  getFirestore,
  doc, 
  collection, 
  getDocs, 
  addDoc, 
  deleteDoc, 
  query, 
  where, 
  updateDoc, 
  onSnapshot, 
  getDoc, 
  setDoc, 
  serverTimestamp,
  orderBy,
  limit
} from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { getAnalytics } from "firebase/analytics";

// Detect environment
const isLocalhost = typeof window !== 'undefined' && 
  (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

console.log(`Running in ${isLocalhost ? 'development (localhost)' : 'production'} environment`);

// Firebase configuration with your actual values
const firebaseConfig = {
  apiKey: "AIzaSyDsheDQQhNmVfYl0JQxuR64bl-ciR1rMe8",
  authDomain: "nwrepo-bf088.firebaseapp.com",
  databaseURL: "https://nwrepo-bf088-default-rtdb.firebaseio.com",
  projectId: "nwrepo-bf088",
  storageBucket: "nwrepo-bf088.firebasestorage.app",
  messagingSenderId: "499202829104",
  appId: "1:499202829104:web:c5a451b5ccc68f07dbb6f5",
  measurementId: "G-H3YSTEJ27Q"
};

// Initialize Firebase - with safe initialization that prevents duplicate app
let app;
let analytics;

try {
  // Check if Firebase apps already initialized
  if (getApps().length === 0) {
    // No Firebase apps initialized yet, create a new one
    app = initializeApp(firebaseConfig);
    console.log("Firebase initialized successfully");
    
    // Initialize analytics in browser environment
    if (typeof window !== 'undefined') {
      analytics = getAnalytics(app);
    }
  } else {
    // Firebase app already initialized, get the existing one
    app = getApp();
    console.log("Using existing Firebase app");
  }
} catch (error) {
  console.error("Firebase initialization error:", error);
  throw error;
}

// Initialize services
export const db = getFirestore(app);
export const auth = getAuth(app);

// Make Firebase app instance available globally for components that need direct access
if (typeof window !== 'undefined') {
  window.firebaseApp = app;
  window.firestoreDB = db;
  window.firebaseAuth = auth;
  
  // Clear previous auth state if we're on localhost to avoid conflicts
  if (isLocalhost) {
    const localStorageKeys = Object.keys(localStorage);
    const firebaseKeys = localStorageKeys.filter(key => key.startsWith('firebase:'));
    
    if (firebaseKeys.length > 0) {
      console.log("Found existing Firebase auth state in localStorage. Consider clearing it if you're experiencing auth issues.");
      console.log("You can use window.clearAuthState() to reset auth state if needed.");
    }
  }
}

// Helper to clear auth state (useful for debugging)
export const clearAuthState = () => {
  if (auth) {
    // Sign out from Firebase Auth
    auth.signOut().then(() => {
      console.log("User signed out");
      
      // Clear all Firebase-related localStorage items
      const localStorageKeys = Object.keys(localStorage);
      localStorageKeys.forEach(key => {
        if (key.startsWith('firebase:')) {
          console.log(`Clearing localStorage item: ${key}`);
          localStorage.removeItem(key);
        }
      });
      
      // Reload the page to ensure a clean state
      window.location.reload();
    }).catch(error => {
      console.error("Error signing out:", error);
    });
  } else {
    console.error("Auth is not initialized");
  }
};

// Add clearAuthState to window for debugging access
if (typeof window !== 'undefined') {
  window.clearAuthState = clearAuthState;
}

// Function to validate team access
export const validateTeamAccess = async (db, userId, teamId, isAdmin = false) => {
  if (!userId || !teamId) return false;
  
  // Admins always have access
  if (isAdmin) return true;
  
  try {
    // Check if user is a member of the team
    const membersCollection = collection(db, `teams/${teamId}/teamMembers`);
    const memberQuery = query(membersCollection, where("userId", "==", userId));
    const memberSnapshot = await getDocs(memberQuery);
    
    return !memberSnapshot.empty;
  } catch (error) {
    console.error("Error validating team access:", error);
    return false;
  }
};

// Get team data
export const getTeamData = async (db, teamId) => {
  if (!teamId) return null;
  
  try {
    const teamRef = doc(db, 'teams', teamId);
    const teamDoc = await getDoc(teamRef);
    
    if (teamDoc.exists()) {
      return {
        id: teamDoc.id,
        ...teamDoc.data()
      };
    } else {
      console.log(`No team found with ID ${teamId}`);
      return null;
    }
  } catch (error) {
    console.error("Error fetching team data:", error);
    return null;
  }
};

// Team location subscription
export const subscribeToTeamLocations = (db, teamId, onSuccess, onError) => {
  try {
    console.log(`Setting up locations listener for team: ${teamId}`);
    
    const locationsCollection = collection(db, 'teams', teamId, 'locations');
    const locationsQuery = query(locationsCollection);
    
    return onSnapshot(locationsQuery, 
      (snapshot) => {
        const locations = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          position: doc.data().position || { lat: 0, lng: 0 }
        }));
        onSuccess(locations);
      },
      (error) => {
        console.error(`Error in team locations subscription: ${error}`);
        if (onError) onError(error);
      }
    );
  } catch (error) {
    console.error("Error setting up locations subscription:", error);
    if (onError) onError(error);
    return () => {}; // Return a no-op unsubscribe function
  }
};

// User locations subscription
export const subscribeToTeamUserLocations = (db, teamId, onSuccess, onError, options = {}) => {
  try {
    const { onlineOnly = true, excludeCurrentUser = false, currentUserId = null } = options;
    
    console.log(`Setting up user locations listener for team: ${teamId}`);
    
    // Get team users collection
    const userLocationsRef = collection(db, 'userLocations');
    
    // Create query with team filter
    let userLocationsQuery = query(userLocationsRef, where('teamId', '==', teamId));
    
    // Add online filter if requested
    if (onlineOnly) {
      userLocationsQuery = query(userLocationsQuery, where('online', '==', true));
    }
    
    return onSnapshot(userLocationsQuery, 
      (snapshot) => {
        const users = snapshot.docs
          .map(doc => ({
            uid: doc.id,
            ...doc.data(),
            location: doc.data().location || { lat: 0, lng: 0 }
          }))
          .filter(user => {
            // Exclude current user if requested
            return !(excludeCurrentUser && user.uid === currentUserId);
          });
        
        console.log(`Found ${users.length} active team users`);
        onSuccess(users);
      },
      (error) => {
        console.error(`Error in team user locations subscription: ${error}`);
        if (onError) onError(error);
      }
    );
  } catch (error) {
    console.error("Error setting up user locations subscription:", error);
    if (onError) onError(error);
    return () => {}; // Return a no-op unsubscribe function
  }
};

// Update user location
export const updateUserLocation = async (db, position, user, teamId) => {
  if (!user || !position) return Promise.reject("User or position missing");
  
  try {
    const userLocationRef = doc(db, 'userLocations', user.uid);
    
    const locationData = {
      location: position,
      lastUpdated: serverTimestamp(),
      email: user.email || '',
      displayName: user.displayName || '',
      online: true
    };
    
    // Only add teamId if provided
    if (teamId) {
      locationData.teamId = teamId;
    }
    
    await setDoc(userLocationRef, locationData, { merge: true });
    return true;
  } catch (error) {
    console.error("Error updating user location:", error);
    return Promise.reject(error);
  }
};

// Update user online status
export const updateUserOnlineStatus = async (db, isOnline, user, teamId) => {
  if (!user) return Promise.reject("User missing");
  
  try {
    const userLocationRef = doc(db, 'userLocations', user.uid);
    
    const statusData = {
      online: isOnline,
      lastUpdated: serverTimestamp(),
      email: user.email || '',
      displayName: user.displayName || ''
    };
    
    // Only add teamId if provided
    if (teamId) {
      statusData.teamId = teamId;
    }
    
    await setDoc(userLocationRef, statusData, { merge: true });
    return true;
  } catch (error) {
    console.error("Error updating user online status:", error);
    return Promise.reject(error);
  }
};

// User traces subscription
export const subscribeToTeamUserTraces = (db, teamId, onSuccess, onError) => {
  try {
    console.log(`Setting up user traces listener for team: ${teamId}`);
    
    const userTracesRef = collection(db, 'userTraces');
    const userTracesQuery = query(userTracesRef, where('teamId', '==', teamId));
    
    return onSnapshot(userTracesQuery, 
      (snapshot) => {
        const traces = {};
        
        snapshot.docs.forEach(doc => {
          const userId = doc.id;
          const userData = doc.data();
          
          if (userData && userData.tracePath && Array.isArray(userData.tracePath)) {
            traces[userId] = userData.tracePath;
          }
        });
        
        onSuccess(traces);
      },
      (error) => {
        console.error(`Error in team user traces subscription: ${error}`);
        if (onError) onError(error);
      }
    );
  } catch (error) {
    console.error("Error setting up user traces subscription:", error);
    if (onError) onError(error);
    return () => {}; // Return a no-op unsubscribe function
  }
};

// Update user trace (breadcrumb path)
export const updateUserTrace = async (db, position, user, teamId) => {
  if (!user || !position || !teamId) return Promise.reject("Missing required parameters");
  
  try {
    const userTraceRef = doc(db, 'userTraces', user.uid);
    
    // Get existing trace path or create new one
    const traceDoc = await getDoc(userTraceRef);
    
    if (traceDoc.exists()) {
      // Update existing trace path
      const existingData = traceDoc.data();
      let tracePath = existingData.tracePath || [];
      
      // Add timestamp to position
      const pointWithTimestamp = {
        ...position,
        timestamp: new Date().getTime()
      };
      
      // Add new position to trace path
      tracePath.push(pointWithTimestamp);
      
      // Keep only last 100 points to avoid exceeding Firestore limits
      if (tracePath.length > 100) {
        tracePath = tracePath.slice(tracePath.length - 100);
      }
      
      // Update document
      await updateDoc(userTraceRef, {
        tracePath,
        teamId,
        lastUpdated: serverTimestamp()
      });
    } else {
      // Create new trace path
      const pointWithTimestamp = {
        ...position,
        timestamp: new Date().getTime()
      };
      
      await setDoc(userTraceRef, {
        tracePath: [pointWithTimestamp],
        teamId,
        lastUpdated: serverTimestamp(),
        userId: user.uid
      });
    }
    
    return true;
  } catch (error) {
    console.error("Error updating user trace:", error);
    return Promise.reject(error);
  }
};

// Clear user trace
export const clearUserTrace = async (db, userId, currentUser, teamId) => {
  if (!userId || !teamId) return Promise.reject("Missing required parameters");
  
  try {
    // Security check - only allow users to clear their own trace unless admin
    if (userId !== currentUser.uid && !currentUser.isAdmin) {
      return Promise.reject("Permission denied: Cannot clear another user's trace");
    }
    
    const userTraceRef = doc(db, 'userTraces', userId);
    
    await updateDoc(userTraceRef, {
      tracePath: [],
      clearedAt: serverTimestamp(),
      clearedBy: currentUser.uid,
      teamId
    });
    
    return true;
  } catch (error) {
    console.error("Error clearing user trace:", error);
    return Promise.reject(error);
  }
};

// Get user profile
export const getUserProfile = async (db, userId) => {
  if (!userId) return null;
  
  try {
    const userProfileRef = doc(db, 'userProfiles', userId);
    const profileDoc = await getDoc(userProfileRef);
    
    if (profileDoc.exists()) {
      return {
        id: profileDoc.id,
        ...profileDoc.data()
      };
    } else {
      console.log(`No profile found for user ${userId}`);
      return null;
    }
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return null;
  }
};

// Get user clock status
export const getUserClockStatus = async (db, userId) => {
  if (!userId) return { clockedIn: false, clockInTime: null };
  
  try {
    const userClockRef = doc(db, 'userClockStatus', userId);
    const clockDoc = await getDoc(userClockRef);
    
    if (clockDoc.exists()) {
      const data = clockDoc.data();
      return {
        clockedIn: data.clockedIn || false,
        clockInTime: data.clockInTime ? data.clockInTime.toDate() : null,
        lastUpdated: data.lastUpdated ? data.lastUpdated.toDate() : null
      };
    } else {
      return { clockedIn: false, clockInTime: null };
    }
  } catch (error) {
    console.error("Error fetching user clock status:", error);
    return { clockedIn: false, clockInTime: null };
  }
};

// Get user teams
export const getUserTeams = async (db, userId) => {
  if (!userId) return [];
  
  try {
    // Get team memberships
    const teamMembersCollection = collection(db, 'teamMembers');
    const membershipQuery = query(teamMembersCollection, where("userId", "==", userId));
    const membershipSnapshot = await getDocs(membershipQuery);
    
    if (membershipSnapshot.empty) {
      return [];
    }
    
    // Get team IDs from memberships
    const teamIds = membershipSnapshot.docs.map(doc => doc.data().teamId);
    
    // Get team data for each team ID
    const teams = [];
    for (const teamId of teamIds) {
      const teamData = await getTeamData(db, teamId);
      if (teamData) {
        teams.push(teamData);
      }
    }
    
    return teams;
  } catch (error) {
    console.error("Error fetching user teams:", error);
    return [];
  }
};

// Create team
export const createTeam = async (db, teamData, userId) => {
  if (!teamData || !userId) return Promise.reject("Missing required parameters");
  
  try {
    // Create team document
    const newTeamRef = await addDoc(collection(db, 'teams'), {
      ...teamData,
      createdBy: userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      memberCount: 1
    });
    
    console.log(`Created team with ID: ${newTeamRef.id}`);
    
    // Add creating user as admin in teamMembers subcollection
    await addDoc(collection(db, `teams/${newTeamRef.id}/teamMembers`), {
      userId: userId,
      role: 'admin',
      addedAt: serverTimestamp(),
      addedBy: userId
    });
    
    // Also add to root teamMembers collection for backwards compatibility
    await addDoc(collection(db, 'teamMembers'), {
      userId: userId,
      teamId: newTeamRef.id,
      role: 'admin',
      addedAt: serverTimestamp(),
      addedBy: userId
    });
    
    return newTeamRef.id;
  } catch (error) {
    console.error("Error creating team:", error);
    return Promise.reject(error);
  }
};

// Add user to team
export const addUserToTeam = async (db, userId, teamId, role = 'member', addedBy) => {
  if (!userId || !teamId) return Promise.reject("Missing required parameters");
  
  try {
    // Add to team members subcollection
    await addDoc(collection(db, `teams/${teamId}/teamMembers`), {
      userId,
      role,
      addedAt: serverTimestamp(),
      addedBy
    });
    
    // Also add to root teamMembers collection for backwards compatibility
    await addDoc(collection(db, 'teamMembers'), {
      userId,
      teamId,
      role,
      addedAt: serverTimestamp(),
      addedBy
    });
    
    // Update team member count
    const teamRef = doc(db, "teams", teamId);
    const teamDoc = await getDoc(teamRef);
    
    if (teamDoc.exists()) {
      const teamCount = teamDoc.data().memberCount || 0;
      await updateDoc(teamRef, {
        memberCount: teamCount + 1,
        updatedAt: serverTimestamp()
      });
    }
    
    return true;
  } catch (error) {
    console.error("Error adding user to team:", error);
    return Promise.reject(error);
  }
};

export default app;