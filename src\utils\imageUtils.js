/**
 * Utility functions for image processing, compression, and manipulation
 */

/**
 * Compress an image file to base64 with resizing
 * 
 * @param {File} file - Image file from input or camera
 * @param {Object} options - Compression options
 * @returns {Promise<string>} Promise resolving to base64 image data
 */
export const compressImageToBase64 = (file, options = {}) => {
  const {
    maxWidth = 800,
    maxHeight = 800,
    quality = 0.7,
    type = 'image/jpeg'
  } = options;
  
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (event) => {
        const img = new Image();
        img.src = event.target.result;
        img.onload = () => {
          // Create a canvas to resize the image
          const canvas = document.createElement('canvas');
          let width = img.width;
          let height = img.height;
          
          // Calculate new dimensions while maintaining aspect ratio
          if (width > height) {
            if (width > maxWidth) {
              height = Math.round(height * maxWidth / width);
              width = maxWidth;
            }
          } else {
            if (height > maxHeight) {
              width = Math.round(width * maxHeight / height);
              height = maxHeight;
            }
          }
          
          canvas.width = width;
          canvas.height = height;
          
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);
          
          // Get base64 representation
          const base64 = canvas.toDataURL(type, quality);
          resolve(base64);
        };
        img.onerror = (error) => reject(error);
      };
      reader.onerror = (error) => reject(error);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Add metadata overlay to an image
 * 
 * @param {string} imageBase64 - Base64 image data
 * @param {Object} metadata - Metadata to overlay
 * @returns {Promise<string>} Promise resolving to base64 image with metadata
 */
export const addMetadataToImage = async (imageBase64, metadata = {}) => {
  return new Promise((resolve, reject) => {
    try {
      const img = new Image();
      img.src = imageBase64;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const width = img.width;
        const height = img.height;
        
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        
        // Draw the original image
        ctx.drawImage(img, 0, 0, width, height);
        
        // Add semi-transparent background for text
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(0, height - 80, width, 80);
        
        // Add metadata text
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        
        // Format date and time
        const dateTime = metadata.dateTime 
          ? new Date(metadata.dateTime).toLocaleString() 
          : new Date().toLocaleString();
        
        ctx.fillText(`Date/Time: ${dateTime}`, 10, height - 60);
        
        if (metadata.latitude !== undefined && metadata.longitude !== undefined) {
          ctx.fillText(`Location: ${metadata.latitude.toFixed(6)}, ${metadata.longitude.toFixed(6)}`, 10, height - 40);
        }
        
        if (metadata.address) {
          ctx.fillText(`Address: ${metadata.address}`, 10, height - 20);
        }
        
        if (metadata.intersection) {
          ctx.fillText(`Intersection: ${metadata.intersection}`, 10, height - 0);
        }
        
        // Convert back to base64
        const newImageBase64 = canvas.toDataURL('image/jpeg', 0.7);
        resolve(newImageBase64);
      };
      img.onerror = (error) => reject(error);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Capture a screenshot of an HTML element
 * 
 * @param {HTMLElement} element - Element to capture
 * @param {Object} options - Screenshot options
 * @returns {Promise<string>} Promise resolving to base64 image data
 */
export const captureElementScreenshot = async (element, options = {}) => {
  if (!element) {
    throw new Error("Element to capture is required");
  }
  
  try {
    // Dynamic import html2canvas to reduce bundle size in applications that don't use this function
    const html2canvas = await import('html2canvas').then(module => module.default);
    
    const {
      scale = 1,
      backgroundColor = '#1a1a1a',
      useCORS = true,
      logging = false,
      allowTaint = true
    } = options;
    
    // Use html2canvas to take a screenshot
    const canvas = await html2canvas(element, {
      useCORS,
      scale,
      allowTaint,
      backgroundColor,
      logging
    });
    
    // Convert canvas to Base64 image
    const imageBase64 = canvas.toDataURL('image/png');
    return imageBase64;
  } catch (error) {
    console.error("Error capturing screenshot:", error);
    throw error;
  }
};

/**
 * Open the device camera to take a photo
 * 
 * @param {Object} options - Camera options
 * @returns {Promise<File>} Promise resolving to image file
 */
export const openCameraForPhoto = (options = {}) => {
  const {
    useRearCamera = true,
    acceptedTypes = 'image/*'
  } = options;
  
  return new Promise((resolve, reject) => {
    try {
      // Create a file input element programmatically
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = acceptedTypes;
      
      // Use the rear camera by default
      if (useRearCamera) {
        input.capture = 'environment';
      }
      
      // Add change event listener
      input.addEventListener('change', (e) => {
        if (e.target.files && e.target.files[0]) {
          resolve(e.target.files[0]);
        } else {
          reject(new Error("No image captured"));
        }
      });
      
      // Add cancel handler
      input.addEventListener('cancel', () => {
        reject(new Error("Image capture canceled"));
      });
      
      // Trigger the file picker
      input.click();
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Create an image thumbnail
 * 
 * @param {string} imageBase64 - Base64 image data
 * @param {Object} options - Thumbnail options
 * @returns {Promise<string>} Promise resolving to base64 thumbnail
 */
export const createThumbnail = (imageBase64, options = {}) => {
  const {
    width = 100,
    height = 100,
    quality = 0.5
  } = options;
  
  return new Promise((resolve, reject) => {
    try {
      const img = new Image();
      img.src = imageBase64;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        
        // Calculate dimensions while maintaining aspect ratio
        let destWidth = width;
        let destHeight = height;
        
        // Crop to square if requested
        if (options.square) {
          const size = Math.min(img.width, img.height);
          const startX = (img.width - size) / 2;
          const startY = (img.height - size) / 2;
          
          canvas.width = width;
          canvas.height = height;
          
          const ctx = canvas.getContext('2d');
          ctx.drawImage(
            img,
            startX, startY, size, size,
            0, 0, width, height
          );
        } else {
          // Maintain aspect ratio
          if (img.width / img.height > width / height) {
            destHeight = img.height * width / img.width;
          } else {
            destWidth = img.width * height / img.height;
          }
          
          canvas.width = destWidth;
          canvas.height = destHeight;
          
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, destWidth, destHeight);
        }
        
        // Get base64 representation
        const thumbnailBase64 = canvas.toDataURL('image/jpeg', quality);
        resolve(thumbnailBase64);
      };
      img.onerror = (error) => reject(error);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Prompt user to select multiple images
 * 
 * @param {Object} options - Selection options
 * @returns {Promise<Array>} Promise resolving to array of selected files
 */
export const promptForImages = (options = {}) => {
  const {
    multiple = true,
    acceptedTypes = 'image/*',
    maxCount = 10
  } = options;
  
  return new Promise((resolve, reject) => {
    try {
      // Create file input
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = acceptedTypes;
      
      if (multiple) {
        input.multiple = true;
      }
      
      // Add change event listener
      input.addEventListener('change', (e) => {
        if (e.target.files && e.target.files.length > 0) {
          // Convert FileList to array and limit to maxCount
          const files = Array.from(e.target.files).slice(0, maxCount);
          resolve(files);
        } else {
          reject(new Error("No images selected"));
        }
      });
      
      // Trigger file picker
      input.click();
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Process multiple image files (compress and convert to base64)
 * 
 * @param {Array} files - Array of image files
 * @param {Object} options - Processing options
 * @returns {Promise<Array>} Promise resolving to array of base64 images
 */
export const processMultipleImages = async (files, options = {}) => {
  if (!files || files.length === 0) {
    return [];
  }
  
  try {
    // Process each file
    const processedImages = await Promise.all(
      files.map(file => compressImageToBase64(file, options))
    );
    
    return processedImages;
  } catch (error) {
    console.error("Error processing images:", error);
    throw error;
  }
};

/**
 * Create an image element from a base64 string
 * 
 * @param {string} base64 - Base64 image data
 * @param {Object} attributes - Additional image attributes
 * @returns {Promise<HTMLImageElement>} Promise resolving to image element
 */
export const createImageElement = (base64, attributes = {}) => {
  return new Promise((resolve, reject) => {
    try {
      const img = new Image();
      
      // Add event listeners
      img.onload = () => resolve(img);
      img.onerror = (error) => reject(error);
      
      // Set attributes
      Object.entries(attributes).forEach(([key, value]) => {
        img.setAttribute(key, value);
      });
      
      // Set source last
      img.src = base64;
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Preload a series of images for smooth display
 * 
 * @param {Array} imageUrls - Array of image URLs or base64 strings
 * @returns {Promise<Array>} Promise resolving to array of loaded images
 */
export const preloadImages = async (imageUrls) => {
  if (!imageUrls || imageUrls.length === 0) {
    return [];
  }
  
  const loadPromises = imageUrls.map(url => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = (error) => reject(error);
      img.src = url;
    });
  });
  
  try {
    return await Promise.all(loadPromises);
  } catch (error) {
    console.error("Error preloading images:", error);
    // Return any successfully loaded images
    return loadPromises.filter(p => p.status === 'fulfilled').map(p => p.value);
  }
};

/**
 * Convert a file object to a data URL
 * 
 * @param {File} file - File object
 * @returns {Promise<string>} Promise resolving to data URL
 */
export const fileToDataUrl = (file) => {
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (e) => reject(e);
      reader.readAsDataURL(file);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Display an image viewer modal
 * 
 * @param {Array} images - Array of image URLs or base64 strings
 * @param {number} initialIndex - Initial image index to display
 * @returns {HTMLElement} Created viewer element
 */
export const showImageViewer = (images, initialIndex = 0) => {
  if (!images || images.length === 0) {
    console.error("No images to display");
    return null;
  }
  
  try {
    // Create viewer container
    const viewer = document.createElement('div');
    viewer.className = 'image-viewer';
    viewer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2000;
    `;
    
    // Current image index
    let currentIndex = initialIndex >= 0 && initialIndex < images.length ? initialIndex : 0;
    
    // Generate inner HTML
    viewer.innerHTML = `
      <div class="image-viewer-content" style="position: relative; max-width: 90%; max-height: 90%;">
        <button class="image-viewer-close" style="position: absolute; top: -30px; right: 0; color: white; background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
        <img src="${images[currentIndex]}" alt="Enlarged image" style="max-width: 100%; max-height: 90vh; border: 2px solid white;">
        ${images.length > 1 ? `
          <div style="position: absolute; top: 0; left: 0; right: 0; display: flex; justify-content: space-between; padding: 10px;">
            <div style="background-color: rgba(0, 0, 0, 0.5); color: white; padding: 5px 10px; border-radius: 10px; font-size: 14px;">
              ${currentIndex + 1} / ${images.length}
            </div>
          </div>
          <button class="image-viewer-prev" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); background-color: rgba(0, 0, 0, 0.5); color: white; border: none; border-radius: 50%; width: 40px; height: 40px; font-size: 20px; cursor: pointer;">←</button>
          <button class="image-viewer-next" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background-color: rgba(0, 0, 0, 0.5); color: white; border: none; border-radius: 50%; width: 40px; height: 40px; font-size: 20px; cursor: pointer;">→</button>
        ` : ''}
      </div>
    `;
    
    // Add to DOM
    document.body.appendChild(viewer);
    
    // Add event listeners
    viewer.querySelector('.image-viewer-close').addEventListener('click', () => {
      document.body.removeChild(viewer);
    });
    
    // Background click closes viewer
    viewer.addEventListener('click', (e) => {
      if (e.target === viewer) {
        document.body.removeChild(viewer);
      }
    });
    
    // Navigation buttons for multiple images
    if (images.length > 1) {
      // Previous button
      viewer.querySelector('.image-viewer-prev').addEventListener('click', (e) => {
        e.stopPropagation();
        currentIndex = (currentIndex - 1 + images.length) % images.length;
        viewer.querySelector('img').src = images[currentIndex];
        viewer.querySelector('div > div').textContent = `${currentIndex + 1} / ${images.length}`;
      });
      
      // Next button
      viewer.querySelector('.image-viewer-next').addEventListener('click', (e) => {
        e.stopPropagation();
        currentIndex = (currentIndex + 1) % images.length;
        viewer.querySelector('img').src = images[currentIndex];
        viewer.querySelector('div > div').textContent = `${currentIndex + 1} / ${images.length}`;
      });
      
      // Keyboard navigation
      const handleKeydown = (e) => {
        if (e.key === 'ArrowLeft') {
          currentIndex = (currentIndex - 1 + images.length) % images.length;
          viewer.querySelector('img').src = images[currentIndex];
          viewer.querySelector('div > div').textContent = `${currentIndex + 1} / ${images.length}`;
        } else if (e.key === 'ArrowRight') {
          currentIndex = (currentIndex + 1) % images.length;
          viewer.querySelector('img').src = images[currentIndex];
          viewer.querySelector('div > div').textContent = `${currentIndex + 1} / ${images.length}`;
        } else if (e.key === 'Escape') {
          document.body.removeChild(viewer);
          document.removeEventListener('keydown', handleKeydown);
        }
      };
      
      document.addEventListener('keydown', handleKeydown);
      
      // Clean up event listener on close
      viewer.querySelector('.image-viewer-close').addEventListener('click', () => {
        document.removeEventListener('keydown', handleKeydown);
      });
    }
    
    return viewer;
  } catch (error) {
    console.error("Error showing image viewer:", error);
    return null;
  }
};