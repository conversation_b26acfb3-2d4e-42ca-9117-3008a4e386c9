import React, { useContext, useEffect, useRef } from 'react';
import { MapContext } from '../MapContext';
import L from 'leaflet';

const NavigationControl = () => {
  const {
    mapRef,
    currentLocation,
    selectedLocation,
    isNavigating,
    setIsNavigating,
    optimizedRoute,
    isFollowingOptimizedRoute,
    setIsFollowingOptimizedRoute,
    setSelectedLocation,
    setDetailsPanelLocation,
    setDetailsVisible
  } = useContext(MapContext);
  
  // Local state refs for managing routing UI
  const routingControlRef = useRef(null);
  const navigationElementsRef = useRef([]);
  const routeLineRef = useRef(null);
  
  // Clean up effect when component unmounts
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, []);
  
  // Effect for navigation state
  useEffect(() => {
    if (!mapRef.current || !isNavigating || !selectedLocation || !currentLocation) {
      return;
    }
    
    // Calculate and display the road-based route
    calculateAndDisplayRoute(currentLocation, selectedLocation.position);
    
    // Clean up function
    return () => {
      cleanup();
    };
  }, [isNavigating, selectedLocation, currentLocation]);
  
  // Function to calculate and display route
  const calculateAndDisplayRoute = (origin, destination) => {
    // Clean up existing controls
    cleanup();
    
    // Create routing control
    try {
      if (!mapRef.current) return;
      
      // Create a basic straight line between points if routing is not available
      if (!L.Routing || !L.Routing.control) {
        createDirectRoute(origin, destination);
        return;
      }
      
      // Create routing control if available
      const routingControl = L.Routing.control({
        waypoints: [
          L.latLng(origin.lat, origin.lng),
          L.latLng(destination.lat, destination.lng)
        ],
        routeWhileDragging: false,
        showAlternatives: false,
        lineOptions: {
          styles: [
            {color: '#3B82F6', opacity: 0.8, weight: 5}
          ],
          addWaypoints: false,
          extendToWaypoints: true,
          missingRouteTolerance: 0
        },
        createMarker: function() { return null; }, // Suppress default markers
        fitSelectedRoutes: false
      }).addTo(mapRef.current);
      
      routingControlRef.current = routingControl;
      
      // Listen for route calculation
      routingControl.on('routesfound', function(e) {
        try {
          const routes = e.routes;
          const summary = routes[0].summary;
          
          // Extract useful information
          const distance = (summary.totalDistance / 1609.34).toFixed(1); // convert meters to miles
          const duration = formatTime(summary.totalTime);
          const steps = routes[0].instructions || [];
          
          // Create navigation panel with information
          createNavigationPanel({
            distance,
            duration,
            instructions: steps,
            destination
          });
          
        } catch (err) {
          console.error("Error processing route:", err);
          createDirectRoute(origin, destination);
        }
      });
      
      // Handle routing errors
      routingControl.on('routingerror', function(e) {
        console.error("Routing error:", e);
        createDirectRoute(origin, destination);
      });
      
    } catch (err) {
      console.error("Error creating routing control:", err);
      createDirectRoute(origin, destination);
    }
  };
  
  // Create a direct straight-line route when routing service fails
  const createDirectRoute = (origin, destination) => {
    if (!mapRef.current) return;
    
    try {
      // Calculate straight-line distance
      const distance = calculateDistance(origin, destination);
      
      // Draw a straight line
      const routeLine = L.polyline([
        [origin.lat, origin.lng],
        [destination.lat, destination.lng]
      ], {
        color: '#3B82F6',
        weight: 5,
        opacity: 0.8,
        dashArray: '10, 10'
      }).addTo(mapRef.current);
      
      routeLineRef.current = routeLine;
      
      navigationElementsRef.current.push({
        remove: () => {
          if (routeLine && mapRef.current) {
            routeLine.remove();
          }
        }
      });
      
      // Create navigation panel with fallback info
      const direction = getDirectionText(calculateBearing(origin, destination));
      const directionArrow = getDirectionArrow(calculateBearing(origin, destination));
      
      createNavigationPanel({
        distance: distance.toFixed(1),
        duration: formatTime(distance / 40 * 3600), // Roughly estimate travel time at 40mph
        instructions: [{ text: `Head ${direction} toward destination` }],
        directionArrow,
        destination
      });
      
      // Fit bounds to show the route
      mapRef.current.fitBounds([
        [origin.lat, origin.lng],
        [destination.lat, destination.lng]
      ], { padding: [50, 50] });
      
    } catch (err) {
      console.error("Error creating direct route:", err);
    }
  };
  
  // Create navigation panel with route information
  const createNavigationPanel = (routeInfo) => {
    if (!mapRef.current || !selectedLocation) return;
    
    const navPanel = document.createElement('div');
    navPanel.className = 'navigation-panel dark-mode';
    
    // Determine image to show
    let imageUrl = '';
    if (selectedLocation && selectedLocation.images && selectedLocation.images.length > 0) {
      imageUrl = selectedLocation.images[0];
    }
    
    // Determine parking side info
    let parkingSideHtml = '';
    if (selectedLocation.parkingSide) {
      const side = selectedLocation.parkingSide.charAt(0).toUpperCase() + selectedLocation.parkingSide.slice(1);
      const arrow = selectedLocation.parkingSide === 'left' ? '←' : '→';
      parkingSideHtml = `
        <div class="parking-side-indicator">
          Vehicle Parked on ${side} Side ${arrow}
        </div>
      `;
    }
    
    // Status indicator
    let statusHtml = '';
    if (selectedLocation.status === 'picked-up') {
      statusHtml = `<div class="parking-side-indicator" style="background-color: #10B981;">
        Picked Up
      </div>`;
    }
    
    // Vehicle info section
    let vehicleInfoHtml = '';
    if (selectedLocation.make || selectedLocation.model || selectedLocation.plateNumber) {
      vehicleInfoHtml = `
        <div class="vehicle-info" style="font-size: 10px; margin-top: 3px; margin-bottom: 3px;">
          ${selectedLocation.make && selectedLocation.model ? `${selectedLocation.make} ${selectedLocation.model}` : ''}
          ${selectedLocation.plateNumber ? `• Plate: ${selectedLocation.plateNumber}` : ''}
        </div>
      `;
    }
    
    // Generate HTML for the panel
    navPanel.innerHTML = `
      <div class="navigation-image">
        ${imageUrl ? `<img src="${imageUrl}" alt="Destination" style="width:100%; height:100%; object-fit:cover;">` : 
        `<div style="color: #9CA3AF; text-align: center;">No image available</div>`}
      </div>
      <div class="navigation-details">
        ${parkingSideHtml}
        ${statusHtml}
        <div class="navigation-destination">${selectedLocation.name || 'Destination'}</div>
        ${vehicleInfoHtml}
        <div class="navigation-distance-time">
          <div>${routeInfo.distance} miles</div>
          <div>${routeInfo.duration}</div>
        </div>
        <div class="navigation-direction">
          <div class="navigation-direction-arrow">${routeInfo.directionArrow || '→'}</div>
          <div>${routeInfo.instructions[0]?.text || 'Proceed to destination'}</div>
        </div>
        
        <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm w-full mt-2">
          Stop Navigation
        </button>
      </div>
    `;
    
    // Add the panel to the map container
    const mapContainer = mapRef.current.getContainer();
    mapContainer.appendChild(navPanel);
    
    // Add event listener to stop button
    const stopButton = navPanel.querySelector('button');
    stopButton.addEventListener('click', stopNavigation);
    
    // Save reference for cleanup
    navigationElementsRef.current.push({
      remove: () => {
        if (navPanel.parentNode) {
          navPanel.parentNode.removeChild(navPanel);
        }
      }
    });
  };
  
  // Stop navigation function
  const stopNavigation = () => {
    // Clean up all routing elements
    cleanup();
    
    // Reset navigation state
    setIsNavigating(false);
    setIsFollowingOptimizedRoute(false);
  };
  
  // Cleanup function for routing elements
  const cleanup = () => {
    // Clean up routing control
    if (routingControlRef.current && mapRef.current) {
      try {
        mapRef.current.removeControl(routingControlRef.current);
      } catch (err) {
        console.warn("Error removing routing control:", err);
      }
      routingControlRef.current = null;
    }
    
    // Clean up direct route line
    if (routeLineRef.current && mapRef.current) {
      try {
        routeLineRef.current.remove();
      } catch (err) {
        console.warn("Error removing route line:", err);
      }
      routeLineRef.current = null;
    }
    
    // Clean up navigation elements
    navigationElementsRef.current.forEach(element => {
      if (element && element.remove) {
        try {
          element.remove();
        } catch (err) {
          console.warn("Error removing navigation element:", err);
        }
      }
    });
    navigationElementsRef.current = [];
  };
  
  // Helper function to calculate distance
  const calculateDistance = (point1, point2) => {
    const R = 3958.8; // Earth's radius in miles
    const φ1 = point1.lat * Math.PI/180;
    const φ2 = point2.lat * Math.PI/180;
    const Δφ = (point2.lat-point1.lat) * Math.PI/180;
    const Δλ = (point2.lng-point1.lng) * Math.PI/180;
    
    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    
    return R * c; // in miles
  };
  
  // Helper function to format time
  const formatTime = (seconds) => {
    if (seconds < 60) {
      return `${Math.round(seconds)} seconds`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)} minutes`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.round((seconds % 3600) / 60);
      return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }
  };
  
  // Calculate bearing between two points (in degrees)
  const calculateBearing = (start, end) => {
    const startLat = start.lat * Math.PI / 180;
    const startLng = start.lng * Math.PI / 180;
    const endLat = end.lat * Math.PI / 180;
    const endLng = end.lng * Math.PI / 180;
  
    const y = Math.sin(endLng - startLng) * Math.cos(endLat);
    const x = Math.cos(startLat) * Math.sin(endLat) -
              Math.sin(startLat) * Math.cos(endLat) * Math.cos(endLng - startLng);
    
    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360; // Normalize to 0-360
  };
  
  // Get direction text from bearing
  const getDirectionText = (bearing) => {
    const directions = ['North', 'Northeast', 'East', 'Southeast', 'South', 'Southwest', 'West', 'Northwest'];
    const index = Math.round(bearing / 45) % 8;
    return directions[index];
  };
  
  // Helper function to get an appropriate arrow based on bearing
  const getDirectionArrow = (bearing) => {
    // Map bearing to one of 8 cardinal directions with corresponding arrows
    const directions = [
      { min: 337.5, max: 360, arrow: '⬆️' }, // North
      { min: 0, max: 22.5, arrow: '⬆️' },    // North
      { min: 22.5, max: 67.5, arrow: '↗️' },  // Northeast
      { min: 67.5, max: 112.5, arrow: '➡️' }, // East
      { min: 112.5, max: 157.5, arrow: '↘️' }, // Southeast
      { min: 157.5, max: 202.5, arrow: '⬇️' }, // South
      { min: 202.5, max: 247.5, arrow: '↙️' }, // Southwest
      { min: 247.5, max: 292.5, arrow: '⬅️' }, // West
      { min: 292.5, max: 337.5, arrow: '↖️' }  // Northwest
    ];
    
    // Find the direction that matches the bearing
    for (const dir of directions) {
      if ((bearing >= dir.min && bearing < dir.max) || 
          (dir.min > dir.max && (bearing >= dir.min || bearing < dir.max))) {
        return dir.arrow;
      }
    }
    
    return '⬆️'; // Default to North if something goes wrong
  };

  // When using optimized route navigation
  const moveToNextOptimizedLocation = () => {
    if (!isFollowingOptimizedRoute || !optimizedRoute || !selectedLocation) {
      return;
    }
    
    const currentIndex = optimizedRoute.findIndex(item => 
      item.location.id === selectedLocation.id);
      
    if (currentIndex !== -1 && currentIndex < optimizedRoute.length - 1) {
      // Move to next location in optimized route
      const nextLocation = optimizedRoute[currentIndex + 1].location;
      setSelectedLocation(nextLocation);
      setDetailsPanelLocation(nextLocation);
      setDetailsVisible(true);
    } else if (currentIndex === optimizedRoute.length - 1) {
      // We've reached the last location
      setIsFollowingOptimizedRoute(false);
    }
  };

  // This component doesn't render any visible UI directly
  return null;
};

export default NavigationControl;