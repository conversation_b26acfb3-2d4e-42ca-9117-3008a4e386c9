import React, { useRef, useEffect, useState, useCallback, memo } from 'react';
import { addDoc, collection, doc, deleteDoc, serverTimestamp, getDocs, writeBatch } from 'firebase/firestore';
import ReactDOM from 'react-dom';
import { db } from './firebase'; // UPDATED - Import from consolidated firebase.js

// Use React.memo to prevent unnecessary re-renders
const ChatInterface = memo(({ 
  chatMessages, 
  setChatMessages, 
  newChatMessage, 
  setNewChatMessage, 
  currentUser, 
  mediaToUpload,
  setMediaToUpload,
  userProfilePictures,
  openImageViewer,
  teamId // Added teamId parameter
}) => {
  const chatMessagesRef = useRef(null);
  const inputRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false);
  const [viewingImage, setViewingImage] = useState(null);
  const [isCompressing, setIsCompressing] = useState(false);
  const [cachedProfilePics, setCachedProfilePics] = useState({});
  const prevTeamIdRef = useRef(teamId);
  const [isDeletingAll, setIsDeletingAll] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  
  // Debug log for component render states - use a ref to avoid triggering re-renders
  const renderCountRef = useRef(0);
  
  // Helper function to get consistent user ID from different user object structures
  const getUserId = useCallback((user) => {
    if (!user) return null;
    return user.uid || user.id || null;
  }, []);
  
  // Helper function to get consistent user display name
  const getUserDisplayName = useCallback((user) => {
    if (!user) return 'Unknown User';
    return user.displayName || user.name || user.email?.split('@')[0] || 'User';
  }, []);
  
  // Helper function to get consistent user email
  const getUserEmail = useCallback((user) => {
    if (!user) return null;
    return user.email || null;
  }, []);
  
  useEffect(() => {
    renderCountRef.current++;
    console.log(`ChatInterface render #${renderCountRef.current} for team: ${teamId}`, {
      messagesCount: chatMessages?.length || 0,
      isLoading,
      currentUser: currentUser ? {
        id: getUserId(currentUser),
        email: getUserEmail(currentUser),
        displayName: getUserDisplayName(currentUser),
        originalStructure: Object.keys(currentUser)
      } : null
    });
  });
  
  // Check for team change and reset states if needed
  useEffect(() => {
    if (prevTeamIdRef.current !== teamId) {
      console.log(`Team changed from ${prevTeamIdRef.current} to ${teamId}, resetting states`);
      setIsLoading(true);
      setCachedProfilePics({});
      prevTeamIdRef.current = teamId;
    }
  }, [teamId]);
  
  // Memoize the scroll function to prevent recreation
  const scrollToBottom = useCallback(() => {
    if (chatMessagesRef.current) {
      chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
    }
  }, []);
  
  // Improved preload profile picture - memoized with useCallback
  const preloadProfilePicture = useCallback((userId) => {
    // Skip if we've already tried to load this user's profile picture
    if (cachedProfilePics[userId]) return;
    
    const profilePicUrl = userProfilePictures && userProfilePictures[userId];
    
    if (profilePicUrl) {
      // Create a new image to preload but avoid state changes during loading
      const img = new Image();
      
      img.onload = () => {
        setCachedProfilePics(prev => ({
          ...prev,
          [userId]: {
            url: profilePicUrl,
            status: 'loaded'
          }
        }));
      };
      
      img.onerror = () => {
        setCachedProfilePics(prev => ({
          ...prev,
          [userId]: {
            url: null,
            status: 'error'
          }
        }));
      };
      
      img.src = profilePicUrl;
      
      // Mark as loading without causing a re-render for each user
      setCachedProfilePics(prev => {
        // Only update if not already set
        if (prev[userId]) return prev;
        
        return {
          ...prev,
          [userId]: {
            url: profilePicUrl,
            status: 'loading'
          }
        };
      });
    }
  }, [cachedProfilePics, userProfilePictures]);
  
  // Effect for handling chat messages - optimized
  useEffect(() => {
    // Skip if no messages or teamId
    if (!chatMessages || !teamId) return;
    
    // Set loading to false when we have messages
    setIsLoading(false);
    
    // Use a Set to handle unique user IDs efficiently
    const uniqueUserIds = new Set();
    
    // Collect unique user IDs
    chatMessages.forEach(message => {
      const senderId = getUserId(message.sender);
      if (senderId) {
        uniqueUserIds.add(senderId);
      }
    });
    
    // Batch preload all profile pictures - use setTimeout to avoid blocking the render
    if (uniqueUserIds.size > 0) {
      const timer = setTimeout(() => {
        uniqueUserIds.forEach(uid => preloadProfilePicture(uid));
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [chatMessages, teamId, preloadProfilePicture, getUserId]);
  
  // Effect for scrolling to bottom when messages change
  useEffect(() => {
    if (!chatMessages || chatMessages.length === 0) return;
    
    // Scroll to bottom after a short delay to ensure render is complete
    const timer = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timer);
  }, [chatMessages, scrollToBottom]);
  
  // Memoized submission handler with better user validation
  const handleChatSubmit = useCallback(async (e) => {
    e.preventDefault();
    
    // Enhanced validation with better error messages
    if (!newChatMessage.trim() && !mediaToUpload) {
      console.log('No message content to send');
      return;
    }
    
    if (!currentUser) {
      console.error('No current user available', currentUser);
      alert('Please sign in to send messages');
      return;
    }
    
    if (!teamId) {
      console.error('No team ID available', teamId);
      alert('Please select a team to send messages');
      return;
    }
    
    const userId = getUserId(currentUser);
    if (!userId) {
      console.error('Invalid user ID. User object:', currentUser, 'Extracted ID:', userId);
      alert('Invalid user session. Please refresh the page and sign in again.');
      return;
    }
    
    console.log('🎯 All validation passed, proceeding with message send:', {
      hasMessage: !!newChatMessage.trim(),
      hasMedia: !!mediaToUpload,
      userId,
      teamId,
      userObject: currentUser
    });

    try {
      // Get profile photo if available
      const photo = userProfilePictures && userId ? 
        userProfilePictures[userId] || null : null;
      
      // Get display name
      const displayName = getUserDisplayName(currentUser);
      
      // Create message data with proper user ID
      const messageData = {
        text: newChatMessage,
        teamId: teamId, // Store teamId in the message
        sender: {
          uid: userId, // Use the extracted user ID
          name: displayName,
          photo: photo
        },
        timestamp: serverTimestamp()
      };
      
      console.log('Sending message with user data:', {
        userId,
        displayName,
        teamId,
        messageText: newChatMessage.substring(0, 50) + '...'
      });
      
      // Process media if present
      if (mediaToUpload) {
        setIsCompressing(true);
        
        try {
          let mediaUrl = mediaToUpload.data;
          
          // If it's an image, compress it
          if (mediaToUpload.type === 'image') {
            // First attempt with high compression
            let compressedImage = await compressImage(mediaToUpload.data, 800, 800, 0.6);
            
            // Check if still too large (> 900KB to be safe)
            let sizeKB = Math.round((compressedImage.length * 3) / 4 / 1024);
            
            // If still too large, compress more aggressively
            if (sizeKB > 900) {
              compressedImage = await compressImage(mediaToUpload.data, 600, 600, 0.4);
              sizeKB = Math.round((compressedImage.length * 3) / 4 / 1024);
              
              // If still too large, try one last time
              if (sizeKB > 900) {
                compressedImage = await compressImage(mediaToUpload.data, 400, 400, 0.3);
                sizeKB = Math.round((compressedImage.length * 3) / 4 / 1024);
                
                // If still too large, we can't send it
                if (sizeKB > 900) {
                  throw new Error("Image is too large even after compression.");
                }
              }
            }
            
            mediaUrl = compressedImage;
          }
          
          messageData.mediaUrl = mediaUrl;
          messageData.mediaType = mediaToUpload.type;
        } finally {
          setIsCompressing(false);
        }
      }
      
      // Add to team-specific Firestore collection
      await addDoc(collection(db, 'teams', teamId, 'chatMessages'), messageData);
      console.log(`Message sent successfully to team ${teamId}`);
      
      // Clear the input
      setNewChatMessage('');
      setMediaToUpload(null);
      
      // Focus back on input
      if (inputRef.current) {
        inputRef.current.focus();
      }
      
      // Scroll to bottom after sending
      setTimeout(scrollToBottom, 100);
      
    } catch (err) {
      console.error("Error sending chat message:", err);
      
      // Show user-friendly error message
      if (err.message && err.message.includes("longer than")) {
        alert("The message or attachment is too large to send. Please try using a smaller image.");
      } else {
        alert("Failed to send message. Please try again.");
      }
      
      // Fallback: Add message locally if Firestore fails
      const newMessage = {
        id: Date.now().toString(),
        text: newChatMessage,
        teamId: teamId,
        sender: {
          uid: userId,
          name: getUserDisplayName(currentUser),
          photo: userProfilePictures && userId ? userProfilePictures[userId] || null : null
        },
        timestamp: new Date(),
        mediaUrl: mediaToUpload?.data || null,
        mediaType: mediaToUpload?.type || null,
        local: true
      };
      
      setChatMessages(prev => [...(prev || []), newMessage]);
      setNewChatMessage('');
      setMediaToUpload(null);
      
      // Focus back on input
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  }, [newChatMessage, mediaToUpload, currentUser, teamId, userProfilePictures, setChatMessages, setNewChatMessage, setMediaToUpload, scrollToBottom, getUserId, getUserDisplayName]);
  
  // Memoized delete message handler
  const handleDeleteMessage = useCallback(async (messageId) => {
    try {
      if (!teamId) {
        console.error("Cannot delete message: teamId is missing");
        return;
      }
      
      // Delete from team-specific Firestore collection
      await deleteDoc(doc(db, 'teams', teamId, 'chatMessages', messageId));
      console.log(`Message deleted successfully from team ${teamId}`);
      
      // Update local state
      setChatMessages(prev => (prev || []).filter(message => message.id !== messageId));
    } catch (err) {
      console.error(`Error deleting message from team ${teamId}:`, err);
    }
  }, [teamId, setChatMessages]);
  
  // Delete ALL messages handler
  const handleDeleteAllMessages = useCallback(async () => {
    if (!teamId || !db) {
      console.error("Cannot delete messages: teamId or db is missing");
      return;
    }

    setIsDeletingAll(true);
    
    try {
      console.log(`🗑️ Deleting all messages for team ${teamId}...`);
      
      // Get all messages in the collection
      const messagesSnapshot = await getDocs(collection(db, 'teams', teamId, 'chatMessages'));
      
      if (messagesSnapshot.empty) {
        console.log("No messages to delete");
        alert("No messages to delete");
        return;
      }

      // Use batch delete for better performance
      const batch = writeBatch(db);
      let deleteCount = 0;

      messagesSnapshot.docs.forEach((messageDoc) => {
        batch.delete(messageDoc.ref);
        deleteCount++;
      });

      // Commit the batch delete
      await batch.commit();
      
      console.log(`✅ Successfully deleted ${deleteCount} messages from team ${teamId}`);
      
      // Clear local state immediately
      setChatMessages([]);
      
      alert(`Successfully deleted ${deleteCount} messages!`);
      
    } catch (error) {
      console.error(`❌ Error deleting all messages from team ${teamId}:`, error);
      alert("Error deleting messages. Please try again.");
    } finally {
      setIsDeletingAll(false);
      setShowDeleteConfirm(false);
    }
  }, [teamId, setChatMessages]);

  // Confirm delete all messages
  const confirmDeleteAllMessages = useCallback(() => {
    if (!chatMessages || chatMessages.length === 0) {
      alert("No messages to delete");
      return;
    }
    
    setShowDeleteConfirm(true);
  }, [chatMessages]);
  
  // Image compression function
  const compressImage = (imgDataUrl, maxWidth = 800, maxHeight = 800, quality = 0.6) => {
    return new Promise((resolve, reject) => {
      try {
        // Create an image object from the data URL
        const img = new Image();
        img.onload = () => {
          // Calculate new dimensions while maintaining aspect ratio
          let width = img.width;
          let height = img.height;
          
          if (width > height) {
            if (width > maxWidth) {
              height = Math.round(height * (maxWidth / width));
              width = maxWidth;
            }
          } else {
            if (height > maxHeight) {
              width = Math.round(width * (maxHeight / height));
              height = maxHeight;
            }
          }
          
          // Create a canvas and draw the resized image
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);
          
          // Get the compressed data URL
          const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
          
          // Log size reduction stats
          const originalSizeKB = Math.round((imgDataUrl.length * 3) / 4 / 1024);
          const compressedSizeKB = Math.round((compressedDataUrl.length * 3) / 4 / 1024);
          console.log(`Image compressed: ${originalSizeKB}KB → ${compressedSizeKB}KB (${Math.round((1 - compressedSizeKB / originalSizeKB) * 100)}% reduction)`);
          
          resolve(compressedDataUrl);
        };
        
        img.onerror = () => {
          reject(new Error('Failed to load image for compression'));
        };
        
        img.src = imgDataUrl;
      } catch (error) {
        reject(error);
      }
    });
  };
  
  // Memoized media upload handler
  const handleChatMediaUpload = useCallback((mediaType) => {
    try {
      const input = document.createElement('input');
      input.type = 'file';
      
      if (mediaType === 'image') {
        input.accept = 'image/*';
      } else if (mediaType === 'video') {
        input.accept = 'video/*';
      }
      
      input.onchange = async (e) => {
        if (e.target.files && e.target.files[0]) {
          try {
            const file = e.target.files[0];
            
            // Check file size (reject if too large for any reasonable compression)
            if (file.size > 8 * 1024 * 1024) { // 8MB max for initial size
              alert("File is too large. Please select an image smaller than 8MB.");
              return;
            }
            
            const reader = new FileReader();
            
            reader.onload = (event) => {
              setMediaToUpload({
                type: mediaType,
                data: event.target.result,
                file: file
              });
              
              // Focus back on input after upload
              if (inputRef.current) {
                inputRef.current.focus();
              }
            };
            
            reader.readAsDataURL(file);
          } catch (err) {
            console.error("Error processing media:", err);
            alert("Error processing media. Please try again with a different file.");
          }
        }
      };
      
      input.click();
    } catch (err) {
      console.error("Error handling media upload:", err);
    }
  }, []);

  // Native camera handler - uses device's default camera app
  const handleNativeCamera = useCallback(() => {
    try {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.capture = 'environment'; // This triggers the device's camera app
      
      input.onchange = async (e) => {
        if (e.target.files && e.target.files[0]) {
          try {
            const file = e.target.files[0];
            
            // Check file size (reject if too large for any reasonable compression)
            if (file.size > 8 * 1024 * 1024) { // 8MB max for initial size
              alert("Photo is too large. Please try taking a photo with lower resolution.");
              return;
            }
            
            const reader = new FileReader();
            
            reader.onload = (event) => {
              setMediaToUpload({
                type: 'image',
                data: event.target.result,
                file: file
              });
              
              // Focus back on input after upload
              if (inputRef.current) {
                inputRef.current.focus();
              }
            };
            
            reader.readAsDataURL(file);
          } catch (err) {
            console.error("Error processing camera photo:", err);
            alert("Error processing photo. Please try again.");
          }
        }
      };
      
      input.click();
    } catch (err) {
      console.error("Error opening camera:", err);
      alert("Error opening camera. Please try again.");
    }
  }, []);
  
  // Extracted ImageViewer component with memo for performance
  const ImageViewer = memo(({ src, onClose }) => {
    useEffect(() => {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'auto';
      };
    }, []);
    
    return ReactDOM.createPortal(
      <div 
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.85)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 99999
        }}
        onClick={onClose}
      >
        <div style={{ position: 'relative', maxWidth: '90%', maxHeight: '90%' }}>
          <img 
            src={src} 
            alt="Enlarged" 
            style={{ maxWidth: '100%', maxHeight: '90vh', objectFit: 'contain' }}
          />
          <button 
            style={{
              position: 'absolute',
              top: '10px',
              right: '10px',
              backgroundColor: '#ef4444',
              color: 'white',
              borderRadius: '9999px',
              width: '32px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '20px',
              border: 'none',
              cursor: 'pointer'
            }}
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
          >
            ×
          </button>
        </div>
      </div>,
      document.body
    );
  });
  
  // Helper function to get initials from name
  const getInitials = (name) => {
    if (!name) return "?";
    return name.charAt(0).toUpperCase();
  };
  
  // Helper function to generate color based on user ID
  const getUserColor = (userId) => {
    if (!userId) return "bg-gray-600";
    
    const colorIndex = userId.charCodeAt(0) % 8;
    const bgColors = [
      'bg-blue-600', 'bg-purple-600', 'bg-green-600', 
      'bg-red-600', 'bg-yellow-600', 'bg-pink-600',
      'bg-indigo-600', 'bg-teal-600'
    ];
    return bgColors[colorIndex];
  };
  
  // Memoized profile picture renderer
  const renderProfilePicture = useCallback((sender) => {
    const senderId = getUserId(sender);
    const userName = getUserDisplayName(sender);
    
    // Generate initials and color as fallback
    const initials = getInitials(userName);
    const avatarColor = getUserColor(senderId);
    
    // Check for profile pic in different sources
    const profilePic = 
      (sender?.photo) || 
      (senderId && userProfilePictures?.[senderId]) ||
      (senderId && cachedProfilePics[senderId]?.status === 'loaded' && cachedProfilePics[senderId]?.url);
    
    if (!profilePic) {
      // No profile picture found, use initials
      return (
        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold ${avatarColor}`}>
          {initials}
        </div>
      );
    }
    
    // Has profile picture
    return (
      <div className={`w-6 h-6 rounded-full overflow-hidden relative ${avatarColor}`}>
        <img
          src={profilePic}
          alt={userName}
          className="w-full h-full object-cover"
          onClick={() => setViewingImage(profilePic)}
          style={{ cursor: 'pointer' }}
          onError={(e) => {
            e.target.style.display = 'none';
            e.target.nextElementSibling.style.display = 'flex';
          }}
        />
        <div 
          className="w-full h-full absolute top-0 left-0 flex items-center justify-center text-white text-xs font-bold"
          style={{ display: 'none' }}
        >
          {initials}
        </div>
      </div>
    );
  }, [cachedProfilePics, userProfilePictures, getUserId, getUserDisplayName]);
  
  // Memoized chat message renderer with improved user ID handling
  const renderChatMessage = useCallback((message) => {
    const currentUserId = getUserId(currentUser);
    const messageSenderId = getUserId(message.sender);
    const isCurrentUser = currentUserId && messageSenderId && messageSenderId === currentUserId;
    
    return (
      <div 
        key={message.id} 
        className={`mb-2 p-2 rounded ${isCurrentUser ? 'bg-blue-900 ml-auto mr-2 max-w-[80%]' : 'bg-gray-800 mr-auto ml-2 max-w-[80%]'}`}
      >
        <div className="flex items-center mb-1">
          <div className="mr-2 flex-shrink-0">
            {renderProfilePicture(message.sender)}
          </div>
          <div className="text-sm font-medium text-gray-300 truncate flex-grow">
            {getUserDisplayName(message.sender)}
          </div>
          
          {/* Delete button - only visible to the message author */}
          {isCurrentUser && !message.local && (
            <button
              onClick={() => handleDeleteMessage(message.id)}
              className="text-gray-400 hover:text-red-500 transition-colors duration-200 ml-2 p-1"
              title="Delete message"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          )}
        </div>
        
        <div className="text-white break-words">
          {message.text}
        </div>
        
        {/* Render media if present */}
        {message.mediaUrl && message.mediaType === 'image' && (
          <div className="mt-2">
            <img 
              src={message.mediaUrl} 
              alt="Shared" 
              onClick={() => setViewingImage(message.mediaUrl)}
              className="max-w-full h-auto rounded cursor-pointer"
            />
          </div>
        )}
        
        {message.mediaUrl && message.mediaType === 'video' && (
          <div className="mt-2">
            <video 
              src={message.mediaUrl} 
              controls
              className="max-w-full h-auto rounded"
            />
          </div>
        )}
        
        <div className="text-xs text-gray-400 mt-1 text-right">
          {message.local ? 'Sending...' : (
            message.timestamp ? (typeof message.timestamp.toDate === 'function' 
              ? message.timestamp.toDate().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) 
              : message.timestamp instanceof Date 
                ? message.timestamp.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
                : '') : ''
          )}
        </div>
      </div>
    );
  }, [currentUser, handleDeleteMessage, renderProfilePicture, getUserId, getUserDisplayName]);

  // Get current user ID for input validation
  const currentUserId = getUserId(currentUser);
  const isUserValid = Boolean(currentUserId);

  return (
    <div className="h-full flex flex-col">
      {/* Image viewer using portal to render outside normal DOM hierarchy */}
      {viewingImage && (
        <ImageViewer 
          src={viewingImage} 
          onClose={() => setViewingImage(null)} 
        />
      )}

      {/* Delete All Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900 rounded-xl p-6 max-w-md w-full border border-red-600 shadow-2xl">
            <h3 className="text-xl font-bold text-red-400 mb-4 flex items-center">
              <span className="mr-2">🗑️</span>
              Delete All Messages
            </h3>
            
            <div className="mb-6">
              <p className="text-gray-300 mb-3">
                Are you sure you want to delete <strong>ALL {chatMessages.length} messages</strong> in this team chat?
              </p>
              <div className="bg-red-900 bg-opacity-30 rounded-lg p-3 border border-red-600">
                <p className="text-red-300 text-sm">
                  <strong>⚠️ Warning:</strong> This action cannot be undone. All messages, images, and videos will be permanently deleted for everyone in the team.
                </p>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleDeleteAllMessages}
                disabled={isDeletingAll}
                className="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 rounded-lg font-bold shadow-lg transition-all flex items-center justify-center"
              >
                {isDeletingAll ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <span className="mr-2">🗑️</span>
                    Delete All Messages
                  </>
                )}
              </button>
              <button
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeletingAll}
                className="flex-1 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-700 text-white py-3 rounded-lg font-bold shadow-lg transition-all"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Messages area - scrollable */}
      <div 
        ref={chatMessagesRef}
        className="flex-grow overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 pb-2"
        style={{ display: 'flex', flexDirection: 'column' }}
      >
        {/* Delete All Messages Button - shown when there are messages */}
        {!isLoading && chatMessages && chatMessages.length > 0 && (
          <div className="flex justify-center p-2 border-b border-gray-700">
            <button
              onClick={confirmDeleteAllMessages}
              disabled={isDeletingAll}
              className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-xs font-bold transition-all flex items-center gap-1"
              title={`Delete all ${chatMessages.length} messages`}
            >
              <span>🗑️</span>
              Delete All ({chatMessages.length})
            </button>
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center items-center h-16 mt-4">
            <div className="animate-spin rounded-full h-6 w-6 border-2 border-t-transparent border-blue-500 mr-2"></div>
            <span className="text-gray-400">Loading messages...</span>
          </div>
        ) : (!chatMessages || chatMessages.length === 0) ? (
          <div className="flex justify-center items-center h-16 mt-4 text-gray-400">
            {!teamId ? (
              <span>Please select a team to see messages</span>
            ) : !isUserValid ? (
              <span>Please sign in to chat</span>
            ) : (
              <span>No messages yet. Start the conversation!</span>
            )}
          </div>
        ) : (
          <div className="pb-2 flex-grow">
            {chatMessages.map(message => renderChatMessage(message))}
          </div>
        )}
      </div>
      
      {/* Input area - fixed height */}
      <div className="flex-none border-t border-gray-700 bg-gray-800 p-1">
        {isCompressing && (
          <div className="text-xs text-blue-400 px-3 py-1">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-3 w-3 border border-t-transparent border-blue-400 mr-2"></div>
              Compressing image...
            </div>
          </div>
        )}
        
        <form onSubmit={handleChatSubmit} className="flex items-center">
          {mediaToUpload && (
            <div className="relative mx-1 my-1 bg-gray-700 rounded overflow-hidden flex-shrink-0" style={{width: '32px', height: '32px'}}>
              {mediaToUpload.type === 'image' && (
                <img 
                  src={mediaToUpload.data} 
                  alt="Upload" 
                  className="w-full h-full object-cover"
                />
              )}
              {mediaToUpload.type === 'video' && (
                <video 
                  src={mediaToUpload.data} 
                  className="w-full h-full object-cover"
                />
              )}
              <button
                type="button"
                className="absolute top-0 right-0 bg-red-500 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs z-10"
                onClick={() => setMediaToUpload(null)}
              >
                ×
              </button>
            </div>
          )}
          
          <input
            ref={inputRef}
            type="text"
            className="flex-grow bg-gray-700 text-white px-3 py-2 my-1 ml-1 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder={isUserValid && teamId ? "Type your message..." : isUserValid ? "Select a team to chat" : "Sign in to chat"}
            value={newChatMessage}
            onChange={(e) => setNewChatMessage(e.target.value)}
            disabled={!isUserValid || !teamId || isCompressing}
            autoComplete="off"
          />
          
          <button 
            type="button" 
            className="p-2 mx-1 my-1 bg-gray-600 text-white rounded hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => handleChatMediaUpload('image')}
            disabled={!isUserValid || !teamId || isCompressing}
            title="Upload image"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
          </button>
          <button 
            type="button" 
            className="p-2 my-1 bg-gray-600 text-white rounded hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleNativeCamera}
            disabled={!isUserValid || !teamId || isCompressing}
            title="Take photo with camera"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586l-.707-.707A1 1 0 0011 4H9a1 1 0 00-.707.293L7.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
            </svg>
          </button>
          <button 
            type="button" 
            className="p-2 my-1 bg-gray-600 text-white rounded hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => handleChatMediaUpload('video')}
            disabled={!isUserValid || !teamId || isCompressing}
            title="Upload video"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
              <path d="M14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
            </svg>
          </button>
          <button 
            type="submit" 
            className="px-4 py-2 my-1 ml-1 bg-blue-600 text-white rounded hover:bg-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={(!newChatMessage.trim() && !mediaToUpload) || !isUserValid || !teamId || isCompressing}
          >
            Send
          </button>
        </form>
      </div>
    </div>
  );
});

export default ChatInterface;