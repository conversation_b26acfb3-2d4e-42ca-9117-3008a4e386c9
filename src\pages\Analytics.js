import React, { useState, useEffect } from 'react';
import { getFirestore, collection, getDocs, doc, getDoc, query, where, orderBy, setDoc, updateDoc, writeBatch, serverTimestamp } from 'firebase/firestore';
import { useAuth } from '../contexts/AuthContext.js';
import { useNavigate } from 'react-router-dom';
import UserList from './UserList';
import UserDashboard from './UserDashboard';
import VehicleTracker from './VehicleTracker';
import TeamSummary from './TeamSummary';

function Analytics() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [teams, setTeams] = useState([]);
  const [selectedTeam, setSelectedTeam] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userStats, setUserStats] = useState(null);
  const [teamStats, setTeamStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userLoading, setUserLoading] = useState(false);
  const [error, setError] = useState(null);
  const [db, setDb] = useState(null);
  const [timeFilter, setTimeFilter] = useState('week');
  const [chartType, setChartType] = useState('line');
  const [searchTerm, setSearchTerm] = useState('');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [topPerformers, setTopPerformers] = useState({
    mostCars: null,
    mostEfficient: null,
    mostImproved: null
  });
  const [timeCardData, setTimeCardData] = useState(null);
  
  // Centralized Vehicle tracking state - single source of truth
  const [selectedWeek, setSelectedWeek] = useState(null);
  const [availableWeeks, setAvailableWeeks] = useState([]);
  const [vehicles, setVehicles] = useState([]);
  const [vehicleStats, setVehicleStats] = useState({
    totalScans: 0,
    totalFound: 0,
    totalSecured: 0,
    recoveryRate: 0,
    dateRange: { start: '', end: '' }
  });
  const [aggregatedVehicleData, setAggregatedVehicleData] = useState({
    month: { totalScans: 0, totalFound: 0, totalSecured: 0, recoveryRate: 0 },
    ytd: { totalScans: 0, totalFound: 0, totalSecured: 0, recoveryRate: 0 }
  });

  // ============ TEAM SYNCHRONIZATION FUNCTIONS ============

  // Function to mark VIN as secured across all team members
  const markVINAsSecuredAcrossTeam = async (teamId, securedByUserId, vin, securedDate, securedByUserName) => {
    if (!db || !teamId || !vin) return;

    try {
      console.log(`🔄 Marking VIN ${vin} as secured across team ${teamId} by user ${securedByUserId}`);
      
      // Get all team members
      const teamMembersCollection = collection(db, `teams/${teamId}/teamMembers`);
      const teamMembersSnapshot = await getDocs(teamMembersCollection);
      const memberIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);
      
      const batch = writeBatch(db);
      let updateCount = 0;
      let affectedUsers = [];
      
      // For each team member
      for (const userId of memberIds) {
        // Skip the user who already secured it
        if (userId === securedByUserId) continue;
        
        try {
          // Get all vehicle weeks for this user
          const weeksQuery = query(
            collection(db, 'users', userId, 'vehicleWeeks'),
            orderBy('startDate', 'desc')
          );
          
          const weeksSnapshot = await getDocs(weeksQuery);
          let userHasUpdates = false;
          
          // For each week
          for (const weekDoc of weeksSnapshot.docs) {
            const weekId = weekDoc.id;
            
            // Get vehicles in this week
            const vehiclesQuery = query(
              collection(db, 'users', userId, 'vehicleWeeks', weekId, 'vehicles')
            );
            
            const vehiclesSnapshot = await getDocs(vehiclesQuery);
            
            // Check each vehicle for matching VIN
            vehiclesSnapshot.forEach(vehicleDoc => {
              const vehicleData = vehicleDoc.data();
              const vehicleVin = vehicleData.vin || vehicleData.VIN;
              
              // If VIN matches and it's not already secured
              if (vehicleVin === vin && vehicleData.status !== 'SECURED') {
                console.log(`🔄 Updating vehicle ${vehicleData.vehicle} for user ${userId} in week ${weekId}`);
                
                batch.update(vehicleDoc.ref, {
                  status: 'SECURED',
                  securedDate: securedDate,
                  securedTimestamp: new Date(),
                  securedByTeammate: true,
                  securedByUserId: securedByUserId,
                  securedByUserName: securedByUserName,
                  autoSecuredFromTeam: true,
                  originalSecuredDate: vehicleData.securedDate,
                  teamSyncTimestamp: new Date(),
                  updatedAt: serverTimestamp()
                });
                
                updateCount++;
                userHasUpdates = true;
              }
            });
          }
          
          if (userHasUpdates) {
            affectedUsers.push(userId);
          }
        } catch (error) {
          console.error(`Error processing user ${userId}:`, error);
        }
      }
      
      if (updateCount > 0) {
        await batch.commit();
        console.log(`✅ Successfully updated ${updateCount} vehicles across team for VIN ${vin}`);
        
        // Update stats for affected users
        for (const userId of affectedUsers) {
          try {
            // Get user's weeks and update stats
            const weeksQuery = query(
              collection(db, 'users', userId, 'vehicleWeeks'),
              orderBy('startDate', 'desc')
            );
            
            const weeksSnapshot = await getDocs(weeksQuery);
            
            for (const weekDoc of weeksSnapshot.docs) {
              await updateVehicleWeekStatsForUser(userId, weekDoc.id);
            }
          } catch (error) {
            console.error(`Error updating stats for user ${userId}:`, error);
          }
        }
        
        // Trigger a refresh of current user's data if they're affected
        if (selectedUser && affectedUsers.includes(selectedUser.id)) {
          console.log("🔄 Refreshing current user data due to team sync");
          setTimeout(async () => {
            await loadVehicleDataForWeek(selectedUser.id, selectedWeek);
            
            // Update team stats as well
            if (selectedTeam) {
              fetchTeamStats(selectedTeam.id, selectedWeek);
            }
          }, 1000); // Small delay to ensure database changes are propagated
        }
      } else {
        console.log(`ℹ️ No matching vehicles found across team for VIN ${vin}`);
      }
      
    } catch (error) {
      console.error("❌ Error marking VIN as secured across team:", error);
    }
  };

  // Helper function to update vehicle week stats for a specific user
  const updateVehicleWeekStatsForUser = async (userId, weekId) => {
    if (!db) return;
    
    try {
      const vehiclesQuery = query(
        collection(db, 'users', userId, 'vehicleWeeks', weekId, 'vehicles')
      );
      
      const vehiclesSnapshot = await getDocs(vehiclesQuery);
      const vehiclesData = vehiclesSnapshot.docs.map(doc => doc.data());
      
      // Calculate stats (same logic as existing updateVehicleWeekStats)
      const totalFound = vehiclesData.filter(v => 
        !v.carriedOver && (v.status === 'FOUND' || v.status === 'SECURED')
      ).length;
      
      const totalSecured = vehiclesData.filter(v => v.status === 'SECURED').length;
      
      const totalCarryover = vehiclesData.filter(v => v.carriedOver).length;
      const totalAvailable = totalFound + totalCarryover;
      const recoveryRate = totalAvailable > 0 ? (totalSecured / totalAvailable) * 100 : 0;
      
      // Get current scan count
      const weekDoc = await getDoc(doc(db, 'users', userId, 'vehicleWeeks', weekId));
      const currentScans = weekDoc.exists() ? (weekDoc.data().totalScans || 0) : 0;
      
      // Update the week document
      await updateDoc(doc(db, 'users', userId, 'vehicleWeeks', weekId), {
        totalFound,
        totalSecured,
        recoveryRate,
        lastTeamSyncUpdate: new Date(),
        updatedAt: serverTimestamp()
      });
      
      console.log(`✅ Updated stats for user ${userId}, week ${weekId}: Found: ${totalFound}, Secured: ${totalSecured}, Rate: ${recoveryRate}%`);
      
    } catch (error) {
      console.error(`Error updating vehicle week stats for user ${userId}:`, error);
    }
  };

  // ============ CARRYOVER FUNCTIONS ============

  // Function to carry over unsecured vehicles from previous weeks
  const carryOverUnsecuredVehicles = async (userId, currentWeekId) => {
    if (!db || !userId || !currentWeekId) {
      console.log("❌ Missing required parameters for carryover");
      return [];
    }

    try {
      console.log(`🔄 Starting carryover process for user ${userId}, week ${currentWeekId}`);
      
      const vehicleWeeksRef = collection(db, 'users', userId, 'vehicleWeeks');
      const weeksSnapshot = await getDocs(vehicleWeeksRef);
      
      console.log(`📅 Found ${weeksSnapshot.docs.length} total weeks for user`);
      
      const unsecuredVehicles = [];
      const processedVINs = new Set();
      
      // Sort weeks by startDate to process chronologically
      const weekDocs = weeksSnapshot.docs
        .map(doc => ({
          id: doc.id,
          ...doc.data(),
          startDate: doc.data().startDate?.toDate()
        }))
        .filter(week => week.startDate)
        .sort((a, b) => a.startDate - b.startDate);
      
      console.log(`📅 Sorted weeks:`, weekDocs.map(w => ({ id: w.id, start: w.startDate })));
      
      // Find current week index
      const currentWeekIndex = weekDocs.findIndex(week => week.id === currentWeekId);
      
      if (currentWeekIndex === -1) {
        console.log("❌ Current week not found in sorted weeks");
        return [];
      }
      
      console.log(`📍 Current week index: ${currentWeekIndex} of ${weekDocs.length}`);
      
      // Process all weeks before the current week
      for (let i = 0; i < currentWeekIndex; i++) {
        const week = weekDocs[i];
        console.log(`🔍 Checking week ${i}: ${week.id} (${week.startDate})`);
        
        // Get vehicles for this week
        const vehiclesRef = collection(db, 'users', userId, 'vehicleWeeks', week.id, 'vehicles');
        const vehiclesSnapshot = await getDocs(vehiclesRef);
        
        console.log(`🚗 Found ${vehiclesSnapshot.docs.length} vehicles in week ${week.id}`);
        
        vehiclesSnapshot.forEach(vehicleDoc => {
          const vehicleData = vehicleDoc.data();
          const vin = vehicleData.vin || vehicleData.VIN;
          
          console.log(`🔍 Vehicle: ${vehicleData.vehicle}, Status: ${vehicleData.status}, VIN: ${vin}`);
          
          // Only process vehicles that are found but not secured, and not already processed
          if ((vehicleData.status === 'FOUND') && vin && !processedVINs.has(vin)) {
            console.log(`✅ Adding vehicle to carryover: ${vehicleData.vehicle} (${vin})`);
            
            unsecuredVehicles.push({
              ...vehicleData,
              id: vehicleDoc.id,
              originalWeek: week.id,
              originalWeekStart: week.startDate,
              carriedOver: true,
              carriedFromWeek: week.id,
              carriedFromDate: week.startDate,
              addedToCurrentWeek: new Date(),
              originalFoundDate: vehicleData.date
            });
            
            processedVINs.add(vin);
          } else {
            console.log(`❌ Skipping vehicle: Status=${vehicleData.status}, VIN=${vin}, AlreadyProcessed=${processedVINs.has(vin)}`);
          }
        });
      }
      
      console.log(`🎯 Found ${unsecuredVehicles.length} unsecured vehicles to carry over:`, unsecuredVehicles);
      return unsecuredVehicles;
      
    } catch (error) {
      console.error("❌ Error carrying over unsecured vehicles:", error);
      return [];
    }
  };

  // Function to automatically add carried over vehicles to a new week
  const initializeWeekWithCarryOvers = async (userId, weekId) => {
    if (!db || !userId || !weekId) {
      console.log("❌ Missing parameters for carryover initialization");
      return;
    }

    try {
      console.log(`🔄 Initializing week ${weekId} with carry-overs for user ${userId}`);
      
      // Check if week already has carryover initialized
      const weekRef = doc(db, 'users', userId, 'vehicleWeeks', weekId);
      const weekDoc = await getDoc(weekRef);
      
      if (weekDoc.exists() && weekDoc.data().carryOverInitialized) {
        console.log("✅ Week already has carryovers initialized");
        return;
      }
      
      // Get unsecured vehicles from previous weeks
      const carryOverVehicles = await carryOverUnsecuredVehicles(userId, weekId);
      
      if (carryOverVehicles.length === 0) {
        console.log("ℹ️ No vehicles to carry over");
        // Still mark as initialized
        await setDoc(weekRef, {
          carryOverInitialized: true,
          carryOverInitializedDate: new Date()
        }, { merge: true });
        return;
      }
      
      console.log(`🚀 Adding ${carryOverVehicles.length} vehicles to week ${weekId}`);
      
      // Create batch to add all carried over vehicles
      const batch = writeBatch(db);
      
      carryOverVehicles.forEach((vehicle, index) => {
        // Create new document for carried over vehicle in current week
        const vehicleRef = doc(collection(db, 'users', userId, 'vehicleWeeks', weekId, 'vehicles'));
        
        // Clean up the vehicle data for the new week
        const cleanVehicleData = {
          ...vehicle,
          // Keep original data but mark as carried over
          carriedOver: true,
          // Remove old IDs to create new documents
          id: undefined,
          originalWeek: undefined,
          originalWeekStart: undefined
        };
        
        console.log(`📝 Adding vehicle ${index + 1}: ${cleanVehicleData.vehicle} (${cleanVehicleData.vin})`);
        batch.set(vehicleRef, cleanVehicleData);
      });
      
      // Mark week as initialized
      batch.update(weekRef, {
        carryOverInitialized: true,
        carryOverInitializedDate: new Date(),
        hasCarryOvers: true,
        carryOverCount: carryOverVehicles.length
      });
      
      // Commit the batch
      await batch.commit();
      
      console.log(`✅ Successfully carried over ${carryOverVehicles.length} vehicles to week ${weekId}`);
      
    } catch (error) {
      console.error("❌ Error initializing week with carry-overs:", error);
    }
  };

  // Function to check and secure carried over vehicles with team sync
  const checkAndSecureCarriedOverVehicle = async (vehicleId, vin) => {
    if (!db || !selectedUser || !selectedWeek || !vehicleId) return;

    try {
      console.log(`🔒 Securing carried over vehicle ${vehicleId} (VIN: ${vin})`);
      
      const securedDate = new Date().toISOString().split('T')[0];
      
      // Update the vehicle as secured in current week
      const vehicleRef = doc(db, 'users', selectedUser.id, 'vehicleWeeks', selectedWeek, 'vehicles', vehicleId);
      await updateDoc(vehicleRef, {
        status: 'SECURED',
        securedTimestamp: new Date(),
        securedInWeek: selectedWeek,
        securedDate: securedDate
      });
      
      // Update local state immediately
      const updatedVehicles = vehicles.map(v => 
        v.id === vehicleId 
          ? {...v, status: 'SECURED', securedDate: securedDate, securedTimestamp: new Date()} 
          : v
      );
      setVehicles(updatedVehicles);
      
      // Mark as secured in all future weeks for this user
      if (vin) {
        await markVehicleAsSecuredInFutureWeeks(selectedUser.id, vin, selectedWeek);
      }
      
      // NEW: Sync across team
      if (vin && selectedTeam) {
        console.log("🔄 Carryover vehicle secured with VIN, syncing across team...");
        
        const userDisplayName = selectedUser.displayName || selectedUser.email?.split('@')[0] || 'Team Member';
        
        await markVINAsSecuredAcrossTeam(
          selectedTeam.id, 
          selectedUser.id, 
          vin, 
          securedDate,
          userDisplayName
        );
      }
      
      console.log(`✅ Successfully secured vehicle ${vehicleId}`);
      
      // Update stats
      await updateVehicleWeekStats(selectedUser.id, selectedWeek);
      
    } catch (error) {
      console.error("❌ Error securing carried over vehicle:", error);
    }
  };

  // Function to mark a vehicle as secured in all future weeks
  const markVehicleAsSecuredInFutureWeeks = async (userId, vin, securedInWeekId) => {
    if (!db || !userId || !vin) return;

    try {
      console.log(`🔒 Marking VIN ${vin} as secured in all future weeks`);
      
      // Get all weeks for this user
      const vehicleWeeksRef = collection(db, 'users', userId, 'vehicleWeeks');
      const weeksSnapshot = await getDocs(vehicleWeeksRef);
      
      const batch = writeBatch(db);
      
      // Process each week to find and update future instances of this VIN
      for (const weekDoc of weeksSnapshot.docs) {
        const weekId = weekDoc.id;
        
        // Skip the week where it was secured (already handled)
        if (weekId === securedInWeekId) continue;
        
        // Get vehicles in this week
        const vehiclesRef = collection(db, 'users', userId, 'vehicleWeeks', weekId, 'vehicles');
        const vehiclesSnapshot = await getDocs(vehiclesRef);
        
        vehiclesSnapshot.forEach(vehicleDoc => {
          const vehicleData = vehicleDoc.data();
          const vehicleVin = vehicleData.vin || vehicleData.VIN;
          
          // If this is the same VIN and it's carried over, mark it as secured
          if (vehicleVin === vin && vehicleData.carriedOver) {
            console.log(`🔒 Updating vehicle ${vehicleData.vehicle} in week ${weekId}`);
            batch.update(vehicleDoc.ref, {
              status: 'SECURED',
              securedTimestamp: new Date(),
              securedInWeek: securedInWeekId,
              securedInFutureWeek: true,
              securedDate: new Date().toISOString().split('T')[0]
            });
          }
        });
      }
      
      await batch.commit();
      console.log(`✅ Marked VIN ${vin} as secured in all future weeks`);
      
    } catch (error) {
      console.error("❌ Error marking vehicle as secured in future weeks:", error);
    }
  };

  // Initialize Firestore
  useEffect(() => {
    try {
      const firestore = getFirestore();
      setDb(firestore);
    } catch (error) {
      console.error("Error initializing Firestore:", error);
      setError("Failed to connect to database.");
    }
  }, []);

  // Check if user is admin
  useEffect(() => {
    if (!isAdmin) {
      navigate('/dashboard');
    }
  }, [isAdmin, navigate]);
  
  // Fetch teams first
  useEffect(() => {
    if (!db) return;
    
    async function fetchTeams() {
      setLoading(true);
      try {
        const teamsCollection = collection(db, 'teams');
        const teamsSnapshot = await getDocs(teamsCollection);
        const teamsData = teamsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setTeams(teamsData);
        
        // If teams are found, select the first one by default
        if (teamsData.length > 0) {
          setSelectedTeam(teamsData[0]);
        }
        
        setLoading(false);
      } catch (err) {
        console.error("Error loading teams data:", err);
        setError("Failed to load teams");
        setLoading(false);
      }
    }
    
    fetchTeams();
  }, [db]);

  // Fetch users for the selected team
  useEffect(() => {
    if (!db || !selectedTeam) return;
    
    async function fetchTeamUsers() {
      setLoading(true);
      try {
        // Get team members from team_members subcollection
        const teamMembersCollection = collection(db, `teams/${selectedTeam.id}/teamMembers`);
        const teamMembersSnapshot = await getDocs(teamMembersCollection);
        
        if (teamMembersSnapshot.empty) {
          setUsers([]);
          setLoading(false);
          return;
        }
        
        // Extract user IDs from team members
        const userIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);
        
        // Fetch full user data for each member
        const teamUsers = await Promise.all(userIds.map(async userId => {
          try {
            const userDoc = await getDoc(doc(db, "users", userId));
            const userData = userDoc.exists() ? userDoc.data() : {};
            
            // Get additional profile data
            const profileDoc = await getDoc(doc(db, "userProfiles", userId));
            const profileData = profileDoc.exists() ? profileDoc.data() : {};
            
            return {
              id: userId,
              ...userData,
              teamId: selectedTeam.id,
              displayName: profileData.displayName || userData.email?.split('@')[0] || 'Unknown User',
              avatar: profileData.avatar || getUserAvatar(userData.role),
              jobTitle: profileData.jobTitle || userData.role
            };
          } catch (error) {
            console.error(`Error fetching data for user ${userId}:`, error);
            return null;
          }
        }));
        
        // Filter out any null results from errors
        setUsers(teamUsers.filter(Boolean));
        
        // Fetch team statistics
        fetchTeamStats(selectedTeam.id);
        
        setLoading(false);
      } catch (err) {
        console.error("Error loading team users:", err);
        setError(`Failed to load users for team ${selectedTeam.name}`);
        setLoading(false);
      }
    }
    
    fetchTeamUsers();
  }, [db, selectedTeam]);

  // Get a default avatar based on role
  const getUserAvatar = (role) => {
    switch(role) {
      case 'admin':
        return '👨‍💼';
      case 'manager':
        return '👩‍💼';
      case 'tow':
        return '🚚';
      case 'technician':
        return '👨‍🔧';
      case 'support':
        return '👩‍💻';
      default:
        return '👤';
    }
  };

  // Update team stats when the selected user's vehicle data changes
  useEffect(() => {
    if (selectedTeam && selectedUser && selectedWeek && vehicleStats) {
      console.log("Vehicle stats changed, updating team stats...", vehicleStats);
      updateTeamStatsWithVehicleData();
    }
  }, [selectedTeam, selectedUser, selectedWeek, vehicleStats]);

  // Update time card data when week changes
  useEffect(() => {
    if (selectedWeek && selectedUser && db) {
      // When week changes in VehicleTracker, update time card data for that week
      const updateTimeCardForSelectedWeek = async () => {
        try {
          const weekDocRef = doc(db, 'users', selectedUser.id, 'vehicleWeeks', selectedWeek);
          const weekDoc = await getDoc(weekDocRef);
          
          if (weekDoc.exists()) {
            const weekStartDate = weekDoc.data().startDate?.toDate();
            const weekEndDate = weekDoc.data().endDate?.toDate();
            
            if (weekStartDate && weekEndDate) {
              // Calculate hours worked for this specific week
              const hoursForWeek = await calculateHoursForDateRange(selectedUser.id, weekStartDate, weekEndDate);
              
              // Update time card data with the week-specific hours
              setTimeCardData(prev => ({
                ...prev,
                weeklyHours: hoursForWeek
              }));
              
              // Also update team stats for this week
              if (selectedTeam) {
                fetchTeamStats(selectedTeam.id, selectedWeek);
              }
            }
          }
        } catch (error) {
          console.error("Error updating time card for selected week:", error);
        }
      };
      
      updateTimeCardForSelectedWeek();
    }
  }, [selectedWeek, selectedUser, db]);

  // Function to update team stats with current vehicle data
  const updateTeamStatsWithVehicleData = async () => {
    if (!selectedTeam || !selectedUser || !vehicleStats) return;

    console.log("Updating team stats with vehicle data:", vehicleStats);
    
    try {
      // Start with either existing team stats or empty object
      const updatedTeamStats = teamStats ? {...teamStats} : {};
      
      // Update weekly stats specifically for the selected week
      updatedTeamStats.selectedWeekStats = {
        weekId: selectedWeek,
        dateRange: vehicleStats.dateRange,
        totalScans: vehicleStats.totalScans || 0,
        totalFound: vehicleStats.totalFound || 0, 
        totalSecured: vehicleStats.totalSecured || 0,
        recoveryRate: vehicleStats.recoveryRate || 0
      };
      
      // Also update team totals for this week (for all team members)
      // First, get all team members
      const teamMembersCollection = collection(db, `teams/${selectedTeam.id}/teamMembers`);
      const teamMembersSnapshot = await getDocs(teamMembersCollection);
      const memberIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);
      
      let teamTotalFound = 0;
      let teamTotalSecured = 0;
      let teamTotalScans = 0;
      let teamTotalHours = 0;
      
      // Get the week document to determine date range
      const weekDocRef = doc(db, 'users', selectedUser.id, 'vehicleWeeks', selectedWeek);
      const weekDoc = await getDoc(weekDocRef);
      let weekStartDate = null;
      let weekEndDate = null;
      
      if (weekDoc.exists()) {
        weekStartDate = weekDoc.data().startDate?.toDate();
        weekEndDate = weekDoc.data().endDate?.toDate();
      }
      
      // Loop through each team member to get their stats for this week
      for (const userId of memberIds) {
        try {
          // Check if this week exists for the user
          const weekDocRef = doc(db, 'users', userId, 'vehicleWeeks', selectedWeek);
          const weekDoc = await getDoc(weekDocRef);
          
          if (weekDoc.exists()) {
            const weekData = weekDoc.data();
            teamTotalFound += (weekData.totalFound || 0);
            teamTotalSecured += (weekData.totalSecured || 0);
            teamTotalScans += (weekData.totalScans || 0);
          }
          
          // Get time card data for hours
          if (weekStartDate && weekEndDate) {
            // Calculate hours specifically for this week's date range
            const hoursForWeek = await calculateHoursForDateRange(userId, weekStartDate, weekEndDate);
            teamTotalHours += hoursForWeek;
          } else {
            // Fallback to summary if date range not available
            const timeCardSummaryRef = doc(db, 'timeCardSummary', userId);
            const timeCardDoc = await getDoc(timeCardSummaryRef);
            if (timeCardDoc.exists()) {
              teamTotalHours += (timeCardDoc.data().weeklyHours || 0);
            }
          }
        } catch (error) {
          console.error(`Error getting week data for user ${userId}:`, error);
        }
      }
      
      // Calculate team recovery rate
      const teamRecoveryRate = teamTotalFound > 0 ? (teamTotalSecured / teamTotalFound) * 100 : 0;
      
      // Update team week totals
      updatedTeamStats.carsFoundThisWeek = teamTotalFound;
      updatedTeamStats.carsRecoveredThisWeek = teamTotalSecured;
      updatedTeamStats.teamScansWeek = teamTotalScans;
      updatedTeamStats.teamHoursWorkedWeek = teamTotalHours;
      updatedTeamStats.weeklyRecoveryRate = teamRecoveryRate;
      
      // Update state with the new team stats
      setTeamStats(updatedTeamStats);
      
      // Also update top performers based on the selected week
      fetchTopPerformers(selectedTeam.id, selectedWeek);
      
      console.log("Updated team stats with vehicle data:", updatedTeamStats);
      
    } catch (error) {
      console.error("Error updating team stats with vehicle data:", error);
    }
  };

  // Update vehicle week stats
  const updateVehicleWeekStats = async (userId, weekId) => {
    if (!db) return;
    
    try {
      const vehiclesQuery = query(
        collection(db, 'users', userId, 'vehicleWeeks', weekId, 'vehicles')
      );
      
      const vehiclesSnapshot = await getDocs(vehiclesQuery);
      const vehiclesData = vehiclesSnapshot.docs.map(doc => doc.data());
      
      // Corrected logic: Only count non-carryover vehicles as "found"
      const totalFound = vehiclesData.filter(v => 
        !v.carriedOver && (v.status === 'FOUND' || v.status === 'SECURED')
      ).length;
      
      // Total secured includes both new and carryover vehicles that are secured
      const totalSecured = vehiclesData.filter(v => v.status === 'SECURED').length;
      
      // Calculate recovery rate based on total available vehicles (found + carryover)
      const totalCarryover = vehiclesData.filter(v => v.carriedOver).length;
      const totalAvailable = totalFound + totalCarryover;
      const recoveryRate = totalAvailable > 0 ? (totalSecured / totalAvailable) * 100 : 0;
      
      // Get current scan count from week document
      const weekDoc = await getDoc(doc(db, 'users', userId, 'vehicleWeeks', weekId));
      const currentScans = weekDoc.exists() ? (weekDoc.data().totalScans || 0) : 0;
      
      // Update the week document with new stats
      await updateDoc(doc(db, 'users', userId, 'vehicleWeeks', weekId), {
        totalFound,
        totalSecured,
        recoveryRate,
        updatedAt: new Date()
      });
      
      // Update vehicleStats state if this is the selected user and week
      if (selectedUser && selectedUser.id === userId && selectedWeek === weekId) {
        setVehicleStats({
          totalScans: currentScans,
          totalFound,
          totalSecured,
          recoveryRate,
          dateRange: weekDoc.exists() && weekDoc.data().displayRange 
            ? {
                start: weekDoc.data().displayRange.split(' - ')[0],
                end: weekDoc.data().displayRange.split(' - ')[1]
              }
            : { start: '', end: '' }
        });
      }
      
      // Also update aggregated data
      const weeksQuery = query(
        collection(db, 'users', userId, 'vehicleWeeks'),
        orderBy('endDate', 'desc')
      );
      
      const weeksSnapshot = await getDocs(weeksQuery);
      const allWeeks = weeksSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        startDate: doc.data().startDate?.toDate(),
        endDate: doc.data().endDate?.toDate()
      }));
      
      await updateAggregatedVehicleData(userId, allWeeks);
      
      // Update team stats if necessary
      if (selectedTeam && selectedUser && selectedUser.id === userId) {
        updateTeamStatsWithVehicleData();
      }
      
    } catch (error) {
      console.error("Error updating vehicle week stats:", error);
    }
  };

  // Update aggregated vehicle data (month, YTD)
  const updateAggregatedVehicleData = async (userId, allWeeks) => {
    if (!db || !userId || !allWeeks) return;
    
    try {
      const now = new Date();
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const yearStart = new Date(now.getFullYear(), 0, 1);
      
      const currentMonthWeeks = allWeeks.filter(week => 
        (week.startDate >= monthStart || week.endDate >= monthStart)
      );
      
      const currentYearWeeks = allWeeks.filter(week => 
        (week.startDate >= yearStart || week.endDate >= yearStart)
      );
      
      let monthTotalFound = 0;
      let monthTotalSecured = 0;
      let monthTotalScans = 0;
      let ytdTotalFound = 0;
      let ytdTotalSecured = 0;
      let ytdTotalScans = 0;
      
      // Calculate monthly aggregates
      for (const week of currentMonthWeeks) {
        monthTotalFound += (week.totalFound || 0);
        monthTotalSecured += (week.totalSecured || 0);
        monthTotalScans += (week.totalScans || 0);
      }
      
      const monthlyData = {
        totalFound: monthTotalFound,
        totalSecured: monthTotalSecured,
        totalScans: monthTotalScans,
        recoveryRate: monthTotalFound > 0 ? (monthTotalSecured / monthTotalFound) * 100 : 0
      };
      
      // Calculate YTD aggregates
      for (const week of currentYearWeeks) {
        ytdTotalFound += (week.totalFound || 0);
        ytdTotalSecured += (week.totalSecured || 0);
        ytdTotalScans += (week.totalScans || 0);
      }
      
      const ytdData = {
        totalFound: ytdTotalFound,
        totalSecured: ytdTotalSecured,
        totalScans: ytdTotalScans,
        recoveryRate: ytdTotalFound > 0 ? (ytdTotalSecured / ytdTotalFound) * 100 : 0
      };
      
      // Update state if this is for the selected user
      if (selectedUser && selectedUser.id === userId) {
        setAggregatedVehicleData({
          month: monthlyData,
          ytd: ytdData
        });
      }
      
      return {
        month: monthlyData,
        ytd: ytdData
      };
      
    } catch (error) {
      console.error("Error updating aggregated vehicle data:", error);
      return {
        month: { totalFound: 0, totalSecured: 0, totalScans: 0, recoveryRate: 0 },
        ytd: { totalFound: 0, totalSecured: 0, totalScans: 0, recoveryRate: 0 }
      };
    }
  };

  // Helper function to calculate hours for a specific date range
  const calculateHoursForDateRange = async (userId, startDate, endDate) => {
    if (!db || !userId || !startDate || !endDate) {
      return 0;
    }
    
    try {
      console.log(`Calculating hours for ${userId} from ${startDate} to ${endDate}`);
      
      // Query for time card entries within this date range
      const historyRef = query(
        collection(db, 'timeCardHistory'),
        where('userId', '==', userId),
        where('timestamp', '>=', startDate),
        where('timestamp', '<=', endDate),
        orderBy('timestamp')
      );
      
      const historySnapshot = await getDocs(historyRef);
      const entries = [];
      
      historySnapshot.forEach((doc) => {
        const data = doc.data();
        entries.push({
          ...data,
          timestamp: data.timestamp?.toDate(),
          id: doc.id
        });
      });
      
      console.log(`Found ${entries.length} time card entries for the date range`);
      
      // Calculate hours worked within this date range
      let totalHours = 0;
      let lastClockIn = null;
      
      for (const entry of entries) {
        if (entry.type === 'clockIn') {
          lastClockIn = entry.timestamp;
        } else if (entry.type === 'clockOut' && lastClockIn) {
          const duration = entry.timestamp - lastClockIn;
          
          // Only count reasonable durations (< 24 hours)
          if (duration > 0 && duration < 24 * 60 * 60 * 1000) {
            totalHours += duration;
          }
          
          lastClockIn = null;
        }
      }
      
      // Convert to hours
      const hoursWorked = totalHours / (1000 * 60 * 60);
      console.log(`Calculated ${hoursWorked} hours for the date range`);
      
      return hoursWorked;
      
    } catch (error) {
      console.error("Error calculating hours for date range:", error);
      return 0;
    }
  };

  // Fetch team statistics
  const fetchTeamStats = async (teamId, weekId = null) => {
    if (!db) return;
    
    try {
      // Look for team stats document
      const teamStatsRef = doc(db, "teamStats", teamId);
      const teamStatsDoc = await getDoc(teamStatsRef);
      
      let teamStatsData = {};
      
      if (teamStatsDoc.exists()) {
        // Get base team stats
        teamStatsData = teamStatsDoc.data();
      } else {
        // Initialize with zeros if no stats exist
        teamStatsData = {
          recoveryRate: 0,
          avgScansPerDay: 0,
          carsFoundThisMonth: 0,
          efficiencyScore: 0,
          teamHoursWorkedToday: 0,
          teamHoursWorkedWeek: 0,
          teamHoursWorkedMonth: 0,
          teamHoursWorkedYTD: 0
        };
      }
      
      // If a specific week is selected, aggregate team data for that week
      if (weekId) {
        // First try to get the week document to determine date range
        let weekStartDate = null;
        let weekEndDate = null;
        
        // Try from selected user first
        if (selectedUser) {
          const weekDocRef = doc(db, 'users', selectedUser.id, 'vehicleWeeks', weekId);
          const weekDoc = await getDoc(weekDocRef);
          
          if (weekDoc.exists()) {
            weekStartDate = weekDoc.data().startDate?.toDate();
            weekEndDate = weekDoc.data().endDate?.toDate();
          }
        }
        
        // If we couldn't get dates from selected user, try from any team member
        if (!weekStartDate) {
          // Get team members
          const teamMembersCollection = collection(db, `teams/${teamId}/teamMembers`);
          const teamMembersSnapshot = await getDocs(teamMembersCollection);
          const memberIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);
          
          // Try to find week document from any member
          for (const userId of memberIds) {
            const weekDocRef = doc(db, 'users', userId, 'vehicleWeeks', weekId);
            const weekDoc = await getDoc(weekDocRef);
            
            if (weekDoc.exists()) {
              weekStartDate = weekDoc.data().startDate?.toDate();
              weekEndDate = weekDoc.data().endDate?.toDate();
              break;
            }
          }
        }
        
        // Get all team members
        const teamMembersCollection = collection(db, `teams/${teamId}/teamMembers`);
        const teamMembersSnapshot = await getDocs(teamMembersCollection);
        const memberIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);
        
        // Initialize weekly aggregates
        let weekTotalFound = 0;
        let weekTotalSecured = 0;
        let weekTotalScans = 0;
        let weekTotalHours = 0;
        
        // Gather stats from each team member for this week
        for (const userId of memberIds) {
          try {
            // Get the user's vehicle stats for this specific week
            const weekDocRef = doc(db, 'users', userId, 'vehicleWeeks', weekId);
            const weekDoc = await getDoc(weekDocRef);
            
            if (weekDoc.exists()) {
              const weekData = weekDoc.data();
              weekTotalFound += (weekData.totalFound || 0);
              weekTotalSecured += (weekData.totalSecured || 0);
              weekTotalScans += (weekData.totalScans || 0);
            }
            
            // Get time card data if available
            if (weekStartDate && weekEndDate) {
              // Calculate hours specifically for this week's date range
              const hoursForWeek = await calculateHoursForDateRange(userId, weekStartDate, weekEndDate);
              weekTotalHours += hoursForWeek;
            } else {
              // Fallback to summary if date range not available
              const timeCardSummaryRef = doc(db, 'timeCardSummary', userId);
              const timeCardDoc = await getDoc(timeCardSummaryRef);
              if (timeCardDoc.exists()) {
                weekTotalHours += (timeCardDoc.data().weeklyHours || 0);
              }
            }
          } catch (error) {
            console.error(`Error fetching week data for user ${userId}:`, error);
          }
        }
        
        // Calculate weekly recovery rate
        const weeklyRecoveryRate = weekTotalFound > 0 ? (weekTotalSecured / weekTotalFound) * 100 : 0;
        
        // Update team stats with week-specific data
        teamStatsData.carsFoundThisWeek = weekTotalFound;
        teamStatsData.carsRecoveredThisWeek = weekTotalSecured;
        teamStatsData.teamScansWeek = weekTotalScans;
        teamStatsData.teamHoursWorkedWeek = weekTotalHours;
        teamStatsData.weeklyRecoveryRate = weeklyRecoveryRate;
        
        // Store the selected week info
        teamStatsData.selectedWeekStats = {
          weekId: weekId,
          totalFound: weekTotalFound,
          totalSecured: weekTotalSecured,
          totalScans: weekTotalScans,
          recoveryRate: weeklyRecoveryRate
        };
      }
      
      if (selectedUser) {
        // Try to fetch timecard data for the selected user
        if (weekId && selectedWeek === weekId) {
          // If we have a week selected with date range, calculate hours for that specific week
          const weekDocRef = doc(db, 'users', selectedUser.id, 'vehicleWeeks', weekId);
          const weekDoc = await getDoc(weekDocRef);
          
          if (weekDoc.exists()) {
            const weekStartDate = weekDoc.data().startDate?.toDate();
            const weekEndDate = weekDoc.data().endDate?.toDate();
            
            if (weekStartDate && weekEndDate) {
              // Calculate user hours for this specific date range
              const hoursForSelectedWeek = await calculateHoursForDateRange(selectedUser.id, weekStartDate, weekEndDate);
              
              // Create time card data with the specific week's hours
              const weekSpecificTimeCard = {
                dailyHours: 0, // We don't have day-specific info for past weeks
                weeklyHours: hoursForSelectedWeek,
                monthlyHours: 0, // We don't update this for specific weeks
                ytdHours: 0, // We don't update this for specific weeks
                // Keep any existing status info
                currentStatus: timeCardData?.currentStatus 
              };
              
              setTimeCardData(weekSpecificTimeCard);
            } else {
              // Fallback to regular time card data
              const timeCardData = await fetchTimeCardData(selectedUser.id);
              setTimeCardData(timeCardData);
            }
          } else {
            // Fallback to regular time card data
            const timeCardData = await fetchTimeCardData(selectedUser.id);
            setTimeCardData(timeCardData);
          }
        } else {
          // Regular time card data for current week
          const timeCardData = await fetchTimeCardData(selectedUser.id);
          setTimeCardData(timeCardData);
        }
        
        // Add user's stats to the team stats for the personal section
        const userStatsRef = doc(db, "userStats", selectedUser.id);
        const userStatsDoc = await getDoc(userStatsRef);
        
        if (userStatsDoc.exists()) {
          teamStatsData.userStats = userStatsDoc.data();
        }
      }
      
      setTeamStats(teamStatsData);
      
      // Fetch top performers for this team (and optionally for this week)
      fetchTopPerformers(teamId, weekId);
      
    } catch (error) {
      console.error("Error fetching team stats:", error);
      setTeamStats({
        recoveryRate: 0,
        avgScansPerDay: 0,
        carsFoundThisMonth: 0,
        carsFoundThisWeek: 0,
        carsRecoveredThisWeek: 0,
        teamScansWeek: 0,
        teamHoursWorkedWeek: 0,
        weeklyRecoveryRate: 0,
        efficiencyScore: 0
      });
    }
  };

  // Fetch timecard data for a user
  const fetchTimeCardData = async (userId) => {
    if (!db) return null;
    
    try {
      // First check if there's a summary document
      const summaryRef = doc(db, 'timeCardSummary', userId);
      const summaryDoc = await getDoc(summaryRef);
      
      if (summaryDoc.exists()) {
        return summaryDoc.data();
      }
      
      // If no summary, return null
      return null;
    } catch (error) {
      console.error("Error fetching time card data:", error);
      return null;
    }
  };

  // Fetch top performers data
  const fetchTopPerformers = async (teamId, weekId = null) => {
    if (!db) return;
    
    try {
      // Get time period for filters - use the selected week if provided
      const now = new Date();
      let startDate;
      let endDate;
      
      if (weekId) {
        // Try to get the specific week start/end dates
        try {
          // First try the selected user's week
          if (selectedUser) {
            const weekDocRef = doc(db, 'users', selectedUser.id, 'vehicleWeeks', weekId);
            const weekDoc = await getDoc(weekDocRef);
            
            if (weekDoc.exists()) {
              startDate = weekDoc.data().startDate?.toDate();
              endDate = weekDoc.data().endDate?.toDate();
            }
          }
          
          // If not found, try to find the week from any team member
          if (!startDate) {
            const teamMembersCollection = collection(db, `teams/${teamId}/teamMembers`);
            const teamMembersSnapshot = await getDocs(teamMembersCollection);
            const memberIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);
            
            for (const userId of memberIds) {
              const weekDocRef = doc(db, 'users', userId, 'vehicleWeeks', weekId);
              const weekDoc = await getDoc(weekDocRef);
              
              if (weekDoc.exists()) {
                startDate = weekDoc.data().startDate?.toDate();
                endDate = weekDoc.data().endDate?.toDate();
                break;
              }
            }
          }
        } catch (error) {
          console.error("Error fetching week dates:", error);
        }
      }
      
      // Fallback to time filter if week dates aren't available
      if (!startDate) {
        switch(timeFilter) {
          case 'day':
            startDate = new Date(now.setHours(0, 0, 0, 0));
            break;
          case 'week':
            startDate = new Date(now.setDate(now.getDate() - 7));
            break;
          case 'month':
            startDate = new Date(now.setMonth(now.getMonth() - 1));
            break;
          case 'ytd':
            startDate = new Date(now.getFullYear(), 0, 1);
            break;
          default:
            startDate = new Date(now.setDate(now.getDate() - 7)); // Default to week
        }
      }
      
      // Get team members
      const teamMembersCollection = collection(db, `teams/${teamId}/teamMembers`);
      const teamMembersSnapshot = await getDocs(teamMembersCollection);
      const memberIds = teamMembersSnapshot.docs.map(doc => doc.data().userId);
      
      if (memberIds.length === 0) {
        setTopPerformers({
          mostCars: null,
          mostEfficient: null,
          mostImproved: null
        });
        return;
      }
      
      // Most cars found - collect all stats and sort locally
      const userStatsPromises = memberIds.map(async userId => {
        const userStatsDoc = await getDoc(doc(db, "userStats", userId));
        
        // If using a specific week, get the vehicle data for that week
        if (weekId) {
          try {
            const weekDocRef = doc(db, 'users', userId, 'vehicleWeeks', weekId);
            const weekDoc = await getDoc(weekDocRef);
            
            if (weekDoc.exists()) {
              const weekData = weekDoc.data();
              const userData = userStatsDoc.exists() ? userStatsDoc.data() : {};
              
              // Get week-specific hours if available
              let weekHours = 0;
              if (startDate && endDate) {
                weekHours = await calculateHoursForDateRange(userId, startDate, endDate);
              } else {
                // Fallback to timecard summary
                const timeCardSummaryRef = doc(db, 'timeCardSummary', userId);
                const timeCardDoc = await getDoc(timeCardSummaryRef);
                const hoursData = timeCardDoc.exists() ? timeCardDoc.data() : {};
                weekHours = hoursData.weeklyHours || 0;
              }
              
              // Merge weekly data with user stats
              return {
                userId,
                ...userData,
                // Override relevant stats with week-specific data
                carsFoundWeek: weekData.totalFound || 0,
                carsRecoveredWeek: weekData.totalSecured || 0,
                userScansWeek: weekData.totalScans || 0,
                userHoursWorkedWeek: weekHours,
                // Include recovery rate
                weekRecoveryRate: weekData.totalFound > 0 ? 
                  (weekData.totalSecured / weekData.totalFound) * 100 : 0
              };
            }
          } catch (error) {
            console.error(`Error fetching week data for user ${userId}:`, error);
          }
        }
        
        // Default to normal user stats if no specific week or week not found
        if (userStatsDoc.exists()) {
          return {
            userId,
            ...userStatsDoc.data()
          };
        }
        
        return null;
      });
      
      const allUserStats = (await Promise.all(userStatsPromises)).filter(Boolean);
      
      // Sort for most cars
      const sortedByCars = [...allUserStats]
        .filter(user => user[getTimeProperty('carsFound')] > 0) // Only include users who found cars
        .sort((a, b) => {
          const aValue = a[getTimeProperty('carsFound')] || 0;
          const bValue = b[getTimeProperty('carsFound')] || 0;
          return bValue - aValue;
        });
      
      // Sort for most efficient - with minimum hours requirement
      const sortedByEfficiency = [...allUserStats]
        .filter(user => {
          // Apply minimum thresholds to be considered "efficient"
          const hours = user[getTimeProperty('userHoursWorked')] || 0;
          const cars = user[getTimeProperty('carsFound')] || 0;
          
          // Require at least some cars found to be considered
          return cars > 0;
        })
        .sort((a, b) => {
          // Get hours with minimum value for calculation (not display)
          const aHours = a[getTimeProperty('userHoursWorked')] || 0;
          const bHours = b[getTimeProperty('userHoursWorked')] || 0;
          const aCars = a[getTimeProperty('carsFound')] || 0;
          const bCars = b[getTimeProperty('carsFound')] || 0;
          
          // Use a minimum of 1 hour for efficiency calculation to avoid 
          // extremely high efficiency for very small amounts of time
          const aEfficiency = aCars / Math.max(aHours, 1);
          const bEfficiency = bCars / Math.max(bHours, 1);
          
          return bEfficiency - aEfficiency;
        });
      
      // Sort for most improved
      const sortedByImprovement = [...allUserStats]
        .filter(user => user.recoveryRateIncrease > 0 || user.weekRecoveryRate > 0) // Include weekly recovery
        .sort((a, b) => {
          const aImprovement = a.recoveryRateIncrease || a.weekRecoveryRate || 0;
          const bImprovement = b.recoveryRateIncrease || b.weekRecoveryRate || 0;
          return bImprovement - aImprovement;
        });
      
      // Update state with top performers
      const topPerformersData = {
        mostCars: sortedByCars.length > 0 ? {
          userId: sortedByCars[0].userId,
          value: sortedByCars[0][getTimeProperty('carsFound')] || 0
        } : null,
        mostEfficient: sortedByEfficiency.length > 0 ? {
          userId: sortedByEfficiency[0].userId,
          // Use the time-appropriate properties
          value: Math.min((sortedByEfficiency[0][getTimeProperty('carsFound')] || 0) / 
                 Math.max(sortedByEfficiency[0][getTimeProperty('userHoursWorked')] || 0.25, 0.25), 100), // Cap at 100/hour
          // Include the actual hours for context
          hours: sortedByEfficiency[0][getTimeProperty('userHoursWorked')] || 0,
          // Include cars found for context
          cars: sortedByEfficiency[0][getTimeProperty('carsFound')] || 0
        } : null,
        mostImproved: sortedByImprovement.length > 0 ? {
          userId: sortedByImprovement[0].userId,
          value: sortedByImprovement[0].recoveryRateIncrease || sortedByImprovement[0].weekRecoveryRate || 0
        } : null
      };
      
      // Find user data for top performers
      for (const key in topPerformersData) {
        if (topPerformersData[key]) {
          const user = users.find(u => u.id === topPerformersData[key].userId);
          if (user) {
            topPerformersData[key].name = user.displayName || user.email?.split('@')[0] || 'Unknown User';
          } else {
            // Get user info if not in current users array
            try {
              const userDoc = await getDoc(doc(db, "users", topPerformersData[key].userId));
              if (userDoc.exists()) {
                topPerformersData[key].name = userDoc.data().email?.split('@')[0] || 'Unknown User';
              } else {
                topPerformersData[key].name = 'Unknown User';
              }
            } catch (error) {
              console.error("Error fetching user for top performer:", error);
              topPerformersData[key].name = 'Unknown User';
            }
          }
        }
      }
      
      setTopPerformers(topPerformersData);
      
    } catch (error) {
      console.error("Error fetching top performers:", error);
      setTopPerformers({
        mostCars: null,
        mostEfficient: null,
        mostImproved: null
      });
    }
  };

  // Handle user selection with enhanced data synchronization
  const handleUserSelect = async (user) => {
    setSelectedUser(user);
    setUserStats(null); // Clear previous stats
    setUserLoading(true);
    
    try {
      // Get the stats for the selected user
      const userStatsRef = doc(db, "userStats", user.id);
      const userStatsDoc = await getDoc(userStatsRef);
      
      if (userStatsDoc.exists()) {
        // User has stats
        const statsData = userStatsDoc.data();
        setUserStats(statsData);
      } else {
        // No stats document found, initialize with zeros
        setUserStats(getEmptyUserStats());
        setError(`No stats data found for ${user.displayName || user.email}`);
      }
      
      // Fetch timecard data for this user
      const timeData = await fetchTimeCardData(user.id);
      setTimeCardData(timeData);
      
      // Important: First load the vehicle weeks for this user
      // This will set availableWeeks and selectedWeek
      await loadAvailableWeeks(user.id);
      
      // Now refresh team stats to include the selected user's data
      if (selectedTeam) {
        fetchTeamStats(selectedTeam.id, selectedWeek);
      }
      
    } catch (err) {
      console.error("Error fetching user stats:", err);
      setError(`Failed to load stats for ${user.displayName || user.email}`);
      setUserStats(getEmptyUserStats());
    } finally {
      setUserLoading(false);
    }
    
    // Close mobile menu when a user is selected
    setIsMobileMenuOpen(false);
  };

  // Load available vehicle weeks for the selected user
  const loadAvailableWeeks = async (userId) => {
    if (!db) return;
    
    try {
      // Reset vehicle state
      setVehicles([]);
      setVehicleStats({
        totalScans: 0,
        totalFound: 0,
        totalSecured: 0,
        recoveryRate: 0,
        dateRange: { start: '', end: '' }
      });
      
      // Get all weeks for this user
      const weeksQuery = query(
        collection(db, 'users', userId, 'vehicleWeeks'),
        orderBy('endDate', 'desc')
      );
      
      const weeksSnapshot = await getDocs(weeksQuery);
      const weeksData = weeksSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        startDate: doc.data().startDate?.toDate(),
        endDate: doc.data().endDate?.toDate()
      }));
      
      setAvailableWeeks(weeksData);
      
      // Select the current week or most recent week
      const today = new Date();
      let currentWeek = weeksData.find(week => 
        week.startDate <= today && week.endDate >= today
      );
      
      if (!currentWeek && weeksData.length > 0) {
        currentWeek = weeksData[0]; // Use most recent week if no current week
      }
      
      if (currentWeek) {
        setSelectedWeek(currentWeek.id);
        await initializeWeekWithCarryOvers(userId, currentWeek.id);
        await loadVehicleDataForWeek(userId, currentWeek.id);
      }
      
      // Also update aggregated data
      await updateAggregatedVehicleData(userId, weeksData);
      
    } catch (error) {
      console.error("Error loading available weeks:", error);
      setAvailableWeeks([]);
    }
  };

  // Load vehicle data for a specific week
  const loadVehicleDataForWeek = async (userId, weekId) => {
    if (!db || !userId || !weekId) return;
    
    try {
      // Get week metadata first
      const weekDoc = await getDoc(doc(db, 'users', userId, 'vehicleWeeks', weekId));
      if (weekDoc.exists()) {
        const weekData = weekDoc.data();
        
        // Update vehicle stats
        setVehicleStats({
          totalScans: weekData.totalScans || 0,
          totalFound: weekData.totalFound || 0,
          totalSecured: weekData.totalSecured || 0,
          recoveryRate: weekData.recoveryRate || 0,
          dateRange: {
            start: weekData.displayRange?.split(' - ')[0] || '',
            end: weekData.displayRange?.split(' - ')[1] || ''
          }
        });
        
        // Make sure carryovers are initialized
        await initializeWeekWithCarryOvers(userId, weekId);
      }
      
      // Now get the vehicles for this week
      const vehiclesQuery = query(
        collection(db, 'users', userId, 'vehicleWeeks', weekId, 'vehicles'),
        orderBy('date', 'asc')
      );
      
      const vehiclesSnapshot = await getDocs(vehiclesQuery);
      const vehiclesData = vehiclesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      // Sort vehicles: secured first, then by date
      const sortedVehicles = vehiclesData.sort((a, b) => {
        if (a.status === 'SECURED' && b.status !== 'SECURED') return -1;
        if (b.status === 'SECURED' && a.status !== 'SECURED') return 1;
        
        const dateA = new Date(a.date || '1900-01-01');
        const dateB = new Date(b.date || '1900-01-01');
        return dateA - dateB;
      });
      
      setVehicles(sortedVehicles);
      
    } catch (error) {
      console.error("Error loading vehicle data for week:", error);
      setVehicles([]);
      setVehicleStats({
        totalScans: 0,
        totalFound: 0,
        totalSecured: 0,
        recoveryRate: 0,
        dateRange: { start: '', end: '' }
      });
    }
  };

  // Get empty user stats with all zeros
  const getEmptyUserStats = () => {
    return {
      userOpenOrdersToday: 0,
      userOpenOrdersWeek: 0,
      userOpenOrdersMonth: 0,
      userOpenOrdersYTD: 0,
      
      teamOpenOrdersToday: 0,
      teamOpenOrdersWeek: 0,
      teamOpenOrdersMonth: 0,
      teamOpenOrdersYTD: 0,
      
      userHoursWorkedToday: 0,
      userHoursWorkedWeek: 0,
      userHoursWorkedMonth: 0,
      userHoursWorkedYTD: 0,
      
      teamHoursWorkedToday: 0,
      teamHoursWorkedWeek: 0,
      teamHoursWorkedMonth: 0,
      teamHoursWorkedYTD: 0,
      
      carsFoundToday: 0,
      carsFoundWeek: 0,
      carsFoundMonth: 0,
      carsFoundYTD: 0,
      
      carsRecoveredToday: 0,
      carsRecoveredWeek: 0,
      carsRecoveredMonth: 0,
      carsRecoveredYTD: 0,
      
      userScansToday: 0,
      userScansWeek: 0,
      userScansMonth: 0,
      userScansYTD: 0,
      
      teamScansToday: 0,
      teamScansWeek: 0,
      teamScansMonth: 0,
      teamScansYTD: 0,
      
      userMarkersCreatedToday: 0,
      userMarkersCreatedWeek: 0,
      userMarkersCreatedMonth: 0,
      userMarkersCreatedYTD: 0,
      
      teamMarkersCreatedToday: 0,
      teamMarkersCreatedWeek: 0,
      teamMarkersCreatedMonth: 0,
      teamMarkersCreatedYTD: 0,
      
      userMilesTraveledToday: 0,
      userMilesTraveledWeek: 0,
      userMilesTraveledMonth: 0,
      userMilesTraveledYTD: 0,
      
      teamMilesTraveledToday: 0,
      teamMilesTraveledWeek: 0,
      teamMilesTraveledMonth: 0,
      teamMilesTraveledYTD: 0,
      
      dailyData: [],
      weeklyData: [],
      monthlyData: []
    };
  };

  // Filter users based on search term
  const filteredUsers = users.filter(user => 
    (user.displayName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) || 
    (user.email?.toLowerCase() || '').includes(searchTerm.toLowerCase()) || 
    (user.jobTitle?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (user.role?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  // Handle team selection change
  const handleTeamChange = (e) => {
    const selectedTeamId = e.target.value;
    const team = teams.find(t => t.id === selectedTeamId);
    if (team) {
      setSelectedTeam(team);
      setSelectedUser(null);
      setUserStats(null);
      setTimeCardData(null);
      setError(null);
      
      // Reset vehicle tracking state
      setVehicles([]);
      setAvailableWeeks([]);
      setSelectedWeek(null);
      setVehicleStats({
        totalScans: 0,
        totalFound: 0,
        totalSecured: 0,
        recoveryRate: 0,
        dateRange: { start: '', end: '' }
      });
      setAggregatedVehicleData({
        month: { totalScans: 0, totalFound: 0, totalSecured: 0, recoveryRate: 0 },
        ytd: { totalScans: 0, totalFound: 0, totalSecured: 0, recoveryRate: 0 }
      });
    }
  };

  // Get time period property name
  const getTimeProperty = (baseName) => {
    switch(timeFilter) {
      case 'day':
        return `${baseName}Today`;
      case 'week':
        return `${baseName}Week`;
      case 'month':
        return `${baseName}Month`;
      default:
        return `${baseName}YTD`;
    }
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Top Navigation Bar */}
      <nav className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 shadow-md border-b border-gray-700">
        <div className="mx-auto px-2 sm:px-4 lg:px-6">
          <div className="flex justify-between h-14">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">NWRepo</h1>
              </div>
              <div className="ml-4 flex space-x-2">
                <button onClick={() => navigate('/dashboard')} className="text-gray-300 hover:text-white px-2 py-2 rounded-md text-sm font-medium">
                  Dashboard
                </button>
              </div>
            </div>
            {/* Mobile menu button */}
            <div className="md:hidden flex items-center">
              <button 
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="text-gray-300 hover:text-white p-1 rounded-md focus:outline-none"
                aria-expanded={isMobileMenuOpen}
                aria-label="Toggle menu"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>
      
      <div className="py-2 sm:py-3">
        <div className="mx-auto px-2 sm:px-4 lg:px-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3">
            <h1 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-2 sm:mb-0">
              Team Analytics
            </h1>
            
            {/* Controls section - Compact design */}
            <div className="flex flex-col space-y-1 w-full sm:w-auto sm:flex-row sm:space-y-0 sm:space-x-2">
              {/* Team selection dropdown */}
              <select
                value={selectedTeam?.id || ''}
                onChange={handleTeamChange}
                className="py-1 px-2 bg-gray-800 border border-gray-700 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                disabled={loading}
                aria-label="Select team"
              >
                {teams.length === 0 ? (
                  <option value="">No teams</option>
                ) : (
                  teams.map(team => (
                    <option key={team.id} value={team.id}>{team.name}</option>
                  ))
                )}
              </select>
              
              {/* Search input */}
              <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="py-1 px-2 bg-gray-800 border border-gray-700 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 w-full sm:w-36"
                aria-label="Search team members"
              />
              
              {/* Time filter */}
              <select
                value={timeFilter}
                onChange={(e) => setTimeFilter(e.target.value)}
                className="py-1 px-2 bg-gray-800 border border-gray-700 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                aria-label="Select time period"
              >
                <option value="day">Daily</option>
                <option value="week">Weekly</option>
                <option value="month">Monthly</option>
                <option value="ytd">Year</option>
              </select>
              
              {/* Chart type */}
              <select
                value={chartType}
                onChange={(e) => setChartType(e.target.value)}
                className="py-1 px-2 bg-gray-800 border border-gray-700 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                aria-label="Select chart type"
              >
                <option value="line">Line</option>
                <option value="bar">Bar</option>
                <option value="pie">Pie</option>
                <option value="doughnut">Doughnut</option>
              </select>
            </div>
          </div>

          {/* Error message */}
          {error && (
            <div className="mb-2 bg-red-900 bg-opacity-50 text-red-200 px-3 py-2 rounded text-sm">
              <div className="flex justify-between items-center">
                <span>{error}</span>
                <button 
                  onClick={() => setError(null)}
                  className="text-red-200 hover:text-white ml-2"
                  aria-label="Dismiss error"
                >
                  &times;
                </button>
              </div>
            </div>
          )}

          <div className="flex flex-col md:flex-row md:space-x-3">
            {/* User List Panel */}
            <UserList 
              users={filteredUsers}
              selectedTeam={selectedTeam}
              selectedUser={selectedUser}
              loading={loading}
              isMobileMenuOpen={isMobileMenuOpen}
              handleUserSelect={handleUserSelect}
            />
            
            {/* User Dashboard Panel - Pass centralized state */}
            <UserDashboard 
              selectedUser={selectedUser}
              selectedTeam={selectedTeam}
              userStats={userStats}
              userLoading={userLoading}
              isMobileMenuOpen={isMobileMenuOpen}
              timeFilter={timeFilter}
              chartType={chartType}
              getTimeProperty={getTimeProperty}
              db={db}
              timeCardData={timeCardData}
              // Pass centralized vehicle data
              vehicles={vehicles}
              availableWeeks={availableWeeks}
              selectedWeek={selectedWeek}
              vehicleStats={vehicleStats}
              aggregatedVehicleData={aggregatedVehicleData}
            />
          </div>
          
          {/* Team Analytics Summary - Pass centralized state */}
          <TeamSummary 
            selectedUser={selectedUser}
            selectedTeam={selectedTeam}
            teamStats={teamStats}
            topPerformers={topPerformers}
            loading={loading}
            selectedWeek={selectedWeek}
            userStats={userStats}
            timeCardData={timeCardData}
            vehicleStats={vehicleStats}
            aggregatedVehicleData={aggregatedVehicleData}
          />
          
          {/* VehicleTracker with Team Sync Functions */}
          {selectedUser && 
            <VehicleTracker 
              selectedUser={selectedUser}
              selectedTeam={selectedTeam}
              db={db}
              timeFilter={timeFilter}
              userStats={userStats}
              // Pass state setters up
              setVehicleStats={setVehicleStats}
              setVehicles={setVehicles}
              setAvailableWeeks={setAvailableWeeks}
              setSelectedWeek={setSelectedWeek}
              setAggregatedVehicleData={setAggregatedVehicleData}
              // Pass carryover functions
              carryOverUnsecuredVehicles={carryOverUnsecuredVehicles}
              initializeWeekWithCarryOvers={initializeWeekWithCarryOvers}
              checkAndSecureCarriedOverVehicle={checkAndSecureCarriedOverVehicle}
              markVehicleAsSecuredInFutureWeeks={markVehicleAsSecuredInFutureWeeks}
              // Pass team sync function
              markVINAsSecuredAcrossTeam={markVINAsSecuredAcrossTeam}
              // Pass current state values too
              vehicles={vehicles}
              availableWeeks={availableWeeks}
              selectedWeek={selectedWeek}
              vehicleStats={vehicleStats}
              aggregatedVehicleData={aggregatedVehicleData}
            />
          }
        </div>
      </div>
    </div>
  );
}

export default Analytics;