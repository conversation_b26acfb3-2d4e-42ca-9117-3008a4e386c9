import { useState, useEffect } from 'react';

/**
 * Custom hook to handle responsive layout calculations
 */
export function useResponsiveLayout() {
  const [screenConfig, setScreenConfig] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
    isSmallScreen: typeof window !== 'undefined' ? window.innerWidth < 768 : false,
    isMediumScreen: typeof window !== 'undefined' ? window.innerWidth >= 768 && window.innerWidth <= 1024 : false,
    isLargeScreen: typeof window !== 'undefined' ? window.innerWidth > 1024 : false,
    isLandscape: typeof window !== 'undefined' ? window.innerWidth > window.innerHeight : false,
    isPad: typeof window !== 'undefined' ? 
      (window.innerWidth >= 768 && window.innerWidth <= 1024) ||
      (/iPad/.test(navigator.userAgent) || (/Macintosh/.test(navigator.userAgent) && 'ontouchend' in document))
      : false,
    isPhone: typeof window !== 'undefined' ? 
      (/iPhone|iPod/.test(navigator.userAgent)) : false,
    safeAreaInsets: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    }
  });

  const updateScreenConfig = () => {
    if (typeof window === 'undefined') return;
    
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    // Get CSS safe area insets if available
    let safeAreaInsets = {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    };
    
    if (window.CSS && window.CSS.supports && 
        window.CSS.supports('padding-top: env(safe-area-inset-top)')) {
      const computedStyle = getComputedStyle(document.documentElement);
      safeAreaInsets = {
        top: parseInt(computedStyle.getPropertyValue('--safe-area-inset-top') || '0', 10),
        right: parseInt(computedStyle.getPropertyValue('--safe-area-inset-right') || '0', 10),
        bottom: parseInt(computedStyle.getPropertyValue('--safe-area-inset-bottom') || '0', 10),
        left: parseInt(computedStyle.getPropertyValue('--safe-area-inset-left') || '0', 10)
      };
    }
    
    setScreenConfig({
      width,
      height,
      isSmallScreen: width < 768,
      isMediumScreen: width >= 768 && width <= 1024,
      isLargeScreen: width > 1024,
      isLandscape: width > height,
      isPad: (width >= 768 && width <= 1024) || 
             (typeof navigator !== 'undefined' && (/iPad/.test(navigator.userAgent) || 
             (/Macintosh/.test(navigator.userAgent) && 'ontouchend' in document))),
      isPhone: typeof navigator !== 'undefined' && (/iPhone|iPod/.test(navigator.userAgent)),
      safeAreaInsets
    });
  };

  useEffect(() => {
    // Initial update
    updateScreenConfig();
    
    // Listen for resize and orientation change events
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', updateScreenConfig);
      window.addEventListener('orientationchange', () => {
        // Short delay to ensure dimensions are updated
        setTimeout(updateScreenConfig, 100);
      });
      
      // Custom orientation changed event
      window.addEventListener('orientationChanged', updateScreenConfig);
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', updateScreenConfig);
        window.removeEventListener('orientationchange', updateScreenConfig);
        window.removeEventListener('orientationChanged', updateScreenConfig);
      }
    };
  }, []);

  // Dynamically calculate panel visibility based on screen config
  const adjustPanelsForOrientation = (detailsVisible, locationsVisible) => {
    if (screenConfig.isSmallScreen || (screenConfig.isMediumScreen && !screenConfig.isLandscape)) {
      // On small screens or iPad portrait, only show one panel at most
      if (detailsVisible && locationsVisible) {
        return { detailsVisible, locationsVisible: false };
      }
    }
    return { detailsVisible, locationsVisible };
  };

  return {
    screenConfig,
    updateScreenConfig,
    adjustPanelsForOrientation
  };
}

export default useResponsiveLayout;