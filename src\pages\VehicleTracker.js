import React, { useState, useEffect, useRef } from 'react';
import { collection, getDocs, doc, getDoc, query, where, orderBy, limit, setDoc, updateDoc, serverTimestamp, addDoc, deleteDoc } from 'firebase/firestore';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

function VehicleTracker({ 
  selectedUser, 
  selectedTeam, // NEW: Added team prop for sync
  db, 
  timeFilter, 
  userStats,
  // Received from parent Analytics component
  setVehicleStats,
  setVehicles, 
  setAvailableWeeks, 
  setSelectedWeek,
  setAggregatedVehicleData,
  // Received carryover functions
  carryOverUnsecuredVehicles,
  initializeWeekWithCarryOvers,
  checkAndSecureCarriedOverVehicle,
  markVehicleAsSecuredInFutureWeeks,
  // NEW: Team sync function
  markVINAsSecuredAcrossTeam,
  // Existing data from parent
  vehicles = [],
  availableWeeks = [],
  selectedWeek = null,
  vehicleStats = { totalScans: 0, totalFound: 0, totalSecured: 0, recoveryRate: 0, dateRange: { start: '', end: '' }},
  aggregatedVehicleData = { month: {}, ytd: {} }
}) {
  // Local state for editing/UI management - not shared with parent
  const [editingVehicle, setEditingVehicle] = useState(null);
  const [editVehicleData, setEditVehicleData] = useState({});
  const [showVehicleForm, setShowVehicleForm] = useState(false);
  const [newVehicle, setNewVehicle] = useState({
    date: new Date().toISOString().split('T')[0],
    vehicle: '',
    vin: '',
    plateNumber: '',
    accountNumber: '',
    financier: '',
    address: '',
    status: 'FOUND',
    securedDate: new Date().toISOString().split('T')[0]
  });
  
  // Print functionality state
  const [showPrintView, setShowPrintView] = useState(false);
  const printRef = useRef(null);
  
  // Edit scan amount state
  const [editingScans, setEditingScans] = useState(false);
  const [editScanAmount, setEditScanAmount] = useState(0);
  
  // YTD and Lifetime stats
  const [lifetimeSecuredCount, setLifetimeSecuredCount] = useState(0);
  const [ytdStats, setYtdStats] = useState({
    totalFound: 0,
    totalSecured: 0,
    totalScans: 0,
    recoveryRate: 0,
    month: {
      totalFound: 0,
      totalSecured: 0,
      totalScans: 0,
      recoveryRate: 0
    }
  });

  // NEW: Team sync notifications state
  const [teamSyncNotifications, setTeamSyncNotifications] = useState([]);
  
  // Calculate weekly stats properly (excluding carryovers from found count)
  const calculateWeeklyStats = () => {
    if (!vehicles || vehicles.length === 0) {
      return {
        weeklyFound: 0,
        weeklySecured: 0,
        totalCarryover: 0,
        weeklyRecoveryRate: 0
      };
    }
    
    // Weekly Found: Only count vehicles found THIS week (not carryovers)
    const weeklyFound = vehicles.filter(v => 
      !v.carriedOver && (v.status === 'FOUND' || v.status === 'SECURED')
    ).length;
    
    // Weekly Secured: Count newly secured + carryover vehicles that were secured this week
    const weeklySecured = vehicles.filter(v => v.status === 'SECURED').length;
    
    // Total carryover vehicles
    const totalCarryover = vehicles.filter(v => v.carriedOver).length;
    
    // Recovery rate based on weekly found + carryover vehicles
    const totalAvailable = weeklyFound + totalCarryover;
    const weeklyRecoveryRate = totalAvailable > 0 ? (weeklySecured / totalAvailable) * 100 : 0;
    
    return {
      weeklyFound,
      weeklySecured,
      totalCarryover,
      weeklyRecoveryRate
    };
  };
  
  // Calculate Month and YTD stats
  const calculateMonthAndYTDStats = async () => {
    if (!selectedUser || !db) return;
    
    try {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth();
      
      const yearStart = new Date(currentYear, 0, 1);
      const yearEnd = new Date(currentYear, 11, 31, 23, 59, 59);
      const monthStart = new Date(currentYear, currentMonth, 1);
      const monthEnd = new Date(currentYear, currentMonth + 1, 0, 23, 59, 59);
      
      const weeksQuery = query(
        collection(db, 'users', selectedUser.id, 'vehicleWeeks'),
        orderBy('startDate', 'desc')
      );
      
      const weeksSnapshot = await getDocs(weeksQuery);
      let ytdTotalFound = 0;
      let ytdTotalSecured = 0;
      let ytdTotalScans = 0;
      let monthTotalFound = 0;
      let monthTotalSecured = 0;
      let monthTotalScans = 0;
      
      // Get current week's live stats
      const currentWeeklyStats = calculateWeeklyStats();
      let currentWeekIncluded = false;
      
      for (const weekDoc of weeksSnapshot.docs) {
        const weekData = weekDoc.data();
        const weekStartDate = weekData.startDate?.toDate();
        const weekEndDate = weekData.endDate?.toDate();
        
        if (weekStartDate) {
          // YTD calculations
          if (weekStartDate >= yearStart && weekStartDate <= yearEnd) {
            // For current week, use live data
            if (selectedWeek && weekDoc.id === selectedWeek) {
              ytdTotalFound += currentWeeklyStats.weeklyFound;
              ytdTotalSecured += currentWeeklyStats.weeklySecured;
              ytdTotalScans += (vehicleStats.totalScans || 0);
              currentWeekIncluded = true;
            } else {
              ytdTotalFound += (weekData.totalFound || 0);
              ytdTotalSecured += (weekData.totalSecured || 0);
              ytdTotalScans += (weekData.totalScans || 0);
            }
          }
          
          // Monthly calculations
          if ((weekStartDate <= monthEnd && weekEndDate >= monthStart) || 
              (weekStartDate >= monthStart && weekStartDate <= monthEnd)) {
            
            // For current week, use live data
            if (selectedWeek && weekDoc.id === selectedWeek) {
              monthTotalFound += currentWeeklyStats.weeklyFound;
              monthTotalSecured += currentWeeklyStats.weeklySecured;
              monthTotalScans += (vehicleStats.totalScans || 0);
            } else {
              monthTotalFound += (weekData.totalFound || 0);
              monthTotalSecured += (weekData.totalSecured || 0);
              monthTotalScans += (weekData.totalScans || 0);
            }
          }
        }
      }
      
      // If current week wasn't found in database but we have data, add it
      if (!currentWeekIncluded && vehicles && vehicles.length > 0) {
        monthTotalFound += currentWeeklyStats.weeklyFound;
        monthTotalSecured += currentWeeklyStats.weeklySecured;
        monthTotalScans += (vehicleStats.totalScans || 0);
        
        ytdTotalFound += currentWeeklyStats.weeklyFound;
        ytdTotalSecured += currentWeeklyStats.weeklySecured;
        ytdTotalScans += (vehicleStats.totalScans || 0);
      }
      
      const ytdRecoveryRate = ytdTotalFound > 0 ? (ytdTotalSecured / ytdTotalFound) * 100 : 0;
      const monthRecoveryRate = monthTotalFound > 0 ? (monthTotalSecured / monthTotalFound) * 100 : 0;
      
      const newYtdStats = {
        totalFound: ytdTotalFound,
        totalSecured: ytdTotalSecured,
        totalScans: ytdTotalScans,
        recoveryRate: ytdRecoveryRate,
        month: {
          totalFound: monthTotalFound,
          totalSecured: monthTotalSecured,
          totalScans: monthTotalScans,
          recoveryRate: monthRecoveryRate
        }
      };
      
      setYtdStats(newYtdStats);
      
      // Update aggregated data in parent component too
      setAggregatedVehicleData({
        month: newYtdStats.month,
        ytd: {
          totalFound: ytdTotalFound,
          totalSecured: ytdTotalSecured,
          totalScans: ytdTotalScans,
          recoveryRate: ytdRecoveryRate
        }
      });
      
    } catch (error) {
      console.error("Error calculating Month and YTD stats:", error);
    }
  };
  
  // Calculate lifetime secured count
  const calculateLifetimeSecuredCount = async () => {
    if (!selectedUser || !db) return;
    
    try {
      const currentYear = new Date().getFullYear();
      const yearStart = new Date(currentYear, 0, 1);
      const yearEnd = new Date(currentYear, 11, 31, 23, 59, 59);
      
      const weeksQuery = query(
        collection(db, 'users', selectedUser.id, 'vehicleWeeks'),
        orderBy('startDate', 'desc')
      );
      
      const weeksSnapshot = await getDocs(weeksQuery);
      let totalSecured = 0;
      
      for (const weekDoc of weeksSnapshot.docs) {
        const weekData = weekDoc.data();
        const weekStartDate = weekData.startDate?.toDate();
        
        if (weekStartDate && weekStartDate >= yearStart && weekStartDate <= yearEnd) {
          const vehiclesQuery = query(
            collection(db, 'users', selectedUser.id, 'vehicleWeeks', weekDoc.id, 'vehicles')
          );
          
          const vehiclesSnapshot = await getDocs(vehiclesQuery);
          vehiclesSnapshot.forEach(vehicleDoc => {
            const vehicleData = vehicleDoc.data();
            if (vehicleData.status === 'SECURED') {
              totalSecured++;
            }
          });
        }
      }
      
      setLifetimeSecuredCount(totalSecured);
    } catch (error) {
      console.error("Error calculating lifetime secured count:", error);
    }
  };

  // NEW: Function to show team sync notifications
  const showTeamSyncNotification = (vehicleData) => {
    if (vehicleData.autoSecuredFromTeam && vehicleData.securedByUserName) {
      console.log(`🎉 Vehicle ${vehicleData.vehicle} (VIN: ${vehicleData.vin}) was secured by your teammate ${vehicleData.securedByUserName}!`);
      
      const notification = {
        id: Date.now(),
        type: 'team_sync',
        message: `${vehicleData.securedByUserName} secured ${vehicleData.vehicle} (VIN: ${vehicleData.vin})`,
        timestamp: new Date(),
        vehicleId: vehicleData.id
      };
      
      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep only 5 most recent
      
      // Auto-remove notification after 10 seconds
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 10000);
    }
  };

  // NEW: Check for team-secured vehicles when vehicles change
  useEffect(() => {
    if (vehicles && vehicles.length > 0) {
      vehicles.forEach(vehicle => {
        if (vehicle.autoSecuredFromTeam && vehicle.securedByTeammate) {
          showTeamSyncNotification(vehicle);
        }
      });
    }
  }, [vehicles]);
  
  // Format numbers for display
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0';
    return new Intl.NumberFormat('en-US').format(Math.round(num * 100) / 100);
  };
  
  // Get user display name
  const getUserDisplayName = () => {
    if (!selectedUser) return "";
    
    if (selectedUser.displayName) {
      return selectedUser.displayName.toUpperCase();
    } else if (selectedUser.email) {
      return selectedUser.email.split('@')[0].toUpperCase();
    }
    return "USER";
  };

  // Get week display name
  const getWeekDisplayName = () => {
    if (!selectedWeek || !availableWeeks) return "CURRENT WEEK";
    
    const week = availableWeeks.find(w => w.id === selectedWeek);
    return week ? (week.displayRange || "WEEK") : "CURRENT WEEK";
  };

  // FIXED PDF Export Function with proper column sizing
  const exportToPDF = () => {
    if (!vehicles || vehicles.length === 0) {
      alert("No vehicle data to export. Please add some vehicles first.");
      return;
    }
    
    try {
      const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
      const usableWidth = pageWidth - 20; // Account for margins
      
      // Header
      doc.setFillColor(41, 128, 185);
      doc.rect(0, 0, pageWidth, 30, 'F');
      
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(24);
      doc.setFont('helvetica', 'bold');
      doc.text('VEHICLE TRACKING REPORT', pageWidth / 2, 20, { align: 'center' });

      // Reset text color
      doc.setTextColor(0, 0, 0);

      let currentY = 40;

      // User info box
      doc.setFillColor(245, 245, 245);
      doc.setDrawColor(200, 200, 200);
      doc.rect(10, currentY, (pageWidth - 30) / 2, 25, 'FD');
      
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.text('AGENT INFORMATION', 15, currentY + 8);
      
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      doc.text('Agent: ' + getUserDisplayName(), 15, currentY + 15);
      doc.text('Report Date: ' + new Date().toLocaleDateString(), 15, currentY + 20);

      // Week info box
      doc.setFillColor(230, 247, 255);
      doc.setDrawColor(52, 152, 219);
      doc.rect((pageWidth + 10) / 2, currentY, (pageWidth - 30) / 2, 25, 'FD');
      
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(12);
      doc.text('PERIOD INFORMATION', (pageWidth + 20) / 2, currentY + 8);
      
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      doc.text('Week: ' + getWeekDisplayName(), (pageWidth + 20) / 2, currentY + 15);
      // Calculate proper vehicle count: just secured vehicles
      const properVehicleCount = vehicles.filter(v => v.status === 'SECURED').length;
      
      doc.text('Total Vehicles: ' + properVehicleCount, (pageWidth + 20) / 2, currentY + 20);

      currentY += 35;

      // Calculate stats
      const weeklyStats = calculateWeeklyStats();

      // PERFORMANCE SUMMARY - NO OVERLAPPING
      doc.setFillColor(240, 248, 255);
      doc.setDrawColor(52, 152, 219);
      doc.setLineWidth(1);
      doc.rect(10, currentY, pageWidth - 20, 55, 'FD');
      
      // Title
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(14);
      doc.setTextColor(52, 152, 219);
      doc.text('PERFORMANCE SUMMARY', 15, currentY + 12);
      
      // WEEK row
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(10);
      doc.setTextColor(34, 139, 34);
      doc.text('WEEK:', 15, currentY + 25);
      
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(9);
      doc.setTextColor(0, 0, 0);
      doc.text('Scans: ' + String(vehicleStats.totalScans || 0), 70, currentY + 25);
      doc.text('Found: ' + String(weeklyStats.weeklyFound), 140, currentY + 25);
      doc.text('Secured: ' + String(weeklyStats.weeklySecured), 190, currentY + 25);
      doc.text('Carryover: ' + String(weeklyStats.totalCarryover), 250, currentY + 25);
      
      // MONTH row
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(10);
      doc.setTextColor(255, 140, 0);
      doc.text('MONTH:', 15, currentY + 35);
      
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(9);
      doc.setTextColor(0, 0, 0);
      doc.text('Scans: ' + String(ytdStats.month?.totalScans || 0), 70, currentY + 35);
      doc.text('Found: ' + String(ytdStats.month?.totalFound || 0), 140, currentY + 35);
      doc.text('Secured: ' + String(ytdStats.month?.totalSecured || 0), 190, currentY + 35);
      doc.text('Rate: ' + String((ytdStats.month?.recoveryRate || 0).toFixed(1)) + '%', 250, currentY + 35);
      
      // YEAR row
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(10);
      doc.setTextColor(30, 144, 255);
      doc.text('YEAR:', 15, currentY + 45);
      
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(9);
      doc.setTextColor(0, 0, 0);
      doc.text('Scans: ' + String(ytdStats.totalScans || 0), 70, currentY + 45);
      doc.text('Found: ' + String(ytdStats.totalFound || 0), 140, currentY + 45);
      doc.text('Secured: ' + String(ytdStats.totalSecured || 0), 190, currentY + 45);
      doc.text('Rate: ' + String((ytdStats.recoveryRate || 0).toFixed(1)) + '%', 250, currentY + 45);

      currentY += 65;

      // ============= VEHICLE TABLES WITH PROPER COLUMN SIZING =============
      
      // Separate vehicles: Secured vehicles go to "new" section, only unsecured carryovers stay in carryover section
      const newVehicles = vehicles.filter(v => !v.carriedOver || v.status === 'SECURED');
      const carryoverVehicles = vehicles.filter(v => v.carriedOver && v.status !== 'SECURED');

      // Table headers
      const tableHeaders = [
        'Date', 'Vehicle', 'VIN', 'Plate #', 'Account #', 'Financier', 'Address', 'Status', 'C/O', 'Secured Date', 'Team'
      ];

      // FIXED: Calculate proper column widths that use full page width
      const totalTableWidth = usableWidth;
      const columnWidths = {
        0: totalTableWidth * 0.08,  // Date - 8%
        1: totalTableWidth * 0.18,  // Vehicle - 18%
        2: totalTableWidth * 0.10,  // VIN - 10%
        3: totalTableWidth * 0.08,  // Plate # - 8%
        4: totalTableWidth * 0.10,  // Account # - 10%
        5: totalTableWidth * 0.12,  // Financier - 12%
        6: totalTableWidth * 0.20,  // Address - 20%
        7: totalTableWidth * 0.06,  // Status - 6%
        8: totalTableWidth * 0.04,  // C/O - 4%
        9: totalTableWidth * 0.08,  // Secured Date - 8%
        10: totalTableWidth * 0.04  // Team - 4%
      };

      // NEW VEHICLES section
      if (newVehicles.length > 0) {
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(41, 128, 185);
        doc.text('NEW VEHICLES', pageWidth / 2, currentY, { align: 'center' });
        currentY += 10;
        
        const newVehicleTableData = newVehicles.map(vehicle => [
          vehicle.date || '',
          vehicle.vehicle || '',
          vehicle.vin || '',
          vehicle.plateNumber || '',
          vehicle.accountNumber || '',
          vehicle.financier || '',
          vehicle.address && vehicle.address.length > 30 ? vehicle.address.substring(0, 30) + '...' : vehicle.address || '',
          vehicle.status || '',
          vehicle.carriedOver ? 'YES' : 'NO',
          vehicle.securedDate || '',
          vehicle.autoSecuredFromTeam ? 'TEAM' : 'SELF' // NEW: Team indicator
        ]);

        if (typeof autoTable === 'function') {
          autoTable(doc, {
            head: [tableHeaders],
            body: newVehicleTableData,
            startY: currentY,
            tableWidth: totalTableWidth,
            styles: {
              fontSize: 7,
              cellPadding: 2,
              overflow: 'linebreak',
              halign: 'left'
            },
            headStyles: {
              fillColor: [41, 128, 185],
              textColor: [255, 255, 255],
              fontStyle: 'bold',
              fontSize: 8,
              halign: 'center'
            },
            alternateRowStyles: {
              fillColor: [248, 249, 250]
            },
            columnStyles: columnWidths,
            didParseCell: function(data) {
              if (data.column.index === 7) {
                const status = data.cell.raw;
                if (status === 'SECURED') {
                  data.cell.styles.fillColor = [40, 167, 69];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                } else if (status === 'FOUND') {
                  data.cell.styles.fillColor = [255, 193, 7];
                  data.cell.styles.textColor = [0, 0, 0];
                  data.cell.styles.fontStyle = 'bold';
                } else if (status === 'NOT FOUND') {
                  data.cell.styles.fillColor = [220, 53, 69];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
              }
              if (data.column.index === 8) {
                const carryover = data.cell.raw;
                if (carryover === 'YES') {
                  data.cell.styles.fillColor = [255, 152, 0];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
              }
              // NEW: Style team column
              if (data.column.index === 10) {
                const teamSecured = data.cell.raw;
                if (teamSecured === 'TEAM') {
                  data.cell.styles.fillColor = [138, 43, 226];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
              }
            },
            margin: { left: 10, right: 10 }
          });
        }
        
        currentY = doc.lastAutoTable.finalY + 20;
      }

      // Add separation line between sections
      if (newVehicles.length > 0 && carryoverVehicles.length > 0) {
        doc.setDrawColor(0, 0, 0);
        doc.setLineWidth(2);
        doc.line(10, currentY, pageWidth - 10, currentY);
        currentY += 20;
      }

      // CARRYOVER VEHICLES section
      if (carryoverVehicles.length > 0) {
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(255, 152, 0);
        doc.text('CARRYOVER VEHICLES', pageWidth / 2, currentY, { align: 'center' });
        currentY += 10;
        
        const carryoverTableData = carryoverVehicles.map(vehicle => [
          vehicle.date || '',
          vehicle.vehicle || '',
          vehicle.vin || '',
          vehicle.plateNumber || '',
          vehicle.accountNumber || '',
          vehicle.financier || '',
          vehicle.address && vehicle.address.length > 30 ? vehicle.address.substring(0, 30) + '...' : vehicle.address || '',
          vehicle.status || '',
          vehicle.carriedOver ? 'YES' : 'NO',
          vehicle.securedDate || '',
          vehicle.autoSecuredFromTeam ? 'TEAM' : 'SELF' // NEW: Team indicator
        ]);

        if (typeof autoTable === 'function') {
          autoTable(doc, {
            head: [tableHeaders],
            body: carryoverTableData,
            startY: currentY,
            tableWidth: totalTableWidth,
            styles: {
              fontSize: 7,
              cellPadding: 2,
              overflow: 'linebreak',
              halign: 'left'
            },
            headStyles: {
              fillColor: [255, 152, 0],
              textColor: [255, 255, 255],
              fontStyle: 'bold',
              fontSize: 8,
              halign: 'center'
            },
            alternateRowStyles: {
              fillColor: [248, 249, 250]
            },
            columnStyles: columnWidths,
            didParseCell: function(data) {
              if (data.column.index === 7) {
                const status = data.cell.raw;
                if (status === 'SECURED') {
                  data.cell.styles.fillColor = [40, 167, 69];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                } else if (status === 'FOUND') {
                  data.cell.styles.fillColor = [255, 193, 7];
                  data.cell.styles.textColor = [0, 0, 0];
                  data.cell.styles.fontStyle = 'bold';
                } else if (status === 'NOT FOUND') {
                  data.cell.styles.fillColor = [220, 53, 69];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
              }
              if (data.column.index === 8) {
                const carryover = data.cell.raw;
                if (carryover === 'YES') {
                  data.cell.styles.fillColor = [255, 152, 0];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
              }
              // NEW: Style team column
              if (data.column.index === 10) {
                const teamSecured = data.cell.raw;
                if (teamSecured === 'TEAM') {
                  data.cell.styles.fillColor = [138, 43, 226];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
              }
            },
            margin: { left: 10, right: 10 }
          });
        }
      }

      // Footer
      const footerY = pageHeight - 15;
      doc.setDrawColor(200, 200, 200);
      doc.line(10, footerY - 5, pageWidth - 10, footerY - 5);
      
      doc.setFontSize(8);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(64, 64, 64);
      doc.text('Generated by NWRepo Analytics System', 15, footerY);
      doc.text('Page 1 of 1 • ' + new Date().toLocaleString(), pageWidth - 15, footerY, { align: 'right' });

      // Save
      const fileName = 'Vehicle_Report_' + getUserDisplayName() + '_' + getWeekDisplayName().replace(/[^a-zA-Z0-9]/g, '_') + '_' + new Date().toISOString().split('T')[0] + '.pdf';
      doc.save(fileName);
      
    } catch (error) {
      console.error("Error generating PDF:", error);
      alert("There was an error generating the PDF. Please try again.");
    }
  };

  // Handle printing
  const handlePrint = () => {
    if (vehicles.length === 0) {
      alert("No vehicle data to export.");
      return;
    }
    
    setShowPrintView(true);
    
    setTimeout(() => {
      window.print();
      setTimeout(() => {
        setShowPrintView(false);
      }, 500);
    }, 300);
  };

  // Print view component
  const PrintView = () => {
    const currentDate = new Date().toLocaleDateString();
    const weeklyStats = calculateWeeklyStats();
    
    return (
      <div ref={printRef} className="print-container p-8 bg-white text-black">
        <style type="text/css" media="print">
          {`
            @page { 
              size: landscape;
              margin: 0.5in;
            }
            
            body { 
              font-family: Arial, sans-serif;
              color: black;
              background-color: white;
            }
            
            .print-container {
              background-color: white;
              color: black;
            }
            
            table {
              width: 100%;
              border-collapse: collapse;
            }
            
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }
            
            th {
              background-color: #f2f2f2;
              font-weight: bold;
            }
            
            tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            
            .print-header {
              margin-bottom: 20px;
            }
            
            .print-title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            
            .print-subtitle {
              font-size: 16px;
              margin-bottom: 5px;
            }
            
            .print-summary {
              margin-bottom: 20px;
              padding: 10px;
              border: 1px solid #ddd;
              background-color: #f5f5f5;
            }
            
            .print-summary-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            
            .print-summary-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 5px;
            }
            
            .print-summary-label {
              font-weight: bold;
            }
            
            .print-footer {
              margin-top: 20px;
              font-size: 10px;
              text-align: center;
              color: #666;
            }
            
            @media screen {
              .print-only {
                display: none;
              }
            }
          `}
        </style>
      
        <div className="print-header">
          <div className="print-title">Vehicle Tracking Report</div>
          <div className="print-subtitle">User: {getUserDisplayName()}</div>
          <div className="print-subtitle">Week: {vehicleStats.dateRange.start} - {vehicleStats.dateRange.end}</div>
          <div className="print-subtitle">Total Vehicles: {getProperVehicleCount()}</div>
          <div className="print-subtitle">Report Generated: {currentDate}</div>
        </div>
        
        <div className="print-summary">
          <div className="print-summary-title">Weekly Summary</div>
          <div className="print-summary-row">
            <div className="print-summary-label">Weekly Scans:</div>
            <div>{vehicleStats.totalScans}</div>
          </div>
          <div className="print-summary-row">
            <div className="print-summary-label">Weekly Found:</div>
            <div>{weeklyStats.weeklyFound}</div>
          </div>
          <div className="print-summary-row">
            <div className="print-summary-label">Weekly Secured:</div>
            <div>{weeklyStats.weeklySecured}</div>
          </div>
          <div className="print-summary-row">
            <div className="print-summary-label">Carryover Vehicles:</div>
            <div>{weeklyStats.totalCarryover}</div>
          </div>
          <div className="print-summary-row">
            <div className="print-summary-label">Weekly Recovery Rate:</div>
            <div>{formatNumber(weeklyStats.weeklyRecoveryRate)}%</div>
          </div>
        </div>
        
        <div className="print-summary">
          <div className="print-summary-title">Monthly Summary</div>
          <div className="print-summary-row">
            <div className="print-summary-label">Month Total Scans:</div>
            <div>{ytdStats.month?.totalScans || 0}</div>
          </div>
          <div className="print-summary-row">
            <div className="print-summary-label">Month Total Found:</div>
            <div>{ytdStats.month?.totalFound || 0}</div>
          </div>
          <div className="print-summary-row">
            <div className="print-summary-label">Month Total Secured:</div>
            <div>{ytdStats.month?.totalSecured || 0}</div>
          </div>
          <div className="print-summary-row">
            <div className="print-summary-label">Month Recovery Rate:</div>
            <div>{formatNumber(ytdStats.month?.recoveryRate || 0)}%</div>
          </div>
        </div>
        
        <div className="print-summary">
          <div className="print-summary-title">Year-to-Date Summary</div>
          <div className="print-summary-row">
            <div className="print-summary-label">YTD Total Scans:</div>
            <div>{ytdStats.totalScans}</div>
          </div>
          <div className="print-summary-row">
            <div className="print-summary-label">YTD Total Found:</div>
            <div>{ytdStats.totalFound}</div>
          </div>
          <div className="print-summary-row">
            <div className="print-summary-label">YTD Total Secured:</div>
            <div>{ytdStats.totalSecured}</div>
          </div>
          <div className="print-summary-row">
            <div className="print-summary-label">YTD Recovery Rate:</div>
            <div>{formatNumber(ytdStats.recoveryRate)}%</div>
          </div>
        </div>
      
        <table className="vehicle-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Vehicle</th>
              <th>VIN</th>
              <th>Plate #</th>
              <th>Account #</th>
              <th>Financier</th>
              <th>Address</th>
              <th>Status</th>
              <th>C/O</th>
              <th>Secured Date</th>
              <th>Team</th>
            </tr>
          </thead>
          <tbody>
            {vehicles.length === 0 ? (
              <tr>
                <td colSpan="11" style={{ textAlign: 'center' }}>No vehicles found for this week.</td>
              </tr>
            ) : (
              vehicles.map((vehicle, index) => (
                <tr key={index}>
                  <td>{vehicle.date || ''}</td>
                  <td>{vehicle.vehicle || ''}</td>
                  <td>{vehicle.vin || ''}</td>
                  <td>{vehicle.plateNumber || ''}</td>
                  <td>{vehicle.accountNumber || ''}</td>
                  <td>{vehicle.financier || ''}</td>
                  <td>{vehicle.address || ''}</td>
                  <td>{vehicle.status || ''}</td>
                  <td>{vehicle.carriedOver ? 'YES' : 'NO'}</td>
                  <td>{vehicle.securedDate || ''}</td>
                  <td>{vehicle.autoSecuredFromTeam ? 'TEAM' : 'SELF'}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
        
        <div className="print-footer">
          Generated from NWRepo Analytics on {currentDate} for {getUserDisplayName()}
        </div>
      </div>
    );
  };

  // Load vehicle weeks when user changes
  useEffect(() => {
    if (selectedUser && db) {
      loadAvailableWeeks();
      calculateLifetimeSecuredCount();
      calculateMonthAndYTDStats();
    }
  }, [selectedUser, db]);

  // Update parent when selected week changes
  useEffect(() => {
    if (selectedWeek) {
      setSelectedWeek(selectedWeek);
      loadVehicleDataForWeek(selectedWeek);
    }
  }, [selectedWeek]);

  // Recalculate monthly and YTD stats when vehicles change
  useEffect(() => {
    if (selectedUser && db && vehicles.length > 0) {
      calculateMonthAndYTDStats();
    }
  }, [vehicles]);

  // Update aggregated data when YTD stats change
  useEffect(() => {
    setAggregatedVehicleData({
      month: ytdStats.month || { totalScans: 0, totalFound: 0, totalSecured: 0, recoveryRate: 0 },
      ytd: {
        totalFound: ytdStats.totalFound || 0,
        totalSecured: ytdStats.totalSecured || 0,
        totalScans: ytdStats.totalScans || 0,
        recoveryRate: ytdStats.recoveryRate || 0
      }
    });
  }, [ytdStats]);

  // Load available weeks for vehicle tracking
  const loadAvailableWeeks = async () => {
    if (!db || !selectedUser) return;
    
    try {
      // Reset vehicle state
      setVehicles([]);
      
      // Send updated stats to parent
      setVehicleStats({
        totalScans: 0, 
        totalFound: 0,
        totalSecured: 0,
        recoveryRate: 0,
        dateRange: { start: '', end: '' }
      });
      
      const weeksQuery = query(
        collection(db, 'users', selectedUser.id, 'vehicleWeeks'),
        orderBy('endDate', 'desc')
      );
      
      const weeksSnapshot = await getDocs(weeksQuery);
      const weeksData = weeksSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        startDate: doc.data().startDate?.toDate(),
        endDate: doc.data().endDate?.toDate()
      }));
      
      // Update weeks in both local and parent state
      setAvailableWeeks(weeksData);
      
      const today = new Date();
      let currentWeek = weeksData.find(week => 
        week.startDate <= today && week.endDate >= today
      );
      
      const mostRecentWeek = weeksData.length > 0 ? weeksData[0] : null;
      
      // Pick the appropriate week to select
      let weekToSelect = null;
      if (currentWeek) {
        weekToSelect = currentWeek;
      } else if (mostRecentWeek) {
        weekToSelect = mostRecentWeek;
      }
      
      if (weekToSelect) {
        // Set selected week
        setSelectedWeek(weekToSelect.id);
        
        // Carry over unsecured vehicles if needed
        await initializeWeekWithCarryOvers(selectedUser.id, weekToSelect.id);
        
        // Load vehicle data for the selected week
        await loadVehicleDataForWeek(weekToSelect.id);
      }
      
      // Load aggregated data for month and YTD
      await calculateMonthAndYTDStats();
      
    } catch (error) {
      console.error("Error loading available weeks:", error);
      setAvailableWeeks([]);
    }
  };

  // Load vehicle data for a specific week
  const loadVehicleDataForWeek = async (weekId) => {
    if (!db || !selectedUser || !weekId) return;
    
    try {
      const weekDoc = await getDoc(doc(db, 'users', selectedUser.id, 'vehicleWeeks', weekId));
      if (weekDoc.exists()) {
        const weekData = weekDoc.data();
        const statsData = {
          totalScans: weekData.totalScans || 0,
          totalFound: weekData.totalFound || 0,
          totalSecured: weekData.totalSecured || 0,
          recoveryRate: weekData.recoveryRate || 0,
          dateRange: {
            start: weekData.displayRange?.split(' - ')[0] || '',
            end: weekData.displayRange?.split(' - ')[1] || ''
          }
        };
        
        // Update stats in both local and parent state
        setVehicleStats(statsData);
      }
      
      // Initialize carryovers if needed
      await initializeWeekWithCarryOvers(selectedUser.id, weekId);
      
      // Get vehicles for this week
      const vehiclesQuery = query(
        collection(db, 'users', selectedUser.id, 'vehicleWeeks', weekId, 'vehicles'),
        orderBy('date', 'asc')
      );
      
      const vehiclesSnapshot = await getDocs(vehiclesQuery);
      const vehiclesData = vehiclesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      // Sort vehicles: secured carryovers first, then new vehicles, then unsecured carryovers
      const sortedVehicles = vehiclesData.sort((a, b) => {
        if (a.carriedOver && a.status === 'SECURED' && !(b.carriedOver && b.status === 'SECURED')) return -1;
        if (b.carriedOver && b.status === 'SECURED' && !(a.carriedOver && a.status === 'SECURED')) return 1;
        
        if (!a.carriedOver && b.carriedOver && b.status !== 'SECURED') return -1;
        if (!b.carriedOver && a.carriedOver && a.status !== 'SECURED') return 1;
        
        if (a.carriedOver && a.status !== 'SECURED' && !b.carriedOver) return 1;
        if (b.carriedOver && b.status !== 'SECURED' && !a.carriedOver) return -1;
        
        const dateA = new Date(a.securedDate || a.date || '1900-01-01');
        const dateB = new Date(b.securedDate || b.date || '1900-01-01');
        
        if (a.carriedOver && a.status === 'SECURED' && b.carriedOver && b.status === 'SECURED') {
          return dateB - dateA;
        }
        
        return dateA - dateB;
      });
      
      // Update vehicles in both local and parent state
      setVehicles(sortedVehicles);
      
    } catch (error) {
      console.error("Error loading vehicle data for week:", error);
      setVehicles([]);
      setVehicleStats({
        totalScans: 0,
        totalFound: 0,
        totalSecured: 0,
        recoveryRate: 0,
        dateRange: { start: '', end: '' }
      });
    }
  };

  // Update vehicle week stats with corrected logic
  const updateVehicleWeekStats = async (userId, weekId) => {
    try {
      const vehiclesQuery = query(
        collection(db, 'users', userId, 'vehicleWeeks', weekId, 'vehicles')
      );
      
      const vehiclesSnapshot = await getDocs(vehiclesQuery);
      const vehiclesData = vehiclesSnapshot.docs.map(doc => doc.data());
      
      // Corrected logic: Only count non-carryover vehicles as "found"
      const totalFound = vehiclesData.filter(v => 
        !v.carriedOver && (v.status === 'FOUND' || v.status === 'SECURED')
      ).length;
      
      // Total secured includes both new and carryover vehicles that are secured
      const totalSecured = vehiclesData.filter(v => v.status === 'SECURED').length;
      
      // Calculate recovery rate based on total available vehicles (found + carryover)
      const totalCarryover = vehiclesData.filter(v => v.carriedOver).length;
      const totalAvailable = totalFound + totalCarryover;
      const recoveryRate = totalAvailable > 0 ? (totalSecured / totalAvailable) * 100 : 0;
      
      const weekDoc = await getDoc(doc(db, 'users', userId, 'vehicleWeeks', weekId));
      const currentScans = weekDoc.exists() ? (weekDoc.data().totalScans || 0) : 0;
      
      await updateDoc(doc(db, 'users', userId, 'vehicleWeeks', weekId), {
        totalFound,
        totalSecured,
        recoveryRate,
        updatedAt: serverTimestamp()
      });
      
      // Update stats for current week if this is the active week
      if (weekId === selectedWeek) {
        const updatedStats = {
          totalScans: currentScans,
          totalFound,
          totalSecured,
          recoveryRate,
          dateRange: {
            start: (weekDoc.exists() && weekDoc.data().displayRange) 
              ? weekDoc.data().displayRange.split(' - ')[0] 
              : '',
            end: (weekDoc.exists() && weekDoc.data().displayRange) 
              ? weekDoc.data().displayRange.split(' - ')[1]
              : ''
          }
        };
        
        // Update stats in both local and parent state
        setVehicleStats(updatedStats);
      }
      
      // Also recalculate month and YTD stats
      calculateMonthAndYTDStats();
      
    } catch (error) {
      console.error("Error updating vehicle week stats:", error);
    }
  };

  // Handle adding a new vehicle with VIN checking and team sync
  const handleAddVehicle = async () => {
    if (!selectedUser || !selectedWeek) return;
    
    if (newVehicle.vin && newVehicle.vin.trim() !== '') {
      const existingVehicle = vehicles.find(v => 
        v.vin && v.vin.toLowerCase() === newVehicle.vin.toLowerCase()
      );
      
      if (existingVehicle && existingVehicle.carriedOver && existingVehicle.status !== 'SECURED') {
        const shouldUpdate = window.confirm(
          `A carryover vehicle with VIN "${newVehicle.vin}" already exists.\n\n` +
          `Would you like to update it to SECURED status instead of adding a new entry?\n\n` +
          `Click OK to update the existing carryover vehicle, or Cancel to add as a new vehicle.`
        );
        
        if (shouldUpdate) {
          try {
            const updatedVehicleData = {
              ...existingVehicle,
              status: 'SECURED',
              securedDate: newVehicle.securedDate || new Date().toISOString().split('T')[0],
              securedFromCarryover: true,
              securedTimestamp: new Date(),
              updatedAt: serverTimestamp()
            };
            
            await setDoc(doc(db, 'users', selectedUser.id, 'vehicleWeeks', selectedWeek, 'vehicles', existingVehicle.id), updatedVehicleData);
            
            const updatedVehicles = vehicles.map(v => 
              v.id === existingVehicle.id ? {...updatedVehicleData, id: v.id} : v
            );
            
            // Update vehicles in both local and parent state
            setVehicles(updatedVehicles);

            // NEW: Team sync for secured carryover vehicle
            if (selectedTeam && newVehicle.vin) {
              console.log("🔄 Carryover vehicle secured, syncing across team...");
              const userDisplayName = selectedUser.displayName || selectedUser.email?.split('@')[0] || 'Team Member';
              
              await markVINAsSecuredAcrossTeam(
                selectedTeam.id, 
                selectedUser.id, 
                newVehicle.vin, 
                updatedVehicleData.securedDate,
                userDisplayName
              );
            }
            
            setNewVehicle({
              date: new Date().toISOString().split('T')[0],
              vehicle: '',
              vin: '',
              plateNumber: '',
              accountNumber: '',
              financier: '',
              address: '',
              status: 'FOUND',
              securedDate: new Date().toISOString().split('T')[0]
            });
            
            setShowVehicleForm(false);
            await updateVehicleWeekStats(selectedUser.id, selectedWeek);
            alert(`Successfully updated carryover vehicle "${existingVehicle.vehicle}" to SECURED status!`);
            return;
            
          } catch (error) {
            console.error("Error updating carryover vehicle:", error);
            alert("Error updating carryover vehicle. Please try again.");
            return;
          }
        }
      }
    }
    
    try {
      const newVehicleRef = doc(collection(db, 'users', selectedUser.id, 'vehicleWeeks', selectedWeek, 'vehicles'));
      
      await setDoc(newVehicleRef, {
        ...newVehicle,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      
      const newVehicleWithId = {
        id: newVehicleRef.id,
        ...newVehicle
      };
      
      // Update vehicles in both local and parent state
      const updatedVehicles = [newVehicleWithId, ...vehicles];
      setVehicles(updatedVehicles);

      // NEW: Team sync if vehicle is secured and has VIN
      if (newVehicle.status === 'SECURED' && newVehicle.vin && newVehicle.vin.trim() !== '' && selectedTeam) {
        console.log("🔄 New vehicle secured with VIN, syncing across team...");
        const userDisplayName = selectedUser.displayName || selectedUser.email?.split('@')[0] || 'Team Member';
        
        await markVINAsSecuredAcrossTeam(
          selectedTeam.id, 
          selectedUser.id, 
          newVehicle.vin, 
          newVehicle.securedDate,
          userDisplayName
        );
      }
      
      setNewVehicle({
        date: new Date().toISOString().split('T')[0],
        vehicle: '',
        vin: '',
        plateNumber: '',
        accountNumber: '',
        financier: '',
        address: '',
        status: 'FOUND',
        securedDate: new Date().toISOString().split('T')[0]
      });
      
      setShowVehicleForm(false);
      await updateVehicleWeekStats(selectedUser.id, selectedWeek);
      
    } catch (error) {
      console.error("Error adding new vehicle:", error);
    }
  };

  // Other handler functions with team sync
  const handleEditVehicle = (vehicle) => {
    setEditingVehicle(vehicle.id);
    setEditVehicleData({...vehicle});
  };
  
  const handleSaveVehicle = async () => {
    if (!selectedUser || !selectedWeek) return;
    
    try {
      // Get the original vehicle data to compare
      const originalVehicle = vehicles.find(v => v.id === editingVehicle);
      const wasNotSecured = originalVehicle && originalVehicle.status !== 'SECURED';
      const isNowSecured = editVehicleData.status === 'SECURED';
      const hasVIN = editVehicleData.vin && editVehicleData.vin.trim() !== '';
      
      await setDoc(doc(db, 'users', selectedUser.id, 'vehicleWeeks', selectedWeek, 'vehicles', editingVehicle), {
        ...editVehicleData,
        updatedAt: serverTimestamp()
      });
      
      const updatedVehicles = vehicles.map(v => v.id === editingVehicle ? {...editVehicleData, id: v.id} : v);
      
      // Update vehicles in both local and parent state
      setVehicles(updatedVehicles);

      // NEW: If vehicle was just marked as SECURED and has VIN, sync across team
      if (wasNotSecured && isNowSecured && hasVIN && selectedTeam) {
        console.log("🔄 Vehicle just secured with VIN, syncing across team...");
        
        // Get user display name for the sync
        const userDisplayName = selectedUser.displayName || selectedUser.email?.split('@')[0] || 'Team Member';
        
        // Call the team sync function
        await markVINAsSecuredAcrossTeam(
          selectedTeam.id, 
          selectedUser.id, 
          editVehicleData.vin, 
          editVehicleData.securedDate,
          userDisplayName
        );
      }
      
      setEditingVehicle(null);
      setEditVehicleData({});
      
      await updateVehicleWeekStats(selectedUser.id, selectedWeek);
      
    } catch (error) {
      console.error("Error saving vehicle data:", error);
    }
  };
  
  const handleCancelEdit = () => {
    setEditingVehicle(null);
    setEditVehicleData({});
  };

  const handleDeleteVehicle = async (id) => {
    if (!selectedUser || !selectedWeek) return;
    
    if (window.confirm("Are you sure you want to delete this vehicle?")) {
      try {
        await deleteDoc(doc(db, 'users', selectedUser.id, 'vehicleWeeks', selectedWeek, 'vehicles', id));
        
        const updatedVehicles = vehicles.filter(v => v.id !== id);
        
        // Update vehicles in both local and parent state
        setVehicles(updatedVehicles);
        
        await updateVehicleWeekStats(selectedUser.id, selectedWeek);
        
      } catch (error) {
        console.error("Error deleting vehicle:", error);
      }
    }
  };

  const handleEditInputChange = (e) => {
    const { name, value } = e.target;
    setEditVehicleData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleNewVehicleChange = (e) => {
    const { name, value } = e.target;
    setNewVehicle(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleWeekChange = (e) => {
    const newWeekId = e.target.value;
    
    // Set selected week in parent and load data for this week
    setSelectedWeek(newWeekId);
    loadVehicleDataForWeek(newWeekId);
  };

  const handleEditScans = () => {
    setEditingScans(true);
    setEditScanAmount(vehicleStats.totalScans);
  };
  
  const handleSaveScans = async () => {
    if (!selectedUser || !selectedWeek) return;
    
    try {
      const scanAmount = parseInt(editScanAmount);
      
      await updateDoc(doc(db, 'users', selectedUser.id, 'vehicleWeeks', selectedWeek), {
        totalScans: scanAmount,
        updatedAt: serverTimestamp()
      });
      
      // Update stats in both local and parent state
      const updatedStats = {
        ...vehicleStats,
        totalScans: scanAmount
      };
      
      setVehicleStats(updatedStats);
      
      // Recalculate month and YTD stats
      calculateMonthAndYTDStats();
      
      setEditingScans(false);
    } catch (error) {
      console.error("Error saving scan amount:", error);
    }
  };
  
  const handleCancelEditScans = () => {
    setEditingScans(false);
  };
  
  const handleScanInputChange = (e) => {
    setEditScanAmount(e.target.value);
  };

  // Get weekly stats for display
  const weeklyStats = calculateWeeklyStats();

  // Get proper vehicle count for display (just secured vehicles)
  const getProperVehicleCount = () => {
    return vehicles ? vehicles.filter(v => v.status === 'SECURED').length : 0;
  };

  // NEW: Function to render vehicle row with team sync indicators
  const renderVehicleRow = (vehicle) => {
    const isTeamSecured = vehicle.autoSecuredFromTeam && vehicle.securedByTeammate;
    
    if (editingVehicle === vehicle.id) {
      // Editing row
      return (
        <>
          <td className="px-2 py-1">
            <input
              type="text"
              name="date"
              value={editVehicleData.date || ''}
              onChange={handleEditInputChange}
              className="w-full bg-gray-800 border border-blue-700 rounded-md p-1 text-white text-xs"
            />
          </td>
          <td className="px-2 py-1">
            <input
              type="text"
              name="vehicle"
              value={editVehicleData.vehicle || ''}
              onChange={handleEditInputChange}
              className="w-full bg-gray-800 border border-blue-700 rounded-md p-1 text-white text-xs"
            />
          </td>
          <td className="px-2 py-1">
            <input
              type="text"
              name="vin"
              value={editVehicleData.vin || ''}
              onChange={handleEditInputChange}
              maxLength={6}
              className="w-full bg-gray-800 border border-blue-700 rounded-md p-1 text-white text-xs"
            />
          </td>
          <td className="px-2 py-1">
            <input
              type="text"
              name="plateNumber"
              value={editVehicleData.plateNumber || ''}
              onChange={handleEditInputChange}
              className="w-full bg-gray-800 border border-blue-700 rounded-md p-1 text-white text-xs"
            />
          </td>
          <td className="px-2 py-1">
            <input
              type="text"
              name="accountNumber"
              value={editVehicleData.accountNumber || ''}
              onChange={handleEditInputChange}
              className="w-full bg-gray-800 border border-blue-700 rounded-md p-1 text-white text-xs"
            />
          </td>
          <td className="px-2 py-1">
            <input
              type="text"
              name="financier"
              value={editVehicleData.financier || ''}
              onChange={handleEditInputChange}
              className="w-full bg-gray-800 border border-blue-700 rounded-md p-1 text-white text-xs"
            />
          </td>
          <td className="px-2 py-1">
            <input
              type="text"
              name="address"
              value={editVehicleData.address || ''}
              onChange={handleEditInputChange}
              className="w-full bg-gray-800 border border-blue-700 rounded-md p-1 text-white text-xs"
            />
          </td>
          <td className="px-2 py-1">
            <select
              name="status"
              value={editVehicleData.status || 'FOUND'}
              onChange={handleEditInputChange}
              className="w-full bg-gray-800 border border-blue-700 rounded-md p-1 text-white text-xs"
            >
              <option value="FOUND">FOUND</option>
              <option value="SECURED">SECURED</option>
              <option value="NOT FOUND">NOT FOUND</option>
            </select>
          </td>
          <td className="px-2 py-1">
            <span className={`px-1.5 py-0.5 inline-flex text-xs font-semibold rounded-full 
              ${vehicle.carriedOver ? 'bg-orange-900 text-orange-300' : 'bg-gray-900 text-gray-300'}`}
            >
              {vehicle.carriedOver ? 'YES' : 'NO'}
            </span>
          </td>
          <td className="px-2 py-1">
            <input
              type="text"
              name="securedDate"
              value={editVehicleData.securedDate || ''}
              onChange={handleEditInputChange}
              className="w-full bg-gray-800 border border-blue-700 rounded-md p-1 text-white text-xs"
            />
          </td>
          <td className="px-2 py-1 text-right space-x-1">
            <button
              onClick={handleSaveVehicle}
              className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 text-xs rounded-md shadow-md"
            >
              Save
            </button>
            <button
              onClick={handleCancelEdit}
              className="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 text-xs rounded-md shadow-md"
            >
              Cancel
            </button>
          </td>
        </>
      );
    } else {
      // Display row with team sync indicators
      return (
        <>
          <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-300">
            {vehicle.date}
          </td>
          <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-300">
            {vehicle.vehicle}
          </td>
          <td className="px-2 py-2 whitespace-nowrap text-xs font-medium text-red-400">
            {vehicle.vin}
          </td>
          <td className="px-2 py-2 whitespace-nowrap text-xs font-medium text-orange-400">
            {vehicle.plateNumber}
          </td>
          <td className="px-2 py-2 whitespace-nowrap text-xs font-medium text-blue-400">
            {vehicle.accountNumber}
          </td>
          <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-300">
            {vehicle.financier}
          </td>
          <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-300">
            {vehicle.address}
          </td>
          <td className="px-2 py-2 whitespace-nowrap text-xs">
            <div className="flex items-center">
              <span className={`px-1.5 py-0.5 inline-flex text-xs font-semibold rounded-full 
                ${vehicle.status === 'FOUND' ? 'bg-green-900 text-green-300' : 
                  vehicle.status === 'SECURED' ? 'bg-blue-900 text-blue-300' : 
                  'bg-red-900 text-red-300'}`}
              >
                {vehicle.status}
              </span>
              {isTeamSecured && (
                <span 
                  className="ml-1 text-purple-400 text-xs" 
                  title={`Secured by teammate: ${vehicle.securedByUserName}`}
                >
                  👥
                </span>
              )}
            </div>
            {isTeamSecured && (
              <div className="text-xs text-purple-400 mt-1">
                By: {vehicle.securedByUserName}
              </div>
            )}
          </td>
          <td className="px-2 py-2 whitespace-nowrap text-xs">
            <span className={`px-1.5 py-0.5 inline-flex text-xs font-semibold rounded-full 
              ${vehicle.carriedOver ? 'bg-orange-900 text-orange-300' : 'bg-gray-900 text-gray-300'}`}
            >
              {vehicle.carriedOver ? 'YES' : 'NO'}
            </span>
          </td>
          <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-300">
            {vehicle.securedDate}
          </td>
          <td className="px-2 py-2 whitespace-nowrap text-xs text-right space-x-1">
            <button
              onClick={() => handleEditVehicle(vehicle)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-0.5 text-xs rounded-md shadow-md"
            >
              Edit
            </button>
            <button
              onClick={() => handleDeleteVehicle(vehicle.id)}
              className="bg-red-600 hover:bg-red-700 text-white px-2 py-0.5 text-xs rounded-md shadow-md"
            >
              Delete
            </button>
            {vehicle.carriedOver && vehicle.status === 'FOUND' && (
              <button
                onClick={() => checkAndSecureCarriedOverVehicle(vehicle.id, vehicle.vin)}
                className="bg-green-600 hover:bg-green-700 text-white px-2 py-0.5 text-xs rounded-md shadow-md"
              >
                Secure
              </button>
            )}
          </td>
        </>
      );
    }
  };

  return (
    <>
      <div className="bg-gray-700 p-2 rounded border border-gray-600 mt-6">
        {/* NEW: Team Sync Notifications */}
        {teamSyncNotifications.length > 0 && (
          <div className="mb-4">
            <h4 className="text-xs font-semibold text-purple-300 mb-2">Team Sync Notifications</h4>
            <div className="space-y-1">
              {teamSyncNotifications.map(notification => (
                <div 
                  key={notification.id} 
                  className="bg-purple-900 bg-opacity-50 border border-purple-600 rounded p-2 text-xs text-purple-200"
                >
                  <div className="flex items-center">
                    <span className="text-purple-400 mr-2">🎉</span>
                    <span>{notification.message}</span>
                    <button 
                      onClick={() => setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id))}
                      className="ml-auto text-purple-400 hover:text-white"
                    >
                      ×
                    </button>
                  </div>
                  <div className="text-xs text-purple-400 mt-1">
                    {notification.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <div>
            <h3 className="text-sm font-semibold text-blue-300">Vehicle Tracking Data</h3>
            
            <div className="flex flex-wrap items-center text-gray-400 text-xs mt-1">
              <span>{getUserDisplayName()} • {vehicleStats.dateRange.start || ''} THRU {vehicleStats.dateRange.end || ''} • VEHICLES: {getProperVehicleCount()} • </span>
              
              {editingScans ? (
                <div className="flex items-center ml-1 mt-1 md:mt-0">
                  <span>WEEKLY SCANS: </span>
                  <input
                    type="number"
                    value={editScanAmount}
                    onChange={handleScanInputChange}
                    className="mx-2 w-20 bg-gray-800 border border-blue-700 rounded-md p-1 text-white text-xs"
                    min="0"
                  />
                  <button
                    onClick={handleSaveScans}
                    className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 text-xs rounded-md shadow-md ml-1"
                  >
                    Save
                  </button>
                  <button
                    onClick={handleCancelEditScans}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 text-xs rounded-md shadow-md ml-1"
                  >
                    Cancel
                  </button>
                </div>
              ) : (
                <div className="flex flex-wrap items-center ml-1">
                  <span className="text-green-400 font-semibold">WEEK:</span>
                  <span className="ml-1">SCANS: {vehicleStats.totalScans.toLocaleString()} • FOUND: {weeklyStats.weeklyFound} • SECURED: {weeklyStats.weeklySecured} • RATE: {formatNumber(weeklyStats.weeklyRecoveryRate)}%</span>
                  <span className="mx-2">|</span>
                  <span className="text-orange-400 font-semibold">MONTH:</span>
                  <span className="ml-1">SCANS: {(ytdStats.month?.totalScans || 0).toLocaleString()} • FOUND: {ytdStats.month?.totalFound || 0} • SECURED: {ytdStats.month?.totalSecured || 0} • RATE: {formatNumber(ytdStats.month?.recoveryRate || 0)}%</span>
                  <span className="mx-2">|</span>
                  <span className="text-blue-400 font-semibold">YEAR:</span>
                  <span className="ml-1">SCANS: {ytdStats.totalScans.toLocaleString()} • FOUND: {ytdStats.totalFound} • SECURED: {ytdStats.totalSecured} • RATE: {formatNumber(ytdStats.recoveryRate)}%</span>
                  <span className="mx-2">|</span>
                  <span className="text-purple-400 font-semibold">CARRYOVER: {weeklyStats.totalCarryover}</span>
                  <button
                    onClick={handleEditScans}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 text-xs rounded-md shadow-md ml-2"
                    title="Edit Weekly Scan Count"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </button>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mt-2 md:mt-0">
            <div className="flex items-center">
              <select
                value={selectedWeek || ''}
                onChange={handleWeekChange}
                className="bg-gray-800 border border-gray-700 rounded-md p-1 text-white text-xs"
              >
                {availableWeeks.length === 0 ? (
                  <option value="">No weeks available</option>
                ) : (
                  availableWeeks.map(week => (
                    <option key={week.id} value={week.id}>
                      {week.displayRange || 'Week of ' + new Date(week.startDate).toLocaleDateString()}
                      {week.startDate <= new Date() && week.endDate >= new Date() ? ' (Current)' : ''}
                    </option>
                  ))
                )}
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowVehicleForm(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md shadow-md flex items-center justify-center text-xs"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add Vehicle
              </button>
              
              {/* PDF Export button */}
              <button
                onClick={exportToPDF}
                disabled={vehicles.length === 0}
                className={`${
                  vehicles.length === 0 
                    ? 'bg-gray-500 cursor-not-allowed' 
                    : 'bg-red-600 hover:bg-red-700'
                } text-white px-3 py-1 rounded-md shadow-md flex items-center justify-center text-xs`}
                title="Export to PDF"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export PDF
              </button>
              
              {/* Print Report button */}
              <button
                onClick={handlePrint}
                disabled={vehicles.length === 0}
                className={`${
                  vehicles.length === 0 
                    ? 'bg-gray-500 cursor-not-allowed' 
                    : 'bg-purple-600 hover:bg-purple-700'
                } text-white px-3 py-1 rounded-md shadow-md flex items-center justify-center text-xs`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                </svg>
                Print Report
              </button>
            </div>
          </div>
        </div>

        {/* Add Vehicle Form */}
        {showVehicleForm && (
          <div className="bg-gray-800 p-3 rounded-lg mb-4 border border-blue-800">
            <h4 className="text-xs font-semibold text-blue-300 mb-3">Add New Vehicle</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-3">
              <div>
                <label className="block text-gray-400 text-xs mb-1">Date</label>
                <input
                  type="text"
                  name="date"
                  value={newVehicle.date}
                  onChange={handleNewVehicleChange}
                  className="w-full bg-gray-900 border border-gray-700 rounded-md p-1 text-white text-xs"
                />
              </div>
              <div>
                <label className="block text-gray-400 text-xs mb-1">Vehicle</label>
                <input
                  type="text"
                  name="vehicle"
                  value={newVehicle.vehicle}
                  onChange={handleNewVehicleChange}
                  className="w-full bg-gray-900 border border-gray-700 rounded-md p-1 text-white text-xs"
                />
              </div>
              <div>
                <label className="block text-gray-400 text-xs mb-1">VIN</label>
                <input
                  type="text"
                  name="vin"
                  value={newVehicle.vin}
                  onChange={handleNewVehicleChange}
                  maxLength={6}
                  className="w-full bg-gray-900 border border-gray-700 rounded-md p-1 text-white text-xs"
                  placeholder="6 digits max"
                />
              </div>
              <div>
                <label className="block text-gray-400 text-xs mb-1">Plate Number</label>
                <input
                  type="text"
                  name="plateNumber"
                  value={newVehicle.plateNumber}
                  onChange={handleNewVehicleChange}
                  className="w-full bg-gray-900 border border-gray-700 rounded-md p-1 text-white text-xs"
                />
              </div>
              <div>
                <label className="block text-gray-400 text-xs mb-1">Account Number</label>
                <input
                  type="text"
                  name="accountNumber"
                  value={newVehicle.accountNumber}
                  onChange={handleNewVehicleChange}
                  className="w-full bg-gray-900 border border-gray-700 rounded-md p-1 text-white text-xs"
                />
              </div>
              <div>
                <label className="block text-gray-400 text-xs mb-1">Financier</label>
                <input
                  type="text"
                  name="financier"
                  value={newVehicle.financier}
                  onChange={handleNewVehicleChange}
                  className="w-full bg-gray-900 border border-gray-700 rounded-md p-1 text-white text-xs"
                />
              </div>
              <div>
                <label className="block text-gray-400 text-xs mb-1">Address</label>
                <input
                  type="text"
                  name="address"
                  value={newVehicle.address}
                  onChange={handleNewVehicleChange}
                  className="w-full bg-gray-900 border border-gray-700 rounded-md p-1 text-white text-xs"
                />
              </div>
              <div>
                <label className="block text-gray-400 text-xs mb-1">Status</label>
                <select
                  name="status"
                  value={newVehicle.status}
                  onChange={handleNewVehicleChange}
                  className="w-full bg-gray-900 border border-gray-700 rounded-md p-1 text-white text-xs"
                >
                  <option value="FOUND">FOUND</option>
                  <option value="SECURED">SECURED</option>
                  <option value="NOT FOUND">NOT FOUND</option>
                </select>
              </div>
              <div>
                <label className="block text-gray-400 text-xs mb-1">Secured Date</label>
                <input
                  type="text"
                  name="securedDate"
                  value={newVehicle.securedDate}
                  onChange={handleNewVehicleChange}
                  className="w-full bg-gray-900 border border-gray-700 rounded-md p-1 text-white text-xs"
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowVehicleForm(false)}
                className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-md shadow-md text-xs"
              >
                Cancel
              </button>
              <button
                onClick={handleAddVehicle}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md shadow-md text-xs"
              >
                Save Vehicle
              </button>
            </div>
          </div>
        )}
        
        {/* Vehicle Table with Team Sync Features */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-700 rounded-lg text-xs">
            <thead className="bg-gray-800">
              <tr>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Vehicle
                </th>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  VIN
                </th>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Plate #
                </th>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Account #
                </th>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Financier
                </th>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Address
                </th>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  C/O
                </th>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Secured Date
                </th>
                <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-gray-900 divide-y divide-gray-800">
              {selectedWeek === null ? (
                <tr>
                  <td colSpan="11" className="px-2 py-3 text-center text-gray-400">
                    No week selected
                  </td>
                </tr>
              ) : vehicles.length === 0 ? (
                <tr>
                  <td colSpan="11" className="px-2 py-3 text-center text-gray-400">
                    No vehicles found for this week. Click "Add Vehicle" to add one.
                  </td>
                </tr>
              ) : (
                vehicles.map((vehicle) => {
                  const isTeamSecured = vehicle.autoSecuredFromTeam && vehicle.securedByTeammate;
                  
                  return (
                    <tr 
                      key={vehicle.id} 
                      className={`hover:bg-gray-800 transition-colors duration-150 ${
                        isTeamSecured ? 'bg-purple-900 bg-opacity-20' : ''
                      }`}
                    >
                      {renderVehicleRow(vehicle)}
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Team Sync Legend */}
        <div className="mt-4 p-2 bg-gray-800 rounded border border-gray-600">
          <h4 className="text-xs font-semibold text-gray-300 mb-2">Legend</h4>
          <div className="flex flex-wrap gap-4 text-xs">
            <div className="flex items-center">
              <span className="inline-block w-3 h-3 bg-blue-600 rounded mr-2"></span>
              <span className="text-gray-400">Self Secured</span>
            </div>
            <div className="flex items-center">
              <span className="inline-block w-3 h-3 bg-purple-600 rounded mr-2"></span>
              <span className="text-gray-400">Team Secured</span>
            </div>
            <div className="flex items-center">
              <span className="text-purple-400 mr-1">👥</span>
              <span className="text-gray-400">Team Sync Indicator</span>
            </div>
            <div className="flex items-center">
              <span className="inline-block w-3 h-3 bg-orange-600 rounded mr-2"></span>
              <span className="text-gray-400">Carryover Vehicle</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Print View (visible only when printing) */}
      {showPrintView && <PrintView />}
      
      {/* Print-specific styles - only applied when printing */}
      <style type="text/css" media="print">
        {`
          @page { size: landscape; }
          
          /* Hide regular UI during print */
          body > *:not(.print-container) {
            display: none !important;
          }
          
          /* Show print container */
          .print-container {
            display: block !important;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
          }
        `}
      </style>
    </>
  );
}

export default VehicleTracker;