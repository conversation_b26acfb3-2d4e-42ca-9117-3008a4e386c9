// VehicleTrackerStats.js - Statistics Module - UPDATED WITH ALL VEHICLES AS FOUND

import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { formatNumber } from './VehicleTrackerUtils';

// Calculate weekly statistics from vehicles array
export const calculateWeeklyStats = (vehicles) => {
  if (!vehicles || vehicles.length === 0) {
    return {
      weeklyFound: 0,
      weeklySecured: 0,
      totalCarryover: 0,
      weeklyRecoveryRate: 0
    };
  }

  // UPDATED: Count ALL vehicles that aren't carryovers as "found"
  const weeklyFound = vehicles.filter(v => !v.carriedOver).length;

  const weeklySecured = vehicles.filter(v => v.status === 'SECURED').length;
  const totalCarryover = vehicles.filter(v => v.carriedOver).length;
  
  // Recovery rate is based on all vehicles (not just new ones)
  const totalVehicles = vehicles.length;
  const weeklyRecoveryRate = totalVehicles > 0 ? (weeklySecured / totalVehicles) * 100 : 0;

  return {
    weeklyFound,
    weeklySecured,
    totalCarryover,
    weeklyRecoveryRate
  };
};

// Calculate Month and Year-to-Date statistics
export const calculateMonthAndYTDStats = async (db, user, vehicles, selectedWeek, vehicleStats) => {
  if (!user || !db) {
    return {
      totalFound: 0,
      totalSecured: 0,
      totalScans: 0,
      recoveryRate: 0,
      month: {
        totalFound: 0,
        totalSecured: 0,
        totalScans: 0,
        recoveryRate: 0
      }
    };
  }

  try {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    const yearStart = new Date(currentYear, 0, 1);
    const yearEnd = new Date(currentYear, 11, 31, 23, 59, 59);
    const monthStart = new Date(currentYear, currentMonth, 1);
    const monthEnd = new Date(currentYear, currentMonth + 1, 0, 23, 59, 59);

    const weeksQuery = query(
      collection(db, 'users', user.id, 'vehicleWeeks'),
      orderBy('startDate', 'desc')
    );

    const weeksSnapshot = await getDocs(weeksQuery);
    let ytdTotalFound = 0;
    let ytdTotalSecured = 0;
    let ytdTotalScans = 0;
    let monthTotalFound = 0;
    let monthTotalSecured = 0;
    let monthTotalScans = 0;

    const currentWeeklyStats = calculateWeeklyStats(vehicles);
    let currentWeekIncluded = false;

    for (const weekDoc of weeksSnapshot.docs) {
      const weekData = weekDoc.data();
      const weekStartDate = weekData.startDate?.toDate();
      const weekEndDate = weekData.endDate?.toDate();

      if (weekStartDate) {
        // Year-to-date calculations
        if (weekStartDate >= yearStart && weekStartDate <= yearEnd) {
          if (selectedWeek && weekDoc.id === selectedWeek) {
            // For current week, use the actual vehicle count (all non-carryover vehicles)
            ytdTotalFound += currentWeeklyStats.weeklyFound;
            ytdTotalSecured += currentWeeklyStats.weeklySecured;
            ytdTotalScans += (vehicleStats.totalScans || 0);
            currentWeekIncluded = true;
          } else {
            // For historical weeks, we need to count ALL vehicles that were added
            const vehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', weekDoc.id, 'vehicles');
            const vehiclesSnapshot = await getDocs(vehiclesRef);
            
            // Count all non-carryover vehicles as "found"
            const weeklyVehicles = vehiclesSnapshot.docs.map(doc => doc.data());
            const weeklyFoundCount = weeklyVehicles.filter(v => !v.carriedOver).length;
            const weeklySecuredCount = weeklyVehicles.filter(v => v.status === 'SECURED').length;
            
            ytdTotalFound += weeklyFoundCount;
            ytdTotalSecured += weeklySecuredCount;
            ytdTotalScans += (weekData.totalScans || 0);
          }
        }

        // Monthly calculations
        if ((weekStartDate <= monthEnd && weekEndDate >= monthStart) ||
          (weekStartDate >= monthStart && weekStartDate <= monthEnd)) {

          if (selectedWeek && weekDoc.id === selectedWeek) {
            // For current week, use the actual vehicle count
            monthTotalFound += currentWeeklyStats.weeklyFound;
            monthTotalSecured += currentWeeklyStats.weeklySecured;
            monthTotalScans += (vehicleStats.totalScans || 0);
          } else {
            // For historical weeks in this month
            const vehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', weekDoc.id, 'vehicles');
            const vehiclesSnapshot = await getDocs(vehiclesRef);
            
            // Count all non-carryover vehicles as "found"
            const weeklyVehicles = vehiclesSnapshot.docs.map(doc => doc.data());
            const weeklyFoundCount = weeklyVehicles.filter(v => !v.carriedOver).length;
            const weeklySecuredCount = weeklyVehicles.filter(v => v.status === 'SECURED').length;
            
            monthTotalFound += weeklyFoundCount;
            monthTotalSecured += weeklySecuredCount;
            monthTotalScans += (weekData.totalScans || 0);
          }
        }
      }
    }

    // Include current week if not already included
    if (!currentWeekIncluded && vehicles && vehicles.length > 0) {
      monthTotalFound += currentWeeklyStats.weeklyFound;
      monthTotalSecured += currentWeeklyStats.weeklySecured;
      monthTotalScans += (vehicleStats.totalScans || 0);

      ytdTotalFound += currentWeeklyStats.weeklyFound;
      ytdTotalSecured += currentWeeklyStats.weeklySecured;
      ytdTotalScans += (vehicleStats.totalScans || 0);
    }

    const ytdRecoveryRate = ytdTotalFound > 0 ? (ytdTotalSecured / ytdTotalFound) * 100 : 0;
    const monthRecoveryRate = monthTotalFound > 0 ? (monthTotalSecured / monthTotalFound) * 100 : 0;

    return {
      totalFound: ytdTotalFound,
      totalSecured: ytdTotalSecured,
      totalScans: ytdTotalScans,
      recoveryRate: ytdRecoveryRate,
      month: {
        totalFound: monthTotalFound,
        totalSecured: monthTotalSecured,
        totalScans: monthTotalScans,
        recoveryRate: monthRecoveryRate
      }
    };

  } catch (error) {
    console.error("Error calculating Month and YTD stats:", error);
    return {
      totalFound: 0,
      totalSecured: 0,
      totalScans: 0,
      recoveryRate: 0,
      month: {
        totalFound: 0,
        totalSecured: 0,
        totalScans: 0,
        recoveryRate: 0
      }
    };
  }
};

// Stats display components
export const StatsCards = ({ vehicleStats, weeklyStats, editingScans, editScanAmount, handleEditScans, handleSaveScans, handleCancelEditScans, handleScanInputChange }) => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
      <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl p-4 text-center shadow-xl backdrop-blur-lg border border-blue-500">
        <div className="text-3xl font-bold text-white">{vehicleStats.totalScans.toLocaleString()}</div>
        <div className="text-sm text-blue-100">Weekly Scans</div>
        {editingScans ? (
          <div className="mt-2">
            <input
              type="number"
              value={editScanAmount}
              onChange={handleScanInputChange}
              className="w-20 bg-blue-900 border border-blue-400 rounded px-2 py-1 text-xs text-white"
              min="0"
            />
            <div className="mt-1 space-x-1">
              <button
                onClick={handleSaveScans}
                className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 text-xs rounded"
              >
                ✓
              </button>
              <button
                onClick={handleCancelEditScans}
                className="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 text-xs rounded"
              >
                ✕
              </button>
            </div>
          </div>
        ) : (
          <button
            onClick={handleEditScans}
            className="mt-2 text-xs text-blue-200 hover:text-white"
            title="Edit scan count"
          >
            ✏️ Edit
          </button>
        )}
      </div>
      <div className="bg-gradient-to-br from-yellow-600 to-yellow-800 rounded-xl p-4 text-center shadow-xl backdrop-blur-lg border border-yellow-500">
        <div className="text-3xl font-bold text-white">{weeklyStats.weeklyFound}</div>
        <div className="text-sm text-yellow-100">Weekly Uploaded</div>
        <div className="text-xs text-yellow-200 mt-1">All new vehicles</div>
      </div>
      <div className="bg-gradient-to-br from-green-600 to-green-800 rounded-xl p-4 text-center shadow-xl backdrop-blur-lg border border-green-500">
        <div className="text-3xl font-bold text-white">{weeklyStats.weeklySecured}</div>
        <div className="text-sm text-green-100">Weekly Secured</div>
      </div>
      <div className="bg-gradient-to-br from-purple-600 to-purple-800 rounded-xl p-4 text-center shadow-xl backdrop-blur-lg border border-purple-500">
        <div className="text-3xl font-bold text-white">{formatNumber(weeklyStats.weeklyRecoveryRate)}%</div>
        <div className="text-sm text-purple-100">Recovery Rate</div>
      </div>
    </div>
  );
};

export const MonthlyAndYTDStats = ({ ytdStats }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <div className="bg-gradient-to-r from-orange-600 to-orange-700 rounded-xl p-4 shadow-xl backdrop-blur-lg border border-orange-500">
        <h3 className="text-lg font-bold text-white mb-3 flex items-center">
          <span className="mr-2">📊</span>
          Monthly Stats
        </h3>
        <div className="grid grid-cols-4 gap-2 text-center text-sm">
          <div>
            <div className="text-2xl font-bold">{(ytdStats.month?.totalScans || 0).toLocaleString()}</div>
            <div className="text-orange-100">Scans</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{ytdStats.month?.totalFound || 0}</div>
            <div className="text-orange-100">Uploaded</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{ytdStats.month?.totalSecured || 0}</div>
            <div className="text-orange-100">Secured</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{formatNumber(ytdStats.month?.recoveryRate || 0)}%</div>
            <div className="text-orange-100">Rate</div>
          </div>
        </div>
      </div>
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-4 shadow-xl backdrop-blur-lg border border-blue-500">
        <h3 className="text-lg font-bold text-white mb-3 flex items-center">
          <span className="mr-2">📈</span>
          Year-to-Date Stats
        </h3>
        <div className="grid grid-cols-4 gap-2 text-center text-sm">
          <div>
            <div className="text-2xl font-bold">{ytdStats.totalScans.toLocaleString()}</div>
            <div className="text-blue-100">Scans</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{ytdStats.totalFound}</div>
            <div className="text-blue-100">Uploaded</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{ytdStats.totalSecured}</div>
            <div className="text-blue-100">Secured</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{formatNumber(ytdStats.recoveryRate)}%</div>
            <div className="text-blue-100">Rate</div>
          </div>
        </div>
      </div>
    </div>
  );
};