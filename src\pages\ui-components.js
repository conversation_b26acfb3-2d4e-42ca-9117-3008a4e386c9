import React from 'react';
import { STATUS_OPTIONS, COLORS_MAP, formatDate, formatAddress } from './utility-functions';

// Status Badge Component
export const StatusBadge = ({ status }) => {
  const statusStyle = STATUS_OPTIONS[status] || STATUS_OPTIONS['open'];
  
  return (
    <span className={`px-3 py-1 rounded-full text-xs font-medium ${statusStyle.bgColor} ${statusStyle.textColor}`}>
      {statusStyle.label}
    </span>
  );
};

// Confirmation Dialog Component
export const ConfirmationDialog = ({ 
  isOpen, 
  title, 
  message, 
  onConfirm, 
  onCancel, 
  confirmText = "Delete", 
  confirmButtonClass = "bg-red-600 hover:bg-red-700" 
}) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 animate-fadeIn">
      <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg w-full max-w-md mx-4 p-4">
        <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
        <p className="mb-4 text-gray-300">{message}</p>
        <div className="flex justify-end space-x-2">
          <button 
            onClick={onCancel}
            className="px-4 py-2 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors duration-200"
          >
            Cancel
          </button>
          <button 
            onClick={onConfirm}
            className={`px-4 py-2 text-sm ${confirmButtonClass} text-white rounded transition-colors duration-200`}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

// Vehicle Render Component
export const VehicleRender = ({ 
  vehicleImageUrl, 
  renderedViews, 
  make, 
  model, 
  year, 
  color,
  selectedViewIndex = 0,
  onSwitchView,
  isLoading = false,
  getFallbackImageUrl
}) => {
  return (
    <div className="rounded overflow-hidden border border-gray-700 bg-gray-800">
      {isLoading ? (
        <div className="flex items-center justify-center h-48">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : vehicleImageUrl ? (
        <div className="relative">
          <img 
            src={vehicleImageUrl}
            alt={`${make} ${model}`} 
            className="w-full h-auto" 
            onError={(e) => {
              e.target.onerror = null;
              if (getFallbackImageUrl) {
                const fallbackUrl = getFallbackImageUrl({ make, model, year, color });
                if (fallbackUrl) {
                  e.target.src = fallbackUrl;
                  return;
                }
              }
              e.target.src = `data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="400" height="225" viewBox="0 0 400 225"><rect width="400" height="225" fill="#1A2642" /><text x="200" y="112.5" font-family="Arial" font-size="20" fill="white" text-anchor="middle">${year} ${make} ${model}</text></svg>`;
            }}
          />
          
          {/* View angle selector */}
          {renderedViews?.length > 1 && (
            <div className="absolute bottom-2 left-0 right-0 flex justify-center space-x-1">
              {renderedViews.map((view, index) => (
                <button
                  key={index}
                  onClick={() => onSwitchView && onSwitchView(index)}
                  className={`w-6 h-6 rounded-full flex items-center justify-center transition-colors ${
                    selectedViewIndex === index 
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-800 bg-opacity-60 text-gray-300 hover:bg-gray-700'
                  }`}
                  title={`View angle ${view.angle}`}
                  type="button"
                >
                  {index + 1}
                </button>
              ))}
            </div>
          )}
        </div>
      ) : (
        <div className="flex items-center justify-center h-48 text-gray-500 text-sm">
          {make && model ? "No image available" : "Enter make, model, and year to see vehicle preview"}
        </div>
      )}
    </div>
  );
};

// Order Stats Component
export const OrderStats = ({ orders }) => {
  // Calculate GPS stats
  const gpsStats = {
    total: orders.length,
    withGPS: orders.filter(order => {
      return order.addresses && order.addresses.some(addr => addr.position?.lat && addr.position?.lng);
    }).length,
    withoutGPS: orders.filter(order => {
      return !order.addresses || !order.addresses.some(addr => addr.position?.lat && addr.position?.lng);
    }).length,
    addressesWithGPS: orders.reduce((count, order) => {
      const addresses = order.addresses || [];
      return count + addresses.filter(addr => addr.position?.lat && addr.position?.lng).length;
    }, 0),
    totalAddresses: orders.reduce((count, order) => {
      const addresses = order.addresses || [];
      return count + addresses.length;
    }, 0),
    with3DRender: orders.filter(order => {
      return order.vehicleRenderViews && order.vehicleRenderViews.length > 0;
    }).length
  };
  
  return (
    <>
      {/* Stats row */}
      <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-7 gap-2 md:gap-4 mt-4">
        <div className="p-3 bg-gradient-to-r from-blue-900/40 to-blue-800/30 rounded-lg border border-blue-700/30">
          <div className="text-xs text-blue-300">Total Orders</div>
          <div className="text-xl md:text-2xl font-bold text-white">{orders.length}</div>
        </div>
        <div className="p-3 bg-gradient-to-r from-green-900/40 to-green-800/30 rounded-lg border border-green-700/30">
          <div className="text-xs text-green-300">Secure</div>
          <div className="text-xl md:text-2xl font-bold text-white">{orders.filter(o => o.secure && o.status === 'secure').length}</div>
        </div>
        <div className="p-3 bg-gradient-to-r from-yellow-900/40 to-yellow-800/30 rounded-lg border border-yellow-700/30">
          <div className="text-xs text-yellow-300">Pending Pickup</div>
          <div className="text-xl md:text-2xl font-bold text-white">{orders.filter(o => o.status === 'pending-pickup').length}</div>
        </div>
        <div className="p-3 bg-gradient-to-r from-blue-900/40 to-blue-800/30 rounded-lg border border-blue-700/30">
          <div className="text-xs text-blue-300">Open</div>
          <div className="text-xl md:text-2xl font-bold text-white">{orders.filter(o => o.status === 'open').length}</div>
        </div>
        <div className="p-3 bg-gradient-to-r from-purple-900/40 to-purple-800/30 rounded-lg border border-purple-700/30">
          <div className="text-xs text-purple-300">On-Hold</div>
          <div className="text-xl md:text-2xl font-bold text-white">{orders.filter(o => o.status === 'on-hold').length}</div>
        </div>
        <div className="p-3 bg-gradient-to-r from-pink-900/40 to-pink-800/30 rounded-lg border border-pink-700/30">
          <div className="text-xs text-pink-300">Claim</div>
          <div className="text-xl md:text-2xl font-bold text-white">{orders.filter(o => o.status === 'claim').length}</div>
        </div>
        <div className="p-3 bg-gradient-to-r from-red-900/40 to-red-800/30 rounded-lg border border-red-700/30">
          <div className="text-xs text-red-300">Restricted</div>
          <div className="text-xl md:text-2xl font-bold text-white">{orders.filter(o => o.status === 'restricted').length}</div>
        </div>
      </div>
      
      {/* GPS & 3D Render Stats row */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2 md:gap-4 mt-2">
        <div className="p-3 bg-gradient-to-r from-teal-900/40 to-teal-800/30 rounded-lg border border-teal-700/30">
          <div className="text-xs text-teal-300">Orders with GPS</div>
          <div className="text-xl font-bold text-white">{gpsStats.withGPS} / {gpsStats.total}</div>
          <div className="text-xs text-teal-200 mt-1">
            {gpsStats.total > 0 ? Math.round((gpsStats.withGPS / gpsStats.total) * 100) : 0}% of orders
          </div>
        </div>
        <div className="p-3 bg-gradient-to-r from-teal-900/40 to-teal-800/30 rounded-lg border border-teal-700/30">
          <div className="text-xs text-teal-300">Orders without GPS</div>
          <div className="text-xl font-bold text-white">{gpsStats.withoutGPS}</div>
          <div className="text-xs text-teal-200 mt-1">
            Need geocoding
          </div>
        </div>
        <div className="p-3 bg-gradient-to-r from-teal-900/40 to-teal-800/30 rounded-lg border border-teal-700/30">
          <div className="text-xs text-teal-300">Addresses with GPS</div>
          <div className="text-xl font-bold text-white">{gpsStats.addressesWithGPS} / {gpsStats.totalAddresses}</div>
          <div className="text-xs text-teal-200 mt-1">
            {gpsStats.totalAddresses > 0 ? Math.round((gpsStats.addressesWithGPS / gpsStats.totalAddresses) * 100) : 0}% of addresses
          </div>
        </div>
        <div className="p-3 bg-gradient-to-r from-purple-900/40 to-purple-800/30 rounded-lg border border-purple-700/30">
          <div className="text-xs text-purple-300">3D Renders</div>
          <div className="text-xl font-bold text-white">{gpsStats.with3DRender} / {gpsStats.total}</div>
          <div className="text-xs text-purple-200 mt-1">
            {gpsStats.total > 0 ? Math.round((gpsStats.with3DRender / gpsStats.total) * 100) : 0}% of vehicles
          </div>
        </div>
      </div>
    </>
  );
};

// Toolbar Component
export const Toolbar = ({ 
  searchTerm,
  onSearchChange,
  selectedFilter,
  onFilterChange,
  onAddOrder,
  onGeocodeAll,
  onRegenerate3D,
  isGeocodingInProgress,
  isLoading
}) => {
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between">
      <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4 md:mb-0">
        Vehicle Orders
      </h1>
      
      <div className="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search orders..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full md:w-64 px-4 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
          </svg>
        </div>
        
        <select
          value={selectedFilter}
          onChange={(e) => onFilterChange(e.target.value)}
          className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-1 focus:ring-blue-500"
        >
          <option value="all">All Orders</option>
          <option value="open">Open Orders</option>
          <option value="pending-pickup">Pending Pickup</option>
          <option value="secure">Secure</option>
          <option value="on-hold">On-Hold</option>
          <option value="claim">Claim</option>
          <option value="closed">Closed</option>
          <option value="restricted">Restricted</option>
        </select>
        
        <button
          onClick={onAddOrder}
          className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white rounded-md shadow-md flex items-center justify-center transition-colors duration-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          Add Order
        </button>
        
        {/* Geocode All Button */}
        <button
          onClick={onGeocodeAll}
          disabled={isGeocodingInProgress || isLoading}
          className={`px-4 py-2 flex items-center justify-center rounded-md shadow-md transition-colors duration-200 ${
            isGeocodingInProgress || isLoading
              ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
              : 'bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-500 hover:to-teal-500 text-white'
          }`}
        >
          {isGeocodingInProgress ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Geocoding...
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
              Geocode All
            </>
          )}
        </button>
        
        {/* Regenerate 3D Button */}
        <button
          onClick={onRegenerate3D}
          disabled={isLoading}
          className={`px-4 py-2 flex items-center justify-center rounded-md shadow-md transition-colors duration-200 ${
            isLoading
              ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
              : 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white'
          }`}
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Generating...
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
              </svg>
              Regenerate 3D
            </>
          )}
        </button>
      </div>
    </div>
  );
};

// Alert Component
export const Alert = ({ 
  type = 'success', 
  message, 
  onClose
}) => {
  if (!message) return null;
  
  const alertClasses = {
    success: 'bg-green-900/30 border-green-700 text-green-200',
    error: 'bg-red-900/30 border-red-700 text-red-200',
    warning: 'bg-yellow-900/30 border-yellow-700 text-yellow-200',
    info: 'bg-blue-900/30 border-blue-700 text-blue-200'
  };
  
  const iconMap = {
    success: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    ),
    error: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
      </svg>
    ),
    warning: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
      </svg>
    ),
    info: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2h2a1 1 0 100-2H9z" clipRule="evenodd" />
      </svg>
    )
  };
  
  return (
    <div className={`${alertClasses[type]} border px-4 py-3 rounded mb-4 flex items-center justify-between animate-fadeIn`}>
      <div className="flex items-center">
        {iconMap[type]}
        <span>{message}</span>
      </div>
      <button 
        onClick={onClose}
        className={`${type === 'success' ? 'text-green-200 hover:text-white' : 'text-red-200 hover:text-white'} transition-colors duration-200`}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>
    </div>
  );
};

// Loading Spinner Component
export const LoadingSpinner = () => (
  <div className="flex justify-center items-center py-12">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>
);

// No Orders Message Component
export const NoOrdersMessage = ({ searchTerm, onAddOrder }) => (
  <div className="bg-gray-800 rounded-lg shadow-md border border-gray-700 p-8 text-center animate-fadeIn">
    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
    </svg>
    <h3 className="text-xl font-bold text-gray-300 mb-2">No Orders Found</h3>
    <p className="text-gray-400 mb-6">
      {searchTerm ? 
        `No orders match your search for "${searchTerm}"` : 
        "You don't have any orders yet."}
    </p>
    <button
      onClick={onAddOrder}
      className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white rounded-md shadow-md inline-flex items-center transition-colors duration-200"
    >
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
      </svg>
      Create Your First Order
    </button>
  </div>
);

// Coordinate Editor Component 
export const CoordinateEditor = ({ position, onSave, onCancel }) => {
  const [coordinates, setCoordinates] = React.useState({
    lat: position?.lat || '',
    lng: position?.lng || ''
  });

  const handleChange = (field, value) => {
    setCoordinates(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const lat = parseFloat(coordinates.lat);
    const lng = parseFloat(coordinates.lng);
    
    if (isNaN(lat) || isNaN(lng)) {
      alert('Please enter valid numeric coordinates');
      return;
    }
    
    if (lat < -90 || lat > 90) {
      alert('Latitude must be between -90 and 90 degrees');
      return;
    }
    
    if (lng < -180 || lng > 180) {
      alert('Longitude must be between -180 and 180 degrees');
      return;
    }
    
    onSave({
      lat,
      lng
    });
  };

  return (
    <div className="p-3 bg-gray-800 border border-gray-700 rounded-md">
      <h4 className="text-sm font-semibold text-blue-400 mb-2">Edit GPS Coordinates</h4>
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-2 gap-2 mb-3">
          <div>
            <label className="block text-xs text-gray-400 mb-1">Latitude</label>
            <input
              type="text"
              value={coordinates.lat}
              onChange={(e) => handleChange('lat', e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded px-2 py-1 text-sm text-white focus:outline-none focus:border-blue-500"
              placeholder="e.g., 40.7128"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-400 mb-1">Longitude</label>
            <input
              type="text"
              value={coordinates.lng}
              onChange={(e) => handleChange('lng', e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded px-2 py-1 text-sm text-white focus:outline-none focus:border-blue-500"
              placeholder="e.g., -74.0060"
            />
          </div>
        </div>
        <div className="flex justify-end space-x-2">
          <button
            type="button"
            onClick={onCancel}
            className="px-2 py-1 text-xs text-gray-300 border border-gray-600 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-2 py-1 text-xs bg-blue-600 hover:bg-blue-700 text-white rounded"
          >
            Save
          </button>
        </div>
      </form>
    </div>
  );
};