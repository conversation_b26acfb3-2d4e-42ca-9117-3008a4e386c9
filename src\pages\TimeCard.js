import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext.js';
import { 
  getFirestore, 
  collection, 
  getDocs, 
  getDoc,
  doc, 
  query, 
  orderBy, 
  where,
  Timestamp,
  setDoc,
  deleteDoc,
  addDoc,
  serverTimestamp
} from 'firebase/firestore';

const TimeCard = () => {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [timeCards, setTimeCards] = useState([]);
  const [activeUsers, setActiveUsers] = useState([]);
  const [timeCardHistory, setTimeCardHistory] = useState([]);
  const [userProfiles, setUserProfiles] = useState({});
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [viewMode, setViewMode] = useState('active'); // 'active', 'history', 'reports'
  const [userProfile, setUserProfile] = useState(null);
  
  // User's own clock status
  const [isClockedIn, setIsClockedIn] = useState(false);
  const [userClockInTime, setUserClockInTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState('00:00:00');
  
  // Advanced filtering
  const [filterUser, setFilterUser] = useState('');
  const [filterStartDate, setFilterStartDate] = useState('');
  const [filterEndDate, setFilterEndDate] = useState('');
  const [isFiltering, setIsFiltering] = useState(false);

  // Selected user for detailed view
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserModal, setShowUserModal] = useState(false);
  
  // Admin time entry edit modal
  const [showEditModal, setShowEditModal] = useState(false);
  const [editUserId, setEditUserId] = useState('');
  const [editDate, setEditDate] = useState(new Date().toISOString().split('T')[0]);
  const [editTime, setEditTime] = useState(new Date().toTimeString().slice(0, 5));
  const [editType, setEditType] = useState('clockIn');
  const [editSuccess, setEditSuccess] = useState('');
  const [editError, setEditError] = useState('');
  const [editEntryId, setEditEntryId] = useState(null);
  const [editingEntry, setEditingEntry] = useState(null);

  // Admin time entry bulk edit modal
  const [showBulkEditModal, setShowBulkEditModal] = useState(false);
  const [bulkEditEntries, setBulkEditEntries] = useState([]);
  
  // Auto-redirect non-admin users to history view
  useEffect(() => {
    if (!isAdmin && viewMode === 'active') {
      setViewMode('history');
    }
  }, [isAdmin, viewMode]);
  
  // Update clock
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
      
      // Update elapsed time if user is clocked in
      if (isClockedIn && userClockInTime) {
        setElapsedTime(calculateElapsedTime(userClockInTime));
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [isClockedIn, userClockInTime]);
  
  // Fetch user profile for current user
  useEffect(() => {
    async function fetchUserProfile() {
      if (!currentUser) return;
      
      try {
        const db = getFirestore();
        const userProfileRef = doc(db, "userProfiles", currentUser.uid);
        const userProfileDoc = await getDoc(userProfileRef);
        
        if (userProfileDoc.exists()) {
          setUserProfile(userProfileDoc.data());
        }
      } catch (error) {
        console.error("Error fetching user profile:", error);
      }
    }
    
    fetchUserProfile();
  }, [currentUser]);

  // Check if the current user is clocked in
  useEffect(() => {
    async function checkUserClockStatus() {
      if (!currentUser) return;
      
      try {
        const db = getFirestore();
        const timeCardRef = doc(db, 'timeCards', currentUser.uid);
        const timeCardDoc = await getDoc(timeCardRef);
        
        if (timeCardDoc.exists()) {
          const timeCardData = timeCardDoc.data();
          const today = new Date().toLocaleDateString();
          
          // Check if the user is currently clocked in
          if (timeCardData.currentDay === today && 
              timeCardData.clockedIn && 
              !timeCardData.clockedOut) {
            setIsClockedIn(true);
            setUserClockInTime(timeCardData.clockInTime?.toDate());
          } else {
            setIsClockedIn(false);
            setUserClockInTime(null);
          }
        } else {
          setIsClockedIn(false);
          setUserClockInTime(null);
        }
      } catch (error) {
        console.error("Error checking clock status:", error);
        setIsClockedIn(false);
        setUserClockInTime(null);
      }
    }
    
    checkUserClockStatus();
  }, [currentUser]);

  // Load time cards based on view mode
  useEffect(() => {
    async function fetchTimeCards() {
      setLoading(true);
      setError('');
      
      try {
        const db = getFirestore();
        
        // First fetch user profiles (for display info)
        const profilesSnapshot = await getDocs(collection(db, 'userProfiles'));
        const profilesData = {};
        
        profilesSnapshot.forEach((doc) => {
          profilesData[doc.id] = doc.data();
        });
        
        setUserProfiles(profilesData);
        
        if (viewMode === 'active' && isAdmin) {
          // Fetch active users (currently clocked in) - only for admins
          const activeSnapshot = await getDocs(collection(db, 'timeCards'));
          const activeData = [];
          
          activeSnapshot.forEach((doc) => {
            const data = doc.data();
            // Only include users who are currently clocked in
            if (data.clockedIn && !data.clockedOut) {
              activeData.push({
                id: doc.id,
                ...data,
                clockInTime: data.clockInTime?.toDate(),
                clockOutTime: data.clockOutTime?.toDate(),
                profile: profilesData[doc.id] || {}
              });
            }
          });
          
          setActiveUsers(activeData);
          
        } else if (viewMode === 'history') {
          // Apply date filter for history
          const startOfDay = new Date(selectedDate);
          startOfDay.setHours(0, 0, 0, 0);
          
          const endOfDay = new Date(selectedDate);
          endOfDay.setHours(23, 59, 59, 999);
          
          const startTimestamp = Timestamp.fromDate(startOfDay);
          const endTimestamp = Timestamp.fromDate(endOfDay);
          
          // Fetch history for the selected date
          let historyQuery;
          
          if (isFiltering && filterUser) {
            historyQuery = query(
              collection(db, 'timeCardHistory'),
              where('userId', '==', filterUser),
              where('timestamp', '>=', startTimestamp),
              where('timestamp', '<=', endTimestamp),
              orderBy('timestamp')
            );
          } else if (isAdmin) {
            // Admins can see all user data
            historyQuery = query(
              collection(db, 'timeCardHistory'),
              where('timestamp', '>=', startTimestamp),
              where('timestamp', '<=', endTimestamp),
              orderBy('timestamp')
            );
          } else {
            // Non-admins can only see their own data
            historyQuery = query(
              collection(db, 'timeCardHistory'),
              where('userId', '==', currentUser.uid),
              where('timestamp', '>=', startTimestamp),
              where('timestamp', '<=', endTimestamp),
              orderBy('timestamp')
            );
          }
          
          const historySnapshot = await getDocs(historyQuery);
          const historyData = [];
          
          historySnapshot.forEach((doc) => {
            const data = doc.data();
            historyData.push({
              id: doc.id,
              ...data,
              timestamp: data.timestamp?.toDate(),
              profile: profilesData[data.userId] || {}
            });
          });
          
          setTimeCardHistory(historyData);
          
          // Also fetch all time cards for reporting
          let timeCardsSnapshot;
          
          if (isAdmin) {
            timeCardsSnapshot = await getDocs(collection(db, 'timeCards'));
          } else {
            // For non-admins, only fetch their own card
            const userCardRef = doc(db, 'timeCards', currentUser.uid);
            const userCardDoc = await getDoc(userCardRef);
            
            if (userCardDoc.exists()) {
              timeCardsSnapshot = { 
                docs: [userCardDoc],
                forEach: (callback) => callback(userCardDoc)
              };
            } else {
              timeCardsSnapshot = { docs: [], forEach: () => {} };
            }
          }
          
          const timeCardsData = [];
          
          timeCardsSnapshot.forEach((doc) => {
            const data = doc.data();
            timeCardsData.push({
              id: doc.id,
              ...data,
              clockInTime: data.clockInTime?.toDate(),
              clockOutTime: data.clockOutTime?.toDate(),
              profile: profilesData[doc.id] || {}
            });
          });
          
          setTimeCards(timeCardsData);
        } else if (viewMode === 'reports' && isAdmin) {
          // Reports view is only for admins
          // Apply date range filter for reports
          let timeCardsQuery;
          
          if (isFiltering && filterStartDate && filterEndDate) {
            const startDate = new Date(filterStartDate);
            startDate.setHours(0, 0, 0, 0);
            
            const endDate = new Date(filterEndDate);
            endDate.setHours(23, 59, 59, 999);
            
            const startTimestamp = Timestamp.fromDate(startDate);
            const endTimestamp = Timestamp.fromDate(endDate);
            
            if (filterUser) {
              // Filter by user and date range
              timeCardsQuery = query(
                collection(db, 'timeCardHistory'),
                where('userId', '==', filterUser),
                where('timestamp', '>=', startTimestamp),
                where('timestamp', '<=', endTimestamp),
                orderBy('timestamp')
              );
            } else {
              // Filter by date range only
              timeCardsQuery = query(
                collection(db, 'timeCardHistory'),
                where('timestamp', '>=', startTimestamp),
                where('timestamp', '<=', endTimestamp),
                orderBy('timestamp')
              );
            }
          } else {
            // Default to last 7 days
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            sevenDaysAgo.setHours(0, 0, 0, 0);
            
            const startTimestamp = Timestamp.fromDate(sevenDaysAgo);
            const endTimestamp = Timestamp.fromDate(new Date());
            
            timeCardsQuery = query(
              collection(db, 'timeCardHistory'),
              where('timestamp', '>=', startTimestamp),
              where('timestamp', '<=', endTimestamp),
              orderBy('timestamp')
            );
          }
          
          const timeCardsSnapshot = await getDocs(timeCardsQuery);
          const timeCardsData = [];
          
          timeCardsSnapshot.forEach((doc) => {
            const data = doc.data();
            timeCardsData.push({
              id: doc.id,
              ...data,
              timestamp: data.timestamp?.toDate(),
              profile: profilesData[data.userId] || {}
            });
          });
          
          setTimeCardHistory(timeCardsData);
        } else if (viewMode === 'active' && !isAdmin) {
          // For non-admins in active view, we'll only show the user's own info
          // which is already handled by the user clock-in/out panel
          setActiveUsers([]);
        }
      } catch (error) {
        console.error("Error fetching time cards:", error);
        setError("Failed to load time card data. Please try again.");
      } finally {
        setLoading(false);
      }
    }
    
    fetchTimeCards();
  }, [viewMode, selectedDate, isFiltering, filterUser, filterStartDate, filterEndDate, currentUser, isAdmin]);

  // Format date for display
  const formatDate = (date) => {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  };

  // Format time for display
  const formatTime = (date) => {
    const options = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };
    return date.toLocaleTimeString('en-US', options);
  };

  // Calculate elapsed time between two dates
  const calculateElapsedTime = (startTime, endTime = new Date()) => {
    if (!startTime) return '--:--:--';
    
    const elapsed = endTime - startTime;
    const hours = Math.floor(elapsed / (1000 * 60 * 60));
    const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Format duration in hours with decimal points
  const formatDuration = (milliseconds) => {
    if (!milliseconds) return 'N/A';
    
    // Convert to hours with decimal precision for minutes
    const totalHours = milliseconds / (1000 * 60 * 60);
  
    // Display with 2 decimal places
    return `${totalHours.toFixed(2)} hours`;
  };

  // Calculate hours worked based on time card history
  const calculateHoursWorked = (userId, date) => {
    if (!timeCardHistory.length) return 0;
    
    // Get all entries for this user, regardless of who created them
    const userEntries = timeCardHistory
      .filter(entry => entry.userId === userId)
      .sort((a, b) => a.timestamp - b.timestamp);
    
    // For daily calculation, only consider entries from the specified date
    const dateStart = new Date(date);
    dateStart.setHours(0, 0, 0, 0);
    
    const dateEnd = new Date(date);
    dateEnd.setHours(23, 59, 59, 999);
    
    let total = 0;
    let lastClockIn = null;
    
    // Process all entries chronologically
    for (const entry of userEntries) {
      if (entry.type === 'clockIn') {
        lastClockIn = entry.timestamp;
        console.log(`Processing clock in for ${userId} at ${lastClockIn}, admin created: ${entry.adminAction || false}`);
      } else if (entry.type === 'clockOut' && lastClockIn !== null) {
        // Calculate how much of this shift falls within our day
        let startTime = lastClockIn;
        let endTime = entry.timestamp;
        
        // Shift started before our day, adjust start time to beginning of our day
        if (startTime < dateStart) {
          startTime = dateStart;
        }
        
        // Shift ended after our day, adjust end time to end of our day
        if (endTime > dateEnd) {
          endTime = dateEnd;
        }
        
        // Only count if there's actual time within our day
        if (startTime < endTime) {
          const duration = endTime - startTime;
          total += duration;
          console.log(`Processing clock out for ${userId} at ${endTime}, admin created: ${entry.adminAction || false}, duration: ${duration / (1000 * 60 * 60)} hours`);
        }
        
        lastClockIn = null;
      }
    }
    
    // Handle ongoing shifts
    if (lastClockIn !== null) {
      let endTime = new Date(); // Now
      let startTime = lastClockIn;
      
      // Adjust times to be within our day
      if (startTime < dateStart) {
        startTime = dateStart;
      }
      
      if (endTime > dateEnd) {
        endTime = dateEnd;
      }
      
      if (startTime < endTime) {
        const duration = endTime - startTime;
        total += duration;
        console.log(`Processing active shift for ${userId}, duration so far: ${duration / (1000 * 60 * 60)} hours`);
      }
    }
    
    console.log(`Total hours worked for ${userId} on ${date}: ${total / (1000 * 60 * 60)}`);
    return total;
  };

  // Calculate total hours for a user - FIXED VERSION
  const calculateTotalHours = (userId) => {
    // Get all entries for this user and sort chronologically
    const userHistory = timeCardHistory
      .filter(entry => entry.userId === userId)
      .sort((a, b) => {
        // Ensure we're comparing Date objects properly
        return new Date(a.timestamp) - new Date(b.timestamp);
      });
    
    // Debug log to ensure proper data
    console.log(`Calculating total hours for ${userId} with ${userHistory.length} entries`);
    
    let total = 0;
    let lastClockIn = null;
    
    // Process entries in chronological order, regardless of who created them
    for (const entry of userHistory) {
      if (entry.type === 'clockIn') {
        // Store the most recent clock-in, whether user or admin created it
        lastClockIn = entry.timestamp;
        console.log(`Clock in at ${lastClockIn}, admin created: ${entry.adminAction || false}`);
      } else if (entry.type === 'clockOut' && lastClockIn !== null) {
        // Ensure both timestamps are Date objects
        const clockInTime = new Date(lastClockIn);
        const clockOutTime = new Date(entry.timestamp);
        
        // Calculate duration in milliseconds
        const duration = clockOutTime - clockInTime;
        
        // Avoid negative or unreasonable values (more than 24 hours for a single shift)
        if (duration > 0 && duration < 24 * 60 * 60 * 1000) {
          total += duration;
          console.log(`Clock out at ${clockOutTime}, admin created: ${entry.adminAction || false}, duration: ${duration / (1000 * 60 * 60)} hours`);
        } else {
          console.warn(`Suspicious duration detected: ${duration} ms`);
        }
        
        lastClockIn = null; // Reset the clock-in
      }
    }
    
    // If there's still an open clock-in with no matching clock-out,
    // calculate time until now (for currently active shifts)
    if (lastClockIn !== null) {
      const clockInTime = new Date(lastClockIn);
      const now = new Date();
      const duration = now - clockInTime;
      
      if (duration > 0 && duration < 24 * 60 * 60 * 1000) {
        total += duration;
        console.log(`Active shift from ${clockInTime}, duration so far: ${duration / (1000 * 60 * 60)} hours`);
      }
    }
    
    console.log(`Total milliseconds: ${total}, Total hours: ${total / (1000 * 60 * 60)}`);
    return total;
  };

  // Update worked hours in Firestore database
  const updateWorkedHours = async (userId, clockInTime, clockOutTime) => {
    try {
      const db = getFirestore();
      
      // Calculate hours worked for this shift
      const shiftDurationMs = clockOutTime - clockInTime;
      const hoursWorked = shiftDurationMs / (1000 * 60 * 60);
      
      if (hoursWorked <= 0 || isNaN(hoursWorked)) {
        console.error("Invalid shift duration:", hoursWorked);
        return;
      }
      
      console.log(`Updating worked hours: ${hoursWorked.toFixed(2)} hours for user ${userId}`);
      
      // Get date info
      const date = new Date(clockOutTime);
      const year = date.getFullYear();
      const month = date.getMonth() + 1; // 1-12
      const day = date.getDate();
      const dateString = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      
      // Create document ID
      const docId = `${userId}_${dateString}`;
      const workedHoursRef = doc(db, 'workedHours', docId);
      
      // Get current week start (Sunday)
      const today = new Date();
      const currentDayOfWeek = today.getDay(); // 0-6, 0 is Sunday
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - currentDayOfWeek); // Set to Sunday
      weekStart.setHours(0, 0, 0, 0);
      
      // Get month start
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
      
      // Check if record exists
      const workedHoursDoc = await getDoc(workedHoursRef);
      
      if (workedHoursDoc.exists()) {
        // Update existing record
        const existingData = workedHoursDoc.data();
        
        await setDoc(workedHoursRef, {
          dailyHours: (existingData.dailyHours || 0) + hoursWorked,
          weeklyHours: (existingData.weeklyHours || 0) + hoursWorked,
          monthlyHours: (existingData.monthlyHours || 0) + hoursWorked,
          lastUpdated: serverTimestamp()
        }, { merge: true });
        
        console.log(`Updated worked hours for ${userId} on ${dateString}: ${hoursWorked.toFixed(2)} hours added`);
      } else {
        // Create new record
        await setDoc(workedHoursRef, {
          userId,
          userName: userProfiles[userId]?.displayName || 'Unknown User',
          date: Timestamp.fromDate(date),
          dateString,
          dailyHours: hoursWorked,
          weeklyHours: hoursWorked, 
          monthlyHours: hoursWorked,
          weekStart: Timestamp.fromDate(weekStart),
          monthStart: Timestamp.fromDate(monthStart), 
          day: day,
          month: month,
          year: year,
          lastUpdated: serverTimestamp(),
          createdAt: serverTimestamp()
        });
        
        console.log(`Created new worked hours record for ${userId} on ${dateString}: ${hoursWorked.toFixed(2)} hours`);
      }
    } catch (error) {
      console.error("Error updating worked hours:", error);
    }
  };

  // NEW FUNCTION: Aggregate team time data across all users
  const aggregateTeamTimeData = async () => {
    if (!isAdmin) return;
    
    try {
      const db = getFirestore();
      
      // Get all users from the team
      const profilesSnapshot = await getDocs(collection(db, 'userProfiles'));
      const userIds = [];
      
      profilesSnapshot.forEach((doc) => {
        userIds.push(doc.id);
      });
      
      // Calculate aggregated time data
      let teamHoursToday = 0;
      let teamHoursWeek = 0;
      let teamHoursMonth = 0;
      let teamHoursYTD = 0;
      
      // Current date info for filtering
      const today = new Date();
      const startOfToday = new Date(today);
      startOfToday.setHours(0, 0, 0, 0);
      
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday
      startOfWeek.setHours(0, 0, 0, 0);
      
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const startOfYear = new Date(today.getFullYear(), 0, 1);
      
      // Calculate for each user
      for (const userId of userIds) {
        // Calculate hours today
        let userHoursToday = calculateHoursWorked(userId, today.toISOString().split('T')[0]);
        teamHoursToday += userHoursToday;
        
        // Get all time card history for week/month/year calculations - no filter on adminAction
        const historyQuery = query(
          collection(db, 'timeCardHistory'),
          where('userId', '==', userId),
          orderBy('timestamp')
        );
        
        const historySnapshot = await getDocs(historyQuery);
        const userHistory = [];
        
        historySnapshot.forEach((doc) => {
          const data = doc.data();
          userHistory.push({
            ...data,
            timestamp: data.timestamp?.toDate()
          });
        });
        
        // Calculate weekly, monthly, YTD hours
        let weekHours = 0;
        let monthHours = 0;
        let ytdHours = 0;
        
        let lastClockIn = null;
        
        for (const entry of userHistory) {
          const timestamp = new Date(entry.timestamp);
          
          if (entry.type === 'clockIn') {
            lastClockIn = timestamp;
          } else if (entry.type === 'clockOut' && lastClockIn !== null) {
            const duration = timestamp - lastClockIn;
            
            // Add to appropriate time periods
            if (lastClockIn >= startOfWeek) {
              weekHours += duration;
            }
            
            if (lastClockIn >= startOfMonth) {
              monthHours += duration;
            }
            
            if (lastClockIn >= startOfYear) {
              ytdHours += duration;
            }
            
            lastClockIn = null;
          }
        }
        
        // Convert milliseconds to hours
        teamHoursWeek += (weekHours / (1000 * 60 * 60));
        teamHoursMonth += (monthHours / (1000 * 60 * 60));
        teamHoursYTD += (ytdHours / (1000 * 60 * 60));
        
        // Update user stats document
        const userStatsRef = doc(db, 'userStats', userId);
        await setDoc(userStatsRef, {
          userHoursWorkedToday: userHoursToday / (1000 * 60 * 60),
          userHoursWorkedWeek: weekHours / (1000 * 60 * 60),
          userHoursWorkedMonth: monthHours / (1000 * 60 * 60),
          userHoursWorkedYTD: ytdHours / (1000 * 60 * 60),
          lastUpdated: serverTimestamp()
        }, { merge: true });
        
        console.log(`Updated stats for user ${userId}: Today=${userHoursToday / (1000 * 60 * 60)}, Week=${weekHours / (1000 * 60 * 60)}, Month=${monthHours / (1000 * 60 * 60)}, YTD=${ytdHours / (1000 * 60 * 60)}`);
      }
      
      // Update team stats in Firestore
      const teamStatsRef = doc(db, 'teamStats', 'summary');
      await setDoc(teamStatsRef, {
        teamHoursWorkedToday: teamHoursToday / (1000 * 60 * 60),
        teamHoursWorkedWeek: teamHoursWeek,
        teamHoursWorkedMonth: teamHoursMonth,
        teamHoursWorkedYTD: teamHoursYTD,
        lastUpdated: serverTimestamp()
      }, { merge: true });
      
      console.log("Time card data aggregation completed successfully");
      console.log(`Team totals: Today=${teamHoursToday / (1000 * 60 * 60)}, Week=${teamHoursWeek}, Month=${teamHoursMonth}, YTD=${teamHoursYTD}`);
      
    } catch (error) {
      console.error("Error aggregating time data:", error);
    }
  };

  // Clock in current user
  const clockInUser = async () => {
    if (!currentUser) return;
    
    try {
      setLoading(true);
      setError('');
      
      const db = getFirestore();
      const timeCardRef = doc(db, 'timeCards', currentUser.uid);
      const currentDate = new Date();
      const today = currentDate.toLocaleDateString();
      
      // Check if already clocked in
      const timeCardDoc = await getDoc(timeCardRef);
      if (timeCardDoc.exists()) {
        const data = timeCardDoc.data();
        if (data.currentDay === today && data.clockedIn && !data.clockedOut) {
          setError("You are already clocked in for today.");
          setLoading(false);
          return;
        }
      }
      
      // Clock in the user
      await setDoc(timeCardRef, {
        userId: currentUser.uid,
        userName: userProfile?.displayName || currentUser.displayName || currentUser.email,
        userPhotoBase64: userProfile?.photoBase64 || null,
        currentDay: today,
        clockedIn: true,
        clockedOut: false,
        clockInTime: Timestamp.fromDate(currentDate),
        clockOutTime: null,
        updatedAt: serverTimestamp()
      }, { merge: true });
      
      // Add to history collection
      await addDoc(collection(db, 'timeCardHistory'), {
        userId: currentUser.uid,
        userName: userProfile?.displayName || currentUser.displayName || currentUser.email,
        userPhotoBase64: userProfile?.photoBase64 || null,
        date: today,
        type: 'clockIn',
        adminAction: false,
        timestamp: Timestamp.fromDate(currentDate)
      });
      
      // Update local state
      setIsClockedIn(true);
      setUserClockInTime(currentDate);
      
      // Add this user to active users if viewing active users as admin
      if (viewMode === 'active' && isAdmin) {
        const newActiveUser = {
          id: currentUser.uid,
          userId: currentUser.uid,
          userName: userProfile?.displayName || currentUser.displayName || currentUser.email,
          userPhotoBase64: userProfile?.photoBase64 || null,
          clockedIn: true,
          clockedOut: false,
          clockInTime: currentDate,
          clockOutTime: null,
          profile: userProfile || {}
        };
        
        setActiveUsers(prev => {
          // Check if user already exists
          const exists = prev.some(user => user.id === currentUser.uid);
          if (exists) {
            return prev.map(user => user.id === currentUser.uid ? newActiveUser : user);
          } else {
            return [...prev, newActiveUser];
          }
        });
      }
      
      // Update aggregated time data
      if (isAdmin) {
        await aggregateTeamTimeData();
      }
      
    } catch (error) {
      console.error("Error clocking in:", error);
      setError("Failed to clock in. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Clock out current user
  const clockOutUser = async () => {
    if (!currentUser || !isClockedIn) return;
    
    try {
      setLoading(true);
      setError('');
      
      const db = getFirestore();
      const timeCardRef = doc(db, 'timeCards', currentUser.uid);
      const currentDate = new Date();
      const today = currentDate.toLocaleDateString();
      
      // Fetch the clock-in time to calculate hours
      const timeCardDoc = await getDoc(timeCardRef);
      if (!timeCardDoc.exists()) {
        setError("Could not find your active time card.");
        setLoading(false);
        return;
      }
      
      const data = timeCardDoc.data();
      const clockInTime = data.clockInTime?.toDate();
      
      // Clock out the user
      await setDoc(timeCardRef, {
        clockedOut: true,
        clockOutTime: Timestamp.fromDate(currentDate),
        updatedAt: serverTimestamp()
      }, { merge: true });
      
      // Add to history collection
      await addDoc(collection(db, 'timeCardHistory'), {
        userId: currentUser.uid,
        userName: userProfile?.displayName || currentUser.displayName || currentUser.email,
        userPhotoBase64: userProfile?.photoBase64 || null,
        date: today,
        type: 'clockOut',
        adminAction: false,
        timestamp: Timestamp.fromDate(currentDate)
      });
      
      // Update worked hours database
      if (clockInTime) {
        await updateWorkedHours(currentUser.uid, clockInTime, currentDate);
      }
      
      // Update local state
      setIsClockedIn(false);
      setUserClockInTime(null);
      
      // Remove from active users if viewing active users as admin
      if (viewMode === 'active' && isAdmin) {
        setActiveUsers(prev => prev.filter(user => user.id !== currentUser.uid));
      }
      
      // Update aggregated time data
      if (isAdmin) {
        await aggregateTeamTimeData();
      }
      
    } catch (error) {
      console.error("Error clocking out:", error);
      setError("Failed to clock out. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Clock in a user (admin functionality)
  const adminClockInUser = async (userId) => {
    if (!isAdmin) return;
    
    try {
      setLoading(true);
      const db = getFirestore();
      const timeCardRef = doc(db, 'timeCards', userId);
      const currentDate = new Date();
      const today = currentDate.toLocaleDateString();
      
      await setDoc(timeCardRef, {
        userId,
        userName: userProfiles[userId]?.displayName || 'Unknown User',
        userPhotoBase64: userProfiles[userId]?.photoBase64 || null,
        currentDay: today,
        clockedIn: true,
        clockedOut: false,
        clockInTime: Timestamp.fromDate(currentDate),
        clockOutTime: null,
        updatedAt: serverTimestamp()
      }, { merge: true });
      
      // Also add to history collection
      await addDoc(collection(db, 'timeCardHistory'), {
        userId,
        userName: userProfiles[userId]?.displayName || 'Unknown User',
        userPhotoBase64: userProfiles[userId]?.photoBase64 || null,
        date: today,
        type: 'clockIn',
        adminAction: true,
        adminId: currentUser.uid,
        adminName: userProfile?.displayName || currentUser.displayName || currentUser.email,
        timestamp: Timestamp.fromDate(currentDate)
      });
      
      // Refresh data
      if (viewMode === 'active') {
        const updatedActiveUsers = [...activeUsers];
        const existingUserIndex = updatedActiveUsers.findIndex(user => user.id === userId);
        
        if (existingUserIndex >= 0) {
          updatedActiveUsers[existingUserIndex] = {
            ...updatedActiveUsers[existingUserIndex],
            clockedIn: true,
            clockedOut: false,
            clockInTime: currentDate,
            clockOutTime: null
          };
        } else {
          updatedActiveUsers.push({
            id: userId,
            userId,
            clockedIn: true,
            clockedOut: false,
            clockInTime: currentDate,
            clockOutTime: null,
            profile: userProfiles[userId] || {}
          });
        }
        
        setActiveUsers(updatedActiveUsers);
      }
      
      // Update aggregated time data
      await aggregateTeamTimeData();
      
    } catch (error) {
      console.error("Error clocking in user:", error);
      setError("Failed to clock in user. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Clock out a user (admin functionality)
  const adminClockOutUser = async (userId) => {
    if (!isAdmin) return;
    
    try {
      setLoading(true);
      const db = getFirestore();
      const timeCardRef = doc(db, 'timeCards', userId);
      const currentDate = new Date();
      const today = currentDate.toLocaleDateString();
      
      // Fetch the clock-in time to calculate hours
      const timeCardDoc = await getDoc(timeCardRef);
      if (!timeCardDoc.exists()) {
        setError("Could not find the active time card for this user.");
        setLoading(false);
        return;
      }
      
      const data = timeCardDoc.data();
      const clockInTime = data.clockInTime?.toDate();
      
      await setDoc(timeCardRef, {
        clockedOut: true,
        clockOutTime: Timestamp.fromDate(currentDate),
        updatedAt: serverTimestamp()
      }, { merge: true });
      
      // Add to history collection
      await addDoc(collection(db, 'timeCardHistory'), {
        userId,
        userName: userProfiles[userId]?.displayName || 'Unknown User',
        userPhotoBase64: userProfiles[userId]?.photoBase64 || null,
        date: today,
        type: 'clockOut',
        adminAction: true,
        adminId: currentUser.uid,
        adminName: userProfile?.displayName || currentUser.displayName || currentUser.email,
        timestamp: Timestamp.fromDate(currentDate)
      });
      
      // Update worked hours database
      if (clockInTime) {
        await updateWorkedHours(userId, clockInTime, currentDate);
      }
      
      // Update local state
      if (viewMode === 'active') {
        setActiveUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
      }
      
      // Update aggregated time data
      await aggregateTeamTimeData();
      
    } catch (error) {
      console.error("Error clocking out user:", error);
      setError("Failed to clock out user. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // View user details
  const viewUserDetails = (user) => {
    setSelectedUser(user);
    setShowUserModal(true);
  };

  // Close user details modal
  const closeUserModal = () => {
    setShowUserModal(false);
    setSelectedUser(null);
  };

  // Apply filters
  const applyFilters = () => {
    setIsFiltering(true);
  };

  // Clear filters
  const clearFilters = () => {
    setFilterUser('');
    setFilterStartDate('');
    setFilterEndDate('');
    setIsFiltering(false);
  };

  // Open edit modal for creating new time card entry
  const openNewEntryModal = (userId = '') => {
    setEditUserId(userId);
    setEditDate(new Date().toISOString().split('T')[0]);
    setEditTime(new Date().toTimeString().slice(0, 5));
    setEditType('clockIn');
    setEditEntryId(null);
    setEditingEntry(null);
    setEditError('');
    setEditSuccess('');
    setShowEditModal(true);
  };

  // Open edit modal with existing entry data
  const openEditEntryModal = (entry) => {
    const entryDate = entry.timestamp.toISOString().split('T')[0];
    const entryTime = entry.timestamp.toTimeString().slice(0, 5);
    
    setEditUserId(entry.userId);
    setEditDate(entryDate);
    setEditTime(entryTime);
    setEditType(entry.type);
    setEditEntryId(entry.id);
    setEditingEntry(entry);
    setEditError('');
    setEditSuccess('');
    setShowEditModal(true);
  };

  // Delete time card entry
  const deleteTimeCardEntry = async (entryId) => {
    if (!isAdmin) return;
    
    try {
      setLoading(true);
      setEditError('');
      
      const db = getFirestore();
      const entryRef = doc(db, 'timeCardHistory', entryId);
      
      await deleteDoc(entryRef);
      
      // Refresh data
      setTimeCardHistory(prevHistory => prevHistory.filter(entry => entry.id !== entryId));
      
      setEditSuccess('Entry deleted successfully.');
      
      // Update aggregated time data
      await aggregateTeamTimeData();
      
      // Close the modal after a delay
      setTimeout(() => {
        setShowEditModal(false);
        setEditSuccess('');
      }, 1500);
      
    } catch (error) {
      console.error("Error deleting entry:", error);
      setEditError("Failed to delete entry. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Save time card entry (add or update)
  const saveTimeCardEntry = async () => {
    if (!isAdmin) return;
    
    try {
      setLoading(true);
      setEditError('');
      setEditSuccess('');
      
      // Validate inputs
      if (!editUserId) {
        setEditError('Please select a user.');
        setLoading(false);
        return;
      }
      
      if (!editDate || !editTime) {
        setEditError('Please select a valid date and time.');
        setLoading(false);
        return;
      }
      
      const db = getFirestore();
      
      // Create timestamp from date and time inputs
      const [year, month, day] = editDate.split('-').map(num => parseInt(num));
      const [hours, minutes] = editTime.split(':').map(num => parseInt(num));
      
      const entryTimestamp = new Date(year, month - 1, day, hours, minutes);
      const formattedDate = entryTimestamp.toLocaleDateString();
      
      // Check for existing entries (when creating a new entry)
      if (!editEntryId) {
        const startOfDay = new Date(entryTimestamp);
        startOfDay.setHours(0, 0, 0, 0);
        
        const endOfDay = new Date(entryTimestamp);
        endOfDay.setHours(23, 59, 59, 999);
        
        const historyQuery = query(
          collection(db, 'timeCardHistory'),
          where('userId', '==', editUserId),
          where('timestamp', '>=', Timestamp.fromDate(startOfDay)),
          where('timestamp', '<=', Timestamp.fromDate(endOfDay)),
          where('type', '==', editType)
        );
        
        const historySnapshot = await getDocs(historyQuery);
        
        // Warn about duplicate entries but don't prevent creation
        if (!historySnapshot.empty) {
          if (!window.confirm(`There are already ${editType} entries for this user on this date. Are you sure you want to add another?`)) {
            setLoading(false);
            return;
          }
        }
      }
      
      // Prepare entry data
      const entryData = {
        userId: editUserId,
        userName: userProfiles[editUserId]?.displayName || 'Unknown User',
        userPhotoBase64: userProfiles[editUserId]?.photoBase64 || null,
        date: formattedDate,
        type: editType,
        adminAction: true,
        adminId: currentUser.uid,
        adminName: userProfile?.displayName || currentUser.displayName || currentUser.email,
        timestamp: Timestamp.fromDate(entryTimestamp),
        updatedAt: serverTimestamp()
      };
      
      // Add or update entry
      if (editEntryId) {
        // Update existing entry
        const entryRef = doc(db, 'timeCardHistory', editEntryId);
        await setDoc(entryRef, entryData, { merge: true });
        
        // Update the timeCards document as well if this is the most recent entry
        if (editType === 'clockIn') {
          // Check if this is the most recent clock in
          const timeCardRef = doc(db, 'timeCards', editUserId);
          const timeCardDoc = await getDoc(timeCardRef);
          
          if (timeCardDoc.exists()) {
            const data = timeCardDoc.data();
            
            // Only update if this is a current day edit
            if (data.currentDay === formattedDate) {
              await setDoc(timeCardRef, {
                clockInTime: Timestamp.fromDate(entryTimestamp),
                updatedAt: serverTimestamp()
              }, { merge: true });
            }
          }
        } else if (editType === 'clockOut') {
          // Check if this is the most recent clock out
          const timeCardRef = doc(db, 'timeCards', editUserId);
          const timeCardDoc = await getDoc(timeCardRef);
          
          if (timeCardDoc.exists()) {
            const data = timeCardDoc.data();
            
            // Only update if this is a current day edit
            if (data.currentDay === formattedDate) {
              await setDoc(timeCardRef, {
                clockOutTime: Timestamp.fromDate(entryTimestamp),
                updatedAt: serverTimestamp()
              }, { merge: true });
            }
            
            // If this is a clock-out, update worked hours
            if (data.clockInTime && editType === 'clockOut') {
              const clockInTime = data.clockInTime.toDate();
              const clockOutTime = entryTimestamp;
              
              // Update worked hours
              await updateWorkedHours(editUserId, clockInTime, clockOutTime);
            }
          }
        }
        
        setEditSuccess('Entry updated successfully.');
      } else {
        // Add new entry
        await addDoc(collection(db, 'timeCardHistory'), entryData);
        
        // Update the timeCards document if this is today's entry
        const today = new Date().toLocaleDateString();
        
        if (formattedDate === today) {
          const timeCardRef = doc(db, 'timeCards', editUserId);
          
          if (editType === 'clockIn') {
            await setDoc(timeCardRef, {
              userId: editUserId,
              userName: userProfiles[editUserId]?.displayName || 'Unknown User',
              userPhotoBase64: userProfiles[editUserId]?.photoBase64 || null,
              currentDay: formattedDate,
              clockedIn: true,
              clockedOut: false,
              clockInTime: Timestamp.fromDate(entryTimestamp),
              updatedAt: serverTimestamp()
            }, { merge: true });
          } else if (editType === 'clockOut') {
            // Get clock-in time if available
            const timeCardDoc = await getDoc(timeCardRef);
            let clockInTime = null;
            
            if (timeCardDoc.exists()) {
              const data = timeCardDoc.data();
              clockInTime = data.clockInTime?.toDate();
              
              await setDoc(timeCardRef, {
                clockedOut: true,
                clockOutTime: Timestamp.fromDate(entryTimestamp),
                updatedAt: serverTimestamp()
              }, { merge: true });
              
              // Update worked hours if we have both clock-in and clock-out
              if (clockInTime) {
                await updateWorkedHours(editUserId, clockInTime, entryTimestamp);
              }
            }
          }
        } else {
          // Find pairs for historical entries
          if (editType === 'clockOut') {
            // Find the corresponding clock-in
            const historyQuery = query(
              collection(db, 'timeCardHistory'),
              where('userId', '==', editUserId),
              where('type', '==', 'clockIn'),
              orderBy('timestamp', 'desc')
            );
            
            const historySnapshot = await getDocs(historyQuery);
            let matchingClockIn = null;
            
            historySnapshot.forEach(doc => {
              const data = doc.data();
              const clockInTime = data.timestamp.toDate();
              
              if (clockInTime < entryTimestamp && !matchingClockIn) {
                matchingClockIn = clockInTime;
              }
            });
            
            // Update worked hours if found a matching clock-in
            if (matchingClockIn) {
              await updateWorkedHours(editUserId, matchingClockIn, entryTimestamp);
            }
          }
        }
        
        setEditSuccess('Entry created successfully.');
      }
      
      // Update aggregated time data
      await aggregateTeamTimeData();
      
      // Refresh the data
      const startOfDay = new Date(editDate);
      startOfDay.setHours(0, 0, 0, 0);
      
      const endOfDay = new Date(editDate);
      endOfDay.setHours(23, 59, 59, 999);
      
      const historyQuery = query(
        collection(db, 'timeCardHistory'),
        where('timestamp', '>=', Timestamp.fromDate(startOfDay)),
        where('timestamp', '<=', Timestamp.fromDate(endOfDay)),
        orderBy('timestamp')
      );
      
      const historySnapshot = await getDocs(historyQuery);
      const historyData = [];
      
      historySnapshot.forEach((doc) => {
        const data = doc.data();
        historyData.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate(),
          profile: userProfiles[data.userId] || {}
        });
      });
      
      setTimeCardHistory(historyData);
      
      // Reset form and close modal after a delay
      setTimeout(() => {
        setShowEditModal(false);
        setEditSuccess('');
      }, 1500);
      
    } catch (error) {
      console.error("Error saving time card entry:", error);
      setEditError("Failed to save entry. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Open bulk edit modal
  const openBulkEditModal = () => {
    setBulkEditEntries([
      {
        userId: '',
        date: new Date().toISOString().split('T')[0],
        clockInTime: '09:00',
        clockOutTime: '17:00'
      }
    ]);
    setShowBulkEditModal(true);
  };

  // Add another entry to bulk edit
  const addBulkEntry = () => {
    setBulkEditEntries([
      ...bulkEditEntries, 
      {
        userId: bulkEditEntries[bulkEditEntries.length - 1].userId || '',
        date: bulkEditEntries[bulkEditEntries.length - 1].date,
        clockInTime: '09:00',
        clockOutTime: '17:00'
      }
    ]);
  };

  // Update bulk entry at index
  const updateBulkEntry = (index, field, value) => {
    const newEntries = [...bulkEditEntries];
    newEntries[index] = {
      ...newEntries[index],
      [field]: value
    };
    setBulkEditEntries(newEntries);
  };

  // Remove bulk entry at index
  const removeBulkEntry = (index) => {
    if (bulkEditEntries.length <= 1) return;
    const newEntries = bulkEditEntries.filter((_, i) => i !== index);
    setBulkEditEntries(newEntries);
  };

  // Save bulk entries
  const saveBulkEntries = async () => {
    if (!isAdmin) return;
    
    try {
      setLoading(true);
      setEditError('');
      
      const db = getFirestore();
      let successCount = 0;
      
      // Process each entry
      for (const entry of bulkEditEntries) {
        if (!entry.userId || !entry.date || !entry.clockInTime || !entry.clockOutTime) {
          continue; // Skip incomplete entries
        }
        
        // Create clock in entry
        const [year, month, day] = entry.date.split('-').map(num => parseInt(num));
        const [inHours, inMinutes] = entry.clockInTime.split(':').map(num => parseInt(num));
        const [outHours, outMinutes] = entry.clockOutTime.split(':').map(num => parseInt(num));
        
        const clockInTimestamp = new Date(year, month - 1, day, inHours, inMinutes);
        const clockOutTimestamp = new Date(year, month - 1, day, outHours, outMinutes);
        const formattedDate = clockInTimestamp.toLocaleDateString();
        
        // Add clock in entry
        const clockInEntry = {
          userId: entry.userId,
          userName: userProfiles[entry.userId]?.displayName || 'Unknown User',
          userPhotoBase64: userProfiles[entry.userId]?.photoBase64 || null,
          date: formattedDate,
          type: 'clockIn',
          adminAction: true,
          adminId: currentUser.uid,
          adminName: userProfile?.displayName || currentUser.displayName || currentUser.email,
          timestamp: Timestamp.fromDate(clockInTimestamp),
          updatedAt: serverTimestamp()
        };
        
        const clockInRef = await addDoc(collection(db, 'timeCardHistory'), clockInEntry);
        console.log(`Added clock in entry for ${entry.userId} at ${clockInTimestamp.toLocaleString()}`);
        
        // Add clock out entry
        const clockOutEntry = {
          userId: entry.userId,
          userName: userProfiles[entry.userId]?.displayName || 'Unknown User',
          userPhotoBase64: userProfiles[entry.userId]?.photoBase64 || null,
          date: formattedDate,
          type: 'clockOut',
          adminAction: true,
          adminId: currentUser.uid,
          adminName: userProfile?.displayName || currentUser.displayName || currentUser.email,
          timestamp: Timestamp.fromDate(clockOutTimestamp),
          updatedAt: serverTimestamp()
        };
        
        const clockOutRef = await addDoc(collection(db, 'timeCardHistory'), clockOutEntry);
        console.log(`Added clock out entry for ${entry.userId} at ${clockOutTimestamp.toLocaleString()}`);
        
        // Update worked hours for this pair
        await updateWorkedHours(entry.userId, clockInTimestamp, clockOutTimestamp);
        
        // If this is today, update the timeCards document
        const today = new Date().toLocaleDateString();
        
        if (formattedDate === today) {
          const timeCardRef = doc(db, 'timeCards', entry.userId);
          
          await setDoc(timeCardRef, {
            userId: entry.userId,
            userName: userProfiles[entry.userId]?.displayName || 'Unknown User',
            userPhotoBase64: userProfiles[entry.userId]?.photoBase64 || null,
            currentDay: formattedDate,
            clockedIn: true,
            clockedOut: true,
            clockInTime: Timestamp.fromDate(clockInTimestamp),
            clockOutTime: Timestamp.fromDate(clockOutTimestamp),
            updatedAt: serverTimestamp()
          }, { merge: true });
        }
        
        successCount++;
      }
      
      // Update aggregated time data
      await aggregateTeamTimeData();
      
      // Refresh data if we're in the right view
      if (viewMode === 'history') {
        const startOfDay = new Date(selectedDate);
        startOfDay.setHours(0, 0, 0, 0);
        
        const endOfDay = new Date(selectedDate);
        endOfDay.setHours(23, 59, 59, 999);
        
        const historyQuery = query(
          collection(db, 'timeCardHistory'),
          where('timestamp', '>=', Timestamp.fromDate(startOfDay)),
          where('timestamp', '<=', Timestamp.fromDate(endOfDay)),
          orderBy('timestamp')
        );
        
        const historySnapshot = await getDocs(historyQuery);
        const historyData = [];
        
        historySnapshot.forEach((doc) => {
          const data = doc.data();
          historyData.push({
            id: doc.id,
            ...data,
            timestamp: data.timestamp?.toDate(),
            profile: userProfiles[data.userId] || {}
          });
        });
        
        setTimeCardHistory(historyData);
      }
      
      setEditSuccess(`Successfully added ${successCount} time card entries.`);
      
      // Close modal after a delay
      setTimeout(() => {
        setShowBulkEditModal(false);
        setEditSuccess('');
      }, 1500);
      
    } catch (error) {
      console.error("Error saving bulk entries:", error);
      setEditError("Failed to save entries. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 shadow-md border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">
              Time Cards
            </h1>
            <div className="flex space-x-2">
              <button
                onClick={() => navigate('/inspection')}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105"
              >
                Go to Inspection
              </button>
              <button
                onClick={() => navigate('/dashboard')}
                className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Current Time Banner */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-700 shadow-md mb-6 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <div className="flex items-center mb-3 sm:mb-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-300 font-mono">{formatDate(currentTime)} {formatTime(currentTime)}</span>
            </div>
            
            <div className="flex items-center">
              <div className="text-sm text-gray-400 mr-2">
                Currently viewing: 
              </div>
              <span className="px-3 py-1 bg-blue-900 rounded-full text-blue-200 text-sm">
                {viewMode === 'active' ? 'Active Users' : viewMode === 'history' ? 'Daily History' : 'Time Reports'}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* User Clock In/Out Panel */}
        <div className="mb-6 bg-gradient-to-br from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700">
          <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">
            Your Time Card
          </h2>
          
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div className="w-full md:w-1/2 flex items-center">
              {userProfile?.photoBase64 ? (
                <div className="h-16 w-16 rounded-full overflow-hidden mr-4 border-2 border-blue-500">
                  <img src={userProfile.photoBase64} alt="User" className="h-full w-full object-cover" />
                </div>
              ) : (
                <div className="h-16 w-16 rounded-full bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
              )}
              
              <div>
                <h3 className="text-lg font-medium text-white">
                  {userProfile?.displayName || currentUser?.displayName || currentUser?.email || 'User'}
                </h3>
                <div className="flex items-center mt-1">
                  <span className={`inline-flex h-2 w-2 rounded-full ${isClockedIn ? 'bg-green-500' : 'bg-red-500'} mr-2`}>
                    {isClockedIn && <span className="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-green-400 opacity-75"></span>}
                  </span>
                  <span className={`text-sm ${isClockedIn ? 'text-green-400' : 'text-red-400'}`}>
                    {isClockedIn ? 'Currently Clocked In' : 'Not Clocked In'}
                  </span>
                </div>
                {isClockedIn && userClockInTime && (
                  <div className="mt-1 text-sm text-gray-300">
                    <span className="font-medium">Since:</span> {formatTime(userClockInTime)} ({elapsedTime})
                  </div>
                )}
              </div>
            </div>
            
            <div className="w-full md:w-1/2 flex justify-center md:justify-end">
              {!isClockedIn ? (
                <button
                  onClick={clockInUser}
                  disabled={loading}
                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white px-8 py-3 rounded-md shadow-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center text-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <svg className="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )}
                  Clock In
                </button>
              ) : (
                <button
                  onClick={clockOutUser}
                  disabled={loading}
                  className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-8 py-3 rounded-md shadow-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center text-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <svg className="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )}
                  Clock Out
                </button>
              )}
            </div>
          </div>
          
          {error && (
            <div className="mt-4 bg-red-900 bg-opacity-50 text-red-200 p-3 rounded-lg border border-red-700">
              <p className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                {error}
              </p>
            </div>
          )}
        </div>
        
        {/* View mode tabs */}
        <div className="mb-6 flex flex-wrap justify-center">
          <div className="bg-gray-800 rounded-lg p-1 inline-flex shadow-lg">
            {/* Only show Active Users tab for admins */}
            {isAdmin && (
              <button
                onClick={() => setViewMode('active')}
                className={`px-4 py-2 rounded-md transition-all duration-300 ${
                  viewMode === 'active'
                    ? 'bg-gradient-to-r from-green-600 to-green-700 text-white shadow-inner'
                    : 'text-gray-400 hover:bg-gray-700 hover:text-white'
                }`}
              >
                Active Users
              </button>
            )}
            <button
              onClick={() => setViewMode('history')}
              className={`px-4 py-2 rounded-md transition-all duration-300 ${
                viewMode === 'history'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-inner'
                  : 'text-gray-400 hover:bg-gray-700 hover:text-white'
              }`}
            >
              Daily History
            </button>
            {isAdmin && (
              <button
                onClick={() => setViewMode('reports')}
                className={`px-4 py-2 rounded-md transition-all duration-300 ${
                  viewMode === 'reports'
                    ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-inner'
                    : 'text-gray-400 hover:bg-gray-700 hover:text-white'
                }`}
              >
                Time Reports
              </button>
            )}
          </div>
        </div>
        
        {/* Admin Edit Buttons */}
        {isAdmin && (
          <div className="mb-6 flex flex-wrap justify-center space-x-2">
            <button
              onClick={() => openNewEntryModal()}
              className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-500 hover:to-yellow-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Single Time Entry
            </button>
            <button
              onClick={openBulkEditModal}
              className="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
              Bulk Add Time Entries
            </button>
          </div>
        )}
        
        {/* Active Users View - Only visible to admins */}
        {viewMode === 'active' && isAdmin && (
          <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-400 mb-6">
              Currently Clocked In Users
            </h2>
            
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin h-12 w-12 border-t-2 border-b-2 border-green-500 rounded-full"></div>
              </div>
            ) : activeUsers.length === 0 ? (
              <div className="bg-gray-900 p-8 rounded-lg text-center border border-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-gray-400">No users currently clocked in</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-gray-900 rounded-lg overflow-hidden">
                  <thead className="bg-gradient-to-r from-gray-800 to-gray-700 text-gray-300">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">User</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Clock In Time</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Duration</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-800">
                    {activeUsers.map((user) => (
                      <tr 
                        key={user.id} 
                        className="hover:bg-gray-800 transition-colors cursor-pointer"
                        onClick={() => viewUserDetails(user)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {user.userPhotoBase64 || user.profile?.photoBase64 ? (
                              <div className="h-10 w-10 rounded-full overflow-hidden mr-3 border border-gray-600">
                                <img 
                                  src={user.userPhotoBase64 || user.profile?.photoBase64} 
                                  alt={user.userName} 
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-green-800 flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                              </div>
                            )}
                            <div>
                              <div className="font-medium text-white">{user.userName || user.profile?.displayName || 'Unknown User'}</div>
                              {user.profile?.jobTitle && (
                                <div className="text-sm text-gray-400">{user.profile.jobTitle}</div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {user.clockInTime ? (
                            <div>
                              <div>{user.clockInTime.toLocaleDateString()}</div>
                              <div className="text-green-400 font-mono">{user.clockInTime.toLocaleTimeString()}</div>
                            </div>
                          ) : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <div className="text-green-400 font-mono text-lg">
                            {calculateElapsedTime(user.clockInTime)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-900 text-green-300 items-center">
                            <span className="h-2 w-2 bg-green-400 rounded-full mr-1 animate-ping absolute"></span>
                            <span className="h-2 w-2 bg-green-400 rounded-full mr-1 relative"></span>
                            Active
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              adminClockOutUser(user.id);
                            }}
                            className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-3 py-1 rounded shadow-md transition-all duration-300 transform hover:scale-105"
                          >
                            Clock Out
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            
            <div className="mt-6 bg-gray-900 p-4 rounded-lg border border-gray-700">
              <h3 className="text-lg font-medium text-blue-300 mb-3">Admin Actions</h3>
              <p className="text-gray-400 mb-3">As an administrator, you can manually clock in/out users:</p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-end">
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">Select User</label>
                  <select
                    className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onChange={(e) => setFilterUser(e.target.value)}
                    value={filterUser}
                  >
                    <option value="">Select a user...</option>
                    {Object.entries(userProfiles).map(([userId, profile]) => (
                      <option key={userId} value={userId}>
                        {profile.displayName || profile.email || userId}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={() => filterUser && adminClockInUser(filterUser)}
                    disabled={!filterUser || loading}
                    className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 flex-grow disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Clock In User
                  </button>
                  <button
                    onClick={() => filterUser && adminClockOutUser(filterUser)}
                    disabled={!filterUser || loading}
                    className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 flex-grow disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Clock Out User
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Daily History View */}
        {viewMode === 'history' && (
          <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-6">
              Daily Time Card History
            </h2>
            
            {/* Date Selector */}
            <div className="mb-6 bg-gray-900 p-4 rounded-lg border border-gray-700">
              <div className="flex flex-col sm:flex-row items-center justify-between">
                <div className="mb-3 sm:mb-0">
                  <label className="block text-gray-400 mb-2 text-sm">Select Date</label>
                  <input
                    type="date"
                    className="bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                  />
                </div>
                
                <div className="text-right">
                  <button
                    onClick={() => {
                      const date = new Date();
                      setSelectedDate(date.toISOString().split('T')[0]);
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded shadow-md transition-colors"
                  >
                    Today
                  </button>
                </div>
              </div>
            </div>
            
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin h-12 w-12 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
              </div>
            ) : timeCardHistory.length === 0 ? (
              <div className="bg-gray-900 p-8 rounded-lg text-center border border-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-gray-400">No time card history found for the selected date</p>
                
                {/* Add time entry button for empty days */}
                {isAdmin && (
                  <div className="mt-4">
                    <button
                      onClick={() => openNewEntryModal()}
                      className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-500 hover:to-yellow-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 flex items-center mx-auto"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add Time Entry for this Day
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-gray-900 rounded-lg overflow-hidden">
                  <thead className="bg-gradient-to-r from-gray-800 to-gray-700 text-gray-300">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">User</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Time</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Action</th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Details</th>
                      {isAdmin && (
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Admin</th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-800">
                    {timeCardHistory.map((entry) => (
                      <tr 
                        key={entry.id} 
                        className={`hover:bg-gray-800 transition-colors cursor-pointer ${
                          entry.type === 'clockIn' ? 'border-l-4 border-green-600' : 'border-l-4 border-red-600'
                        }`}
                        onClick={() => viewUserDetails({
                          ...entry,
                          id: entry.userId,
                          profile: userProfiles[entry.userId] || {}
                        })}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {entry.userPhotoBase64 || entry.profile?.photoBase64 ? (
                              <div className="h-10 w-10 rounded-full overflow-hidden mr-3 border border-gray-600">
                                <img 
                                  src={entry.userPhotoBase64 || entry.profile?.photoBase64} 
                                  alt={entry.userName} 
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                              </div>
                            )}
                            <div>
                              <div className="font-medium text-white">{entry.userName || entry.profile?.displayName || 'Unknown User'}</div>
                              {entry.profile?.jobTitle && (
                                <div className="text-sm text-gray-400">{entry.profile.jobTitle}</div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {entry.timestamp ? (
                            <div className="font-mono">
                              {entry.timestamp.toLocaleTimeString()}
                            </div>
                          ) : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full items-center ${
                            entry.type === 'clockIn'
                              ? 'bg-green-900 text-green-300'
                              : 'bg-red-900 text-red-300'
                          }`}>
                            <span className={`h-2 w-2 ${entry.type === 'clockIn' ? 'bg-green-400' : 'bg-red-400'} rounded-full mr-1`}></span>
                            {entry.type === 'clockIn' ? 'Clock In' : 'Clock Out'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                          {entry.adminAction && (
                            <div className="flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                              </svg>
                              <span>Admin: {entry.adminName}</span>
                            </div>
                          )}
                        </td>
                        {isAdmin && (
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <div className="flex space-x-2">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  openEditEntryModal(entry);
                                }}
                                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-2 py-1 rounded shadow-md transition-all duration-300"
                              >
                                Edit
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (window.confirm("Are you sure you want to delete this entry?")) {
                                    deleteTimeCardEntry(entry.id);
                                  }
                                }}
                                className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-2 py-1 rounded shadow-md transition-all duration-300"
                              >
                                Delete
                              </button>
                            </div>
                          </td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            
            {/* Summary Table */}
            {timeCardHistory.length > 0 && (
              <div className="mt-8">
                <h3 className="text-lg font-medium text-blue-300 mb-3">Daily Summary</h3>
                <div className="bg-gray-900 rounded-lg overflow-hidden">
                  <table className="min-w-full">
                    <thead className="bg-gradient-to-r from-gray-800 to-gray-700 text-gray-300">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">User</th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Clock Ins</th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Clock Outs</th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Hours Worked</th>
                        {isAdmin && (
                          <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                        )}
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-800">
                      {/* Group entries by user */}
                      {Array.from(new Set(timeCardHistory.map(entry => entry.userId))).map(userId => {
                        const userEntries = timeCardHistory.filter(entry => entry.userId === userId);
                        const clockIns = userEntries.filter(entry => entry.type === 'clockIn').length;
                        const clockOuts = userEntries.filter(entry => entry.type === 'clockOut').length;
                        const userName = userEntries[0]?.userName || userProfiles[userId]?.displayName || 'Unknown User';
                        const hoursWorked = calculateHoursWorked(userId, selectedDate);
                        
                        return (
                          <tr key={userId} className="hover:bg-gray-800 transition-colors">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                {userProfiles[userId]?.photoBase64 ? (
                                  <div className="h-8 w-8 rounded-full overflow-hidden mr-3 border border-gray-600">
                                    <img 
                                      src={userProfiles[userId].photoBase64} 
                                      alt={userName} 
                                      className="h-full w-full object-cover"
                                    />
                                  </div>
                                ) : (
                                  <div className="h-8 w-8 rounded-full bg-gray-700 flex items-center justify-center mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                  </div>
                                )}
                                <span className="text-white">{userName}</span>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <span className="bg-green-900 text-green-200 px-2 py-1 rounded-full">{clockIns}</span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <span className="bg-red-900 text-red-200 px-2 py-1 rounded-full">{clockOuts}</span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-300">
                              {formatDuration(hoursWorked)}
                            </td>
                            {isAdmin && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                <div className="flex space-x-2">
                                  <button
                                    onClick={() => openNewEntryModal(userId)}
                                    className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-500 hover:to-yellow-600 text-white px-2 py-1 rounded shadow-md transition-all duration-300"
                                  >
                                    Add Entry
                                  </button>
                                  {clockIns !== clockOuts && (
                                    <button
                                      onClick={() => {
                                        // Auto-create missing clock in/out
                                        const newEntry = {
                                          userId,
                                          userName,
                                          profile: userProfiles[userId] || {},
                                          type: clockIns > clockOuts ? 'clockOut' : 'clockIn',
                                          timestamp: new Date(selectedDate + (clockIns > clockOuts ? 'T17:00:00' : 'T09:00:00'))
                                        };
                                        openEditEntryModal(newEntry);
                                      }}
                                      className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white px-2 py-1 rounded shadow-md transition-all duration-300"
                                    >
                                      Fix Missing {clockIns > clockOuts ? 'Clock Out' : 'Clock In'}
                                    </button>
                                  )}
                                </div>
                              </td>
                            )}
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Time Reports View (Admin Only) */}
        {viewMode === 'reports' && isAdmin && (
          <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-purple-300 to-blue-400 mb-6">
              Time Card Reports
            </h2>
            
            {/* Filters */}
            <div className="mb-6 bg-gray-900 p-4 rounded-lg border border-gray-700">
              <h3 className="text-lg font-medium text-purple-300 mb-3">Filter Options</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">User</label>
                  <select
                    className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    value={filterUser}
                    onChange={(e) => setFilterUser(e.target.value)}
                  >
                    <option value="">All Users</option>
                    {Object.entries(userProfiles).map(([userId, profile]) => (
                      <option key={userId} value={userId}>
                        {profile.displayName || profile.email || userId}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">Date Range</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="date"
                      className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="Start Date"
                      value={filterStartDate}
                      onChange={(e) => setFilterStartDate(e.target.value)}
                    />
                    <input
                      type="date"
                      className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="End Date"
                      value={filterEndDate}
                      onChange={(e) => setFilterEndDate(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={applyFilters}
                    className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 flex-grow"
                  >
                    Apply Filters
                  </button>
                  {isFiltering && (
                    <button
                      onClick={clearFilters}
                      className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300"
                    >
                      Clear
                    </button>
                  )}
                </div>
              </div>
              
              {isFiltering && (
                <div className="mt-2 bg-purple-900 bg-opacity-30 p-2 rounded border border-purple-800 text-purple-200 text-sm">
                  <span>Filtered: </span>
                  {filterUser && (
                    <span className="mr-2">User: {userProfiles[filterUser]?.displayName || filterUser}</span>
                  )}
                  {filterStartDate && filterEndDate && (
                    <span>Date Range: {new Date(filterStartDate).toLocaleDateString()} to {new Date(filterEndDate).toLocaleDateString()}</span>
                  )}
                </div>
              )}
            </div>
            
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin h-12 w-12 border-t-2 border-b-2 border-purple-500 rounded-full"></div>
              </div>
            ) : timeCardHistory.length === 0 ? (
              <div className="bg-gray-900 p-8 rounded-lg text-center border border-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p className="text-gray-400">No time card data found for the selected filters</p>
                
                <div className="mt-4">
                  <button
                    onClick={openBulkEditModal}
                    className="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 flex items-center mx-auto"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                    </svg>
                    Bulk Add Time Entries
                  </button>
                </div>
              </div>
            ) : (
              <>
                {/* Summary by User */}
                <div className="bg-gray-900 rounded-lg overflow-hidden mb-8">
                  <div className="p-4 bg-gradient-to-r from-purple-900 to-gray-800 border-b border-gray-700">
                    <h3 className="text-lg font-medium text-white">Summary by User</h3>
                  </div>
                  
                  <div className="p-4">
                    <table className="min-w-full">
                      <thead className="bg-gray-800 text-gray-300">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">User</th>
                          <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Total Days</th>
                          <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Total Hours</th>
                          <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Avg Hours/Day</th>
                          <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-800">
                        {/* Group data by user */}
                        {Array.from(new Set(timeCardHistory.map(entry => entry.userId))).map(userId => {
                          const userEntries = timeCardHistory.filter(entry => entry.userId === userId);
                          const userName = userEntries[0]?.userName || userProfiles[userId]?.displayName || 'Unknown User';
                          
                          // Calculate total hours
                          const totalMilliseconds = calculateTotalHours(userId);
                          const totalHours = totalMilliseconds / (1000 * 60 * 60);
                          
                          // Calculate unique days
                          const uniqueDays = new Set(userEntries.map(entry => 
                            entry.timestamp.toDateString()
                          )).size;
                          
                          // Calculate average hours per day
                          const avgHoursPerDay = uniqueDays > 0 ? totalHours / uniqueDays : 0;
                          
                          // Check for missing pairs (unpaired clock ins/outs)
                          const uniqueDates = [...new Set(userEntries.map(entry => 
                            entry.timestamp.toDateString()
                          ))];
                          
                          let hasMissingPairs = false;
                          uniqueDates.forEach(date => {
                            const dateEntries = userEntries.filter(entry => 
                              entry.timestamp.toDateString() === date
                            );
                            const dateClockIns = dateEntries.filter(entry => entry.type === 'clockIn').length;
                            const dateClockOuts = dateEntries.filter(entry => entry.type === 'clockOut').length;
                            
                            if (dateClockIns !== dateClockOuts) {
                              hasMissingPairs = true;
                            }
                          });
                          
                          return (
                            <tr key={userId} className="hover:bg-gray-800 transition-colors">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  {userProfiles[userId]?.photoBase64 ? (
                                    <div className="h-8 w-8 rounded-full overflow-hidden mr-3 border border-gray-600">
                                      <img 
                                        src={userProfiles[userId].photoBase64} 
                                        alt={userName} 
                                        className="h-full w-full object-cover"
                                      />
                                    </div>
                                  ) : (
                                    <div className="h-8 w-8 rounded-full bg-purple-900 flex items-center justify-center mr-3">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                      </svg>
                                    </div>
                                  )}
                                  <div>
                                    <div className="font-medium text-white">{userName}</div>
                                    {userProfiles[userId]?.jobTitle && (
                                      <div className="text-xs text-gray-400">{userProfiles[userId].jobTitle}</div>
                                    )}
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                <span className="bg-gray-800 text-purple-300 px-3 py-1 rounded-full">
                                  {uniqueDays}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-purple-300 font-medium">
                                {totalHours.toFixed(2)} hours
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-green-300 font-medium">
                                {avgHoursPerDay.toFixed(2)} hours
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                <div className="flex space-x-2">
                                  <button
                                    onClick={() => openNewEntryModal(userId)}
                                    className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-500 hover:to-yellow-600 text-white px-2 py-1 rounded shadow-md transition-all duration-300"
                                  >
                                    Add Entry
                                  </button>
                                  
                                  {hasMissingPairs && (
                                    <button
                                      onClick={() => {
                                        // Open bulk edit modal with this user pre-selected
                                        setBulkEditEntries([
                                          {
                                            userId,
                                            date: filterStartDate || new Date().toISOString().split('T')[0],
                                            clockInTime: '09:00',
                                            clockOutTime: '17:00'
                                          }
                                        ]);
                                        setShowBulkEditModal(true);
                                      }}
                                      className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-2 py-1 rounded shadow-md transition-all duration-300"
                                    >
                                      Fix Missing Pairs
                                    </button>
                                  )}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
                
                {/* Detailed Time Log */}
                <div className="overflow-x-auto bg-gray-900 rounded-lg overflow-hidden">
                  <div className="p-4 bg-gradient-to-r from-gray-800 to-purple-900 border-b border-gray-700">
                    <h3 className="text-lg font-medium text-white">Detailed Time Log</h3>
                  </div>
                  
                  <table className="min-w-full">
                    <thead className="bg-gray-800 text-gray-300">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">User</th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Time</th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Action</th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Notes</th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Admin</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-800">
                      {timeCardHistory.map((entry) => (
                        <tr 
                          key={entry.id} 
                          className={`hover:bg-gray-800 transition-colors ${
                            entry.type === 'clockIn' ? 'border-l-2 border-green-600' : 'border-l-2 border-red-600'
                          }`}
                        >
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {entry.timestamp ? entry.timestamp.toLocaleDateString() : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {entry.userPhotoBase64 || userProfiles[entry.userId]?.photoBase64 ? (
                                <div className="h-8 w-8 rounded-full overflow-hidden mr-2 border border-gray-600">
                                  <img 
                                    src={entry.userPhotoBase64 || userProfiles[entry.userId]?.photoBase64} 
                                    alt={entry.userName} 
                                    className="h-full w-full object-cover"
                                  />
                                </div>
                              ) : (
                                <div className="h-8 w-8 rounded-full bg-gray-700 flex items-center justify-center mr-2">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                  </svg>
                                </div>
                              )}
                              <span className="text-sm text-white">{entry.userName}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-300">
                            {entry.timestamp ? entry.timestamp.toLocaleTimeString() : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full items-center ${
                              entry.type === 'clockIn'
                                ? 'bg-green-900 text-green-300'
                                : 'bg-red-900 text-red-300'
                            }`}>
                              <span className={`h-2 w-2 ${entry.type === 'clockIn' ? 'bg-green-400' : 'bg-red-400'} rounded-full mr-1`}></span>
                              {entry.type === 'clockIn' ? 'Clock In' : 'Clock Out'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                            {entry.adminAction && (
                              <div className="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                <span>Admin: {entry.adminName}</span>
                              </div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => openEditEntryModal(entry)}
                                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-2 py-1 rounded shadow-md transition-all duration-300"
                              >
                                Edit
                              </button>
                              <button
                                onClick={() => {
                                  if (window.confirm("Are you sure you want to delete this entry?")) {
                                    deleteTimeCardEntry(entry.id);
                                  }
                                }}
                                className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-2 py-1 rounded shadow-md transition-all duration-300"
                              >
                                Delete
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            )}
          </div>
        )}
      </div>
      
      {/* User Details Modal */}
      {showUserModal && selectedUser && (
        <div className="fixed inset-0 overflow-y-auto z-50 flex items-center justify-center px-4">
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" onClick={closeUserModal}></div>
          
          <div className="relative bg-gray-900 rounded-lg max-w-md w-full overflow-hidden shadow-xl border border-gray-700 transform transition-all">
            <div className="bg-gradient-to-r from-blue-900 to-gray-800 p-4 border-b border-gray-700">
              <h3 className="text-lg font-medium text-white">User Time Card Details</h3>
            </div>
            
            <div className="p-6">
              <div className="flex flex-col items-center mb-6">
                {selectedUser.userPhotoBase64 || selectedUser.profile?.photoBase64 ? (
                  <div className="h-20 w-20 rounded-full overflow-hidden border-2 border-blue-500 shadow-lg mb-3">
                    <img 
                      src={selectedUser.userPhotoBase64 || selectedUser.profile?.photoBase64} 
                      alt={selectedUser.userName} 
                      className="h-full w-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="h-20 w-20 rounded-full bg-blue-900 flex items-center justify-center mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                )}
                
                <h4 className="text-xl font-semibold text-white">
                  {selectedUser.userName || selectedUser.profile?.displayName || 'Unknown User'}
                </h4>
                
                {selectedUser.profile?.jobTitle && (
                  <p className="text-gray-400">{selectedUser.profile.jobTitle}</p>
                )}
                
                {/* If viewing from active users, show active status */}
                {selectedUser.clockedIn && !selectedUser.clockedOut && (
                  <div className="mt-2 flex items-center bg-green-900 bg-opacity-25 px-3 py-1 rounded-full">
                    <span className="relative flex h-3 w-3 mr-2">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-3 w-3 bg-green-500"></span>
                    </span>
                    <span className="text-green-300">Currently Active</span>
                  </div>
                )}
                
                {/* Time details */}
                <div className="w-full mt-4 space-y-2 bg-gray-800 rounded-lg p-4">
                  {selectedUser.clockInTime && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Clock In:</span>
                      <span className="text-blue-300 font-mono">
                        {selectedUser.clockInTime.toLocaleTimeString()}
                      </span>
                    </div>
                  )}
                  
                  {selectedUser.timestamp && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Time:</span>
                      <span className="text-blue-300 font-mono">
                        {selectedUser.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                  )}
                  
                  {selectedUser.clockOutTime && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Clock Out:</span>
                      <span className="text-red-300 font-mono">
                        {selectedUser.clockOutTime.toLocaleTimeString()}
                      </span>
                    </div>
)}
                  
                  {selectedUser.clockInTime && selectedUser.clockOutTime && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Duration:</span>
                      <span className="text-purple-300 font-mono">
                        {calculateElapsedTime(selectedUser.clockInTime, selectedUser.clockOutTime)}
                      </span>
                    </div>
                  )}
                  
                  {selectedUser.type && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Action:</span>
                      <span className={`${selectedUser.type === 'clockIn' ? 'text-green-300' : 'text-red-300'}`}>
                        {selectedUser.type === 'clockIn' ? 'Clock In' : 'Clock Out'}
                      </span>
                    </div>
                  )}
                </div>
                
                {/* Admin Actions */}
                {isAdmin && selectedUser.clockedIn && !selectedUser.clockedOut && (
                  <div className="mt-4 w-full">
                    <button 
                      onClick={() => {
                        adminClockOutUser(selectedUser.id);
                        closeUserModal();
                      }}
                      className="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white py-2 rounded-md shadow-md transition-all duration-300"
                    >
                      Clock Out User
                    </button>
                  </div>
                )}
                
                {isAdmin && (!selectedUser.clockedIn || selectedUser.clockedOut) && (
                  <div className="mt-4 w-full">
                    <button 
                      onClick={() => {
                        adminClockInUser(selectedUser.id);
                        closeUserModal();
                      }}
                      className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white py-2 rounded-md shadow-md transition-all duration-300"
                    >
                      Clock In User
                    </button>
                  </div>
                )}
                
                {/* Edit Button for Admins */}
                {isAdmin && selectedUser.id && selectedUser.timestamp && (
                  <div className="mt-4 w-full">
                    <button 
                      onClick={() => {
                        closeUserModal();
                        openEditEntryModal(selectedUser);
                      }}
                      className="w-full bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-500 hover:to-yellow-600 text-white py-2 rounded-md shadow-md transition-all duration-300"
                    >
                      Edit Time Entry
                    </button>
                  </div>
                )}
              </div>
              
              <div className="flex justify-center">
                <button
                  onClick={closeUserModal}
                  className="bg-gradient-to-r from-gray-700 to-gray-600 hover:from-gray-600 hover:to-gray-500 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Edit Time Entry Modal (Admin Only) */}
      {showEditModal && isAdmin && (
        <div className="fixed inset-0 overflow-y-auto z-50 flex items-center justify-center px-4">
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" onClick={() => setShowEditModal(false)}></div>
          
          <div className="relative bg-gray-900 rounded-lg max-w-md w-full overflow-hidden shadow-xl border border-gray-700 transform transition-all">
            <div className="bg-gradient-to-r from-yellow-800 to-gray-800 p-4 border-b border-gray-700">
              <h3 className="text-lg font-medium text-white">
                {editEntryId ? 'Edit Time Entry' : 'Add New Time Entry'}
              </h3>
            </div>
            
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">User</label>
                  <select
                    className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    value={editUserId}
                    onChange={(e) => setEditUserId(e.target.value)}
                    disabled={editEntryId !== null}
                  >
                    <option value="">Select a user...</option>
                    {Object.entries(userProfiles).map(([userId, profile]) => (
                      <option key={userId} value={userId}>
                        {profile.displayName || profile.email || userId}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">Date</label>
                  <input
                    type="date"
                    className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    value={editDate}
                    onChange={(e) => setEditDate(e.target.value)}
                  />
                </div>
                
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">Time</label>
                  <input
                    type="time"
                    className="w-full bg-gray-800 text-white rounded-md border border-gray-700 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    value={editTime}
                    onChange={(e) => setEditTime(e.target.value)}
                  />
                </div>
                
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">Action Type</label>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      className={`flex-1 py-2 rounded-md ${
                        editType === 'clockIn' 
                          ? 'bg-green-800 text-green-200' 
                          : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
                      }`}
                      onClick={() => setEditType('clockIn')}
                    >
                      Clock In
                    </button>
                    <button
                      type="button"
                      className={`flex-1 py-2 rounded-md ${
                        editType === 'clockOut' 
                          ? 'bg-red-800 text-red-200' 
                          : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
                      }`}
                      onClick={() => setEditType('clockOut')}
                    >
                      Clock Out
                    </button>
                  </div>
                </div>
                
                {editError && (
                  <div className="bg-red-900 bg-opacity-50 text-red-200 p-3 rounded-lg border border-red-700">
                    <p className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      {editError}
                    </p>
                  </div>
                )}
                
                {editSuccess && (
                  <div className="bg-green-900 bg-opacity-50 text-green-200 p-3 rounded-lg border border-green-700">
                    <p className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {editSuccess}
                    </p>
                  </div>
                )}
                
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={saveTimeCardEntry}
                    disabled={loading}
                    className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-500 hover:to-yellow-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 flex-grow disabled:opacity-50"
                  >
                    {loading ? (
                      <svg className="animate-spin h-5 w-5 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : editEntryId ? 'Update Entry' : 'Add Entry'}
                  </button>
                  
                  {editEntryId && (
                    <button
                      onClick={() => {
                        if (window.confirm("Are you sure you want to delete this entry?")) {
                          deleteTimeCardEntry(editEntryId);
                        }
                      }}
                      disabled={loading}
                      className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 disabled:opacity-50"
                    >
                      Delete
                    </button>
                  )}
                  
                  <button
                    onClick={() => setShowEditModal(false)}
                    className="bg-gradient-to-r from-gray-700 to-gray-600 hover:from-gray-600 hover:to-gray-500 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Bulk Edit Modal (Admin Only) */}
      {showBulkEditModal && isAdmin && (
        <div className="fixed inset-0 overflow-y-auto z-50 flex items-center justify-center px-4">
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" onClick={() => setShowBulkEditModal(false)}></div>
          
          <div className="relative bg-gray-900 rounded-lg max-w-3xl w-full overflow-hidden shadow-xl border border-gray-700 transform transition-all">
            <div className="bg-gradient-to-r from-indigo-800 to-gray-800 p-4 border-b border-gray-700">
              <h3 className="text-lg font-medium text-white">
                Bulk Add Time Entries
              </h3>
            </div>
            
            <div className="p-6 max-h-[80vh] overflow-y-auto">
              <p className="text-gray-300 mb-4">
                Add multiple time entries at once. This is useful for creating missed days or fixing missing punches.
              </p>
              
              {bulkEditEntries.map((entry, index) => (
                <div key={index} className="bg-gray-800 rounded-lg p-4 mb-4 border border-gray-700">
                  <div className="flex justify-between items-center mb-3">
                    <h4 className="text-white font-medium">Entry #{index + 1}</h4>
                    {bulkEditEntries.length > 1 && (
                      <button
                        onClick={() => removeBulkEntry(index)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-gray-400 mb-2 text-sm">User</label>
                      <select
                        className="w-full bg-gray-700 text-white rounded-md border border-gray-600 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        value={entry.userId}
                        onChange={(e) => updateBulkEntry(index, 'userId', e.target.value)}
                      >
                        <option value="">Select a user...</option>
                        {Object.entries(userProfiles).map(([userId, profile]) => (
                          <option key={userId} value={userId}>
                            {profile.displayName || profile.email || userId}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-gray-400 mb-2 text-sm">Date</label>
                      <input type="date"
                        className="w-full bg-gray-700 text-white rounded-md border border-gray-600 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        value={entry.date}
                        onChange={(e) => updateBulkEntry(index, 'date', e.target.value)}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-gray-400 mb-2 text-sm">Clock In Time</label>
                      <input
                        type="time"
                        className="w-full bg-gray-700 text-white rounded-md border border-gray-600 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        value={entry.clockInTime}
                        onChange={(e) => updateBulkEntry(index, 'clockInTime', e.target.value)}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-gray-400 mb-2 text-sm">Clock Out Time</label>
                      <input
                        type="time"
                        className="w-full bg-gray-700 text-white rounded-md border border-gray-600 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        value={entry.clockOutTime}
                        onChange={(e) => updateBulkEntry(index, 'clockOutTime', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              ))}
              
              <div className="mt-2 mb-4">
                <button
                  onClick={addBulkEntry}
                  className="bg-indigo-700 hover:bg-indigo-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Another Entry
                </button>
              </div>
              
              {editError && (
                <div className="bg-red-900 bg-opacity-50 text-red-200 p-3 rounded-lg border border-red-700 mb-4">
                  <p className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    {editError}
                  </p>
                </div>
              )}
              
              {editSuccess && (
                <div className="bg-green-900 bg-opacity-50 text-green-200 p-3 rounded-lg border border-green-700 mb-4">
                  <p className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {editSuccess}
                  </p>
                </div>
              )}
              
              <div className="flex space-x-3 pt-4 border-t border-gray-700">
                <button
                  onClick={saveBulkEntries}
                  disabled={loading}
                  className="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 flex-grow disabled:opacity-50"
                >
                  {loading ? (
                    <svg className="animate-spin h-5 w-5 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : 'Save All Entries'}
                </button>
                
                <button
                  onClick={() => setShowBulkEditModal(false)}
                  className="bg-gradient-to-r from-gray-700 to-gray-600 hover:from-gray-600 hover:to-gray-500 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Missing pairs identification and fixing modal */}
      {isAdmin && (
        <div className="fixed bottom-4 right-4 z-40">
          <button
            onClick={() => {
              // Count missing pairs
              const uniqueUsers = new Set(timeCardHistory.map(entry => entry.userId));
              let missingPairsCount = 0;
              
              uniqueUsers.forEach(userId => {
                const userEntries = timeCardHistory.filter(entry => entry.userId === userId);
                const dates = new Set(userEntries.map(entry => entry.timestamp.toDateString()));
                
                dates.forEach(date => {
                  const dateEntries = userEntries.filter(entry => entry.timestamp.toDateString() === date);
                  const clockIns = dateEntries.filter(entry => entry.type === 'clockIn').length;
                  const clockOuts = dateEntries.filter(entry => entry.type === 'clockOut').length;
                  
                  if (clockIns !== clockOuts) {
                    missingPairsCount++;
                  }
                });
              });
              
              if (missingPairsCount > 0) {
                openBulkEditModal();
              } else {
                alert('No missing punch pairs detected in the current view.');
              }
            }}
            className="bg-gradient-to-r from-yellow-600 to-red-600 hover:from-yellow-500 hover:to-red-500 text-white px-4 py-3 rounded-full shadow-lg transition-all duration-300 flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            Fix Missing Punches
          </button>
        </div>
      )}
    </div>
  );
};

export default TimeCard;