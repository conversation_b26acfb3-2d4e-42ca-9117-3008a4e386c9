import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet.markercluster/dist/leaflet.markercluster.js';
import 'leaflet.markercluster/dist/MarkerCluster.css';
import 'leaflet.markercluster/dist/MarkerCluster.Default.css';
import 'leaflet-routing-machine';
import 'leaflet-routing-machine/dist/leaflet-routing-machine.css';

// Route request cache and rate limiting
const ROUTE_REQUEST_CACHE = new Map();
const PENDING_REQUESTS = new Set();
const LAST_REQUEST_TIME = { time: 0 };
let MIN_REQUEST_INTERVAL = 1000;
const MAX_CACHE_SIZE = 50;

// Enhanced Leaflet Path patch
const applyLeafletPathPatch = () => {
  if (typeof L === 'undefined' || !L.Path || !L.Path.prototype) return;

  if (!window._leafletPathRegistry) {
    window._leafletPathRegistry = new WeakMap();
  }

  const originalInitPath = L.Path.prototype._initPath;
  L.Path.prototype._initPath = function () {
    try {
      originalInitPath.call(this);
      if (this._path) {
        window._leafletPathRegistry.set(this, {
          hasPath: true,
          inDOM: !!this._path.parentNode
        });
      }
    } catch (e) {
      console.warn('Error in patched _initPath:', e);
      originalInitPath.call(this);
    }
  };

  const originalRemovePath = L.Path.prototype._removePath;
  L.Path.prototype._removePath = function () {
    try {
      const pathInfo = window._leafletPathRegistry.get(this);

      if (pathInfo && !pathInfo.hasPath) {
        console.debug('Path already marked as removed, skipping _removePath');
        return;
      }

      if (!this._path) {
        console.debug('No _path property, skipping _removePath');
        if (pathInfo) {
          window._leafletPathRegistry.set(this, { hasPath: false, inDOM: false });
        }
        return;
      }

      if (!this._path.parentNode) {
        console.debug('Path not in DOM, skipping _removePath');
        if (pathInfo) {
          window._leafletPathRegistry.set(this, { hasPath: true, inDOM: false });
        }
        return;
      }

      originalRemovePath.call(this);
      window._leafletPathRegistry.set(this, { hasPath: false, inDOM: false });
    } catch (e) {
      console.warn('Error in patched _removePath caught and handled:', e);

      if (this._path) {
        try {
          if (this._path.parentNode) {
            this._path.parentNode.removeChild(this._path);
          }
          this._path = null;
        } catch (cleanupError) {
          console.warn('Error during path cleanup:', cleanupError);
        }
      }

      window._leafletPathRegistry.set(this, { hasPath: false, inDOM: false });
    }
  };

  const originalRemoveLayer = L.LayerGroup.prototype.removeLayer;
  L.LayerGroup.prototype.removeLayer = function (layer) {
    if (!layer) {
      console.debug('Attempted to remove undefined layer, skipping');
      return this;
    }

    try {
      return originalRemoveLayer.call(this, layer);
    } catch (e) {
      console.warn('Error in LayerGroup.removeLayer caught and handled:', e);

      if (this._layers) {
        if (layer in this._layers) {
          delete this._layers[layer];
        } else if (layer._leaflet_id && this._layers[layer._leaflet_id]) {
          delete this._layers[layer._leaflet_id];
        }
      }

      return this;
    }
  };

  console.log("Enhanced Leaflet Path and LayerGroup patches applied");
};

// ZIP Code Boundaries Manager
const ZipCodeBoundaryManager = {
  isLoaded: false,
  isLoading: false,
  boundariesVisible: false,
  stateGeoJsonPaths: {
    'il': '/data/il-zip-codes.geojson',
    'in': '/data/in-zip-codes.geojson',
    'wi': '/data/wi-zip-codes.geojson'
  },
  fallbackPaths: {
    'il': 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/il_illinois_zip_codes_geo.min.json',
    'in': 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/in_indiana_zip_codes_geo.min.json',
    'wi': 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/wi_wisconsin_zip_codes_geo.min.json'
  },
  stateLabels: {
    'il': 'Illinois',
    'in': 'Indiana',
    'wi': 'Wisconsin'
  },
  stateFeatureCollections: {
    'il': null,
    'in': null,
    'wi': null
  },
  layerGroups: { zoomedOut: null, zoomedIn: null },
  selectedZipCodes: [],
  loadAttempts: { 'il': 0, 'in': 0, 'wi': 0 },
  maxAttempts: 2,

  loadGeoJson: async function (mapRefs) {
    if (this.isLoading) return false;
    this.isLoading = true;

    try {
      console.log("Loading ZIP code boundaries for multiple states", this.stateGeoJsonPaths);
      
      let loadedStates = [];
      const statePromises = [];
      
      for (const stateCode of ['il', 'in', 'wi']) {
        if (this.stateFeatureCollections[stateCode]) {
          loadedStates.push(stateCode);
          continue;
        }
        
        this.loadAttempts[stateCode] = (this.loadAttempts[stateCode] || 0) + 1;
        
        const statePromise = this.loadStateGeoJson(stateCode)
          .then(data => {
            if (data) {
              console.log(`Loaded ${data.features.length} ZIP boundaries for ${this.stateLabels[stateCode]}`);
              this.stateFeatureCollections[stateCode] = data;
              loadedStates.push(stateCode);
              return true;
            }
            return false;
          })
          .catch(error => {
            console.error(`Error loading ${stateCode.toUpperCase()} ZIP boundaries:`, error);
            return false;
          });
          
        statePromises.push(statePromise);
      }
      
      await Promise.allSettled(statePromises);

      this.isLoaded = loadedStates.length > 0;
      
      console.log(`Successfully loaded ${loadedStates.length} states: ${loadedStates.join(', ')}`);
      
      if (this.boundariesVisible && mapRefs.zoomedOutMapRef.current && mapRefs.zoomedInMapRef.current) {
        this.displayBoundaries(mapRefs.zoomedOutMapRef.current, mapRefs.zoomedInMapRef.current);
      }

      this.isLoading = false;
      return this.isLoaded;
    } catch (error) {
      console.error("Error loading ZIP code boundaries:", error);
      this.isLoaded = false;
      this.isLoading = false;
      return false;
    }
  },

  loadStateGeoJson: async function(stateCode) {
    if (!stateCode || !this.stateGeoJsonPaths[stateCode]) {
      console.error(`Invalid state code or missing path: ${stateCode}`);
      return null;
    }
    
    const primaryPath = this.stateGeoJsonPaths[stateCode];
    const fallbackPath = this.fallbackPaths[stateCode];
    const stateName = this.stateLabels[stateCode] || stateCode.toUpperCase();
    
    // Always use fallback paths since local files might not exist
    if (fallbackPath) {
      try {
        console.log(`Loading ${stateName} ZIP boundaries from fallback: ${fallbackPath}`);
        
        const response = await fetch(fallbackPath, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          }
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}`);
        }
        
        const data = await response.json();
        
        if (!data.type || data.type !== 'FeatureCollection' || !Array.isArray(data.features)) {
          throw new Error('Not a valid GeoJSON FeatureCollection');
        }
        
        console.log(`Successfully loaded ${stateName} GeoJSON`);
        return data;
      } catch (error) {
        console.error(`Failed to load ${stateName} ZIP boundaries:`, error);
        return null;
      }
    }
    
    return null;
  },

  getFeatures: function() {
    const allFeatures = [];
    let featureCounts = {};
    
    Object.entries(this.stateFeatureCollections).forEach(([stateCode, collection]) => {
      if (collection && collection.features) {
        featureCounts[stateCode] = collection.features.length;
        
        const stateFeatures = collection.features.map(feature => ({
          ...feature,
          properties: {
            ...feature.properties,
            stateCode: stateCode
          }
        }));
        allFeatures.push(...stateFeatures);
      } else {
        featureCounts[stateCode] = 0;
      }
    });
    
    console.log(`Found features by state:`, featureCounts);
    console.log(`Total features to display: ${allFeatures.length}`);
    
    return allFeatures;
  },

  displayBoundaries: function (zoomedOutMap, zoomedInMap) {
    if (!this.isLoaded) {
      console.log("No GeoJSON data loaded");
      return false;
    }

    const features = this.getFeatures();
    if (!features || features.length === 0) {
      console.log("No features to display");
      return false;
    }

    this.clearBoundaries(zoomedOutMap, zoomedInMap);

    this.layerGroups.zoomedOut = L.layerGroup().addTo(zoomedOutMap);
    this.layerGroups.zoomedIn = L.layerGroup().addTo(zoomedInMap);

    const zoom = zoomedOutMap.getZoom();

    let displayLimit = 100;
    let skipFactor = 1;

    if (zoom < 10) {
      displayLimit = 50;
      skipFactor = 10;
    } else if (zoom < 12) {
      displayLimit = 75;
      skipFactor = 5;
    }

    let featuresInView = features
      .filter((feature, index) => {
        if (index % skipFactor !== 0) return false;

        if (!feature.properties || !feature.geometry) return false;

        const zipCode = feature.properties.ZCTA5CE10 ||
          feature.properties.ZIP ||
          feature.properties.zipCode ||
          feature.properties.zip ||
          feature.properties.postalCode;

        if (!zipCode) return false;

        return true;
      })
      .slice(0, displayLimit);

    console.log(`Displaying ${featuresInView.length} ZIP boundaries`);

    const processBatch = (features, startIndex) => {
      const batchSize = 10;
      const endIndex = Math.min(startIndex + batchSize, features.length);

      for (let i = startIndex; i < endIndex; i++) {
        const feature = features[i];

        try {
          const zipCode = feature.properties.ZCTA5CE10 ||
            feature.properties.ZIP ||
            feature.properties.zipCode ||
            feature.properties.zip ||
            feature.properties.postalCode;

          if (!feature.geometry || !zipCode) continue;

          const isSelected = this.selectedZipCodes.includes(zipCode);
          
          const stateCode = feature.properties.stateCode || 'unknown';
          
          let stateColor;
          switch(stateCode) {
            case 'il': stateColor = '#3b82f6'; break;
            case 'in': stateColor = '#8b5cf6'; break;
            case 'wi': stateColor = '#f97316'; break;
            default: stateColor = '#3b82f6';
          }

          const polygonOptions = {
            color: isSelected ? '#10b981' : stateColor,
            weight: isSelected ? 2 : 1,
            opacity: 0.8,
            fillColor: isSelected ? '#10b981' : stateColor,
            fillOpacity: isSelected ? 0.4 : 0.2,
            className: `zip-boundary ${stateCode}`,
            interactive: true
          };

          const layer = L.geoJSON(feature, {
            style: polygonOptions
          });

          const tooltipContent = `${zipCode} (${stateCode.toUpperCase()})`;
            
          layer.bindTooltip(tooltipContent, {
            permanent: false,
            direction: 'center',
            className: 'zip-tooltip'
          });

          layer.on('click', (e) => {
            if (e && e.originalEvent) {
              e.originalEvent.stopPropagation();
            }

            if (this.selectedZipCodes.includes(zipCode)) {
              this.selectedZipCodes = this.selectedZipCodes.filter(code => code !== zipCode);
              layer.setStyle({
                fillColor: stateColor,
                color: stateColor,
                fillOpacity: 0.2,
                weight: 1
              });
            } else {
              this.selectedZipCodes.push(zipCode);
              layer.setStyle({
                fillColor: '#10b981',
                color: '#10b981',
                fillOpacity: 0.4,
                weight: 2
              });
            }
          });

          if (this.layerGroups.zoomedOut) {
            this.layerGroups.zoomedOut.addLayer(layer);
          }

          if (this.layerGroups.zoomedIn) {
            const layerClone = L.geoJSON(feature, {
              style: polygonOptions
            });

            layerClone.bindTooltip(tooltipContent, {
              permanent: false,
              direction: 'center',
              className: 'zip-tooltip'
            });

            layerClone.on('click', (e) => {
              if (e && e.originalEvent) {
                e.originalEvent.stopPropagation();
              }

              if (this.selectedZipCodes.includes(zipCode)) {
                this.selectedZipCodes = this.selectedZipCodes.filter(code => code !== zipCode);
                layerClone.setStyle({
                  fillColor: stateColor,
                  color: stateColor,
                  fillOpacity: 0.2,
                  weight: 1
                });
              } else {
                this.selectedZipCodes.push(zipCode);
                layerClone.setStyle({
                  fillColor: '#10b981',
                  color: '#10b981',
                  fillOpacity: 0.4,
                  weight: 2
                });
              }
            });

            this.layerGroups.zoomedIn.addLayer(layerClone);
          }
        } catch (error) {
          console.warn("Error processing boundary:", error);
        }
      }

      if (endIndex < features.length) {
        setTimeout(() => {
          processBatch(features, endIndex);
        }, 0);
      }
    };

    processBatch(featuresInView, 0);

    this.boundariesVisible = true;
    return true;
  },

  clearBoundaries: function (zoomedOutMap, zoomedInMap) {
    if (this.layerGroups.zoomedOut && zoomedOutMap) {
      zoomedOutMap.removeLayer(this.layerGroups.zoomedOut);
      this.layerGroups.zoomedOut = null;
    }

    if (this.layerGroups.zoomedIn && zoomedInMap) {
      zoomedInMap.removeLayer(this.layerGroups.zoomedIn);
      this.layerGroups.zoomedIn = null;
    }

    this.boundariesVisible = false;
  },

  toggleBoundaries: function (zoomedOutMap, zoomedInMap) {
    if (this.boundariesVisible) {
      this.clearBoundaries(zoomedOutMap, zoomedInMap);
    } else {
      if (this.isLoaded) {
        this.displayBoundaries(zoomedOutMap, zoomedInMap);
      } else if (!this.isLoading) {
        this.loadGeoJson({
          zoomedOutMapRef: { current: zoomedOutMap },
          zoomedInMapRef: { current: zoomedInMap }
        });
      }
    }

    return this.boundariesVisible;
  }
};

// Add CSS styles
const addMapStyles = () => {
  if (!document.getElementById('map-display-styles')) {
    const styleEl = document.createElement('style');
    styleEl.id = 'map-display-styles';
    styleEl.textContent = `
      .zip-boundary {
        transition: all 0.2s ease;
      }
      
      .zip-boundary:hover {
        fillOpacity: 0.3 !important;
        weight: 2 !important;
        cursor: pointer;
      }
      
      .zip-tooltip {
        background: rgba(0, 0, 0, 0.7) !important;
        border: none !important;
        color: white !important;
        font-weight: bold;
        padding: 2px 6px !important;
      }
      
      .zip-boundary.il:hover {
        fill-opacity: 0.4 !important;
      }
      
      .zip-boundary.in:hover {
        fill-opacity: 0.4 !important;
      }
      
      .zip-boundary.wi:hover {
        fill-opacity: 0.4 !important;
      }

      .current-location-marker {
        background: transparent !important;
        border: none !important;
      }

      .current-marker-inner {
        width: 20px;
        height: 20px;
        background-color: #3B82F6;
        border: 3px solid #FFFFFF;
        border-radius: 50%;
        position: relative;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
      }

      .current-marker-inner.pulse {
        animation: pulse-blue 2s infinite;
      }

      @keyframes pulse-blue {
        0% {
          box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
        }
        70% {
          box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
        }
      }

      .vehicle-marker {
        background: transparent !important;
        border: none !important;
        z-index: 1000 !important;
      }

      .vehicle-marker-container {
        cursor: pointer !important;
        transition: transform 0.2s ease;
        pointer-events: auto !important;
      }

      .vehicle-marker-container:hover {
        transform: scale(1.1);
      }

      .vehicle-marker-bg {
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        transition: box-shadow 0.2s ease;
      }

      .vehicle-marker-container:hover .vehicle-marker-bg {
        box-shadow: 0 4px 12px rgba(0,0,0,0.4);
      }

      @keyframes vehicle-pulse {
        0% { 
          transform: scale(1); 
          opacity: 0.3; 
        }
        50% { 
          transform: scale(1.2); 
          opacity: 0.1; 
        }
        100% { 
          transform: scale(1); 
          opacity: 0.3; 
        }
      }

      .vehicle-marker-pulse {
        animation: vehicle-pulse 2s infinite;
      }

      .vehicle-marker.priority .vehicle-marker-pulse {
        animation: vehicle-pulse 1s infinite;
      }

      .team-vehicle-marker {
        background: transparent !important;
        border: none !important;
        z-index: 2000 !important;
      }
      
      .team-vehicle-marker .leaflet-marker-icon {
        z-index: 2000 !important;
      }

      .team-vehicle-marker-bg {
        border: 3px solid #9333EA;
        box-shadow: 0 0 0 2px rgba(147, 51, 234, 0.3);
      }

      .team-vehicle-marker.in-route .team-vehicle-marker-bg {
        border-color: #3B82F6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
      }

      .team-vehicle-marker.arrived .team-vehicle-marker-bg {
        border-color: #10B981;
        box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.5);
      }

      .team-vehicle-marker.bottom-status .team-vehicle-marker-bg {
        border-color: #EF4444;
        box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
      }

      .heading-indicator {
        position: absolute;
        top: -8px;
        left: 50%;
        transform-origin: center;
        margin-left: -8px;
        z-index: 1000;
      }

      .heading-indicator svg {
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
      }

      .team-member-marker {
        background: transparent !important;
        border: none !important;
        z-index: 1000 !important;
      }

      .team-member-marker .pulse-ring {
        animation: pulse-ring 2s infinite;
      }

      @keyframes pulse-ring {
        0% {
          transform: scale(0.8);
          opacity: 1;
        }
        100% {
          transform: scale(2.4);
          opacity: 0;
        }
      }

      .team-marker-container {
        position: relative;
      }

      .team-marker-bg {
        border-radius: 50%;
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
      }

      .team-marker-bg:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(0,0,0,0.4);
      }

      .online-indicator {
        animation: online-pulse 2s infinite;
      }

      @keyframes online-pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }

      @keyframes pulse-ring {
        0% {
          transform: scale(1);
          opacity: 0.5;
        }
        100% {
          transform: scale(1.3);
          opacity: 0;
        }
      }

      .leaflet-marker-icon {
        background: transparent !important;
        border: none !important;
      }

      .leaflet-div-icon {
        background: transparent !important;
        border: none !important;
      }

      .order-popup {
        min-width: 200px;
      }

      .order-popup button {
        transition: all 0.2s ease;
      }

      .order-popup button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      }

      .manual-location-marker {
        background: transparent !important;
        border: none !important;
      }

      .location-marker-container {
        cursor: pointer !important;
        transition: transform 0.2s ease;
      }

      .location-marker-container:hover {
        transform: scale(1.1);
      }
      
      .team-vehicle-popup-wrapper .leaflet-popup-content-wrapper {
        padding: 0 !important;
        overflow: hidden;
        border-radius: 8px;
      }
      
      .team-vehicle-popup-wrapper .leaflet-popup-content {
        margin: 0 !important;
        padding: 20px !important;
      }
      
      .team-vehicle-popup-wrapper .leaflet-popup-tip {
        background: #7C3AED;
      }

      .context-menu-popup {
        padding: 0 !important;
      }

      .context-menu {
        background: white;
        border-radius: 4px;
        padding: 4px 0;
        min-width: 150px;
      }

      .context-menu-item {
        display: block;
        width: 100%;
        padding: 8px 12px;
        text-align: left;
        border: none;
        background: none;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
      }

      .context-menu-item:hover {
        background-color: #f3f4f6;
      }

      .destination-marker {
        background: transparent !important;
        border: none !important;
      }

      .destination-marker-inner {
        width: 32px;
        height: 32px;
        background-color: #EF4444;
        border: 3px solid #FFFFFF;
        border-radius: 50%;
        position: relative;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
      }

      .destination-marker-inner::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 8px;
        height: 8px;
        background-color: #FFFFFF;
        border-radius: 50%;
      }

      .map-container-wrapper {
        background-color: #1f2937;
      }

      .leaflet-container {
        background-color: #374151;
      }

      .rotate-indicator {
        pointer-events: none;
      }

      .transform-wrapper {
        width: 100%;
        height: 100%;
      }
    `;
    document.head.appendChild(styleEl);
  }
};

// Main Standalone MapDisplay Component
const StandaloneMapDisplay = ({
  // Optional callbacks
  onMapClick = () => {},
  onLocationUpdate = () => {},
  onNavigationStatusChange = () => {},
  onTeamVehicleClick = () => {},
  onTeamVehicleNavigate = () => {},
  
  // Vehicle data
  vehicles = [],
  teamVehicles = [],
  mapFilter = 'all',
  
  // Optional data
  initialLocation = null,
  teamMembers = [],
  onlineUsers = [],
  currentUser = null,
  processedMarkers = null,
  
  // Optional configuration
  showZipBoundaries = false, // Changed to false by default to avoid errors
  enableNavigation = true,
  enableLocationTracking = true,
  isAdmin = false,
  
  // Optional UI props
  userDisplayNames = {},
  userProfilePictures = {},
  
  // Screen config
  screenConfig = {
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
    isSmallScreen: false,
    isMediumScreen: false
  }
}) => {
  // State
  const [currentLocation, setCurrentLocation] = useState(initialLocation || { lat: 37.7749, lng: -122.4194 });
  const [internalLoading, setInternalLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showZipCodes, setShowZipCodes] = useState(showZipBoundaries);
  const [locationTrackingEnabled, setLocationTrackingEnabled] = useState(enableLocationTracking);
  const [mapRotation, setMapRotation] = useState(0);
  const [mapScale, setMapScale] = useState(1.5);
  const [isRotationEnabled, setIsRotationEnabled] = useState(false);
  const [routeInfo, setRouteInfo] = useState(null);
  const [nextManeuver, setNextManeuver] = useState(null);
  
  // Refs
  const zoomedOutMapRef = useRef(null);
  const zoomedInMapRef = useRef(null);
  const zoomedOutContainerRef = useRef(null);
  const zoomedInContainerRef = useRef(null);
  const userHasDraggedMap = useRef(false);
  const zoomedOutMarkerClusterRef = useRef(null);
  const zoomedInMarkerClusterRef = useRef(null);
  const zoomedOutUserMarkerClusterRef = useRef(null);
  const zoomedInUserMarkerClusterRef = useRef(null);
  const processedMarkersRef = useRef({ zoomedOut: new Map(), zoomedIn: new Map() });
  const teamMemberMarkersRef = useRef({ zoomedOut: new Map(), zoomedIn: new Map() });
  const teamVehicleMarkersRef = useRef({ zoomedOut: new Map(), zoomedIn: new Map() });
  const zoomedOutRoutingControlRef = useRef(null);
  const zoomedInRoutingControlRef = useRef(null);
  const locationWatchIdRef = useRef(null);
  const destinationRef = useRef(null);
  const currentLocationMarkerRef = useRef(null);
  const destinationMarkerRef = useRef(null);
  const updateInProgressRef = useRef(false);
  const lastLocationUpdateRef = useRef(Date.now());
  const geocodingCache = useRef({}); 
  const geocodingInProgress = useRef({});
  
  // Constants
  const ZOOMED_OUT_LEVEL = 15;
  const ZOOMED_IN_LEVEL = 19;
  
  // PROCESS ALL VEHICLES FOR MAP DISPLAY
  const processedVehiclesForMap = React.useMemo(() => {
    const allVehicles = [];
    
    // Add user's own vehicles
    vehicles.forEach((vehicle) => {
      if (vehicle.position || vehicle.address) {
        allVehicles.push({
          ...vehicle,
          uniqueKey: `user-${currentUser?.id || 'unknown'}-${vehicle.id}`,
          isTeamVehicle: false,
          isOwnVehicle: true,
          teamMemberName: currentUser?.displayName || currentUser?.email?.split('@')[0] || 'You',
          teamMemberId: currentUser?.id
        });
      }
    });

    // Add team vehicles
    teamVehicles.forEach((vehicle) => {
      if (vehicle.position || vehicle.address) {
        allVehicles.push({
          ...vehicle,
          uniqueKey: vehicle.uniqueKey || `team-${vehicle.teamMemberId}-${vehicle.id}`,
          isTeamVehicle: true,
          isOwnVehicle: false
        });
      }
    });

    console.log(`Processing ${allVehicles.length} vehicles for map (${vehicles.length} user, ${teamVehicles.length} team)`);
    
    // Apply filter
    switch (mapFilter) {
      case 'user':
        return allVehicles.filter(v => v.isOwnVehicle);
      case 'team':
        return allVehicles.filter(v => v.isTeamVehicle && !v.isOwnVehicle);
      default:
        return allVehicles;
    }
  }, [vehicles, teamVehicles, currentUser, mapFilter]);

  // Apply patches and styles on mount
  useEffect(() => {
    applyLeafletPathPatch();
    addMapStyles();
  }, []);

  // Create icon functions
  const createVehicleIcon = useCallback((markerData, size) => {
    const bgColor = markerData.markerColor || '#2563EB';
    const iconPath = markerData.iconPath || "M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 15c-.83 0-1.5-.67-1.5-1.5S5.67 12 6.5 12s1.5.67 1.5 1.5S7.33 15 6.5 15zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 10l1.5-4.5h11L19 10H5z";
    
    return L.divIcon({
      className: `vehicle-marker ${markerData.priority ? 'priority' : ''} status-${markerData.status || 'unknown'}`,
      html: `
        <div class="vehicle-marker-container" style="width:${size}px; height:${size}px; position: relative;">
          <div class="vehicle-marker-bg" style="
            background-color: ${bgColor}; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            position: relative; 
            border: 3px solid #FFFFFF;
            box-shadow: 0 3px 8px rgba(0,0,0,0.4);
            display: flex;
            align-items: center;
            justify-content: center;
          ">
            <svg viewBox="0 0 24 24" style="
              width: ${size * 0.65}px; 
              height: ${size * 0.65}px;
              fill: #FFFFFF;
            ">
              <path d="${iconPath}" />
            </svg>
          </div>
          <div class="vehicle-marker-pulse" style="
            position: absolute; 
            top: 0; 
            left: 0; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            background-color: ${bgColor}; 
            opacity: 0.3; 
            z-index: -1;
          "></div>
        </div>
      `,
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2]
    });
  }, []);

  const createTeamVehicleIcon = useCallback((vehicleData, size = 36) => {
    let bgColor = '#9333EA';
    let statusClass = 'team-vehicle-marker';
    let pulseAnimation = '';
    
    if (vehicleData.arrivedDriverId) {
      bgColor = '#10B981';
      statusClass += ' arrived';
    } else if (vehicleData.inRouteDriverId) {
      bgColor = '#3B82F6';
      statusClass += ' in-route';
      pulseAnimation = 'animation: vehicle-pulse 1.5s infinite;';
    } else if (vehicleData.bottomStatus) {
      bgColor = '#EF4444';
      statusClass += ' bottom-status';
    }
    
    const carIconPath = "M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 15c-.83 0-1.5-.67-1.5-1.5S5.67 12 6.5 12s1.5.67 1.5 1.5S7.33 15 6.5 15zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 10l1.5-4.5h11L19 10H5z";
    
    return L.divIcon({
      className: statusClass,
      html: `
        <div class="vehicle-marker-container" style="width:${size}px; height:${size}px; position: relative;">
          <div style="
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border: 3px solid ${bgColor};
            border-radius: 50%;
            opacity: 0.3;
          "></div>
          
          <div class="team-vehicle-marker-bg vehicle-marker-bg" style="
            background-color: ${bgColor}; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            position: relative; 
            border: 3px solid #FFFFFF;
            box-shadow: 0 4px 8px rgba(0,0,0,0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
          ">
            <svg viewBox="0 0 24 24" style="
              width: ${size * 0.65}px; 
              height: ${size * 0.65}px;
              fill: #FFFFFF;
            ">
              <path d="${carIconPath}" />
            </svg>
          </div>
          ${pulseAnimation ? `
            <div class="vehicle-marker-pulse" style="
              position: absolute; 
              top: 0; 
              left: 0; 
              width: ${size}px; 
              height: ${size}px; 
              border-radius: 50%; 
              background-color: ${bgColor}; 
              opacity: 0.3; 
              z-index: -1;
              ${pulseAnimation}
            "></div>
          ` : ''}
        </div>
      `,
      iconSize: [size + 8, size + 8],
      iconAnchor: [(size + 8) / 2, (size + 8) / 2]
    });
  }, []);

  const createUserAvatarIcon = useCallback((markerData, size) => {
    const isOnline = markerData.online || false;
    const userColor = markerData.markerColor || '#6B7280';
    const profilePicture = markerData.profilePicture || '';
    const displayName = markerData.name || `User ${markerData.userId}`;
    
    let headingIndicator = '';
    if (markerData.heading !== undefined && markerData.heading !== null) {
      headingIndicator = `
        <div class="heading-indicator" style="transform: rotate(${markerData.heading}deg); position: absolute; top: -8px; left: 50%; transform-origin: center;">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="12" height="12">
            <path fill="#FFFFFF" d="M12,2L4,13h16L12,2z"/>
          </svg>
        </div>
      `;
    }

    return L.divIcon({
      className: `team-member-marker ${isOnline ? 'online' : 'offline'}`,
      html: `
        <div class="team-marker-container" style="width:${size}px; height:${size}px; position: relative;">
          <div class="team-marker-bg" style="
            background-color: ${userColor}; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            border: 3px solid white;
            position: relative;
            ${isOnline ? 'box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);' : ''}
          ">
            ${profilePicture ? 
              `<img src="${profilePicture}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;" />` :
              `<div style="
                width: 100%; 
                height: 100%; 
                display: flex; 
                align-items: center; 
                justify-content: center; 
                color: white; 
                font-weight: bold; 
                font-size: ${size * 0.4}px;
              ">${displayName.charAt(0).toUpperCase()}</div>`
            }
            ${isOnline ? `<div class="online-indicator" style="
              position: absolute; 
              bottom: 2px; 
              right: 2px; 
              width: 8px; 
              height: 8px; 
              background-color: #10B981; 
              border-radius: 50%; 
              border: 2px solid white;
            "></div>` : ''}
          </div>
          ${headingIndicator}
          ${isOnline ? `<div class="pulse-ring" style="
            position: absolute; 
            top: 0; 
            left: 0; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            background-color: ${userColor}; 
            opacity: 0.3;
          "></div>` : ''}
        </div>
      `,
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2]
    });
  }, []);

  const createLocationPinIcon = useCallback((markerData, size) => {
    const bgColor = markerData.markerColor || '#6B7280';
    const iconPath = markerData.iconPath || "M12,2C8.13,2 5,5.13 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9C19,5.13 15.87,2 12,2M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5Z";
    
    return L.divIcon({
      className: 'manual-location-marker',
      html: `
        <div class="location-marker-container" style="width:${size}px; height:${size}px;">
          <div style="
            background-color: ${bgColor}; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            position: relative; 
            border: 2px solid #FFFFFF;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
          ">
            <svg viewBox="0 0 24 24" style="
              width: ${size * 0.7}px; 
              height: ${size * 0.7}px;
              fill: #FFFFFF;
            ">
              <path d="${iconPath}" />
            </svg>
          </div>
        </div>
      `,
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2]
    });
  }, []);

  const createGenericIcon = useCallback((markerData, size) => {
    const bgColor = markerData.markerColor || '#6B7280';
    
    return L.divIcon({
      className: 'generic-marker',
      html: `
        <div style="
          background-color: ${bgColor}; 
          width: ${size}px; 
          height: ${size}px; 
          border-radius: 50%; 
          border: 2px solid #FFFFFF;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: ${size * 0.3}px;
        ">
          ?
        </div>
      `,
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2]
    });
  }, []);

  // Create marker from processed data
  const createMarkerFromProcessedData = useCallback((markerData, isZoomedIn = false) => {
    if (!markerData || !markerData.position) {
      console.warn("Invalid marker data:", markerData);
      return null;
    }

    let icon;
    const size = isZoomedIn ? (markerData.size || 28) + 4 : markerData.size || 28;

    switch (markerData.iconType) {
      case 'vehicle':
        icon = createVehicleIcon(markerData, size);
        break;
      case 'user-avatar':
        icon = createUserAvatarIcon(markerData, size);
        break;
      case 'location-pin':
        icon = createLocationPinIcon(markerData, size);
        break;
      default:
        icon = createGenericIcon(markerData, size);
    }

    const marker = L.marker([markerData.position.lat, markerData.position.lng], {
      icon: icon,
      title: markerData.name || 'Marker',
      zIndexOffset: markerData.zIndex || 100,
      riseOnHover: true
    });

    if (markerData.showPopup && markerData.popupContent) {
      marker.bindPopup(markerData.popupContent, { maxWidth: 250 });
    }

    if (markerData.clickable && markerData.onClick) {
      marker.on('click', () => {
        console.log(`Marker clicked: ${markerData.id} (${markerData.type})`);
        markerData.onClick();
      });
    }

    return marker;
  }, [createVehicleIcon, createUserAvatarIcon, createLocationPinIcon, createGenericIcon]);

  // Safe utility functions
  const safeRemoveLayer = (map, layer) => {
    if (!map || !layer) return;
    try {
      if (map.hasLayer(layer)) {
        map.removeLayer(layer);
      }
    } catch (error) {
      console.warn("Error safely removing layer:", error);
    }
  };

  const safeRemoveFromCluster = (cluster, layer) => {
    if (!cluster || !layer) return;
    try {
      if (cluster.hasLayer(layer)) {
        cluster.removeLayer(layer);
      }
    } catch (error) {
      console.warn("Error safely removing layer from cluster:", error);
    }
  };

  const safeClearRouteLines = (routingControl) => {
    if (!routingControl) return false;

    try {
      if (!routingControl._plan ||
        !routingControl._routes ||
        !Array.isArray(routingControl._routes) ||
        routingControl._routes.length === 0) {
        return false;
      }

      if (typeof routingControl._clearLines !== 'function') {
        return false;
      }

      const hasValidPaths = routingControl._routes.some(route => {
        try {
          return route &&
            route.line &&
            route.line._path &&
            route.line._path.parentNode;
        } catch (e) {
          return false;
        }
      });

      if (hasValidPaths) {
        return new Promise((resolve) => {
          const timeout = setTimeout(() => {
            console.warn("Route clearing timed out");
            resolve(false);
          }, 300);

          try {
            routingControl._routes.forEach(route => {
              try {
                if (route && route.line) {
                  if (typeof route.line.eachLayer === 'function') {
                    const layers = [];
                    route.line.eachLayer(layer => layers.push(layer));

                    layers.forEach(layer => {
                      try {
                        route.line.removeLayer(layer);
                      } catch (e) {
                        console.warn("Error removing layer from line:", e);
                      }
                    });
                  }

                  if (routingControl._container && routingControl._map) {
                    try {
                      if (route.line._leaflet_id && routingControl._map._layers &&
                        routingControl._map._layers[route.line._leaflet_id]) {
                        routingControl._map.removeLayer(route.line);
                      }
                    } catch (e) {
                      console.warn("Error removing line from map:", e);
                    }
                  }
                }
              } catch (e) {
                console.warn("Error in route cleanup:", e);
              }
            });

            routingControl._routes = [];

            clearTimeout(timeout);
            resolve(true);
          } catch (error) {
            console.warn("Error in route clearing:", error);
            clearTimeout(timeout);
            resolve(false);
          }
        });
      } else {
        routingControl._routes = [];
        return true;
      }
    } catch (error) {
      console.warn("Error in safeClearRouteLines:", error);
      return false;
    }
  };

  // Geocoding function for addresses
  const geocodeAddress = async (address) => {
    if (!address) return null;
    
    // Check cache first
    if (geocodingCache.current[address]) {
      console.log(`Using cached coordinates for: ${address}`);
      return geocodingCache.current[address];
    }
    
    // Check if already geocoding this address
    if (geocodingInProgress.current[address]) {
      console.log(`Already geocoding: ${address}`);
      return null;
    }
    
    try {
      geocodingInProgress.current[address] = true;
      console.log(`Geocoding address: ${address}`);
      
      // Use OpenStreetMap Nominatim API (free, no API key required)
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1`,
        {
          headers: {
            'User-Agent': 'MapDisplay/1.0' // Required by Nominatim
          }
        }
      );
      
      if (!response.ok) {
        throw new Error(`Geocoding API error: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.length > 0) {
        const position = {
          lat: parseFloat(data[0].lat),
          lng: parseFloat(data[0].lon)
        };
        
        // Cache the result
        geocodingCache.current[address] = position;
        console.log(`Successfully geocoded ${address} to:`, position);
        
        return position;
      } else {
        console.warn(`No geocoding results found for: ${address}`);
        return null;
      }
    } catch (error) {
      console.error(`Error geocoding address "${address}":`, error);
      return null;
    } finally {
      delete geocodingInProgress.current[address];
    }
  };

  // Location tracking functions
  const updateUserLocationSmooth = (location) => {
    if (!location || !location.lat || !location.lng) return false;
    
    console.log("🔄 Smoothly updating user location:", location);
    
    // Update internal state
    setCurrentLocation(location);

    try {
      if (zoomedOutMapRef.current && !userHasDraggedMap.current) {
        zoomedOutMapRef.current.panTo([location.lat, location.lng], { 
          animate: true, 
          duration: 0.25,
          noMoveStart: true 
        });
      }
      if (zoomedInMapRef.current && !userHasDraggedMap.current) {
        zoomedInMapRef.current.panTo([location.lat, location.lng], { 
          animate: true, 
          duration: 0.25,
          noMoveStart: true 
        });
      }
    } catch (error) {
      console.warn("Error updating map views:", error);
    }

    if (currentLocationMarkerRef.current) {
      try {
        let updateSuccessful = true;
        
        if (currentLocationMarkerRef.current.zoomed_out) {
          try {
            currentLocationMarkerRef.current.zoomed_out.setLatLng([location.lat, location.lng]);
            console.log("✅ Updated zoomed out user marker");
          } catch (error) {
            console.warn("❌ Error updating zoomed out marker:", error);
            updateSuccessful = false;
          }
        } else {
          updateSuccessful = false;
        }
        
        if (currentLocationMarkerRef.current.zoomed_in) {
          try {
            currentLocationMarkerRef.current.zoomed_in.setLatLng([location.lat, location.lng]);
            console.log("✅ Updated zoomed in user marker");
          } catch (error) {
            console.warn("❌ Error updating zoomed in marker:", error);
            updateSuccessful = false;
          }
        } else {
          updateSuccessful = false;
        }
        
        if (!updateSuccessful) {
          console.log("🔄 Marker update failed, recreating user markers");
          createNewUserMarkers(location);
        }
        
      } catch (error) {
        console.warn("❌ Error in marker update, recreating:", error);
        createNewUserMarkers(location);
      }
    } else {
      console.log("🆕 No existing marker, creating new user markers");
      createNewUserMarkers(location);
    }

    // Call external update callback
    if (typeof onLocationUpdate === 'function') {
      onLocationUpdate(location);
    }

    return true;
  };

  const createNewUserMarkers = (location) => {
    if (!zoomedOutMapRef.current || !zoomedInMapRef.current) {
      console.warn("❌ Maps not ready for marker creation");
      return;
    }

    console.log("🆕 Creating new user markers at:", location);

    if (currentLocationMarkerRef.current) {
      try {
        if (currentLocationMarkerRef.current.zoomed_out) {
          safeRemoveLayer(zoomedOutMapRef.current, currentLocationMarkerRef.current.zoomed_out);
        }
        
        if (currentLocationMarkerRef.current.zoomed_in) {
          safeRemoveLayer(zoomedInMapRef.current, currentLocationMarkerRef.current.zoomed_in);
        }
        
        currentLocationMarkerRef.current = null;
        console.log("🗑️ Cleaned up existing user markers");
      } catch (error) {
        console.warn("⚠️ Error cleaning up existing markers:", error);
      }
    }

    let headingIndicator = '';
    if (location.heading !== undefined && location.heading !== null) {
      headingIndicator = `
        <div class="heading-indicator" style="transform: rotate(${location.heading}deg)">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
            <path fill="#FFFFFF" d="M12,2L4,13h16L12,2z"/>
          </svg>
        </div>
      `;
    }

    const userIcon = L.divIcon({
      className: 'current-location-marker',
      html: `
        <div class="current-marker-inner pulse"></div>
        ${headingIndicator}
      `,
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });

    const userMarkerZoomedOut = L.marker([location.lat, location.lng], {
      icon: userIcon,
      zIndexOffset: 1000,
      title: "Your Location"
    });

    const userMarkerZoomedIn = L.marker([location.lat, location.lng], {
      icon: userIcon,
      zIndexOffset: 1000,
      title: "Your Location"
    });

    try {
      zoomedOutMapRef.current.addLayer(userMarkerZoomedOut);
      zoomedInMapRef.current.addLayer(userMarkerZoomedIn);
      
      console.log("✅ Added user markers to maps directly");

      currentLocationMarkerRef.current = {
        zoomed_out: userMarkerZoomedOut,  
        zoomed_in: userMarkerZoomedIn
      };
      
      console.log("✅ User markers created and stored successfully");
      
    } catch (error) {
      console.warn("❌ Error adding new user markers:", error);
    }
  };

  const startContinuousTracking = () => {
    if (locationWatchIdRef.current) return;

    console.log("Starting enhanced continuous location tracking");
    
    if (navigator.geolocation) {
      try {
        const watchId = navigator.geolocation.watchPosition(
          (position) => {
            const { latitude, longitude, accuracy, heading, speed } = position.coords;
            
            const location = { 
              lat: latitude, 
              lng: longitude,
              accuracy: accuracy,
              heading: heading || 0,
              speed: speed || 0,
              timestamp: Date.now()
            };
            
            console.log("Live location update received:", location);
            
            if (zoomedOutMapRef.current && zoomedInMapRef.current) {
              updateUserLocationSmooth(location);
            }
          },
          (error) => {
            console.error("Enhanced location tracking error:", error);
            setError(`Location error: ${error.message}`);
          },
          {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0,
            desiredAccuracy: 10,
          }
        );
        
        locationWatchIdRef.current = watchId;
        setLocationTrackingEnabled(true);
        
        return watchId;
      } catch (error) {
        console.error("Error setting up enhanced location tracking:", error);
        setLocationTrackingEnabled(false);
        return null;
      }
    } else {
      setError("Geolocation is not supported by this browser");
    }
  };
  
  const stopContinuousTracking = () => {
    if (locationWatchIdRef.current !== null) {
      console.log("Stopping continuous location tracking");
      navigator.geolocation.clearWatch(locationWatchIdRef.current);
      locationWatchIdRef.current = null;
      setLocationTrackingEnabled(false);
    }
  };
  
  const toggleLocationTracking = () => {
    if (locationTrackingEnabled) {
      stopContinuousTracking();
    } else {
      startContinuousTracking();
    }
  };

  // Navigation functions
  const setDestination = (lat, lng) => {
    if (updateInProgressRef.current) return false;
    updateInProgressRef.current = true;

    console.log(`Setting destination to: ${lat}, ${lng}`);

    try {
      destinationRef.current = { lat, lng };

      if (currentLocation && currentLocation.lat && currentLocation.lng) {
        setRoutingWaypoints(currentLocation, { lat, lng });
      }

      if (zoomedOutMapRef.current && zoomedInMapRef.current) {
        if (destinationMarkerRef.current) {
          if (destinationMarkerRef.current.zoomed_out) {
            safeRemoveLayer(zoomedOutMapRef.current, destinationMarkerRef.current.zoomed_out);
          }

          if (destinationMarkerRef.current.zoomed_in) {
            safeRemoveLayer(zoomedInMapRef.current, destinationMarkerRef.current.zoomed_in);
          }
        }

        const destinationIcon = L.divIcon({
          className: 'destination-marker',
          html: '<div class="destination-marker-inner"></div>',
          iconSize: [32, 32],
          iconAnchor: [16, 16]
        });

        const destinationMarker = L.marker([lat, lng], { icon: destinationIcon });
        const destinationMarkerClone = L.marker([lat, lng], { icon: destinationIcon });

        const popupContent = `
          <div class="text-center">
            <strong>Navigation Destination</strong><br>
            <span class="text-xs">${lat.toFixed(6)}, ${lng.toFixed(6)}</span><br>
            <button id="center-maps-btn" class="bg-blue-600 text-white px-2 py-1 rounded text-xs mt-1">
              Center Maps Here
            </button>
          </div>
        `;

        destinationMarker.bindPopup(popupContent);
        destinationMarkerClone.bindPopup(popupContent);

        destinationMarker.on('popupopen', () => {
          setTimeout(() => {
            const centerBtn = document.getElementById('center-maps-btn');
            if (centerBtn) {
              centerBtn.addEventListener('click', () => {
                zoomedOutMapRef.current.setView([lat, lng], ZOOMED_OUT_LEVEL, { animate: true });
                zoomedInMapRef.current.setView([lat, lng], ZOOMED_IN_LEVEL, { animate: true });
              });
            }
          }, 10);
        });

        try {
          zoomedOutMapRef.current.addLayer(destinationMarkerClone);
          zoomedInMapRef.current.addLayer(destinationMarker);
          
          destinationMarkerRef.current = {
            zoomed_out: destinationMarkerClone,
            zoomed_in: destinationMarker
          };
        } catch (error) {
          console.warn("Error adding destination markers:", error);
        }

        if (!isRotationEnabled) {
          setIsRotationEnabled(true);
        }

        updateMapRotation();

        try {
          zoomedOutMapRef.current.setView([lat, lng], ZOOMED_OUT_LEVEL, { animate: true });
          zoomedInMapRef.current.setView([lat, lng], ZOOMED_IN_LEVEL, { animate: true });
        } catch (error) {
          console.warn("Error centering maps:", error);
        }
      }

      updateInProgressRef.current = false;
      return true;
    } catch (error) {
      console.error("Error setting destination:", error);
      updateInProgressRef.current = false;
      return false;
    }
  };

  const setRoutingWaypoints = async (start, end) => {
    if (!start || !end || (!start.lat && !start.lng) || (!end.lat && !end.lng)) {
      console.error("Invalid waypoints for routing:", { start, end });
      return false;
    }

    try {
      const roundCoord = coord => parseFloat(coord.toFixed(5));
      const cacheKey = `${roundCoord(start.lat)},${roundCoord(start.lng)};${roundCoord(end.lat)},${roundCoord(end.lng)}`;

      if (ROUTE_REQUEST_CACHE.has(cacheKey)) {
        console.log("Using cached route:", cacheKey);
        const cachedRoute = ROUTE_REQUEST_CACHE.get(cacheKey);

        if (cachedRoute && cachedRoute.waypoints) {
          setTimeout(() => {
            updateRouteDisplayFromCache(cachedRoute);
          }, 10);
          return true;
        }
      }

      if (PENDING_REQUESTS.has(cacheKey)) {
        console.log("Request already pending:", cacheKey);
        return true;
      }

      const waypoints = [
        L.latLng(start.lat, start.lng),
        L.latLng(end.lat, end.lng)
      ];

      const now = Date.now();
      const timeSinceLastRequest = now - LAST_REQUEST_TIME.time;

      if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
        console.log(`Rate limiting in effect. Delaying route request for ${MIN_REQUEST_INTERVAL - timeSinceLastRequest}ms`);

        PENDING_REQUESTS.add(cacheKey);

        setTimeout(() => {
          PENDING_REQUESTS.delete(cacheKey);
          setRoutingWaypoints(start, end);
        }, MIN_REQUEST_INTERVAL - timeSinceLastRequest + Math.random() * 500);

        return true;
      }

      if (zoomedOutRoutingControlRef.current) {
        LAST_REQUEST_TIME.time = now;
        PENDING_REQUESTS.add(cacheKey);

        const waypointsCopy = waypoints.map(wp => L.latLng(wp.lat, wp.lng));

        await safeClearRouteLines(zoomedOutRoutingControlRef.current);

        const handleRouteFound = (e) => {
          try {
            if (e.routes && e.routes.length > 0) {
              const routeData = JSON.parse(JSON.stringify(e.routes[0]));

              ROUTE_REQUEST_CACHE.set(cacheKey, {
                waypoints: waypointsCopy,
                route: routeData,
                timestamp: Date.now()
              });

              if (ROUTE_REQUEST_CACHE.size > MAX_CACHE_SIZE) {
                const oldestKey = [...ROUTE_REQUEST_CACHE.entries()]
                  .sort((a, b) => a[1].timestamp - b[1].timestamp)[0][0];
                ROUTE_REQUEST_CACHE.delete(oldestKey);
              }

              updateAllMapsWithRouteData(waypointsCopy, routeData);
              PENDING_REQUESTS.delete(cacheKey);
              zoomedOutRoutingControlRef.current.off('routesfound', handleRouteFound);
            }
          } catch (error) {
            console.warn("Error handling route found event:", error);
            PENDING_REQUESTS.delete(cacheKey);
          }
        };

        const handleRouteError = (e) => {
          console.warn("Routing error:", e);
          PENDING_REQUESTS.delete(cacheKey);

          zoomedOutRoutingControlRef.current.off('routingerror', handleRouteError);
          zoomedOutRoutingControlRef.current.off('routesfound', handleRouteFound);

          if (e.error && e.error.status === 429) {
            MIN_REQUEST_INTERVAL = Math.min(MIN_REQUEST_INTERVAL * 2, 10000);

            console.warn(`Rate limit exceeded. Increasing delay to ${MIN_REQUEST_INTERVAL}ms`);

            setTimeout(() => {
              MIN_REQUEST_INTERVAL = 1000;
            }, 30000);
          }
        };

        zoomedOutRoutingControlRef.current.once('routesfound', handleRouteFound);
        zoomedOutRoutingControlRef.current.once('routingerror', handleRouteError);

        setTimeout(() => {
          try {
            zoomedOutRoutingControlRef.current.setWaypoints(waypointsCopy);
          } catch (error) {
            console.warn("Error setting primary waypoints:", error);
            PENDING_REQUESTS.delete(cacheKey);
            zoomedOutRoutingControlRef.current.off('routesfound', handleRouteFound);
            zoomedOutRoutingControlRef.current.off('routingerror', handleRouteError);
          }
        }, 50);

        return true;
      }

      return false;
    } catch (error) {
      console.error("Error in rate-limited setRoutingWaypoints:", error);
      return false;
    }
  };

  const updateRouteDisplayFromCache = (cachedData) => {
    if (!cachedData || !cachedData.route) return;

    try {
      processRouteInformation(cachedData.route);

      Promise.resolve().then(() => {
        return updateAllMapsWithRouteData(cachedData.waypoints, cachedData.route);
      }).catch(error => {
        console.warn("Error processing cached route:", error);
      });
    } catch (error) {
      console.warn("Error updating from cached route:", error);
    }
  };

  const updateAllMapsWithRouteData = async (waypoints, routeData) => {
    try {
      const updatePromises = [];

      if (zoomedInRoutingControlRef.current) {
        updatePromises.push(
          updateRouteDisplayManually(zoomedInRoutingControlRef.current, waypoints, routeData)
        );
      }

      await Promise.all(updatePromises);
      return true;
    } catch (error) {
      console.warn("Error updating multiple maps with route data:", error);
      return false;
    }
  };

  const updateRouteDisplayManually = async (routingControl, waypoints, routeData) => {
    if (!routingControl) return false;

    try {
      await safeClearRouteLines(routingControl);

      if (!routingControl._container) {
        console.warn("Routing control has no container, skipping manual update");
        return false;
      }

      const freshRouteData = {
        ...routeData,
        name: routeData.name,
        coordinates: [...(routeData.coordinates || [])],
        instructions: [...(routeData.instructions || [])],
        summary: { ...routeData.summary },
        inputWaypoints: waypoints.map(wp => ({ ...wp })),
        waypoints: waypoints.map(wp => ({ ...wp }))
      };

      try {
        routingControl._routes = [freshRouteData];

        if (typeof routingControl._updateLineCallback === 'function') {
          try {
            routingControl._updateLineCallback(freshRouteData);
          } catch (renderError) {
            console.warn("Error in manual route rendering callback:", renderError);
          }
        }
      } catch (controlError) {
        console.warn("Error updating routing control state:", controlError);
        return false;
      }

      return true;
    } catch (error) {
      console.warn("Error manually updating route display:", error);
      return false;
    }
  };

  const processRouteInformation = (route) => {
    if (!route || !route.instructions || route.instructions.length === 0) {
      console.log("No valid route instructions received");
      return;
    }

    try {
      console.log("Processing route information:", route);

      const nextInstruction = route.instructions.find(instr => instr.distance > 0);

      if (nextInstruction) {
        console.log("Next navigation instruction:", nextInstruction);
        setNextManeuver(nextInstruction);
        
        onNavigationStatusChange({
          isNavigating: true,
          nextInstruction: nextInstruction,
          routeSummary: route.summary
        });
      }
    } catch (error) {
      console.error("Error processing route information:", error);
    }
  };

  const cancelNavigation = () => {
    console.log("Canceling navigation...");
    
    try {
      destinationRef.current = null;
      
      if (destinationMarkerRef.current) {
        if (destinationMarkerRef.current.zoomed_out) {
          safeRemoveLayer(zoomedOutMapRef.current, destinationMarkerRef.current.zoomed_out);
        }
        
        if (destinationMarkerRef.current.zoomed_in) {
          safeRemoveLayer(zoomedInMapRef.current, destinationMarkerRef.current.zoomed_in);
        }
        
        destinationMarkerRef.current = null;
      }
      
      const clearPromises = [];
      
      if (zoomedOutRoutingControlRef.current) {
        clearPromises.push(safeClearRouteLines(zoomedOutRoutingControlRef.current));
      }
      
      if (zoomedInRoutingControlRef.current) {
        clearPromises.push(safeClearRouteLines(zoomedInRoutingControlRef.current));
      }
      
      Promise.all(clearPromises).then(() => {
        console.log("All routes cleared");
      });
      
      ROUTE_REQUEST_CACHE.clear();
      PENDING_REQUESTS.clear();
      
      setRouteInfo(null);
      setNextManeuver(null);
      
      if (isRotationEnabled) {
        setMapRotation(0);
        setMapScale(1.5);
      }
      
      onNavigationStatusChange({
        isNavigating: false,
        nextInstruction: null,
        routeSummary: null
      });
      
      console.log("Navigation canceled successfully");
      return true;
      
    } catch (error) {
      console.error("Error canceling navigation:", error);
      return false;
    }
  };

  // Map rotation functions
  const calculateScaleFactor = (angle) => {
    const rad = angle * Math.PI / 180;
    const scale = Math.max(
      Math.abs(Math.cos(rad)) + Math.abs(Math.sin(rad)),
      Math.abs(Math.sin(rad)) + Math.abs(Math.cos(rad))
    );
    return Math.max(scale * 1.2, 1.5);
  };

  const calculateBearing = (startLat, startLng, endLat, endLng) => {
    const startLatRad = startLat * Math.PI / 180;
    const startLngRad = startLng * Math.PI / 180;
    const endLatRad = endLat * Math.PI / 180;
    const endLngRad = endLng * Math.PI / 180;

    const y = Math.sin(endLngRad - startLngRad) * Math.cos(endLatRad);
    const x = Math.cos(startLatRad) * Math.sin(endLatRad) -
      Math.sin(startLatRad) * Math.cos(endLatRad) * Math.cos(endLngRad - startLngRad);

    let bearing = Math.atan2(y, x) * 180 / Math.PI;
    bearing = (bearing + 360) % 360;

    return bearing;
  };

  const updateMapRotation = () => {
    if (!currentLocation || !destinationRef.current || !isRotationEnabled) return;

    const { lat: startLat, lng: startLng } = currentLocation;
    const { lat: endLat, lng: endLng } = destinationRef.current;

    const bearing = calculateBearing(startLat, startLng, endLat, endLng);
    const scaleFactor = calculateScaleFactor(bearing);

    setMapRotation(bearing);
    setMapScale(scaleFactor);

    console.log("MapDisplay: Updated map rotation, bearing:", bearing, "scale:", scaleFactor);

    setTimeout(() => {
      if (zoomedInMapRef.current) {
        zoomedInMapRef.current.invalidateSize();
      }
    }, 300);
  };

  const toggleRotation = () => {
    setIsRotationEnabled(!isRotationEnabled);
    
    if (!isRotationEnabled && destinationRef.current) {
      setTimeout(updateMapRotation, 0);
    }
    
    if (isRotationEnabled) {
      setMapRotation(0);
      setMapScale(1.5);
    }
  };

  const resetRotation = () => {
    setMapRotation(0);
    setMapScale(1.5);
  };

  // Other functions
  const toggleZipBoundaries = () => {
    setShowZipCodes(prevState => {
      const newState = !prevState;

      if (newState) {
        if (ZipCodeBoundaryManager.isLoaded) {
          ZipCodeBoundaryManager.displayBoundaries(
            zoomedOutMapRef.current,
            zoomedInMapRef.current
          );
        } else {
          console.log("Loading ZIP code boundaries...");
          ZipCodeBoundaryManager.stateFeatureCollections = {
            'il': null,
            'in': null,
            'wi': null
          };
          
          ZipCodeBoundaryManager.loadGeoJson({
            zoomedOutMapRef,
            zoomedInMapRef
          }).then(success => {
            if (success) {
              console.log("Successfully loaded all ZIP code boundaries");
              ZipCodeBoundaryManager.displayBoundaries(
                zoomedOutMapRef.current,
                zoomedInMapRef.current
              );
            } else {
              console.error("Failed to load ZIP code boundaries");
            }
          });
        }
      } else {
        ZipCodeBoundaryManager.clearBoundaries(
          zoomedOutMapRef.current,
          zoomedInMapRef.current
        );
      }

      return newState;
    });
  };

  const handleFindMyLocation = () => {
    if (locationTrackingEnabled && currentLocation) {
      console.log("Using current location");
      
      if (zoomedOutMapRef.current) {
        zoomedOutMapRef.current.setView([currentLocation.lat, currentLocation.lng], ZOOMED_OUT_LEVEL);
      }
      
      if (zoomedInMapRef.current) {
        zoomedInMapRef.current.setView([currentLocation.lat, currentLocation.lng], ZOOMED_IN_LEVEL);
      }
      
      return;
    }
    
    console.log("Starting location tracking to find location");
    startContinuousTracking();
  };

  const handleRefreshMap = () => {
    if (zoomedOutMapRef.current && zoomedInMapRef.current) {
      zoomedOutMapRef.current.invalidateSize();
      zoomedInMapRef.current.invalidateSize();
      
      if (currentLocation) {
        updateUserLocationSmooth(currentLocation);
      }
    }
    
    if (showZipCodes && zoomedOutMapRef.current && zoomedInMapRef.current) {
      ZipCodeBoundaryManager.displayBoundaries(
        zoomedOutMapRef.current,
        zoomedInMapRef.current
      );
    }
  };

  // Handle showing vehicle details
  const showVehicleDetails = (vehicle) => {
    console.log("Showing details for vehicle:", vehicle);
    if (onTeamVehicleClick) {
      onTeamVehicleClick(vehicle);
    }
  };

  // Utility function to calculate distance between two points
  const calculateDistance = (point1, point2) => {
    if (!point1 || !point2 || !point1.lat || !point1.lng || !point2.lat || !point2.lng) {
      return 999999;
    }
    
    const lat1 = point1.lat;
    const lon1 = point1.lng;
    const lat2 = point2.lat;
    const lon2 = point2.lng;
    
    const R = 3959; // Radius of the Earth in miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Format distance for display
  const formatDistance = (distance) => {
    if (distance < 0.1) {
      return `${Math.round(distance * 5280)} ft`;
    } else {
      return `${distance.toFixed(1)} mi`;
    }
  };

  // Function to display vehicle markers from addresses
  const displayVehicleMarkers = useCallback(async (vehicles) => {
    if (!vehicles || !vehicles.length || !zoomedOutMapRef.current || !zoomedInMapRef.current) {
      return;
    }
    
    // Clear any existing vehicle markers first
    clearTeamVehicleMarkers();
    
    console.log(`🚗 Processing ${vehicles.length} vehicles for map display`);
    
    let markersAdded = 0;
    
    for (const vehicle of vehicles) {
      let position = null;
      
      // Use existing position if available
      if (vehicle.position && vehicle.position.lat && vehicle.position.lng) {
        position = vehicle.position;
      } else if (vehicle.lat && vehicle.lng) {
        position = { lat: vehicle.lat, lng: vehicle.lng };
      } else if (vehicle.address) {
        // Try to geocode the address
        position = await geocodeAddress(vehicle.address);
        if (position) {
          console.log(`Geocoded ${vehicle.vehicle} to:`, position);
        }
      }
      
      if (position) {
        // Create marker for this vehicle
        const icon = createTeamVehicleIcon(vehicle, 36);
        
        const zoomedOutMarker = L.marker([position.lat, position.lng], {
          icon: icon,
          title: `🚗 ${vehicle.vehicle || "Vehicle"} - ${vehicle.vin || ""}`,
          zIndexOffset: 2000,
          riseOnHover: true
        });
        
        const zoomedInMarker = L.marker([position.lat, position.lng], {
          icon: icon,
          title: `🚗 ${vehicle.vehicle || "Vehicle"} - ${vehicle.vin || ""}`,
          zIndexOffset: 2000,
          riseOnHover: true
        });
        
        // Add popup with vehicle info
        const popupContent = `
          <div class="team-vehicle-popup" style="min-width: 300px; max-width: 400px;">
            <div style="background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%); color: white; padding: 12px; margin: -20px -20px 12px -20px; border-radius: 8px 8px 0 0;">
              <h3 style="margin: 0; font-size: 18px; font-weight: bold; display: flex; align-items: center;">
                <span style="font-size: 24px; margin-right: 8px;">🚗</span>
                ${vehicle.isTeamVehicle ? 'TEAM VEHICLE' : 'VEHICLE'}
              </h3>
            </div>
            
            <div style="background: #F3F4F6; padding: 12px; border-radius: 8px; margin-bottom: 12px;">
              <h4 style="font-weight: bold; margin: 0 0 8px 0; font-size: 20px; color: #1F2937;">
                ${vehicle.vehicle || "Unknown Vehicle"}
              </h4>
              
              <div style="display: grid; grid-template-columns: auto 1fr; gap: 8px; font-size: 14px; color: #4B5563;">
                ${vehicle.vin ? `<strong>VIN:</strong> <span style="font-family: monospace;">${vehicle.vin}</span>` : ''}
                ${vehicle.plateNumber ? `<strong>Plate:</strong> <span>${vehicle.plateNumber}</span>` : ''}
                ${vehicle.address ? `<strong>Address:</strong> <span>${vehicle.address}</span>` : ''}
                ${vehicle.teamMemberName ? `<strong>Member:</strong> <span>${vehicle.teamMemberName}</span>` : ''}
                ${vehicle.status ? `<strong>Status:</strong> <span>${vehicle.status}</span>` : ''}
              </div>
            </div>
            
            <div style="display: flex; gap: 10px; margin-top: 16px;">
              <button id="team-vehicle-details-btn-${vehicle.id || vehicle.uniqueKey}" style="
                flex: 1;
                background: linear-gradient(135deg, #6366F1 0%, #4F46E5 100%);
                color: white;
                border: none;
                padding: 12px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 600;
                font-size: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                transition: all 0.2s;
              ">
                View Full Details
              </button>
              <button id="team-vehicle-navigate-btn-${vehicle.id || vehicle.uniqueKey}" style="
                flex: 1;
                background: linear-gradient(135deg, #10B981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 12px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 600;
                font-size: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                transition: all 0.2s;
              ">
                Navigate to Vehicle
              </button>
            </div>
          </div>
        `;
        
        zoomedOutMarker.bindPopup(popupContent, { 
          maxWidth: 400,
          className: 'team-vehicle-popup-wrapper',
          offset: [0, -20]
        });
        
        zoomedInMarker.bindPopup(popupContent, { 
          maxWidth: 400,
          className: 'team-vehicle-popup-wrapper',
          offset: [0, -20]
        });
        
        const setupPopupHandlers = (marker) => {
          marker.on('popupopen', () => {
            setTimeout(() => {
              const detailsBtn = document.getElementById(`team-vehicle-details-btn-${vehicle.id || vehicle.uniqueKey}`);
              if (detailsBtn) {
                detailsBtn.addEventListener('click', () => {
                  console.log("Opening details for vehicle:", vehicle);
                  if (onTeamVehicleClick) {
                    onTeamVehicleClick(vehicle);
                  }
                  marker.closePopup();
                });
              }

              const navigateBtn = document.getElementById(`team-vehicle-navigate-btn-${vehicle.id || vehicle.uniqueKey}`);
              if (navigateBtn) {
                navigateBtn.addEventListener('click', () => {
                  console.log("Navigating to vehicle:", vehicle);
                  if (onTeamVehicleNavigate) {
                    onTeamVehicleNavigate(vehicle);
                  } else {
                    setDestination(position.lat, position.lng);
                  }
                  marker.closePopup();
                });
              }
            }, 10);
          });
        };

        setupPopupHandlers(zoomedOutMarker);
        setupPopupHandlers(zoomedInMarker);
        
        // Add markers to maps
        zoomedOutMapRef.current.addLayer(zoomedOutMarker);
        zoomedInMapRef.current.addLayer(zoomedInMarker);
        
        // Store markers for later cleanup
        const markerId = vehicle.uniqueKey || vehicle.id || `vehicle-${Date.now()}-${Math.random()}`;
        teamVehicleMarkersRef.current.zoomedOut.set(markerId, zoomedOutMarker);
        teamVehicleMarkersRef.current.zoomedIn.set(markerId, zoomedInMarker);
        
        markersAdded++;
        console.log(`✅ Added marker for ${vehicle.vehicle} at ${position.lat}, ${position.lng}`);
      } else {
        console.warn(`❌ No position available for vehicle: ${vehicle.vehicle}`);
      }
    }
    
    console.log(`🎯 Successfully added ${markersAdded}/${vehicles.length} vehicle markers`);
  }, [createTeamVehicleIcon, geocodeAddress, onTeamVehicleClick, onTeamVehicleNavigate, setDestination]);

  // Team vehicle marker management
  const addTeamVehicleMarkers = useCallback(() => {
    if (!processedVehiclesForMap || processedVehiclesForMap.length === 0) {
      console.log("No vehicles to display");
      clearTeamVehicleMarkers();
      return;
    }

    if (!zoomedOutMapRef.current || !zoomedInMapRef.current) {
      console.log("Maps not ready for vehicle markers");
      return;
    }

    console.log(`🚗 Processing vehicles for display`);
    
    displayVehicleMarkers(processedVehiclesForMap);
    
  }, [processedVehiclesForMap, displayVehicleMarkers]);

  const clearTeamVehicleMarkers = useCallback(() => {
    console.log("Clearing team vehicle markers");

    teamVehicleMarkersRef.current.zoomedOut.forEach((marker, id) => {
      try {
        if (zoomedOutMarkerClusterRef.current && zoomedOutMarkerClusterRef.current.hasLayer(marker)) {
          zoomedOutMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedOutMapRef.current && zoomedOutMapRef.current.hasLayer(marker)) {
          zoomedOutMapRef.current.removeLayer(marker);
        }
      } catch (error) {
        console.warn(`Error removing zoomed out team vehicle marker ${id}:`, error);
      }
    });

    teamVehicleMarkersRef.current.zoomedIn.forEach((marker, id) => {
      try {
        if (zoomedInMarkerClusterRef.current && zoomedInMarkerClusterRef.current.hasLayer(marker)) {
          zoomedInMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedInMapRef.current && zoomedInMapRef.current.hasLayer(marker)) {
          zoomedInMapRef.current.removeLayer(marker);
        }
      } catch (error) {
        console.warn(`Error removing zoomed in team vehicle marker ${id}:`, error);
      }
    });

    teamVehicleMarkersRef.current.zoomedOut.clear();
    teamVehicleMarkersRef.current.zoomedIn.clear();
  }, []);

  // Define clearProcessedMarkers
  const clearProcessedMarkers = useCallback(() => {
    console.log("🗑️ Clearing processed markers");

    processedMarkersRef.current.zoomedOut.forEach((marker, id) => {
      try {
        if (zoomedOutMarkerClusterRef.current && zoomedOutMarkerClusterRef.current.hasLayer(marker)) {
          zoomedOutMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedOutUserMarkerClusterRef.current && zoomedOutUserMarkerClusterRef.current.hasLayer(marker)) {
          zoomedOutUserMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedOutMapRef.current && zoomedOutMapRef.current.hasLayer(marker)) {
          zoomedOutMapRef.current.removeLayer(marker);
        }
      } catch (error) {
        console.warn(`Error removing zoomed out marker ${id}:`, error);
      }
    });

    processedMarkersRef.current.zoomedIn.forEach((marker, id) => {
      try {
        if (zoomedInMarkerClusterRef.current && zoomedInMarkerClusterRef.current.hasLayer(marker)) {
          zoomedInMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedInUserMarkerClusterRef.current && zoomedInUserMarkerClusterRef.current.hasLayer(marker)) {
          zoomedInUserMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedInMapRef.current && zoomedInMapRef.current.hasLayer(marker)) {
          zoomedInMapRef.current.removeLayer(marker);
        }
      } catch (error) {
        console.warn(`Error removing zoomed in marker ${id}:`, error);
      }
    });

    processedMarkersRef.current.zoomedOut.clear();
    processedMarkersRef.current.zoomedIn.clear();
  }, []);

  // Define addProcessedMarkers
  const addProcessedMarkers = useCallback((markersToProcess) => {
    const markers = markersToProcess || processedMarkers;
    
    if (!markers || !zoomedOutMapRef.current || !zoomedInMapRef.current) {
      console.log("⏳ Cannot add markers - missing processed data or maps not ready");
      return;
    }

    console.log("🔄 Adding processed markers to maps", {
      orderMarkers: markers.orderMarkers?.length || 0,
      manualMarkers: markers.manualMarkers?.length || 0,
      teamMarkers: markers.teamMarkers?.length || 0
    });

    clearProcessedMarkers();

    const allMarkers = [
      ...(markers.orderMarkers || []),
      ...(markers.manualMarkers || []),
      ...(markers.teamMarkers || [])
    ];

    let addedCount = 0;

    allMarkers.forEach(markerData => {
      try {
        const zoomedOutMarker = createMarkerFromProcessedData(markerData, false);
        const zoomedInMarker = createMarkerFromProcessedData(markerData, true);

        if (zoomedOutMarker && zoomedInMarker) {
          let zoomedOutAdded = false;
          let zoomedInAdded = false;

          let targetZoomedOutCluster, targetZoomedInCluster;

          if (markerData.category === 'user') {
            targetZoomedOutCluster = zoomedOutUserMarkerClusterRef.current;
            targetZoomedInCluster = zoomedInUserMarkerClusterRef.current;
          } else {
            targetZoomedOutCluster = zoomedOutMarkerClusterRef.current;
            targetZoomedInCluster = zoomedInMarkerClusterRef.current;
          }

          try {
            if (targetZoomedOutCluster) {
              targetZoomedOutCluster.addLayer(zoomedOutMarker);
              zoomedOutAdded = true;
            } else if (zoomedOutMapRef.current) {
              zoomedOutMapRef.current.addLayer(zoomedOutMarker);
              zoomedOutAdded = true;
            }
          } catch (e) {
            console.warn(`Error adding ${markerData.id} to zoomed out map:`, e);
            if (zoomedOutMapRef.current) {
              try {
                zoomedOutMapRef.current.addLayer(zoomedOutMarker);
                zoomedOutAdded = true;
              } catch (e2) {
                console.error(`Failed to add ${markerData.id} to zoomed out map:`, e2);
              }
            }
          }

          try {
            if (targetZoomedInCluster) {
              targetZoomedInCluster.addLayer(zoomedInMarker);
              zoomedInAdded = true;
            } else if (zoomedInMapRef.current) {
              zoomedInMapRef.current.addLayer(zoomedInMarker);
              zoomedInAdded = true;
            }
          } catch (e) {
            console.warn(`Error adding ${markerData.id} to zoomed in map:`, e);
            if (zoomedInMapRef.current) {
              try {
                zoomedInMapRef.current.addLayer(zoomedInMarker);
                zoomedInAdded = true;
              } catch (e2) {
                console.error(`Failed to add ${markerData.id} to zoomed in map:`, e2);
              }
            }
          }

          if (zoomedOutAdded && zoomedInAdded) {
            processedMarkersRef.current.zoomedOut.set(markerData.id, zoomedOutMarker);
            processedMarkersRef.current.zoomedIn.set(markerData.id, zoomedInMarker);
            addedCount++;
          }
        }
      } catch (error) {
        console.error(`Error processing marker ${markerData.id}:`, error);
      }
    });

    console.log(`✅ Successfully added ${addedCount}/${allMarkers.length} processed markers to maps`);

    setTimeout(() => {
      if (zoomedOutMapRef.current) {
        zoomedOutMapRef.current.invalidateSize();
      }
      if (zoomedInMapRef.current) {
        zoomedInMapRef.current.invalidateSize();
      }
    }, 100);
  }, [processedMarkers, createMarkerFromProcessedData, clearProcessedMarkers]);

  // Routing control creation - Fixed
  const createRoutingControl = (map, options = {}) => {
    if (!map) return null;

    try {
      // Check if L.Routing exists
      if (!L.Routing || !L.Routing.control || !L.Routing.plan) {
        console.error("Leaflet Routing Machine not properly loaded");
        return null;
      }

      // Remove any existing routing control
      let existingControl = null;
      map.eachLayer((layer) => {
        if (layer && layer._plan && layer._routes) {
          existingControl = layer;
        }
      });

      if (existingControl) {
        safelyDisposeRoutingControl(existingControl);
      }

      // Create routing options with default values
      const routingOptions = {
        show: false,
        collapsible: true,
        showAlternatives: false,
        routeWhileDragging: false,
        fitSelectedRoutes: false,
        draggableWaypoints: false,
        autoRoute: true,
        addWaypoints: false,
        useZoomParameter: false,
        lineOptions: {
          styles: [
            { color: '#4a89f3', opacity: 0.8, weight: 5 },
            { color: '#2a69d3', opacity: 0.9, weight: 3, className: 'routing-line-inner' }
          ],
          extendToWaypoints: true,
          missingRouteTolerance: 10,
          className: 'custom-route-path'
        },
        createMarker: function () { return null; },
        plan: L.Routing.plan([], {
          createMarker: function() { return null; },
          routeWhileDragging: false,
          addWaypoints: false
        }),
        router: L.Routing.osrmv1({
          serviceUrl: 'https://router.project-osrm.org/route/v1',
          profile: 'driving',
          suppressDemoServerWarning: true,
          timeout: 30 * 1000,
          geometryOnly: false
        }),
        ...options
      };

      const routingControl = L.Routing.control(routingOptions);

      // Add event handlers
      routingControl.on('routingerror', (e) => {
        console.warn('Routing error encountered:', e.error);

        if (e.error && e.error.status === 429) {
          console.warn('Rate limit exceeded (429), implementing exponential backoff');
          MIN_REQUEST_INTERVAL = Math.min(MIN_REQUEST_INTERVAL * 2, 10000);

          setTimeout(() => {
            MIN_REQUEST_INTERVAL = 1000;
          }, 30000);
        }

        if (routingControl._routes) {
          try {
            safeClearRouteLines(routingControl);
          } catch (clearError) {
            console.warn('Error clearing routes after routing error:', clearError);
          }
        }
      });

      routingControl.on('routesfound', (e) => {
        const routes = e.routes;
        if (routes && routes.length > 0) {
          const primaryRoute = routes[0];
          setRouteInfo(primaryRoute);
          processRouteInformation(primaryRoute);
        }
      });

      // Add to map after a delay
      setTimeout(() => {
        try {
          if (map && map._container) {
            routingControl.addTo(map);
            console.log("Routing control added to map successfully");
          } else {
            console.warn("Map not ready for routing control");
          }
        } catch (error) {
          console.warn("Error adding routing control to map:", error);
        }
      }, 500);

      return routingControl;
    } catch (error) {
      console.error("Error creating routing control:", error);
      return null;
    }
  };

  const safelyDisposeRoutingControl = (routingControl) => {
    if (!routingControl) return Promise.resolve();

    return new Promise((resolve) => {
      setTimeout(async () => {
        try {
          await safeClearRouteLines(routingControl);

          try {
            if (routingControl._map) {
              routingControl.removeFrom(routingControl._map);
            }
          } catch (e) {
            console.warn("Error removing routing control from map:", e);
          }

          try {
            routingControl._map = null;
            routingControl._container = null;
            routingControl._routes = [];
          } catch (e) {
            console.warn("Error clearing routing control references:", e);
          }

          resolve();
        } catch (e) {
          console.warn("Error disposing routing control:", e);
          resolve();
        }
      }, 100);
    });
  };

  // Container dimension calculations
  const calculateContainerDimensions = (containerType) => {
    const { width, height, isSmallScreen, isMediumScreen } = screenConfig;
    const isLandscape = width > height;
    const isPad = width >= 768 && width <= 1024;

    const baseStyles = {
      minHeight: "250px",
      width: "100%",
      flex: "1",
      display: "flex",
      flexDirection: "column"
    };

    if (!isSmallScreen && !isMediumScreen) {
      return {
        ...baseStyles,
        height: "50%",
        minHeight: "300px",
      };
    }

    if (isPad && isLandscape) {
      return {
        ...baseStyles,
        height: "50%",
        minHeight: "250px"
      };
    }

    if (isPad && !isLandscape) {
      return {
        ...baseStyles,
        height: "50%",
        minHeight: "250px"
      };
    }

    if (isSmallScreen) {
      if (isLandscape) {
        return {
          ...baseStyles,
          height: "50%",
          minHeight: "150px"
        };
      } else {
        return {
          ...baseStyles,
          height: "50%",
          minHeight: "175px"
        };
      }
    }

    return {
      ...baseStyles,
      height: "50%",
      minHeight: "250px"
    };
  };

  const ensureContainerDimensions = (container, type) => {
    if (!container) return;

    const dimensions = calculateContainerDimensions(type);

    Object.entries(dimensions).forEach(([key, value]) => {
      container.style[key] = value;
    });

    console.log(`Map container dimensions (${type}):`, {
      offsetWidth: container.offsetWidth,
      offsetHeight: container.offsetHeight,
      appliedStyles: dimensions
    });
  };

  // Initialize maps
  useEffect(() => {
    if (!zoomedOutContainerRef.current || !zoomedInContainerRef.current) {
      return;
    }

    if (zoomedOutMapRef.current && zoomedInMapRef.current) {
      console.log("Maps already exist");
      setInternalLoading(false);
      return;
    }

    console.log("Starting map initialization");

    try {
      // Check if required Leaflet plugins are loaded
      if (!L.markerClusterGroup) {
        console.error("Leaflet MarkerCluster plugin not loaded");
        setError("Required map plugins not loaded. Please refresh the page.");
        setInternalLoading(false);
        return;
      }

      if (enableNavigation && (!L.Routing || !L.Routing.control)) {
        console.error("Leaflet Routing Machine plugin not loaded");
        setError("Navigation plugin not loaded. Some features may be unavailable.");
      }

      ensureContainerDimensions(zoomedOutContainerRef.current, 'zoomedOut');
      ensureContainerDimensions(zoomedInContainerRef.current, 'zoomedIn');

      const zoomedOutMap = L.map(zoomedOutContainerRef.current, {
        zoomControl: false,
        dragging: true,
        touchZoom: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        boxZoom: true,
        keyboard: true,
        attributionControl: true,
        preferCanvas: false,
        zIndex: 10
      }).setView([
        currentLocation?.lat || 37.7749,
        currentLocation?.lng || -122.4194
      ], ZOOMED_OUT_LEVEL);

      const zoomedInMap = L.map(zoomedInContainerRef.current, {
        zoomControl: false,
        dragging: true,
        touchZoom: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        boxZoom: true,
        keyboard: true,
        attributionControl: true,
        preferCanvas: false,
        zIndex: 10
      }).setView([
        currentLocation?.lat || 37.7749,
        currentLocation?.lng || -122.4194
      ], ZOOMED_IN_LEVEL);

      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
      }).addTo(zoomedOutMap);

      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
      }).addTo(zoomedInMap);

      zoomedOutMapRef.current = zoomedOutMap;
      zoomedInMapRef.current = zoomedInMap;

      if (typeof window !== 'undefined') {
        window.zoomedOutMapInstance = zoomedOutMap;
        window.zoomedInMapInstance = zoomedInMap;
      }

      console.log("Maps created successfully!");

      // Create marker clusters
      zoomedOutMarkerClusterRef.current = L.markerClusterGroup({
        animate: false,
        animateAddingMarkers: false,
        maxClusterRadius: 40,
        spiderfyOnMaxZoom: true,
        showCoverageOnHover: false,
        zoomToBoundsOnClick: true
      }).addTo(zoomedOutMap);

      zoomedInMarkerClusterRef.current = L.markerClusterGroup({
        animate: false,
        animateAddingMarkers: false,
        maxClusterRadius: 30,
        spiderfyOnMaxZoom: true,
        showCoverageOnHover: false,
        zoomToBoundsOnClick: true
      }).addTo(zoomedInMap);

      zoomedOutUserMarkerClusterRef.current = L.markerClusterGroup({
        animate: false,
        animateAddingMarkers: false,
        maxClusterRadius: 40,
        spiderfyOnMaxZoom: true,
        showCoverageOnHover: false,
        zoomToBoundsOnClick: true
      }).addTo(zoomedOutMap);

      zoomedInUserMarkerClusterRef.current = L.markerClusterGroup({
        animate: false,
        animateAddingMarkers: false,
        maxClusterRadius: 30,
        spiderfyOnMaxZoom: true,
        showCoverageOnHover: false,
        zoomToBoundsOnClick: true
      }).addTo(zoomedInMap);

      // Add drag event handlers
      zoomedOutMap.on('dragstart', () => {
        userHasDraggedMap.current = true;
      });

      zoomedInMap.on('dragstart', () => {
        userHasDraggedMap.current = true;
      });

      zoomedOutMap.on('dragend', () => {
        setTimeout(() => {
          userHasDraggedMap.current = false;
        }, 5000);
      });

      zoomedInMap.on('dragend', () => {
        setTimeout(() => {
          userHasDraggedMap.current = false;
        }, 5000);
      });

      // Add routing controls
      if (enableNavigation) {
        setTimeout(() => {
          console.log("Creating routing controls");
          
          // Check if L.Routing exists
          if (!L.Routing || !L.Routing.control || !L.Routing.plan) {
            console.error("Leaflet Routing Machine not loaded");
            return;
          }

          try {
            zoomedOutRoutingControlRef.current = createRoutingControl(zoomedOutMap);
          } catch (error) {
            console.warn("Failed to create zoomed out routing control:", error);
          }

          setTimeout(() => {
            try {
              zoomedInRoutingControlRef.current = createRoutingControl(zoomedInMap);
            } catch (error) {
              console.warn("Failed to create zoomed in routing control:", error);
            }
          }, 250);
        }, 500);
      }

      // Add click handlers
      zoomedOutMap.on('click', (e) => {
        onMapClick(e);

        const newCenter = e.latlng;
        zoomedOutMap.setView(newCenter, ZOOMED_OUT_LEVEL, { animate: true });
        zoomedInMap.setView(newCenter, ZOOMED_IN_LEVEL, { animate: true });

        if (isRotationEnabled) {
          setDestination(newCenter.lat, newCenter.lng);
        }
      });

      zoomedInMap.on('click', (e) => {
        onMapClick(e);

        const newCenter = e.latlng;
        zoomedOutMap.setView(newCenter, ZOOMED_OUT_LEVEL, { animate: true });
        zoomedInMap.setView(newCenter, ZOOMED_IN_LEVEL, { animate: true });

        if (isRotationEnabled) {
          setDestination(newCenter.lat, newCenter.lng);
        }
      });

      // Add context menus
      zoomedOutMap.on('contextmenu', (e) => {
        const contextMenu = L.popup({
          closeButton: false,
          className: 'context-menu-popup',
          offset: [0, 0]
        })
          .setLatLng(e.latlng)
          .setContent(`
          <div class="context-menu">
            <button class="context-menu-item" id="center-map-btn">Center maps here</button>
            <button class="context-menu-item" id="set-location-btn">Set my location here</button>
            <button class="context-menu-item" id="navigate-here-btn">Navigate to this point</button>
          </div>
        `)
          .openOn(zoomedOutMap);

        setTimeout(() => {
          document.getElementById('center-map-btn')?.addEventListener('click', () => {
            zoomedOutMap.setView(e.latlng, ZOOMED_OUT_LEVEL, { animate: true });
            zoomedInMap.setView(e.latlng, ZOOMED_IN_LEVEL, { animate: true });
            zoomedOutMap.closePopup();
          });

          document.getElementById('set-location-btn')?.addEventListener('click', () => {
            const newLocation = {
              lat: e.latlng.lat,
              lng: e.latlng.lng,
              timestamp: Date.now()
            };
            
            updateUserLocationSmooth(newLocation);
            zoomedOutMap.closePopup();
          });

          document.getElementById('navigate-here-btn')?.addEventListener('click', () => {
            setDestination(e.latlng.lat, e.latlng.lng);
            zoomedOutMap.closePopup();
          });
        }, 10);
      });

      zoomedInMap.on('contextmenu', (e) => {
        const contextMenu = L.popup({
          closeButton: false,
          className: 'context-menu-popup',
          offset: [0, 0]
        })
          .setLatLng(e.latlng)
          .setContent(`
          <div class="context-menu">
            <button class="context-menu-item" id="center-maps-btn">Center maps here</button>
            <button class="context-menu-item" id="set-location-btn">Set my location here</button>
            <button class="context-menu-item" id="navigate-here-btn">Navigate to this point</button>
          </div>
        `)
          .openOn(zoomedInMap);

        setTimeout(() => {
          document.getElementById('center-maps-btn')?.addEventListener('click', () => {
            zoomedOutMap.setView(e.latlng, ZOOMED_OUT_LEVEL, { animate: true });
            zoomedInMap.setView(e.latlng, ZOOMED_IN_LEVEL, { animate: true });
            zoomedInMap.closePopup();
          });

          document.getElementById('set-location-btn')?.addEventListener('click', () => {
            const newLocation = {
              lat: e.latlng.lat,
              lng: e.latlng.lng,
              timestamp: Date.now()
            };
            
            updateUserLocationSmooth(newLocation);
            zoomedInMap.closePopup();
          });

          document.getElementById('navigate-here-btn')?.addEventListener('click', () => {
            setDestination(e.latlng.lat, e.latlng.lng);
            zoomedInMap.closePopup();
          });
        }, 10);
      });

      // Invalidate size
      setTimeout(() => {
        if (zoomedOutMapRef.current) {
          zoomedOutMapRef.current.invalidateSize({
            animate: false,
            pan: false
          });
        }
        if (zoomedInMapRef.current) {
          zoomedInMapRef.current.invalidateSize({
            animate: false,
            pan: false
          });
        }
      }, 200);

      // Show initial location
      if (currentLocation && currentLocation.lat && currentLocation.lng) {
        console.log("Showing initial user location:", currentLocation);
        setTimeout(() => {
          updateUserLocationSmooth(currentLocation);
        }, 300);
      }

      // Start location tracking
      if (enableLocationTracking && !locationWatchIdRef.current) {
        console.log("Starting location tracking from initialization");
        startContinuousTracking();
      }

      // Load ZIP boundaries if enabled
      if (showZipCodes) {
        setTimeout(() => {
          console.log("Loading ZIP code boundaries");
          
          ZipCodeBoundaryManager.stateFeatureCollections = {
            'il': null,
            'in': null,
            'wi': null
          };
          
          ZipCodeBoundaryManager.loadGeoJson({
            zoomedOutMapRef,
            zoomedInMapRef
          }).then(success => {
            if (success) {
              console.log("Successfully loaded all ZIP code boundaries");
              
              if (zoomedOutMapRef.current && zoomedInMapRef.current) {
                ZipCodeBoundaryManager.displayBoundaries(
                  zoomedOutMapRef.current,
                  zoomedInMapRef.current
                );
              }
            }
          });
        }, 1000);
      }

      setInternalLoading(false);

    } catch (err) {
      console.error("Error during maps initialization:", err);
      setError(`Failed to initialize maps: ${err.message}`);
      setInternalLoading(false);
    }

    // Cleanup function
    return () => {
      if (locationWatchIdRef.current !== null) {
        console.log("Cleaning up location tracking...");
        navigator.geolocation.clearWatch(locationWatchIdRef.current);
        locationWatchIdRef.current = null;
      }
      
      if (zoomedOutMapRef.current) {
        try {
          if (zoomedOutRoutingControlRef.current) {
            try {
              safeClearRouteLines(zoomedOutRoutingControlRef.current);
              zoomedOutRoutingControlRef.current.remove();
              zoomedOutRoutingControlRef.current = null;
            } catch (error) {
              console.warn("Error cleaning up zoomed out routing control:", error);
            }
          }

          zoomedOutMapRef.current.off();
          zoomedOutMapRef.current.remove();
        } catch (err) {
          console.warn("Error cleaning up zoomed out map:", err);
        }
      }

      if (zoomedInMapRef.current) {
        try {
          if (zoomedInRoutingControlRef.current) {
            try {
              safeClearRouteLines(zoomedInRoutingControlRef.current);
              zoomedInRoutingControlRef.current.remove();
              zoomedInRoutingControlRef.current = null;
            } catch (error) {
              console.warn("Error cleaning up zoomed in routing control:", error);
            }
          }

          zoomedInMapRef.current.off();
          zoomedInMapRef.current.remove();
        } catch (err) {
          console.warn("Error cleaning up zoomed in map:", err);
        }
      }

      ZipCodeBoundaryManager.clearBoundaries(zoomedOutMapRef.current, zoomedInMapRef.current);

      if (typeof window !== 'undefined') {
        window.zoomedOutMapInstance = null;
        window.zoomedInMapInstance = null;
      }

      zoomedOutMapRef.current = null;
      zoomedInMapRef.current = null;
    };
  }, []);

  // Start continuous location tracking on mount
  useEffect(() => {
    if (enableLocationTracking && !locationWatchIdRef.current) {
      console.log("Starting location tracking from mount effect");
      const watchId = startContinuousTracking();
      locationWatchIdRef.current = watchId;
    }
    
    return () => {
      if (locationWatchIdRef.current !== null) {
        console.log("Stopping continuous location tracking on unmount");
        navigator.geolocation.clearWatch(locationWatchIdRef.current);
        locationWatchIdRef.current = null;
      }
    };
  }, [enableLocationTracking]);

  // Update rotation when current location changes
  useEffect(() => {
    if (currentLocation && destinationRef.current && isRotationEnabled) {
      updateMapRotation();
    }
  }, [currentLocation, isRotationEnabled]);

  // Handle all vehicle changes (both user and team vehicles)
  useEffect(() => {
    if (!processedVehiclesForMap || processedVehiclesForMap.length === 0) {
      clearTeamVehicleMarkers();
      return;
    }

    if (!zoomedOutMapRef.current || !zoomedInMapRef.current) {
      console.log("Maps not ready for vehicle markers");
      return;
    }

    console.log("Vehicles changed, updating markers");
    addTeamVehicleMarkers();

    return () => {
      clearTeamVehicleMarkers();
    };
  }, [processedVehiclesForMap, addTeamVehicleMarkers, clearTeamVehicleMarkers]);
  // Handle processed markers changes
  useEffect(() => {
    if (!processedMarkers) {
      console.log("📭 No processed markers available");
      clearProcessedMarkers();
      return;
    }

    if (!zoomedOutMapRef.current || !zoomedInMapRef.current) {
      console.log("🗺️ Maps not ready yet for marker placement");
      return;
    }

    console.log("🔄 Processed markers changed, updating map display", processedMarkers.summary);
    addProcessedMarkers();

    return () => {
      clearProcessedMarkers();
    };
  }, [processedMarkers, addProcessedMarkers, clearProcessedMarkers]);

  // Handle vehicles with addresses but without coordinates
  useEffect(() => {
    const processAddresses = async () => {
      if (!processedVehiclesForMap || processedVehiclesForMap.length === 0 || !zoomedOutMapRef.current || !zoomedInMapRef.current) {
        return;
      }
      
      console.log("Checking for vehicles with addresses that need geocoding");
      
      const vehiclesNeedingGeocoding = processedVehiclesForMap.filter(vehicle => 
        !vehicle.position && !vehicle.lat && !vehicle.lng && vehicle.address
      );
      
      if (vehiclesNeedingGeocoding.length === 0) {
        return;
      }
      
      console.log(`Found ${vehiclesNeedingGeocoding.length} vehicles that need geocoding`);
      
      // Directly update vehicle markers with geocoding
      addTeamVehicleMarkers();
    };
    
    processAddresses();
  }, [processedVehiclesForMap, addTeamVehicleMarkers]);

  // Expose functions globally
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.setMapDestination = setDestination;
      window.toggleMapRotation = toggleRotation;
      window.resetMapRotation = resetRotation;
      window.updateUserLocation = updateUserLocationSmooth;
      window.toggleZipBoundaries = toggleZipBoundaries;
      window.toggleLocationTracking = toggleLocationTracking;
      window.cancelNavigation = cancelNavigation;
      window.handleFindMyLocation = handleFindMyLocation;
      window.handleRefreshMap = handleRefreshMap;
      window.clearProcessedMarkers = clearProcessedMarkers;
      window.geocodeAddress = geocodeAddress;
      
      console.log("Standalone map functions available globally");
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        delete window.setMapDestination;
        delete window.toggleMapRotation;
        delete window.resetMapRotation;
        delete window.updateUserLocation;
        delete window.toggleZipBoundaries;
        delete window.toggleLocationTracking;
        delete window.cancelNavigation;
        delete window.handleFindMyLocation;
        delete window.handleRefreshMap;
        delete window.clearProcessedMarkers;
        delete window.geocodeAddress;
      }
    };
  }, [setDestination, toggleRotation, resetRotation, updateUserLocationSmooth, toggleZipBoundaries, toggleLocationTracking, cancelNavigation, handleFindMyLocation, handleRefreshMap, clearProcessedMarkers, geocodeAddress]);

  const containerLayout = { flexDirection: "column", gap: "16px" };

  return (
    <div className="map-container-wrapper relative flex" style={{ 
      width: "100%", 
      height: "100%", 
      zIndex: 10,
      backgroundColor: '#1f2937',
      ...containerLayout
    }}>
      {/* Detailed Map (zoomed in) - ALWAYS ON TOP - WITH ROTATION */}
      <div className="zoomed-in-map-wrapper relative flex flex-col" style={{ flex: 1 }}>
        <div className="text-center text-white text-sm bg-gray-800 py-1 mb-1 rounded-t-lg flex justify-between items-center px-2">
          <span>Detailed Map</span>
          <div className="flex items-center">
            <button 
              className={`text-xs px-2 py-1 rounded ${isRotationEnabled ? 'bg-green-600' : 'bg-gray-600'}`}
              onClick={toggleRotation}
              title={isRotationEnabled ? "Disable auto-rotation" : "Enable auto-rotation"}
            >
              {isRotationEnabled ? "Auto-Rotate: ON" : "Auto-Rotate: OFF"}
            </button>
          </div>
        </div>
        <div 
          className="zoomed-in-map-container relative border border-gray-700 rounded-lg overflow-hidden"
          style={{ 
            flex: 1,
            position: "relative"
          }}
        >
          <div 
            className="absolute inset-0 transform-wrapper"
            style={{
              transform: isRotationEnabled ? `rotate(${mapRotation}deg) scale(${mapScale})` : "none",
              transition: "transform 0.3s ease-out",
              transformOrigin: "center center",
              willChange: "transform"
            }}
          >
            <div 
              ref={zoomedInContainerRef} 
              className="absolute inset-0 leaflet-container"
            />
          </div>
          
          {isRotationEnabled && destinationRef.current && (
            <div className="absolute top-4 left-4 rotate-indicator z-50">
              <div className="bg-black bg-opacity-50 px-2 py-1 rounded text-white text-xs flex items-center">
                <svg className="w-4 h-4 mr-1 text-green-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                </svg>
                Rotating
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Overview Map (zoomed out) - ALWAYS AT BOTTOM */}
      <div className="zoomed-out-map-wrapper relative flex flex-col" style={{ flex: 1 }}>
        <div className="text-center text-white text-sm bg-gray-800 py-1 mb-1 rounded-t-lg">
          Overview Map
        </div>
        <div 
          ref={zoomedOutContainerRef} 
          className="w-full rounded-lg border border-gray-700 leaflet-container flex-grow"
          style={{ 
            position: "relative",
            overflow: "hidden",
            zIndex: 10
          }} 
        />
      </div>
      
      {/* Loading indicator */}
      {internalLoading && (
        <div className="absolute inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50">
          <div className="text-center p-4 bg-gray-800 rounded-lg">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-200 text-lg">Loading enhanced maps...</p>
            <p className="text-gray-400 text-sm mt-2">
              Setting up live location tracking and processing markers...
            </p>
            <button 
              className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
              onClick={() => {
                setInternalLoading(false);
                window.location.reload();
              }}
            >
              Refresh Page
            </button>
          </div>
        </div>
      )}
      
      {/* Map control buttons */}
      <div className="absolute bottom-4 right-4 flex flex-col space-y-2 z-20">
        {/* Location tracking status indicator */}
        <div 
          className={`${locationTrackingEnabled ? 'bg-green-600' : 'bg-red-600'} text-white p-2 rounded-full shadow flex items-center justify-center cursor-pointer relative group`}
          onClick={toggleLocationTracking}
          title={locationTrackingEnabled ? "Live tracking enabled - click to disable" : "Live tracking disabled - click to enable"}
        >
          {locationTrackingEnabled ? (
            <svg className="h-6 w-6 animate-pulse" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 3l-6 6m0 0V4m0 5h5M5 3a2 2 0 00-2 2v1c0 8.284 6.716 15 15 15h1a2 2 0 002-2v-3.28a1 1 0 00-.684-.948l-4.493-1.498a1 1 0 00-1.21.502l-1.13 2.257a11.042 11.042 0 01-5.516-5.517l2.257-1.128a1 1 0 00.502-1.21L9.228 3.683A1 1 0 008.279 3H5z" />
            </svg>
          ) : (
            <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
            </svg>
          )}
          
          <div className="absolute right-full mr-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap pointer-events-none">
            {locationTrackingEnabled ? "Live Tracking: ON" : "Live Tracking: OFF"}
          </div>
        </div>
        
        {/* Find location button */}
        <div 
          className="bg-blue-600 hover:bg-blue-500 text-white p-2 rounded-full shadow cursor-pointer"
          onClick={handleFindMyLocation}
          title="Find my location"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" 
            />
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" 
            />
          </svg>
        </div>
        
        {/* Refresh map button */}
        <div 
          className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-full shadow cursor-pointer"
          onClick={handleRefreshMap}
          title="Refresh maps and team zones"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
            />
          </svg>
        </div>
        
        {/* Toggle zip code boundaries button */}
        <div 
          className={`${showZipCodes ? 'bg-blue-600 hover:bg-blue-500' : 'bg-gray-600 hover:bg-gray-500'} text-white p-2 rounded-full shadow cursor-pointer`}
          onClick={toggleZipBoundaries}
          title={showZipCodes ? "Hide ZIP code boundaries" : "Show ZIP code boundaries"}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" 
            />
          </svg>
        </div>
        
        {/* Toggle rotation button */}
        {enableNavigation && (
          <div 
            className={`${isRotationEnabled ? 'bg-green-600 hover:bg-green-500' : 'bg-gray-600 hover:bg-gray-500'} text-white p-2 rounded-full shadow cursor-pointer`}
            onClick={toggleRotation}
            title={isRotationEnabled ? "Disable auto-rotation" : "Enable auto-rotation"}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 4v1m6 11h2m-6 0h-2v4m0-11v-4m6 6h2m-10 0H2" 
              />
            </svg>
          </div>
        )}
        
        {/* Reset rotation button (only shown when rotation is enabled) */}
        {isRotationEnabled && (
          <div 
            className="bg-purple-600 hover:bg-purple-500 text-white p-2 rounded-full shadow cursor-pointer"
            onClick={resetRotation}
            title="Reset map rotation"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M10 19l-7-7m0 0l7-7m-7 7h18" 
              />
            </svg>
          </div>
        )}
        
        {/* Cancel Navigation button (only shown when destination is set) */}
        {destinationRef.current && (
          <div 
            className="bg-red-600 hover:bg-red-500 text-white p-2 rounded-full shadow cursor-pointer"
            onClick={cancelNavigation}
            title="Cancel navigation"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
          </div>
        )}
      </div>
      
      {/* ZIP code info panel (shown when ZIP code boundaries are active) */}
      {showZipCodes && ZipCodeBoundaryManager.selectedZipCodes.length > 0 && (
        <div className="absolute top-4 left-4 bg-gray-800 bg-opacity-90 border border-gray-700 rounded-lg p-2 shadow-lg z-50 max-w-xs">
          <div className="flex flex-col">
            <h3 className="text-white text-sm font-medium mb-1">Selected ZIP Codes</h3>
            <div className="flex flex-wrap gap-1 max-h-32 overflow-y-auto">
              {ZipCodeBoundaryManager.selectedZipCodes.map(zipCode => (
                <span 
                  key={zipCode}
                  className="bg-green-700 text-white text-xs px-2 py-1 rounded-md flex items-center"
                >
                  {zipCode}
                  <button
                    onClick={() => {
                      ZipCodeBoundaryManager.selectedZipCodes = ZipCodeBoundaryManager.selectedZipCodes.filter(z => z !== zipCode);
                      if (zoomedOutMapRef.current && zoomedInMapRef.current) {
                        ZipCodeBoundaryManager.displayBoundaries(
                          zoomedOutMapRef.current,
                          zoomedInMapRef.current
                        );
                      }
                    }}
                    className="ml-1 text-green-300 hover:text-white"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
            <button
              onClick={() => {
                ZipCodeBoundaryManager.selectedZipCodes = [];
                if (zoomedOutMapRef.current && zoomedInMapRef.current) {
                  ZipCodeBoundaryManager.displayBoundaries(
                    zoomedOutMapRef.current,
                    zoomedInMapRef.current
                  );
                }
              }}
              className="mt-2 bg-red-600 hover:bg-red-500 text-white text-xs px-2 py-1 rounded"
            >
              Clear All Selected ZIP Codes
            </button>
          </div>
        </div>
      )}
      
      {/* Real-time GPS tracking indicator */}
      <div className="absolute bottom-4 left-4 z-20">
        {locationTrackingEnabled && (
          <div className="bg-black bg-opacity-50 px-3 py-1 rounded-lg text-white text-xs flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            <span>Live GPS Tracking</span>
          </div>
        )}
        
        {/* Marker status info */}
        {processedMarkers && (
          <div className="bg-blue-800 bg-opacity-50 px-3 py-1 rounded-lg text-white text-xs mt-2">
            <span>📍 {processedMarkers.summary?.totalMarkers || 0} markers loaded</span>
            <span className="ml-2">🚗 {processedMarkersRef.current?.zoomedOut?.size || 0} on map</span>
          </div>
        )}
        
        {/* Vehicles indicator */}
        {processedVehiclesForMap.length > 0 && (
          <div className="bg-purple-800 bg-opacity-50 px-3 py-1 rounded-lg text-white text-xs mt-2">
            <span>🚗 {processedVehiclesForMap.length} vehicles</span>
            <span className="ml-2">
              {processedVehiclesForMap.filter(v => v.isTeamVehicle).length} team
            </span>
          </div>
        )}
      </div>
      
      {/* Marker status info (top right) */}
      {processedMarkers && processedMarkers.summary && (
        <div className="absolute top-4 right-4 bg-gray-800 bg-opacity-90 border border-gray-700 rounded-lg p-2 shadow-lg z-40 text-xs text-white">
          <div className="flex flex-col space-y-1">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-600 rounded-full mr-2"></div>
              <span>Orders: {processedMarkers.summary.orderAddresses || 0}</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-orange-600 rounded-full mr-2"></div>
              <span>Locations: {processedMarkers.summary.manualLocations || 0}</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-600 rounded-full mr-2"></div>
              <span>Team: {processedMarkers.summary.teamMembers || 0}</span>
            </div>
            {processedVehiclesForMap.length > 0 && (
              <div className="flex items-center border-t border-gray-600 pt-1 mt-1">
                <div className="w-3 h-3 bg-purple-600 rounded-full mr-2"></div>
                <span>Vehicles: {processedVehiclesForMap.length}</span>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Error display */}
      {error && (
        <div className="absolute bottom-20 left-4 right-4 bg-red-800 bg-opacity-90 border border-red-600 rounded-lg p-3 shadow-lg z-50">
          <div className="text-white text-sm">
            <div className="flex items-center mb-2">
              <svg className="w-4 h-4 mr-2 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">Map Error</span>
            </div>
            <p className="text-red-100 text-xs">{error}</p>
            <button
              className="mt-2 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs"
              onClick={() => {
                setError(null);
                window.location.reload();
              }}
            >
              Reload Page
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default StandaloneMapDisplay;