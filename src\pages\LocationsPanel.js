import React, { useState, useEffect, useRef } from 'react';
import { collection, query, where, onSnapshot, doc, getDoc, getDocs, setDoc, updateDoc, orderBy, serverTimestamp } from 'firebase/firestore';
import { db } from '../pages/firebase.js';

// Import the modular components
import TeamMembers from './TeamMembers';
import QuickNavigate from './QuickNavigate';
import OpenOrders from './OpenOrders';
import PendingPickup from './PendingPickup';

const LocationsPanel = ({ 
  handleSelectLocation,
  confirmDeleteTrail,
  isAdmin,
  currentUser,
  formatDistance,
  quickNavLinks,
  navigateToQuickLink,
  updateQuickNavLink,
  screenConfig,
  userTracePaths = {},
  isTowTruckUser = false,
  currentLocation = { lat: 0, lng: 0 },
  startNavigation = () => {},
  markLocationAsPickedUp = () => {},
  setDetailsPanelLocation = () => {},
  setDetailsVisible = () => {},
  userRoutesHeight,
  teamId = null,
  // Add order and address selection props
  orderDetails = {},
  setOrderDetails = () => {},
  showAddressSelector = false,
  setShowAddressSelector = () => {},
  addressOptions = [],
  setAddressOptions = () => {},
  selectedOrderForNavigation = null,
  setSelectedOrderForNavigation = () => {},
  // Optional - if provided, use these functions from parent
  formatAddress: parentFormatAddress,
  fetchOrderDetails: parentFetchOrderDetails,
  prepareAddressSelection: parentPrepareAddressSelection,
  navigateToSelectedAddress: parentNavigateToSelectedAddress,
  // Add prop for deleting a trail
  deleteUserTrail = () => {},
  // Team vehicles prop
  teamVehicles = []
}) => {
  // State to track section heights
  const [sectionHeights, setSectionHeights] = useState({
    users: 0,
    navigation: 0,
    routes: 0,
    pickups: 0
  });
  
  // Add state for users and locations fetched directly from Firestore
  const [allUsers, setAllUsers] = useState([]);
  const [locations, setLocations] = useState([]);
  const [optimizedRoute, setOptimizedRoute] = useState([]);
  const [pendingPickups, setPendingPickups] = useState([]);
  const [openOrders, setOpenOrders] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [closestPendingLocation, setClosestPendingLocation] = useState(null);
  const [userDisplayNames, setUserDisplayNames] = useState({});
  const [userProfilePictures, setUserProfilePictures] = useState({});
  const [userTags, setUserTags] = useState({});
  const [debugMode, setDebugMode] = useState(false);
  // Add state for team members
  const [teamMembers, setTeamMembers] = useState([]);
  const [teamMembersLoaded, setTeamMembersLoaded] = useState(false);
  // Add state to track if we're currently loading
  const [isLoading, setIsLoading] = useState(true);
  // Add state for order details including original order document
  const [localOrderDetails, setLocalOrderDetails] = useState({});
  // Add state to track geocoding status
  const [geocodingInProgress, setGeocodingInProgress] = useState(false);
  // Add state to track which location is being geocoded
  const [geocodingLocationId, setGeocodingLocationId] = useState(null);
  
  // NEW: Add state variables for address selector visibility & options
  const [localShowAddressSelector, setLocalShowAddressSelector] = useState(false);
  const [localAddressOptions, setLocalAddressOptions] = useState([]);
  const [localSelectedOrderForNavigation, setLocalSelectedOrderForNavigation] = useState(null);
  
  // NEW: Add state for editing quick nav link
  const [editingQuickNavLink, setEditingQuickNavLink] = useState(null);
  const [editNavLinkData, setEditNavLinkData] = useState({
    name: '',
    position: { lat: 0, lng: 0 }
  });
  
  // NEW: Add state for trail clearing
  const [showTrailConfirmation, setShowTrailConfirmation] = useState(false);
  const [userToDeleteTrail, setUserToDeleteTrail] = useState(null);
  
  // References for input fields to prevent focus loss
  const nameInputRef = useRef(null);
  const latInputRef = useRef(null);
  const lngInputRef = useRef(null);
  
  // NEW: Add state for navigation confirmation popup
  const [showNavigationPopup, setShowNavigationPopup] = useState(false);
  const [locationForNavigation, setLocationForNavigation] = useState(null);
  
  // Local state for quick nav links to immediately show changes
  const [localQuickNavLinks, setLocalQuickNavLinks] = useState([]);
  
  // NEW: Activity timeout constants
  const ACTIVITY_TIMEOUT = 5 * 60 * 1000; // 5 minutes in milliseconds
  const ACTIVITY_UPDATE_INTERVAL = 2 * 60 * 1000; // 2 minutes in milliseconds

  // Update localQuickNavLinks when parent quickNavLinks changes
  useEffect(() => {
    if (quickNavLinks) {
      setLocalQuickNavLinks(quickNavLinks);
    }
  }, [quickNavLinks]);

  // Calculate distance between two points - Fixed to use proper haversine formula
  const calculateDistance = (point1, point2) => {
    if (!point1 || !point2) return 0;
    
    // Earth's radius in miles
    const R = 3958.8; 
    
    // Convert coordinates from degrees to radians
    const lat1 = point1.lat * Math.PI/180;
    const lon1 = point1.lng * Math.PI/180;
    const lat2 = point2.lat * Math.PI/180;
    const lon2 = point2.lng * Math.PI/180;
    
    // Haversine formula
    const dlon = lon2 - lon1;
    const dlat = lat2 - lat1;
    const a = Math.sin(dlat/2)**2 + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dlon/2)**2;
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    
    return distance;
  };
  
  // Format distance for display
  const formatDistanceLocal = (distance) => {
    if (typeof formatDistance === 'function') {
      return formatDistance(distance);
    }
    
    if (distance < 0.1) {
      return `${Math.round(distance * 5280)} ft`;
    } else {
      return `${distance.toFixed(1)} mi`;
    }
  };

  // Function to update user's last activity timestamp
  const updateUserActivity = async () => {
    if (!currentUser || !db) return;
    
    try {
      const userRef = doc(db, "onlineUsers", currentUser.uid);
      await setDoc(userRef, {
        lastActive: serverTimestamp(),
        // Preserve existing user fields
        uid: currentUser.uid,
        email: currentUser.email,
        displayName: currentUser.displayName,
        currentLocation: currentLocation,
        active: true,
        // Add any other fields that should be kept
        ...currentUser
      }, { merge: true });
      
      console.log("Updated user activity timestamp");
    } catch (error) {
      console.error("Error updating user activity:", error);
    }
  };

  // NEW: Handle trail deletion confirmation for a specific user
  const handleConfirmDeleteTrail = (userId) => {
    setUserToDeleteTrail(userId);
    setShowTrailConfirmation(true);
  };

  // NEW: Perform actual trail deletion
  const handleDeleteUserTrail = (userId) => {
    if (!userId) return;
    
    console.log(`Deleting trail for user: ${userId}`);
    
    try {
      // Call parent function if available
      if (typeof deleteUserTrail === 'function') {
        deleteUserTrail(userId);
      }
      
      // Also call global trail deletion function if it exists
      if (typeof window !== 'undefined' && window.clearUserTrail) {
        window.clearUserTrail(userId);
      }
      
      setShowTrailConfirmation(false);
      setUserToDeleteTrail(null);
      
    } catch (error) {
      console.error(`Error deleting trail for user ${userId}:`, error);
      setShowTrailConfirmation(false);
      setUserToDeleteTrail(null);
    }
  };

  // Set up activity listeners for the current user
  useEffect(() => {
    if (!currentUser || !db) return;
    
    console.log("Setting up user activity tracking");
    
    // Update activity on initial load
    updateUserActivity();
    
    // Set up interval to update activity periodically
    const activityInterval = setInterval(updateUserActivity, ACTIVITY_UPDATE_INTERVAL);
    
    // Add listeners for user activity
    const activityEvents = ['mousedown', 'keydown', 'touchstart', 'scroll'];
    let activityTimeout;
    
    const handleUserActivity = () => {
      // Debounce activity updates to avoid too many writes
      clearTimeout(activityTimeout);
      activityTimeout = setTimeout(updateUserActivity, 5000);
    };
    
    activityEvents.forEach(event => {
      window.addEventListener(event, handleUserActivity);
    });
    
    // Clean up function for the effect
    return () => {
      clearInterval(activityInterval);
      clearTimeout(activityTimeout);
      activityEvents.forEach(event => {
        window.removeEventListener(event, handleUserActivity);
      });
    };
  }, [currentUser, db]);

  // Function to geocode addresses without coordinates
  const geocodeAddress = async (address) => {
    if (!address) return null;
    
    // Skip if the address already has valid position data
    if (address.position && address.position.lat && address.position.lng) {
      return address.position;
    }
    
    try {
      setGeocodingInProgress(true);
      
      // First try to construct a searchable address string
      let addressString = '';
      
      if (typeof address === 'string') {
        addressString = address;
      } else {
        // Build address from parts
        const parts = [];
        if (address.street) parts.push(address.street);
        if (address.city) parts.push(address.city);
        if (address.state) parts.push(address.state);
        if (address.zip) parts.push(address.zip);
        
        addressString = parts.join(', ');
      }
      
      if (!addressString) {
        console.warn("Unable to construct address string for geocoding");
        setGeocodingInProgress(false);
        return null;
      }
      
      console.log(`Geocoding address: "${addressString}"`);
      
      // Use OpenStreetMap Nominatim API for geocoding (free, no API key required)
      // Note: For production, consider using a commercial geocoding service with proper rate limits
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(addressString)}&limit=1`);
      const data = await response.json();
      
      if (data && data.length > 0) {
        const result = data[0];
        const position = {
          lat: parseFloat(result.lat),
          lng: parseFloat(result.lon)
        };
        
        console.log(`Successfully geocoded address to: ${position.lat}, ${position.lng}`);
        setGeocodingInProgress(false);
        return position;
      } else {
        console.warn(`No geocoding results found for: ${addressString}`);
        setGeocodingInProgress(false);
        return null;
      }
    } catch (error) {
      console.error("Error geocoding address:", error);
      setGeocodingInProgress(false);
      return null;
    }
  };

  // Function to geocode a location and update it in Firestore
  const geocodeLocation = async (location) => {
    if (!location || !location.id) {
      console.error("Invalid location provided to geocodeLocation");
      return false;
    }
    
    // Check if location already has valid coordinates
    if (location.position && location.position.lat && location.position.lng) {
      console.log(`Location ${location.id} already has coordinates:`, location.position);
      return true;
    }
    
    try {
      setGeocodingLocationId(location.id);
      setGeocodingInProgress(true);
      
      // Try to get order data if this is an order reference
      let orderData = null;
      let addressToGeocode = null;
      
      if (location.orderReference || location.sourceType === 'order') {
        const orderId = location.orderReference || location.id;
        
        // Use parent or local order details
        const orderDetailsToUse = typeof setOrderDetails === 'function' ? orderDetails : localOrderDetails;
        
        // Check if we already have the order details cached
        orderData = orderDetailsToUse[orderId];
        
        // If not cached, fetch from Firestore
        if (!orderData) {
          orderData = await fetchOrderDetails(orderId);
        }
        
        // If we have order data with addresses, use the first address
        if (orderData?.addresses && orderData.addresses.length > 0) {
          const validAddress = orderData.addresses.find(addr => addr.street);
          if (validAddress) {
            addressToGeocode = validAddress;
          }
        }
      }
      
      // If no address found from order data, try to construct one from location fields
      if (!addressToGeocode) {
        addressToGeocode = {
          street: location.address || '',
          city: location.city || '',
          state: location.state || '',
          zip: location.zip || ''
        };
      }
      
      // Geocode the address
      const position = await geocodeAddress(addressToGeocode);
      
      if (position) {
        // Update the location in Firestore
        try {
          const locationRef = doc(db, "locations", location.id);
          await updateDoc(locationRef, {
            position: position,
            updatedAt: serverTimestamp()
          });
          
          console.log(`Updated location ${location.id} with geocoded position:`, position);
          
          // If this is from an order, update the order document too
          if (location.orderReference || location.sourceType === 'order') {
            const orderId = location.orderReference || location.id;
            const orderRef = doc(db, "orders", orderId);
            
            // Check if order exists first
            const orderSnap = await getDoc(orderRef);
            if (orderSnap.exists()) {
              await updateDoc(orderRef, {
                position: position,
                updatedAt: serverTimestamp()
              });
              
              console.log(`Updated order ${orderId} with geocoded position`);
            }
          }
          
          setGeocodingInProgress(false);
          setGeocodingLocationId(null);
          return true;
        } catch (updateError) {
          console.error(`Error updating location ${location.id} with geocoded position:`, updateError);
          setGeocodingInProgress(false);
          setGeocodingLocationId(null);
          return false;
        }
      } else {
        console.warn(`Geocoding failed for location ${location.id}`);
        setGeocodingInProgress(false);
        setGeocodingLocationId(null);
        return false;
      }
    } catch (error) {
      console.error(`Error geocoding location ${location.id}:`, error);
      setGeocodingInProgress(false);
      setGeocodingLocationId(null);
      return false;
    }
  };

  // Find the current user's team if not provided
  useEffect(() => {
    if (teamId || !db || !currentUser) return;

    setIsLoading(true);
    console.log("Finding user's team since no teamId was provided");

    const findUserTeam = async () => {
      try {
        // Query all teams to find which one contains the current user
        const teamsRef = collection(db, "teams");
        const teamsQuery = await getDocs(teamsRef);
        
        console.log(`Found ${teamsQuery.docs.length} teams total`);
        let foundTeamMembers = [];
        
        for (const teamDoc of teamsQuery.docs) {
          console.log(`Checking team ${teamDoc.id} for user ${currentUser.uid}`);
          const membersRef = collection(db, `teams/${teamDoc.id}/teamMembers`);
          const membersQuery = query(membersRef, where("userId", "==", currentUser.uid));
          const memberSnapshot = await getDocs(membersQuery);
          
          if (!memberSnapshot.empty) {
            console.log(`Found user in team: ${teamDoc.id}`);
            // User is in this team, now get all team members
            const allMembersSnapshot = await getDocs(membersRef);
            foundTeamMembers = allMembersSnapshot.docs.map(doc => {
              if (doc.data().userId) {
                return doc.data().userId;
              } else {
                console.warn(`Team member document missing userId field:`, doc.data());
                return null;}
            }).filter(Boolean); // Remove any null values
            
            console.log("Team members IDs:", foundTeamMembers);
            setTeamMembers(foundTeamMembers);
            setTeamMembersLoaded(true);
            setIsLoading(false);
            return;
          }
        }
        
        console.log("User not found in any team");
        setTeamMembersLoaded(true);
        setIsLoading(false);
      } catch (error) {
        console.error("Error finding user team:", error);
        setTeamMembersLoaded(true);
        setIsLoading(false);
      }
    };

    findUserTeam();
  }, [db, currentUser, teamId]);

  // Fetch team members if teamId is provided
  useEffect(() => {
    if (!teamId || !db) return;

    setIsLoading(true);
    console.log(`Fetching team members for team ${teamId}`);

    const fetchTeamMembers = async () => {
      try {
        // Use orderBy to ensure we get all results consistently
        const membersCollection = collection(db, `teams/${teamId}/teamMembers`);
        const membersQuery = query(membersCollection, orderBy("userId"));
        const membersSnapshot = await getDocs(membersQuery);
        
        if (!membersSnapshot.empty) {
          // Extract user IDs and validate them
          const memberIds = membersSnapshot.docs
            .map(doc => {
              const data = doc.data();
              if (!data.userId) {
                console.warn(`Team member document missing userId field:`, data);
                return null;
              }
              return data.userId;
            })
            .filter(Boolean); // Remove any null values
          
          console.log(`Raw member IDs from query (${memberIds.length}):`, memberIds);
          setTeamMembers(memberIds);
          console.log(`Found ${memberIds.length} team members in team ${teamId}`);
        } else {
          console.log(`No team members found in team ${teamId}`);
          setTeamMembers([]);
        }
        setTeamMembersLoaded(true);
        setIsLoading(false);
      } catch (error) {
        console.error(`Error fetching team members:`, error);
        setTeamMembers([]);
        setTeamMembersLoaded(true);
        setIsLoading(false);
      }
    };

    fetchTeamMembers();
  }, [db, teamId]);
  
  // Set up direct Firestore listener for online users AND ensure all team members are shown
  useEffect(() => {
    if (!teamMembersLoaded) {
      console.log("Waiting for team members to load before fetching users");
      return;
    }
    
    console.log("Setting up direct Firestore listener for online users");
    console.log(`Team members loaded: ${teamMembers.length} members`);
    console.log("Current team members:", teamMembers);
    
    // First, create placeholder entries for all team members
    const teamMemberPlaceholders = {};
    teamMembers.forEach(memberId => {
      teamMemberPlaceholders[memberId] = {
        uid: memberId,
        online: false,
        lastActive: null,
        isPlaceholder: true
      };
    });
    
    // Add a placeholder for current user if not already in team
    if (currentUser && !teamMembers.includes(currentUser.uid)) {
      teamMemberPlaceholders[currentUser.uid] = {
        uid: currentUser.uid,
        online: true,
        lastActive: new Date(),
        isPlaceholder: true
      };
    }
    
    const onlineUsersRef = collection(db, "onlineUsers");
    const unsubscribe = onSnapshot(onlineUsersRef, async (querySnapshot) => {
      const onlineUsers = {};
      
      // Get all online users
      querySnapshot.forEach((doc) => {
        const userData = doc.data();
        onlineUsers[doc.id] = {
          uid: doc.id,
          ...userData
        };
      });
      
      console.log("ALL online users fetched:", Object.keys(onlineUsers).length);
      
      // Combine online users with team member placeholders
      let combinedUsers = { ...teamMemberPlaceholders };
      
      // Override placeholders with actual online user data where available
      Object.values(onlineUsers).forEach(user => {
        if (teamMembers.includes(user.uid) || user.uid === currentUser?.uid) {
          combinedUsers[user.uid] = user;
        }
      });
      
      // Convert to array and filter as needed
      let usersArray = Object.values(combinedUsers);
      
      // Filtering logic
      let filteredUsers = usersArray;
      
      if (!isAdmin && teamMembers.length > 0) {
        // Regular user filtering by team membership
        filteredUsers = usersArray.filter(user => {
          const isTeamMember = teamMembers.includes(user.uid);
          const isCurrentUserSelf = user.uid === currentUser?.uid;
          return isTeamMember || isCurrentUserSelf;
        });
        
        console.log(`After team filter: ${filteredUsers.length} users remain`);
      } else if (!isAdmin && teamMembers.length === 0) {
        // If no team members and not admin, just show current user
        filteredUsers = usersArray.filter(user => user.uid === currentUser?.uid);
        console.log("No team members found, showing only current user");
      }
      
      // Ensure current user is always included
      if (currentUser && filteredUsers.every(user => user.uid !== currentUser.uid)) {
        const currentUserData = onlineUsers[currentUser.uid] || {
          uid: currentUser.uid,
          online: true,
          lastActive: new Date()
        };
        filteredUsers.push(currentUserData);
        console.log("Added current user to filtered list");
      }
      
      // Log the final list for debugging
      console.log("Final filtered users to show:", filteredUsers.length);
      console.log("User IDs in final list:", filteredUsers.map(u => u.uid));
      
      setAllUsers(filteredUsers);
      
      // Fetch user profiles for all filtered users
      filteredUsers.forEach(user => {
        fetchUserProfile(user.uid);
      });
      
      // For placeholders (team members without online status), try to get their profile info
      const placeholderUsers = filteredUsers.filter(user => user.isPlaceholder);
      console.log(`Fetching profiles for ${placeholderUsers.length} placeholder users`);
      
      // Batch fetch user data from users collection for placeholders
      if (placeholderUsers.length > 0) {
        try {
          const usersRef = collection(db, "users");
          const usersBatch = await getDocs(usersRef);
          
          usersBatch.forEach(doc => {
            const userData = doc.data();
            if (placeholderUsers.some(user => user.uid === doc.id)) {
              console.log(`Found user data for placeholder: ${doc.id}`);
              // Update placeholder with real data
              setAllUsers(prev => prev.map(user => 
                user.uid === doc.id ? { ...user, ...userData } : user
              ));
            }
          });
        } catch (error) {
          console.error("Error fetching placeholder user data:", error);
        }
      }
    }, (error) => {
      console.error("Error fetching online users:", error);
    });
    
    return () => unsubscribe();
  }, [db, teamMembers, currentUser, isAdmin, teamMembersLoaded]);
  
  // Set up direct Firestore listener for locations with enhanced order integration
  useEffect(() => {
    console.log("Setting up direct Firestore listener for locations");
    
    const locationsRef = collection(db, "locations");
    const unsubscribe = onSnapshot(locationsRef, (querySnapshot) => {
      const locationsList = [];
      
      querySnapshot.forEach((doc) => {
        locationsList.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      console.log(`Fetched ${locationsList.length} locations directly`);
      
      // Process locations into appropriate categories
      const allOpenOrders = [];
      const allPendingPickups = [];
      
      locationsList.forEach(loc => {
        // Normalize status to uppercase for comparison
        const normalizedStatus = (loc.status || '').toUpperCase();
        
        if (normalizedStatus === 'PICKED-UP' || normalizedStatus === 'COMPLETED' || normalizedStatus === 'SECURED') {
          // Already picked up/secured, don't include in either category
          return;
        } else if (normalizedStatus === 'PENDING' || normalizedStatus === 'OPEN-ORDER') {
          allOpenOrders.push(loc);
        } else if (normalizedStatus === 'PENDING-PICKUP' || normalizedStatus === 'AWAITING-PICKUP' || 
                   normalizedStatus === 'PENDING PICKUP' || normalizedStatus === 'FOUND') {
          // Include FOUND and PENDING PICKUP (with space) as pending pickups
          allPendingPickups.push(loc);
        } else {
          // Default: treat as open order if status is unknown
          allOpenOrders.push(loc);
        }
      });
      
      console.log(`Open orders: ${allOpenOrders.length}, Pending pickups: ${allPendingPickups.length}`);
      
      setLocations(locationsList);
      setOpenOrders(allOpenOrders);
      setPendingPickups(allPendingPickups);
      
      // Find closest pending pickup
      if (allPendingPickups.length > 0 && currentLocation.lat !== 0) {
        const closest = allPendingPickups.reduce((prev, curr) => {
          const prevDist = calculateDistance(currentLocation, prev.position);
          const currDist = calculateDistance(currentLocation, curr.position);
          return prevDist < currDist ? prev : curr;
        });
        
        setClosestPendingLocation(closest);
      } else if (allOpenOrders.length > 0 && currentLocation.lat !== 0) {
        // If no pending pickups, use the closest open order
        const closest = allOpenOrders.reduce((prev, curr) => {
          const prevDist = calculateDistance(currentLocation, prev.position);
          const currDist = calculateDistance(currentLocation, curr.position);
          return prevDist < currDist ? prev : curr;
        });
        
        setClosestPendingLocation(closest);
      } else {
        setClosestPendingLocation(null);
      }
      
      // Create optimized route for all open orders
      if (allOpenOrders.length > 0 && currentLocation.lat !== 0) {
        let route = [];
        let remaining = [...allOpenOrders];
        let currentPoint = currentLocation;
        
        while (remaining.length > 0) {
          // Find the closest location to current point
          let closestIndex = 0;
          let closestDistance = calculateDistance(currentPoint, remaining[0].position);
          
          for (let i = 1; i < remaining.length; i++) {
            const distance = calculateDistance(currentPoint, remaining[i].position);
            if (distance < closestDistance) {
              closestDistance = distance;
              closestIndex = i;
            }
          }
          
          // Add to route with distance info
          const nextStop = remaining[closestIndex];
          route.push({
            location: nextStop,
            distanceFromPrevious: closestDistance
          });
          
          // Update current point and remove from remaining
          currentPoint = nextStop.position;
          remaining.splice(closestIndex, 1);
        }
        
        setOptimizedRoute(route);
      } else {
        setOptimizedRoute([]);
      }
    }, (error) => {
      console.error("Error fetching locations:", error);
    });
    
    return () => unsubscribe();
  }, [currentLocation]);
  
  // Fetch order details from firestore - Use parent function if provided, otherwise use local
  const fetchOrderDetails = async (orderId) => {
    // If parent function provided, use it
    if (typeof parentFetchOrderDetails === 'function') {
      return parentFetchOrderDetails(orderId);
    }
    
    // Otherwise use local implementation
    if (!orderId) {
      console.error("fetchOrderDetails called with no orderId");
      return null;
    }
    
    if (!db) {
      console.error("Firestore database is not initialized");
      return null;
    }
    
    try {
      const orderRef = doc(db, "orders", orderId);
      const orderSnap = await getDoc(orderRef);
      
      if (orderSnap.exists()) {
        const orderData = orderSnap.data();
        console.log(`Fetched order details for ${orderId}:`, orderData);
        
        // Cache the order details
        setLocalOrderDetails(prev => ({
          ...prev,
          [orderId]: orderData
        }));
        
        // Also update parent state if available
        if (typeof setOrderDetails === 'function') {
          setOrderDetails(prev => ({
            ...prev,
            [orderId]: orderData
          }));
        }
        
        return orderData;
      } else {
        console.log(`No order found with ID ${orderId}`);
        return null;
      }
    } catch (error) {
      console.error(`Error fetching order details for ${orderId}:`, error);
      return null;
    }
  };
  
  // Fetch user profile data including profile picture and tags
  const fetchUserProfile = async (userId) => {
    if (!userId) {
      console.warn("Attempted to fetch profile for undefined userId");
      return;
    }
    
    try {
      const profileRef = doc(db, "userProfiles", userId);
      const profileSnap = await getDoc(profileRef);
      
      if (profileSnap.exists()) {
        const profileData = profileSnap.data();
        
        // Update display name if available
        if (profileData.displayName) {
          setUserDisplayNames(prev => ({
            ...prev,
            [userId]: profileData.displayName
          }));
        }
        
        // Update profile picture if available
        if (profileData.profilePicture || profileData.photoBase64) {
          setUserProfilePictures(prev => ({
            ...prev,
            [userId]: profileData.profilePicture || profileData.photoBase64
          }));
        }
        
        // Update user tags if available
        if (profileData.tags) {
          setUserTags(prev => ({
            ...prev,
            [userId]: profileData.tags
          }));
          console.log(`Loaded ${profileData.tags.length} tags for user ${userId}`);
        }
        
        console.log(`Loaded profile for ${userId}: ${profileData.displayName || 'No display name'}`);
      } else {
        console.log(`No profile found for user ${userId}`);
        
        // Try alternate location - users collection
        const userRef = doc(db, "users", userId);
        const userSnap = await getDoc(userRef);
        
        if (userSnap.exists()) {
          const userData = userSnap.data();
          
          if (userData.displayName) {
            setUserDisplayNames(prev => ({
              ...prev, 
              [userId]: userData.displayName
            }));
            console.log(`Found display name in users collection: ${userData.displayName}`);
          }
          
          if (userData.photoURL) {
            setUserProfilePictures(prev => ({
              ...prev,
              [userId]: userData.photoURL
            }));
          }
        }
      }
    } catch (error) {
      console.error(`Error fetching profile for user ${userId}:`, error);
    }
  };
  
  // Function to prepare and show address selection dialog
  const prepareAddressSelection = async (location) => {
    // If parent function provided, use it
    if (typeof parentPrepareAddressSelection === 'function') {
      return parentPrepareAddressSelection(location);
    }
    
    // Otherwise use local implementation
    if (!location) {
      console.error("Location is undefined in prepareAddressSelection");
      return false;
    }
    
    // Check if this is an order reference
    if (location.orderReference || location.sourceType === 'order') {
      const orderId = location.orderReference || location.id;
      
      try {
        // Use parent or local order details
        const orderDetailsToUse = typeof setOrderDetails === 'function' ? orderDetails : localOrderDetails;
        
        // Check if we already have the order details cached
        let orderData = orderDetailsToUse[orderId];
        
        // If not cached, fetch from Firestore
        if (!orderData) {
          orderData = await fetchOrderDetails(orderId);
          
          // If fetchOrderDetails returns null or undefined, handle that case
          if (!orderData) {
            console.error(`No order data found for ID: ${orderId}`);
            return false;
          }
        }
        
        // If we have addresses, show the selector
        if (orderData.addresses && orderData.addresses.length > 0) {
          // Update both local and parent state for coordination
          setLocalSelectedOrderForNavigation({
            ...location,
            orderData
          });
          if (typeof setSelectedOrderForNavigation === 'function') {
            setSelectedOrderForNavigation({
              ...location,
              orderData
            });
          }
          
          // Process addresses to ensure they have position data
          setGeocodingInProgress(true);
          
          console.log("Processing addresses for coordinates...", orderData.addresses);
          
          // Use location's position as fallback for all addresses if available
          const fallbackPosition = hasValidCoordinates(location) ? location.position : null;
          
          const processedAddresses = await Promise.all(
            orderData.addresses.map(async (addr, index) => {
              // First check if the address already has valid position data
              const hasPosition = addr.position && 
                                typeof addr.position.lat === 'number' && 
                                typeof addr.position.lng === 'number' &&
                                !isNaN(addr.position.lat) && 
                                !isNaN(addr.position.lng);
              
              console.log(`Address ${index}: has position? ${hasPosition}`, addr);
              
              if (hasPosition) {
                // Address already has good position data
                return {
                  id: index,
                  address: addr,
                  formattedAddress: formatAddress(addr),
                  hasValidCoordinates: true
                };
              }
              
              // Try to geocode the address
              const geocodedPosition = await geocodeAddress(addr);
              
              if (geocodedPosition) {
                console.log(`Address ${index} geocoded:`, geocodedPosition);
                
                // Create a new address object with position data
                return {
                  id: index,
                  address: {
                    ...addr,
                    position: geocodedPosition
                  },
                  formattedAddress: formatAddress(addr),
                  hasValidCoordinates: true
                };
              } else if (fallbackPosition) {
                console.log(`Using fallback position for address ${index}:`, fallbackPosition);
                
                // Use the location's position as fallback
                return {
                  id: index,
                  address: {
                    ...addr,
                    position: fallbackPosition
                  },
                  formattedAddress: formatAddress(addr),
                  hasValidCoordinates: true,
                  usedFallbackPosition: true
                };
              } else {
                console.log(`No position available for address ${index}`);
                
                // No position available
                return {
                  id: index,
                  address: addr,
                  formattedAddress: formatAddress(addr),
                  hasValidCoordinates: false
                };
              }
            })
          );
          
          setGeocodingInProgress(false);
          console.log("Final processed addresses:", processedAddresses);
          
          // Set address options in both local and parent state
          setLocalAddressOptions(processedAddresses);
          if (typeof setAddressOptions === 'function') {
            setAddressOptions(processedAddresses);
          }
          
          // Show the address selector in both local and parent state
          setLocalShowAddressSelector(true);
          if (typeof setShowAddressSelector === 'function') {
            setShowAddressSelector(true);
          }
          
          return true; // Indicates we're showing the selector
        } else {
          console.log(`Order ${orderId} has no addresses to select from`);
          return false;
        }
      } catch (error) {
        setGeocodingInProgress(false);
        console.error(`Error in address selection for order ${orderId}:`, error);
        return false;
      }
    }
    
    // If not an order with addresses, return false to continue normal navigation
    return false;
  };
  
  // Function to navigate to a selected address - FIXED VERSION
  const navigateToSelectedAddress = (addressOption) => {
    // If parent function provided, use it
    if (typeof parentNavigateToSelectedAddress === 'function') {
      return parentNavigateToSelectedAddress(addressOption);
    }
    
    // Otherwise use local implementation
    const selectedOrderInfo = localSelectedOrderForNavigation || selectedOrderForNavigation;
    
    if (!selectedOrderInfo || !addressOption) {
      console.error("Missing selectedOrderForNavigation or addressOption in navigateToSelectedAddress");
      setLocalShowAddressSelector(false);
      if (typeof setShowAddressSelector === 'function') {
        setShowAddressSelector(false);
      }
      return;
    }
    
    try {
      // CRITICAL: Check if the address has valid position data
      if (!addressOption.address.position || !addressOption.address.position.lat || !addressOption.address.position.lng ||
          typeof addressOption.address.position.lat !== 'number' || typeof addressOption.address.position.lng !== 'number' ||
          isNaN(addressOption.address.position.lat) || isNaN(addressOption.address.position.lng)) {
        console.error("Selected address doesn't have valid coordinates:", addressOption);
        alert("This address doesn't have valid GPS coordinates. Please select another address or try again later.");
        return;
      }
      
      // Use the specific address position data - this is the key fix
      const position = addressOption.address.position;
      
      console.log("Using address position for navigation:", position);
      
      // Create a temporary location object with the selected address and EXPLICIT position
      const navigationLocation = {
        ...selectedOrderInfo,
        name: `${selectedOrderInfo.name} - ${addressOption.formattedAddress}`,
        selectedAddress: addressOption.address,
        formattedAddress: addressOption.formattedAddress,
        position: {
          lat: Number(position.lat), // Ensure numeric value
          lng: Number(position.lng)  // Ensure numeric value
        },
        originalPosition: selectedOrderInfo.position // Keep original for reference
      };
      
      console.log("Final navigation location:", navigationLocation);
      
      // IMPORTANT: Select the location (this sets detailsPanelLocation but doesn't affect selectedLocation)
      handleSelectLocation(navigationLocation);
      
      // CRITICAL: Start navigation with the location passed directly
      // This avoids any state synchronization issues
      startNavigation(navigationLocation);
      
      // Close the address selector in both local and parent state
      setLocalShowAddressSelector(false);
      if (typeof setShowAddressSelector === 'function') {
        setShowAddressSelector(false);
      }
      
      setLocalSelectedOrderForNavigation(null);
      if (typeof setSelectedOrderForNavigation === 'function') {
        setSelectedOrderForNavigation(null);
      }
    } catch (error) {
      console.error("Error in navigateToSelectedAddress:", error);
      // Close the selector even if there's an error
      setLocalShowAddressSelector(false);
      if (typeof setShowAddressSelector === 'function') {
        setShowAddressSelector(false);
      }
      
      setLocalSelectedOrderForNavigation(null);
      if (typeof setSelectedOrderForNavigation === 'function') {
        setSelectedOrderForNavigation(null);
      }
    }
  };
  
  // Handle start navigation with address selection and improved error handling
  const handleStartNavigation = async (location) => {
    if (!location) {
      console.error("Location is undefined in handleStartNavigation");
      return;
    }
    
    try {
      console.log("Starting navigation to location:", location);
      
      // First check if location has valid position data
      if (!location.position || !location.position.lat || !location.position.lng ||
          typeof location.position.lat !== 'number' || typeof location.position.lng !== 'number' ||
          isNaN(location.position.lat) || isNaN(location.position.lng)) {
        console.warn("Location missing valid position data:", location);
        
        // Try to geocode the location
        const success = await geocodeLocation(location);
        if (!success) {
          alert("This location doesn't have valid GPS coordinates for navigation. Try geocoding it first.");
          return;
        }
        
        // Get the updated location with coordinates
        try {
          const locationRef = doc(db, "locations", location.id);
          const locationSnap = await getDoc(locationRef);
          if (locationSnap.exists()) {
            location = { ...locationSnap.data(), id: location.id };
            
            // Double check that geocoding worked
            if (!location.position || !location.position.lat || !location.position.lng ||
                typeof location.position.lat !== 'number' || typeof location.position.lng !== 'number' ||
                isNaN(location.position.lat) || isNaN(location.position.lng)) {
              alert("Error: Location coordinates are still invalid after geocoding.");
              return;
            }
          } else {
            alert("Error retrieving location data after geocoding.");
            return;
          }
        } catch (error) {
          console.error("Error getting updated location after geocoding:", error);
          alert("Error retrieving location data after geocoding.");
          return;
        }
      }
      
      // Log the validated position
      console.log("Validated location position:", location.position);
      
      // Now explicitly check if this is an order that might have multiple addresses
      const isOrderWithAddresses = location.orderReference || location.sourceType === 'order';
      
      if (isOrderWithAddresses) {
        // ALWAYS prepare address selection for orders
        console.log("Order detected, checking for addresses to select from...");
        
        // Check for latest fresh order data
        if (location.orderReference || location.id) {
          const orderId = location.orderReference || location.id;
          
          // Force refresh order data to get the latest addresses
          const freshOrderData = await fetchOrderDetails(orderId);
          
          // Update cache with fresh data
          if (freshOrderData) {
            // Update local cache
            setLocalOrderDetails(prev => ({
              ...prev, 
              [orderId]: freshOrderData
            }));
            
            // Update parent cache if available
            if (typeof setOrderDetails === 'function') {
              setOrderDetails(prev => ({
                ...prev,
                [orderId]: freshOrderData
              }));
            }
            
            console.log("Refreshed order data for address selection:", freshOrderData);
          }
        }
        
        const showingSelector = await prepareAddressSelection(location);
        
        if (showingSelector) {
          console.log("Showing address selector dialog");
          // If showing selector, we're done here - user will select an address
          return;
        }
      }
      
      // Set the location for navigation confirmation popup
      setLocationForNavigation(location);
      setShowNavigationPopup(true);
    } catch (error) {
      console.error("Error in handleStartNavigation:", error);
      alert("An error occurred while trying to start navigation. Please try again.");
    }
  };
  
  // NEW: Function to start actual navigation after confirmation
  const confirmAndStartNavigation = (location) => {
    // Close the popup
    setShowNavigationPopup(false);
    
    if (!location) {
      console.error("No location provided to confirmAndStartNavigation");
      return;
    }
    
    // Start navigation using the provided function
    if (typeof startNavigation === 'function') {
      startNavigation(location);
    } else {
      console.error("startNavigation function is not defined");
    }
  };
  
  // Function to navigate to a user's location
  const navigateToUserLocation = (user) => {
    if (!user) {
      console.error("Cannot navigate to undefined user");
      return;
    }
    
    console.log(`Trying to navigate to ${user.displayName || user.email}'s location`);
    
    // Check if user has location data
    if (user.currentLocation) {
      console.log(`User has location:`, user.currentLocation);
      
      // Create a temporary location object
      const userLocation = {
        id: `user-${user.uid}`,
        name: `${user.displayName || user.email}'s Location`,
        position: user.currentLocation,
        isUserLocation: true,
        userId: user.uid
      };
      
      // Select the location and start navigation
      handleSelectLocation(userLocation);
      startNavigation(userLocation);
    } else {
      // If no location data, create a random one near current location
      console.log("User has no location data, creating a random nearby location");
      const randomLocation = {
        lat: currentLocation.lat + (Math.random() * 0.01 - 0.005),
        lng: currentLocation.lng + (Math.random() * 0.01 - 0.005)
      };
      
      const userLocation = {
        id: `user-${user.uid}`,
        name: `${user.displayName || user.email}'s Location (Approximated)`,
        position: randomLocation,
        isUserLocation: true,
        userId: user.uid
      };
      
      handleSelectLocation(userLocation);
      startNavigation(userLocation);
    }
  };
  
  // Format address for display
  const formatAddress = (address) => {
    if (typeof parentFormatAddress === 'function') {
      return parentFormatAddress(address);
    }
    
    if (!address) return 'No address';
    
    if (typeof address === 'string') {
      return address;
    }
    
    try {
      let parts = [];
      if (address.street) parts.push(address.street);
      
      let cityStateZip = '';
      if (address.city) cityStateZip += address.city;
      if (address.state) {
        if (cityStateZip) cityStateZip += ', ';
        cityStateZip += address.state;
      }
      if (address.zip) {
        if (cityStateZip) cityStateZip += ' ';
        cityStateZip += address.zip;
      }
      
      if (cityStateZip) parts.push(cityStateZip);
      
      return parts.length > 0 ? parts.join(', ') : 'Address details unavailable';
    } catch (error) {
      console.error("Error formatting address:", error);
      return 'Error formatting address';
    }
  };
  
  // Get viewport dimensions for dynamic sizing
  const getViewportDimensions = () => {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      isPortrait: window.innerHeight > window.innerWidth,
      isIPad: window.innerWidth >= 768 && window.innerWidth <= 1024
    };
  };
  
  // Calculate dynamic heights based on viewport
  const getScrollableAreaHeight = () => {
    const { width, height, isPortrait, isIPad } = getViewportDimensions();
    
    if (isIPad) {
      // iPad sizing
      return isPortrait 
        ? height * 0.65  // 65% of height for iPad portrait
        : height * 0.75; // 75% of height for iPad landscape
    } else if (width < 768) {
      // Mobile sizing
      return height * 0.6; // 60% of height for mobile
    } else {
      // Desktop sizing
      return height * 0.75; // 75% of height for desktop
    }
  };
  
  // Calculate heights for individual sections
  const calculateSectionHeights = () => {
    const totalHeight = getScrollableAreaHeight();
    const { isPortrait, isIPad } = getViewportDimensions();
    
    // Special handling for iPad in portrait mode
    if (isIPad && isPortrait) {
      // More generous spacing for iPad portrait mode
      setSectionHeights({
        users: totalHeight * 0.22,     // 22% for users
        navigation: totalHeight * 0.22, // 22% for navigation
        routes: totalHeight * 0.22,     // 22% for routes
        pickups: totalHeight * 0.34     // 34% for pickups
      });
    } else {
      // Standard distribution for other devices and orientations
      const equalSectionHeight = totalHeight * 0.2; // 20% for each of the first three sections
      const pickupsHeight = totalHeight * 0.4; // 40% for pickups section
      
      setSectionHeights({
        users: equalSectionHeight,      // Equal height for team members
        navigation: equalSectionHeight, // Equal height for navigation
        routes: equalSectionHeight,     // Equal height for open orders
        pickups: pickupsHeight          // Larger height for pickups
      });
    }
  };
  
  // Recalculate section heights when dimensions change
  useEffect(() => {
    calculateSectionHeights();
    
    const handleResize = () => {
      calculateSectionHeights();
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, []);
  
  // If userRoutesHeight is provided, use that instead
  const panelHeight = userRoutesHeight || getScrollableAreaHeight();
  
  // Check if location has valid GPS coordinates
  const hasValidCoordinates = (location) => {
    return location?.position && 
           typeof location.position.lat === 'number' && 
           typeof location.position.lng === 'number' &&
           !isNaN(location.position.lat) &&
           !isNaN(location.position.lng);
  };
  
  // FIXED: Function to ONLY load details in the details panel, NEVER start navigation
  const showDetailsOnly = (location) => {
    if (!location) return;
    
    console.log("Showing ONLY details for location:", location.id);
    
    // Flag to indicate user explicitly selected this location for viewing details
    const locationWithSelection = {
      ...location,
      userSelected: true,
      // Add a flag to explicitly prevent routing
      preventRouting: true
    };
    
    // CRITICAL FIX: Update the details panel location directly
    if (typeof setDetailsPanelLocation === 'function') {
      setDetailsPanelLocation(locationWithSelection);
      console.log("Set details panel location:", locationWithSelection.id);
    }
    
    // CRITICAL FIX: Always ensure details panel is visible
    if (typeof setDetailsVisible === 'function') {
      // Use a small timeout to ensure panel is opened AFTER location is set
      // This helps coordinate the state update sequence
      setTimeout(() => {
        setDetailsVisible(true);
        console.log("Explicitly set details panel to visible");
      }, 50);
    }
    
    // ALSO pass to handleSelectLocation to maintain any other state
    if (typeof handleSelectLocation === 'function') {
      handleSelectLocation(locationWithSelection);
    }
  };

  // Address Selector Dialog component
  const AddressSelectorDialog = () => {
    // Use either local or parent state for controlling dialog visibility
    const isVisible = localShowAddressSelector || showAddressSelector;
    const addressOpts = localAddressOptions.length > 0 ? localAddressOptions : addressOptions;
    
    if (!isVisible) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[2000]">
        <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg w-full max-w-md p-4">
          <h3 className="text-lg font-semibold text-white mb-2">Select Address for Navigation</h3>
          <p className="text-gray-300 text-sm mb-4">
            Choose which address you would like to navigate to:
          </p>
          
          {geocodingInProgress ? (
            <div className="flex justify-center items-center py-6">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mr-3"></div>
              <p className="text-gray-300">Looking up address coordinates...</p>
            </div>
          ) : (
            <div className="max-h-60 overflow-y-auto mb-4">
              {addressOpts.length > 0 ? (
                addressOpts.map((option) => {
                  // Double check if the coordinate data is valid
                  const validCoords = option.address && 
                                     option.address.position && 
                                     typeof option.address.position.lat === 'number' && 
                                     typeof option.address.position.lng === 'number' &&
                                     !isNaN(option.address.position.lat) && 
                                     !isNaN(option.address.position.lng);
                  
                  // Update hasValidCoordinates status based on our check
                  const addressWithValidation = {
                    ...option,
                    hasValidCoordinates: validCoords
                  };
                  
                  return (
                    <button
                      key={option.id}
                      onClick={() => navigateToSelectedAddress(addressWithValidation)}
                      className={`w-full text-left p-3 mb-2 ${validCoords ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-800 opacity-60'} text-gray-200 rounded flex items-start transition-colors duration-200 relative`}
                      disabled={!validCoords}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 mr-2 mt-1 ${validCoords ? 'text-blue-400' : 'text-gray-500'} flex-shrink-0`} viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                      <div className="flex-1">
                        <span className="text-sm">{option.formattedAddress}</span>
                        
                        {/* Always show coordinates if available */}
                        {validCoords && (
                          <div className="text-xs text-green-400 mt-1 font-mono">
                            {option.address.position.lat.toFixed(6)}, {option.address.position.lng.toFixed(6)}
                            {option.usedFallbackPosition && (
                              <span className="ml-1 text-yellow-400">(fallback)</span>
                            )}
                          </div>
                        )}
                      </div>
                      
                      {/* GPS coordinates indicator */}
                      {validCoords ? (
                        <span className="ml-2 text-xs text-green-400 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          GPS
                        </span>
                      ) : (
                        <span className="ml-2 text-xs text-yellow-500">No GPS</span>
                      )}
                    </button>
                  );
                })
              ) : (
                <div className="text-center py-4 text-gray-400">
                  No addresses available for this location
                </div>
              )}
            </div>
          )}
          
          <div className="flex justify-end">
            <button onClick={() => {
                setLocalShowAddressSelector(false);
                if (typeof setShowAddressSelector === 'function') {
                  setShowAddressSelector(false);
                }
              }}
              className="px-4 py-2 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  // NEW: Navigation Confirmation Dialog
  const NavigationConfirmationDialog = () => {
    if (!showNavigationPopup || !locationForNavigation) return null;
    
    const hasCoordinates = hasValidCoordinates(locationForNavigation);
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[2000]">
        <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg w-full max-w-md p-4">
          <h3 className="text-lg font-semibold text-white mb-2">Start Navigation</h3>
          <p className="text-gray-300 text-sm mb-4">
            Navigate to: <span className="font-medium">{locationForNavigation.name}</span>
          </p>
          
          {/* Show GPS coordinates */}
          {hasCoordinates && (
            <div className="bg-gray-700 p-3 rounded mb-4">
              <div className="font-mono text-sm text-green-400">
                GPS Coordinates:
                <div className="mt-1">{locationForNavigation.position.lat.toFixed(6)}, {locationForNavigation.position.lng.toFixed(6)}</div>
              </div>
            </div>
          )}
          
          <div className="flex justify-end gap-2">
            <button 
              onClick={() => setShowNavigationPopup(false)}
              className="px-4 py-2 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors duration-200"
            >
              Cancel
            </button>
            <button 
              onClick={() => confirmAndStartNavigation(locationForNavigation)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors duration-200"
            >
              Start Navigation
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  // NEW: Trail Deletion Confirmation Dialog
  const TrailDeletionDialog = () => {
    if (!showTrailConfirmation || !userToDeleteTrail) return null;
    
    const userName = userDisplayNames[userToDeleteTrail] || userToDeleteTrail;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[2000]">
        <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg w-full max-w-md p-4">
          <h3 className="text-lg font-semibold text-white mb-2">Delete User Trail</h3>
          <p className="text-gray-300 text-sm mb-4">
            Are you sure you want to delete the movement trail for <span className="font-medium">{userName}</span>? 
            This cannot be undone.
          </p>
          
          <div className="flex justify-end gap-2">
            <button 
              onClick={() => {
                setShowTrailConfirmation(false);
                setUserToDeleteTrail(null);
              }}
              className="px-4 py-2 bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              Cancel
            </button>
            <button 
              onClick={() => handleDeleteUserTrail(userToDeleteTrail)}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-500"
            >
              Delete Trail
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Debug indicator component
  const DebugIndicator = () => (
    debugMode && (
      <div className="fixed bottom-4 right-4 bg-red-600 text-white px-3 py-1 rounded-full text-xs z-50">
        Debug Mode
      </div>
    )
  );

  return (
    <div className="flex flex-col h-full overflow-hidden w-full">
      {/* Main scrollable content area with overflow-x hidden */}
      <div 
        className="locations-panel-content flex-grow overflow-y-auto overflow-x-hidden w-full locations-custom-scrollbar p-3"
        style={{ 
          height: `${panelHeight}px`,
          maxHeight: `${panelHeight}px`
        }}
      >
        {/* TeamMembers Component */}
        <TeamMembers 
          allUsers={allUsers} 
          currentUser={currentUser}
          userDisplayNames={userDisplayNames}
          userProfilePictures={userProfilePictures}
          userTags={userTags}
          isAdmin={isAdmin}
          navigateToUserLocation={navigateToUserLocation}
          handleConfirmDeleteTrail={handleConfirmDeleteTrail}
          isLoading={isLoading}
          teamMembersLoaded={teamMembersLoaded}
          teamMembers={teamMembers}
          screenConfig={screenConfig}
          sectionHeight={sectionHeights.users}
        />

        {/* QuickNavigate Component */}
        <QuickNavigate 
          quickNavLinks={localQuickNavLinks}
          navigateToQuickLink={navigateToQuickLink}
          updateQuickNavLink={updateQuickNavLink}
          isAdmin={isAdmin}
          currentLocation={currentLocation}
          sectionHeight={sectionHeights.navigation}
        />

        {/* OpenOrders Component with all required props */}
        <OpenOrders 
          openOrders={openOrders}
          teamVehicles={teamVehicles}
          selectedLocation={selectedLocation}
          showDetailsOnly={showDetailsOnly}
          handleStartNavigation={handleStartNavigation}
          geocodeLocation={geocodeLocation}
          currentLocation={currentLocation}
          calculateDistance={calculateDistance}
          formatDistance={formatDistance}
          sectionHeight={sectionHeights.routes}
          geocodingLocationId={geocodingLocationId}
        />

        {/* PendingPickup Component */}
        <PendingPickup 
          pendingPickups={pendingPickups}
          teamVehicles={teamVehicles}
          currentLocation={currentLocation}
          formatDistance={formatDistance}
          geocodeLocation={geocodeLocation}
          handleStartNavigation={handleStartNavigation}
          showDetailsOnly={showDetailsOnly}
          markLocationAsPickedUp={markLocationAsPickedUp}
          isTowTruckUser={isTowTruckUser}
          geocodingLocationId={geocodingLocationId}
          closestPendingLocation={closestPendingLocation}
          calculateDistance={calculateDistance}
          sectionHeight={sectionHeights.pickups}
        />
      </div>

      {/* Address Selection Dialog */}
      <AddressSelectorDialog />
      
      {/* Navigation Confirmation Dialog */}
      <NavigationConfirmationDialog />
      
      {/* Trail Deletion Confirmation Dialog */}
      <TrailDeletionDialog />

      {/* Custom CSS for the component */}
      <style>
      {`
        .locations-custom-scrollbar {
          -webkit-overflow-scrolling: touch;
          scrollbar-width: thin;
        }
        
        /* Custom scrollbar styling */
        .locations-custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        
        .locations-custom-scrollbar::-webkit-scrollbar-track {
          background: #1F2937;
        }
        
        .locations-custom-scrollbar::-webkit-scrollbar-thumb {
          background-color: #4B5563;
          border-radius: 20px;
        }
        
        /* Ensure text doesn't overflow with proper truncation */
        .truncate {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
        }
        
        /* Touch-friendly interactive elements */
        button {
          min-height: 30px;
          touch-action: manipulation;
        }
        
        /* Prevent horizontal overflow on small screens */
        .locations-panel-content > * {
          max-width: 100%;
          overflow-x: hidden;
        }
        
        /* Responsive button layouts */
        .button-group {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
        }
        
        @media (max-width: 767px) {
          button {
            min-height: 36px;
          }
          
          /* Allow text wrapping on very small screens */
          .truncate-mobile {
            white-space: normal;
            word-break: break-word;
          }
        }
        
        /* Ensure proper text wrapping in buttons */
        button span {
          display: inline-block;
          max-width: 100%;
        }
      `}
      </style>

      {/* Debug indicator */}
      <DebugIndicator />
    </div>
  );
};

export default LocationsPanel;