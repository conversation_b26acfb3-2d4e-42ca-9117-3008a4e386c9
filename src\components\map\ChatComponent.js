import React, { useContext, useEffect, useRef, useState } from 'react';
import { collection, onSnapshot, addDoc, serverTimestamp } from 'firebase/firestore';
// In all components
import { MapContext } from '../MapContext';
import { playNotificationSound } from '../../utils/mapUtils';

// Helper function to compress image to base64
const compressImageToBase64 = (file, maxWidth = 800, maxHeight = 800, quality = 0.7) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        // Create a canvas to resize the image
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;
        
        // Calculate new dimensions while maintaining aspect ratio
        if (width > height) {
          if (width > maxWidth) {
            height = Math.round(height * maxWidth / width);
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = Math.round(width * maxHeight / height);
            height = maxHeight;
          }
        }
        
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, width, height);
        
        // Get base64 representation
        const base64 = canvas.toDataURL('image/jpeg', quality);
        resolve(base64);
      };
      img.onerror = (error) => reject(error);
    };
    reader.onerror = (error) => reject(error);
  });
};

// Function to open image viewer
const openImageViewer = (imageUrl) => {
  try {
    // Create image viewer overlay
    const viewer = document.createElement('div');
    viewer.className = 'image-viewer';
    viewer.innerHTML = `
      <div class="image-viewer-content">
        <button class="image-viewer-close">×</button>
        <img src="${imageUrl}" alt="Enlarged image">
      </div>
    `;
    
    document.body.appendChild(viewer);
    
    // Add close event
    viewer.querySelector('.image-viewer-close').addEventListener('click', () => {
      try {
        document.body.removeChild(viewer);
      } catch (err) {
        console.warn("Error removing image viewer:", err);
      }
    });
    
    // Close on background click
    viewer.addEventListener('click', (e) => {
      if (e.target === viewer) {
        try {
          document.body.removeChild(viewer);
        } catch (err) {
          console.warn("Error removing image viewer:", err);
        }
      }
    });
  } catch (err) {
    console.error("Error opening image viewer:", err);
  }
};

const ChatComponent = () => {
  const {
    currentUser,
    firestoreRef,
    userProfilePictures,
    userDisplayNames,
    setError
  } = useContext(MapContext);
  
  // Chat state
  const [chatMessages, setChatMessages] = useState([]);
  const [newChatMessage, setNewChatMessage] = useState('');
  const [mediaToUpload, setMediaToUpload] = useState(null);
  const [chatImageUpload, setChatImageUpload] = useState(false);
  const [chatVideoUpload, setChatVideoUpload] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  
  // Ref for chat container to allow auto-scrolling
  const chatMessagesRef = useRef(null);
  
  // Ref to store unsubscribe function for Firestore listener
  const chatMessagesListenerRef = useRef(null);
  
  // Set up chat messages listener when component mounts
  useEffect(() => {
    if (!firestoreRef.current) return;
    
    // Clean up any existing listener
    if (chatMessagesListenerRef.current) {
      chatMessagesListenerRef.current();
    }
    
    console.log("Setting up chat message listener");
    
    // Set up new listener
    chatMessagesListenerRef.current = onSnapshot(
      collection(firestoreRef.current, 'chatMessages'),
      (snapshot) => {
        const messages = [];
        
        snapshot.forEach((doc) => {
          const data = doc.data();
          
          // Convert Firestore timestamp to JS Date
          const timestamp = data.timestamp ? new Date(data.timestamp.toDate()) : new Date();
          
          messages.push({
            id: doc.id,
            text: data.text || '',
            sender: {
              uid: data.sender?.uid || 'unknown',
              name: data.sender?.name || 'Unknown User',
              photo: data.sender?.photo || null
            },
            timestamp: timestamp,
            mediaUrl: data.mediaUrl || null,
            mediaType: data.mediaType || null // 'image' or 'video'
          });
        });
        
        // Sort by timestamp
        messages.sort((a, b) => a.timestamp - b.timestamp);
        
        setChatMessages(messages);
        
        // Scroll to the bottom of the chat
        setTimeout(() => {
          scrollToBottom();
        }, 100);
        
        // Play notification sound for new messages
        if (messages.length > 0 && chatMessages.length > 0) {
          const latestMessage = messages[messages.length - 1];
          const previousLatestId = chatMessages.length > 0 ? chatMessages[chatMessages.length - 1].id : null;
          
          // If there's a new message and it's not from the current user
          if (latestMessage.id !== previousLatestId && latestMessage.sender.uid !== currentUser?.uid) {
            playNotificationSound();
          }
        }
      },
      (error) => {
        console.error("Error in chat messages listener:", error);
        setError("Failed to load chat messages. Please refresh the page.");
      }
    );
    
    return () => {
      // Clean up listener when component unmounts
      if (chatMessagesListenerRef.current) {
        chatMessagesListenerRef.current();
        chatMessagesListenerRef.current = null;
      }
    };
  }, [firestoreRef.current, currentUser]);
  
  // Scroll to bottom of chat
  const scrollToBottom = () => {
    if (chatMessagesRef.current) {
      chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
    }
  };
  
  // Format timestamp to display
  const formatMessageTime = (date) => {
    if (!date) return '';
    
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      // Today, just show time
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      // Older messages, show date and time
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + ' ' + 
             date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  // Handle chat message submission
  const handleChatSubmit = (e) => {
    e.preventDefault();
    if (newChatMessage.trim() || mediaToUpload) {
      sendChatMessage();
    }
  };
  
  // Media upload handlers
  const handleChatMediaUpload = (mediaType) => {
    try {
      const input = document.createElement('input');
      input.type = 'file';
      
      if (mediaType === 'image') {
        input.accept = 'image/*';
        setChatImageUpload(true);
        setChatVideoUpload(false);
      } else if (mediaType === 'video') {
        input.accept = 'video/*';
        setChatImageUpload(false);
        setChatVideoUpload(true);
      }
      
      input.onchange = async (e) => {
        if (e.target.files && e.target.files[0]) {
          try {
            setIsUploading(true);
            const file = e.target.files[0];
            
            // Compress image if it's an image
            if (mediaType === 'image') {
              const base64 = await compressImageToBase64(file);
              setMediaToUpload({
                type: 'image',
                data: base64
              });
            } else {
              // For video, we'll use an object URL for preview
              // and then convert to base64 on send
              const objectURL = URL.createObjectURL(file);
              setMediaToUpload({
                type: 'video',
                data: objectURL,
                file: file
              });
            }
            setIsUploading(false);
          } catch (err) {
            console.error("Error processing chat media:", err);
            setError("Failed to process media. Please try again with a smaller file.");
            setIsUploading(false);
          }
        }
      };
      
      input.click();
    } catch (err) {
      console.error("Error handling chat media upload:", err);
      setError("Could not access media. Please check permissions.");
    }
  };
  
  // Cancel media upload
  const cancelMediaUpload = () => {
    setMediaToUpload(null);
    setChatImageUpload(false);
    setChatVideoUpload(false);
  };
  
  // Convert video file to base64
  const videoToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };
  
  // Send chat message
  const sendChatMessage = async () => {
    if ((!newChatMessage.trim() && !mediaToUpload) || !currentUser || !firestoreRef.current) return;
    
    try {
      setIsUploading(true);
      
      // Get profile photo if available
      const photo = userProfilePictures[currentUser.uid] || null;
      
      // Get display name
      const displayName = userDisplayNames[currentUser.uid] || currentUser.displayName || currentUser.email || 'Me';
      
      // Create message data
      const messageData = {
        text: newChatMessage,
        sender: {
          uid: currentUser.uid,
          name: displayName,
          photo: photo
        },
        timestamp: serverTimestamp()
      };
      
      // Add media if present
      if (mediaToUpload) {
        if (mediaToUpload.type === 'image') {
          // Images are already in base64 format from compression
          messageData.mediaUrl = mediaToUpload.data;
          messageData.mediaType = 'image';
        } else if (mediaToUpload.type === 'video') {
          // For videos, convert to base64
          const videoBase64 = await videoToBase64(mediaToUpload.file);
          messageData.mediaUrl = videoBase64;
          messageData.mediaType = 'video';
        }
      }
      
      // Add to Firestore
      await addDoc(collection(firestoreRef.current, 'chatMessages'), messageData);
      
      // Clear input and media
      setNewChatMessage('');
      setMediaToUpload(null);
      setChatImageUpload(false);
      setChatVideoUpload(false);
      
      // Scroll to bottom of chat
      setTimeout(scrollToBottom, 100);
      
      // Play notification sound
      playNotificationSound();
    } catch (err) {
      console.error("Error sending chat message:", err);
      setError("Failed to send message. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };
  
  // Render chat message with profile picture and media
  const renderChatMessage = (message) => {
    const isCurrentUser = currentUser && message.sender && message.sender.uid === currentUser.uid;
    
    // Try to get profile picture from multiple sources
    let profilePic = null;
    
    // First check if message already has a photo
    if (message.sender.photo) {
      profilePic = message.sender.photo;
    } 
    // Then check our cached profile pictures
    else if (userProfilePictures[message.sender.uid]) {
      profilePic = userProfilePictures[message.sender.uid];
    }
    
    return (
      <div 
        key={message.id} 
        className={`chat-message ${isCurrentUser ? 'sent' : 'received'}`}
      >
        <div className="chat-message-header">
          <div className="chat-profile-pic">
            {profilePic ? (
              <img src={profilePic} alt={message.sender.name} />
            ) : (
              <div className="profile-placeholder">
                {message.sender.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          <div className="chat-message-sender">
            {message.sender.name}
          </div>
        </div>
        <div className="chat-message-content">
          {message.text}
        </div>
        
        {/* Render media if present */}
        {message.mediaUrl && message.mediaType === 'image' && (
          <div className="chat-media">
            <img 
              src={message.mediaUrl} 
              alt="Shared" 
              onClick={() => openImageViewer(message.mediaUrl)}
            />
          </div>
        )}
        
        {message.mediaUrl && message.mediaType === 'video' && (
          <div className="chat-media">
            <video 
              src={message.mediaUrl} 
              controls
              width="100%"
            />
          </div>
        )}
        
        <div className="chat-message-time">
          {formatMessageTime(message.timestamp)}
        </div>
      </div>
    );
  };

  return (
    <div className="chat-container">
      <div className="chat-header">
        <span>Team Chat</span>
        <span>{chatMessages.length} messages</span>
      </div>
      
      <div className="chat-messages" ref={chatMessagesRef}>
        {chatMessages.length === 0 ? (
          <div className="text-center text-gray-400 p-4">
            No messages yet. Start the conversation!
          </div>
        ) : (
          chatMessages.map(message => renderChatMessage(message))
        )}
      </div>
      
      <form className="chat-input-container" onSubmit={handleChatSubmit}>
        {mediaToUpload && (
          <div className="relative mr-2 bg-gray-700 rounded overflow-hidden" style={{ width: '40px', height: '40px' }}>
            {mediaToUpload.type === 'image' && (
              <img 
                src={mediaToUpload.data} 
                alt="Upload" 
                className="w-full h-full object-cover"
              />
            )}
            {mediaToUpload.type === 'video' && (
              <video 
                src={mediaToUpload.data} 
                className="w-full h-full object-cover"
              />
            )}
            <button
              type="button"
              className="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
              onClick={cancelMediaUpload}
              disabled={isUploading}
            >
              ×
            </button>
          </div>
        )}
        
        <input
          type="text"
          className="chat-input"
          placeholder={currentUser ? "Type your message..." : "Login to chat"}
          value={newChatMessage}
          onChange={(e) => setNewChatMessage(e.target.value)}
          disabled={!currentUser || isUploading}
        />
        
        <div className="chat-media-buttons">
          <button 
            type="button" 
            className="chat-media-btn image"
            onClick={() => handleChatMediaUpload('image')}
            disabled={!currentUser || isUploading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M6.002 5.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
              <path d="M2.002 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2h-12zm12 1a1 1 0 0 1 1 1v6.5l-3.777-1.947a.5.5 0 0 0-.577.093l-3.71 3.71-2.66-1.772a.5.5 0 0 0-.63.062L1.002 12V3a1 1 0 0 1 1-1h12z"/>
            </svg>
          </button>
          <button 
            type="button" 
            className="chat-media-btn video"
            onClick={() => handleChatMediaUpload('video')}
            disabled={!currentUser || isUploading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M0 5a2 2 0 0 1 2-2h7.5a2 2 0 0 1 1.983 1.738l3.11-1.382A1 1 0 0 1 16 4.269v7.462a1 1 0 0 1-1.406.913l-3.111-1.382A2 2 0 0 1 9.5 13H2a2 2 0 0 1-2-2V5zm11.5 5.175 3.5 1.556V4.269l-3.5 1.556v4.35zM2 4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h7.5a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1H2z"/>
            </svg>
          </button>
        </div>
        
        <button 
          type="submit" 
          className="send-chat-btn"
          disabled={(!newChatMessage.trim() && !mediaToUpload) || !currentUser || isUploading}
        >
          {isUploading ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Sending
            </span>
          ) : "Send"}
        </button>
      </form>
    </div>
  );
};

export default ChatComponent;