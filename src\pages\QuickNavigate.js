import React, { useState, useRef } from 'react';

const QuickNavigate = ({
  quickNavLinks = [],
  navigateToQuickLink,
  updateQuickNavLink,
  isAdmin = false,
  currentLocation = { lat: 0, lng: 0 },
  sectionHeight = 200
}) => {
  // State for section expansion/collapse
  const [isExpanded, setIsExpanded] = useState(true);
  // State for editing quick nav links
  const [editingQuickNavLink, setEditingQuickNavLink] = useState(null);
  const [editNavLinkData, setEditNavLinkData] = useState({
    name: '',
    position: { lat: 0, lng: 0 }
  });
  
  // References for input fields to prevent focus loss
  const nameInputRef = useRef(null);
  const latInputRef = useRef(null);
  const lngInputRef = useRef(null);
  
  // Local state for quick nav links to immediately show changes
  const [localQuickNavLinks, setLocalQuickNavLinks] = useState(quickNavLinks);
  
  // Update localQuickNavLinks when parent quickNavLinks changes
  React.useEffect(() => {
    if (quickNavLinks) {
      setLocalQuickNavLinks(quickNavLinks);
    }
  }, [quickNavLinks]);
  
  // Format coordinates for display
  const formatCoordinates = (position) => {
    if (!position || !position.lat || !position.lng) return 'No coordinates';
    return `${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`;
  };
  
  // Handle edit quick nav link
  const handleEditQuickNavLink = (link) => {
    if (!isAdmin) return;
    
    // Initialize with actual link data
    const linkData = {
      name: link.name || '',
      position: {
        lat: 0,
        lng: 0
      }
    };
    
    // Ensure we have valid position data
    if (link.position && typeof link.position.lat === 'number' && typeof link.position.lng === 'number') {
      linkData.position = {
        lat: link.position.lat,
        lng: link.position.lng
      };
    } else {
      // Use current location as fallback
      linkData.position = {
        lat: currentLocation.lat,
        lng: currentLocation.lng
      };
    }
    
    setEditNavLinkData(linkData);
    setEditingQuickNavLink(link);
  };
  
  // Save quick nav link with local UI update
  const saveQuickNavLinkPosition = async () => {
    if (!editingQuickNavLink || !editNavLinkData) return;
    
    try {
      // Validate fields
      if (!editNavLinkData.name || editNavLinkData.name.trim() === '') {
        alert("Please enter a name for the location.");
        return;
      }
      
      // Check if we have valid coordinates
      if (!editNavLinkData.position || 
          !editNavLinkData.position.lat || 
          !editNavLinkData.position.lng ||
          typeof editNavLinkData.position.lat !== 'number' || 
          typeof editNavLinkData.position.lng !== 'number') {
        alert("Invalid coordinates. Please enter valid numbers.");
        return;
      }
      
      // Create updated link with new position and name
      const updatedLink = {
        ...editingQuickNavLink,
        name: editNavLinkData.name.trim(),
        position: {
          lat: Number(editNavLinkData.position.lat),
          lng: Number(editNavLinkData.position.lng)
        }
      };
      
      // Immediately update local UI for instant feedback
      setLocalQuickNavLinks(prevLinks => 
        prevLinks.map(link => 
          link.id === updatedLink.id ? updatedLink : link
        )
      );
      
      // Call the provided update function
      if (typeof updateQuickNavLink === 'function') {
        const success = await updateQuickNavLink(updatedLink);
        
        if (!success) {
          console.error("Failed to update quick nav link in backend");
          alert("Failed to save changes. Please try again.");
        }
      } else {
        console.error("updateQuickNavLink function is not provided");
        alert("Cannot save location: update function not available. Please contact support.");
      }
      
      // Close the edit modal
      setEditingQuickNavLink(null);
      setEditNavLinkData({
        name: '',
        position: { lat: 0, lng: 0 }
      });
      
    } catch (error) {
      console.error("Error saving quick nav link:", error);
      alert("Error updating location. Please try again.");
    }
  };

  // Quick Nav Edit Dialog
  const QuickNavEditDialog = () => {
    if (!editingQuickNavLink) return null;
    
    // Handle name input change with event batching
    const handleNameChange = (e) => {
      const newName = e.target.value;
      setEditNavLinkData(prev => ({
        ...prev,
        name: newName
      }));
    };
    
    // Handle lat/lng input changes with event batching
    const handleLatChange = (e) => {
      const newLat = parseFloat(e.target.value);
      setEditNavLinkData(prev => ({
        ...prev,
        position: {
          ...prev.position,
          lat: newLat
        }
      }));
    };
    
    const handleLngChange = (e) => {
      const newLng = parseFloat(e.target.value);
      setEditNavLinkData(prev => ({
        ...prev,
        position: {
          ...prev.position,
          lng: newLng
        }
      }));
    };
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[2000]">
        <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg w-full max-w-md p-4">
          <h3 className="text-lg font-semibold text-white mb-2">Edit Quick Navigation Location</h3>
          
          <div className="space-y-4">
            {/* Name field */}
            <div>
              <label htmlFor="navLinkName" className="block text-sm font-medium text-gray-300 mb-1">
                Location Name
              </label>
              <input
                id="navLinkName"
                type="text"
                ref={nameInputRef}
                value={editNavLinkData.name}
                onChange={handleNameChange}
                placeholder="Enter location name"
                className="w-full bg-gray-700 border border-gray-600 rounded p-2"
              />
            </div>
            
            {/* Coordinates section */}
            <div className="pt-2 border-t border-gray-700">
              <h4 className="text-sm font-medium text-gray-300 mb-2">GPS Coordinates</h4>
              
              <div className="space-y-3">
                <div>
                  <label htmlFor="navLinkLat" className="block text-xs font-medium text-gray-400 mb-1">
                    Latitude
                  </label>
                  <input
                    id="navLinkLat"
                    type="number"
                    step="0.000001"
                    ref={latInputRef}
                    value={editNavLinkData.position?.lat || ''}
                    onChange={handleLatChange}
                    className="w-full bg-gray-700 border border-gray-600 rounded p-2"
                  />
                </div>
                
                <div>
                  <label htmlFor="navLinkLng" className="block text-xs font-medium text-gray-400 mb-1">
                    Longitude
                  </label>
                  <input
                    id="navLinkLng"
                    type="number"
                    step="0.000001"
                    ref={lngInputRef}
                    value={editNavLinkData.position?.lng || ''}
                    onChange={handleLngChange}
                    className="w-full bg-gray-700 border border-gray-600 rounded p-2"
                  />
                </div>
              </div>
            </div>
            
            {/* Current location helper */}
            <div className="flex justify-between items-center pt-2">
              <button
                onClick={() => setEditNavLinkData(prev => ({
                  ...prev,
                  position: {
                    lat: currentLocation.lat,
                    lng: currentLocation.lng
                  }
                }))}
                className="text-sm text-blue-400 hover:text-blue-300 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                Use Current Location
              </button>
              
              <div className="text-xs text-gray-400">
                Current: {formatCoordinates(currentLocation)}
              </div>
            </div>
          </div>
          
          <div className="flex justify-end gap-2 mt-4">
            <button 
              onClick={() => {
                setEditingQuickNavLink(null);
                setEditNavLinkData({
                  name: '',
                  position: { lat: 0, lng: 0 }
                });
              }}
              className="px-4 py-2 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors duration-200"
            >
              Cancel
            </button>
            <button 
              onClick={saveQuickNavLinkPosition}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors duration-200"
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="border-b border-gray-700 w-full mb-3">
      <div className="p-2 flex justify-between items-center bg-gray-800 w-full rounded-t">
        <div className="flex items-center">
          <span className="text-xs font-medium text-gray-300">NAVIGATE</span>
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-2 text-gray-400 hover:text-white"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-4 w-4 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
        {isAdmin && (
          <span className="text-xs text-gray-400">
            {localQuickNavLinks.length} locations
          </span>
        )}
      </div>
      
      {isExpanded && (
        <div 
          className="overflow-y-auto w-full bg-gray-900 rounded-b"
          style={{ maxHeight: `${sectionHeight}px` }}
        >
          {(localQuickNavLinks.length > 0) ? (
            <div className="p-2 bg-gray-900 w-full">
              <div className="flex flex-wrap gap-1">
                {/* Use local state for immediate UI update */}
                {localQuickNavLinks.map((link, index) => (
                  <div key={link.id || index} className="relative group">
                    <button
                      onClick={() => navigateToQuickLink(link)}
                      className="px-2 py-1 bg-gray-700 hover:bg-gray-600 text-xs rounded group-hover:pr-6"
                    >
                      {link.name}
                    </button>
                    
                    {/* Admin gear icon for editing - only visible on hover and for admins */}
                    {isAdmin && (
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditQuickNavLink(link);
                        }}
                        className="absolute right-1 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                        </svg>
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="p-4 text-center text-gray-400">
              No quick navigation links available
            </div>
          )}
        </div>
      )}
      
      {/* Quick Nav Edit Dialog */}
      <QuickNavEditDialog />
    </div>
  );
};

export default QuickNavigate;