// VehicleDriveTypeDatabase.js - Comprehensive American Vehicle Database (2000-2025)

// Vehicle Database Structure
const VEHICLE_DATABASE = {
    // FORD
    ford: {
      // TRUCKS
      'f-150': {
        years: '2000-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'supercab': { base: 'RWD', available: ['RWD', '4WD'] },
          'supercrew': { base: 'RWD', available: ['RWD', '4WD'] },
          'xl': { base: 'RWD', available: ['RWD', '4WD'] },
          'xlt': { base: 'RWD', available: ['RWD', '4WD'] },
          'lariat': { base: 'RWD', available: ['RWD', '4WD'] },
          'king ranch': { base: 'RWD', available: ['RWD', '4WD'] },
          'platinum': { base: 'RWD', available: ['RWD', '4WD'] },
          'limited': { base: 'RWD', available: ['RWD', '4WD'] },
          'raptor': { base: '4WD', available: ['4WD'] },
          'lightning': { base: 'AWD', available: ['AWD'] }, // Electric
          'tremor': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'f-250': {
        years: '2000-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'supercab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'xl': { base: 'RWD', available: ['RWD', '4WD'] },
          'xlt': { base: 'RWD', available: ['RWD', '4WD'] },
          'lariat': { base: 'RWD', available: ['RWD', '4WD'] },
          'king ranch': { base: 'RWD', available: ['RWD', '4WD'] },
          'platinum': { base: 'RWD', available: ['RWD', '4WD'] },
          'limited': { base: 'RWD', available: ['RWD', '4WD'] },
          'tremor': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'f-350': {
        years: '2000-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'supercab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'xl': { base: 'RWD', available: ['RWD', '4WD'] },
          'xlt': { base: 'RWD', available: ['RWD', '4WD'] },
          'lariat': { base: 'RWD', available: ['RWD', '4WD'] },
          'king ranch': { base: 'RWD', available: ['RWD', '4WD'] },
          'platinum': { base: 'RWD', available: ['RWD', '4WD'] },
          'limited': { base: 'RWD', available: ['RWD', '4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'ranger': {
        years: '2000-2011, 2019-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'supercab': { base: 'RWD', available: ['RWD', '4WD'] },
          'xl': { base: 'RWD', available: ['RWD', '4WD'] },
          'xlt': { base: 'RWD', available: ['RWD', '4WD'] },
          'lariat': { base: 'RWD', available: ['RWD', '4WD'] },
          'tremor': { base: '4WD', available: ['4WD'] },
          'raptor': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'maverick': {
        years: '2022-2025',
        trims: {
          'xl': { base: 'FWD', available: ['FWD', 'AWD'] },
          'xlt': { base: 'FWD', available: ['FWD', 'AWD'] },
          'lariat': { base: 'FWD', available: ['FWD', 'AWD'] },
          'tremor': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      // SUVS
      'expedition': {
        years: '2000-2025',
        trims: {
          'xlt': { base: 'RWD', available: ['RWD', '4WD'] },
          'limited': { base: 'RWD', available: ['RWD', '4WD'] },
          'platinum': { base: 'RWD', available: ['RWD', '4WD'] },
          'king ranch': { base: 'RWD', available: ['RWD', '4WD'] },
          'max': { base: 'RWD', available: ['RWD', '4WD'] },
          'el': { base: 'RWD', available: ['RWD', '4WD'] }, // Extended length
          'stealth': { base: 'RWD', available: ['RWD', '4WD'] },
          'timberline': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'explorer': {
        years: '2000-2025',
        trims: {
          'xls': { base: 'RWD', available: ['RWD', 'AWD'] }, // 2000-2010
          'xlt': { base: 'FWD', available: ['FWD', 'AWD'] }, // 2011+
          'limited': { base: 'FWD', available: ['FWD', 'AWD'] },
          'platinum': { base: 'AWD', available: ['AWD'] },
          'sport': { base: 'FWD', available: ['FWD', 'AWD'] },
          'base': { base: 'FWD', available: ['FWD', 'AWD'] },
          'st': { base: 'AWD', available: ['AWD'] }, // Performance
          'timberline': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'escape': {
        years: '2000-2025',
        trims: {
          'xls': { base: 'FWD', available: ['FWD', 'AWD'] }, // 2000-2007
          's': { base: 'FWD', available: ['FWD', 'AWD'] },
          'se': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sel': { base: 'FWD', available: ['FWD', 'AWD'] },
          'titanium': { base: 'FWD', available: ['FWD', 'AWD'] },
          'active': { base: 'FWD', available: ['FWD', 'AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'edge': {
        years: '2007-2025',
        trims: {
          'se': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sel': { base: 'FWD', available: ['FWD', 'AWD'] },
          'limited': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sport': { base: 'FWD', available: ['FWD', 'AWD'] },
          'titanium': { base: 'FWD', available: ['FWD', 'AWD'] },
          'st': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'bronco': {
        years: '2021-2025',
        trims: {
          'base': { base: '4WD', available: ['4WD'] },
          'big bend': { base: '4WD', available: ['4WD'] },
          'black diamond': { base: '4WD', available: ['4WD'] },
          'outer banks': { base: '4WD', available: ['4WD'] },
          'badlands': { base: '4WD', available: ['4WD'] },
          'wildtrak': { base: '4WD', available: ['4WD'] },
          'raptor': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: '4WD'
      },
      'bronco sport': {
        years: '2021-2025',
        trims: {
          'base': { base: 'FWD', available: ['FWD', 'AWD'] },
          'big bend': { base: 'FWD', available: ['FWD', 'AWD'] },
          'outer banks': { base: 'AWD', available: ['AWD'] },
          'badlands': { base: 'AWD', available: ['AWD'] },
          'heritage': { base: 'FWD', available: ['FWD', 'AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      // SEDANS
      'taurus': {
        years: '2000-2019',
        trims: {
          'se': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sel': { base: 'FWD', available: ['FWD', 'AWD'] },
          'limited': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sho': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'fusion': {
        years: '2006-2020',
        trims: {
          's': { base: 'FWD', available: ['FWD', 'AWD'] },
          'se': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sel': { base: 'FWD', available: ['FWD', 'AWD'] },
          'titanium': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sport': { base: 'AWD', available: ['AWD'] },
          'hybrid': { base: 'FWD', available: ['FWD'] }
        },
        defaultDriveType: 'FWD'
      },
      // PERFORMANCE
      'mustang': {
        years: '2000-2025',
        trims: {
          'base': { base: 'RWD', available: ['RWD'] },
          'v6': { base: 'RWD', available: ['RWD'] },
          'gt': { base: 'RWD', available: ['RWD'] },
          'cobra': { base: 'RWD', available: ['RWD'] },
          'mach 1': { base: 'RWD', available: ['RWD'] },
          'bullitt': { base: 'RWD', available: ['RWD'] },
          'shelby gt350': { base: 'RWD', available: ['RWD'] },
          'shelby gt500': { base: 'RWD', available: ['RWD'] },
          'dark horse': { base: 'RWD', available: ['RWD'] }
        },
        defaultDriveType: 'RWD'
      },
      'mustang mach-e': {
        years: '2021-2025',
        trims: {
          'select': { base: 'RWD', available: ['RWD', 'AWD'] },
          'premium': { base: 'RWD', available: ['RWD', 'AWD'] },
          'california route 1': { base: 'RWD', available: ['RWD'] },
          'gt': { base: 'AWD', available: ['AWD'] },
          'rally': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'RWD'
      }
    },
  
    // CHEVROLET
    chevrolet: {
      // TRUCKS
      'silverado': {
        years: '2000-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'extended cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'wt': { base: 'RWD', available: ['RWD', '4WD'] },
          'lt': { base: 'RWD', available: ['RWD', '4WD'] },
          'ltz': { base: 'RWD', available: ['RWD', '4WD'] },
          'high country': { base: 'RWD', available: ['RWD', '4WD'] },
          'rst': { base: 'RWD', available: ['RWD', '4WD'] },
          'trail boss': { base: '4WD', available: ['4WD'] },
          'z71': { base: '4WD', available: ['4WD'] },
          'zr2': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'silverado 2500': {
        years: '2000-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'extended cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'wt': { base: 'RWD', available: ['RWD', '4WD'] },
          'lt': { base: 'RWD', available: ['RWD', '4WD'] },
          'ltz': { base: 'RWD', available: ['RWD', '4WD'] },
          'high country': { base: 'RWD', available: ['RWD', '4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'silverado 3500': {
        years: '2000-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'extended cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'wt': { base: 'RWD', available: ['RWD', '4WD'] },
          'lt': { base: 'RWD', available: ['RWD', '4WD'] },
          'ltz': { base: 'RWD', available: ['RWD', '4WD'] },
          'high country': { base: 'RWD', available: ['RWD', '4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'colorado': {
        years: '2004-2012, 2015-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'extended cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'wt': { base: 'RWD', available: ['RWD', '4WD'] },
          'lt': { base: 'RWD', available: ['RWD', '4WD'] },
          'z71': { base: '4WD', available: ['4WD'] },
          'zr2': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      // SUVS
      'tahoe': {
        years: '2000-2025',
        trims: {
          'ls': { base: 'RWD', available: ['RWD', '4WD'] },
          'lt': { base: 'RWD', available: ['RWD', '4WD'] },
          'ltz': { base: 'RWD', available: ['RWD', '4WD'] },
          'premier': { base: 'RWD', available: ['RWD', '4WD'] },
          'high country': { base: 'RWD', available: ['RWD', '4WD'] },
          'rst': { base: 'RWD', available: ['RWD', '4WD'] },
          'z71': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'suburban': {
        years: '2000-2025',
        trims: {
          'ls': { base: 'RWD', available: ['RWD', '4WD'] },
          'lt': { base: 'RWD', available: ['RWD', '4WD'] },
          'ltz': { base: 'RWD', available: ['RWD', '4WD'] },
          'premier': { base: 'RWD', available: ['RWD', '4WD'] },
          'high country': { base: 'RWD', available: ['RWD', '4WD'] },
          'rst': { base: 'RWD', available: ['RWD', '4WD'] },
          'z71': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'traverse': {
        years: '2009-2025',
        trims: {
          'ls': { base: 'FWD', available: ['FWD', 'AWD'] },
          'lt': { base: 'FWD', available: ['FWD', 'AWD'] },
          'ltz': { base: 'FWD', available: ['FWD', 'AWD'] },
          'premier': { base: 'FWD', available: ['FWD', 'AWD'] },
          'high country': { base: 'AWD', available: ['AWD'] },
          'rs': { base: 'FWD', available: ['FWD', 'AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'equinox': {
        years: '2005-2025',
        trims: {
          'ls': { base: 'FWD', available: ['FWD', 'AWD'] },
          'lt': { base: 'FWD', available: ['FWD', 'AWD'] },
          'ltz': { base: 'FWD', available: ['FWD', 'AWD'] },
          'premier': { base: 'FWD', available: ['FWD', 'AWD'] },
          'activ': { base: 'AWD', available: ['AWD'] },
          'rs': { base: 'FWD', available: ['FWD', 'AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'blazer': {
        years: '2000-2005, 2019-2025',
        trims: {
          'ls': { base: 'FWD', available: ['FWD', 'AWD'] },
          'lt': { base: 'FWD', available: ['FWD', 'AWD'] },
          'premier': { base: 'FWD', available: ['FWD', 'AWD'] },
          'rs': { base: 'FWD', available: ['FWD', 'AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      // SEDANS
      'impala': {
        years: '2000-2020',
        trims: {
          'ls': { base: 'FWD', available: ['FWD'] },
          'lt': { base: 'FWD', available: ['FWD'] },
          'ltz': { base: 'FWD', available: ['FWD'] },
          'premier': { base: 'FWD', available: ['FWD'] },
          'ss': { base: 'FWD', available: ['FWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'malibu': {
        years: '2000-2025',
        trims: {
          'ls': { base: 'FWD', available: ['FWD'] },
          'lt': { base: 'FWD', available: ['FWD'] },
          'ltz': { base: 'FWD', available: ['FWD'] },
          'premier': { base: 'FWD', available: ['FWD'] },
          'hybrid': { base: 'FWD', available: ['FWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'cruze': {
        years: '2011-2019',
        trims: {
          'ls': { base: 'FWD', available: ['FWD'] },
          'lt': { base: 'FWD', available: ['FWD'] },
          'ltz': { base: 'FWD', available: ['FWD'] },
          'premier': { base: 'FWD', available: ['FWD'] },
          'diesel': { base: 'FWD', available: ['FWD'] }
        },
        defaultDriveType: 'FWD'
      },
      // PERFORMANCE
      'camaro': {
        years: '2010-2025',
        trims: {
          'ls': { base: 'RWD', available: ['RWD'] },
          'lt': { base: 'RWD', available: ['RWD'] },
          'ss': { base: 'RWD', available: ['RWD'] },
          'zl1': { base: 'RWD', available: ['RWD'] },
          'z/28': { base: 'RWD', available: ['RWD'] },
          '1le': { base: 'RWD', available: ['RWD'] }
        },
        defaultDriveType: 'RWD'
      },
      'corvette': {
        years: '2000-2025',
        trims: {
          'base': { base: 'RWD', available: ['RWD'] },
          'grand sport': { base: 'RWD', available: ['RWD'] },
          'z06': { base: 'RWD', available: ['RWD'] },
          'zr1': { base: 'RWD', available: ['RWD'] },
          'e-ray': { base: 'AWD', available: ['AWD'] }, // Hybrid
          'stingray': { base: 'RWD', available: ['RWD'] }
        },
        defaultDriveType: 'RWD'
      }
    },
  
    // GMC
    gmc: {
      // TRUCKS
      'sierra': {
        years: '2000-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'extended cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'base': { base: 'RWD', available: ['RWD', '4WD'] },
          'sle': { base: 'RWD', available: ['RWD', '4WD'] },
          'slt': { base: 'RWD', available: ['RWD', '4WD'] },
          'denali': { base: 'RWD', available: ['RWD', '4WD'] },
          'at4': { base: '4WD', available: ['4WD'] },
          'elevation': { base: 'RWD', available: ['RWD', '4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'sierra 2500': {
        years: '2000-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'extended cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'base': { base: 'RWD', available: ['RWD', '4WD'] },
          'sle': { base: 'RWD', available: ['RWD', '4WD'] },
          'slt': { base: 'RWD', available: ['RWD', '4WD'] },
          'denali': { base: 'RWD', available: ['RWD', '4WD'] },
          'at4': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'sierra 3500': {
        years: '2000-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'extended cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'base': { base: 'RWD', available: ['RWD', '4WD'] },
          'sle': { base: 'RWD', available: ['RWD', '4WD'] },
          'slt': { base: 'RWD', available: ['RWD', '4WD'] },
          'denali': { base: 'RWD', available: ['RWD', '4WD'] },
          'at4': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'canyon': {
        years: '2004-2012, 2015-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'extended cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'base': { base: 'RWD', available: ['RWD', '4WD'] },
          'sle': { base: 'RWD', available: ['RWD', '4WD'] },
          'slt': { base: 'RWD', available: ['RWD', '4WD'] },
          'denali': { base: 'RWD', available: ['RWD', '4WD'] },
          'at4': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      // SUVS
      'yukon': {
        years: '2000-2025',
        trims: {
          'sle': { base: 'RWD', available: ['RWD', '4WD'] },
          'slt': { base: 'RWD', available: ['RWD', '4WD'] },
          'denali': { base: 'RWD', available: ['RWD', '4WD'] },
          'at4': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'yukon xl': {
        years: '2000-2025',
        trims: {
          'sle': { base: 'RWD', available: ['RWD', '4WD'] },
          'slt': { base: 'RWD', available: ['RWD', '4WD'] },
          'denali': { base: 'RWD', available: ['RWD', '4WD'] },
          'at4': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'acadia': {
        years: '2007-2025',
        trims: {
          'sle': { base: 'FWD', available: ['FWD', 'AWD'] },
          'slt': { base: 'FWD', available: ['FWD', 'AWD'] },
          'denali': { base: 'FWD', available: ['FWD', 'AWD'] },
          'at4': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'terrain': {
        years: '2010-2025',
        trims: {
          'sle': { base: 'FWD', available: ['FWD', 'AWD'] },
          'slt': { base: 'FWD', available: ['FWD', 'AWD'] },
          'denali': { base: 'FWD', available: ['FWD', 'AWD'] },
          'at4': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'FWD'
      }
    },
  
    // DODGE
    dodge: {
      // TRUCKS
      'ram': {
        years: '2000-2010', // After 2010, Ram became its own brand
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'extended cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'st': { base: 'RWD', available: ['RWD', '4WD'] },
          'slt': { base: 'RWD', available: ['RWD', '4WD'] },
          'laramie': { base: 'RWD', available: ['RWD', '4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      // SUVS
      'durango': {
        years: '2000-2025',
        trims: {
          'sxt': { base: 'RWD', available: ['RWD', 'AWD'] },
          'gt': { base: 'RWD', available: ['RWD', 'AWD'] },
          'citadel': { base: 'RWD', available: ['RWD', 'AWD'] },
          'r/t': { base: 'RWD', available: ['RWD', 'AWD'] },
          'srt': { base: 'RWD', available: ['RWD', 'AWD'] },
          'hellcat': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'RWD'
      },
      'journey': {
        years: '2009-2020',
        trims: {
          'se': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sxt': { base: 'FWD', available: ['FWD', 'AWD'] },
          'gt': { base: 'FWD', available: ['FWD', 'AWD'] },
          'crossroad': { base: 'FWD', available: ['FWD', 'AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      // SEDANS
      'charger': {
        years: '2006-2025',
        trims: {
          'se': { base: 'RWD', available: ['RWD', 'AWD'] },
          'sxt': { base: 'RWD', available: ['RWD', 'AWD'] },
          'gt': { base: 'RWD', available: ['RWD', 'AWD'] },
          'r/t': { base: 'RWD', available: ['RWD', 'AWD'] },
          'srt': { base: 'RWD', available: ['RWD'] },
          'hellcat': { base: 'RWD', available: ['RWD'] },
          'redeye': { base: 'RWD', available: ['RWD'] },
          'jailbreak': { base: 'RWD', available: ['RWD'] }
        },
        defaultDriveType: 'RWD'
      },
      // PERFORMANCE
      'challenger': {
        years: '2008-2025',
        trims: {
          'se': { base: 'RWD', available: ['RWD', 'AWD'] },
          'sxt': { base: 'RWD', available: ['RWD', 'AWD'] },
          'gt': { base: 'RWD', available: ['RWD', 'AWD'] },
          'r/t': { base: 'RWD', available: ['RWD'] },
          'srt': { base: 'RWD', available: ['RWD'] },
          'hellcat': { base: 'RWD', available: ['RWD'] },
          'redeye': { base: 'RWD', available: ['RWD'] },
          'demon': { base: 'RWD', available: ['RWD'] },
          'jailbreak': { base: 'RWD', available: ['RWD'] }
        },
        defaultDriveType: 'RWD'
      },
      'viper': {
        years: '2003-2017',
        trims: {
          'srt': { base: 'RWD', available: ['RWD'] },
          'gts': { base: 'RWD', available: ['RWD'] },
          'acr': { base: 'RWD', available: ['RWD'] }
        },
        defaultDriveType: 'RWD'
      }
    },
  
    // RAM (separate brand after 2010)
    ram: {
      '1500': {
        years: '2011-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'quad cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'tradesman': { base: 'RWD', available: ['RWD', '4WD'] },
          'big horn': { base: 'RWD', available: ['RWD', '4WD'] },
          'lone star': { base: 'RWD', available: ['RWD', '4WD'] },
          'laramie': { base: 'RWD', available: ['RWD', '4WD'] },
          'longhorn': { base: 'RWD', available: ['RWD', '4WD'] },
          'limited': { base: 'RWD', available: ['RWD', '4WD'] },
          'rebel': { base: '4WD', available: ['4WD'] },
          'trx': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      '2500': {
        years: '2011-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'tradesman': { base: 'RWD', available: ['RWD', '4WD'] },
          'big horn': { base: 'RWD', available: ['RWD', '4WD'] },
          'laramie': { base: 'RWD', available: ['RWD', '4WD'] },
          'longhorn': { base: 'RWD', available: ['RWD', '4WD'] },
          'limited': { base: 'RWD', available: ['RWD', '4WD'] },
          'power wagon': { base: '4WD', available: ['4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      '3500': {
        years: '2011-2025',
        trims: {
          'regular cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'crew cab': { base: 'RWD', available: ['RWD', '4WD'] },
          'tradesman': { base: 'RWD', available: ['RWD', '4WD'] },
          'big horn': { base: 'RWD', available: ['RWD', '4WD'] },
          'laramie': { base: 'RWD', available: ['RWD', '4WD'] },
          'longhorn': { base: 'RWD', available: ['RWD', '4WD'] },
          'limited': { base: 'RWD', available: ['RWD', '4WD'] }
        },
        defaultDriveType: 'RWD'
      }
    },
  
    // CHRYSLER
    chrysler: {
      '300': {
        years: '2005-2025',
        trims: {
          'touring': { base: 'RWD', available: ['RWD', 'AWD'] },
          'limited': { base: 'RWD', available: ['RWD', 'AWD'] },
          's': { base: 'RWD', available: ['RWD', 'AWD'] },
          'c': { base: 'RWD', available: ['RWD', 'AWD'] },
          'srt': { base: 'RWD', available: ['RWD'] }
        },
        defaultDriveType: 'RWD'
      },
      'pacifica': {
        years: '2004-2008, 2017-2025',
        trims: {
          'touring': { base: 'FWD', available: ['FWD', 'AWD'] },
          'touring l': { base: 'FWD', available: ['FWD', 'AWD'] },
          'limited': { base: 'FWD', available: ['FWD', 'AWD'] },
          'pinnacle': { base: 'FWD', available: ['FWD', 'AWD'] },
          'hybrid': { base: 'FWD', available: ['FWD'] }
        },
        defaultDriveType: 'FWD'
      }
    },
  
    // CADILLAC
    cadillac: {
      // SUVS
      'escalade': {
        years: '2000-2025',
        trims: {
          'base': { base: 'RWD', available: ['RWD', '4WD'] },
          'luxury': { base: 'RWD', available: ['RWD', '4WD'] },
          'premium luxury': { base: 'RWD', available: ['RWD', '4WD'] },
          'platinum': { base: 'RWD', available: ['RWD', '4WD'] },
          'sport': { base: 'RWD', available: ['RWD', '4WD'] },
          'v': { base: 'RWD', available: ['RWD', '4WD'] },
          'esv': { base: 'RWD', available: ['RWD', '4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'xt4': {
        years: '2019-2025',
        trims: {
          'luxury': { base: 'FWD', available: ['FWD', 'AWD'] },
          'premium luxury': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sport': { base: 'FWD', available: ['FWD', 'AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'xt5': {
        years: '2017-2025',
        trims: {
          'luxury': { base: 'FWD', available: ['FWD', 'AWD'] },
          'premium luxury': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sport': { base: 'FWD', available: ['FWD', 'AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'xt6': {
        years: '2020-2025',
        trims: {
          'luxury': { base: 'FWD', available: ['FWD', 'AWD'] },
          'premium luxury': { base: 'FWD', available: ['FWD', 'AWD'] },
          'sport': { base: 'FWD', available: ['FWD', 'AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      // SEDANS
      'ct4': {
        years: '2020-2025',
        trims: {
          'luxury': { base: 'RWD', available: ['RWD', 'AWD'] },
          'premium luxury': { base: 'RWD', available: ['RWD', 'AWD'] },
          'sport': { base: 'RWD', available: ['RWD', 'AWD'] },
          'v': { base: 'RWD', available: ['RWD'] }
        },
        defaultDriveType: 'RWD'
      },
      'ct5': {
        years: '2020-2025',
        trims: {
          'luxury': { base: 'RWD', available: ['RWD', 'AWD'] },
          'premium luxury': { base: 'RWD', available: ['RWD', 'AWD'] },
          'sport': { base: 'RWD', available: ['RWD', 'AWD'] },
          'v': { base: 'RWD', available: ['RWD'] }
        },
        defaultDriveType: 'RWD'
      },
      'cts': {
        years: '2003-2019',
        trims: {
          'base': { base: 'RWD', available: ['RWD', 'AWD'] },
          'luxury': { base: 'RWD', available: ['RWD', 'AWD'] },
          'premium': { base: 'RWD', available: ['RWD', 'AWD'] },
          'v': { base: 'RWD', available: ['RWD'] }
        },
        defaultDriveType: 'RWD'
      }
    },
  
    // LINCOLN
    lincoln: {
      // SUVS
      'navigator': {
        years: '2000-2025',
        trims: {
          'base': { base: 'RWD', available: ['RWD', '4WD'] },
          'luxury': { base: 'RWD', available: ['RWD', '4WD'] },
          'reserve': { base: 'RWD', available: ['RWD', '4WD'] },
          'black label': { base: 'RWD', available: ['RWD', '4WD'] },
          'l': { base: 'RWD', available: ['RWD', '4WD'] }
        },
        defaultDriveType: 'RWD'
      },
      'aviator': {
        years: '2003-2005, 2020-2025',
        trims: {
          'base': { base: 'RWD', available: ['RWD', 'AWD'] },
          'reserve': { base: 'RWD', available: ['RWD', 'AWD'] },
          'black label': { base: 'AWD', available: ['AWD'] },
          'grand touring': { base: 'AWD', available: ['AWD'] } // Hybrid
        },
        defaultDriveType: 'RWD'
      },
      'corsair': {
        years: '2020-2025',
        trims: {
          'base': { base: 'FWD', available: ['FWD', 'AWD'] },
          'reserve': { base: 'FWD', available: ['FWD', 'AWD'] },
          'black label': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'nautilus': {
        years: '2019-2025',
        trims: {
          'base': { base: 'FWD', available: ['FWD', 'AWD'] },
          'reserve': { base: 'FWD', available: ['FWD', 'AWD'] },
          'black label': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'FWD'
      }
    },
  
    // BUICK
    buick: {
      'enclave': {
        years: '2008-2025',
        trims: {
          'base': { base: 'FWD', available: ['FWD', 'AWD'] },
          'essence': { base: 'FWD', available: ['FWD', 'AWD'] },
          'premium': { base: 'FWD', available: ['FWD', 'AWD'] },
          'avenir': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'envision': {
        years: '2016-2025',
        trims: {
          'base': { base: 'FWD', available: ['FWD', 'AWD'] },
          'essence': { base: 'FWD', available: ['FWD', 'AWD'] },
          'premium': { base: 'FWD', available: ['FWD', 'AWD'] },
          'avenir': { base: 'AWD', available: ['AWD'] }
        },
        defaultDriveType: 'FWD'
      },
      'encore': {
        years: '2013-2025',
        trims: {
          'base': { base: 'FWD', available: ['FWD', 'AWD'] },
          'essence': { base: 'FWD', available: ['FWD', 'AWD'] },
          'premium': { base: 'FWD', available: ['FWD', 'AWD'] },
          'gx': { base: 'FWD', available: ['FWD', 'AWD'] }
        },
        defaultDriveType: 'FWD'
      }
    }
  };
  
  // Common vehicle colors
  const VEHICLE_COLORS = [
    'Black', 'White', 'Silver', 'Gray', 'Red', 'Blue', 'Green', 
    'Yellow', 'Orange', 'Brown', 'Gold', 'Beige', 'Tan', 'Burgundy',
    'Purple', 'Pink', 'Teal', 'Navy', 'Charcoal', 'Pearl White',
    'Metallic Silver', 'Dark Blue', 'Light Blue', 'Dark Gray'
  ];
  
  // Main function to detect drive type
  export const autoDetectDriveType = (vehicleName) => {
    if (!vehicleName || typeof vehicleName !== 'string') {
      return 'FWD'; // Default fallback
    }
  
    const name = vehicleName.toLowerCase().trim();
    
    // First, check for explicit drive type mentions in the name
    if (name.includes('awd') || name.includes('all wheel') || name.includes('all-wheel')) return 'AWD';
    if (name.includes('4wd') || name.includes('4x4') || name.includes('four wheel') || name.includes('4-wheel')) return '4WD';
    if (name.includes('rwd') || name.includes('rear wheel')) return 'RWD';
    if (name.includes('fwd') || name.includes('front wheel')) return 'FWD';
    
    // Extract year if present (4 digits)
    const yearMatch = name.match(/\b(19|20)\d{2}\b/);
    const year = yearMatch ? parseInt(yearMatch[0]) : null;
    
    // Check for specific trim indicators
    const trimIndicators = {
      '4wd': ['4wd', '4x4', 'z71', 'trail boss', 'zr2', 'at4', 'tremor', 'raptor', 'trx', 'rebel', 'power wagon'],
      'awd': ['awd', 'xdrive', 'xi', 'quattro', '4matic', 'sh-awd', 'shawd', 'acr', 'all4'],
      'rwd': ['rwd', 'rear wheel', 'srt', 'hellcat', 'gt', 'ss', 'rs', 'zl1', 'z06', 'zr1']
    };
    
    // Check for trim-specific indicators
    for (const [driveType, indicators] of Object.entries(trimIndicators)) {
      for (const indicator of indicators) {
        if (name.includes(indicator)) {
          return driveType.toUpperCase();
        }
      }
    }
    
    // Parse manufacturer and model
    const manufacturers = Object.keys(VEHICLE_DATABASE);
    let manufacturer = null;
    let model = null;
    
    // Find manufacturer
    for (const mfg of manufacturers) {
      if (name.includes(mfg)) {
        manufacturer = mfg;
        break;
      }
    }
    
    // If no manufacturer found, try common brand variations
    if (!manufacturer) {
      const brandVariations = {
        'chevy': 'chevrolet',
        'gmc': 'gmc',
        'caddy': 'cadillac'
      };
      
      for (const [variation, brand] of Object.entries(brandVariations)) {
        if (name.includes(variation)) {
          manufacturer = brand;
          break;
        }
      }
    }
    
    if (manufacturer && VEHICLE_DATABASE[manufacturer]) {
      // Find model
      const models = Object.keys(VEHICLE_DATABASE[manufacturer]);
      for (const modelName of models) {
        if (name.includes(modelName)) {
          model = modelName;
          break;
        }
      }
      
      if (model && VEHICLE_DATABASE[manufacturer][model]) {
        const vehicleData = VEHICLE_DATABASE[manufacturer][model];
        
        // Check if year is within range
        if (year && vehicleData.years) {
          const yearRanges = vehicleData.years.split(', ');
          let yearInRange = false;
          
          for (const range of yearRanges) {
            if (range.includes('-')) {
              const [start, end] = range.split('-').map(y => parseInt(y));
              if (year >= start && year <= end) {
                yearInRange = true;
                break;
              }
            } else {
              if (year === parseInt(range)) {
                yearInRange = true;
                break;
              }
            }
          }
          
          if (!yearInRange) {
            return vehicleData.defaultDriveType || 'FWD';
          }
        }
        
        // Check for specific trim levels
        if (vehicleData.trims) {
          for (const [trimName, trimData] of Object.entries(vehicleData.trims)) {
            if (name.includes(trimName)) {
              return trimData.base;
            }
          }
        }
        
        // Return default for the model
        return vehicleData.defaultDriveType || 'FWD';
      }
    }
    
    // Fallback logic for common patterns
    if (name.includes('truck') || name.includes('pickup')) {
      return 'RWD'; // Most trucks are RWD by default
    }
    
    if (name.includes('suv') && (name.includes('full size') || name.includes('large'))) {
      return 'RWD'; // Large SUVs often RWD
    }
    
    if (name.includes('sedan') || name.includes('car')) {
      return 'FWD'; // Most sedans are FWD
    }
    
    // Default fallback
    return 'FWD';
  };
  
  // Get detailed drive type information
  export const getDriveTypeInfo = (vehicleName) => {
    if (!vehicleName || typeof vehicleName !== 'string') {
      return {
        detectedType: 'FWD',
        possibleTypes: ['FWD'],
        subModels: ['Standard: FWD']
      };
    }
  
    const name = vehicleName.toLowerCase().trim();
    const detectedType = autoDetectDriveType(vehicleName);
    
    let info = {
      detectedType: detectedType,
      possibleTypes: [detectedType],
      subModels: [`Standard: ${detectedType}`]
    };
    
    // Parse manufacturer and model
    const manufacturers = Object.keys(VEHICLE_DATABASE);
    let manufacturer = null;
    let model = null;
    
    // Find manufacturer
    for (const mfg of manufacturers) {
      if (name.includes(mfg)) {
        manufacturer = mfg;
        break;
      }
    }
    
    // Brand variations
    if (!manufacturer) {
      const brandVariations = {
        'chevy': 'chevrolet',
        'gmc': 'gmc',
        'caddy': 'cadillac'
      };
      
      for (const [variation, brand] of Object.entries(brandVariations)) {
        if (name.includes(variation)) {
          manufacturer = brand;
          break;
        }
      }
    }
    
    if (manufacturer && VEHICLE_DATABASE[manufacturer]) {
      // Find model
      const models = Object.keys(VEHICLE_DATABASE[manufacturer]);
      for (const modelName of models) {
        if (name.includes(modelName)) {
          model = modelName;
          break;
        }
      }
      
      if (model && VEHICLE_DATABASE[manufacturer][model]) {
        const vehicleData = VEHICLE_DATABASE[manufacturer][model];
        
        if (vehicleData.trims) {
          const possibleTypes = new Set();
          const subModels = [];
          
          for (const [trimName, trimData] of Object.entries(vehicleData.trims)) {
            possibleTypes.add(trimData.base);
            if (trimData.available) {
              trimData.available.forEach(type => possibleTypes.add(type));
            }
            
            subModels.push(`${trimName}: ${trimData.base}${trimData.available && trimData.available.length > 1 ? ` (${trimData.available.join(', ')} available)` : ''}`);
          }
          
          info.possibleTypes = Array.from(possibleTypes);
          info.subModels = subModels;
        }
      }
    }
    
    return info;
  };
  
  // Get all vehicle colors
  export const getVehicleColors = () => {
    return VEHICLE_COLORS;
  };
  
  // Search for vehicles by partial name
  export const searchVehicles = (searchTerm) => {
    if (!searchTerm || typeof searchTerm !== 'string') {
      return [];
    }
  
    const term = searchTerm.toLowerCase().trim();
    const results = [];
    
    for (const [manufacturer, models] of Object.entries(VEHICLE_DATABASE)) {
      for (const [model, data] of Object.entries(models)) {
        const fullName = `${manufacturer} ${model}`;
        if (fullName.includes(term) || model.includes(term)) {
          results.push({
            manufacturer: manufacturer,
            model: model,
            fullName: fullName,
            years: data.years,
            defaultDriveType: data.defaultDriveType,
            trims: Object.keys(data.trims || {})
          });
        }
      }
    }
    
    return results.slice(0, 10); // Limit to top 10 results
  };
  
  // Get all manufacturers
  export const getManufacturers = () => {
    return Object.keys(VEHICLE_DATABASE);
  };
  
  // Get models for a manufacturer
  export const getModelsForManufacturer = (manufacturer) => {
    if (!manufacturer || !VEHICLE_DATABASE[manufacturer]) {
      return [];
    }
    
    return Object.keys(VEHICLE_DATABASE[manufacturer]);
  };
  
  // Export the database for direct access if needed
  export { VEHICLE_DATABASE };
  
  export default {
    autoDetectDriveType,
    getDriveTypeInfo,
    getVehicleColors,
    searchVehicles,
    getManufacturers,
    getModelsForManufacturer,
    VEHICLE_DATABASE
  };