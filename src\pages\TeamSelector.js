import React, { useState, useEffect } from 'react';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { createTeam, addUserToTeam } from './firebaseService';

const TeamSelector = ({ db, currentUser, onTeamSelect, isAdmin, userTeams = [], onCreateTeam, noTeamAccess = false, error = null }) => {
  const [loading, setLoading] = useState(false);
  const [directlyFetchedTeams, setDirectlyFetchedTeams] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreatingTeam, setIsCreatingTeam] = useState(false);
  const [newTeamName, setNewTeamName] = useState('');
  const [newTeamArea, setNewTeamArea] = useState('');
  const [areas, setAreas] = useState([]);
  const [localError, setLocalError] = useState(null);
  
  // Fetch teams directly from the database
  useEffect(() => {
    const fetchTeams = async () => {
      if (!db) return;
      
      try {
        setLoading(true);
        setLocalError(null);
        console.log("Directly fetching all teams from database...");
        
        const teamsCollection = collection(db, 'teams');
        // You can use the following line instead if you want to sort by name
        // const teamsQuery = query(teamsCollection, orderBy('name'));
        const teamsSnapshot = await getDocs(teamsCollection);
        
        if (teamsSnapshot.empty) {
          console.log("No teams found in database");
          setDirectlyFetchedTeams([]);
        } else {
          const teamsData = teamsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          
          console.log(`Found ${teamsData.length} teams in database:`, teamsData);
          setDirectlyFetchedTeams(teamsData);
        }
      } catch (error) {
        console.error("Error fetching teams:", error);
        setLocalError("Failed to load teams. Please try again.");
      } finally {
        setLoading(false);
      }
    };
    
    const fetchAreas = async () => {
      if (!db) return;
      
      try {
        const areasCollection = collection(db, 'areas');
        const areasSnapshot = await getDocs(areasCollection);
        
        if (!areasSnapshot.empty) {
          const areasData = areasSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          setAreas(areasData);
        } else {
          // Default areas if none exist
          setAreas([
            { id: 'north', name: 'North Region' },
            { id: 'south', name: 'South Region' },
            { id: 'east', name: 'East Region' },
            { id: 'west', name: 'West Region' },
            { id: 'central', name: 'Central Region' }
          ]);
        }
      } catch (error) {
        console.error("Error fetching areas:", error);
      }
    };
    
    fetchTeams();
    fetchAreas();
  }, [db]);
  
  // Teams to display - combine directly fetched teams with user teams
  const allFetchedTeams = isAdmin ? directlyFetchedTeams : 
    userTeams.length > 0 ? userTeams : directlyFetchedTeams;
  
  // Filter teams based on search term
  const filteredTeams = allFetchedTeams.filter(team => 
    team.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    team.area?.name?.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Handle creating a new team
  const handleCreateTeam = async () => {
    if (!newTeamName) {
      setLocalError('Please enter a team name');
      return;
    }
    
    setLoading(true);
    try {
      // Create team in Firestore
      const selectedAreaObj = areas.find(a => a.id === newTeamArea);
      const areaData = selectedAreaObj ? { id: selectedAreaObj.id, name: selectedAreaObj.name } : null;
      
      if (typeof onCreateTeam === 'function') {
        // Use the parent component's create team function if provided
        await onCreateTeam(newTeamName, areaData);
      } else {
        // Otherwise use the local implementation
        const teamId = await createTeam(db, {
          name: newTeamName,
          area: areaData,
          areaId: newTeamArea || null,
          teamSize: { spotters: 0, towDrivers: 0 }
        }, currentUser?.uid);
        
        // Add current user to team
        await addUserToTeam(db, currentUser.uid, teamId, 'admin', currentUser.uid);
        
        // Select the newly created team
        onTeamSelect(teamId);
      }
      
      // Reset form
      setIsCreatingTeam(false);
      setNewTeamName('');
      setNewTeamArea('');
    } catch (error) {
      console.error("Error creating team:", error);
      setLocalError('Failed to create team. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Get color for team card based on index
  const getTeamColor = (index) => {
    const colors = [
      "from-blue-700 to-blue-600",
      "from-purple-700 to-purple-600",
      "from-green-700 to-green-600",
      "from-red-700 to-red-600",
      "from-yellow-700 to-yellow-600",
      "from-indigo-700 to-indigo-600",
      "from-pink-700 to-pink-600",
      "from-teal-700 to-teal-600"
    ];
    
    return colors[index % colors.length];
  };
  
  // Render create team form
  if (isCreatingTeam) {
    return (
      <div className="p-6 bg-gray-800 rounded-lg shadow-lg max-w-md mx-auto">
        <h2 className="text-xl font-bold text-white mb-4">Create New Team</h2>
        
        {(localError || error) && (
          <div className="mb-4 p-3 bg-red-900 bg-opacity-50 text-red-100 rounded-md">
            {localError || error}
          </div>
        )}
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Team Name*
            </label>
            <input
              type="text"
              value={newTeamName}
              onChange={(e) => setNewTeamName(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter team name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Area
            </label>
            <select
              value={newTeamArea}
              onChange={(e) => setNewTeamArea(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select an area</option>
              {areas.map(area => (
                <option key={area.id} value={area.id}>{area.name}</option>
              ))}
            </select>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => setIsCreatingTeam(false)}
              className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              onClick={handleCreateTeam}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-500"
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Team'}
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-white mb-6 text-center">
        {isAdmin ? "Select Any Team" : "Select Your Team"}
      </h1>
      
      {(localError || error || noTeamAccess) && (
        <div className="mb-6 p-4 bg-red-900 bg-opacity-50 text-red-100 rounded-lg max-w-lg mx-auto">
          {noTeamAccess ? "You don't have access to the requested team." : (localError || error)}
        </div>
      )}
      
      {/* Search bar for multiple teams */}
      {allFetchedTeams.length > 4 && (
        <div className="mb-6 mx-auto max-w-lg">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search teams..."
            className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      )}
      
      {/* Team cards */}
      {loading ? (
        <div className="flex justify-center items-center py-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredTeams.length === 0 ? (
        <div className="text-center py-10 bg-gray-800 rounded-lg max-w-lg mx-auto">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <p className="text-gray-400 mb-6">No teams found. Try creating a new team.</p>
          <button 
            onClick={() => setIsCreatingTeam(true)}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-md"
          >
            Create Your First Team
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTeams.map((team, index) => (
            <div
              key={team.id}
              onClick={() => onTeamSelect(team.id)}
              className={`cursor-pointer bg-gradient-to-r ${getTeamColor(index)} hover:brightness-110 transition-all duration-200 rounded-lg shadow-lg p-6 flex flex-col`}
            >
              <h2 className="text-xl font-bold text-white mb-2">{team.name}</h2>
              <div className="text-sm text-gray-200 mb-1">
                Area: {team.area?.name || "No Area Assigned"}
              </div>
              <div className="text-sm text-gray-300 mt-auto flex justify-between items-center">
                <span>{team.memberCount || 0} members</span>
                {team.createdAt && (
                  <span>Created: {new Date(team.createdAt.toDate ? team.createdAt.toDate() : team.createdAt).toLocaleDateString()}</span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* Admin or empty state actions */}
      <div className="mt-8 text-center">
        {(!isAdmin && filteredTeams.length > 0) && (
          <button 
            onClick={() => setIsCreatingTeam(true)}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-white mr-3"
          >
            Create New Team
          </button>
        )}
        
        {isAdmin && (
          <>
            <button 
              onClick={() => setIsCreatingTeam(true)}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-white mr-3"
            >
              Create Team
            </button>
            <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white">
              Manage Teams
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default TeamSelector;