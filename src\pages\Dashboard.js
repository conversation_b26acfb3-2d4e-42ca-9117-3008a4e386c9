import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext.js';
import { useNavigate } from 'react-router-dom';
import { getFirestore, doc, getDoc, collection, query, where, getDocs, Timestamp, orderBy, onSnapshot, updateDoc } from 'firebase/firestore';

// ============================================================================
// SAFE LOCALSTORAGE FUNCTIONS - PREVENTS JSON PARSE ERRORS
// ============================================================================

// Safe localStorage functions to prevent JSON parse errors
const safeGetFromLocalStorage = (key, fallback = null) => {
  try {
    const value = localStorage.getItem(key);
    if (!value || value === 'undefined' || value === 'null' || value === '') {
      return fallback;
    }
    
    // For boolean strings, parse them correctly
    if (value === 'true') return true;
    if (value === 'false') return false;
    
    // For JSON objects, try to parse
    if (value.startsWith('{') || value.startsWith('[')) {
      try {
        return JSON.parse(value);
      } catch (e) {
        console.warn(`Failed to parse JSON for key ${key}:`, value);
        localStorage.removeItem(key);
        return fallback;
      }
    }
    
    return value;
  } catch (error) {
    console.error(`Error accessing localStorage key ${key}:`, error);
    return fallback;
  }
};

const safeSetToLocalStorage = (key, value) => {
  try {
    if (value === undefined || value === null) {
      localStorage.removeItem(key);
      return false;
    }
    
    // Store booleans as strings
    if (typeof value === 'boolean') {
      localStorage.setItem(key, value.toString());
      return true;
    }
    
    // Store objects as JSON
    if (typeof value === 'object') {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    }
    
    // Store everything else as string
    localStorage.setItem(key, value.toString());
    return true;
  } catch (error) {
    console.error(`Failed to store value in localStorage key ${key}:`, error);
    return false;
  }
};

// Emergency cleanup for this component
const cleanupDashboardStorage = () => {
  const keysToCheck = ['hasCompletedInspection'];
  
  keysToCheck.forEach(key => {
    const value = localStorage.getItem(key);
    if (value === 'undefined' || value === 'null' || value === '') {
      console.log(`🧹 Cleaning corrupted Dashboard key: ${key}`);
      localStorage.removeItem(key);
    }
  });
};

// ============================================================================
// DASHBOARD COMPONENT
// ============================================================================

function Dashboard() {
  const { currentUser, userRole, logout, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDailyPayEnabled, setIsDailyPayEnabled] = useState(true);
  const [hoverCard, setHoverCard] = useState(null);
  const [profileData, setProfileData] = useState({});
  const [profilePicture, setProfilePicture] = useState(null);
  const [unreadMessages, setUnreadMessages] = useState(0);
  
  // Clock in status
  const [isClockedIn, setIsClockedIn] = useState(false);
  
  // Inspection status
  const [hasCompletedInspection, setHasCompletedInspection] = useState(false);
  
  // Modal state
  const [showRequirementModal, setShowRequirementModal] = useState(false);
  const [requirementType, setRequirementType] = useState(''); // 'clock-in', 'inspection', etc.

  // News state
  const [newsArticles, setNewsArticles] = useState([]);
  const [currentArticleIndex, setCurrentArticleIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  // ============================================================================
  // CLEANUP LOCALSTORAGE ON MOUNT
  // ============================================================================
  useEffect(() => {
    cleanupDashboardStorage();
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Function to determine text color based on background color for tags
  const getTextColor = (hexColor) => {
    if (!hexColor || !hexColor.startsWith('#') || hexColor.length !== 7) return '#FFFFFF';
    
    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    
    // Calculate brightness (YIQ formula)
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    
    return yiq >= 150 ? '#000000' : '#FFFFFF';
  };

  // Function to get next article with priority for urgent news
  const getNextArticleIndex = useCallback(() => {
    if (newsArticles.length <= 1) return 0;
    
    // Prioritize urgent articles if they exist
    const urgentArticles = newsArticles.filter(article => article.isUrgent);
    const regularArticles = newsArticles.filter(article => !article.isUrgent);
    
    // If we're currently showing an urgent article, have a 75% chance to show another urgent one if available
    // If we're showing a regular article, have a 50% chance to switch to an urgent one if available
    const currentArticle = newsArticles[currentArticleIndex];
    
    if (urgentArticles.length > 0 && regularArticles.length > 0) {
      if (currentArticle.isUrgent) {
        // Currently showing urgent article
        if (Math.random() < 0.75 && urgentArticles.length > 1) {
          // Show another urgent article (if there's more than one)
          const urgentIndices = urgentArticles
            .map((article, idx) => newsArticles.indexOf(article))
            .filter(idx => idx !== currentArticleIndex);
          return urgentIndices[Math.floor(Math.random() * urgentIndices.length)];
        } else {
          // Switch to a regular article
          const regularIndices = regularArticles.map((article) => newsArticles.indexOf(article));
          return regularIndices[Math.floor(Math.random() * regularIndices.length)];
        }
      } else {
        // Currently showing regular article
        if (Math.random() < 0.5) {
          // Switch to an urgent article
          const urgentIndices = urgentArticles.map((article) => newsArticles.indexOf(article));
          return urgentIndices[Math.floor(Math.random() * urgentIndices.length)];
        } else {
          // Show another regular article
          const regularIndices = regularArticles
            .map((article, idx) => newsArticles.indexOf(article))
            .filter(idx => idx !== currentArticleIndex);
          if (regularIndices.length === 0) {
            // If there's only one regular article, switch to an urgent one
            const urgentIndices = urgentArticles.map((article) => newsArticles.indexOf(article));
            return urgentIndices[Math.floor(Math.random() * urgentIndices.length)];
          }
          return regularIndices[Math.floor(Math.random() * regularIndices.length)];
        }
      }
    } else {
      // Only one type of article available, pick randomly from what's available
      let nextIndex;
      do {
        nextIndex = Math.floor(Math.random() * newsArticles.length);
      } while (nextIndex === currentArticleIndex && newsArticles.length > 1);
      return nextIndex;
    }
  }, [newsArticles, currentArticleIndex]);

  // Auto-cycle through news articles
  useEffect(() => {
    if (newsArticles.length <= 1 || isPaused) return;
    
    const cycleInterval = setTimeout(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentArticleIndex(getNextArticleIndex());
        setIsTransitioning(false);
      }, 500); // Wait for fade-out transition
    }, 7000); // Change article every 7 seconds
    
    return () => clearTimeout(cycleInterval);
  }, [newsArticles, currentArticleIndex, isPaused, getNextArticleIndex]);

  // Manually change article
  const changeArticle = (direction) => {
    if (newsArticles.length <= 1) return;
    
    setIsPaused(true); // Pause auto-cycling when user manually changes
    setIsTransitioning(true);
    
    setTimeout(() => {
      if (direction === 'next') {
        setCurrentArticleIndex(prev => (prev + 1) % newsArticles.length);
      } else {
        setCurrentArticleIndex(prev => (prev - 1 + newsArticles.length) % newsArticles.length);
      }
      setIsTransitioning(false);
      
      // Resume auto-cycling after 15 seconds of inactivity
      const resumeTimeout = setTimeout(() => {
        setIsPaused(false);
      }, 15000);
      
      return () => clearTimeout(resumeTimeout);
    }, 500); // Wait for fade-out transition
  };

  // Check for unread messages
  useEffect(() => {
    if (!currentUser) return;
    
    const db = getFirestore();
    
    // Query conversations where current user is a participant
    const q = query(
      collection(db, "directMessages"),
      where("participants", "array-contains", currentUser.uid),
      orderBy("lastMessageTime", "desc")
    );
    
    const unsubscribe = onSnapshot(q, (snapshot) => {
      let unreadCount = 0;
      
      snapshot.docs.forEach(doc => {
        const conversationData = doc.data();
        
        // Check if the last message is unread
        if (
          conversationData.lastMessage && 
          conversationData.lastMessage.sender !== currentUser.uid &&
          (!conversationData.readBy || !conversationData.readBy[currentUser.uid])
        ) {
          unreadCount++;
        }
      });
      
      setUnreadMessages(unreadCount);
    });
    
    return () => unsubscribe();
  }, [currentUser]);

  // Fetch user profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!currentUser?.uid) return;
      
      try {
        const db = getFirestore();
        const profileRef = doc(db, "userProfiles", currentUser.uid);
        const profileDoc = await getDoc(profileRef);
        
        if (profileDoc.exists()) {
          const data = profileDoc.data();
          setProfileData(data);
          if (data.photoBase64) {
            setProfilePicture(data.photoBase64);
          }
        }
      } catch (error) {
        console.error("Error fetching profile:", error);
      }
    };

    fetchProfileData();
  }, [currentUser]);

  // Check if user is clocked in
  useEffect(() => {
    const checkClockStatus = async () => {
      if (!currentUser) return;
      
      try {
        const db = getFirestore();
        const timeCardRef = doc(db, 'timeCards', currentUser.uid);
        const timeCardDoc = await getDoc(timeCardRef);
        
        if (timeCardDoc.exists()) {
          const timeCardData = timeCardDoc.data();
          const today = new Date().toLocaleDateString();
          
          setIsClockedIn(
            timeCardData.currentDay === today && 
            timeCardData.clockedIn && 
            !timeCardData.clockedOut
          );
        } else {
          setIsClockedIn(false);
        }
      } catch (error) {
        console.error("Error checking clock status:", error);
        setIsClockedIn(false);
      }
    };
    
    checkClockStatus();
  }, [currentUser]);

  // FIXED: Check if user has completed an inspection today with safe localStorage
  useEffect(() => {
    const checkInspectionStatus = async () => {
      if (!currentUser) return;
      
      // First cleanup any corrupted storage
      cleanupDashboardStorage();
      
      try {
        const db = getFirestore();
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        const startTimestamp = Timestamp.fromDate(today);
        const endTimestamp = Timestamp.fromDate(tomorrow);
        
        const q = query(
          collection(db, 'inspections'),
          where('userId', '==', currentUser.uid),
          where('timestamp', '>=', startTimestamp),
          where('timestamp', '<', endTimestamp)
        );
        
        const querySnapshot = await getDocs(q);
        const hasCompleted = !querySnapshot.empty;
        
        setHasCompletedInspection(hasCompleted);
        // FIXED: Use safe localStorage
        safeSetToLocalStorage('hasCompletedInspection', hasCompleted);
        
      } catch (error) {
        console.error("Error checking inspection status:", error);
        // FIXED: Try to get from localStorage as fallback with safe parsing
        const cachedValue = safeGetFromLocalStorage('hasCompletedInspection');
        if (cachedValue !== null) {
          setHasCompletedInspection(cachedValue);
        }
      }
    };
    
    // FIXED: Check from localStorage first for better UX with safe parsing
    const cachedValue = safeGetFromLocalStorage('hasCompletedInspection');
    if (cachedValue !== null) {
      setHasCompletedInspection(cachedValue);
    }
    
    checkInspectionStatus();
  }, [currentUser]);

  // Fetch news articles
  useEffect(() => {
    const fetchNews = async () => {
      try {
        const db = getFirestore();
        const newsQuery = query(collection(db, 'news'), orderBy('timestamp', 'desc'));
        const snapshot = await getDocs(newsQuery);
        
        const articles = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate() || new Date()
        }));
        
        setNewsArticles(articles);
        
        // Set initial article index - prioritize urgent news if available
        if (articles.length > 0) {
          const urgentArticles = articles.filter(article => article.isUrgent);
          if (urgentArticles.length > 0) {
            setCurrentArticleIndex(articles.indexOf(urgentArticles[0]));
          } else {
            setCurrentArticleIndex(0);
          }
        }
      } catch (error) {
        console.error("Error fetching news:", error);
      }
    };
    
    fetchNews();
  }, []);

  // Fetch daily pay settings
  useEffect(() => {
    const fetchDailyPaySettings = async () => {
      try {
        const db = getFirestore();
        const settingsDoc = await getDoc(doc(db, 'settings', 'dailyPay'));
        
        if (settingsDoc.exists()) {
          // Get the enabled status from Firestore
          setIsDailyPayEnabled(settingsDoc.data().enabled);
        }
      } catch (error) {
        console.error("Error fetching daily pay settings:", error);
        // If there's an error, we'll keep the default value (true)
      }
    };

    fetchDailyPaySettings();
  }, []);

  const formatDate = (date) => {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  };

  const formatTime = (date) => {
    const options = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };
    return date.toLocaleTimeString('en-US', options);
  };

  // Format a short date
  const formatShortDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  async function handleLogout() {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error("Failed to log out", error);
    }
  }
  
  // Handle card click with validation and mark messages as read for directory
  const handleCardClick = (path) => {
    // Allow admin to bypass requirements
    if (isAdmin) {
      if (path === '/employee-directory') {
        // Mark messages as read when navigating to directory
        markMessagesAsRead();
      }
      navigate(path);
      return;
    }
    
    // Special handling for Map feature - requires clock-in and inspection
    if (path === '/map' || path === '/camera') {
      if (!isClockedIn) {
        setRequirementType('clock-in-for-map');
        setShowRequirementModal(true);
        return;
      }
      
      if (!hasCompletedInspection) {
        setRequirementType('inspection-for-map');
        setShowRequirementModal(true);
        return;
      }
    }
    
    // Check if user is clocked in (required for Inspection)
    if (path === '/inspection' && !isClockedIn) {
      setRequirementType('clock-in');
      setShowRequirementModal(true);
      return;
    }
    
    // Mark messages as read when navigating to directory
    if (path === '/employee-directory') {
      markMessagesAsRead();
    }
    
    // Otherwise proceed normally
    navigate(path);
  };
  
  // Function to mark all messages as read
  const markMessagesAsRead = async () => {
    if (!currentUser) return;
    
    try {
      const db = getFirestore();
      
      // Get all conversations where user is a participant
      const q = query(
        collection(db, "directMessages"),
        where("participants", "array-contains", currentUser.uid)
      );
      
      const querySnapshot = await getDocs(q);
      
      // Update each conversation with unread messages
      const promises = querySnapshot.docs.map(async (doc) => {
        const conversationData = doc.data();
        
        // Check if there are unread messages
        if (
          conversationData.lastMessage && 
          conversationData.lastMessage.sender !== currentUser.uid &&
          (!conversationData.readBy || !conversationData.readBy[currentUser.uid])
        ) {
          // Create or update readBy object
          const readBy = conversationData.readBy || {};
          readBy[currentUser.uid] = Timestamp.now();
          
          // Update conversation document
          return updateDoc(doc.ref, { readBy });
        }
        
        return Promise.resolve();
      });
      
      await Promise.all(promises);
      
    } catch (error) {
      console.error("Error marking messages as read:", error);
    }
  };

  // Function to determine if a card should be shown based on user tags
  const shouldShowCard = (card) => {
    // If no tag restriction, always show
    if (!card.showIfTag) return true;
    
    // If admin, always show
    if (isAdmin) return true;
    
    // Check if user has any of the required tags
    return profileData.tags && profileData.tags.some(userTag => 
      card.showIfTag.includes(userTag.name)
    );
  };

  // Define navigation cards
  const baseNavigationCards = [
    {
      title: "Team",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      path: '/team',
      color: 'from-blue-600 to-blue-700',
      hoverColor: 'from-blue-500 to-blue-600',
      adminOnly: true
    },
    // TEAM MEETING BUTTON - UPDATED TO LINK TO TEAM-MEETING ROUTE
    {
      title: "Team Meeting",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      ),
      path: '/team-meeting',
      color: 'from-purple-600 to-purple-700',
      hoverColor: 'from-purple-500 to-purple-600'
    },
    // EMPLOYEE DIRECTORY BUTTON - UPDATED WITH NOTIFICATION
    {
      title: "Employee Directory",
      icon: (
        <div className="relative">
          {unreadMessages > 0 && (
            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
              {unreadMessages}
            </div>
          )}
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 ${unreadMessages > 0 ? 'animate-pulse text-blue-300' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
      ),
      path: '/employee-directory',
      color: unreadMessages > 0 ? 'from-blue-500 to-blue-600' : 'from-teal-600 to-teal-700',
      hoverColor: unreadMessages > 0 ? 'from-blue-400 to-blue-500' : 'from-teal-500 to-teal-600',
      glowing: unreadMessages > 0
    },
    // NEW ORIENTATION BUTTON
    {
      title: "Orientation",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
      path: '#',
      color: 'from-amber-600 to-amber-700',
      hoverColor: 'from-amber-500 to-amber-600'
    },
    // NEW TRAINING/RESOURCES BUTTON
    {
      title: "Training/Resources",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      path: '#',
      color: 'from-rose-600 to-rose-700',
      hoverColor: 'from-rose-500 to-rose-600'
    },
    {
      title: "Inspection",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
        </svg>
      ),
      path: '/inspection',
      color: 'from-teal-600 to-teal-700',
      hoverColor: 'from-teal-500 to-teal-600',
      requiresClockIn: true
    },
    {
      title: "Map",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
        </svg>
      ),
      path: '/map',
      color: 'from-green-600 to-green-700',
      hoverColor: 'from-green-500 to-green-600',
      requiresClockIn: true,
      requiresInspection: true
    },
    // ADD NEW CAMERA SYSTEM CARD
    {
      title: "Cameras",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      ),
      path: '/camera',
      color: 'from-indigo-600 to-indigo-700',
      hoverColor: 'from-indigo-500 to-indigo-600',
      requiresClockIn: true,
      requiresInspection: true
    },
    // NEW RECOVERY BUTTON
    {
      title: "Recovery",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
        </svg>
      ),
      path: '/recovery',
      color: 'from-red-600 to-red-700',
      hoverColor: 'from-red-500 to-red-600',
      showIfTag: ['Tow Truck', 'Recovery', 'Repo', 'Admin']
    },
    // FLEET MANAGEMENT BUTTON (NEW)
    {
      title: "Fleet",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
        </svg>
      ),
      path: '/fleet',
      color: 'from-slate-600 to-slate-700',
      hoverColor: 'from-slate-500 to-slate-600',
      adminOnly: true
    },
    // NEW VEHICLE MAINTENANCE BUTTON
    {
      title: "Vehicle Maint",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      path: '#',
      color: 'from-cyan-600 to-cyan-700',
      hoverColor: 'from-cyan-500 to-cyan-600',
      adminOnly: true  // Added adminOnly property
    },
    // NEWS BUTTON
    {
      title: "News",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M8 7h6M8 11h6M8 15h6" />
        </svg>
      ),
      path: '/news',
      color: 'from-blue-600 to-blue-800',
      hoverColor: 'from-blue-500 to-blue-700',
      adminOnly: true
    },
    {
      title: "Orders",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      ),
      path: '/orders',
      color: 'from-orange-600 to-orange-700',
      hoverColor: 'from-orange-500 to-orange-600',
      adminOnly: true
    },
    {
      title: "Analytics",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      path: '/analytics',
      color: 'from-yellow-600 to-yellow-700',
      hoverColor: 'from-yellow-500 to-yellow-600',
      adminOnly: true
    },
    // NEW: Vehicle Tracker URL Generator
    {
      title: "Vehicle URLs",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
          <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
        </svg>
      ),
      path: '/admin/vehicle-urls',
      color: 'from-violet-600 to-violet-700',
      hoverColor: 'from-violet-500 to-violet-600',
      adminOnly: true
    },
    {
      title: "User Mngmnt",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        </svg>
      ),
      path: '/admin',
      color: 'from-blue-600 to-blue-700',
      hoverColor: 'from-blue-500 to-blue-600',
      adminOnly: true
    },
    {
      title: "Tags",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
        </svg>
      ),
      path: '/tags-management',
      color: 'from-red-600 to-red-700',
      hoverColor: 'from-red-500 to-red-600',
      adminOnly: true
    },
    // ARCHIVE BUTTON
    {
      title: "Archive",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
        </svg>
      ),
      path: '#',
      color: 'from-amber-600 to-amber-700',
      hoverColor: 'from-amber-500 to-amber-600',
      adminOnly: true  // Added adminOnly property
    },
    // DATABASE BUTTON
    {
      title: "Database",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
        </svg>
      ),
      path: '#',
      color: 'from-pink-600 to-pink-700',
      hoverColor: 'from-pink-500 to-pink-600',
      adminOnly: true  // Added adminOnly property
    },
    // FINANCIALS BUTTON
    {
      title: "Financials",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 8h6m-5 0a3 3 0 110 6H9l3 3m-3-6h6m6 1a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      path: '#',
      color: 'from-emerald-600 to-emerald-700',
      hoverColor: 'from-emerald-500 to-emerald-600',
      adminOnly: true  // Added adminOnly property
    },
    {
      title: "SkipTraceAI",
      icon: (
        <div className="relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg opacity-75 transition duration-500 animate-pulse"></div>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 relative" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" />
          </svg>
        </div>
      ),
      path: '#',
      color: 'from-purple-600 to-purple-700',
      hoverColor: 'from-purple-500 to-purple-600',
      label: "Coming Soon",
      disabled: true
    },
    {
      title: "QuickRouteAI",
      icon: (
        <div className="relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-lg opacity-75 transition duration-500 animate-pulse"></div>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 relative" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
      ),
      path: '#',
      color: 'from-cyan-600 to-blue-700',
      hoverColor: 'from-cyan-500 to-blue-600',
      label: "Coming Soon",
      disabled: true
    },
    {
      title: "Suggestions",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      ),
      path: '/suggestions-updates',
      color: 'from-blue-600 to-blue-700',
      hoverColor: 'from-blue-500 to-blue-600'
    },
    {
      title: "Awards",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      path: '/awards',
      color: 'from-green-600 to-green-700',
      hoverColor: 'from-green-500 to-green-600'
    },
    {
      title: "Settings",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        </svg>
      ),
      path: '/settings',
      color: 'from-orange-600 to-orange-700',
      hoverColor: 'from-orange-500 to-orange-600',
      adminOnly: true  // Added adminOnly property
    },
    {
      title: "Pay Info",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      path: '/pay-info',
      color: 'from-green-600 to-green-700',
      hoverColor: 'from-green-500 to-green-600'
    },
    {
      title: "Time Card",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      path: '/timecard',
      color: 'from-blue-600 to-blue-700',
      hoverColor: 'from-blue-500 to-blue-600'
    }
  ];

  // Filter navigation cards based on user role and tags
  const filteredCards = baseNavigationCards.filter(card => 
    (!card.adminOnly || isAdmin) && shouldShowCard(card)
  );

  return (
    <div className="min-h-screen bg-gray-900 text-white font-sans flex flex-col">
      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 z-50 bg-black bg-opacity-70 backdrop-blur-sm lg:hidden transition-all duration-300"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <div 
            className="fixed right-0 top-0 bottom-0 w-64 bg-gray-800 shadow-2xl border-l border-gray-700 transform transition-transform duration-300"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-900">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-white">Menu</h2>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="text-gray-400 hover:text-gray-200 transition-colors duration-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="p-4 flex flex-col space-y-4">
              <button
                onClick={() => {
                  navigate('/profile');
                  setIsMobileMenuOpen(false);
                }}
                className="flex items-center text-left px-3 py-2 rounded-md hover:bg-gray-700 text-gray-200 transition-colors duration-200 group"
              >
                {profilePicture ? (
                  <div className="h-8 w-8 rounded-full overflow-hidden mr-3 border border-gray-600">
                    <img src={profilePicture} alt="Profile" className="h-full w-full object-cover" />
                  </div>
                ) : (
                  <div className="bg-purple-800 p-2 rounded-full mr-3 group-hover:bg-purple-700 transition-colors duration-200">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                )}
                Profile
              </button>
              <button
                onClick={() => {
                  navigate('/timecard');
                  setIsMobileMenuOpen(false);
                }}
                className="flex items-center text-left px-3 py-2 rounded-md hover:bg-gray-700 text-gray-200 transition-colors duration-200 group"
              >
                <div className="bg-blue-800 p-2 rounded-full mr-3 group-hover:bg-blue-700 transition-colors duration-200">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                Time Card {isClockedIn && <span className="ml-2 bg-green-900 text-green-200 px-2 py-0.5 rounded-full text-xs">Active</span>}
              </button>
              <button
                onClick={() => {
                  const handleClick = async () => {
                    await markMessagesAsRead();
                    navigate('/employee-directory');
                    setIsMobileMenuOpen(false);
                  };
                  handleClick();
                }}
                className="flex items-center text-left px-3 py-2 rounded-md hover:bg-gray-700 text-gray-200 transition-colors duration-200 group"
              >
                <div className="bg-teal-800 p-2 rounded-full mr-3 group-hover:bg-teal-700 transition-colors duration-200 relative">
                  {unreadMessages > 0 && (
                    <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-4 w-4 flex items-center justify-center animate-pulse">
                      {unreadMessages}
                    </div>
                  )}
                  <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 text-teal-200 ${unreadMessages > 0 ? 'animate-pulse' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                Employee Directory
                {unreadMessages > 0 && (
                  <span className="ml-2 bg-red-900 text-red-200 px-2 py-0.5 rounded-full text-xs animate-pulse">
                    {unreadMessages} new
                  </span>
                )}
              </button>
              <button
                onClick={() => {
                  handleLogout();
                  setIsMobileMenuOpen(false);
                }}
                className="flex items-center text-left px-3 py-2 rounded-md hover:bg-gray-700 text-gray-200 transition-colors duration-200 group"
              >
                <div className="bg-gray-800 p-2 rounded-full mr-3 group-hover:bg-gray-700 transition-colors duration-200">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                </div>
                Sign Out
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Top Navigation Bar */}
      <nav className="bg-gray-900 shadow-lg border-b border-gray-800 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold" style={{
                  backgroundImage: 'linear-gradient(to right, #4f46e5, #a855f7, #4f46e5)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundSize: '200% auto',
                  animation: 'textShine 5s ease infinite'
                }}>NWRepo</h1>
              </div>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-4">
              {isClockedIn ? (
                <div className="px-3 py-1 bg-green-900 bg-opacity-40 rounded-full text-green-300 flex items-center text-sm">
                  <span className="h-2 w-2 bg-green-400 rounded-full mr-2 relative flex">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                  </span>
                  Clocked In
                </div>
              ) : (
                <button
                  onClick={() => navigate('/timecard')}
                  className="bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 rounded-md shadow-md hover:shadow-lg transition duration-300 ease-in-out transform hover:scale-105 flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Clock In
                </button>
              )}
              
              {/* Messages Button */}
              <button
                onClick={() => {
                  markMessagesAsRead();
                  navigate('/employee-directory');
                }}
                className={`relative flex items-center ${
                  unreadMessages > 0 
                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 animate-pulse' 
                    : 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700'
                } text-white px-4 py-2 rounded-md shadow-md hover:shadow-lg transition duration-300 ease-in-out transform hover:scale-105`}
              >
                {unreadMessages > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                    {unreadMessages}
                  </span>
                )}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                Messages
              </button>
              
              <button
                onClick={() => navigate('/profile')}
                className="bg-gradient-to-r from-purple-700 to-purple-800 hover:from-purple-600 hover:to-purple-700 text-white px-4 py-2 rounded-md shadow-md hover:shadow-lg transition duration-300 ease-in-out transform hover:scale-105 flex items-center"
              >
                {profilePicture ? (
                  <div className="h-6 w-6 rounded-full overflow-hidden mr-2 border border-purple-300">
                    <img src={profilePicture} alt="Profile" className="h-full w-full object-cover" />
                  </div>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                )}
                Profile
              </button>
              <button
                onClick={handleLogout}
                className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-md shadow-md hover:shadow-lg transition duration-300 ease-in-out transform hover:scale-105"
              >
                <span className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3 3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Sign Out
                </span>
              </button>
            </div>
            
            {/* Mobile Menu Button */}
            <div className="flex md:hidden items-center">
              {unreadMessages > 0 && (
                <div className="relative mr-4">
                  <button
                    onClick={() => {
                      markMessagesAsRead();
                      navigate('/employee-directory');
                    }}
                    className="relative p-1 rounded-full bg-blue-600 text-white"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                      {unreadMessages}
                    </span>
                  </button>
                </div>
              )}
              <button
                onClick={() => setIsMobileMenuOpen(true)}
                className="text-gray-300 hover:text-white focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Status Banner */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-700 shadow-inner border-t border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="flex flex-wrap items-center justify-between gap-2">
            {/* Clock Status */}
            <div className={`flex items-center px-3 py-1 rounded-full ${
              isClockedIn ? 'bg-green-900 bg-opacity-30 text-green-300 border border-green-800' :
              'bg-red-900 bg-opacity-30 text-red-300 border border-red-800'
            }`}>
              <div className="relative mr-2">
                <span className={`inline-flex h-2 w-2 rounded-full ${
                  isClockedIn ? 'bg-green-400' : 'bg-red-400'
                }`}></span>
                {isClockedIn && (
                  <span className="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-green-400 opacity-75"></span>
                )}
              </div>
              <span className="text-sm">{isClockedIn ? 'Clocked In' : 'Not Clocked In'}</span>
              <button 
                onClick={() => navigate('/timecard')}
                className="ml-2 text-xs underline hover:no-underline"
              >
                {isClockedIn ? 'View' : 'Clock In'}
              </button>
            </div>
            
            {/* Inspection Status */}
            <div className={`flex items-center px-3 py-1 rounded-full ${
              hasCompletedInspection ? 'bg-green-900 bg-opacity-30 text-green-300 border border-green-800' :
              'bg-yellow-900 bg-opacity-30 text-yellow-300 border border-yellow-800'
            }`}>
              <div className="mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
              </div>
              <span className="text-sm">
                {hasCompletedInspection ? 'Inspection Complete' : 'Inspection Required'}
              </span>
              {!hasCompletedInspection && (
                <button 
                  onClick={() => navigate('/inspection')}
                  className="ml-2 text-xs underline hover:no-underline"
                >
                  Inspect Now
                </button>
              )}
            </div>
            
            {/* Current Date/Time */}
            <div className="flex items-center px-3 py-1 rounded-full bg-gray-800 bg-opacity-50 text-gray-300 border border-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-mono">{formatTime(currentTime)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* News Carousel Banner */}
      {newsArticles.length > 0 && (
        <div className="bg-gray-800 border-b border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-white flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M8 7h6M8 11h6M8 15h6" />
                </svg>
                Latest News
                <span className="ml-2 text-sm text-gray-400">
                  ({currentArticleIndex + 1}/{newsArticles.length})
                </span>
              </h3>
              <div className="flex items-center space-x-2">
                {/* Navigation buttons */}
                <button
                  onClick={() => changeArticle('prev')}
                  className="p-1 rounded-full hover:bg-gray-700 transition-colors"
                  aria-label="Previous news"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={() => changeArticle('next')}
                  className="p-1 rounded-full hover:bg-gray-700 transition-colors"
                  aria-label="Next news"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
                <button
                  onClick={() => navigate('/news')}
                  className="text-sm text-blue-400 hover:text-blue-300 transition-colors flex items-center"
                >
                  View All
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
            
            {/* Current Article Display */}
            {newsArticles.length > 0 && (
              <div 
                className={`transition-opacity duration-500 ease-in-out ${isTransitioning ? 'opacity-0' : 'opacity-100'}`}
                style={{ minHeight: '80px' }}
              >
                {newsArticles[currentArticleIndex] && (
                  <div 
                    className={`rounded-md p-3 flex items-start cursor-pointer ${
                      newsArticles[currentArticleIndex].isUrgent
                        ? 'bg-red-900 bg-opacity-20 border border-red-700'
                        : 'bg-gray-700 bg-opacity-40 border border-gray-600'
                    }`}
                    onClick={() => navigate('/news')}
                  >
                    {/* Thumbnail */}
                    {newsArticles[currentArticleIndex].imageUrl ? (
                      <div className="h-16 w-16 rounded-md overflow-hidden mr-3 flex-shrink-0 border border-gray-600">
                        <img 
                          src={newsArticles[currentArticleIndex].imageUrl} 
                          alt=""
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className={`rounded-full p-2 mr-3 flex-shrink-0 ${
                        newsArticles[currentArticleIndex].isUrgent ? 'bg-red-700' : 'bg-blue-700'
                      }`}>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          {newsArticles[currentArticleIndex].isUrgent ? (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          ) : (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M8 7h6M8 11h6M8 15h6" />
                          )}
                        </svg>
                      </div>
                    )}
                    
                    <div className="flex-1">
                      <div className="flex justify-between items-center">
                        <h4 className={`font-medium flex items-center ${
                          newsArticles[currentArticleIndex].isUrgent ? 'text-red-300' : 'text-gray-200'
                        }`}>
                          {newsArticles[currentArticleIndex].title}
                          {newsArticles[currentArticleIndex].isUrgent && (
                            <span className="ml-2 bg-red-800 text-red-200 text-xs px-2 py-0.5 rounded-full">
                              Urgent
                            </span>
                          )}
                        </h4>
                        <span className={`text-xs whitespace-nowrap ml-2 ${
                          newsArticles[currentArticleIndex].isUrgent ? 'text-red-300' : 'text-gray-400'
                        }`}>
                          {formatShortDate(newsArticles[currentArticleIndex].timestamp)}
                        </span>
                      </div>
                      <p className={`mt-1 line-clamp-2 text-sm ${
                        newsArticles[currentArticleIndex].isUrgent ? 'text-red-200' : 'text-gray-300'
                      }`}>
                        {newsArticles[currentArticleIndex].content}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {/* Admin controls */}
            {isAdmin && (
              <div className="mt-2 flex justify-end">
                <button
                  onClick={() => navigate('/news')}
                  className="text-xs text-blue-400 hover:text-blue-300 transition-colors flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Manage News
                </button>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Display requirements widget */}
      {(!isClockedIn || !hasCompletedInspection) && (
        <div className="bg-gray-800 border-b border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span className="text-yellow-300 font-medium">Daily requirements:</span>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {!isClockedIn && (
                  <button
                    onClick={() => navigate('/timecard')}
                    className="flex items-center bg-yellow-900 bg-opacity-30 border border-yellow-800 text-yellow-300 px-3 py-1 rounded-md hover:bg-opacity-50 transition-colors">
                    <span className="mr-1">1.</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Clock In
                  </button>
                )}
                
                {!hasCompletedInspection && (
                  <button
                    onClick={() => navigate('/inspection')}
                    className={`flex items-center ${!isClockedIn ? 'bg-gray-700 text-gray-400 cursor-not-allowed' : 'bg-yellow-900 bg-opacity-30 border border-yellow-800 text-yellow-300 hover:bg-opacity-50'} px-3 py-1 rounded-md transition-colors`}
                    disabled={!isClockedIn}
                  >
                    <span className="mr-1">2.</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                    Complete Inspection
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content - Adapts to screen size */}
      <div className="flex-grow flex flex-col lg:flex-row lg:overflow-hidden">
        {/* Left Column - Welcome/User Info */}
        <div className="lg:w-1/4 p-4 lg:border-r lg:border-gray-700">
          <div className="bg-gray-800 bg-opacity-50 rounded-xl shadow-lg border border-gray-700" style={{backdropFilter: 'blur(12px)'}}>
            <div className="p-4">
              <div className="flex items-center mb-4">
                {/* Profile Picture */}
                {profilePicture ? (
                  <div className="w-14 h-14 rounded-full overflow-hidden border-2 border-purple-500 shadow-lg hover:border-blue-400 transition-all duration-300 cursor-pointer" onClick={() => navigate('/profile')}>
                    <img src={profilePicture} alt="Profile" className="w-full h-full object-cover" />
                  </div>
// Continuation from where the previous artifact was cut off...

) : (
  <div className="w-14 h-14 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-md cursor-pointer" onClick={() => navigate('/profile')}>
    <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
    </svg>
  </div>
)}
<div className="ml-4">
  <h2 className="text-xl font-semibold text-white">
    Welcome, <span style={{
      backgroundImage: 'linear-gradient(to right, #60a5fa, #c084fc)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent'
    }}>{profileData.displayName || currentUser?.email?.split('@')[0] || currentUser?.email || 'User'}</span>
  </h2>
  {profileData.jobTitle && (
    <p className="text-gray-300 text-sm">{profileData.jobTitle}</p>
  )}
  
  {/* Display user tags */}
  {profileData.tags && profileData.tags.length > 0 && (
    <div className="flex flex-wrap gap-1 mt-1">
      {profileData.tags.map((tag, index) => (
        <span
          key={index}
          className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium shadow-sm"
          style={{
            backgroundColor: tag.color,
            color: getTextColor(tag.color)
          }}
        >
          {tag.name}
        </span>
      ))}
    </div>
  )}
</div>
</div>

<div className="space-y-3">
<div className="flex items-center bg-gray-900 bg-opacity-40 px-4 py-2 rounded-lg shadow-inner">
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
  <span className="text-gray-100 truncate">{formatDate(currentTime)}</span>
</div>
<div className="flex items-center bg-gray-900 bg-opacity-40 px-4 py-2 rounded-lg shadow-inner">
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
  <span className="text-gray-100 font-mono">{formatTime(currentTime)}</span>
</div>

{/* Status Indicators */}
<div className="flex flex-col space-y-2">
  <div className={`flex items-center px-4 py-2 rounded-lg ${isClockedIn ? 'bg-green-900 bg-opacity-20' : 'bg-red-900 bg-opacity-20'}`}>
    <div className="relative flex-shrink-0 mr-2">
      <span className={`absolute inset-0 rounded-full ${isClockedIn ? 'animate-ping bg-green-500' : ''} opacity-30`}></span>
      <span className={`relative inline-flex rounded-full h-3 w-3 ${isClockedIn ? 'bg-green-500' : 'bg-red-500'}`}></span>
    </div>
    <span className={`text-sm ${isClockedIn ? 'text-green-300' : 'text-red-300'}`}>
      {isClockedIn ? 'Clocked In' : 'Not Clocked In'}
    </span>
    <button 
      onClick={() => navigate('/timecard')}
      className="ml-auto text-xs text-blue-400 hover:text-blue-300 transition-colors"
    >
      {isClockedIn ? 'View' : 'Clock In'}
    </button>
  </div>
  
  <div className={`flex items-center px-4 py-2 rounded-lg ${hasCompletedInspection ? 'bg-green-900 bg-opacity-20' : 'bg-red-900 bg-opacity-20'}`}>
    <div className="relative flex-shrink-0 mr-2">
      <span className={`relative inline-flex rounded-full h-3 w-3 ${hasCompletedInspection ? 'bg-green-500' : 'bg-red-500'}`}></span>
    </div>
    <span className={`text-sm ${hasCompletedInspection ? 'text-green-300' : 'text-red-300'}`}>
      {hasCompletedInspection ? 'Inspection Complete' : 'Inspection Required'}
    </span>
    {!hasCompletedInspection && (
      <button 
        onClick={() => navigate('/inspection')}
        className="ml-auto text-xs text-blue-400 hover:text-blue-300 transition-colors"
      >
        Inspect
      </button>
    )}
  </div>
  
  {/* Messages Indicator */}
  {unreadMessages > 0 && (
    <div className="flex items-center px-4 py-2 rounded-lg bg-blue-900 bg-opacity-20 animate-pulse">
      <div className="relative flex-shrink-0 mr-2">
        <span className="animate-ping absolute inline-flex h-3 w-3 rounded-full bg-blue-500 opacity-30"></span>
        <span className="relative inline-flex rounded-full h-3 w-3 bg-blue-500"></span>
      </div>
      <span className="text-sm text-blue-300">
        {unreadMessages} New Message{unreadMessages !== 1 ? 's' : ''}
      </span>
      <button 
        onClick={() => {
          markMessagesAsRead();
          navigate('/employee-directory');
        }}
        className="ml-auto text-xs text-blue-400 hover:text-blue-300 transition-colors"
      >
        View
      </button>
    </div>
  )}
</div>
</div>

{/* Quick Profile Overview - Expanded for larger screens */}
<div className="mt-4 hidden lg:block">
<div 
  className="bg-gradient-to-br from-purple-800 to-indigo-900 rounded-xl p-4 shadow-lg cursor-pointer transition-all duration-300 hover:shadow-xl hover:from-purple-700 hover:to-indigo-800"
  onClick={() => navigate('/profile')}
>
  <h3 className="font-medium text-white mb-2 flex items-center">
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
    </svg>
    My Profile
  </h3>
  <div className="text-purple-200 text-sm space-y-1">
    <p>{profileData.location && <span>Location: {profileData.location}</span>}</p>
    <p>{profileData.vehicle && <span>Vehicle #: {profileData.vehicle}</span>}</p>
    <p>{profileData.phoneNumber && <span>Phone: {profileData.phoneNumber}</span>}</p>
    
    {/* Display user tags in profile section */}
    {profileData.tags && profileData.tags.length > 0 && (
      <div className="mt-2">
        <p className="text-purple-300 font-medium">Tags:</p>
        <div className="flex flex-wrap gap-1 mt-1">
          {profileData.tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium shadow-sm"
              style={{
                backgroundColor: tag.color,
                color: getTextColor(tag.color)
              }}
            >
              {tag.name}
            </span>
          ))}
        </div>
      </div>
    )}
  </div>
  <div className="flex justify-end mt-2">
    <span className="text-purple-200 text-xs flex items-center">
      View Full Profile
      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
      </svg>
    </span>
  </div>
</div>
</div>
</div>
</div>

{/* Todo Preview - Visible on larger screens only */}
{profileData.todo && profileData.todo.length > 0 && (
<div className="mt-4 hidden lg:block">
<div className="bg-gray-800 rounded-xl p-4 shadow-lg border border-gray-700">
<h3 className="font-medium text-white mb-2 flex items-center">
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
  </svg>
  Todo List
</h3>
<div className="space-y-2 max-h-40 overflow-y-auto pr-2">
  {profileData.todo.slice(0, 3).map((task, index) => (
    <div key={index} className="bg-gray-750 p-2 rounded border border-gray-700 text-sm text-red-400">
      {task}
    </div>
  ))}
  {profileData.todo.length > 3 && (
    <div className="text-right text-xs text-gray-400">
      +{profileData.todo.length - 3} more tasks
    </div>
  )}
</div>
</div>
</div>
)}
</div>

{/* Right Column - App Grid */}
<div className="lg:w-3/4 p-4">
<div className="mb-4">
<h1 className="text-2xl font-bold" style={{
backgroundImage: 'linear-gradient(to right, #60a5fa, #a78bfa, #60a5fa)',
WebkitBackgroundClip: 'text',
WebkitTextFillColor: 'transparent',
backgroundSize: '200% auto',
animation: 'textShine 5s ease infinite'
}}>Dashboard</h1>
<div className="h-1 w-16 mt-2" style={{
background: 'linear-gradient(to right, #60a5fa, #a78bfa)',
animation: 'pulse 2s infinite'
}}></div>
</div>

{/* Responsive Grid - Adapts to different screen sizes */}
<div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 md:gap-4">
{filteredCards.map((card, index) => (
<div 
key={index}
onClick={() => !card.disabled && handleCardClick(card.path)}
onMouseEnter={() => setHoverCard(index)}
onMouseLeave={() => setHoverCard(null)}
className={`bg-gradient-to-br ${hoverCard === index ? card.hoverColor : card.color} rounded-xl shadow-lg 
  ${card.disabled ? 'opacity-80' : 'cursor-pointer transition-all duration-300 hover:-translate-y-1 hover:shadow-xl'} 
  relative overflow-hidden group ${card.glowing ? 'shadow-lg shadow-blue-500/50' : ''}`}
style={{
  boxShadow: hoverCard === index ? 
    `0 10px 15px -3px rgba(0, 0, 0, 0.3), inset 0 1px 2px 0 rgba(255, 255, 255, 0.2)` 
    : card.glowing ? '0 0 15px rgba(59, 130, 246, 0.5)' : `inset 0 1px 2px 0 rgba(255, 255, 255, 0.1)`,
  aspectRatio: '1/1'
}}
>
{/* Glow effect on hover */}
<div className="absolute inset-0 bg-gradient-to-br from-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>

{/* Coming Soon Label */}
{card.label && (
  <div className="absolute top-1 right-1 bg-yellow-500 text-black text-xs font-bold px-1 py-0.5 rounded-full text-center" style={{
    fontSize: '0.65rem',
    animation: 'pulse 2s infinite'
  }}>
    {card.label}
  </div>
)}

{/* Unread message counter */}
{card.title === "Employee Directory" && unreadMessages > 0 && (
  <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
    {unreadMessages}
  </div>
)}

{/* Locked feature overlay for Map/Camera - UPDATED to check both clock in and inspection */}
{(card.title === "Map" || card.title === "Cameras") && (!isClockedIn || !hasCompletedInspection) && !isAdmin && (
  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center rounded-xl">
    <div className="text-center p-2">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto text-red-400 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      </svg>
      <span className="text-xs text-white">
        {!isClockedIn ? 'Clock in first' : 'Complete inspection first'}
      </span>
    </div>
  </div>
)}

{/* Locked feature overlay for Inspection */}
{card.title === "Inspection" && !isClockedIn && !isAdmin && (
  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center rounded-xl">
    <div className="text-center p-2">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto text-yellow-400 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      </svg>
      <span className="text-xs text-white">Clock in first</span>
    </div>
  </div>
)}

<div className="flex flex-col items-center justify-center h-full p-2">
  <div className="mb-2 transform transition duration-300 group-hover:scale-110">
    {card.icon}
  </div>
  <p className="text-xs sm:text-sm text-center text-white font-medium">
    {card.title}
  </p>
</div>
</div>
))}
</div>
</div>
</div>

{/* Clock In Shortcut */}
{!isClockedIn && (
<div className="fixed bottom-4 right-4 z-40">
<button
onClick={() => navigate('/timecard')}
className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
>
<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
</svg>
<span className="sr-only">Clock In</span>
</button>
</div>
)}

{/* Message Shortcut */}
{unreadMessages > 0 && (
<div className="fixed bottom-4 left-4 z-40">
<button
onClick={() => {
markMessagesAsRead();
navigate('/employee-directory');
}}
className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 animate-pulse relative"
>
<span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
{unreadMessages}
</span>
<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
</svg>
<span className="sr-only">New Messages</span>
</button>
</div>
)}

{/* Requirement Modal */}
{showRequirementModal && (
<div className="fixed inset-0 overflow-y-auto z-50 flex items-center justify-center px-4">
<div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" onClick={() => setShowRequirementModal(false)}></div>

<div className="relative bg-gray-900 rounded-lg max-w-md w-full overflow-hidden shadow-xl border border-gray-700 transform transition-all">
<div className="bg-gradient-to-r from-red-900 to-gray-800 p-4 border-b border-gray-700 flex justify-between items-center">
<h3 className="text-lg font-medium text-white">Action Required</h3>
<button
onClick={() => setShowRequirementModal(false)}
className="text-gray-300 hover:text-white transition-colors"
>
<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
</svg>
</button>
</div>

<div className="p-6">
{/* Clock in requirement for Inspection */}
{requirementType === 'clock-in' && (
<div className="flex flex-col items-center">
  <div className="bg-yellow-900 bg-opacity-20 p-3 rounded-full mb-4 border border-yellow-700">
    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  </div>
  <h4 className="text-xl font-semibold text-white mb-2">Clock In Required</h4>
  <p className="text-gray-300 text-center mb-6">
    You need to clock in before you can perform an inspection. This helps track work hours and ensures proper documentation.
  </p>
  <button
    onClick={() => {
      navigate('/timecard');
      setShowRequirementModal(false);
    }}
    className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white px-6 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 w-full"
  >
    Go to Time Card
  </button>
</div>
)}

{/* Clock in requirement for Map */}
{requirementType === 'clock-in-for-map' && (
<div className="flex flex-col items-center">
  <div className="bg-yellow-900 bg-opacity-20 p-3 rounded-full mb-4 border border-yellow-700">
    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  </div>
  <h4 className="text-xl font-semibold text-white mb-2">Clock In Required</h4>
  <p className="text-gray-300 text-center mb-6">
    You need to clock in before you can access this feature. This ensures your work time is properly tracked for the day.
  </p>
  <button
    onClick={() => {
      navigate('/timecard');
      setShowRequirementModal(false);
    }}
    className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white px-6 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 w-full"
  >
    Go to Time Card
  </button>
</div>
)}

{/* Inspection requirement for Map after clocked in */}
{requirementType === 'inspection-for-map' && (
<div className="flex flex-col items-center">
  <div className="bg-red-900 bg-opacity-20 p-3 rounded-full mb-4 border border-red-700">
    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
    </svg>
  </div>
  <h4 className="text-xl font-semibold text-white mb-2">Vehicle Inspection Required</h4>
  <p className="text-gray-300 text-center mb-6">
    You must complete a daily vehicle inspection before accessing this feature. This ensures your vehicle is safe and ready for the road.
  </p>
  <button
    onClick={() => {
      navigate('/inspection');
      setShowRequirementModal(false);
    }}
    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-6 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 w-full flex justify-center items-center"
  >
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
    </svg>
    Go to Inspection
  </button>
</div>
)}

{/* Original inspection requirement for Map */}
{requirementType === 'inspection' && (
<div className="flex flex-col items-center">
  <div className="bg-red-900 bg-opacity-20 p-3 rounded-full mb-4 border border-red-700">
    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
    </svg>
  </div>
  <h4 className="text-xl font-semibold text-white mb-2">Vehicle Inspection Required</h4>
  <p className="text-gray-300 text-center mb-6">
    You must complete a daily vehicle inspection before accessing the map. This ensures your vehicle is safe and ready for the road.
  </p>
  <div className="w-full space-y-2">
    <button
      onClick={() => {
        navigate('/inspection');
        setShowRequirementModal(false);
      }}
      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-6 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 w-full flex justify-center items-center"
    >
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
      </svg>
      Go to Inspection
    </button>
    {!isClockedIn && (
      <div className="bg-yellow-900 bg-opacity-20 p-3 rounded-md border border-yellow-700 text-yellow-300 text-sm text-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        Remember: You need to clock in first before performing an inspection.
      </div>
    )}
  </div>
</div>
)}
</div>
</div>
</div>
)}

{/* Add animations via style tag */}
<style>
{`
@keyframes textShine {
0%, 100% {
background-position: 0% center;
}
50% {
background-position: 100% center;
}
}

@keyframes pulse {
0%, 100% {
opacity: 1;}
50% {
opacity: 0.6;
}
}

@keyframes bounce {
0%, 100% {
transform: translateY(0);
}
50% {
transform: translateY(-5px);
}
}
`}
</style>
</div>
);
}

export default Dashboard;