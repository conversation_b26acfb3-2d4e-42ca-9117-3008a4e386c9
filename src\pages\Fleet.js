import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext.js';
import { 
  getFirestore, 
  collection, 
  doc, 
  addDoc, 
  setDoc, 
  getDoc, 
  getDocs, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp
} from 'firebase/firestore';
import { 
  getStorage, 
  ref, 
  uploadBytes, 
  getDownloadURL,
  uploadBytesResumable,
  deleteObject
} from 'firebase/storage';

const Fleet = () => {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [vehicles, setVehicles] = useState([]);
  const [users, setUsers] = useState([]);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [showVehicleModal, setShowVehicleModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [vehicleToDelete, setVehicleToDelete] = useState(null);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [filterStatus, setFilterStatus] = useState('all'); // all, available, assigned, maintenance
  const [sortBy, setSortBy] = useState('vehicleNumber'); // vehicleNumber, make, model, year, status
  const [isAssignmentMode, setIsAssignmentMode] = useState(false);
  const [userToAssign, setUserToAssign] = useState(null);
  const [showAssignmentHistory, setShowAssignmentHistory] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [vehicleHistory, setVehicleHistory] = useState([]); // For vehicle history
  const [inspections, setInspections] = useState([]); // For vehicle inspections
  const [maintenanceRecords, setMaintenanceRecords] = useState([]); // For maintenance records
  const [uploadProgress, setUploadProgress] = useState(0);
  const imageInputRef = useRef(null);

  // New vehicle form state
  const [newVehicle, setNewVehicle] = useState({
    vehicleNumber: '',
    make: '',
    model: '',
    year: new Date().getFullYear(),
    licensePlate: '',
    vin: '',
    color: '',
    type: 'Sedan',
    status: 'available',
    fuelType: 'Gasoline',
    currentMileage: '',
    lastServiceDate: '',
    nextServiceDue: '',
    notes: '',
    assignedTo: null,
    assignedToName: '',
    tires: {
      brand: '',
      size: '',
      purchaseDate: '',
      lastRotation: ''
    },
    insurance: {
      provider: '',
      policyNumber: '',
      expirationDate: ''
    },
    registration: {
      expirationDate: '',
      state: ''
    }
  });

  // Tab state for vehicle details
  const [activeTab, setActiveTab] = useState('details'); // details, inspections, maintenance, history

  // Maintenance form state
  const [newMaintenance, setNewMaintenance] = useState({
    type: 'Oil Change',
    date: new Date().toISOString().split('T')[0],
    mileage: '',
    description: '',
    cost: '',
    performedBy: '',
    notes: ''
  });

  // Additional filter states
  const [vehicleTypesFilter, setVehicleTypesFilter] = useState([]);
  const [yearRangeFilter, setYearRangeFilter] = useState({ min: 2000, max: new Date().getFullYear() });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Redirect if not admin
  useEffect(() => {
    if (isAdmin === false) {
      navigate('/dashboard');
    }
  }, [isAdmin, navigate]);

  // Fetch vehicles, users, and available types on load
  useEffect(() => {
    if (isAdmin) {
      fetchVehicles();
      fetchUsers();
      fetchVehicleTypes();
    }
  }, [isAdmin, filterStatus, sortBy]);

  // Fetch vehicle details when selected
  useEffect(() => {
    if (selectedVehicle && selectedVehicle.id) {
      fetchVehicleDetails(selectedVehicle.id);
    }
  }, [selectedVehicle, activeTab]);

  // Fetch all vehicles from Firestore
  const fetchVehicles = async () => {
    try {
      setLoading(true);
      const db = getFirestore();
      let vehiclesQuery = collection(db, 'vehicles');
      
      if (filterStatus !== 'all') {
        vehiclesQuery = query(vehiclesQuery, where('status', '==', filterStatus));
      }

      // Add sorting
      vehiclesQuery = query(vehiclesQuery, orderBy(sortBy));
      
      const querySnapshot = await getDocs(vehiclesQuery);
      
      let vehiclesList = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        lastServiceDate: doc.data().lastServiceDate ? new Date(doc.data().lastServiceDate) : null,
        nextServiceDue: doc.data().nextServiceDue ? new Date(doc.data().nextServiceDue) : null
      }));

      // Apply search filtering on the client side
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        vehiclesList = vehiclesList.filter(vehicle => 
          vehicle.vehicleNumber?.toLowerCase().includes(searchLower) ||
          vehicle.make?.toLowerCase().includes(searchLower) ||
          vehicle.model?.toLowerCase().includes(searchLower) ||
          vehicle.licensePlate?.toLowerCase().includes(searchLower) ||
          vehicle.assignedToName?.toLowerCase().includes(searchLower)
        );
      }

      // Apply additional filters
      if (vehicleTypesFilter.length > 0) {
        vehiclesList = vehiclesList.filter(vehicle => 
          vehicleTypesFilter.includes(vehicle.type)
        );
      }

      // Apply year range filter
      vehiclesList = vehiclesList.filter(vehicle => 
        vehicle.year >= yearRangeFilter.min && vehicle.year <= yearRangeFilter.max
      );
      
      setVehicles(vehiclesList);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching vehicles:", error);
      setError("Failed to load vehicles.");
      setLoading(false);
    }
  };

  // Fetch all unique vehicle types for filtering
  const fetchVehicleTypes = async () => {
    try {
      const db = getFirestore();
      const querySnapshot = await getDocs(collection(db, 'vehicles'));
      
      const types = new Set();
      querySnapshot.docs.forEach(doc => {
        if (doc.data().type) {
          types.add(doc.data().type);
        }
      });
      
      setVehicleTypesFilter(Array.from(types));
    } catch (error) {
      console.error("Error fetching vehicle types:", error);
    }
  };

  // Fetch users from Firestore
  const fetchUsers = async () => {
    try {
      const db = getFirestore();
      const usersQuery = query(collection(db, 'users'));
      const querySnapshot = await getDocs(usersQuery);
      
      const usersList = [];
      
      // Fetch each user's profile to get additional details
      for (const userDoc of querySnapshot.docs) {
        const userData = userDoc.data();
        const profileDoc = await getDoc(doc(db, 'userProfiles', userDoc.id));
        
        let profileData = {};
        if (profileDoc.exists()) {
          profileData = profileDoc.data();
        }
        
        usersList.push({
          id: userDoc.id,
          email: userData.email,
          role: userData.role,
          displayName: profileData.displayName || userData.email,
          jobTitle: profileData.jobTitle || '',
          phoneNumber: profileData.phoneNumber || '',
          photoBase64: profileData.photoBase64 || null
        });
      }
      
      setUsers(usersList);
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  // Fetch detailed info for a specific vehicle
  const fetchVehicleDetails = async (vehicleId) => {
    try {
      setLoading(true);
      const db = getFirestore();
      
      // Fetch vehicle assignment history
      if (activeTab === 'history') {
        const historyQuery = query(
          collection(db, 'vehicleAssignmentHistory'),
          where('vehicleId', '==', vehicleId),
          orderBy('assignmentDate', 'desc')
        );
        
        const historySnapshot = await getDocs(historyQuery);
        const historyData = historySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          assignmentDate: doc.data().assignmentDate?.toDate(),
          returnDate: doc.data().returnDate?.toDate()
        }));
        
        setVehicleHistory(historyData);
      }
      
      // Fetch vehicle inspections
      if (activeTab === 'inspections') {
        const currentYear = new Date().getFullYear();
        const inspectionQuery = query(
          collection(db, 'inspections'),
          where('carNumber', '==', selectedVehicle.vehicleNumber),
          orderBy('timestamp', 'desc'),
          limit(20) // Limit to most recent
        );
        
        const inspectionSnapshot = await getDocs(inspectionQuery);
        const inspectionData = inspectionSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate()
        }));
        
        setInspections(inspectionData);
      }
      
      // Fetch maintenance records
      if (activeTab === 'maintenance') {
        const maintenanceQuery = query(
          collection(db, 'maintenance'),
          where('vehicleId', '==', vehicleId),
          orderBy('date', 'desc')
        );
        
        const maintenanceSnapshot = await getDocs(maintenanceQuery);
        const maintenanceData = maintenanceSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          date: doc.data().date ? new Date(doc.data().date) : null
        }));
        
        setMaintenanceRecords(maintenanceData);
      }
      
      setLoading(false);
    } catch (error) {
      console.error("Error fetching vehicle details:", error);
      setError("Failed to load vehicle details.");
      setLoading(false);
    }
  };

  // Handle form change
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      // Handle nested objects (e.g., tires.brand)
      const [parent, child] = name.split('.');
      setNewVehicle({
        ...newVehicle,
        [parent]: {
          ...newVehicle[parent],
          [child]: value
        }
      });
    } else {
      setNewVehicle({
        ...newVehicle,
        [name]: value
      });
    }
  };

  // Handle maintenance form change
  const handleMaintenanceFormChange = (e) => {
    const { name, value } = e.target;
    setNewMaintenance({
      ...newMaintenance,
      [name]: value
    });
  };

  // Handle image upload
  const handleImageUpload = async (file) => {
    if (!file) return null;
    
    try {
      const storage = getStorage();
      const filename = `${Date.now()}_${file.name}`;
      const storageRef = ref(storage, `vehicleImages/${filename}`);
      
      // Upload with progress tracking
      const uploadTask = uploadBytesResumable(storageRef, file);
      
      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            setUploadProgress(progress);
          },
          (error) => {
            console.error("Error uploading image:", error);
            reject(error);
          },
          async () => {
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              resolve(downloadURL);
            } catch (error) {
              reject(error);
            }
          }
        );
      });
    } catch (error) {
      console.error("Error in image upload:", error);
      return null;
    }
  };

  // Create a new vehicle
  const handleAddVehicle = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError('');
      
      const db = getFirestore();
      
      // Check if vehicle number already exists
      const vehicleQuery = query(
        collection(db, 'vehicles'),
        where('vehicleNumber', '==', newVehicle.vehicleNumber)
      );
      
      const vehicleSnapshot = await getDocs(vehicleQuery);
      
      if (!vehicleSnapshot.empty) {
        setError("A vehicle with this number already exists");
        setLoading(false);
        return;
      }
      
      // Handle image upload if selected
      let imageUrl = null;
      if (imageInputRef.current && imageInputRef.current.files[0]) {
        imageUrl = await handleImageUpload(imageInputRef.current.files[0]);
      }
      
      // Prepare vehicle data for Firestore
      const vehicleData = {
        ...newVehicle,
        imageUrl,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        createdBy: currentUser.uid
      };
      
      // Add vehicle to Firestore
      const docRef = await addDoc(collection(db, 'vehicles'), vehicleData);
      
      // Reset form and refresh list
      setNewVehicle({
        vehicleNumber: '',
        make: '',
        model: '',
        year: new Date().getFullYear(),
        licensePlate: '',
        vin: '',
        color: '',
        type: 'Sedan',
        status: 'available',
        fuelType: 'Gasoline',
        currentMileage: '',
        lastServiceDate: '',
        nextServiceDue: '',
        notes: '',
        assignedTo: null,
        assignedToName: '',
        tires: {
          brand: '',
          size: '',
          purchaseDate: '',
          lastRotation: ''
        },
        insurance: {
          provider: '',
          policyNumber: '',
          expirationDate: ''
        },
        registration: {
          expirationDate: '',
          state: ''
        }
      });
      
      setSuccess("Vehicle added successfully");
      setShowVehicleModal(false);
      fetchVehicles();
      
    } catch (error) {
      console.error("Error adding vehicle:", error);
      setError("Failed to add vehicle. " + error.message);
    } finally {
      setLoading(false);
      setUploadProgress(0);
    }
  };

  // Update an existing vehicle
  const handleUpdateVehicle = async (e) => {
    e.preventDefault();
    
    if (!selectedVehicle || !selectedVehicle.id) {
      setError("No vehicle selected for update");
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      const db = getFirestore();
      
      // Check if vehicle number already exists and isn't this vehicle
      if (newVehicle.vehicleNumber !== selectedVehicle.vehicleNumber) {
        const vehicleQuery = query(
          collection(db, 'vehicles'),
          where('vehicleNumber', '==', newVehicle.vehicleNumber)
        );
        
        const vehicleSnapshot = await getDocs(vehicleQuery);
        
        if (!vehicleSnapshot.empty) {
          setError("A vehicle with this number already exists");
          setLoading(false);
          return;
        }
      }
      
      // Handle image upload if selected
      let imageUrl = selectedVehicle.imageUrl;
      if (imageInputRef.current && imageInputRef.current.files[0]) {
        // Delete old image if exists
        if (selectedVehicle.imageUrl) {
          try {
            const storage = getStorage();
            const imageRef = ref(storage, selectedVehicle.imageUrl);
            await deleteObject(imageRef);
          } catch (error) {
            console.error("Error deleting old image:", error);
          }
        }
        
        imageUrl = await handleImageUpload(imageInputRef.current.files[0]);
      }
      
      // Prepare vehicle data for update
      const vehicleData = {
        ...newVehicle,
        imageUrl,
        updatedAt: Timestamp.now()
      };
      
      // Update vehicle in Firestore
      await updateDoc(doc(db, 'vehicles', selectedVehicle.id), vehicleData);
      
      // Update profiles of both old and new assigned users if changed
      if (selectedVehicle.assignedTo !== newVehicle.assignedTo) {
        // If there was a previous assignment
        if (selectedVehicle.assignedTo) {
          const oldUserProfileRef = doc(db, 'userProfiles', selectedVehicle.assignedTo);
          const oldUserProfileSnap = await getDoc(oldUserProfileRef);
          
          if (oldUserProfileSnap.exists()) {
            await updateDoc(oldUserProfileRef, {
              vehicle: null,
              updatedAt: Timestamp.now()
            });
          }
          
          // Add to assignment history
          await addDoc(collection(db, 'vehicleAssignmentHistory'), {
            vehicleId: selectedVehicle.id,
            vehicleNumber: selectedVehicle.vehicleNumber,
            userId: selectedVehicle.assignedTo,
            userName: selectedVehicle.assignedToName,
            assignmentDate: selectedVehicle.assignmentDate || Timestamp.now(),
            returnDate: Timestamp.now(),
            returnNotes: 'Vehicle reassigned'
          });
        }
        
        // If there's a new assignment
        if (newVehicle.assignedTo) {
          const newUserProfileRef = doc(db, 'userProfiles', newVehicle.assignedTo);
          const newUserProfileSnap = await getDoc(newUserProfileRef);
          
          if (newUserProfileSnap.exists()) {
            await updateDoc(newUserProfileRef, {
              vehicle: newVehicle.vehicleNumber,
              updatedAt: Timestamp.now()
            });
          }
          
          // Add to assignment history
          await addDoc(collection(db, 'vehicleAssignmentHistory'), {
            vehicleId: selectedVehicle.id,
            vehicleNumber: newVehicle.vehicleNumber,
            userId: newVehicle.assignedTo,
            userName: newVehicle.assignedToName,
            assignmentDate: Timestamp.now(),
            returnDate: null
          });
        }
      }
      
      setSuccess("Vehicle updated successfully");
      setShowVehicleModal(false);
      fetchVehicles();
      
    } catch (error) {
      console.error("Error updating vehicle:", error);
      setError("Failed to update vehicle. " + error.message);
    } finally {
      setLoading(false);
      setUploadProgress(0);
    }
  };

  // Delete a vehicle
  const handleDeleteVehicle = async () => {
    if (!vehicleToDelete || !vehicleToDelete.id) {
      setError("No vehicle selected for deletion");
      return;
    }
    
    try {
      setLoading(true);
      const db = getFirestore();
      
      // If vehicle has an image, delete it from storage
      if (vehicleToDelete.imageUrl) {
        try {
          const storage = getStorage();
          const imageRef = ref(storage, vehicleToDelete.imageUrl);
          await deleteObject(imageRef);
        } catch (error) {
          console.error("Error deleting image:", error);
        }
      }
      
      // If vehicle is assigned to a user, update user profile
      if (vehicleToDelete.assignedTo) {
        const userProfileRef = doc(db, 'userProfiles', vehicleToDelete.assignedTo);
        const userProfileSnap = await getDoc(userProfileRef);
        
        if (userProfileSnap.exists()) {
          await updateDoc(userProfileRef, {
            vehicle: null,
            updatedAt: Timestamp.now()
          });
        }
      }
      
      // Delete vehicle from Firestore
      await deleteDoc(doc(db, 'vehicles', vehicleToDelete.id));
      
      setSuccess("Vehicle deleted successfully");
      setShowDeleteModal(false);
      setVehicleToDelete(null);
      fetchVehicles();
      
    } catch (error) {
      console.error("Error deleting vehicle:", error);
      setError("Failed to delete vehicle. " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Add a maintenance record
  const handleAddMaintenance = async (e) => {
    e.preventDefault();
    
    if (!selectedVehicle || !selectedVehicle.id) {
      setError("No vehicle selected");
      return;
    }
    
    try {
      setLoading(true);
      const db = getFirestore();
      
      // Prepare maintenance data
      const maintenanceData = {
        ...newMaintenance,
        vehicleId: selectedVehicle.id,
        vehicleNumber: selectedVehicle.vehicleNumber,
        createdAt: Timestamp.now(),
        createdBy: currentUser.uid
      };
      
      // Add maintenance record to Firestore
      await addDoc(collection(db, 'maintenance'), maintenanceData);
      
      // Update vehicle's last service date if it's newer
      const vehicleRef = doc(db, 'vehicles', selectedVehicle.id);
      const vehicleData = {
        lastServiceDate: newMaintenance.date,
        updatedAt: Timestamp.now()
      };
      
      // Calculate next service due date based on type
      const maintenanceDate = new Date(newMaintenance.date);
      let nextServiceDate = new Date(maintenanceDate);
      
      switch (newMaintenance.type) {
        case 'Oil Change':
          nextServiceDate.setMonth(nextServiceDate.getMonth() + 3); // Due in 3 months
          break;
        case 'Tire Rotation':
          nextServiceDate.setMonth(nextServiceDate.getMonth() + 6); // Due in 6 months
          break;
        case 'Full Service':
          nextServiceDate.setMonth(nextServiceDate.getMonth() + 12); // Due in 12 months
          break;
        default:
          nextServiceDate.setMonth(nextServiceDate.getMonth() + 6); // Default 6 months
      }
      
      vehicleData.nextServiceDue = nextServiceDate.toISOString().split('T')[0];
      
      // Update vehicle in Firestore
      await updateDoc(vehicleRef, vehicleData);
      
      // Reset form and refresh
      setNewMaintenance({
        type: 'Oil Change',
        date: new Date().toISOString().split('T')[0],
        mileage: '',
        description: '',
        cost: '',
        performedBy: '',
        notes: ''
      });
      
      setSuccess("Maintenance record added successfully");
      fetchVehicleDetails(selectedVehicle.id);
      fetchVehicles(); // Refresh vehicle list to update service dates
      
    } catch (error) {
      console.error("Error adding maintenance record:", error);
      setError("Failed to add maintenance record. " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle vehicle selection
  const handleVehicleSelect = (vehicle) => {
    setSelectedVehicle(vehicle);
    setNewVehicle({
      ...vehicle,
      currentMileage: vehicle.currentMileage || '',
      lastServiceDate: vehicle.lastServiceDate ? new Date(vehicle.lastServiceDate).toISOString().split('T')[0] : '',
      nextServiceDue: vehicle.nextServiceDue ? new Date(vehicle.nextServiceDue).toISOString().split('T')[0] : '',
      tires: vehicle.tires || {
        brand: '',
        size: '',
        purchaseDate: '',
        lastRotation: ''
      },
      insurance: vehicle.insurance || {
        provider: '',
        policyNumber: '',
        expirationDate: ''
      },
      registration: vehicle.registration || {
        expirationDate: '',
        state: ''
      }
    });
    setShowVehicleModal(true);
    setActiveTab('details');
  };

  // Handle user assignment to vehicle
  const handleAssignUser = async (userId, userName) => {
    if (!selectedVehicle || !selectedVehicle.id) {
      setError("No vehicle selected");
      return;
    }
    
    try {
      setLoading(true);
      const db = getFirestore();
      
      // If vehicle is already assigned to someone else, update their profile
      if (selectedVehicle.assignedTo && selectedVehicle.assignedTo !== userId) {
        const oldUserProfileRef = doc(db, 'userProfiles', selectedVehicle.assignedTo);
        const oldUserProfileSnap = await getDoc(oldUserProfileRef);
        
        if (oldUserProfileSnap.exists()) {
          await updateDoc(oldUserProfileRef, {
            vehicle: null,
            updatedAt: Timestamp.now()
          });
        }
        
        // Add to assignment history - return old assignment
        await addDoc(collection(db, 'vehicleAssignmentHistory'), {
          vehicleId: selectedVehicle.id,
          vehicleNumber: selectedVehicle.vehicleNumber,
          userId: selectedVehicle.assignedTo,
          userName: selectedVehicle.assignedToName,
          assignmentDate: selectedVehicle.assignmentDate || Timestamp.now(),
          returnDate: Timestamp.now(),
          returnNotes: 'Vehicle reassigned'
        });
      }
      
      // Update user profile with vehicle assignment
      const userProfileRef = doc(db, 'userProfiles', userId);
      const userProfileSnap = await getDoc(userProfileRef);
      
      if (userProfileSnap.exists()) {
        await updateDoc(userProfileRef, {
          vehicle: selectedVehicle.vehicleNumber,
          updatedAt: Timestamp.now()
        });
      }
      
      // Update vehicle with user assignment
      const vehicleRef = doc(db, 'vehicles', selectedVehicle.id);
      await updateDoc(vehicleRef, {
        assignedTo: userId,
        assignedToName: userName,
        assignmentDate: Timestamp.now(),
        status: 'assigned',
        updatedAt: Timestamp.now()
      });
      
      // Add to assignment history - new assignment
      await addDoc(collection(db, 'vehicleAssignmentHistory'), {
        vehicleId: selectedVehicle.id,
        vehicleNumber: selectedVehicle.vehicleNumber,
        userId: userId,
        userName: userName,
        assignmentDate: Timestamp.now(),
        returnDate: null
      });
      
      setSuccess(`Vehicle assigned to ${userName} successfully`);
      setIsAssignmentMode(false);
      fetchVehicles();
      
    } catch (error) {
      console.error("Error assigning vehicle:", error);
      setError("Failed to assign vehicle. " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle unassigning vehicle from user
  const handleUnassignVehicle = async () => {
    if (!selectedVehicle || !selectedVehicle.id || !selectedVehicle.assignedTo) {
      setError("Vehicle is not assigned");
      return;
    }
    
    try {
      setLoading(true);
      const db = getFirestore();
      
      // Update user profile
      const userProfileRef = doc(db, 'userProfiles', selectedVehicle.assignedTo);
      const userProfileSnap = await getDoc(userProfileRef);
      
      if (userProfileSnap.exists()) {
        await updateDoc(userProfileRef, {
          vehicle: null,
          updatedAt: Timestamp.now()
        });
      }
      
      // Update vehicle
      const vehicleRef = doc(db, 'vehicles', selectedVehicle.id);
      await updateDoc(vehicleRef, {
        assignedTo: null,
        assignedToName: '',
        status: 'available',
        updatedAt: Timestamp.now()
      });
      
      // Add to assignment history
      await addDoc(collection(db, 'vehicleAssignmentHistory'), {
        vehicleId: selectedVehicle.id,
        vehicleNumber: selectedVehicle.vehicleNumber,
        userId: selectedVehicle.assignedTo,
        userName: selectedVehicle.assignedToName,
        assignmentDate: selectedVehicle.assignmentDate?.toDate() || Timestamp.now(),
        returnDate: Timestamp.now(),
        returnNotes: 'Vehicle unassigned'
      });
      
      setSuccess("Vehicle unassigned successfully");
      fetchVehicles();
      setShowVehicleModal(false);
      
    } catch (error) {
      console.error("Error unassigning vehicle:", error);
      setError("Failed to unassign vehicle. " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (date) => {
    if (!date) return 'N/A';
    
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge color
  const getStatusColor = (status) => {
    switch (status) {
      case 'available':
        return 'bg-green-600';
      case 'assigned':
        return 'bg-blue-600';
      case 'maintenance':
        return 'bg-yellow-600';
      case 'out-of-service':
        return 'bg-red-600';
      default:
        return 'bg-gray-600';
    }
  };

  // Get maintenance type badge color
  const getMaintenanceTypeColor = (type) => {
    switch (type) {
      case 'Oil Change':
        return 'bg-green-600';
      case 'Tire Rotation':
        return 'bg-blue-600';
      case 'Brake Service':
        return 'bg-red-600';
      case 'Full Service':
        return 'bg-purple-600';
      case 'Repair':
        return 'bg-yellow-600';
      default:
        return 'bg-gray-600';
    }
  };

  // Calculate if maintenance is due soon
  const isMaintenanceDueSoon = (vehicle) => {
    if (!vehicle.nextServiceDue) return false;
    
    const nextService = new Date(vehicle.nextServiceDue);
    const today = new Date();
    const daysUntilService = Math.floor((nextService - today) / (1000 * 60 * 60 * 24));
    
    return daysUntilService <= 14; // Due in the next 14 days
  };

  // Calculate if maintenance is overdue
  const isMaintenanceOverdue = (vehicle) => {
    if (!vehicle.nextServiceDue) return false;
    
    const nextService = new Date(vehicle.nextServiceDue);
    const today = new Date();
    
    return nextService < today;
  };

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-gray-400 mb-4">You don't have permission to access this page.</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md shadow-md"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 shadow-md border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">
              Fleet Management
            </h1>
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Alert Messages */}
        {error && (
          <div className="bg-red-900 border border-red-700 text-white px-4 py-3 rounded-md mb-4 shadow-md">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-300 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>{error}</span>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-900 border border-green-700 text-white px-4 py-3 rounded-md mb-4 shadow-md">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-300 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{success}</span>
            </div>
          </div>
        )}

        {/* Action Bar */}
        <div className="bg-gray-800 p-4 rounded-lg shadow-md mb-6 border border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-3 md:space-y-0">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  setSelectedVehicle(null);
                  setNewVehicle({
                    vehicleNumber: '',
                    make: '',
                    model: '',
                    year: new Date().getFullYear(),
                    licensePlate: '',
                    vin: '',
                    color: '',
                    type: 'Sedan',
                    status: 'available',
                    fuelType: 'Gasoline',
                    currentMileage: '',
                    lastServiceDate: '',
                    nextServiceDue: '',
                    notes: '',
                    assignedTo: null,
                    assignedToName: '',
                    tires: {
                      brand: '',
                      size: '',
                      purchaseDate: '',
                      lastRotation: ''
                    },
                    insurance: {
                      provider: '',
                      policyNumber: '',
                      expirationDate: ''
                    },
                    registration: {
                      expirationDate: '',
                      state: ''
                    }
                  });
                  setShowVehicleModal(true);
                }}
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                </svg>
                Add New Vehicle
              </button>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300'}`}
                  title="Grid View"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                  </svg>
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300'}`}
                  title="List View"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-2 w-full md:w-auto">
              <div className="relative flex-grow md:w-64">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  className="block w-full bg-gray-700 border border-gray-600 rounded-md py-2 pl-10 pr-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Search vehicles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      fetchVehicles();
                    }
                  }}
                />
              </div>
              
              <select
                className="bg-gray-700 border border-gray-600 text-white rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="available">Available</option>
                <option value="assigned">Assigned</option>
                <option value="maintenance">Maintenance</option>
                <option value="out-of-service">Out of Service</option>
              </select>
              
              <select
                className="bg-gray-700 border border-gray-600 text-white rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="vehicleNumber">Sort by #</option>
                <option value="make">Sort by Make</option>
                <option value="model">Sort by Model</option>
                <option value="year">Sort by Year</option>
                <option value="status">Sort by Status</option>
              </select>
              
              <button
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className="bg-gray-700 text-white px-3 py-2 rounded-md hover:bg-gray-600 transition-colors flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                </svg>
                Filters
              </button>
            </div>
          </div>
          
          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="mt-4 p-4 bg-gray-750 rounded-md border border-gray-600">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Vehicle Type</label>
                  <div className="grid grid-cols-2 gap-2">
                    {['Sedan', 'SUV', 'Truck', 'Van'].map(type => (
                      <label key={type} className="inline-flex items-center">
                        <input
                          type="checkbox"
                          className="form-checkbox h-4 w-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                          checked={vehicleTypesFilter.includes(type)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setVehicleTypesFilter([...vehicleTypesFilter, type]);
                            } else {
                              setVehicleTypesFilter(vehicleTypesFilter.filter(t => t !== type));
                            }
                          }}
                        />
                        <span className="ml-2 text-sm text-gray-300">{type}</span>
                      </label>
                    ))}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Year Range</label>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs text-gray-400">Min</label>
                      <input
                        type="number"
                        min="1990"
                        max={new Date().getFullYear()}
                        className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white"
                        value={yearRangeFilter.min}
                        onChange={(e) => setYearRangeFilter({...yearRangeFilter, min: parseInt(e.target.value)})}
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-400">Max</label>
                      <input
                        type="number"
                        min="1990"
                        max={new Date().getFullYear()}
                        className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white"
                        value={yearRangeFilter.max}
                        onChange={(e) => setYearRangeFilter({...yearRangeFilter, max: parseInt(e.target.value)})}
                      />
                    </div>
                  </div>
                </div>
                
                <div className="flex items-end">
                  <button
                    onClick={() => {
                      // Reset all filters
                      setFilterStatus('all');
                      setSortBy('vehicleNumber');
                      setSearchTerm('');
                      setVehicleTypesFilter([]);
                      setYearRangeFilter({ min: 2000, max: new Date().getFullYear() });
                      fetchVehicles();
                    }}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md shadow-md transition-colors"
                  >
                    Reset All Filters
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Loading Indicator */}
        {loading && (
          <div className="flex justify-center my-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        )}

        {/* Grid View */}
        {!loading && viewMode === 'grid' && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {vehicles.length === 0 ? (
              <div className="col-span-full bg-gray-800 p-8 rounded-lg text-center border border-gray-700 shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-300 mb-2">No vehicles found</h3>
                <p className="text-gray-400">Add a new vehicle to get started or adjust your filters</p>
              </div>
            ) : (
              vehicles.map(vehicle => (
                <div
                  key={vehicle.id}
                  className={`bg-gray-800 rounded-lg overflow-hidden border border-gray-700 shadow-md transition-all duration-300 hover:shadow-lg hover:border-blue-500 cursor-pointer ${
                    isMaintenanceOverdue(vehicle) ? 'ring-2 ring-red-500' : 
                    isMaintenanceDueSoon(vehicle) ? 'ring-2 ring-yellow-500' : ''
                  }`}
                  onClick={() => handleVehicleSelect(vehicle)}
                >
                  <div className="relative">
                    {vehicle.imageUrl ? (
                      <img
                        src={vehicle.imageUrl}
                        alt={`${vehicle.make} ${vehicle.model}`}
                        className="w-full h-48 object-cover"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gray-700 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-20 w-20 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
                        </svg>
                      </div>
                    )}
                    <div className="absolute top-2 left-2 right-2 flex justify-between">
                      <span className={`px-2 py-1 rounded-md text-xs font-bold text-white ${getStatusColor(vehicle.status)}`}>
                        {vehicle.status.toUpperCase()}
                      </span>
                      <span className="px-2 py-1 bg-gray-900 bg-opacity-70 rounded-md text-xs font-medium text-white">
                        #{vehicle.vehicleNumber}
                      </span>
                    </div>
                    
                    {isMaintenanceOverdue(vehicle) && (
                      <div className="absolute bottom-2 left-2 right-2 bg-red-900 bg-opacity-80 text-white px-2 py-1 rounded text-sm flex items-center animate-pulse">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Maintenance Overdue
                      </div>
                    )}
                    
                    {!isMaintenanceOverdue(vehicle) && isMaintenanceDueSoon(vehicle) && (
                      <div className="absolute bottom-2 left-2 right-2 bg-yellow-800 bg-opacity-80 text-white px-2 py-1 rounded text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Service Due Soon
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-white">{vehicle.make} {vehicle.model}</h3>
                    <p className="text-gray-400">Year: {vehicle.year}</p>
                    
                    <div className="mt-2 flex justify-between items-center">
                      <div className="text-sm text-gray-300">
                        <span className="font-medium">Type: </span>{vehicle.type}
                      </div>
                      <div className="text-sm text-gray-300">
                        <span className="font-medium">Color: </span>{vehicle.color}
                      </div>
                    </div>
                    
                    {vehicle.currentMileage && (
                      <div className="mt-2 text-sm text-gray-300">
                        <span className="font-medium">Mileage: </span>{new Intl.NumberFormat().format(vehicle.currentMileage)} mi
                      </div>
                    )}
                    
                    <div className="mt-4 pt-4 border-t border-gray-700">
                      {vehicle.assignedTo ? (
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            {users.find(u => u.id === vehicle.assignedTo)?.photoBase64 ? (
                              <div className="h-8 w-8 rounded-full overflow-hidden">
                                <img 
                                  src={users.find(u => u.id === vehicle.assignedTo)?.photoBase64} 
                                  alt="User" 
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            ) : (
                              <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                              </div>
                            )}
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-white">Assigned to:</p>
                            <p className="text-sm text-gray-300">{vehicle.assignedToName}</p>
                          </div>
                        </div>
                      ) : (
                        <p className="text-sm text-gray-400 italic">Not assigned</p>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {/* List View */}
        {!loading && viewMode === 'list' && (
          <div className="bg-gray-800 rounded-lg overflow-hidden shadow-md border border-gray-700">
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-900 text-gray-300 text-left">
                  <tr>
                    <th className="py-3 px-4 font-medium">#</th>
                    <th className="py-3 px-4 font-medium">Vehicle</th>
                    <th className="py-3 px-4 font-medium">Type</th>
                    <th className="py-3 px-4 font-medium">Status</th>
                    <th className="py-3 px-4 font-medium">Mileage</th>
                    <th className="py-3 px-4 font-medium">Assigned To</th>
                    <th className="py-3 px-4 font-medium">Last Service</th>
                    <th className="py-3 px-4 font-medium">Next Service</th>
                    <th className="py-3 px-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {vehicles.length === 0 ? (
                    <tr>
                      <td colSpan="9" className="py-6 text-center">
                        <p className="text-gray-400">No vehicles found</p>
                      </td>
                    </tr>
                  ) : (
                    vehicles.map(vehicle => (
                      <tr 
                        key={vehicle.id}
                        className={`hover:bg-gray-750 cursor-pointer ${
                          isMaintenanceOverdue(vehicle) ? 'bg-red-900 bg-opacity-20' : 
                          isMaintenanceDueSoon(vehicle) ? 'bg-yellow-900 bg-opacity-20' : ''
                        }`}
                        onClick={() => handleVehicleSelect(vehicle)}
                      >
                        <td className="py-3 px-4 text-white">{vehicle.vehicleNumber}</td>
                        <td className="py-3 px-4">
                          <div>
                            <div className="text-white font-medium">{vehicle.make} {vehicle.model}</div>
                            <div className="text-gray-400 text-sm">{vehicle.year} • {vehicle.color}</div>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-gray-300">{vehicle.type}</td>
                        <td className="py-3 px-4">
                          <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium text-white ${getStatusColor(vehicle.status)}`}>
                            {vehicle.status}
                          </span>
                        </td>
                        <td className="py-3 px-4 text-gray-300">
                          {vehicle.currentMileage ? new Intl.NumberFormat().format(vehicle.currentMileage) + ' mi' : 'N/A'}
                        </td>
                        <td className="py-3 px-4">
                          {vehicle.assignedTo ? (
                            <div className="flex items-center">
                              {users.find(u => u.id === vehicle.assignedTo)?.photoBase64 ? (
                                <div className="h-6 w-6 rounded-full overflow-hidden mr-2">
                                  <img 
                                    src={users.find(u => u.id === vehicle.assignedTo)?.photoBase64} 
                                    alt="User" 
                                    className="h-full w-full object-cover"
                                  />
                                </div>
                              ) : (
                                <div className="h-6 w-6 rounded-full bg-blue-600 flex items-center justify-center mr-2">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 07a7 7h14a7 7 0 00-7-7z" />
                                  </svg>
                                </div>
                              )}
                              <span className="text-gray-300">{vehicle.assignedToName}</span>
                            </div>
                          ) : (
                            <span className="text-gray-500 italic">Unassigned</span>
                          )}
                        </td>
                        <td className="py-3 px-4 text-gray-300">
                          {formatDate(vehicle.lastServiceDate)}
                        </td>
                        <td className="py-3 px-4">
                          {isMaintenanceOverdue(vehicle) ? (
                            <span className="text-red-400 flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              {formatDate(vehicle.nextServiceDue)} (Overdue)
                            </span>
                          ) : isMaintenanceDueSoon(vehicle) ? (
                            <span className="text-yellow-400 flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              {formatDate(vehicle.nextServiceDue)} (Soon)
                            </span>
                          ) : (
                            <span className="text-gray-300">{formatDate(vehicle.nextServiceDue)}</span>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleVehicleSelect(vehicle);
                              }}
                              className="bg-blue-600 hover:bg-blue-700 text-white p-1 rounded-md"
                              title="View Details"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedVehicle(vehicle);
                                setIsAssignmentMode(true);
                              }}
                              className="bg-green-600 hover:bg-green-700 text-white p-1 rounded-md"
                              title="Assign User"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                              </svg>
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setVehicleToDelete(vehicle);
                                setShowDeleteModal(true);
                              }}
                              className="bg-red-600 hover:bg-red-700 text-white p-1 rounded-md"
                              title="Delete Vehicle"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>

      {/* Vehicle Details Modal */}
      {showVehicleModal && (
        <div className="fixed inset-0 overflow-y-auto z-50 flex items-center justify-center">
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" onClick={() => setShowVehicleModal(false)}></div>
          
          <div className="relative bg-gray-900 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto border border-gray-700 shadow-xl transform transition-all">
            <div className="sticky top-0 z-10 bg-gradient-to-r from-gray-900 to-gray-800 p-4 border-b border-gray-700 flex justify-between items-center">
              <h3 className="text-xl font-semibold text-white">
                {selectedVehicle ? `Edit Vehicle: ${selectedVehicle.make} ${selectedVehicle.model}` : 'Add New Vehicle'}
              </h3>
              <button
                onClick={() => setShowVehicleModal(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {/* Tabs Navigation (only in edit mode) */}
            {selectedVehicle && (
              <div className="px-4 pt-4 flex border-b border-gray-700">
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'details' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-gray-200'}`}
                  onClick={() => setActiveTab('details')}
                >
                  Details
                </button>
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'inspections' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-gray-200'}`}
                  onClick={() => setActiveTab('inspections')}
                >
                  Inspections
                </button>
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'maintenance' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-gray-200'}`}
                  onClick={() => setActiveTab('maintenance')}
                >
                  Maintenance
                </button>
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'history' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-gray-200'}`}
                  onClick={() => setActiveTab('history')}
                >
                  Assignment History
                </button>
              </div>
            )}
            
            {/* Vehicle Details Tab */}
            {(!selectedVehicle || activeTab === 'details') && (
              <div className="p-4">
                <form onSubmit={selectedVehicle ? handleUpdateVehicle : handleAddVehicle}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Basic Information */}
                    <div className="md:col-span-2 bg-gray-800 p-4 rounded-lg border border-gray-700 mb-4">
                      <h4 className="text-lg font-medium text-blue-300 mb-4">Basic Information</h4>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Vehicle Number</label>
                          <input
                            type="text"
                            name="vehicleNumber"
                            value={newVehicle.vehicleNumber}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Make</label>
                          <input
                            type="text"
                            name="make"
                            value={newVehicle.make}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Model</label>
                          <input
                            type="text"
                            name="model"
                            value={newVehicle.model}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Year</label>
                          <input
                            type="number"
                            name="year"
                            min="1990"
                            max={new Date().getFullYear() + 1}
                            value={newVehicle.year}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Color</label>
                          <input
                            type="text"
                            name="color"
                            value={newVehicle.color}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Type</label>
                          <select
                            name="type"
                            value={newVehicle.type}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="Sedan">Sedan</option>
                            <option value="SUV">SUV</option>
                            <option value="Truck">Truck</option>
                            <option value="Van">Van</option>
                            <option value="Hatchback">Hatchback</option>
                            <option value="Coupe">Coupe</option>
                            <option value="Convertible">Convertible</option>
                            <option value="Wagon">Wagon</option>
                            <option value="Minivan">Minivan</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Fuel Type</label>
                          <select
                            name="fuelType"
                            value={newVehicle.fuelType}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="Gasoline">Gasoline</option>
                            <option value="Diesel">Diesel</option>
                            <option value="Electric">Electric</option>
                            <option value="Hybrid">Hybrid</option>
                            <option value="Plug-in Hybrid">Plug-in Hybrid</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Status</label>
                          <select
                            name="status"
                            value={newVehicle.status}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="available">Available</option>
                            <option value="assigned">Assigned</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="out-of-service">Out of Service</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Current Mileage</label>
                          <input
                            type="number"
                            name="currentMileage"
                            value={newVehicle.currentMileage}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="e.g. 25000"
                          />
                        </div>
                      </div>
                      
                      <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">VIN Number</label>
                          <input
                            type="text"
                            name="vin"
                            value={newVehicle.vin}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Vehicle Identification Number"
                          />
                        </div>
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">License Plate</label>
                          <input
                            type="text"
                            name="licensePlate"
                            value={newVehicle.licensePlate}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* Vehicle Image */}
                    <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                      <h4 className="text-lg font-medium text-blue-300 mb-4">Vehicle Image</h4>
                      
                      <div className="flex flex-col items-center">
                        <div className="w-full h-48 bg-gray-700 mb-4 rounded-lg overflow-hidden border border-gray-600 flex items-center justify-center">
                          {selectedVehicle && selectedVehicle.imageUrl ? (
                            <img
                              src={selectedVehicle.imageUrl}
                              alt="Vehicle"
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-20 w-20 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
                            </svg>
                          )}
                        </div>
                        
                        <input
                          type="file"
                          ref={imageInputRef}
                          accept="image/*"
                          className="hidden"
                        />
                        
                        <button
                          type="button"
                          onClick={() => imageInputRef.current.click()}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md shadow-md transition-colors flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          {selectedVehicle && selectedVehicle.imageUrl ? 'Change Image' : 'Upload Image'}
                        </button>
                        
                        {uploadProgress > 0 && uploadProgress < 100 && (
                          <div className="w-full mt-4">
                            <div className="bg-gray-700 rounded-full h-2.5 overflow-hidden">
                              <div 
                                className="bg-blue-600 h-2.5 rounded-full" 
                                style={{ width: `${uploadProgress}%` }}
                              ></div>
                            </div>
                            <p className="text-sm text-gray-400 text-center mt-1">Uploading: {Math.round(uploadProgress)}%</p>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Service Information */}
                    <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                      <h4 className="text-lg font-medium text-blue-300 mb-4">Service Information</h4>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Last Service Date</label>
                          <input
                            type="date"
                            name="lastServiceDate"
                            value={newVehicle.lastServiceDate}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Next Service Due</label>
                          <input
                            type="date"
                            name="nextServiceDue"
                            value={newVehicle.nextServiceDue}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Tires Brand</label>
                          <input
                            type="text"
                            name="tires.brand"
                            value={newVehicle.tires.brand}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="e.g. Michelin, Goodyear"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Tires Size</label>
                          <input
                            type="text"
                            name="tires.size"
                            value={newVehicle.tires.size}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="e.g. 225/60R16"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Last Tire Rotation</label>
                          <input
                            type="date"
                            name="tires.lastRotation"
                            value={newVehicle.tires.lastRotation}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* Documentation */}
                    <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                      <h4 className="text-lg font-medium text-blue-300 mb-4">Documentation</h4>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Insurance Provider</label>
                          <input
                            type="text"
                            name="insurance.provider"
                            value={newVehicle.insurance.provider}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Insurance Policy Number</label>
                          <input
                            type="text"
                            name="insurance.policyNumber"
                            value={newVehicle.insurance.policyNumber}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Insurance Expiration</label>
                          <input
                            type="date"
                            name="insurance.expirationDate"
                            value={newVehicle.insurance.expirationDate}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Registration State</label>
                          <input
                            type="text"
                            name="registration.state"
                            value={newVehicle.registration.state}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Registration Expiration</label>
                          <input
                            type="date"
                            name="registration.expirationDate"
                            value={newVehicle.registration.expirationDate}
                            onChange={handleFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* Notes Section */}
                    <div className="md:col-span-2 bg-gray-800 p-4 rounded-lg border border-gray-700">
                      <h4 className="text-lg font-medium text-blue-300 mb-4">Notes & Comments</h4>
                      
                      <div>
                        <textarea
                          name="notes"
                          value={newVehicle.notes}
                          onChange={handleFormChange}
                          className="w-full h-24 bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Add any additional notes about this vehicle..."
                        ></textarea>
                      </div>
                    </div>
                  </div>
                  
                  {/* Form Actions */}
                  <div className="mt-6 flex justify-between">
                    <div>
                      {selectedVehicle && selectedVehicle.assignedTo && (
                        <button
                          type="button"
                          onClick={handleUnassignVehicle}
                          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md shadow-md transition-colors"
                        >
                          Unassign Vehicle
                        </button>
                      )}
                    </div>
                    <div className="flex space-x-3">
                      <button
                        type="button"
                        onClick={() => setShowVehicleModal(false)}
                        className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md shadow-md transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md shadow-md transition-colors flex items-center"
                        disabled={loading}
                      >
                        {loading ? (
                          <>
                            <svg className="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                          </>
                        ) : (
                          <>
                            {selectedVehicle ? 'Update Vehicle' : 'Add Vehicle'}
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            )}
            
            {/* Inspections Tab */}
            {selectedVehicle && activeTab === 'inspections' && (
              <div className="p-4">
                <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
                  <div className="p-4 bg-gray-750 border-b border-gray-700">
                    <h4 className="text-lg font-medium text-blue-300">Recent Inspections</h4>
                    <p className="text-sm text-gray-400 mt-1">Showing recent vehicle inspections for {selectedVehicle.make} {selectedVehicle.model} (#{selectedVehicle.vehicleNumber})</p>
                  </div>
                  
                  {loading ? (
                    <div className="flex justify-center p-8">
                      <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                  ) : inspections.length === 0 ? (
                    <div className="p-8 text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-gray-400 mb-2">No inspection records found for this vehicle</p>
                      <p className="text-sm text-gray-500">Inspections will be displayed here when they are completed</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full">
                        <thead className="bg-gray-900 text-gray-300 text-left">
                          <tr>
                            <th className="py-3 px-4 font-medium">Date</th>
                            <th className="py-3 px-4 font-medium">Inspector</th>
                            <th className="py-3 px-4 font-medium">Mileage</th>
                            <th className="py-3 px-4 font-medium">Service Flag</th>
                            <th className="py-3 px-4 font-medium">Notes</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-700">
                          {inspections.map(inspection => (
                            <tr key={inspection.id} className="hover:bg-gray-750">
                              <td className="py-3 px-4 text-white">
                                {inspection.timestamp ? (
                                  <div>
                                    <div>{inspection.timestamp.toLocaleDateString()}</div>
                                    <div className="text-gray-500 text-sm">{inspection.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
                                  </div>
                                ) : 'N/A'}
                              </td>
                              <td className="py-3 px-4">
                                <div className="flex items-center">
                                  {inspection.userPhotoBase64 ? (
                                    <div className="h-8 w-8 rounded-full overflow-hidden mr-2 border border-gray-600">
                                      <img 
                                        src={inspection.userPhotoBase64} 
                                        alt={inspection.userName} 
                                        className="h-full w-full object-cover"
                                      />
                                    </div>
                                  ) : (
                                    <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center mr-2">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                      </svg>
                                    </div>
                                  )}
                                  <div>
                                    <div className="text-gray-300">{inspection.userName}</div>
                                    {inspection.userJobTitle && (
                                      <div className="text-gray-500 text-sm">{inspection.userJobTitle}</div>
                                    )}
                                  </div>
                                </div>
                              </td>
                              <td className="py-3 px-4 text-gray-300">
                                {inspection.mileage ? new Intl.NumberFormat().format(inspection.mileage) + ' mi' : 'N/A'}
                              </td>
                              <td className="py-3 px-4">
                                {inspection.needsService ? (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900 text-red-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    Needs Service
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900 text-green-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Good Condition
                                  </span>
                                )}
                              </td>
                              <td className="py-3 px-4 text-gray-300">
                                {inspection.notes ? (
                                  <div className="truncate max-w-xs" title={inspection.notes}>
                                    {inspection.notes}
                                  </div>
                                ) : (
                                  <span className="text-gray-500 italic">No notes</span>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {/* Maintenance Tab */}
            {selectedVehicle && activeTab === 'maintenance' && (
              <div className="p-4 space-y-6">
                {/* Maintenance Records */}
                <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
                  <div className="p-4 bg-gray-750 border-b border-gray-700">
                    <h4 className="text-lg font-medium text-blue-300">Maintenance History</h4>
                    <p className="text-sm text-gray-400 mt-1">Showing maintenance records for {selectedVehicle.make} {selectedVehicle.model} (#{selectedVehicle.vehicleNumber})</p>
                  </div>
                  
                  {loading ? (
                    <div className="flex justify-center p-8">
                      <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                  ) : maintenanceRecords.length === 0 ? (
                    <div className="p-8 text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <p className="text-gray-400 mb-2">No maintenance records found</p>
                      <p className="text-sm text-gray-500">Add a maintenance record below to start tracking service history</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full">
                        <thead className="bg-gray-900 text-gray-300 text-left">
                          <tr>
                            <th className="py-3 px-4 font-medium">Date</th>
                            <th className="py-3 px-4 font-medium">Type</th>
                            <th className="py-3 px-4 font-medium">Mileage</th>
                            <th className="py-3 px-4 font-medium">Performed By</th>
                            <th className="py-3 px-4 font-medium">Cost</th>
                            <th className="py-3 px-4 font-medium">Description</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-700">
                          {maintenanceRecords.map(record => (
                            <tr key={record.id} className="hover:bg-gray-750">
                              <td className="py-3 px-4 text-white">
                                {formatDate(record.date)}
                              </td>
                              <td className="py-3 px-4">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getMaintenanceTypeColor(record.type)} text-white`}>
                                  {record.type}
                                </span>
                              </td>
                              <td className="py-3 px-4 text-gray-300">
                                {record.mileage ? new Intl.NumberFormat().format(record.mileage) + ' mi' : 'N/A'}
                              </td>
                              <td className="py-3 px-4 text-gray-300">
                                {record.performedBy || 'N/A'}
                              </td>
                              <td className="py-3 px-4 text-gray-300">
                                {record.cost ? '$' + parseFloat(record.cost).toFixed(2) : 'N/A'}
                              </td>
                              <td className="py-3 px-4 text-gray-300">
                                {record.description ? (
                                  <div className="truncate max-w-xs" title={record.description}>
                                    {record.description}
                                  </div>
                                ) : (
                                  <span className="text-gray-500 italic">No description</span>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
                
                {/* Add Maintenance Form */}
                <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
                  <div className="p-4 bg-gray-750 border-b border-gray-700">
                    <h4 className="text-lg font-medium text-blue-300">Add Maintenance Record</h4>
                  </div>
                  
                  <div className="p-4">
                    <form onSubmit={handleAddMaintenance}>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Maintenance Type</label>
                          <select
                            name="type"
                            value={newMaintenance.type}
                            onChange={handleMaintenanceFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          >
                            <option value="Oil Change">Oil Change</option>
                            <option value="Tire Rotation">Tire Rotation</option>
                            <option value="Brake Service">Brake Service</option>
                            <option value="Full Service">Full Service</option>
                            <option value="Repair">Repair</option>
                            <option value="Inspection">Inspection</option>
                            <option value="Other">Other</option>
                          </select>
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Date</label>
                          <input
                            type="date"
                            name="date"
                            value={newMaintenance.date}
                            onChange={handleMaintenanceFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Mileage</label>
                          <input
                            type="number"
                            name="mileage"
                            value={newMaintenance.mileage}
                            onChange={handleMaintenanceFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="e.g. 25000"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Performed By</label>
                          <input
                            type="text"
                            name="performedBy"
                            value={newMaintenance.performedBy}
                            onChange={handleMaintenanceFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="e.g. Service Center Name"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-gray-400 text-sm font-medium mb-1">Cost ($)</label>
                          <input
                            type="number"
                            name="cost"
                            value={newMaintenance.cost}
                            onChange={handleMaintenanceFormChange}
                            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="e.g. 250.00"
                            step="0.01"
                            min="0"
                          />
                        </div>
                        
                        <div className="md:col-span-3">
                          <label className="block text-gray-400 text-sm font-medium mb-1">Description</label>
                          <textarea
                            name="description"
                            value={newMaintenance.description}
                            onChange={handleMaintenanceFormChange}
                            className="w-full h-20 bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Describe the maintenance performed..."
                          ></textarea>
                        </div>
                      </div>
                      
                      <div className="mt-4 flex justify-end">
                        <button
                          type="submit"
                          className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md shadow-md transition-colors flex items-center"
                          disabled={loading}
                        >
                          {loading ? (
                            <>
                              <svg className="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Processing...
                            </>
                          ) : (
                            <>
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                              Add Maintenance Record
                            </>
                          )}
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            )}
            
            {/* Assignment History Tab */}
            {selectedVehicle && activeTab === 'history' && (
              <div className="p-4">
                <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
                  <div className="p-4 bg-gray-750 border-b border-gray-700">
                    <h4 className="text-lg font-medium text-blue-300">Assignment History</h4>
                    <p className="text-sm text-gray-400 mt-1">Showing all user assignments for {selectedVehicle.make} {selectedVehicle.model} (#{selectedVehicle.vehicleNumber})</p>
                  </div>
                  
                  {loading ? (
                    <div className="flex justify-center p-8">
                      <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                  ) : vehicleHistory.length === 0 ? (
                    <div className="p-8 text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                      <p className="text-gray-400 mb-2">No assignment history found</p>
                      <p className="text-sm text-gray-500">This vehicle has not been assigned to any users yet</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full">
                        <thead className="bg-gray-900 text-gray-300 text-left">
                          <tr>
                            <th className="py-3 px-4 font-medium">User</th>
                            <th className="py-3 px-4 font-medium">Assigned Date</th>
                            <th className="py-3 px-4 font-medium">Return Date</th>
                            <th className="py-3 px-4 font-medium">Duration</th>
                            <th className="py-3 px-4 font-medium">Status</th>
                            <th className="py-3 px-4 font-medium">Notes</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-700">
                          {vehicleHistory.map(history => (
                            <tr key={history.id} className="hover:bg-gray-750">
                              <td className="py-3 px-4 text-white">
                                <div className="flex items-center">
                                  <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center mr-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                  </div>
                                  <span>{history.userName}</span>
                                </div>
                              </td>
                              <td className="py-3 px-4 text-gray-300">
                                {formatDate(history.assignmentDate)}
                              </td>
                              <td className="py-3 px-4 text-gray-300">
                                {history.returnDate ? formatDate(history.returnDate) : (
                                  <span className="text-green-400">Current Assignment</span>
                                )}
                              </td>
                              <td className="py-3 px-4 text-gray-300">
                                {history.assignmentDate && (
                                  history.returnDate ? 
                                    `${Math.ceil((history.returnDate - history.assignmentDate) / (1000 * 60 * 60 * 24))} days` :
                                    `${Math.ceil((new Date() - history.assignmentDate) / (1000 * 60 * 60 * 24))} days (ongoing)`
                                )}
                              </td>
                              <td className="py-3 px-4">
                                {!history.returnDate ? (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900 text-green-300">
                                    Active
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-700 text-gray-300">
                                    Returned
                                  </span>
                                )}
                              </td>
                              <td className="py-3 px-4 text-gray-300">
                                {history.returnNotes || '-'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Assign User Modal */}
      {isAssignmentMode && selectedVehicle && (
        <div className="fixed inset-0 overflow-y-auto z-50 flex items-center justify-center">
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" onClick={() => setIsAssignmentMode(false)}></div>
          
          <div className="relative bg-gray-900 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-gray-700 shadow-xl transform transition-all">
            <div className="sticky top-0 z-10 bg-gradient-to-r from-gray-900 to-gray-800 p-4 border-b border-gray-700 flex justify-between items-center">
              <h3 className="text-xl font-semibold text-white">
                Assign Vehicle: {selectedVehicle.make} {selectedVehicle.model}
              </h3>
              <button
                onClick={() => setIsAssignmentMode(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="p-4">
              {selectedVehicle.assignedTo && (
                <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-4 mb-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      {users.find(u => u.id === selectedVehicle.assignedTo)?.photoBase64 ? (
                        <div className="h-12 w-12 rounded-full overflow-hidden border-2 border-blue-500">
                          <img 
                            src={users.find(u => u.id === selectedVehicle.assignedTo)?.photoBase64} 
                            alt="User" 
                            className="h-full w-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="h-12 w-12 rounded-full bg-blue-600 flex items-center justify-center border-2 border-blue-400">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="ml-4">
                      <h4 className="text-lg font-medium text-white">Currently Assigned To</h4>
                      <p className="text-blue-300">{selectedVehicle.assignedToName}</p>
                    </div>
                    <button
                      onClick={handleUnassignVehicle}
                      className="ml-auto bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-md text-sm shadow-md transition-colors"
                    >
                      Unassign
                    </button>
                  </div>
                </div>
              )}

<div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
                <div className="p-4 bg-gray-750 border-b border-gray-700">
                  <h4 className="text-lg font-medium text-blue-300">Select User to Assign</h4>
                  
                  <div className="mt-2 relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                    <input
                      type="text"
                      className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 pl-10 pr-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Search users..."
                      onChange={(e) => setUserToAssign(e.target.value.toLowerCase())}
                    />
                  </div>
                </div>
                
                <div className="overflow-y-auto max-h-96">
                  <ul className="divide-y divide-gray-700">
                    {users
                      .filter(user => 
                        !userToAssign || 
                        user.displayName.toLowerCase().includes(userToAssign) ||
                        user.email.toLowerCase().includes(userToAssign) ||
                        (user.jobTitle && user.jobTitle.toLowerCase().includes(userToAssign))
                      )
                      .map(user => (
                        <li 
                          key={user.id}
                          className={`p-4 hover:bg-gray-750 cursor-pointer transition-colors ${
                            selectedVehicle.assignedTo === user.id ? 'bg-blue-900 bg-opacity-30' : ''
                          }`}
                          onClick={() => handleAssignUser(user.id, user.displayName)}
                        >
                          <div className="flex items-center">
                            {user.photoBase64 ? (
                              <div className="h-10 w-10 rounded-full overflow-hidden mr-4 border border-gray-600">
                                <img 
                                  src={user.photoBase64} 
                                  alt={user.displayName} 
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center mr-4">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                              </div>
                            )}
                            <div>
                              <h4 className="font-medium text-white">{user.displayName}</h4>
                              <div className="text-sm text-gray-400">
                                <div>{user.email}</div>
                                {user.jobTitle && <div>{user.jobTitle}</div>}
                              </div>
                            </div>
                            {selectedVehicle.assignedTo === user.id && (
                              <div className="ml-auto bg-blue-600 px-3 py-1 rounded-full text-xs text-white">
                                Current
                              </div>
                            )}
                          </div>
                        </li>
                      ))
                    }
                    
                    {users.filter(user => 
                      !userToAssign || 
                      user.displayName.toLowerCase().includes(userToAssign) ||
                      user.email.toLowerCase().includes(userToAssign) ||
                      (user.jobTitle && user.jobTitle.toLowerCase().includes(userToAssign))
                    ).length === 0 && (
                      <li className="p-8 text-center">
                        <p className="text-gray-400">No users found</p>
                      </li>
                    )}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 overflow-y-auto z-50 flex items-center justify-center">
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" onClick={() => setShowDeleteModal(false)}></div>
          
          <div className="relative bg-gray-900 rounded-lg w-full max-w-md overflow-hidden border border-gray-700 shadow-xl transform transition-all">
            <div className="bg-gradient-to-r from-red-900 to-gray-800 p-4 border-b border-gray-700">
              <h3 className="text-lg font-medium text-white">Confirm Deletion</h3>
            </div>
            
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="bg-red-900 rounded-full p-3 mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium text-white">Are you sure you want to delete this vehicle?</p>
                  <p className="text-gray-400 mt-1">{vehicleToDelete?.make} {vehicleToDelete?.model} (#{vehicleToDelete?.vehicleNumber})</p>
                  {vehicleToDelete?.assignedTo && (
                    <p className="text-red-400 mt-2">Warning: This vehicle is currently assigned to {vehicleToDelete?.assignedToName}</p>
                  )}
                </div>
              </div>
              
              <p className="text-gray-300 bg-gray-800 p-3 rounded-md mb-4">
                This action cannot be undone. This will permanently delete the vehicle, its maintenance records, and assignment history.
              </p>
              
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowDeleteModal(false)}
                  className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleDeleteVehicle}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors flex items-center"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    'Delete Vehicle'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Fleet;