import React from 'react';

/**
 * MapControls component handles all the control buttons for the map
 */
const MapControls = ({
  handleFindMyLocation,
  handleRefreshMap,
  toggleZipBoundaries,
  toggleRotation,
  resetRotation,
  showZipCodes,
  isRotationEnabled,
  isClockedIn,
  hasMovedSinceClockIn,
  clearTrail,
  isAdmin,
  handleResetAllTrails,
  // Debug props
  ROUTE_REQUEST_CACHE,
  PENDING_REQUESTS,
  MIN_REQUEST_INTERVAL,
  ZipCodeBoundaryManager
}) => {
  // Ensure button handlers include error handling to prevent crashes
  const safeHandleFindMyLocation = (e) => {
    try {
      if (e) e.preventDefault();
      if (typeof handleFindMyLocation === 'function') {
        handleFindMyLocation();
      }
    } catch (error) {
      console.warn("Error in find my location button handler:", error);
    }
  };

  const safeHandleRefreshMap = (e) => {
    try {
      if (e) e.preventDefault();
      if (typeof handleRefreshMap === 'function') {
        handleRefreshMap();
      }
    } catch (error) {
      console.warn("Error in refresh map button handler:", error);
    }
  };

  const safeToggleZipBoundaries = (e) => {
    try {
      if (e) e.preventDefault();
      if (typeof toggleZipBoundaries === 'function') {
        toggleZipBoundaries();
      }
    } catch (error) {
      console.warn("Error in toggle ZIP boundaries button handler:", error);
    }
  };

  const safeToggleRotation = (e) => {
    try {
      if (e) e.preventDefault();
      if (typeof toggleRotation === 'function') {
        toggleRotation();
      }
    } catch (error) {
      console.warn("Error in toggle rotation button handler:", error);
    }
  };

  const safeResetRotation = (e) => {
    try {
      if (e) e.preventDefault();
      if (typeof resetRotation === 'function') {
        resetRotation();
      }
    } catch (error) {
      console.warn("Error in reset rotation button handler:", error);
    }
  };

  const safeClearTrail = (e) => {
    try {
      if (e) e.preventDefault();
      if (typeof clearTrail === 'function') {
        clearTrail();
      }
    } catch (error) {
      console.warn("Error in clear trail button handler:", error);
    }
  };

  const safeHandleResetAllTrails = (e) => {
    try {
      if (e) e.preventDefault();
      if (typeof handleResetAllTrails === 'function') {
        handleResetAllTrails();
      }
    } catch (error) {
      console.warn("Error in reset all trails button handler:", error);
    }
  };

  const safeClearAllRoutes = (e) => {
    try {
      if (e) e.preventDefault();
      if (typeof window !== 'undefined' && window.clearAllRoutes) {
        window.clearAllRoutes();
      }
    } catch (error) {
      console.warn("Error in clear all routes button handler:", error);
    }
  };

  return (
    <>
      {/* Map control buttons - positioned at the bottom right of the wrapper */}
      <div className="absolute bottom-4 right-4 flex flex-col space-y-2 z-20">
        {/* Find location button */}
        <div 
          className="bg-blue-600 hover:bg-blue-500 text-white p-2 rounded-full shadow cursor-pointer"
          onClick={safeHandleFindMyLocation}
          title="Find my location"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" 
            />
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" 
            />
          </svg>
        </div>
        
        {/* Refresh map button */}
        <div 
          className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-full shadow cursor-pointer"
          onClick={safeHandleRefreshMap}
          title="Refresh maps"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
            />
          </svg>
        </div>
        
        {/* Toggle zip code boundaries button */}
        <div 
          className={`${showZipCodes ? 'bg-blue-600 hover:bg-blue-500' : 'bg-gray-600 hover:bg-gray-500'} text-white p-2 rounded-full shadow cursor-pointer`}
          onClick={safeToggleZipBoundaries}
          title={showZipCodes ? "Hide ZIP code boundaries" : "Show ZIP code boundaries"}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" 
            />
          </svg>
        </div>
        
        {/* Toggle rotation button */}
        <div 
          className={`${isRotationEnabled ? 'bg-green-600 hover:bg-green-500' : 'bg-gray-600 hover:bg-gray-500'} text-white p-2 rounded-full shadow cursor-pointer`}
          onClick={safeToggleRotation}
          title={isRotationEnabled ? "Disable auto-rotation" : "Enable auto-rotation"}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 4v1m6 11h2m-6 0h-2v4m0-11v-4m6 6h2m-10 0H2" 
            />
          </svg>
        </div>
        
        {/* Reset rotation button (only shown when rotation is enabled) */}
        {isRotationEnabled && (
          <div 
            className="bg-purple-600 hover:bg-purple-500 text-white p-2 rounded-full shadow cursor-pointer"
            onClick={safeResetRotation}
            title="Reset map rotation"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M10 19l-7-7m0 0l7-7m-7 7h18" 
              />
            </svg>
          </div>
        )}
        
        {/* Emergency route clear button (only shown in development) */}
        {process.env.NODE_ENV === 'development' && (
          <div 
            className="bg-red-600 hover:bg-red-500 text-white p-2 rounded-full shadow cursor-pointer"
            onClick={safeClearAllRoutes}
            title="Emergency: Clear all routes"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" 
              />
            </svg>
          </div>
        )}
        
        {/* Clear trail button (only shown when clocked in and has moved) */}
        {isClockedIn && hasMovedSinceClockIn && (
          <div 
            className="bg-red-600 hover:bg-red-500 text-white p-2 rounded-full shadow cursor-pointer"
            onClick={safeClearTrail}
            title="Clear trail"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" 
              />
            </svg>
          </div>
        )}
      </div>
      
      {/* Add Admin Trail Reset Button */}
      {isAdmin && (
        <div className="absolute bottom-4 left-4 z-20">
          <button
            className="bg-red-600 hover:bg-red-500 text-white px-4 py-2 rounded shadow flex items-center"
            onClick={safeHandleResetAllTrails}
            title="Reset all team trails"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4,2a1,1 0 0,1 1,1v2.101a7.002 7.002 0 0,0 11.601 2.566 1,1 0 1,1-1.885.666A5.002 5.002 0 0,1 5.999 7H9a1,1 0 0,1 0 2H4a1,1 0 0,1-1-1V3a1,1 0 0,1 1-1zm.008 9.057a1,1 0 0,1 1.276.61A5.002 5.002 0 0,0 14.001 13H11a1,1 0 1,1 0-2h5a1,1 0 0,1 1 1v5a1,1 0 1,1-2 0v-2.101a7.002 7.002 0 0,1-11.601-2.566 1,1 0 0,1 .61-1.276z" clipRule="evenodd" />
            </svg>
            Reset All Trails
          </button>
        </div>
      )}
      
      {/* Debug panel for rate limiting (only in development mode) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-4 right-4 bg-black bg-opacity-70 text-white p-2 rounded text-xs z-50">
          <div>Cache: {ROUTE_REQUEST_CACHE?.size || 0} routes</div>
          <div>Pending: {PENDING_REQUESTS?.size || 0} requests</div>
          <div>Interval: {MIN_REQUEST_INTERVAL || 0}ms</div>
          {showZipCodes && ZipCodeBoundaryManager && (
            <div>ZIP Loaded: {ZipCodeBoundaryManager.isLoaded ? 'Yes' : 'No'}</div>
          )}
        </div>
      )}
    </>
  );
};

export default MapControls;