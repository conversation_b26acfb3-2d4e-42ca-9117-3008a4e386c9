import React, { useContext, useState, useEffect, createContext } from 'react';
import { collection, doc, getDoc, onSnapshot, where, query, getDocs, setDoc, updateDoc, serverTimestamp, addDoc, deleteDoc } from 'firebase/firestore';
// FIXED IMPORT: Use correct path to firebase.js in pages directory
import { db } from '../pages/firebase.js';
// We'll use crypto-js for password hashing
import CryptoJ<PERSON> from 'crypto-js';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false); // THIS IS WHAT THE VEHICLE URLs BUTTON USES
  const [loading, setLoading] = useState(true);
  // Add userTeams state to store user's team memberships
  const [userTeams, setUserTeams] = useState([]);
  // FIXED: Add flag to track navigation vs actual page close
  const [isNavigating, setIsNavigating] = useState(false);
  // Add clock status state to track if user is clocked in
  const [isClockedIn, setIsClockedIn] = useState(false);

  // ADDED: Track active listeners to clean them up properly
  const [activeListeners, setActiveListeners] = useState([]);

  // Function to hash passwords
  const hashPassword = (password) => {
    return CryptoJS.SHA256(password).toString();
  };

  // ADDED: Safe cleanup function for listeners
  const cleanupListeners = () => {
    activeListeners.forEach(unsubscribe => {
      try {
        if (typeof unsubscribe === 'function') {
          unsubscribe();
        }
      } catch (error) {
        console.error("Error cleaning up listener:", error);
      }
    });
    setActiveListeners([]);
  };

  // ADDED: Function to safely parse JSON from localStorage (minimal usage now)
  const safeJSONParse = (item, fallback = null) => {
    if (!item || item === 'undefined' || item === 'null') {
      return fallback;
    }
    try {
      return JSON.parse(item);
    } catch (e) {
      console.error(`Error parsing JSON for item: ${item}`, e);
      return fallback;
    }
  };

  // ADDED: Function to clear corrupted localStorage
  const clearCorruptedStorage = () => {
    console.log('Clearing corrupted localStorage...');
    localStorage.removeItem('currentUser');
    localStorage.removeItem('isAdmin');
    localStorage.removeItem('isClockedIn');
    localStorage.removeItem('userTeams');
    localStorage.removeItem('preserveTimeCard');
    localStorage.removeItem('hasCompletedInspection');
    // Clear cache versions too
    localStorage.removeItem('currentUser_cache');
    localStorage.removeItem('isAdmin_cache');
    localStorage.removeItem('isClockedIn_cache');
    localStorage.removeItem('userTeams_cache');
  };

  // FIXED: Add timecard checking function with LIVE listeners
  const checkClockStatus = async (userId) => {
    if (!userId) return false;
    
    try {
      console.log(`🔴 LIVE: Setting up timecard listener for ${userId}`);
      
      // Set up LIVE listener for timecard - this is the primary data source
      const timeCardRef = doc(db, 'timeCards', userId);
      
      const unsubscribe = onSnapshot(timeCardRef, (docSnapshot) => {
        try {
          if (docSnapshot.exists()) {
            const timeCardData = docSnapshot.data();
            const today = new Date().toLocaleDateString();
            
            const clockedIn = timeCardData.currentDay === today && 
                          timeCardData.clockedIn && 
                          !timeCardData.clockedOut;
                          
            console.log(`🔴 LIVE Clock status for ${userId}: ${clockedIn ? 'Clocked In' : 'Clocked Out'}`);
            setIsClockedIn(clockedIn);
            
            // Cache for recovery only
            localStorage.setItem('isClockedIn', JSON.stringify(clockedIn));
            if (clockedIn) {
              localStorage.setItem('clockInTime', timeCardData.clockInTime?.toDate?.() || new Date().toISOString());
              // FIXED: Set flag to preserve timecard during navigation
              localStorage.setItem('preserveTimeCard', 'true');
            }
          } else {
            console.log(`🔴 LIVE: No timecard document found for ${userId}`);
            setIsClockedIn(false);
            localStorage.setItem('isClockedIn', 'false');
          }
        } catch (error) {
          console.error("Error processing timecard snapshot:", error);
          setIsClockedIn(false);
        }
      }, (error) => {
        console.error("🔴 LIVE: Timecard listener error:", error);
        // Try to use cached value as fallback
        const cachedStatus = localStorage.getItem('isClockedIn');
        const isClocked = cachedStatus === 'true';
        setIsClockedIn(isClocked);
      });
      
      // Track listener for cleanup
      setActiveListeners(prev => [...prev, unsubscribe]);
      
      return true;
    } catch (error) {
      console.error("Error setting up timecard listener:", error);
      return false;
    }
  };

  // UPDATED: Function to validate team access - improved with multiple checks and better logging
  const hasTeamAccess = async (teamId) => {
    if (!currentUser) return false;
    if (isAdmin) return true; // Admins have access to all teams - IMPORTANT FOR VEHICLE URLs
    
    try {
      console.log(`🔴 LIVE: Checking team access for user ${currentUser.uid} in team ${teamId}`);
      
      // NEW: Check for bypass records first (fastest check)
      const bypassRef = doc(db, 'userBypass', currentUser.uid);
      const bypassDoc = await getDoc(bypassRef);
      
      if (bypassDoc.exists()) {
        const bypassData = bypassDoc.data();
        if (bypassData.bypassTeamCheck === true) {
          console.log("🔴 LIVE: Access granted - Global team bypass enabled");
          return true;
        }
        
        if (bypassData.teams && Array.isArray(bypassData.teams) && 
            bypassData.teams.includes(teamId)) {
          console.log("🔴 LIVE: Access granted - Team in bypass list");
          return true;
        }
      }
      
      // 1. Check the nested collection (preferred structure)
      const nestedMembersRef = collection(db, `teams/${teamId}/teamMembers`);
      const nestedQuery = query(nestedMembersRef, where('userId', '==', currentUser.uid));
      const nestedSnapshot = await getDocs(nestedQuery);
      
      if (!nestedSnapshot.empty) {
        console.log("🔴 LIVE: Access granted - Found in nested collection");
        return true;
      }
      
      // 2. Check the root collection as fallback
      const rootMembersRef = collection(db, 'teamMembers');
      const rootQuery = query(
        rootMembersRef, 
        where('userId', '==', currentUser.uid),
        where('teamId', '==', teamId)
      );
      
      const rootSnapshot = await getDocs(rootQuery);
      if (!rootSnapshot.empty) {
        console.log("🔴 LIVE: Access granted - Found in root collection");
        return true;
      }
      
      // 3. Check if team has a members array directly
      const teamRef = doc(db, 'teams', teamId);
      const teamDoc = await getDoc(teamRef);
      
      if (teamDoc.exists()) {
        const teamData = teamDoc.data();
        if (teamData.members && Array.isArray(teamData.members) && 
            teamData.members.includes(currentUser.uid)) {
          console.log("🔴 LIVE: Access granted - Found in team.members array");
          return true;
        }
        
        // 4. Check if user has a teams array in their profile
        const userProfileRef = doc(db, 'userProfiles', currentUser.uid);
        const profileDoc = await getDoc(userProfileRef);
        
        if (profileDoc.exists()) {
          const profileData = profileDoc.data();
          if (profileData.teams && Array.isArray(profileData.teams) && 
              profileData.teams.includes(teamId)) {
            console.log("🔴 LIVE: Access granted - Found in user profile teams array");
            return true;
          }
        }
      }
      
      // NEW: As a last resort, check if user is in the onlineUsers collection for this team
      try {
        const onlineUsersRef = collection(db, `teams/${teamId}/onlineUsers`);
        const onlineUserQuery = query(onlineUsersRef, where('uid', '==', currentUser.uid));
        const onlineUserSnapshot = await getDocs(onlineUserQuery);
        
        if (!onlineUserSnapshot.empty) {
          console.log("🔴 LIVE: Access granted - Found in team's onlineUsers collection");
          // Also add user to proper team members collection for future checks
          ensureUserInTeam(currentUser.uid, teamId, 'member');
          return true;
        }
      } catch (onlineCheckError) {
        console.warn("Error checking onlineUsers collection:", onlineCheckError);
      }
      
      console.log("🔴 LIVE: Access denied - No team membership found");
      return false;
    } catch (error) {
      console.error("🔴 LIVE: Error validating team access:", error);
      // In case of error, check localStorage for cached team access
      const cachedTeams = localStorage.getItem('userTeams');
      if (cachedTeams && cachedTeams !== 'undefined' && cachedTeams !== 'null') {
        const teams = safeJSONParse(cachedTeams, []);
        if (teams && Array.isArray(teams)) {
          const hasAccess = teams.some(team => team.id === teamId);
          console.log(`🔴 LIVE: Using cached team access (${hasAccess ? 'granted' : 'denied'})`);
          return hasAccess;
        }
      }
      return false;
    }
  };

  // FIXED: Function to ensure user is in team with correct parameter order
  async function ensureUserInTeam(userId, teamId, role = 'member') {
    try {
      if (!userId || !teamId) {
        console.error("Invalid userId or teamId in ensureUserInTeam");
        return false;
      }
      
      console.log(`🔴 LIVE: Ensuring user ${userId} is in team ${teamId}`);
      
      // Check nested collection
      const nestedMembersRef = collection(db, `teams/${teamId}/teamMembers`);
      const nestedQuery = query(nestedMembersRef, where('userId', '==', userId));
      const nestedSnapshot = await getDocs(nestedQuery);
      
      // Add to nested collection if not found
      if (nestedSnapshot.empty) {
        await addDoc(nestedMembersRef, {
          userId: userId,
          addedAt: serverTimestamp(),
          addedBy: "auto-fix",
          role: role
        });
        console.log(`🔴 LIVE: Added user to teams/${teamId}/teamMembers`);
      }
      
      // Check root collection
      const rootMembersRef = collection(db, 'teamMembers');
      const rootQuery = query(
        rootMembersRef, 
        where('userId', '==', userId),
        where('teamId', '==', teamId)
      );
      
      const rootSnapshot = await getDocs(rootQuery);
      
      // Add to root collection if not found
      if (rootSnapshot.empty) {
        await addDoc(rootMembersRef, {
          userId: userId,
          teamId: teamId,
          addedAt: serverTimestamp(),
          addedBy: "auto-fix",
          role: role
        });
        console.log(`🔴 LIVE: Added user to teamMembers root collection`);
      }
      
      // Also make sure team document has updated member count
      const teamRef = doc(db, 'teams', teamId);
      const teamDoc = await getDoc(teamRef);
      
      if (teamDoc.exists()) {
        const memberCount = teamDoc.data().memberCount || 0;
        await updateDoc(teamRef, {
          memberCount: memberCount + 1,
          updatedAt: serverTimestamp()
        });
      }
      
      return true;
    } catch (error) {
      console.error("🔴 LIVE: Error ensuring user in team:", error);
      return false;
    }
  }

  // ADDED: Function to create a bypass record for emergency team access
  const createBypassRecord = async (userId, options = {}) => {
    if (!userId) return false;
    
    try {
      console.log(`🔴 LIVE: Creating bypass record for user ${userId}...`);
      const bypassRef = doc(db, 'userBypass', userId);
      
      await setDoc(bypassRef, {
        userId,
        bypassTimeCheck: options.bypassTimeCheck || false,
        bypassInspectionCheck: options.bypassInspectionCheck || false,
        bypassTeamCheck: options.bypassTeamCheck || true, // Default to true
        teams: options.teams || [],
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        createdBy: options.createdBy || 'system-fix',
        notes: options.notes || 'Auto-created bypass record for team access'
      });
      
      console.log(`🔴 LIVE: Successfully created bypass record for user ${userId}`);
      return true;
    } catch (error) {
      console.error("🔴 LIVE: Error creating bypass record:", error);
      return false;
    }
  };

  // Function to get user's teams - UPDATED to check multiple sources with LIVE data
  const getUserTeams = async () => {
    if (!currentUser) return [];
    
    try {
      console.log(`🔴 LIVE: Fetching teams for user ${currentUser.uid}`);
      let teams = [];
      
      if (isAdmin) {
        // ADMINS CAN SEE ALL TEAMS - CRITICAL FOR VEHICLE URL GENERATOR
        const teamsRef = collection(db, 'teams');
        const teamsSnapshot = await getDocs(teamsRef);
        teams = teamsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        console.log(`🔴 LIVE: Admin user has access to ${teams.length} teams`);
      } else {
        // For regular users, collect teams from multiple sources
        const teamIds = new Set(); // Use Set to avoid duplicates
        
        // 1. Get team memberships from root collection
        const rootMembersRef = collection(db, 'teamMembers');
        const rootQuery = query(rootMembersRef, where('userId', '==', currentUser.uid));
        const rootSnapshot = await getDocs(rootQuery);
        
        rootSnapshot.docs.forEach(doc => {
          const teamId = doc.data().teamId;
          if (teamId) teamIds.add(teamId);
        });
        
        // 2. Check team memberships in nested collections
        const teamsRef = collection(db, 'teams');
        const teamsSnapshot = await getDocs(teamsRef);
        
        for (const teamDoc of teamsSnapshot.docs) {
          const teamId = teamDoc.id;
          
          // Check nested team members collection
          const nestedMembersRef = collection(db, `teams/${teamId}/teamMembers`);
          const nestedQuery = query(nestedMembersRef, where('userId', '==', currentUser.uid));
          const nestedSnapshot = await getDocs(nestedQuery);
          
          if (!nestedSnapshot.empty) {
            teamIds.add(teamId);
          }
          
          // Also check if team has a members array directly
          const teamData = teamDoc.data();
          if (teamData.members && Array.isArray(teamData.members) && 
              teamData.members.includes(currentUser.uid)) {
            teamIds.add(teamId);
          }
        }
        
        // 3. Check user profile for team memberships
        const userProfileRef = doc(db, 'userProfiles', currentUser.uid);
        const profileDoc = await getDoc(userProfileRef);
        
        if (profileDoc.exists()) {
          const profileData = profileDoc.data();
          if (profileData.teams && Array.isArray(profileData.teams)) {
            profileData.teams.forEach(teamId => teamIds.add(teamId));
          }
        }
        
        // 4. Check bypass collection
        const bypassRef = doc(db, 'userBypass', currentUser.uid);
        const bypassDoc = await getDoc(bypassRef);
        
        if (bypassDoc.exists()) {
          const bypassData = bypassDoc.data();
          if (bypassData.teams && Array.isArray(bypassData.teams)) {
            bypassData.teams.forEach(teamId => teamIds.add(teamId));
          }
        }
        
        // Get team details for each team ID
        teams = [];
        for (const teamId of teamIds) {
          const teamRef = doc(db, 'teams', teamId);
          const teamSnap = await getDoc(teamRef);
          
          if (teamSnap.exists()) {
            teams.push({
              id: teamSnap.id,
              ...teamSnap.data()
            });
          }
        }
        
        console.log(`🔴 LIVE: Regular user has access to ${teams.length} teams`);
      }
      
      // Update state and cache in localStorage
      setUserTeams(teams);
      localStorage.setItem('userTeams', JSON.stringify(teams));
      return teams;
    } catch (error) {
      console.error("🔴 LIVE: Error fetching user teams:", error);
      
      // Try to use cached teams if available
      const cachedTeams = localStorage.getItem('userTeams');
      if (cachedTeams && cachedTeams !== 'undefined' && cachedTeams !== 'null') {
        const teams = safeJSONParse(cachedTeams, []);
        if (teams && Array.isArray(teams)) {
          setUserTeams(teams);
          return teams;
        } else {
          // Clear corrupted localStorage
          localStorage.removeItem('userTeams');
          return [];
        }
      }
      
      return [];
    }
  };

  // FIXED: Custom login function that checks Firestore directly and sets up LIVE listeners
  async function login(email, password) {
    try {
      console.log(`🔴 LIVE: Attempting login for ${email}`);
      
      // Clean up any existing listeners first
      cleanupListeners();
      
      // Look up user by email in Firestore
      const usersRef = collection(db, "users");
      const q = query(usersRef, where("email", "==", email));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        throw new Error("Invalid email or password");
      }
      
      // Get the first matching user document
      const userDoc = querySnapshot.docs[0];
      const userData = userDoc.data();
      
      // Check if password matches (compare hashed passwords)
      const hashedPassword = hashPassword(password);
      if (userData.passwordHash !== hashedPassword) {
        throw new Error("Invalid email or password");
      }
      
      // Create a user object similar to Firebase Auth user
      const user = {
        uid: userDoc.id,
        email: userData.email,
        role: userData.role,
        displayName: userData.displayName || "",
        online: true,
        lastLoginAt: new Date().toISOString()
      };
      
      // CRITICAL: Set admin status for Vehicle URLs feature
      const adminStatus = userData.role === 'admin';
      
      // Set the user in state and localStorage for persistence
      setCurrentUser(user);
      setUserRole(userData.role);
      setIsAdmin(adminStatus); // THIS CONTROLS ACCESS TO VEHICLE URLs BUTTON
      localStorage.setItem('currentUser', JSON.stringify(user));
      localStorage.setItem('isAdmin', adminStatus.toString()); // Cache admin status
      
      // Update online status in a separate onlineUsers collection
      await setDoc(doc(db, "onlineUsers", user.uid), {
        uid: user.uid,
        email: user.email,
        role: user.role,
        displayName: user.displayName || "",
        lastSeen: new Date().toISOString(),
        online: true
      });
      
      // FIXED: Check clock status for this user - sets up LIVE listener
      await checkClockStatus(user.uid);
      
      // Set up LIVE listener for role changes
      const userDocRef = doc(db, "users", user.uid);
      const roleUnsubscribe = onSnapshot(userDocRef, (docSnapshot) => {
        if (docSnapshot.exists()) {
          const userData = docSnapshot.data();
          const liveAdminStatus = userData.role === 'admin';
          
          console.log(`🔴 LIVE: Role updated from Firestore. Admin status: ${liveAdminStatus}`);
          
          setUserRole(userData.role);
          setIsAdmin(liveAdminStatus); // UPDATE ADMIN STATUS FOR VEHICLE URLs
          
          // Cache updated admin status
          localStorage.setItem('isAdmin', liveAdminStatus.toString());
          
          // Refresh teams when role changes
          getUserTeams();
        } else {
          setUserRole('user');
          setIsAdmin(false);
          localStorage.setItem('isAdmin', 'false');
        }
      }, (error) => {
        console.error("🔴 LIVE: Role listener error:", error);
        // If listener fails, still use role from login
        setUserRole(userData.role);
        const fallbackAdmin = userData.role === 'admin';
        setIsAdmin(fallbackAdmin);
        localStorage.setItem('isAdmin', fallbackAdmin.toString());
      });
      
      // Track listener for cleanup
      setActiveListeners(prev => [...prev, roleUnsubscribe]);
      
      // Fetch user's teams after login
      await getUserTeams();
      
      console.log(`🔴 LIVE: User logged in. Admin status: ${adminStatus}`);
      return user;
    } catch (error) {
      console.error("🔴 LIVE: Login error:", error);
      throw error;
    }
  }

  // FIXED: Sign out function that preserves clock status if needed
  async function logout(preserveClockStatus = false) {
    try {
      if (currentUser && currentUser.uid) {
        // Update online status to false when logging out
        await setDoc(doc(db, "onlineUsers", currentUser.uid), {
          uid: currentUser.uid,
          lastSeen: new Date().toISOString(),
          online: false
        }, { merge: true });
        
        // If needed, preserve clock status in timecard
        if (preserveClockStatus && isClockedIn) {
          // Flag to indicate we're intentionally signing out but staying clocked in
          localStorage.setItem('preserveTimeCard', 'true');
        } else {
          localStorage.removeItem('preserveTimeCard');
        }
      }
    } catch (error) {
      console.error("Error updating online status:", error);
    } finally {
      // Clean up all listeners
      cleanupListeners();
      
      setCurrentUser(null);
      setUserRole(null);
      setIsAdmin(false); // RESET ADMIN STATUS ON LOGOUT
      setUserTeams([]);
      
      // Clear admin status from localStorage
      localStorage.removeItem('currentUser');
      localStorage.removeItem('isAdmin');
      
      console.log("🔴 LIVE: User logged out and listeners cleaned up");
    }
  }

  // ADMIN BYPASS FUNCTION - CRITICAL FOR TESTING VEHICLE URLs FEATURE
  async function bypassLogin(adminEmail = '<EMAIL>') {
    try {
      console.log("🔴 LIVE: Admin bypass login starting");
      
      // Clean up existing listeners
      cleanupListeners();
      
      // Create a bypass admin user with a consistent ID
      const adminUserId = 'admin-bypass-user';
      const adminUser = {
        uid: adminUserId,
        email: adminEmail,
        role: 'admin',
        displayName: 'Admin Bypass',
        online: true,
        lastLoginAt: new Date().toISOString()
      };
      
      // Set states directly - CRITICAL FOR VEHICLE URLs ACCESS
      setCurrentUser(adminUser);
      setUserRole('admin');
      setIsAdmin(true); // THIS ENABLES THE VEHICLE URLs BUTTON
      
      // Store in localStorage
      localStorage.setItem('currentUser', JSON.stringify(adminUser));
      localStorage.setItem('isAdmin', 'true'); // Cache admin status
      
      // Add this user to the onlineUsers collection for visibility
      await setDoc(doc(db, "onlineUsers", adminUserId), {
        ...adminUser,
        lastSeen: new Date().toISOString()
      });
      
      // Also add/update this user in the regular users collection
      await setDoc(doc(db, "users", adminUserId), {
        email: adminEmail,
        role: 'admin',
        displayName: 'Admin Bypass',
        createdAt: new Date().toISOString(),
        passwordHash: hashPassword('admin') // Dummy password hash
      });
      
      // Create/update user profile for this admin
      await setDoc(doc(db, "userProfiles", adminUserId), {
        displayName: 'Admin Bypass',
        jobTitle: 'System Administrator',
        tags: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      // Fetch all teams for admin
      await getUserTeams();
      
      console.log('🔴 LIVE: Admin bypass login successful - Vehicle URLs button should now be visible');
      return adminUser;
    } catch (error) {
      console.error("Error in bypass login:", error);
      // Even if Firestore operations fail, still set the local state
      const adminUser = {
        uid: 'admin-bypass-user',
        email: adminEmail,
        role: 'admin',
        displayName: 'Admin Bypass',
        online: true
      };
      
      setCurrentUser(adminUser);
      setUserRole('admin');
      setIsAdmin(true); // ENSURE ADMIN STATUS IS SET
      localStorage.setItem('currentUser', JSON.stringify(adminUser));
      localStorage.setItem('isAdmin', 'true');
      
      return adminUser;
    }
  }

  // Function to refresh user role from Firestore
  const refreshUserRole = async () => {
    if (currentUser) {
      // Skip refresh for bypass users
      if (currentUser.uid === 'admin-bypass-user') {
        return;
      }
      
      try {
        console.log(`🔴 LIVE: Refreshing role for user ${currentUser.uid}`);
        const docRef = doc(db, "users", currentUser.uid);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          const userData = docSnap.data();
          const adminStatus = userData.role === 'admin';
          
          setUserRole(userData.role);
          setIsAdmin(adminStatus); // UPDATE ADMIN STATUS FOR VEHICLE URLs
          
          // Cache updated admin status
          localStorage.setItem('isAdmin', adminStatus.toString());
          
          // Also refresh user teams when role changes
          await getUserTeams();
          
          // FIXED: Check clock status again
          await checkClockStatus(currentUser.uid);
          
          console.log(`🔴 LIVE: Role refreshed. Admin status: ${adminStatus}`);
        } else {
          setUserRole('user');
          setIsAdmin(false);
          setUserTeams([]);
          localStorage.setItem('isAdmin', 'false');
        }
      } catch (error) {
        console.error("🔴 LIVE: Error refreshing user role:", error);
        setUserRole('user');
        setIsAdmin(false);
        setUserTeams([]);
        localStorage.setItem('isAdmin', 'false');
      }
    }
  };

  // Debugging function to help identify team access issues
  const debugTeamAccess = async (userId, teamId) => {
    console.log(`🔍 🔴 LIVE: Debugging team access for user ${userId} in team ${teamId}...`);
    
    try {
      // 1. Check nested collection
      console.log(`Checking in teams/${teamId}/teamMembers...`);
      const nestedRef = collection(db, `teams/${teamId}/teamMembers`);
      const nestedDocs = await getDocs(nestedRef);
      console.log(`Found ${nestedDocs.size} total documents`);
      
      // Log all team members
      nestedDocs.forEach(doc => {
        console.log(`Member document: ${doc.id}`, doc.data());
      });
      
      // 2. Check root collection
      console.log(`Checking in teamMembers collection...`);
      const rootRef = collection(db, 'teamMembers');
      const rootDocs = await getDocs(rootRef);
      console.log(`Found ${rootDocs.size} total documents`);
      
      // Log team members that match your team
      const teamMembers = rootDocs.docs.filter(doc => doc.data().teamId === teamId);
      console.log(`Found ${teamMembers.length} members for this team`);
      teamMembers.forEach(doc => {
        console.log(`Team member: ${doc.id}`, doc.data());
      });
      
      // 3. Check if the user is in the team document directly
      console.log(`Checking team document...`);
      const teamRef = doc(db, 'teams', teamId);
      const teamDoc = await getDoc(teamRef);
      if (teamDoc.exists()) {
        console.log('Team document:', teamDoc.data());
        // Check for any members array or similar structure
        if (teamDoc.data().members) {
          console.log('Team has members array:', teamDoc.data().members);
        }
      }

      // 4. Check if user has a bypass record
      console.log(`Checking for bypass record...`);
      const bypassRef = doc(db, 'userBypass', userId);
      const bypassDoc = await getDoc(bypassRef);
      
      if (bypassDoc.exists()) {
        console.log('User has bypass record:', bypassDoc.data());
      } else {
        console.log('No bypass record found');
      }
      
      // 5. Check timecard status
      console.log(`Checking timecard status...`);
      const timeCardRef = doc(db, 'timeCards', userId);
      const timeCardDoc = await getDoc(timeCardRef);
      
      if (timeCardDoc.exists()) {
        const timeCardData = timeCardDoc.data();
        const today = new Date().toLocaleDateString();
        
        const clockedIn = timeCardData.currentDay === today && 
                      timeCardData.clockedIn && 
                      !timeCardData.clockedOut;
                      
        console.log('Timecard status:', { 
          clockedIn, 
          currentDay: timeCardData.currentDay,
          today,
          clockInTime: timeCardData.clockInTime,
          clockOutTime: timeCardData.clockOutTime
        });
      } else {
        console.log('No timecard document found');
      }
      
      // Offer recommendations
      console.log('\n🔧 RECOMMENDATIONS:');
      
      // If not in either collection, suggest adding
      if (nestedDocs.size === 0 && teamMembers.length === 0) {
        console.log('- User is missing from both team collections. Run ensureUserInTeam function to add them.');
      }
      
      // Create a bypass record if needed
      console.log('- Create a bypass record to temporarily override access checks:');
      console.log(`  createBypassRecord('${userId}', { bypassTeamCheck: true, teams: ['${teamId}'] })`);
      
      // Check team name
      if (teamDoc.exists()) {
        console.log(`- Verify user is trying to access the correct team: "${teamDoc.data().name}"`);
      }
      
    } catch (error) {
      console.error('🔴 LIVE: Error debugging team access:', error);
    }
  };

  // FIXED: Add event handler to detect navigation vs. actual page close
  useEffect(() => {
    // Function to detect navigation vs. tab/window close
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // Tab is hidden - might be navigation or closing
        setIsNavigating(true);
        
        // Set a flag in localStorage that we'll check when the page loads again
        localStorage.setItem('wasNavigating', 'true');
      } else if (document.visibilityState === 'visible') {
        // Tab is visible again - was navigation, not closing
        setIsNavigating(false);
        
        // Check if we were navigating before
        const wasNavigating = localStorage.getItem('wasNavigating') === 'true';
        if (wasNavigating) {
          // This was a navigation, not a page reload
          localStorage.removeItem('wasNavigating');
        }
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Effect to check for stored user on load with LIVE listener setup
  useEffect(() => {
    const storedUser = localStorage.getItem('currentUser');
    const cachedAdminStatus = localStorage.getItem('isAdmin') === 'true';
    
    if (storedUser && storedUser !== 'undefined' && storedUser !== 'null') {
      const user = safeJSONParse(storedUser);
      
      if (!user) {
        console.error('🔴 LIVE: Failed to parse stored user, clearing storage');
        clearCorruptedStorage();
        setLoading(false);
        return;
      }
      
      console.log("🔴 LIVE: Restoring user from cache, setting up live listeners");
      setCurrentUser(user);
      
      // Restore admin status from cache - CRITICAL FOR VEHICLE URLs
      if (cachedAdminStatus) {
        setIsAdmin(true);
        console.log('🔴 LIVE: Restored admin status from cache - Vehicle URLs should be available');
      }
      
      // Restore clock status from localStorage if available
      const cachedClockStatus = localStorage.getItem('isClockedIn');
      if (cachedClockStatus && cachedClockStatus !== 'undefined' && cachedClockStatus !== 'null') {
        setIsClockedIn(cachedClockStatus === 'true');
      }
      
      // For bypass users, directly set role without Firestore check
      if (user.uid === 'admin-bypass-user') {
        setUserRole(user.role);
        setIsAdmin(user.role === 'admin'); // ENSURE ADMIN STATUS FOR BYPASS USER
        
        // Update the online status in Firestore for the bypass user
        try {
          setDoc(doc(db, "onlineUsers", user.uid), {
            ...user,
            lastSeen: new Date().toISOString(),
            online: true
          }, { merge: true });
          
          // Fetch teams for admin bypass user
          getUserTeams().then(() => {
            setLoading(false);
          });
        } catch (error) {
          console.error("Error updating bypass user status:", error);
          setLoading(false);
        }
        
        return;
      }
      
      // For regular users, update online status and set up listener for role changes
      try {
        // Update online status
        setDoc(doc(db, "onlineUsers", user.uid), {
          uid: user.uid,
          email: user.email,
          role: user.role || 'user',
          displayName: user.displayName || "",
          lastSeen: new Date().toISOString(),
          online: true
        }, { merge: true });
        
        // Set up LIVE listener for user role
        const userDocRef = doc(db, "users", user.uid);
        const unsubscribeRole = onSnapshot(userDocRef, (docSnapshot) => {
          if (docSnapshot.exists()) {
            const userData = docSnapshot.data();
            const adminStatus = userData.role === 'admin';
            
            console.log(`🔴 LIVE: Role updated from Firestore listener. Admin status: ${adminStatus}`);
            
            setUserRole(userData.role);
            setIsAdmin(adminStatus); // UPDATE ADMIN STATUS FOR VEHICLE URLs
            
            // Cache updated admin status
            localStorage.setItem('isAdmin', adminStatus.toString());
            
            // Refresh teams when role changes
            getUserTeams();
          } else {
            setUserRole('user');
            setIsAdmin(false);
            localStorage.setItem('isAdmin', 'false');
          }
        }, (error) => {
          console.error("🔴 LIVE: Role listener error:", error);
          // If listener fails, still use role from localStorage
          setUserRole(user.role || 'user');
          const fallbackAdmin = (user.role || 'user') === 'admin';
          setIsAdmin(fallbackAdmin);
          localStorage.setItem('isAdmin', fallbackAdmin.toString());
        });
        
        // Track listener for cleanup
        setActiveListeners(prev => [...prev, unsubscribeRole]);
        
        // Set up LIVE listener for timecard
        checkClockStatus(user.uid);
        
        // Fetch teams initially
        getUserTeams().then(() => {
          setLoading(false);
        });
        
      } catch (error) {
        console.error("🔴 LIVE: Error setting up role listener:", error);
        // Fall back to role from localStorage
        setUserRole(user.role || 'user');
        const fallbackAdmin = (user.role || 'user') === 'admin';
        setIsAdmin(fallbackAdmin);
        localStorage.setItem('isAdmin', fallbackAdmin.toString());
        
        // Still try to get teams
        getUserTeams().finally(() => {
          setLoading(false);
        });
      }
    } else {
      setLoading(false);
    }
    
    // Cleanup listeners on unmount
    return () => {
      cleanupListeners();
    };
  }, [isNavigating]);

  // FIXED: Set up modified cleanup function for page unload
  useEffect(() => {
    // This function runs when the page is about to unload
    const handleBeforeUnload = async (event) => {
      if (currentUser && currentUser.uid) {
        // FIXED: Check if we should preserve timecard status
        const preserveTimeCard = localStorage.getItem('preserveTimeCard') === 'true';
        
        if (!preserveTimeCard) {
          try {
            console.log("🔴 LIVE: Setting user offline on unload");
            // Update lastSeen
            await setDoc(doc(db, "onlineUsers", currentUser.uid), {
              uid: currentUser.uid,
              lastSeen: new Date().toISOString(),
              // FIXED: Don't change online status unless explicitly logging out
            }, { merge: true });
          } catch (error) {
            console.error("Error updating status on page unload:", error);
          }
        } else {
          console.log("🔴 LIVE: Preserving timecard on unload - not setting offline");
        }
      }
    };

    // Only add for true page unload events
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      
      // FIXED: Don't set offline status on component unmount
      // This ensures navigation between pages doesn't affect clock status
    };
  }, [currentUser]);

  // THE VALUE OBJECT - INCLUDES ALL NECESSARY EXPORTS FOR VEHICLE URLs FEATURE
  const value = {
    currentUser,
    userRole,
    isAdmin,              // ← THIS IS WHAT THE VEHICLE URLs BUTTON CHECKS (LIVE DATA)
    userTeams,           // ← LIVE FROM FIREBASE
    isClockedIn,         // ← LIVE FROM FIREBASE LISTENER
    login,               // ← SETS UP LIVE LISTENERS
    logout,
    bypassLogin,          // ← USE THIS TO TEST VEHICLE URLs FEATURE
    refreshUserRole,     // ← REFRESHES FROM LIVE DATA
    getUserTeams,         // ← USED BY VEHICLE URL GENERATOR TO GET TEAMS (LIVE DATA)
    hasTeamAccess,       // ← ALWAYS CHECKS LIVE DATA
    debugTeamAccess,     // ← DEBUGGING HELPER
    ensureUserInTeam,    // ← TEAM MANAGEMENT
    createBypassRecord,  // ← EMERGENCY ACCESS
    hashPassword,
    checkClockStatus,    // ← SETS UP LIVE LISTENER
    clearCorruptedStorage, // ← HELPER TO CLEAR CORRUPTED STORAGE
    safeJSONParse         // ← SAFE JSON PARSING HELPER
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}