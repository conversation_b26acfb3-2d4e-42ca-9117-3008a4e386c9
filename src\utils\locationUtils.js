/**
 * Utility functions for location, distance, and mapping operations
 */

/**
 * Calculate distance between two points (returns distance in MILES)
 * 
 * @param {Object} point1 - First point with lat/lng properties
 * @param {Object} point2 - Second point with lat/lng properties
 * @returns {number} Distance in miles
 */
export const calculateDistance = (point1, point2) => {
  if (!point1 || !point2) return 0;
  
  const R = 3958.8; // Earth's radius in MILES
  const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
  const φ2 = point2.lat * Math.PI/180;
  const Δφ = (point2.lat-point1.lat) * Math.PI/180;
  const Δλ = (point2.lng-point1.lng) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  const d = R * c; // in MILES
  return d;
};

/**
 * Find the closest location to a given position
 * 
 * @param {Object} fromPosition - Starting position with lat/lng
 * @param {Array} locationsList - List of location objects with position property
 * @returns {Object} Object containing closest location and distance
 */
export const findClosestLocation = (fromPosition, locationsList) => {
  if (!locationsList || locationsList.length === 0) return null;
  
  let closestLocation = null;
  let shortestDistance = Infinity;
  
  for (const location of locationsList) {
    const distance = calculateDistance(fromPosition, location.position);
    if (distance < shortestDistance) {
      shortestDistance = distance;
      closestLocation = location;
    }
  }
  
  return { location: closestLocation, distance: shortestDistance };
};

/**
 * Optimize route ordering for a list of locations
 * 
 * @param {Object} startPosition - Starting position with lat/lng
 * @param {Array} locationsList - List of location objects to visit
 * @returns {Array} Ordered list of locations with distances
 */
export const optimizeRoute = (startPosition, locationsList) => {
  if (!locationsList || locationsList.length === 0) return [];
  
  const unvisitedLocations = [...locationsList];
  const optimizedRoute = [];
  let currentPosition = startPosition;
  
  while (unvisitedLocations.length > 0) {
    const { location: closestLocation, distance } = findClosestLocation(currentPosition, unvisitedLocations);
    
    if (closestLocation) {
      optimizedRoute.push({
        location: closestLocation,
        distanceFromPrevious: distance
      });
      
      const index = unvisitedLocations.findIndex(loc => loc.id === closestLocation.id);
      if (index !== -1) {
        unvisitedLocations.splice(index, 1);
      }
      currentPosition = closestLocation.position;
    }
  }
  
  return optimizedRoute;
};

/**
 * Calculate bearing between two points (in degrees)
 * 
 * @param {Object} start - Starting point with lat/lng
 * @param {Object} end - Ending point with lat/lng
 * @returns {number} Bearing in degrees (0-360)
 */
export const calculateBearing = (start, end) => {
  const startLat = start.lat * Math.PI / 180;
  const startLng = start.lng * Math.PI / 180;
  const endLat = end.lat * Math.PI / 180;
  const endLng = end.lng * Math.PI / 180;

  const y = Math.sin(endLng - startLng) * Math.cos(endLat);
  const x = Math.cos(startLat) * Math.sin(endLat) -
            Math.sin(startLat) * Math.cos(endLat) * Math.cos(endLng - startLng);
  
  const bearing = Math.atan2(y, x) * 180 / Math.PI;
  return (bearing + 360) % 360; // Normalize to 0-360
};

/**
 * Get direction text from bearing
 * 
 * @param {number} bearing - Bearing in degrees (0-360)
 * @returns {string} Direction as text (e.g., "North", "Southeast")
 */
export const getDirectionText = (bearing) => {
  const directions = ['North', 'Northeast', 'East', 'Southeast', 'South', 'Southwest', 'West', 'Northwest'];
  const index = Math.round(bearing / 45) % 8;
  return directions[index];
};

/**
 * Get direction arrow symbol from bearing
 * 
 * @param {number} bearing - Bearing in degrees (0-360)
 * @returns {string} Direction as arrow emoji
 */
export const getDirectionArrow = (bearing) => {
  // Map bearing to one of 8 cardinal directions with corresponding arrows
  const directions = [
    { min: 337.5, max: 360, arrow: '⬆️' }, // North
    { min: 0, max: 22.5, arrow: '⬆️' },    // North
    { min: 22.5, max: 67.5, arrow: '↗️' },  // Northeast
    { min: 67.5, max: 112.5, arrow: '➡️' }, // East
    { min: 112.5, max: 157.5, arrow: '↘️' }, // Southeast
    { min: 157.5, max: 202.5, arrow: '⬇️' }, // South
    { min: 202.5, max: 247.5, arrow: '↙️' }, // Southwest
    { min: 247.5, max: 292.5, arrow: '⬅️' }, // West
    { min: 292.5, max: 337.5, arrow: '↖️' }  // Northwest
  ];
  
  // Find the direction that matches the bearing
  for (const dir of directions) {
    if ((bearing >= dir.min && bearing < dir.max) || 
        (dir.min > dir.max && (bearing >= dir.min || bearing < dir.max))) {
      return dir.arrow;
    }
  }
  
  return '⬆️'; // Default to North if something goes wrong
};

/**
 * Format distance in human-readable form (miles or feet)
 * 
 * @param {number} miles - Distance in miles
 * @returns {string} Formatted distance string
 */
export const formatDistance = (miles) => {
  if (miles < 0.1) {
    return `${Math.round(miles * 5280)} feet`;
  } else {
    return `${miles.toFixed(1)} miles`;
  }
};

/**
 * Format time in human-readable form
 * 
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time string
 */
export const formatTime = (seconds) => {
  if (seconds < 60) {
    return `${Math.round(seconds)} seconds`;
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)} minutes`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
};

/**
 * Format clock time (hours and minutes)
 * 
 * @param {Date} date - Date object
 * @returns {string} Formatted time string (e.g., "14:05")
 */
export const formatClockTime = (date) => {
  return date ? date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : '';
};

/**
 * Get address from coordinates using Nominatim
 * 
 * @param {Object} position - Position with lat/lng
 * @returns {Promise<Object>} Promise resolving to address data
 */
export const getAddressFromCoordinates = async (position) => {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.lat}&lon=${position.lng}&addressdetails=1`,
      {
        headers: {
          'User-Agent': 'MyMapApp/1.0' // Nominatim requires a user agent
        }
      }
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch address');
    }
    
    const data = await response.json();
    return {
      formatted_address: data.display_name,
      streets: getStreetsFromNominatimResponse(data)
    };
  } catch (error) {
    console.error('Error fetching address:', error);
    return { formatted_address: '', streets: [] };
  }
};

/**
 * Extract street names from Nominatim response
 * 
 * @param {Object} data - Nominatim response data
 * @returns {Array} List of street names
 */
export const getStreetsFromNominatimResponse = (data) => {
  const streets = [];
  
  // Try to get road
  if (data.address && data.address.road) {
    streets.push(data.address.road);
  }
  
  // Try to get nearest roads
  if (data.address) {
    const possibleRoads = ['pedestrian', 'footway', 'path', 'street', 'residential'];
    
    for (const roadType of possibleRoads) {
      if (data.address[roadType] && !streets.includes(data.address[roadType])) {
        streets.push(data.address[roadType]);
      }
    }
  }
  
  return streets;
};

/**
 * Generate a consistent color based on a user ID
 * 
 * @param {string} userId - User identifier
 * @returns {string} Color as hex code
 */
export const getUserColor = (userId) => {
  // List of distinct colors for different users
  const colors = [
    '#FF5555', // Red (default color)
    '#4CAF50', // Green
    '#2196F3', // Blue
    '#FF9800', // Orange
    '#9C27B0', // Purple
    '#00BCD4', // Cyan
    '#FFEB3B', // Yellow
    '#795548', // Brown
    '#009688', // Teal
    '#E91E63', // Pink
    '#673AB7', // Deep Purple
    '#FFC107', // Amber
    '#8BC34A', // Light Green
    '#03A9F4', // Light Blue
    '#FF5722', // Deep Orange
    '#607D8B'  // Blue Grey
  ];
  
  if (!userId) return colors[0]; // Default color
  
  // Create a simple hash from the user ID
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // Use the hash to pick a color
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

/**
 * Check if a user is near a given location
 * 
 * @param {Object} userPosition - User's position with lat/lng
 * @param {Object} locationPosition - Location's position with lat/lng
 * @param {number} maxDistanceFeet - Maximum distance in feet
 * @returns {boolean} Whether user is within the specified distance
 */
export const isNearLocation = (userPosition, locationPosition, maxDistanceFeet = 1000) => {
  if (!userPosition || !locationPosition) return false;
  
  const distanceMiles = calculateDistance(userPosition, locationPosition);
  const distanceFeet = distanceMiles * 5280;
  
  return distanceFeet <= maxDistanceFeet;
};

/**
 * Debounce function to limit frequency of function calls
 * 
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function(...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
};