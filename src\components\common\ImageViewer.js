import React, { useState, useEffect } from 'react';

/**
 * Full-screen image viewer component with gallery navigation
 */
const ImageViewer = ({
  // Image data
  images = [],
  initialIndex = 0,
  
  // Display state
  isOpen,
  
  // Event handlers
  onClose
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isLoading, setIsLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  
  // Reset state when images change or viewer opens
  useEffect(() => {
    if (isOpen) {
      setCurrentIndex(initialIndex);
      setIsLoading(true);
      setImageError(false);
    }
  }, [isOpen, initialIndex, images]);
  
  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          navigateToPrevious();
          break;
        case 'ArrowRight':
          navigateToNext();
          break;
        default:
          break;
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    // Clean up event listener
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, currentIndex, images]);
  
  // Navigate to previous image
  const navigateToPrevious = () => {
    if (images.length <= 1) return;
    
    setIsLoading(true);
    setImageError(false);
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };
  
  // Navigate to next image
  const navigateToNext = () => {
    if (images.length <= 1) return;
    
    setIsLoading(true);
    setImageError(false);
    setCurrentIndex((prevIndex) => 
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  };
  
  // Handle image load success
  const handleImageLoad = () => {
    setIsLoading(false);
    setImageError(false);
  };
  
  // Handle image load error
  const handleImageError = () => {
    setIsLoading(false);
    setImageError(true);
  };
  
  // Don't render if not open
  if (!isOpen || !images || images.length === 0) return null;
  
  const currentImage = images[currentIndex];
  
  return (
    <div 
      className="fixed inset-0 z-[2000] bg-black bg-opacity-90 flex items-center justify-center"
      onClick={onClose} // Close when clicking background
      role="dialog"
      aria-modal="true"
      aria-label="Image viewer"
    >
      <div className="relative w-full h-full flex flex-col items-center justify-center">
        {/* Close button */}
        <button 
          className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 z-10 hover:bg-opacity-70 transition-opacity"
          onClick={onClose}
          aria-label="Close image viewer"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        {/* Image counter */}
        {images.length > 1 && (
          <div className="absolute top-4 left-4 text-white bg-black bg-opacity-50 px-3 py-1 rounded-full text-sm">
            {currentIndex + 1} / {images.length}
          </div>
        )}
        
        {/* Main image container */}
        <div 
          className="relative w-full h-full flex items-center justify-center p-4"
          onClick={(e) => e.stopPropagation()} // Prevent closing when clicking the image
        >
          {/* Loading indicator */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-white border-t-transparent"></div>
            </div>
          )}
          
          {/* Error message */}
          {imageError && (
            <div className="text-red-400 bg-red-900 bg-opacity-30 p-4 rounded-md">
              Failed to load image. The image may be missing or corrupted.
            </div>
          )}
          
          {/* Image */}
          <img
            src={currentImage}
            alt={`Image ${currentIndex + 1}`}
            className={`max-w-full max-h-full object-contain transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
          
          {/* Navigation arrows - only show if more than one image */}
          {images.length > 1 && (
            <>
              <button 
                className="absolute left-4 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 focus:outline-none transition-opacity"
                onClick={(e) => {
                  e.stopPropagation();
                  navigateToPrevious();
                }}
                aria-label="Previous image"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              <button 
                className="absolute right-4 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 focus:outline-none transition-opacity"
                onClick={(e) => {
                  e.stopPropagation();
                  navigateToNext();
                }}
                aria-label="Next image"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImageViewer;