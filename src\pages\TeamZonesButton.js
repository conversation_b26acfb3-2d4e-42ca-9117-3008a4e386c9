import React, { useState, useEffect, useCallback } from 'react';
import TeamZones from './TeamZones';
import ZipCodeDebugTool from './ZipCodeDebugTool'; // Import the debug tool if available

function TeamZonesButton({ 
  teamId, 
  isAdmin, 
  currentUser, 
  mapRef, 
  userDisplayNames, 
  userProfilePictures, 
  teamMembers 
}) {
  const [showZonesPanel, setShowZonesPanel] = useState(false);
  const [newZoneId, setNewZoneId] = useState(null);
  const [isReshapeMode, setIsReshapeMode] = useState(false);
  const [showOpacityControl, setShowOpacityControl] = useState(false);
  const [globalZoneOpacity, setGlobalZoneOpacity] = useState(0.3); // Default opacity
  const [showZipBoundaries, setShowZipBoundaries] = useState(true); // Default to showing ZIP boundaries
  const [showDebugTool, setShowDebugTool] = useState(false);
  const firstLoadRef = React.useRef(true);
  
  // NEW: State for storing multiple state GeoJSON paths
  const [stateGeoJsonPaths, setStateGeoJsonPaths] = useState({
    il: '/data/il-zip-codes.geojson', // Default Illinois path
    in: '/data/in-zip-codes.geojson', // Default Indiana path
    wi: '/data/wi-zip-codes.geojson' // Default Wisconsin path
  });
  
  // Handle reshape mode changes from TeamZones component
  const handleReshapeModeChange = useCallback((isReshaping) => {
    setIsReshapeMode(isReshaping);
    
    // If entering reshape mode, hide the panel completely
    if (isReshaping) {
      setShowZonesPanel(false);
      setShowOpacityControl(false);
    } else {
      // If exiting reshape mode and panel was previously showing, show it again
      setShowZonesPanel(true);
    }
  }, []);
  
  // Handle global opacity change
  const handleGlobalOpacityChange = useCallback((opacity) => {
    setGlobalZoneOpacity(opacity);
    
    // Apply opacity to all zone elements on the map
    if (mapRef.current) {
      // Find all zone elements and update their opacity
      const zoneElements = document.querySelectorAll('.zone-rectangle, .zip-boundary');
      zoneElements.forEach(el => {
        el.style.fillOpacity = opacity;
      });
      
      // Store preference in localStorage
      try {
        localStorage.setItem('globalZoneOpacity', opacity.toString());
      } catch (e) {
        console.warn('Could not save opacity preference', e);
      }
      
      // Trigger refresh of zones to apply new opacity
      if (typeof window !== 'undefined') {
        // Refresh both team zones and ZIP boundaries
        if (window.refreshTeamZones) {
          setTimeout(() => {
            window.refreshTeamZones();
          }, 50);
        }
        
        if (window.refreshBoundaries) {
          setTimeout(() => {
            window.refreshBoundaries();
          }, 100);
        }
      }
    }
  }, [mapRef]);
  
  // NEW: Function to update state GeoJSON paths
  const updateStateGeoJsonPaths = useCallback((newPaths) => {
    setStateGeoJsonPaths(prev => ({
      ...prev,
      ...newPaths
    }));
    
    // Also update the global window property if available
    if (typeof window !== 'undefined') {
      window.stateGeoJsonPaths = {
        ...(window.stateGeoJsonPaths || {}),
        ...newPaths
      };
      
      // Trigger a refresh of boundaries
      if (window.refreshBoundaries) {
        setTimeout(() => window.refreshBoundaries(), 100);
      }
    }
    
    // Save to localStorage for persistence
    try {
      localStorage.setItem('stateGeoJsonPaths', JSON.stringify({
        ...stateGeoJsonPaths,
        ...newPaths
      }));
    } catch (e) {
      console.warn('Could not save state GeoJSON paths to localStorage', e);
    }
  }, [stateGeoJsonPaths]);
  
  // NEW: Load state GeoJSON paths from localStorage on mount
  useEffect(() => {
    try {
      const savedPaths = localStorage.getItem('stateGeoJsonPaths');
      if (savedPaths) {
        const parsedPaths = JSON.parse(savedPaths);
        setStateGeoJsonPaths(parsedPaths);
        
        // Also update the global window property
        if (typeof window !== 'undefined') {
          window.stateGeoJsonPaths = parsedPaths;
        }
      }
    } catch (e) {
      console.warn('Could not load state GeoJSON paths from localStorage', e);
    }
  }, []);
  
  // NEW: Listen for global state path updates
  useEffect(() => {
    const handleStatePathsUpdate = (event) => {
      if (event.detail && event.detail.paths) {
        setStateGeoJsonPaths(event.detail.paths);
      }
    };
    
    if (typeof window !== 'undefined') {
      window.addEventListener('statepathsupdate', handleStatePathsUpdate);
      
      // Expose updateStateGeoJsonPaths globally
      window.updateStateGeoJsonPaths = updateStateGeoJsonPaths;
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('statepathsupdate', handleStatePathsUpdate);
      }
    };
  }, [updateStateGeoJsonPaths]);
  
  // Load saved opacity preference on mount
  useEffect(() => {
    try {
      const savedOpacity = localStorage.getItem('globalZoneOpacity');
      if (savedOpacity) {
        setGlobalZoneOpacity(parseFloat(savedOpacity));
        
        // Apply the opacity immediately when the component mounts
        setTimeout(() => {
          handleGlobalOpacityChange(parseFloat(savedOpacity));
        }, 1000); // Delay to ensure zones are rendered
      }
    } catch (e) {
      console.warn('Could not load opacity preference', e);
    }
  }, [handleGlobalOpacityChange]);
  
  // Navigate to a specific zone
  const handleNavigateToZone = useCallback((zone) => {
    console.log("Navigating to zone:", zone.id);
    // Add any navigation logic here if needed
  }, []);
  
  // CRITICAL: Force initial load of zones when map is ready
  useEffect(() => {
    if (mapRef.current && firstLoadRef.current) {
      console.log("INITIAL LOAD: Forcing zone display on map initialization");
      firstLoadRef.current = false;
      
      // Add a slight delay to ensure map is fully initialized
      const timeoutId = setTimeout(() => {
        if (typeof window !== 'undefined') {
          // Make state paths available globally
          window.stateGeoJsonPaths = stateGeoJsonPaths;
          
          if (window.refreshTeamZones) {
            console.log("AUTO-LOADING ZONES: Map ready, forcing zones display");
            window.refreshTeamZones();
          }
          
          // Also load ZIP code boundaries
          if (window.refreshBoundaries) {
            console.log("AUTO-LOADING ZIP BOUNDARIES: Map ready, forcing boundaries display");
            window.refreshBoundaries();
          }
          
          // Try again after a bit longer to be extra sure
          setTimeout(() => {
            if (window.refreshTeamZones) window.refreshTeamZones();
            if (window.refreshBoundaries) window.refreshBoundaries();
          }, 2000);
        }
      }, 500);
      
      return () => clearTimeout(timeoutId);
    }
  }, [mapRef, stateGeoJsonPaths]);
  
  // Effect to trigger zone refresh when panel visibility changes
  useEffect(() => {
    // When the zones panel is shown or hidden, refresh zones on map
    if (typeof window !== 'undefined') {
      console.log("Panel visibility changed - refreshing team zones and ZIP boundaries");
      if (window.refreshTeamZones) window.refreshTeamZones();
      if (window.refreshBoundaries) window.refreshBoundaries();
    }
  }, [showZonesPanel]);
  
  // Effect to handle escape key to close panel (unless in reshape mode)
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        if (showZonesPanel && !isReshapeMode) {
          setShowZonesPanel(false);
        }
        if (showOpacityControl) {
          setShowOpacityControl(false);
        }
        if (showDebugTool) {
          setShowDebugTool(false);
        }
      }
    };
    
    window.addEventListener('keydown', handleEscKey);
    return () => window.removeEventListener('keydown', handleEscKey);
  }, [showZonesPanel, showOpacityControl, isReshapeMode, showDebugTool]);
  
  // Toggle the opacity control
  const toggleOpacityControl = (e) => {
    e.stopPropagation();
    setShowOpacityControl(!showOpacityControl);
  };

  // Toggle ZIP code boundaries
  const toggleZipBoundaries = (e) => {
    e.stopPropagation();
    setShowZipBoundaries(!showZipBoundaries);
    
    // Call the global function if available
    if (typeof window !== 'undefined' && window.toggleZipBoundaries) {
      window.toggleZipBoundaries();
    }
  };
  
  // NEW: Toggle debug tool
  const toggleDebugTool = (e) => {
    if (e) e.stopPropagation();
    
    if (typeof window !== 'undefined') {
      if (window.showZipCodeDebugTool && !showDebugTool) {
        window.showZipCodeDebugTool();
      } else if (window.closeDebugTool && showDebugTool) {
        window.closeDebugTool();
      }
    }
    
    setShowDebugTool(!showDebugTool);
  };
  
  return (
    <>
      <div className="flex items-center">
        {/* Button to show zones panel */}
        <button
          onClick={() => {
            // Don't toggle panel if in reshape mode
            if (!isReshapeMode) {
              setShowZonesPanel(!showZonesPanel);
              
              // Hide opacity control when toggling panel
              if (showOpacityControl) {
                setShowOpacityControl(false);
              }
            }
          }}
          className={`flex items-center ${isReshapeMode ? 'bg-green-700' : 'bg-gray-800 hover:bg-gray-700'} text-white px-2 py-1 rounded-l-lg shadow-sm transition-colors`}
          title={isReshapeMode ? "Editing Zone Shape" : "Team Zones"}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5z" />
            <path d="M11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
          </svg>
          <span className="ml-1 hidden md:inline">
            {isReshapeMode ? "Editing Zone" : "Zones"}
          </span>
          {isReshapeMode && (
            <span className="animate-pulse ml-1">
              <span className="inline-block w-2 h-2 bg-green-400 rounded-full"></span>
            </span>
          )}
        </button>
        
        {/* ZIP Boundaries toggle button */}
        <button
          onClick={toggleZipBoundaries}
          className={`flex items-center ${showZipBoundaries ? 'bg-indigo-600' : 'bg-gray-800 hover:bg-gray-700'} text-white px-2 py-1 border-l border-gray-600`}
          title={showZipBoundaries ? "Hide ZIP Boundaries" : "Show ZIP Boundaries"}
          disabled={isReshapeMode}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z" clipRule="evenodd" />
          </svg>
        </button>
        
        {/* Opacity control button */}
        <button
          onClick={toggleOpacityControl}
          className={`flex items-center ${showOpacityControl ? 'bg-blue-600' : 'bg-gray-800 hover:bg-gray-700'} text-white px-2 py-1 border-l border-gray-600`}
          title="Adjust Zone Transparency"
          disabled={isReshapeMode}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" style={{ opacity: 0.5 }}>
            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
          </svg>
        </button>
        
        {/* NEW: Debug button - for admins only */}
        {isAdmin && (
          <button
            onClick={toggleDebugTool}
            className={`flex items-center ${showDebugTool ? 'bg-red-600' : 'bg-gray-800 hover:bg-gray-700'} text-white px-2 py-1 rounded-r-lg border-l border-gray-600`}
            title="ZIP Code Debug Tool"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>
      
      {/* Transparency controls popup */}
      {showOpacityControl && (
        <div className="absolute mt-2 p-3 bg-gray-800 rounded-md shadow-lg border border-gray-700 z-30">
          <div className="w-48">
            <label className="block text-sm font-medium text-white mb-1">
              Zone Transparency
            </label>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-400">Solid</span>
              <input
                type="range"
                min="0.1"
                max="0.9"
                step="0.1"
                value={globalZoneOpacity}
                onChange={(e) => handleGlobalOpacityChange(parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <span className="text-xs text-gray-400">Clear</span>
            </div>
            <div className="mt-1 flex justify-between items-center">
              <div 
                className="w-6 h-6 rounded" 
                style={{ 
                  backgroundColor: '#3b82f6', 
                  opacity: globalZoneOpacity 
                }}
              ></div>
              <span className="text-xs text-white">{Math.round(globalZoneOpacity * 100)}%</span>
            </div>
          </div>
        </div>
      )}
      
      {/* TeamZones component is always mounted for team zones */}
      <TeamZones
        teamId={teamId}
        isAdmin={isAdmin}
        currentUser={currentUser}
        mapRef={mapRef}
        userDisplayNames={userDisplayNames}
        userProfilePictures={userProfilePictures}
        visible={showZonesPanel} // Controls UI visibility based on panel state
        onClose={() => setShowZonesPanel(false)}
        zoomToZone={newZoneId}
        alwaysShowZones={true} // CRITICAL: Always keep zones visible on map regardless of UI state
        onNavigateToZone={handleNavigateToZone}
        onReshapeModeChange={handleReshapeModeChange}
        defaultOpacity={globalZoneOpacity} // Pass the global opacity setting to TeamZones
        showZipBoundaries={showZipBoundaries} // Pass the ZIP boundaries visibility state
        teamMembers={teamMembers} // Pass team members for ZIP code assignments
        
        // FIXED: Pass the state-specific GeoJSON paths instead of a single path
        stateGeoJsonPaths={stateGeoJsonPaths} // Pass all state paths
        // Still include the original prop for backward compatibility
        geoJsonPath={stateGeoJsonPaths.il} // Default to Illinois path for backward compatibility
      />
      
      {/* Render the debug tool container if needed */}
      {isAdmin && (
        <div id="zip-code-debug-tool"></div>
      )}
    </>
  );
}

export default TeamZonesButton;