// Main index file to export all components
export { default as OrderForm } from './OrderForm';
export { default as OrderEditForm } from './OrderEditForm';
export { default as OrderCard } from './OrderCard';
export { default as AddressComponent } from './AddressComponent';
export { default as Orders } from './Orders';

// Export UI components
export { 
  StatusBadge, 
  ConfirmationDialog, 
  VehicleRender, 
  OrderStats,
  Toolbar,
  Alert,
  LoadingSpinner,
  NoOrdersMessage,
  CoordinateEditor
} from './ui-components';

// Export utility functions for reuse
export {
  STATUS_OPTIONS,
  STATUS_MAPPING,
  COLORS_MAP,
  calculateDaysPastDue,
  calculateDaysSinceLastCheckIn,
  formatDate,
  formatAddress,
  formatUserDisplayName,
  processAddresses,
  determineDriveType,
  geocodeAddress,
  fetchCarDataAndGenerateImage,
  generateFallbackCarImage,
  generateMultipleCarViews,
  getFallbackImageUrl,
  prepareOrderData
} from './utility-functions';