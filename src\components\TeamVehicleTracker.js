// TeamVehicleTracker.js - UPDATED: Camera Car Order Support + Vehicle Selection from Map + Recovery Modal Integration + Photo Upload Fix

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { collection, getDocs, doc, getDoc, updateDoc, serverTimestamp, addDoc, setDoc, query, orderBy, onSnapshot, deleteDoc, where, limit } from 'firebase/firestore';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Import utilities
import {
  calculateElapsedTime,
  getTimerDisplay,
  getNotificationColor,
  playNotificationSound,
  playMoneySound,
  playReminderSound,
  navigateToAddress
} from './VehicleTrackerUtils';

// Import Firebase operations
import {
  markVINAsSecuredAcrossTeam,
  loadNeverSecuredVehicles,
  markTeamVehicleBottomStatus,
  recheckTeamVehicle
} from './VehicleTrackerFirebase';

// Import Slack functions from slackIntegration.js
import { postVehicleToSlack, formatVehicleForSlack } from './slackIntegration';

// Import Recovery component
import Recovery from '../pages/Recovery';

// Build full address helper - same as in StandaloneVehicleTracker
const buildFullAddress = (address, city, state, zipCode) => {
  const parts = [];
  if (address) parts.push(address);
  if (city) parts.push(city);
  if (state) parts.push(state);
  if (zipCode) parts.push(zipCode);
  return parts.join(', ');
};

// Driver type detection keywords
const DRIVER_TYPE_KEYWORDS = {
  tow: ['tow', 'truck', 'wrecker', 'flatbed', 'rollback', 'recovery', 'towing'],
  camera: ['camera', 'spot', 'car', 'surveillance', 'scout', 'spotter']
};

// Order status mapping
const ORDER_STATUS_MAPPING = {
  'open': { label: 'Open Order', class: 'open' },
  'open-order': { label: 'Open Order', class: 'open' },
  'pending': { label: 'Pending', class: 'pending' },
  'secure': { label: 'Secured', class: 'secure' },
  'secured': { label: 'Secured', class: 'secure' },
  'pending-pickup': { label: 'Pending Pickup', class: 'pending' },
  'awaiting-pickup': { label: 'Awaiting Pickup', class: 'pending' },
  'claim': { label: 'Claim', class: 'claim' },
  'restricted': { label: 'Restricted', class: 'restricted' },
  'not-secure': { label: 'Not Secure', class: 'open' },
  'unsecure': { label: 'Unsecure', class: 'open' },
  '': { label: 'No Status', class: 'open' },
  undefined: { label: 'No Status', class: 'open' },
  null: { label: 'No Status', class: 'open' }
};

// Check if order should be shown on tracker (filter by team and status)
const shouldShowOrderOnTracker = (order, currentTeamId) => {
  // Filter by team first
  if (!order.teamId || order.teamId !== currentTeamId) {
    return false;
  }

  const status = (order.status || '').toLowerCase();
  const secure = order.secure;
  
  // If secure is explicitly true, don't show it
  if (secure === true) {
    return false;
  }

  // Only hide if status is explicitly 'secure' or 'secured' AND secure is not false
  const hideStatuses = ['secure', 'secured'];
  
  if (hideStatuses.includes(status) && secure !== false) {
    return false;
  }

  return true;
};

// Camera Car Action Modal Component
const CameraCarActionModal = ({ 
  order, 
  actionType, 
  onClose, 
  onSubmit, 
  currentUser,
  db,
  team 
}) => {
  const [notes, setNotes] = useState('');
  const [vinVerified, setVinVerified] = useState(false);
  const [photos, setPhotos] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef(null);
  const cameraInputRef = useRef(null);

  const actionTitles = {
    located: 'Vehicle Located',
    not_present: 'Vehicle Not Currently Present',
    blocked: 'Vehicle Blocked In'
  };

  const actionEmojis = {
    located: '✅',
    not_present: '⏰',
    blocked: '🚧'
  };

  // Handle photo upload
  const handlePhotoUpload = (event) => {
    const files = Array.from(event.target.files);
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPhotos(prev => [...prev, {
            file,
            url: e.target.result,
            name: file.name
          }]);
        };
        reader.readAsDataURL(file);
      }
    });
  };

  // Handle camera capture
  const handleCameraCapture = (event) => {
    const files = Array.from(event.target.files);
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPhotos(prev => [...prev, {
            file,
            url: e.target.result,
            name: `camera_${Date.now()}.jpg`
          }]);
        };
        reader.readAsDataURL(file);
      }
    });
  };

  // Remove photo
  const removePhoto = (index) => {
    setPhotos(prev => prev.filter((_, i) => i !== index));
  };

  // Handle submit
  const handleSubmit = async () => {
    if (!notes.trim()) {
      alert('Please add notes about your check-in');
      return;
    }

    if (actionType === 'located' && photos.length === 0) {
      if (!confirm('No photos added. Are you sure you want to continue without photos?')) {
        return;
      }
    }

    setIsSubmitting(true);

    try {
      await onSubmit({
        actionType,
        notes: notes.trim(),
        vinVerified: actionType === 'located' ? vinVerified : false,
        photos,
        timestamp: new Date(),
        userId: currentUser.id,
        userName: currentUser.displayName || currentUser.email?.split('@')[0] || 'Camera Car Driver'
      });

      onClose();
    } catch (error) {
      console.error('Error submitting camera car action:', error);
      alert('Error submitting action. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 z-[60] flex items-center justify-center p-4" onClick={onClose}>
      <div className="bg-gray-900 rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto border-2 border-blue-600 shadow-2xl" onClick={(e) => e.stopPropagation()}>
        <div className="p-4 border-b border-gray-700 bg-gradient-to-r from-gray-900 to-gray-800 rounded-t-xl">
          <div className="flex justify-between items-center">
            <div className="text-xl font-bold text-blue-400">
              {actionEmojis[actionType]} {actionTitles[actionType]}
            </div>
            <button onClick={onClose} className="text-gray-400 hover:text-white text-2xl">
              ×
            </button>
          </div>
        </div>

        <div className="p-4">
          {/* Vehicle Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="bg-gray-800 rounded-lg p-3 border border-gray-600">
              <div className="text-gray-400 text-sm">Vehicle</div>
              <div className="text-white font-bold">{order.year} {order.make} {order.model}</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-3 border border-gray-600">
              <div className="text-gray-400 text-sm">VIN</div>
              <div className="text-blue-400 font-mono text-sm">{order.vin}</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-3 border border-gray-600">
              <div className="text-gray-400 text-sm">Current Status</div>
              <div className="text-white">{ORDER_STATUS_MAPPING[order.status]?.label || order.status || 'Unknown'}</div>
            </div>
            {order.licensePlate && (
              <div className="bg-gray-800 rounded-lg p-3 border border-gray-600">
                <div className="text-gray-400 text-sm">License Plate</div>
                <div className="text-yellow-400 font-bold">{order.licensePlate}</div>
              </div>
            )}
          </div>

          {/* VIN Verification - Only for located vehicles */}
          {actionType === 'located' && (
            <div className="bg-green-900 bg-opacity-30 rounded-lg p-4 mb-6 border border-green-600">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={vinVerified}
                  onChange={(e) => setVinVerified(e.target.checked)}
                  className="w-5 h-5 text-green-600 bg-gray-800 border-gray-600 rounded focus:ring-green-500 focus:ring-2 mr-3"
                />
                <span className="text-green-300 font-semibold">
                  ✅ VIN has been physically verified on the vehicle
                </span>
              </label>
            </div>
          )}

          {/* Notes */}
          <div className="mb-6">
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              📝 Notes about this check-in (Required)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="w-full min-h-[100px] p-3 border-2 border-gray-600 rounded-lg bg-gray-800 text-white focus:border-blue-500 focus:outline-none resize-vertical"
              placeholder={
                actionType === 'located' 
                  ? 'Describe vehicle condition, location details, accessibility for tow truck, etc.'
                  : actionType === 'not_present'
                  ? 'Describe what you found - empty parking spot, different vehicle, etc.'
                  : 'Describe what is blocking the vehicle and any potential solutions'
              }
              required
            />
          </div>

          {/* Photo Section */}
          <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-600">
            <h4 className="text-gray-300 font-semibold mb-3">
              📸 Photos {actionType === 'located' && '(Recommended)'}
            </h4>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
              <button
                type="button"
                className="bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold transition-all flex items-center justify-center"
                onClick={() => cameraInputRef.current?.click()}
                disabled={isSubmitting}
              >
                📷 Take Photo
              </button>
              <button
                type="button"
                className="bg-purple-600 hover:bg-purple-700 text-white py-3 px-4 rounded-lg font-semibold transition-all flex items-center justify-center"
                onClick={() => fileInputRef.current?.click()}
                disabled={isSubmitting}
              >
                📁 Upload Photo
              </button>
            </div>

            {/* Hidden file inputs */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handlePhotoUpload}
              style={{ display: 'none' }}
            />
            <input
              ref={cameraInputRef}
              type="file"
              accept="image/*"
              capture="environment"
              multiple
              onChange={handleCameraCapture}
              style={{ display: 'none' }}
            />

            {/* Photo previews */}
            {photos.length > 0 && (
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {photos.map((photo, index) => (
                  <div key={index} className="relative aspect-square">
                    <img 
                      src={photo.url} 
                      alt={`Photo ${index + 1}`}
                      className="w-full h-full object-cover rounded-lg border border-gray-600"
                    />
                    <button
                      type="button"
                      className="absolute -top-2 -right-2 w-6 h-6 bg-red-600 hover:bg-red-700 text-white rounded-full text-sm font-bold flex items-center justify-center"
                      onClick={() => removePhoto(index)}
                      disabled={isSubmitting}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <button
              type="button"
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 px-4 rounded-lg font-bold transition-all flex items-center justify-center"
              onClick={handleSubmit}
              disabled={isSubmitting || !notes.trim()}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Submitting...
                </>
              ) : (
                <>
                  {actionEmojis[actionType]} Submit {actionTitles[actionType]}
                </>
              )}
            </button>
            <button
              type="button"
              className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-700 text-white py-3 px-4 rounded-lg font-bold transition-all"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

function TeamVehicleTracker({ 
  db, 
  user, 
  team, 
  selectedWeek, 
  vehicles, 
  setVehicles,
  canSecureVehicles,
  soundEnabled,
  updateVehicleWeekStats,
  showImageInModal,
  teamSyncNotifications,
  setTeamSyncNotifications,
  setTeamVehicles: onTeamVehiclesUpdate,
  selectedVehicleId,  // Vehicle ID selected from map
  onVehicleSelect,    // Callback when vehicle is selected (for highlighting on map)
  orders = [],        // NEW: Orders from parent component
  userProfile,        // NEW: User profile for driver type detection
  selectedOrderId,    // NEW: Order ID selected from map
  onOrderSelect       // NEW: Callback when order is selected
}) {
  // Team vehicles state
  const [teamVehicles, setTeamVehicles] = useState([]);
  const [loadingTeamVehicles, setLoadingTeamVehicles] = useState(false);
  const [isFlashingTeamCount, setIsFlashingTeamCount] = useState(false);

  // NEW: Orders state
  const [teamOrders, setTeamOrders] = useState([]);
  const [loadingOrders, setLoadingOrders] = useState(false);
  const [orderCheckIns, setOrderCheckIns] = useState({});

  // NEW: Camera car action modal state
  const [showCameraAction, setShowCameraAction] = useState(false);
  const [cameraActionType, setCameraActionType] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);

  // NEW: Recovery modal state
  const [showRecoveryModal, setShowRecoveryModal] = useState(false);
  const [recoveryVehicle, setRecoveryVehicle] = useState(null);

  // Single card navigation state
  const [currentVehicleIndex, setCurrentVehicleIndex] = useState(0);
  const [currentOrderIndex, setCurrentOrderIndex] = useState(0);
  const [activeTab, setActiveTab] = useState('vehicles'); // 'vehicles' or 'orders'

  // Never secured vehicles state
  const [neverSecuredVehicles, setNeverSecuredVehicles] = useState([]);
  const [showNeverSecured, setShowNeverSecured] = useState(false);

  // Connection status state
  const [connectionStatus, setConnectionStatus] = useState('connected');
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  // Team vehicle timers and listeners
  const [teamVehicleTimers, setTeamVehicleTimers] = useState({});
  const teamVehicleTimerRefs = useRef({});
  const teamVehicleListenersRef = useRef([]);
  const lastTeamVehicleCount = useRef(0);

  // Track initial load state to prevent posting existing vehicles to Slack
  const isInitialLoadRef = useRef(true);
  const initialLoadCompleteRef = useRef(false);

  // Live timer update state
  const [currentTime, setCurrentTime] = useState(Date.now());

  // Use a persistent ref to store reminder states
  const vehicleReminderStatesRef = useRef({});

  // Bottom status vehicles and daily reminders
  const bottomStatusVehicles = useRef({});
  const [lastDailyReminder, setLastDailyReminder] = useState(null);
  const dailyReminderSentRef = useRef({});

  // Geocoding cache
  const geocodingCache = useRef({});
  const geocodingInProgress = useRef({});

  // Can't Secure reason modal state
  const [showCantSecureModal, setShowCantSecureModal] = useState(false);
  const [cantSecureVehicle, setCantSecureVehicle] = useState(null);
  const [cantSecureReason, setCantSecureReason] = useState('');

  // Delete vehicle confirmation modal state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [vehicleToDelete, setVehicleToDelete] = useState(null);
  const [deleteReason, setDeleteReason] = useState('');

  // Map selection state
  const [mapSelectedNotification, setMapSelectedNotification] = useState(null);

  // NEW: Driver type detection
  const isCameraCarDriver = useMemo(() => {
    if (!userProfile || !user) return false;
    
    // Check driver type
    if (userProfile.driverType === 'camera') return true;
    
    // Check tags for camera car indicators
    if (userProfile.tags && Array.isArray(userProfile.tags)) {
      const hasCarTag = userProfile.tags.some(tag => 
        DRIVER_TYPE_KEYWORDS.camera.some(keyword => 
          (tag.name || '').toLowerCase().includes(keyword)
        )
      );
      if (hasCarTag) return true;
    }
    
    // Check job title
    const jobTitle = (userProfile.jobTitle || '').toLowerCase();
    if (DRIVER_TYPE_KEYWORDS.camera.some(keyword => jobTitle.includes(keyword))) {
      return true;
    }
    
    // Default assume camera car if not explicitly tow truck
    const isTowTruck = userProfile.driverType === 'tow' ||
      (userProfile.tags && Array.isArray(userProfile.tags) && 
       userProfile.tags.some(tag => 
         DRIVER_TYPE_KEYWORDS.tow.some(keyword => 
           (tag.name || '').toLowerCase().includes(keyword)
         )
       ));
    
    return !isTowTruck;
  }, [userProfile, user]);

  // NEW: Filter orders for current team
  const filteredTeamOrders = useMemo(() => {
    if (!team?.id || !orders) return [];
    return orders.filter(order => shouldShowOrderOnTracker(order, team.id));
  }, [orders, team?.id]);

  // NEW: Load check-in history for an order
  const loadOrderCheckIns = async (orderId) => {
    if (!db || !orderId) return [];

    try {
      const checkInsQuery = query(
        collection(db, 'orderCheckIns'),
        where('orderId', '==', orderId),
        orderBy('timestamp', 'desc'),
        limit(50)
      );
      
      const snapshot = await getDocs(checkInsQuery);
      const checkIns = [];
      snapshot.docs.forEach(doc => {
        checkIns.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      return checkIns;
    } catch (error) {
      console.error('Error loading order check-ins:', error);
      return [];
    }
  };

  // NEW: Get counters for each action type
  const getActionCounter = (order, actionType) => {
    const checkIns = orderCheckIns[order.id] || [];
    return checkIns.filter(checkIn => checkIn.actionType === actionType).length;
  };

  // NEW: Handle camera car actions
  const handleCameraAction = (actionType, order) => {
    setSelectedOrder(order);
    setCameraActionType(actionType);
    setShowCameraAction(true);
  };

  // NEW: Submit camera car action
  const submitCameraAction = async (actionData) => {
    try {
      // Upload photos to storage if any (in real implementation)
      const photoUrls = [];
      if (actionData.photos && actionData.photos.length > 0) {
        // For now, use data URLs (not recommended for production)
        photoUrls = actionData.photos.map(photo => ({
          url: photo.url,
          name: photo.name
        }));
      }

      // Create check-in record
      const checkInData = {
        orderId: selectedOrder.id,
        actionType: actionData.actionType,
        notes: actionData.notes,
        vinVerified: actionData.vinVerified,
        photos: photoUrls,
        timestamp: serverTimestamp(),
        userId: user.id,
        userName: actionData.userName,
        teamId: team.id
      };

      await addDoc(collection(db, 'orderCheckIns'), checkInData);

      // If vehicle is located, update order status and add to team vehicles
      if (actionData.actionType === 'located') {
        // Update order status
        await updateDoc(doc(db, 'orders', selectedOrder.id), {
          status: 'pending-pickup',
          lastCheckIn: serverTimestamp(),
          cameraCarVerified: true,
          vinVerified: actionData.vinVerified,
          updatedAt: serverTimestamp()
        });

        // Create vehicle entry for team vehicle tracker
        const weekId = `week_${new Date().getFullYear()}_${Math.ceil((new Date().getTime() - new Date(new Date().getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}`;
        
        // Ensure week document exists
        await setDoc(
          doc(db, 'users', user.id, 'vehicleWeeks', weekId),
          {
            startDate: new Date(),
            displayRange: `Week ${Math.ceil((new Date().getTime() - new Date(new Date().getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}`,
            updatedAt: serverTimestamp()
          },
          { merge: true }
        );

        // Format address helper
        const formatAddress = (address) => {
          if (!address) return '';
          if (typeof address === 'string') return address;
          
          let parts = [];
          if (address.street) parts.push(address.street);
          
          let cityStateZip = '';
          if (address.city) cityStateZip += address.city;
          if (address.state) {
            if (cityStateZip) cityStateZip += ', ';
            cityStateZip += address.state;
          }
          if (address.zip) {
            if (cityStateZip) cityStateZip += ' ';
            cityStateZip += address.zip;
          }
          
          if (cityStateZip) parts.push(cityStateZip);
          return parts.join(', ');
        };

        const vehicleData = {
          vehicle: `${selectedOrder.year} ${selectedOrder.make} ${selectedOrder.model}`,
          vin: selectedOrder.vin,
          vinVerified: actionData.vinVerified,
          make: selectedOrder.make,
          model: selectedOrder.model,
          year: selectedOrder.year,
          color: selectedOrder.color || '',
          plateNumber: selectedOrder.licensePlate || '',
          accountNumber: selectedOrder.caseNumber || '',
          financier: selectedOrder.customerName || '',
          address: selectedOrder.addresses?.[0]?.street || '',
          city: selectedOrder.addresses?.[0]?.city || '',
          state: selectedOrder.addresses?.[0]?.state || '',
          zipCode: selectedOrder.addresses?.[0]?.zip || '',
          fullAddress: formatAddress(selectedOrder.addresses?.[0]) || '',
          position: selectedOrder.addresses?.[0]?.position || selectedOrder.position || null,
          date: new Date().toISOString().split('T')[0],
          status: 'PENDING PICKUP',
          notes: actionData.notes,
          images: photoUrls,
          teamMemberId: user.id,
          teamMemberName: actionData.userName,
          weekId: weekId,
          weekRange: `Week ${Math.ceil((new Date().getTime() - new Date(new Date().getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}`,
          uniqueKey: `${user.id}_${weekId}_${selectedOrder.id}`,
          orderId: selectedOrder.id,
          fromCameraCarCheck: true,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        // Add vehicle to week
        await addDoc(
          collection(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles'),
          vehicleData
        );

        playMoneySound(soundEnabled);

        // Show success notification
        const notification = {
          id: Date.now(),
          type: 'team_sync_success',
          message: `✅ Vehicle located and added to pending pickup: ${selectedOrder.year} ${selectedOrder.make} ${selectedOrder.model}`,
          timestamp: new Date()
        };

        setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
        setTimeout(() => {
          setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
        }, 8000);
      }

      // Update order counters
      const counterUpdates = {};
      if (actionData.actionType === 'not_present') {
        counterUpdates.notPresentCount = (selectedOrder.notPresentCount || 0) + 1;
      } else if (actionData.actionType === 'blocked') {
        counterUpdates.blockedCount = (selectedOrder.blockedCount || 0) + 1;
      }

      if (Object.keys(counterUpdates).length > 0) {
        await updateDoc(doc(db, 'orders', selectedOrder.id), {
          ...counterUpdates,
          lastCheckIn: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      }

      // Reload check-ins for this order
      const updatedCheckIns = await loadOrderCheckIns(selectedOrder.id);
      setOrderCheckIns(prev => ({
        ...prev,
        [selectedOrder.id]: updatedCheckIns
      }));

      // Show success notification
      const notification = {
        id: Date.now(),
        type: 'team_sync_success',
        message: `📋 Check-in recorded: ${selectedOrder.year} ${selectedOrder.make} ${selectedOrder.model} - ${actionData.actionType}`,
        timestamp: new Date()
      };

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 5000);

    } catch (error) {
      console.error('Error submitting camera car action:', error);
      throw error;
    }
  };

  // ENHANCED: Geocode address with structured data and IL/IN/WI focus
  const geocodeAddress = async (address, city, state, zipCode) => {
    // Build full address from components if provided
    let fullAddress = address;
    if (city || state || zipCode) {
      fullAddress = buildFullAddress(address, city, state, zipCode);
    }
    
    if (!fullAddress) return null;
    
    if (geocodingCache.current[fullAddress]) {
      console.log(`Using cached coordinates for: ${fullAddress}`);
      return geocodingCache.current[fullAddress];
    }
    
    if (geocodingInProgress.current[fullAddress]) {
      console.log(`Already geocoding: ${fullAddress}`);
      return null;
    }
    
    try {
      geocodingInProgress.current[fullAddress] = true;
      console.log(`Geocoding address: ${fullAddress}`);
      
      // Bounding box for Illinois, Indiana, and Wisconsin
      const boundingBox = {
        minLat: 37.0,  // Southern Illinois
        maxLat: 47.0,  // Northern Wisconsin
        minLng: -92.5, // Western Wisconsin
        maxLng: -84.5  // Eastern Indiana
      };
      
      let position = null;
      
      // Strategy 1: Use structured query if we have city and state
      if (city && state) {
        console.log(`Using structured query with city: ${city}, state: ${state}`);
        const structuredQuery = new URLSearchParams({
          format: 'json',
          street: address || '',
          city: city,
          state: state,
          postalcode: zipCode || '',
          countrycodes: 'us',
          bounded: '1',
          viewbox: `${boundingBox.minLng},${boundingBox.maxLat},${boundingBox.maxLng},${boundingBox.minLat}`,
          limit: '1'
        });
        
        const responseStructured = await fetch(
          `https://nominatim.openstreetmap.org/search?${structuredQuery}`,
          {
            headers: {
              'User-Agent': 'TeamVehicleTracker/1.0'
            }
          }
        );
        
        if (responseStructured.ok) {
          const dataStructured = await responseStructured.json();
          if (dataStructured && dataStructured.length > 0) {
            position = {
              lat: parseFloat(dataStructured[0].lat),
              lng: parseFloat(dataStructured[0].lon)
            };
            console.log(`Geocoded with structured query: ${fullAddress}`);
          }
        }
      }
      
      // Strategy 2: Try with bounding box and US country code
      if (!position) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // Rate limit
        
        const responseWithBounds = await fetch(
          `https://nominatim.openstreetmap.org/search?` +
          `format=json&` +
          `q=${encodeURIComponent(fullAddress)}&` +
          `countrycodes=us&` +
          `bounded=1&` +
          `viewbox=${boundingBox.minLng},${boundingBox.maxLat},${boundingBox.maxLng},${boundingBox.minLat}&` +
          `limit=1`,
          {
            headers: {
              'User-Agent': 'TeamVehicleTracker/1.0'
            }
          }
        );
        
        if (responseWithBounds.ok) {
          const dataWithBounds = await responseWithBounds.json();
          if (dataWithBounds && dataWithBounds.length > 0) {
            position = {
              lat: parseFloat(dataWithBounds[0].lat),
              lng: parseFloat(dataWithBounds[0].lon)
            };
            console.log(`Geocoded with IL/IN/WI bounds: ${fullAddress}`);
          }
        }
      }
      
      if (position) {
        geocodingCache.current[fullAddress] = position;
        console.log(`Successfully geocoded ${fullAddress} to:`, position);
        return position;
      } else {
        console.warn(`No geocoding results found for: ${fullAddress}`);
        return null;
      }
    } catch (error) {
      console.error(`Error geocoding address "${fullAddress}":`, error);
      return null;
    } finally {
      delete geocodingInProgress.current[fullAddress];
    }
  };

  // ENHANCED: Auto-geocode vehicle with structured address fields
  const autoGeocodeVehicle = async (vehicleData, memberId, weekId, vehicleId) => {
    // Skip if already has position
    if (vehicleData.position) {
      return vehicleData;
    }
    
    // Skip if no address data at all
    if (!vehicleData.address && !vehicleData.city && !vehicleData.fullAddress) {
      return vehicleData;
    }
    
    // Use structured geocoding with separate fields if available
    const position = await geocodeAddress(
      vehicleData.address || vehicleData.fullAddress,
      vehicleData.city,
      vehicleData.state,
      vehicleData.zipCode
    );
    
    if (position) {
      try {
        await updateDoc(
          doc(db, 'users', memberId, 'vehicleWeeks', weekId, 'vehicles', vehicleId),
          {
            position: position,
            updatedAt: serverTimestamp()
          }
        );
        console.log(`Updated vehicle ${vehicleData.vehicle} with geocoded position`);
      } catch (error) {
        console.error(`Error updating vehicle position in Firestore:`, error);
      }
      
      return {
        ...vehicleData,
        position: position
      };
    }
    
    return vehicleData;
  };

  // Calculate ETA (15 minutes from when driver goes in route)
  const calculateETA = (inRouteTimestamp) => {
    if (!inRouteTimestamp) return null;
    const inRouteTime = inRouteTimestamp.toDate ? inRouteTimestamp.toDate() : new Date(inRouteTimestamp);
    const eta = new Date(inRouteTime.getTime() + 15 * 60 * 1000);
    return eta;
  };

  // Get time remaining to ETA
  const getTimeToETA = (eta) => {
    if (!eta) return null;
    const now = new Date();
    const remaining = eta - now;
    if (remaining <= 0) return 'Arriving Now';
    const minutes = Math.ceil(remaining / 60000);
    return `${minutes} min`;
  };

  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    const timeStr = date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });

    if (date.toDateString() === now.toDateString()) {
      return `Today ${timeStr}`;
    }
    else if (diffDays === 1) {
      return `Yesterday ${timeStr}`;
    }
    else if (diffDays < 7) {
      return `${diffDays}d ago ${timeStr}`;
    }
    else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    }
  };

  // Get status emoji
  const getStatusEmoji = (status) => {
    switch (status) {
      case 'SECURED': return '✅';
      case 'FOUND': return '🔍';
      case 'PENDING PICKUP': return '📋';
      case 'DO NOT SECURE': return '🚫';
      case 'NOT FOUND': return '❌';
      default: return '❓';
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'SECURED': return 'bg-green-600';
      case 'FOUND': return 'bg-yellow-600';
      case 'PENDING PICKUP': return 'bg-blue-600';
      case 'DO NOT SECURE': return 'bg-purple-600';
      case 'NOT FOUND': return 'bg-red-600';
      default: return 'bg-gray-600';
    }
  };

  // Show reminder notification
  const showReminderNotification = (vehicleData, timeElapsed, isBottomStatus = false) => {
    playReminderSound(soundEnabled);

    const message = isBottomStatus 
      ? `⚠️ DAILY REMINDER: Recheck ${vehicleData.vehicle} (VIN: ${vehicleData.vin}) - Status: ${vehicleData.bottomStatus} - From ${vehicleData.teamMemberName}`
      : `⏰ REMINDER: ${vehicleData.vehicle} (VIN: ${vehicleData.vin}) from ${vehicleData.teamMemberName} - ${timeElapsed} elapsed!`;

    const notification = {
      id: Date.now() + Math.random(),
      type: 'reminder',
      message: message,
      timestamp: new Date(),
      vehicleId: vehicleData.id
    };

    setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);

    setTimeout(() => {
      setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 15000);
  };

  // Check reminders for a team vehicle
  const checkTeamVehicleReminders = (vehicle) => {
    if (vehicle.inRouteDriverId || vehicle.arrivedDriverId) return;
    
    const timeData = calculateElapsedTime(vehicle);
    if (!timeData) return;

    const { elapsed } = timeData;
    const oneHour = 60 * 60 * 1000;
    const twentyFourHours = 24 * oneHour;
    const sixHours = 6 * oneHour;

    const vehicleKey = vehicle.uniqueKey || `${vehicle.teamMemberId}_${vehicle.weekId}_${vehicle.id}`;

    if (vehicle.bottomStatus) {
      const today = new Date().toDateString();
      
      if (!dailyReminderSentRef.current[vehicleKey] || dailyReminderSentRef.current[vehicleKey] !== today) {
        dailyReminderSentRef.current[vehicleKey] = today;
        showReminderNotification(vehicle, '', true);
      }
      return;
    }

    if (!vehicleReminderStatesRef.current[vehicleKey]) {
      vehicleReminderStatesRef.current[vehicleKey] = { remindersSent: 0 };
    }
    const currentReminders = vehicleReminderStatesRef.current[vehicleKey].remindersSent;

    let shouldSendReminder = false;
    let reminderMessage = '';
    let newReminderCount = currentReminders;

    if (elapsed >= oneHour && currentReminders === 0) {
      shouldSendReminder = true;
      reminderMessage = '1 hour';
      newReminderCount = 1;
    } else if (elapsed < twentyFourHours && elapsed >= oneHour) {
      const hoursElapsed = Math.floor(elapsed / oneHour);
      if (hoursElapsed > currentReminders && currentReminders < 24) {
        shouldSendReminder = true;
        reminderMessage = `${hoursElapsed} hour${hoursElapsed > 1 ? 's' : ''}`;
        newReminderCount = hoursElapsed;
      }
    } else if (elapsed >= twentyFourHours) {
      const totalHours = Math.floor(elapsed / oneHour);
      const hoursSince24 = totalHours - 24;
      const sixHourIntervalsSince24 = Math.floor(hoursSince24 / 6) + 1;
      const expectedReminders = 24 + sixHourIntervalsSince24;

      if (expectedReminders > currentReminders) {
        shouldSendReminder = true;
        const daysPassed = Math.floor(totalHours / 24);
        if (daysPassed >= 2) {
          reminderMessage = `${daysPassed} days`;
        } else {
          reminderMessage = `${totalHours} hours`;
        }
        newReminderCount = expectedReminders;
      }
    }

    if (shouldSendReminder) {
      showReminderNotification(vehicle, reminderMessage);

      vehicleReminderStatesRef.current[vehicleKey].remindersSent = newReminderCount;

      setTeamVehicleTimers(prev => ({
        ...prev,
        [vehicleKey]: {
          ...prev[vehicleKey],
          remindersSent: newReminderCount
        }
      }));
    }
  };

  // Delete vehicle from team tracker and move to never secured
  const handleDeleteVehicleClick = (vehicle) => {
    setVehicleToDelete(vehicle);
    setDeleteReason('');
    setShowDeleteModal(true);
  };

  // Process vehicle deletion
  const processVehicleDelete = async () => {
    if (!db || !user || !team || !vehicleToDelete || !deleteReason.trim()) return;

    try {
      const reasonText = deleteReason.trim();
      
      // Close modal first
      setShowDeleteModal(false);

      // Add to never secured list with deletion reason
      const neverSecuredRef = doc(collection(db, 'teams', team.id, 'neverSecuredVehicles'));
      const neverSecuredData = {
        ...vehicleToDelete,
        bottomStatus: 'MANUALLY REMOVED',
        deleteReason: reasonText,
        deletedByUserId: user.id,
        deletedByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
        deletedFromTeamTracker: true,
        lastAttemptDate: new Date().toISOString(),
        createdAt: serverTimestamp()
      };

      await setDoc(neverSecuredRef, neverSecuredData);

      // Update the original vehicle to mark as never secured
      try {
        await updateDoc(
          doc(db, 'users', vehicleToDelete.teamMemberId, 'vehicleWeeks', vehicleToDelete.weekId, 'vehicles', vehicleToDelete.id),
          {
            isNeverSecured: true,
            neverSecuredDate: new Date().toISOString(),
            manuallyRemoved: true,
            manuallyRemovedBy: user.displayName || user.email?.split('@')[0] || 'Team Member',
            manuallyRemovedReason: reasonText,
            updatedAt: serverTimestamp()
          }
        );
      } catch (updateError) {
        console.warn('Could not update original vehicle - it may have been deleted:', updateError);
        // Continue with the process even if original vehicle update fails
      }

      // Remove from team vehicles list
      setTeamVehicles(prev => {
        const newList = prev.filter(v => v.uniqueKey !== vehicleToDelete.uniqueKey);
        lastTeamVehicleCount.current = newList.length;
        
        const vehicleKey = vehicleToDelete.uniqueKey;
        delete vehicleReminderStatesRef.current[vehicleKey];
        delete dailyReminderSentRef.current[vehicleKey];
        
        return newList;
      });

      // Reload never secured list
      const updatedNeverSecured = await loadNeverSecuredVehicles(db, team.id);
      setNeverSecuredVehicles(updatedNeverSecured);

      const notification = {
        id: Date.now(),
        type: 'team_sync_success',
        message: `🗑️ ${vehicleToDelete.vehicle} removed from team tracker and moved to Never Secured list`,
        timestamp: new Date()
      };

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 8000);

      playNotificationSound(soundEnabled);
      
      // Clear state
      setVehicleToDelete(null);
      setDeleteReason('');

      // Post to Slack
      if (team && team.id && typeof postVehicleToSlack === 'function') {
        try {
          const slackVehicle = formatVehicleForSlack({
            ...vehicleToDelete,
            status: 'MANUALLY REMOVED',
            deletedByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
            deleteReason: reasonText
          });
          postVehicleToSlack(team.id, slackVehicle, 'deleted');
        } catch (error) {
          console.error("Error posting deletion to Slack:", error);
        }
      }

    } catch (error) {
      console.error("Error deleting vehicle:", error);
      alert("Error removing vehicle. Please try again.");
    }
  };

  // Export vehicle card as image
  const exportVehicleCard = async (vehicle) => {
    // Create a canvas to draw the vehicle card
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size (larger for more info)
    canvas.width = 500;
    canvas.height = 1000;
    
    // Background
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Header background
    const statusColor = vehicle.bottomStatus ? '#dc2626' :
                       vehicle.status === 'SECURED' ? '#059669' :
                       vehicle.status === 'FOUND' ? '#d97706' :
                       vehicle.status === 'PENDING PICKUP' ? '#2563eb' : 
                       vehicle.status === 'DO NOT SECURE' ? '#9333ea' : '#dc2626';
    
    ctx.fillStyle = statusColor;
    ctx.fillRect(0, 0, canvas.width, 70);
    
    // Status text
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 24px Arial';
    ctx.fillText(vehicle.status || 'UNKNOWN', 20, 45);
    
    // Time in top right
    ctx.font = '14px Arial';
    ctx.fillText(new Date().toLocaleString(), canvas.width - 180, 30);
    ctx.font = '12px Arial';
    ctx.fillText('Export Time', canvas.width - 180, 50);
    
    // Vehicle name
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 28px Arial';
    ctx.fillText(vehicle.vehicle || 'Unknown Vehicle', 20, 110);
    
    // VIN Section
    ctx.fillStyle = '#dc2626';
    ctx.fillRect(20, 125, 200, 40);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 20px Arial';
    ctx.fillText(`VIN: ${vehicle.vin || 'N/A'}`, 30, 150);
    
    // VIN Verified badge
    if (vehicle.vinVerified) {
      ctx.fillStyle = '#059669';
      ctx.fillRect(230, 125, 120, 40);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Arial';
      ctx.fillText('✓ VERIFIED', 245, 150);
    } else {
      ctx.fillStyle = '#6b7280';
      ctx.fillRect(230, 125, 130, 40);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Arial';
      ctx.fillText('NOT VERIFIED', 240, 150);
    }
    
    // Information fields
    let yPos = 190;
    ctx.font = '14px Arial';
    
    // Two column layout for info
    const leftFields = [
      { label: 'From:', value: vehicle.teamMemberName },
      { label: 'Date:', value: vehicle.date },
      { label: 'Created:', value: vehicle.createdAt ? formatTimestamp(vehicle.createdAt) : 'Unknown' },
      { label: 'Week:', value: vehicle.weekRange },
      { label: 'Color:', value: vehicle.color || 'Not provided' },
      { label: 'Drive Type:', value: vehicle.driveType || 'Not provided' }
    ];
    
    const rightFields = [
      { label: 'Plate #:', value: vehicle.plateNumber || 'Not provided' },
      { label: 'Account #:', value: vehicle.accountNumber || 'Not provided' },
      { label: 'Financier:', value: vehicle.financier || 'Not provided' }
    ];
    
    // Draw left column
    leftFields.forEach(field => {
      ctx.fillStyle = '#9ca3af';
      ctx.font = 'bold 12px Arial';
      ctx.fillText(field.label, 20, yPos);
      ctx.fillStyle = '#ffffff';
      ctx.font = '14px Arial';
      ctx.fillText(field.value, 20, yPos + 18);
      yPos += 40;
    });
    
    // Draw right column
    yPos = 190;
    rightFields.forEach(field => {
      ctx.fillStyle = '#9ca3af';
      ctx.font = 'bold 12px Arial';
      ctx.fillText(field.label, 260, yPos);
      ctx.fillStyle = '#ffffff';
      ctx.font = '14px Arial';
      ctx.fillText(field.value, 260, yPos + 18);
      yPos += 40;
    });
    
    yPos = 430;
    
    // DO NOT SECURE reason if present
    if (vehicle.status === 'DO NOT SECURE' && vehicle.doNotSecureReason) {
      ctx.fillStyle = '#9333ea';
      ctx.fillRect(20, yPos, canvas.width - 40, 60);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Arial';
      ctx.fillText('🚫 DO NOT SECURE REASON:', 30, yPos + 25);
      ctx.font = '14px Arial';
      ctx.fillText(vehicle.doNotSecureReason, 30, yPos + 45);
      yPos += 70;
    }
    
    // Address Section - Use structured address if available
    const displayAddress = vehicle.fullAddress || 
                          buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode) ||
                          vehicle.address;
    
    if (displayAddress) {
      ctx.fillStyle = '#2563eb';
      ctx.fillRect(20, yPos, canvas.width - 40, 80);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 14px Arial';
      ctx.fillText('📍 VEHICLE LOCATION:', 30, yPos + 20);
      ctx.font = '16px Arial';
      
      // Word wrap for address
      const words = displayAddress.split(' ');
      let line = '';
      let lineY = yPos + 45;
      
      words.forEach((word, i) => {
        const testLine = line + word + ' ';
        const metrics = ctx.measureText(testLine);
        const testWidth = metrics.width;
        
        if (testWidth > canvas.width - 60 && i > 0) {
          ctx.fillText(line, 30, lineY);
          line = word + ' ';
          lineY += 22;
        } else {
          line = testLine;
        }
      });
      ctx.fillText(line, 30, lineY);
      yPos += 90;
    }
    
    // GPS Coordinates
    if (vehicle.position) {
      ctx.fillStyle = '#059669';
      ctx.fillRect(20, yPos, canvas.width - 40, 50);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 14px Arial';
      ctx.fillText('GPS COORDINATES:', 30, yPos + 20);
      ctx.font = '16px Arial';
      ctx.fillText(`Lat: ${vehicle.position.lat.toFixed(6)}, Lng: ${vehicle.position.lng.toFixed(6)}`, 30, yPos + 40);
      yPos += 60;
    } else {
      ctx.fillStyle = '#6b7280';
      ctx.fillRect(20, yPos, canvas.width - 40, 40);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 14px Arial';
      ctx.fillText('NO GPS COORDINATES AVAILABLE', 30, yPos + 25);
      yPos += 50;
    }
    
    // Bottom status if present
    if (vehicle.bottomStatus) {
      ctx.fillStyle = '#dc2626';
      ctx.fillRect(20, yPos, canvas.width - 40, 50);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 18px Arial';
      ctx.fillText(`⚠️ ${vehicle.bottomStatus}`, 30, yPos + 30);
      ctx.font = '14px Arial';
      ctx.fillText(`Attempt ${vehicle.bottomStatusCount || 1}/3`, canvas.width - 120, yPos + 30);
      yPos += 60;
    }
    
    // Driver Status
    if (vehicle.inRouteDriverId || vehicle.arrivedDriverId) {
      const driverStatusColor = vehicle.arrivedDriverId ? '#059669' : '#2563eb';
      ctx.fillStyle = driverStatusColor;
      ctx.fillRect(20, yPos, canvas.width - 40, 50);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Arial';
      
      if (vehicle.arrivedDriverId) {
        ctx.fillText(`📍 DRIVER ARRIVED: ${vehicle.arrivedDriverName}`, 30, yPos + 20);
        ctx.font = '12px Arial';
        ctx.fillText(vehicle.arrivedTimestamp ? formatTimestamp(vehicle.arrivedTimestamp) : '', 30, yPos + 38);
      } else {
        ctx.fillText(`🚚 IN ROUTE: ${vehicle.inRouteDriverName}`, 30, yPos + 20);
        ctx.font = '12px Arial';
        ctx.fillText(vehicle.inRouteTimestamp ? formatTimestamp(vehicle.inRouteTimestamp) : '', 30, yPos + 38);
      }
      yPos += 60;
    }
    
    // Notes
    if (vehicle.notes) {
      ctx.fillStyle = '#374151';
      ctx.fillRect(20, yPos, canvas.width - 40, 80);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 14px Arial';
      ctx.fillText('📝 NOTES:', 30, yPos + 20);
      ctx.font = '12px Arial';
      
      // Word wrap for notes
      const noteWords = vehicle.notes.split(' ');
      let noteLine = '';
      let noteY = yPos + 40;
      let lineCount = 0;
      
      noteWords.forEach((word, i) => {
        const testLine = noteLine + word + ' ';
        const metrics = ctx.measureText(testLine);
        const testWidth = metrics.width;
        
        if ((testWidth > canvas.width - 60 && i > 0) || lineCount >= 3) {
          if (lineCount < 3) {
            ctx.fillText(noteLine, 30, noteY);
            noteLine = word + ' ';
            noteY += 18;
            lineCount++;
          }
        } else {
          noteLine = testLine;
        }
      });
      if (lineCount < 3) {
        ctx.fillText(noteLine, 30, noteY);
      }
      yPos += 90;
    }
    
    // Timer info if no driver assigned
    if (!vehicle.inRouteDriverId && !vehicle.arrivedDriverId && !vehicle.bottomStatus) {
      const timerDisplay = getTimerDisplay(vehicle);
      if (timerDisplay) {
        ctx.fillStyle = '#ea580c';
        ctx.fillRect(20, yPos, canvas.width - 40, 40);
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 16px Arial';
        ctx.fillText(`⏱️ TIME ELAPSED: ${timerDisplay}`, 30, yPos + 25);
        yPos += 50;
      }
    }
    
    // Footer
    ctx.fillStyle = '#4b5563';
    ctx.font = '10px Arial';
    ctx.fillText(`Vehicle ID: ${vehicle.id || 'N/A'}`, 20, canvas.height - 40);
    ctx.fillText(`Generated: ${new Date().toLocaleString()}`, 20, canvas.height - 25);
    ctx.fillText(`Team: ${team?.name || 'N/A'}`, 20, canvas.height - 10);
    ctx.fillText('NWRepo Vehicle Tracker', canvas.width - 150, canvas.height - 10);
    
    // Convert to blob and download as JPG
    canvas.toBlob((blob) => {
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Vehicle_${vehicle.vehicle.replace(/\s+/g, '_')}_${vehicle.vin}_${new Date().getTime()}.jpg`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      // Show success notification
      const notification = {
        id: Date.now(),
        type: 'team_sync_success',
        message: `📸 Vehicle card exported successfully!`,
        timestamp: new Date()
      };
      
      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 3000);
      
      playNotificationSound(soundEnabled);
    }, 'image/jpeg', 0.9);
  };

  // Navigate to previous item
  const navigateToPrevious = () => {
    if (activeTab === 'vehicles') {
      const newIndex = currentVehicleIndex > 0 ? currentVehicleIndex - 1 : teamVehicles.length - 1;
      setCurrentVehicleIndex(newIndex);
      
      const vehicle = teamVehicles[newIndex];
      if (vehicle && onVehicleSelect && typeof onVehicleSelect === 'function') {
        onVehicleSelect(vehicle.uniqueKey);
      }
    } else {
      const newIndex = currentOrderIndex > 0 ? currentOrderIndex - 1 : filteredTeamOrders.length - 1;
      setCurrentOrderIndex(newIndex);
      
      const order = filteredTeamOrders[newIndex];
      if (order && onOrderSelect && typeof onOrderSelect === 'function') {
        onOrderSelect(order.id);
      }
    }
  };

  // Navigate to next item
  const navigateToNext = () => {
    if (activeTab === 'vehicles') {
      const newIndex = currentVehicleIndex < teamVehicles.length - 1 ? currentVehicleIndex + 1 : 0;
      setCurrentVehicleIndex(newIndex);
      
      const vehicle = teamVehicles[newIndex];
      if (vehicle && onVehicleSelect && typeof onVehicleSelect === 'function') {
        onVehicleSelect(vehicle.uniqueKey);
      }
    } else {
      const newIndex = currentOrderIndex < filteredTeamOrders.length - 1 ? currentOrderIndex + 1 : 0;
      setCurrentOrderIndex(newIndex);
      
      const order = filteredTeamOrders[newIndex];
      if (order && onOrderSelect && typeof onOrderSelect === 'function') {
        onOrderSelect(order.id);
      }
    }
  };

  // Setup real-time listeners for team vehicles with enhanced error handling
  const setupTeamVehicleListeners = async (retryAttempt = 0) => {
    if (!db || !team || !user) return;

    try {
      // Mark that we're in initial load
      isInitialLoadRef.current = true;
      setConnectionStatus('connecting');
      setLoadingTeamVehicles(true);
      
      // Clear existing listeners
      teamVehicleListenersRef.current.forEach(unsubscribe => {
        try {
          if (typeof unsubscribe === 'function') {
            unsubscribe();
          }
        } catch (error) {
          console.error('Error unsubscribing:', error);
        }
      });
      teamVehicleListenersRef.current = [];

      const teamMembersQuery = query(
        collection(db, 'teams', team.id, 'teamMembers')
      );

      const teamMembersSnapshot = await getDocs(teamMembersQuery);
      const teamMemberIds = teamMembersSnapshot.docs
        .map(doc => doc.data().userId);

      if (teamMemberIds.length === 0) {
        setTeamVehicles([]);
        setConnectionStatus('connected');
        setLoadingTeamVehicles(false);
        return;
      }

      const now = new Date();
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

      console.log('Starting initial geocoding for existing vehicles...');
      
      for (const memberId of teamMemberIds) {
        try {
          const memberDoc = await getDoc(doc(db, 'users', memberId));
          const memberData = memberDoc.exists() ? memberDoc.data() : {};
          const memberName = memberData.displayName || memberData.email?.split('@')[0] || 'Team Member';
          const isCurrentUser = memberId === user.id;

          const weeksQuery = query(
            collection(db, 'users', memberId, 'vehicleWeeks'),
            orderBy('startDate', 'desc')
          );

          const weeksSnapshot = await getDocs(weeksQuery);

          for (const weekDoc of weeksSnapshot.docs) {
            const weekData = weekDoc.data();
            const weekStartDate = weekData.startDate?.toDate();

            if (weekStartDate && weekStartDate >= threeMonthsAgo) {
              const vehiclesRef = collection(db, 'users', memberId, 'vehicleWeeks', weekDoc.id, 'vehicles');

              const unsubscribe = onSnapshot(vehiclesRef, async (snapshot) => {
                for (const change of snapshot.docChanges()) {
                  let vehicleData = {
                    id: change.doc.id,
                    ...change.doc.data(),
                    teamMemberId: memberId,
                    teamMemberName: memberName,
                    weekId: weekDoc.id,
                    weekRange: weekData.displayRange || 'Unknown Week',
                    uniqueKey: `${memberId}_${weekDoc.id}_${change.doc.id}`,
                    isOwnVehicle: isCurrentUser
                  };

                  if (change.type === 'added' || change.type === 'modified') {
                    const bottomStatuses = ['GONE', 'BLOCKED IN', 'DEBTOR INTERFERENCE', 'CANT SECURE'];
                    const isBottomStatus = vehicleData.bottomStatus && bottomStatuses.includes(vehicleData.bottomStatus);

                    if ((vehicleData.status === 'FOUND' || vehicleData.status === 'PENDING PICKUP' || vehicleData.status === 'DO NOT SECURE' || vehicleData.status === 'NOT FOUND') &&
                      vehicleData.vin &&
                      !vehicleData.securedByTeammate &&
                      !vehicleData.isNeverSecured) {

                      // Check if this vehicle already has 3 attempts and should be in never secured
                      if (vehicleData.bottomStatus && vehicleData.bottomStatusCount >= 3) {
                        // Auto move to never secured if not already marked
                        const neverSecuredRef = doc(collection(db, 'teams', team.id, 'neverSecuredVehicles'));
                        const neverSecuredData = {
                          ...vehicleData,
                          attemptCount: vehicleData.bottomStatusCount,
                          lastAttemptDate: vehicleData.bottomStatusDate || new Date().toISOString(),
                          autoMovedAfter3Attempts: true,
                          createdAt: serverTimestamp()
                        };

                        await setDoc(neverSecuredRef, neverSecuredData);

                        // Update the original vehicle
                        await updateDoc(
                          doc(db, 'users', memberId, 'vehicleWeeks', weekDoc.id, 'vehicles', change.doc.id),
                          {
                            isNeverSecured: true,
                            neverSecuredDate: new Date().toISOString(),
                            updatedAt: serverTimestamp()
                          }
                        );

                        // Reload never secured list
                        const updatedNeverSecured = await loadNeverSecuredVehicles(db, team.id);
                        setNeverSecuredVehicles(updatedNeverSecured);
                        
                        // Remove from team vehicles if it exists
                        setTeamVehicles(prev => {
                          const newList = prev.filter(v => v.uniqueKey !== vehicleData.uniqueKey);
                          lastTeamVehicleCount.current = newList.length;
                          
                          const vehicleKey = vehicleData.uniqueKey;
                          delete vehicleReminderStatesRef.current[vehicleKey];
                          delete dailyReminderSentRef.current[vehicleKey];
                          
                          return newList;
                        });
                      } else {
                        // Auto-geocode with structured fields
                        if ((vehicleData.address || vehicleData.city || vehicleData.fullAddress) && !vehicleData.position) {
                          console.log(`Auto-geocoding vehicle: ${vehicleData.vehicle}`);
                          vehicleData = await autoGeocodeVehicle(vehicleData, memberId, weekDoc.id, change.doc.id);
                        }

                        if (isBottomStatus) {
                          vehicleData.bottomStatus = vehicleData.bottomStatus;  // Keep the bottom status
                          
                          if (change.type === 'modified' && !isCurrentUser) {
                            const prevVehicle = teamVehicles.find(v => v.uniqueKey === vehicleData.uniqueKey);
                            if (prevVehicle && !prevVehicle.bottomStatus && vehicleData.bottomStatus) {
                              showReminderNotification(vehicleData, '', true);
                            }
                          }
                        }

                        setTeamVehicles(prev => {
                          const filtered = prev.filter(v => v.uniqueKey !== vehicleData.uniqueKey);

                          const vinExists = filtered.some(v =>
                            v.vin && v.vin.toUpperCase().trim() === vehicleData.vin.toUpperCase().trim()
                          );

                          if (!vinExists) {
                            const newList = [...filtered, vehicleData];

                            // UPDATED SORTING: Newest first by createdAt timestamp
                            const sortedList = newList.sort((a, b) => {
                              // First sort by createdAt timestamp (newest first)
                              if (a.createdAt && b.createdAt) {
                                const timestampA = a.createdAt.toDate ? a.createdAt.toDate() : new Date(a.createdAt);
                                const timestampB = b.createdAt.toDate ? b.createdAt.toDate() : new Date(b.createdAt);
                                const timeDiff = timestampB.getTime() - timestampA.getTime();
                                
                                // If created within the same minute, use other sorting criteria
                                if (Math.abs(timeDiff) > 60000) {
                                  return timeDiff;
                                }
                              }
                              
                              // Then sort: PENDING PICKUP at top
                              if (a.status === 'PENDING PICKUP' && b.status !== 'PENDING PICKUP') return -1;
                              if (a.status !== 'PENDING PICKUP' && b.status === 'PENDING PICKUP') return 1;
                              
                              // Then sort: Vehicles with drivers at top
                              if ((a.inRouteDriverId || a.arrivedDriverId) && !(b.inRouteDriverId || b.arrivedDriverId)) return -1;
                              if (!(a.inRouteDriverId || a.arrivedDriverId) && (b.inRouteDriverId || b.arrivedDriverId)) return 1;
                              
                              // Then sort: Bottom status vehicles at bottom
                              if (!a.bottomStatus && b.bottomStatus) return -1;
                              if (a.bottomStatus && !b.bottomStatus) return 1;

                              // Finally sort by date
                              const dateA = new Date(a.date || '1970-01-01');
                              const dateB = new Date(b.date || '1970-01-01');

                              return dateB.getTime() - dateA.getTime();
                            });

                            if (change.type === 'added' && sortedList.length > lastTeamVehicleCount.current) {
                              setIsFlashingTeamCount(true);
                              setTimeout(() => setIsFlashingTeamCount(false), 3000);
                              playNotificationSound(soundEnabled);

                              const notification = {
                                id: Date.now() + Math.random(),
                                type: 'team_sync',
                                message: `🚗 New vehicle from ${vehicleData.teamMemberName}: ${vehicleData.vehicle} (VIN: ${vehicleData.vin})`,
                                timestamp: new Date(),
                                vehicleId: vehicleData.id
                              };

                              setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
                              setTimeout(() => {
                                setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
                              }, 10000);
                              
                              // Only post to Slack if not in initial load and recently created
                              const shouldPostToSlack = !isInitialLoadRef.current && vehicleData.createdAt;
                              
                              if (shouldPostToSlack) {
                                const createdTime = vehicleData.createdAt.toDate ? vehicleData.createdAt.toDate() : new Date(vehicleData.createdAt);
                                const timeSinceCreation = Date.now() - createdTime.getTime();
                                const isRecentlyCreated = timeSinceCreation < 30000; // 30 seconds
                                
                                if (isRecentlyCreated && team && team.id && typeof postVehicleToSlack === 'function') {
                                  try {
                                    console.log(`Posting new vehicle to Slack: ${vehicleData.vehicle} (created ${Math.round(timeSinceCreation / 1000)}s ago)`);
                                    const slackVehicle = formatVehicleForSlack(vehicleData);
                                    postVehicleToSlack(team.id, slackVehicle, 'new');
                                  } catch (error) {
                                    console.error("Error posting to Slack:", error);
                                  }
                                }
                              }
                            }

                            lastTeamVehicleCount.current = sortedList.length;
                            return sortedList;
                          }

                          return filtered;
                        });
                      }
                    } else if (vehicleData.status === 'SECURED' || vehicleData.securedByTeammate) {
                      setTeamVehicles(prev => {
                        const newList = prev.filter(v => v.uniqueKey !== vehicleData.uniqueKey);
                        lastTeamVehicleCount.current = newList.length;

                        const vehicleKey = vehicleData.uniqueKey;
                        delete vehicleReminderStatesRef.current[vehicleKey];
                        delete dailyReminderSentRef.current[vehicleKey];

                        return newList;
                      });
                    }
                  } else if (change.type === 'removed') {
                    setTeamVehicles(prev => {
                      const newList = prev.filter(v => v.uniqueKey !== vehicleData.uniqueKey);
                      lastTeamVehicleCount.current = newList.length;

                      const vehicleKey = vehicleData.uniqueKey;
                      delete vehicleReminderStatesRef.current[vehicleKey];
                      delete dailyReminderSentRef.current[vehicleKey];

                      return newList;
                    });
                  }
                }
              }, (error) => {
                console.error(`Error in vehicle listener for member ${memberId}:`, error);
                setConnectionStatus('error');
                
                // Attempt reconnection
                if (retryAttempt < maxRetries) {
                  console.log(`Retrying connection (attempt ${retryAttempt + 1}/${maxRetries})...`);
                  setTimeout(() => {
                    setupTeamVehicleListeners(retryAttempt + 1);
                  }, 2000 * (retryAttempt + 1)); // Exponential backoff
                }
              });

              teamVehicleListenersRef.current.push(unsubscribe);
            }
          }
        } catch (error) {
          console.error(`Error setting up listeners for team member ${memberId}:`, error);
        }
      }

      // After all listeners are set up, mark initial load as complete
      setTimeout(() => {
        isInitialLoadRef.current = false;
        initialLoadCompleteRef.current = true;
        setConnectionStatus('connected');
        setRetryCount(0);
        console.log('Initial load complete - new vehicles will now post to Slack');
      }, 2000); // 2 second delay to ensure all initial vehicles are loaded

      setLoadingTeamVehicles(false);

    } catch (error) {
      console.error("Error setting up team vehicle listeners:", error);
      setConnectionStatus('error');
      setLoadingTeamVehicles(false);
      
      // Retry connection if under max retries
      if (retryAttempt < maxRetries) {
        setRetryCount(retryAttempt + 1);
        const retryDelay = 2000 * (retryAttempt + 1); // Exponential backoff
        console.log(`Retrying connection in ${retryDelay}ms (attempt ${retryAttempt + 1}/${maxRetries})...`);
        
        setTimeout(() => {
          setupTeamVehicleListeners(retryAttempt + 1);
        }, retryDelay);
      } else {
        console.error('Max retries reached. Please refresh the page.');
      }
    }
  };

  // Load unsecured vehicles from teammates
  const loadTeamVehicles = async () => {
    if (!db || !team || !user) return;

    try {
      setLoadingTeamVehicles(true);
      await setupTeamVehicleListeners();

    } catch (error) {
      console.error("Error loading team vehicles:", error);
      setTeamVehicles([]);
      setLoadingTeamVehicles(false);
    }
  };

  // Mark vehicle as in route
  const markInRoute = async (teamVehicle) => {
    if (!db || !user || !selectedWeek || !canSecureVehicles) return;

    try {
      if (!confirm(`Are you sure you want to mark yourself as IN ROUTE to ${teamVehicle.vehicle}?`)) {
        return;
      }

      const userDisplayName = user.displayName || user.email?.split('@')[0] || 'Team Member';

      await updateDoc(
        doc(db, 'users', teamVehicle.teamMemberId, 'vehicleWeeks', teamVehicle.weekId, 'vehicles', teamVehicle.id),
        {
          inRouteDriverId: user.id,
          inRouteDriverName: userDisplayName,
          inRouteTimestamp: serverTimestamp(),
          updatedAt: serverTimestamp()
        }
      );

      playNotificationSound(soundEnabled);

      const notification = {
        id: Date.now(),
        type: 'team_sync_success',
        message: `🚚 You are now IN ROUTE to ${teamVehicle.vehicle}`,
        timestamp: new Date()
      };

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 5000);
      
      // Post to Slack
      if (team && team.id && typeof postVehicleToSlack === 'function') {
        try {
          const slackVehicle = formatVehicleForSlack({
            ...teamVehicle,
            inRouteDriverId: user.id,
            inRouteDriverName: userDisplayName,
            inRouteTimestamp: new Date().toISOString()
          });
          postVehicleToSlack(team.id, slackVehicle, 'in_route');
        } catch (error) {
          console.error("Error posting to Slack:", error);
        }
      }

    } catch (error) {
      console.error("Error marking in route:", error);
      alert("Error marking in route. Please try again.");
    }
  };

  // Mark vehicle as arrived
  const markArrived = async (teamVehicle) => {
    if (!db || !user || !selectedWeek || !canSecureVehicles) return;

    try {
      const userDisplayName = user.displayName || user.email?.split('@')[0] || 'Team Member';

      await updateDoc(
        doc(db, 'users', teamVehicle.teamMemberId, 'vehicleWeeks', teamVehicle.weekId, 'vehicles', teamVehicle.id),
        {
          arrivedDriverId: user.id,
          arrivedDriverName: userDisplayName,
          arrivedTimestamp: serverTimestamp(),
          updatedAt: serverTimestamp()
        }
      );

      playNotificationSound(soundEnabled);

      const notification = {
        id: Date.now(),
        type: 'team_sync_success',
        message: `📍 You have ARRIVED at ${teamVehicle.vehicle}`,
        timestamp: new Date()
      };

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 5000);
      
      // Post to Slack
      if (team && team.id && typeof postVehicleToSlack === 'function') {
        try {
          const slackVehicle = formatVehicleForSlack({
            ...teamVehicle,
            arrivedDriverId: user.id,
            arrivedDriverName: userDisplayName,
            arrivedTimestamp: new Date().toISOString()
          });
          postVehicleToSlack(team.id, slackVehicle, 'arrived');
        } catch (error) {
          console.error("Error posting to Slack:", error);
        }
      }

    } catch (error) {
      console.error("Error marking arrived:", error);
      alert("Error marking arrived. Please try again.");
    }
  };

  // MODIFIED: Open recovery modal instead of immediately securing
  const secureTeammateVehicle = async (teamVehicle) => {
    if (!db || !user || !selectedWeek) return;

    // Open recovery modal with vehicle data
    setRecoveryVehicle(teamVehicle);
    setShowRecoveryModal(true);
  };

  // NEW: Handle recovery completion from modal with photo upload fix
  const handleRecoveryComplete = async (recoveryData) => {
    if (!db || !user || !selectedWeek || !recoveryVehicle) return;

    try {
      // Close recovery modal
      setShowRecoveryModal(false);

      // Remove from team vehicles list immediately
      setTeamVehicles(prev => {
        const newList = prev.filter(v => v.uniqueKey !== recoveryVehicle.uniqueKey);
        lastTeamVehicleCount.current = newList.length;

        const vehicleKey = recoveryVehicle.uniqueKey;
        delete vehicleReminderStatesRef.current[vehicleKey];
        delete dailyReminderSentRef.current[vehicleKey];

        return newList;
      });

      // UPLOAD PHOTOS TO STORAGE FIRST - NEW CODE TO FIX DOCUMENT SIZE LIMIT
      let uploadedPhotoURLs = {};
      
      // Check if photos contain file data and need to be uploaded
      if (recoveryData.photos && Object.keys(recoveryData.photos).length > 0) {
        console.log('Uploading recovery photos to Firebase Storage...');
        
        const storage = getStorage();
        
        // Helper function to convert data URL to blob
        const dataURLToBlob = async (dataURL) => {
          const response = await fetch(dataURL);
          return response.blob();
        };
        
        // Upload each photo type that contains data
        for (const [photoType, photoValue] of Object.entries(recoveryData.photos)) {
          if (!photoValue) continue;
          
          try {
            let fileToUpload = null;
            let fileName = `${photoType}_${Date.now()}.jpg`;
            
            // Handle different photo data formats
            if (typeof photoValue === 'string' && photoValue.startsWith('data:')) {
              // Base64 data URL
              fileToUpload = await dataURLToBlob(photoValue);
            } else if (photoValue.file) {
              // File object
              fileToUpload = photoValue.file;
              fileName = `${photoType}_${Date.now()}_${photoValue.file.name}`;
            } else if (Array.isArray(photoValue)) {
              // Array of photos (like damage photos)
              const uploadedUrls = [];
              for (let i = 0; i < photoValue.length; i++) {
                const photo = photoValue[i];
                let damageFileToUpload = null;
                let damageFileName = `${photoType}_${Date.now()}_${i}.jpg`;
                
                if (typeof photo === 'string' && photo.startsWith('data:')) {
                  damageFileToUpload = await dataURLToBlob(photo);
                } else if (photo && photo.file) {
                  damageFileToUpload = photo.file;
                  damageFileName = `${photoType}_${Date.now()}_${i}_${photo.file.name}`;
                } else if (typeof photo === 'string' && photo.startsWith('http')) {
                  // Already a URL, keep as-is
                  uploadedUrls.push(photo);
                  continue;
                }
                
                if (damageFileToUpload) {
                  const damageStorageRef = ref(storage, `recoveries/${user.id}/${recoveryVehicle.vin}/${damageFileName}`);
                  await uploadBytes(damageStorageRef, damageFileToUpload);
                  const damageUrl = await getDownloadURL(damageStorageRef);
                  uploadedUrls.push(damageUrl);
                }
              }
              uploadedPhotoURLs[photoType] = uploadedUrls;
              continue;
            } else if (typeof photoValue === 'string' && photoValue.startsWith('http')) {
              // Already a URL, keep as-is
              uploadedPhotoURLs[photoType] = photoValue;
              continue;
            }
            
            // Upload single photo
            if (fileToUpload) {
              const storageRef = ref(storage, `recoveries/${user.id}/${recoveryVehicle.vin}/${fileName}`);
              await uploadBytes(storageRef, fileToUpload);
              const downloadURL = await getDownloadURL(storageRef);
              uploadedPhotoURLs[photoType] = downloadURL;
              console.log(`Uploaded ${photoType} photo to:`, downloadURL);
            }
          } catch (uploadError) {
            console.error(`Error uploading ${photoType} photo:`, uploadError);
            // Continue with other photos even if one fails
          }
        }
        
        console.log(`Successfully uploaded recovery photos:`, uploadedPhotoURLs);
      }

      // Create sanitized recovery data with storage URLs only
      const sanitizedRecoveryData = {
        ...recoveryData,
        photos: uploadedPhotoURLs, // Use uploaded URLs instead of raw data
        vehicleCondition: recoveryData.vehicleCondition,
        personalPropertyHandled: recoveryData.personalPropertyHandled,
        personalPropertyNotes: recoveryData.personalPropertyNotes,
        checklist: recoveryData.checklist,
        notes: recoveryData.notes,
        timestamp: new Date().toISOString(),
        recoveredBy: user.displayName || user.email?.split('@')[0] || 'Team Member',
        recoveredByUserId: user.id
      };

      const existingVehicle = vehicles.find(v =>
        v.vin && v.vin.toLowerCase() === recoveryVehicle.vin.toLowerCase()
      );

      if (existingVehicle) {
        // Update existing vehicle with recovery data
        await updateDoc(doc(db, 'users', user.id, 'vehicleWeeks', selectedWeek, 'vehicles', existingVehicle.id), {
          status: 'SECURED',
          securedDate: new Date().toISOString().split('T')[0],
          securedTimestamp: new Date(),
          securedFromTeammate: true,
          position: recoveryVehicle.position || existingVehicle.position,
          // Use sanitized recovery data
          recoveryData: sanitizedRecoveryData,
          vehicleCondition: sanitizedRecoveryData.vehicleCondition,
          recoveryPhotos: uploadedPhotoURLs, // Storage URLs only
          personalPropertyHandled: sanitizedRecoveryData.personalPropertyHandled,
          personalPropertyNotes: sanitizedRecoveryData.personalPropertyNotes,
          checklist: sanitizedRecoveryData.checklist,
          recoveryNotes: sanitizedRecoveryData.notes,
          recoveryDate: new Date().toISOString(),
          recoveredBy: user.displayName || user.email?.split('@')[0] || 'Team Member',
          updatedAt: serverTimestamp()
        });

        setVehicles(vehicles.map(v =>
          v.id === existingVehicle.id
            ? {
              ...v,
              status: 'SECURED',
              securedDate: new Date().toISOString().split('T')[0],
              securedTimestamp: new Date(),
              securedFromTeammate: true,
              position: recoveryVehicle.position || v.position,
              recoveryData: sanitizedRecoveryData
            }
            : v
        ));
      } else {
        // Create new vehicle with recovery data
        const newVehicleRef = doc(collection(db, 'users', user.id, 'vehicleWeeks', selectedWeek, 'vehicles'));

        // Geocode if no position but has address data
        let position = recoveryVehicle.position;
        if (!position && (recoveryVehicle.address || recoveryVehicle.city || recoveryVehicle.fullAddress)) {
          position = await geocodeAddress(
            recoveryVehicle.address || recoveryVehicle.fullAddress,
            recoveryVehicle.city,
            recoveryVehicle.state,
            recoveryVehicle.zipCode
          );
        }

        const newVehicleData = {
          vehicle: recoveryVehicle.vehicle,
          vin: recoveryVehicle.vin,
          vinVerified: recoveryVehicle.vinVerified || false,
          plateNumber: recoveryVehicle.plateNumber || '',
          accountNumber: recoveryVehicle.accountNumber || '',
          financier: recoveryVehicle.financier || '',
          address: recoveryVehicle.address || '',
          city: recoveryVehicle.city || '',
          state: recoveryVehicle.state || '',
          zipCode: recoveryVehicle.zipCode || '',
          fullAddress: recoveryVehicle.fullAddress || buildFullAddress(recoveryVehicle.address, recoveryVehicle.city, recoveryVehicle.state, recoveryVehicle.zipCode),
          position: position || null,
          date: new Date().toISOString().split('T')[0],
          status: 'SECURED',
          securedDate: new Date().toISOString().split('T')[0],
          securedTimestamp: new Date(),
          securedFromTeammate: true,
          teamMemberId: recoveryVehicle.teamMemberId,
          teamMemberName: recoveryVehicle.teamMemberName,
          notes: recoveryVehicle.notes || '',
          images: recoveryVehicle.images || [],
          color: recoveryVehicle.color || '',
          driveType: recoveryVehicle.driveType || '',
          // Use sanitized recovery data
          recoveryData: sanitizedRecoveryData,
          vehicleCondition: sanitizedRecoveryData.vehicleCondition,
          recoveryPhotos: uploadedPhotoURLs, // Storage URLs only
          personalPropertyHandled: sanitizedRecoveryData.personalPropertyHandled,
          personalPropertyNotes: sanitizedRecoveryData.personalPropertyNotes,
          checklist: sanitizedRecoveryData.checklist,
          recoveryNotes: sanitizedRecoveryData.notes,
          recoveryDate: new Date().toISOString(),
          recoveredBy: user.displayName || user.email?.split('@')[0] || 'Team Member',
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        await setDoc(newVehicleRef, newVehicleData);

        setVehicles([
          { id: newVehicleRef.id, ...newVehicleData },
          ...vehicles
        ]);
      }

      // Update original vehicle
      if (recoveryVehicle.teamMemberId && recoveryVehicle.weekId) {
        await updateDoc(
          doc(db, 'users', recoveryVehicle.teamMemberId, 'vehicleWeeks', recoveryVehicle.weekId, 'vehicles', recoveryVehicle.id),
          {
            status: 'SECURED',
            securedDate: new Date().toISOString().split('T')[0],
            securedTimestamp: new Date(),
            securedByTeammate: true,
            securedByUserId: user.id,
            securedByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
            autoSecuredFromTeam: true,
            teamSyncTimestamp: new Date(),
            recoveryCompleted: true,
            // Store only essential recovery info, not full photo data
            recoveryCompletedBy: user.displayName || user.email?.split('@')[0] || 'Team Member',
            recoveryPhotoCount: Object.keys(uploadedPhotoURLs).length,
            hasRecoveryPhotos: Object.keys(uploadedPhotoURLs).length > 0,
            updatedAt: serverTimestamp()
          }
        );
      }

      // Mark VIN as secured across team
      if (team && recoveryVehicle.vin) {
        const userDisplayName = user.displayName || user.email?.split('@')[0] || 'Team Member';

        await markVINAsSecuredAcrossTeam(
          db,
          team.id,
          user.id,
          recoveryVehicle.vin,
          new Date().toISOString().split('T')[0],
          userDisplayName
        );
      }

      await updateVehicleWeekStats(db, user.id, selectedWeek);

      playMoneySound(soundEnabled);

      const notification = {
        id: Date.now(),
        type: 'team_sync_success',
        message: `💰 Recovery completed: ${recoveryVehicle.vehicle} from ${recoveryVehicle.teamMemberName}`,
        timestamp: new Date()
      };

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 5000);
      
      // Post to Slack with recovery details
      if (team && team.id && typeof postVehicleToSlack === 'function') {
        try {
          const slackVehicle = formatVehicleForSlack({
            ...recoveryVehicle,
            status: 'SECURED',
            securedDate: new Date().toISOString().split('T')[0],
            securedTimestamp: new Date().toISOString(),
            securedByTeammate: true,
            securedByUserId: user.id,
            securedByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
            recoveryCompleted: true,
            recoveryPhotoCount: Object.keys(uploadedPhotoURLs).length
          });
          postVehicleToSlack(team.id, slackVehicle, 'recovery_completed');
        } catch (error) {
          console.error("Error posting to Slack:", error);
        }
      }

      // Clear recovery state
      setRecoveryVehicle(null);

    } catch (error) {
      console.error("Error completing recovery:", error);

      await loadTeamVehicles();

      const notification = {
        id: Date.now(),
        type: 'team_sync_error',
        message: `❌ Failed to complete recovery: ${error.message}`,
        timestamp: new Date()
      };

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 8000);
    }
  };

  // Handle Can't Secure with reason modal
  const handleCantSecureClick = (teamVehicle) => {
    setCantSecureVehicle(teamVehicle);
    setCantSecureReason('');
    setShowCantSecureModal(true);
  };

  // Process Can't Secure with reason - Add to never secured list
  const processCantSecure = async () => {
    if (!db || !user || !selectedWeek || !team || !cantSecureVehicle || !cantSecureReason.trim()) return;

    try {
      const reasonText = cantSecureReason.trim();
      
      // Close modal
      setShowCantSecureModal(false);

      // Add to never secured list with reason
      const neverSecuredRef = doc(collection(db, 'teams', team.id, 'neverSecuredVehicles'));
      const neverSecuredData = {
        ...cantSecureVehicle,
        bottomStatus: 'CANT SECURE',
        cantSecureReason: reasonText,
        bottomStatusCount: (cantSecureVehicle.bottomStatusCount || 0) + 1,
        attemptCount: (cantSecureVehicle.bottomStatusCount || 0) + 1,
        lastAttemptDate: new Date().toISOString(),
        markedByUserId: user.id,
        markedByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
        createdAt: serverTimestamp()
      };

      await setDoc(neverSecuredRef, neverSecuredData);

      // Update the original vehicle
      await updateDoc(
        doc(db, 'users', cantSecureVehicle.teamMemberId, 'vehicleWeeks', cantSecureVehicle.weekId, 'vehicles', cantSecureVehicle.id),
        {
          bottomStatus: 'CANT SECURE',
          cantSecureReason: reasonText,
          bottomStatusCount: (cantSecureVehicle.bottomStatusCount || 0) + 1,
          bottomStatusByUserId: user.id,
          bottomStatusByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
          bottomStatusDate: new Date().toISOString(),
          isNeverSecured: true,
          neverSecuredDate: new Date().toISOString(),
          updatedAt: serverTimestamp()
        }
      );

      // Remove from team vehicles list
      setTeamVehicles(prev => {
        const newList = prev.filter(v => v.uniqueKey !== cantSecureVehicle.uniqueKey);
        lastTeamVehicleCount.current = newList.length;
        
        const vehicleKey = cantSecureVehicle.uniqueKey;
        delete vehicleReminderStatesRef.current[vehicleKey];
        delete dailyReminderSentRef.current[vehicleKey];
        
        return newList;
      });

      // Reload never secured list
      const updatedNeverSecured = await loadNeverSecuredVehicles(db, team.id);
      setNeverSecuredVehicles(updatedNeverSecured);

      const notification = {
        id: Date.now(),
        type: 'team_sync_error',
        message: `❌ ${cantSecureVehicle.vehicle} marked as CANNOT BE SECURED - Moved to Never Secured list`,
        timestamp: new Date()
      };

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 8000);

      playNotificationSound(soundEnabled);
      
      // Clear state
      setCantSecureVehicle(null);
      setCantSecureReason('');

    } catch (error) {
      console.error("Error processing can't secure:", error);
      alert("Error marking vehicle as can't secure. Please try again.");
    }
  };

  // Mark team vehicle with bottom status - DO NOT ADD TO USER'S LIST
  const handleMarkTeamVehicleBottomStatus = async (teamVehicle, bottomStatus) => {
    if (!db || !user || !selectedWeek || !team) return;

    try {
      // Special handling for "CANT SECURE" - show reason modal
      if (bottomStatus === 'CANT SECURE') {
        handleCantSecureClick(teamVehicle);
        return;
      }

      // Get current bottom status count
      const currentCount = teamVehicle.bottomStatusCount || 0;
      const newCount = currentCount + 1;

      // Check if this is the 3rd attempt - auto move to never secured
      if (newCount >= 3) {
        // Add to never secured list
        const neverSecuredRef = doc(collection(db, 'teams', team.id, 'neverSecuredVehicles'));
        const neverSecuredData = {
          ...teamVehicle,
          bottomStatus: bottomStatus,
          bottomStatusCount: newCount,
          attemptCount: newCount,
          lastAttemptDate: new Date().toISOString(),
          markedByUserId: user.id,
          markedByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
          autoMovedAfter3Attempts: true,
          createdAt: serverTimestamp()
        };

        await setDoc(neverSecuredRef, neverSecuredData);

        // Update the original vehicle
        await updateDoc(
          doc(db, 'users', teamVehicle.teamMemberId, 'vehicleWeeks', teamVehicle.weekId, 'vehicles', teamVehicle.id),
          {
            bottomStatus: bottomStatus,
            bottomStatusCount: newCount,
            bottomStatusByUserId: user.id,
            bottomStatusByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
            bottomStatusDate: new Date().toISOString(),
            isNeverSecured: true,
            neverSecuredDate: new Date().toISOString(),
            updatedAt: serverTimestamp()
          }
        );

        // Remove from team vehicles list
        setTeamVehicles(prev => {
          const newList = prev.filter(v => v.uniqueKey !== teamVehicle.uniqueKey);
          lastTeamVehicleCount.current = newList.length;
          
          const vehicleKey = teamVehicle.uniqueKey;
          delete vehicleReminderStatesRef.current[vehicleKey];
          delete dailyReminderSentRef.current[vehicleKey];
          
          return newList;
        });

        // Reload never secured list
        const updatedNeverSecured = await loadNeverSecuredVehicles(db, team.id);
        setNeverSecuredVehicles(updatedNeverSecured);

        const notification = {
          id: Date.now(),
          type: 'team_sync_error',
          message: `❌ ${teamVehicle.vehicle} marked as ${bottomStatus} (3rd attempt) - Automatically moved to Never Secured list`,
          timestamp: new Date()
        };

        setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
        setTimeout(() => {
          setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
        }, 8000);

        playNotificationSound(soundEnabled);
        return;
      }

      // Only update the original vehicle with bottom status
      await updateDoc(
        doc(db, 'users', teamVehicle.teamMemberId, 'vehicleWeeks', teamVehicle.weekId, 'vehicles', teamVehicle.id),
        {
          bottomStatus: bottomStatus,
          bottomStatusCount: newCount,
          bottomStatusByUserId: user.id,
          bottomStatusByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
          bottomStatusDate: new Date().toISOString(),
          updatedAt: serverTimestamp()
        }
      );

      const notification = {
        id: Date.now(),
        type: 'team_sync_success',
        message: `📍 Marked ${teamVehicle.vehicle} as ${bottomStatus} (Attempt ${newCount}/3)`,
        timestamp: new Date()
      };

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 5000);

      // Send immediate reminder for new bottom status
      showReminderNotification(teamVehicle, '', true);

      playNotificationSound(soundEnabled);
      
      // Post to Slack
      if (team && team.id && typeof postVehicleToSlack === 'function') {
        try {
          const slackVehicle = formatVehicleForSlack({
            ...teamVehicle,
            bottomStatus: bottomStatus,
            bottomStatusCount: newCount,
            bottomStatusByUserId: user.id,
            bottomStatusByUserName: user.displayName || user.email?.split('@')[0] || 'Team Member',
            bottomStatusDate: new Date().toISOString()
          });
          postVehicleToSlack(team.id, slackVehicle, 'bottom_status');
        } catch (error) {
          console.error("Error posting to Slack:", error);
        }
      }

    } catch (error) {
      console.error("Error marking bottom status:", error);
      alert("Error marking vehicle status. Please try again.");
    }
  };

  // Reset/Recheck team vehicle
  const handleRecheckTeamVehicle = async (teamVehicle) => {
    if (!db || !user) return;

    try {
      if (!confirm(`Are you sure you want to recheck ${teamVehicle.vehicle}? This will reset its status.`)) {
        return;
      }

      const result = await recheckTeamVehicle(db, user, teamVehicle);

      if (result.success) {
        const notification = {
          id: Date.now(),
          type: 'team_sync_success',
          message: `🔄 Reset status for ${teamVehicle.vehicle} - You can now try again`,
          timestamp: new Date()
        };

        setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
        setTimeout(() => {
          setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
        }, 5000);

        playNotificationSound(soundEnabled);
      }

    } catch (error) {
      console.error("Error resetting vehicle status:", error);
      alert("Error resetting vehicle status. Please try again.");
    }
  };

  // NEW: Load check-ins for orders
  useEffect(() => {
    if (!db || !filteredTeamOrders.length) return;

    const loadAllCheckIns = async () => {
      setLoadingOrders(true);
      const checkInsMap = {};

      for (const order of filteredTeamOrders) {
        const checkIns = await loadOrderCheckIns(order.id);
        checkInsMap[order.id] = checkIns;
      }

      setOrderCheckIns(checkInsMap);
      setLoadingOrders(false);
    };

    loadAllCheckIns();
  }, [db, filteredTeamOrders]);

  // Live timer update effect
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(Date.now());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Daily reminder check for bottom status vehicles
  useEffect(() => {
    const checkDailyReminders = () => {
      const today = new Date().toDateString();
      
      teamVehicles.forEach(vehicle => {
        if (vehicle.bottomStatus) {
          const vehicleKey = vehicle.uniqueKey || `${vehicle.teamMemberId}_${vehicle.weekId}_${vehicle.id}`;
          
          if (!dailyReminderSentRef.current[vehicleKey] || dailyReminderSentRef.current[vehicleKey] !== today) {
            dailyReminderSentRef.current[vehicleKey] = today;
            showReminderNotification(vehicle, '', true);
          }
        }
      });
    };
    
    checkDailyReminders();
    
    const interval = setInterval(checkDailyReminders, 3600000);
    
    return () => clearInterval(interval);
  }, [teamVehicles]);

  // Timer check interval for regular reminders
  useEffect(() => {
    if (!teamVehicles || teamVehicles.length === 0) return;

    const interval = setInterval(() => {
      teamVehicles.forEach(vehicle => {
        if (!vehicle.bottomStatus && !vehicle.inRouteDriverId && !vehicle.arrivedDriverId) {
          checkTeamVehicleReminders(vehicle);
        }
      });
    }, 1800000);

    return () => clearInterval(interval);
  }, [teamVehicles]);

  // NEW: Handle vehicle selection from map
  useEffect(() => {
    if (selectedVehicleId && teamVehicles.length > 0) {
      const vehicleIndex = teamVehicles.findIndex(v => v.uniqueKey === selectedVehicleId);
      
      if (vehicleIndex !== -1) {
        if (vehicleIndex !== currentVehicleIndex || activeTab !== 'vehicles') {
          setActiveTab('vehicles');
          setCurrentVehicleIndex(vehicleIndex);
          
          // Show notification that vehicle was selected from map
          const selectedVehicle = teamVehicles[vehicleIndex];
          setMapSelectedNotification({
            vehicle: selectedVehicle.vehicle,
            vin: selectedVehicle.vin,
            type: 'vehicle'
          });
          
          // Auto-hide notification after 3 seconds
          setTimeout(() => {
            setMapSelectedNotification(null);
          }, 3000);
        }
      }
    }
  }, [selectedVehicleId, teamVehicles, currentVehicleIndex, activeTab]);

  // NEW: Handle order selection from map
  useEffect(() => {
    if (selectedOrderId && filteredTeamOrders.length > 0) {
      const orderIndex = filteredTeamOrders.findIndex(o => o.id === selectedOrderId);
      
      if (orderIndex !== -1) {
        if (orderIndex !== currentOrderIndex || activeTab !== 'orders') {
          setActiveTab('orders');
          setCurrentOrderIndex(orderIndex);
          
          // Show notification that order was selected from map
          const selectedOrder = filteredTeamOrders[orderIndex];
          setMapSelectedNotification({
            vehicle: `${selectedOrder.year} ${selectedOrder.make} ${selectedOrder.model}`,
            vin: selectedOrder.vin,
            type: 'order'
          });
          
          // Auto-hide notification after 3 seconds
          setTimeout(() => {
            setMapSelectedNotification(null);
          }, 3000);
        }
      }
    }
  }, [selectedOrderId, filteredTeamOrders, currentOrderIndex, activeTab]);

  // Initial setup
  useEffect(() => {
    if (db && team && user) {
      setupTeamVehicleListeners();
      loadNeverSecuredVehicles(db, team.id).then(vehicles => {
        setNeverSecuredVehicles(vehicles);
      });
    }

    return () => {
      Object.keys(teamVehicleTimerRefs.current).forEach(vehicleId => {
        if (teamVehicleTimerRefs.current[vehicleId]?.interval) {
          clearInterval(teamVehicleTimerRefs.current[vehicleId].interval);
        }
      });
      teamVehicleListenersRef.current.forEach(unsubscribe => {
        if (typeof unsubscribe === 'function') {
          unsubscribe();
        }
      });
    };
  }, [db, team, user]);

  // Update parent component with team vehicles
  useEffect(() => {
    if (onTeamVehiclesUpdate && typeof onTeamVehiclesUpdate === 'function') {
      onTeamVehiclesUpdate(teamVehicles);
    }
  }, [teamVehicles, onTeamVehiclesUpdate]);

  // Reset current index when items change (unless selected from map)
  useEffect(() => {
    if (activeTab === 'vehicles' && !selectedVehicleId) {
      setCurrentVehicleIndex(0);
      
      // Auto-select first vehicle on map when team vehicles are loaded
      if (teamVehicles.length > 0 && onVehicleSelect && typeof onVehicleSelect === 'function') {
        const firstVehicle = teamVehicles[0];
        onVehicleSelect(firstVehicle.uniqueKey);
      }
    } else if (activeTab === 'orders' && !selectedOrderId) {
      setCurrentOrderIndex(0);
      
      // Auto-select first order on map when orders are loaded
      if (filteredTeamOrders.length > 0 && onOrderSelect && typeof onOrderSelect === 'function') {
        const firstOrder = filteredTeamOrders[0];
        onOrderSelect(firstOrder.id);
      }
    }
  }, [teamVehicles.length, filteredTeamOrders.length, activeTab, selectedVehicleId, selectedOrderId, onVehicleSelect, onOrderSelect]);

  if (!team) return null;

  const currentVehicle = teamVehicles[currentVehicleIndex];
  const currentOrder = filteredTeamOrders[currentOrderIndex];

  return (
    <div className="mb-4">
      <div className="bg-gradient-to-r from-purple-800 to-purple-900 rounded-xl shadow-xl backdrop-blur-lg border border-purple-600 overflow-hidden">
        <div className="p-4">
          {/* Header with Tab Navigation */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-4">
              <h3 className="text-lg font-bold text-white flex items-center">
                <span className="mr-2 text-2xl">👥</span>
                Team Tracker
              </h3>
              
              {/* Tab Navigation */}
              <div className="flex bg-gray-800 rounded-lg p-1">
                <button
                  onClick={() => setActiveTab('vehicles')}
                  className={`px-4 py-2 rounded-md text-sm font-semibold transition-all ${
                    activeTab === 'vehicles'
                      ? 'bg-purple-600 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                  }`}
                >
                  🚗 Vehicles ({teamVehicles.length})
                </button>
                <button
                  onClick={() => setActiveTab('orders')}
                  className={`px-4 py-2 rounded-md text-sm font-semibold transition-all ${
                    activeTab === 'orders'
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                  }`}
                >
                  📋 Orders ({filteredTeamOrders.length})
                </button>
              </div>

              {connectionStatus === 'error' && (
                <span className="text-xs text-red-400">
                  ⚠️ Connection Error - Retrying...
                </span>
              )}
              {connectionStatus === 'connecting' && (
                <span className="text-xs text-yellow-400 animate-pulse">
                  🔄 Connecting...
                </span>
              )}
            </div>
            
            <div className="flex space-x-2">
              <button 
                onClick={() => setShowNeverSecured(!showNeverSecured)}
                className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-semibold shadow-lg transition-all"
              >
                {showNeverSecured ? '❌ Hide' : '📋'} Never Secured ({neverSecuredVehicles.length})
              </button>
            </div>
          </div>

          {/* Map selection notification */}
          {mapSelectedNotification && (
            <div className="mb-3 bg-yellow-600 bg-opacity-20 border border-yellow-500 rounded-lg p-3">
              <div className="flex items-center">
                <span className="mr-2">{mapSelectedNotification.type === 'order' ? '📋' : '🗺️'}</span>
                <span className="text-yellow-200 font-semibold">
                  Selected from map: {mapSelectedNotification.vehicle} (VIN: {mapSelectedNotification.vin})
                </span>
              </div>
            </div>
          )}

          {/* Driver Type Info */}
          <div className="mb-3 text-sm text-purple-200">
            <span className="inline-flex items-center bg-purple-700 bg-opacity-50 px-3 py-1 rounded-lg">
              <span className="mr-2">💡</span>
              {isCameraCarDriver ? 
                'Camera car driver - you can check in on orders to help tow truck drivers locate vehicles' :
                'Tow truck driver - claim vehicles for pickup and secure them'
              }
            </span>
          </div>

          {showNeverSecured && (
            <div className="mb-4 bg-red-900 bg-opacity-30 rounded-xl p-4 border border-red-600">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-lg font-bold text-red-200">Never Secured Vehicles (Unable to Secure)</h4>
              </div>
              {neverSecuredVehicles.length === 0 ? (
                <p className="text-red-100 text-center py-4">No vehicles marked as never secured</p>
              ) : (
                <div className="space-y-2 min-h-[200px] max-h-[400px] md:max-h-[600px] overflow-y-auto p-2 overscroll-contain">
                  {neverSecuredVehicles.map(vehicle => (
                    <div key={vehicle.id} className="bg-gray-800 bg-opacity-90 rounded-lg p-3 border border-red-600">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <p className="font-bold text-white">{vehicle.vehicle}</p>
                          <p className="text-sm text-gray-300">VIN: {vehicle.vin}</p>
                          <p className="text-xs text-gray-400">From: {vehicle.teamMemberName}</p>
                          <p className="text-xs text-red-400">Status: {vehicle.bottomStatus}</p>
                          {vehicle.cantSecureReason && (
                            <p className="text-xs text-yellow-400 mt-1">
                              <strong>Reason:</strong> {vehicle.cantSecureReason}
                            </p>
                          )}
                          {vehicle.deleteReason && (
                            <p className="text-xs text-orange-400 mt-1">
                              <strong>Removal Reason:</strong> {vehicle.deleteReason}
                            </p>
                          )}
                          <p className="text-xs text-gray-400">Last Attempt: {new Date(vehicle.lastAttemptDate).toLocaleDateString()}</p>
                          {vehicle.markedByUserName && (
                            <p className="text-xs text-gray-400">Marked by: {vehicle.markedByUserName}</p>
                          )}
                          {vehicle.deletedByUserName && (
                            <p className="text-xs text-gray-400">Removed by: {vehicle.deletedByUserName}</p>
                          )}
                        </div>
                        <div className="text-right">
                          <span className="bg-red-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                            {vehicle.bottomStatus === 'CANT SECURE' ? 'UNABLE TO SECURE' : 
                             vehicle.bottomStatus === 'MANUALLY REMOVED' ? 'MANUALLY REMOVED' :
                             `${vehicle.attemptCount || vehicle.bottomStatusCount || 1} Attempts`}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Content based on active tab */}
          {activeTab === 'vehicles' ? (
            // VEHICLES TAB
            <>
              {loadingTeamVehicles ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-400 mx-auto mb-3"></div>
                  <p className="text-purple-200 text-sm">Loading team vehicles...</p>
                </div>
              ) : teamVehicles.length === 0 ? (
                <div className="bg-purple-700 bg-opacity-30 rounded-lg p-6 text-center">
                  <span className="text-5xl mb-3 block">🎉</span>
                  <p className="text-purple-100 font-semibold">All team vehicles secured!</p>
                  <p className="text-purple-200 text-sm mt-1">Great teamwork!</p>
                  {connectionStatus === 'error' && (
                    <p className="text-red-400 text-xs mt-2">⚠️ Connection issues - data might be incomplete</p>
                  )}
                </div>
              ) : (
                <div className="relative">
                  {/* Navigation controls */}
                  <div className="flex justify-between items-center mb-3">
                    <button
                      onClick={navigateToPrevious}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-semibold shadow-lg transition-all flex items-center"
                      disabled={teamVehicles.length <= 1}
                    >
                      <span className="mr-2">⬆️</span>
                      Previous
                    </button>
                    <span className="text-white font-semibold">
                      Vehicle {currentVehicleIndex + 1} of {teamVehicles.length}
                      {selectedVehicleId && (
                        <span className="ml-2 text-yellow-400 text-xs">
                          🗺️ (Selected from map)
                        </span>
                      )}
                    </span>
                    <button
                      onClick={navigateToNext}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-semibold shadow-lg transition-all flex items-center"
                      disabled={teamVehicles.length <= 1}
                    >
                      Next
                      <span className="ml-2">⬇️</span>
                    </button>
                  </div>

                  {/* Single vehicle card */}
                  {currentVehicle && (
                    <div className="h-full">
                      {(() => {
                        const vehicle = currentVehicle;
                        const isInRoute = vehicle.inRouteDriverId === user.id;
                        const hasArrived = vehicle.arrivedDriverId === user.id;
                        const someoneElseInRoute = vehicle.inRouteDriverId && vehicle.inRouteDriverId !== user.id;
                        const someoneElseArrived = vehicle.arrivedDriverId && vehicle.arrivedDriverId !== user.id;
                        const eta = vehicle.inRouteTimestamp ? calculateETA(vehicle.inRouteTimestamp) : null;
                        const timeToETA = eta ? getTimeToETA(eta) : null;

                        return (
<div 
                            className={`bg-gray-800 bg-opacity-90 rounded-xl p-4 shadow-lg border ${
                              vehicle.bottomStatus ? 'border-red-600 opacity-75' : 
                              selectedVehicleId === vehicle.uniqueKey ? 'border-yellow-500 border-2' :
                              'border-gray-700 hover:border-purple-500'
                            } transition-all`}
                          >
                            {/* Vehicle Header */}
                            <div className="flex justify-between items-start mb-3">
                              <div className="flex-1">
                                <h4 className="font-bold text-lg text-white flex items-center">
                                  {vehicle.vehicle}
                                  {selectedVehicleId === vehicle.uniqueKey && (
                                    <span className="ml-2 bg-yellow-600 text-black px-2 py-1 rounded-full text-xs font-bold animate-pulse">
                                      🗺️ SELECTED
                                    </span>
                                  )}
                                  {vehicle.status === 'PENDING PICKUP' && (
                                    <span className="ml-2 bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                                      PENDING PICKUP
                                    </span>
                                  )}
                                  {vehicle.status === 'DO NOT SECURE' && (
                                    <span className="ml-2 bg-purple-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                                      DO NOT SECURE
                                    </span>
                                  )}
                                </h4>
                                <p className="text-sm text-purple-300">
                                  <span className="font-semibold">From:</span> {vehicle.teamMemberName}
                                </p>
                                <p className="text-xs text-gray-400 flex items-center">
                                  <span>{vehicle.weekRange} • {vehicle.date}</span>
                                  {vehicle.createdAt && (
                                    <span className="ml-2 text-gray-500">
                                      • Added {formatTimestamp(vehicle.createdAt)}
                                    </span>
                                  )}
                                </p>
                              </div>
                              <div className="text-right">
                                <span className="inline-block bg-red-600 text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                                  VIN: {vehicle.vin}
                                </span>
                                <div className="mt-1 text-xs">
                                  {vehicle.vinVerified ? (
                                    <span className="inline-block bg-green-600 text-white px-2 py-1 rounded-full font-bold">
                                      ✓ VIN Verified
                                    </span>
                                  ) : (
                                    <span className="inline-block bg-gray-600 text-white px-2 py-1 rounded-full">
                                      VIN Not Verified
                                    </span>
                                  )}
                                </div>
                                {vehicle.position ? (
                                  <div className="mt-1 text-green-400 text-xs font-semibold">
                                    📍 GPS Available
                                  </div>
                                ) : (vehicle.address || vehicle.city || vehicle.fullAddress) ? (
                                  <div className="mt-1 text-yellow-400 text-xs font-semibold">
                                    ⏳ Geocoding...
                                  </div>
                                ) : null}
                                {vehicle.bottomStatus && (
                                  <div className="mt-1 bg-red-900 text-red-200 px-2 py-1 rounded-full text-xs font-bold">
                                    {vehicle.bottomStatus} ({vehicle.bottomStatusCount || 1}/3)
                                  </div>
                                )}
                                {getTimerDisplay(vehicle) && !vehicle.bottomStatus && !vehicle.inRouteDriverId && !vehicle.arrivedDriverId && (
                                  <div className="mt-1 text-orange-400 text-xs font-semibold" key={currentTime}>
                                    ⏱️ {getTimerDisplay(vehicle)}
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Action buttons row */}
                            <div className="mb-3 flex space-x-2">
                              <button
                                onClick={() => exportVehicleCard(vehicle)}
                                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm font-semibold shadow-lg transition-all flex items-center justify-center"
                                title="Export this vehicle card as image for texting"
                              >
                                <span className="mr-2">📸</span>
                                Export Card
                              </button>
                              <button
                                onClick={() => handleDeleteVehicleClick(vehicle)}
                                className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-semibold shadow-lg transition-all flex items-center justify-center"
                                title="Remove this vehicle from team tracker and move to Never Secured list"
                              >
                                <span className="mr-1">🗑️</span>
                                Delete
                              </button>
                            </div>

                            {/* DO NOT SECURE Reason Display */}
                            {vehicle.status === 'DO NOT SECURE' && vehicle.doNotSecureReason && (
                              <div className="mb-3 bg-purple-900 bg-opacity-40 rounded-lg p-3 border border-purple-600">
                                <p className="text-purple-300 text-xs font-semibold mb-1">
                                  🚫 DO NOT SECURE - REASON:
                                </p>
                                <p className="text-purple-100 font-bold">
                                  {vehicle.doNotSecureReason}
                                </p>
                              </div>
                            )}

                            {/* Bottom Status Daily Reminder Notice */}
                            {vehicle.bottomStatus && (
                              <div className="mb-3 bg-yellow-900 bg-opacity-40 rounded-lg p-2 border border-yellow-600">
                                <p className="text-yellow-300 text-xs font-semibold">
                                  ⚠️ Daily reminders active - Attempt {vehicle.bottomStatusCount || 1}/3
                                </p>
                                {vehicle.cantSecureReason && (
                                  <p className="text-yellow-200 text-xs mt-1">
                                    <strong>Reason:</strong> {vehicle.cantSecureReason}
                                  </p>
                                )}
                              </div>
                            )}

                            {/* Live Tracking Status */}
                            {(vehicle.inRouteDriverId || vehicle.arrivedDriverId) && (
                              <div className={`mb-3 p-3 rounded-lg ${
                                vehicle.arrivedDriverId ? 'bg-green-900 bg-opacity-40 border border-green-600' :
                                'bg-blue-900 bg-opacity-40 border border-blue-600'
                              }`}>
                                <div className="flex items-center justify-between">
                                  <div>
                                    {vehicle.arrivedDriverId ? (
                                      <>
                                        <p className="text-green-300 font-bold flex items-center">
                                          <span className="mr-2 text-lg">📍</span>
                                          DRIVER ARRIVED
                                        </p>
                                        <p className="text-green-200 text-sm">
                                          {vehicle.arrivedDriverName} has arrived at location
                                        </p>
                                        {vehicle.arrivedTimestamp && (
                                          <p className="text-green-300 text-xs mt-1">
                                            Arrived {formatTimestamp(vehicle.arrivedTimestamp)}
                                          </p>
                                        )}
                                      </>
                                    ) : vehicle.inRouteDriverId ? (
                                      <>
                                        <p className="text-blue-300 font-bold flex items-center">
                                          <span className="mr-2 text-lg animate-pulse">🚚</span>
                                          DRIVER IN ROUTE
                                        </p>
                                        <p className="text-blue-200 text-sm">
                                          {vehicle.inRouteDriverName} is on the way
                                        </p>
                                        {vehicle.inRouteTimestamp && (
                                          <p className="text-blue-300 text-xs mt-1">
                                            Departed {formatTimestamp(vehicle.inRouteTimestamp)}
                                          </p>
                                        )}
                                      </>
                                    ) : null}
                                  </div>
                                  {vehicle.inRouteDriverId && !vehicle.arrivedDriverId && timeToETA && (
                                    <div className="text-right" key={currentTime}>
                                      <p className="text-yellow-400 font-bold text-lg">{timeToETA}</p>
                                      <p className="text-yellow-300 text-xs">ETA</p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Vehicle Details Grid */}
                            <div className="grid grid-cols-2 gap-3 mb-3 text-sm">
                              {vehicle.color && (
                                <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                                  <p className="text-gray-400 text-xs">Color</p>
                                  <p className="text-pink-400 font-semibold">{vehicle.color}</p>
                                </div>
                              )}
                              {vehicle.driveType && (
                                <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                                  <p className="text-gray-400 text-xs">Drive Type</p>
                                  <p className="text-purple-400 font-semibold">{vehicle.driveType}</p>
                                </div>
                              )}
                              {vehicle.plateNumber && (
                                <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                                  <p className="text-gray-400 text-xs">Plate #</p>
                                  <p className="text-orange-400 font-semibold">{vehicle.plateNumber}</p>
                                </div>
                              )}
                              {vehicle.accountNumber && (
                                <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                                  <p className="text-gray-400 text-xs">Account #</p>
                                  <p className="text-blue-400 font-semibold">{vehicle.accountNumber}</p>
                                </div>
                              )}
                              {vehicle.financier && (
                                <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2 col-span-2">
                                  <p className="text-gray-400 text-xs">Financier</p>
                                  <p className="text-yellow-400 font-semibold">{vehicle.financier}</p>
                                </div>
                              )}
                            </div>

                            {/* Address - Display structured address or fallback */}
                            {(vehicle.address || vehicle.fullAddress) && (
                              <div 
                                className="bg-blue-900 bg-opacity-40 rounded-lg p-3 mb-3 border border-blue-600 cursor-pointer hover:bg-blue-800 hover:bg-opacity-50 transition-all"
                                onClick={() => navigateToAddress(vehicle.fullAddress || buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode) || vehicle.address)}
                              >
                                <p className="text-blue-300 text-xs font-semibold mb-1 flex items-center">
                                  <span className="mr-1">📍</span>
                                  LOCATION (TAP TO NAVIGATE)
                                </p>
                                <p className="text-white font-bold text-sm">
                                  {vehicle.fullAddress || buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode) || vehicle.address}
                                </p>
                                {vehicle.city && vehicle.state && (
                                  <p className="text-blue-200 text-xs mt-1">
                                    City: {vehicle.city}, State: {vehicle.state}
                                    {vehicle.zipCode && ` • ZIP: ${vehicle.zipCode}`}
                                  </p>
                                )}
                              </div>
                            )}

                            {/* Images Section */}
                            {vehicle.images && vehicle.images.length > 0 && (
                              <div className="mb-3">
                                <p className="text-gray-400 text-xs font-semibold mb-2 flex items-center">
                                  <span className="mr-1">📸</span>
                                  PHOTOS ({vehicle.images.length})
                                </p>
                                <div className="grid grid-cols-4 gap-2">
                                  {vehicle.images.slice(0, 4).map((image, idx) => (
                                    <div 
                                      key={idx}
                                      className="aspect-square rounded-lg overflow-hidden cursor-pointer border border-gray-600 hover:border-blue-500 transition-all"
                                      onClick={() => showImageInModal(image.url)}
                                    >
                                      <img 
                                        src={image.url} 
                                        alt={`Vehicle ${idx + 1}`}
                                        className="w-full h-full object-cover"
                                      />
                                    </div>
                                  ))}
                                  {vehicle.images.length > 4 && (
                                    <div 
                                      className="aspect-square rounded-lg bg-gray-700 flex items-center justify-center cursor-pointer border border-gray-600 hover:border-blue-500 transition-all"
                                      onClick={() => showImageInModal(vehicle.images[4].url)}
                                    >
                                      <span className="text-gray-300 text-sm font-semibold">
                                        +{vehicle.images.length - 4} more
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Notes */}
                            {vehicle.notes && (
                              <div className="bg-gray-700 bg-opacity-50 rounded-lg p-3 mb-3">
                                <p className="text-gray-400 text-xs font-semibold mb-1">📝 Notes</p>
                                <p className="text-gray-200 text-sm">{vehicle.notes}</p>
                              </div>
                            )}

                            {/* Action Buttons - Always allow tow truck drivers to claim */}
                            {canSecureVehicles ? (
                              <div className="space-y-2">
                                {/* Recheck button for vehicles with bottom status */}
                                {vehicle.bottomStatus && (
                                  <button
                                    onClick={() => handleRecheckTeamVehicle(vehicle)}
                                    className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-3 rounded-lg font-bold shadow-lg flex items-center justify-center transition-all transform hover:scale-105"
                                  >
                                    <span className="mr-2 text-xl">🔄</span>
                                    RECHECK THIS VEHICLE (Reset Status)
                                  </button>
                                )}

                                {/* Show different buttons based on status */}
                                {!vehicle.inRouteDriverId && !vehicle.arrivedDriverId ? (
                                  // No one has claimed it yet
                                  <button
                                    onClick={() => markInRoute(vehicle)}
                                    className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-3 rounded-lg font-bold shadow-lg flex items-center justify-center transition-all transform hover:scale-105"
                                  >
                                    <span className="mr-2 text-xl">🚚</span>
                                    MARK IN ROUTE
                                  </button>
                                ) : isInRoute && !hasArrived ? (
                                  // Current user is in route
                                  <button
                                    onClick={() => markArrived(vehicle)}
                                    className="w-full bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white py-3 rounded-lg font-bold shadow-lg flex items-center justify-center transition-all transform hover:scale-105"
                                  >
                                    <span className="mr-2 text-xl">📍</span>
                                    MARK ARRIVED
                                  </button>
                                ) : hasArrived ? (
                                  // Current user has arrived - show action buttons
                                  <>
                                    <button
                                      onClick={() => secureTeammateVehicle(vehicle)}
                                      className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3 rounded-lg font-bold shadow-lg flex items-center justify-center transition-all transform hover:scale-105"
                                    >
                                      <span className="mr-2 text-xl">🔒</span>
                                      SECURE THIS VEHICLE
                                    </button>
                                    
                                    {/* Bottom Status Options */}
                                    <div className="grid grid-cols-2 gap-2">
                                      <button
                                        onClick={() => handleMarkTeamVehicleBottomStatus(vehicle, 'GONE')}
                                        className="bg-red-600 hover:bg-red-700 text-white py-2 rounded-lg text-sm font-semibold"
                                      >
                                        Gone on Arrival
                                      </button>
                                      <button
                                        onClick={() => handleMarkTeamVehicleBottomStatus(vehicle, 'BLOCKED IN')}
                                        className="bg-orange-600 hover:bg-orange-700 text-white py-2 rounded-lg text-sm font-semibold"
                                      >
                                        Blocked In
                                      </button>
                                      <button
                                        onClick={() => handleCantSecureClick(vehicle)}
                                        className="bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg text-sm font-semibold"
                                      >
                                        Can't Secure
                                      </button>
                                      <button
                                        onClick={() => handleMarkTeamVehicleBottomStatus(vehicle, 'DEBTOR INTERFERENCE')}
                                        className="bg-purple-600 hover:bg-purple-700 text-white py-2 rounded-lg text-sm font-semibold"
                                      >
                                        Debtor Interference
                                      </button>
                                    </div>
                                  </>
                                ) : (
                                  // Someone else is handling it
                                  <div className="bg-gray-700 rounded-lg p-3 text-center">
                                    <p className="text-gray-300 text-sm">
                                      {someoneElseArrived ? 
                                        `🚧 ${vehicle.arrivedDriverName} is handling this vehicle` : 
                                        `🚚 ${vehicle.inRouteDriverName} is on the way`
                                      }
                                    </p>
                                  </div>
                                )}
                              </div>
                            ) : (
                              <div className="bg-gray-700 rounded-lg p-3 text-center">
                                <p className="text-gray-300 text-sm">
                                  {vehicle.bottomStatus ? 
                                    `⚠️ Status: ${vehicle.bottomStatus} (${vehicle.bottomStatusCount || 1}/3)` : 
                                    vehicle.status === 'DO NOT SECURE' ?
                                    `🚫 DO NOT SECURE: ${vehicle.doNotSecureReason || 'No reason provided'}` :
                                    '🔒 Tow truck drivers only'
                                  }
                                </p>
                              </div>
                            )}
                          </div>
                        );
                      })()}
                    </div>
                  )}
                </div>
              )}
            </>
          ) : (
            // ORDERS TAB - NEW IMPLEMENTATION
            <>
              {loadingOrders ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-400 mx-auto mb-3"></div>
                  <p className="text-blue-200 text-sm">Loading team orders...</p>
                </div>
              ) : filteredTeamOrders.length === 0 ? (
                <div className="bg-blue-700 bg-opacity-30 rounded-lg p-6 text-center">
                  <span className="text-5xl mb-3 block">📋</span>
                  <p className="text-blue-100 font-semibold">No open orders found</p>
                  <p className="text-blue-200 text-sm mt-1">All orders have been secured!</p>
                </div>
              ) : (
                <div className="relative">
                  {/* Navigation controls */}
                  <div className="flex justify-between items-center mb-3">
                    <button
                      onClick={navigateToPrevious}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-semibold shadow-lg transition-all flex items-center"
                      disabled={filteredTeamOrders.length <= 1}
                    >
                      <span className="mr-2">⬆️</span>
                      Previous
                    </button>
                    <span className="text-white font-semibold">
                      Order {currentOrderIndex + 1} of {filteredTeamOrders.length}
                      {selectedOrderId && (
                        <span className="ml-2 text-yellow-400 text-xs">
                          🗺️ (Selected from map)
                        </span>
                      )}
                    </span>
                    <button
                      onClick={navigateToNext}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-semibold shadow-lg transition-all flex items-center"
                      disabled={filteredTeamOrders.length <= 1}
                    >
                      Next
                      <span className="ml-2">⬇️</span>
                    </button>
                  </div>

                  {/* Single order card */}
                  {currentOrder && (
                    <div className="h-full">
                      {(() => {
                        const order = currentOrder;
                        const statusInfo = ORDER_STATUS_MAPPING[order.status] || { label: order.status || 'Unknown', class: 'open' };
                        const checkIns = orderCheckIns[order.id] || [];

                        // Format address helper for orders
                        const formatOrderAddress = (address) => {
                          if (!address) return 'N/A';
                          if (typeof address === 'string') return address;

                          let parts = [];
                          if (address.street) parts.push(address.street);

                          let cityStateZip = '';
                          if (address.city) cityStateZip += address.city;
                          if (address.state) {
                            if (cityStateZip) cityStateZip += ', ';
                            cityStateZip += address.state;
                          }
                          if (address.zip) {
                            if (cityStateZip) cityStateZip += ' ';
                            cityStateZip += address.zip;
                          }

                          if (cityStateZip) parts.push(cityStateZip);
                          return parts.join(', ');
                        };

                        return (
                          <div 
                            className={`bg-gray-800 bg-opacity-90 rounded-xl p-4 shadow-lg border ${
                              selectedOrderId === order.id ? 'border-yellow-500 border-2' :
                              'border-gray-700 hover:border-blue-500'
                            } transition-all`}
                          >
                            {/* Order Header */}
                            <div className="flex justify-between items-start mb-3">
                              <div className="flex-1">
                                <h4 className="font-bold text-lg text-white flex items-center">
                                  📋 {order.year} {order.make} {order.model}
                                  {selectedOrderId === order.id && (
                                    <span className="ml-2 bg-yellow-600 text-black px-2 py-1 rounded-full text-xs font-bold animate-pulse">
                                      🗺️ SELECTED
                                    </span>
                                  )}
                                  <span className={`ml-2 px-2 py-1 rounded-full text-xs font-bold ${
                                    statusInfo.class === 'open' ? 'bg-blue-600 text-white' :
                                    statusInfo.class === 'pending' ? 'bg-yellow-600 text-white' :
                                    statusInfo.class === 'secure' ? 'bg-green-600 text-white' :
                                    statusInfo.class === 'claim' ? 'bg-pink-600 text-white' :
                                    statusInfo.class === 'restricted' ? 'bg-red-600 text-white' :
                                    'bg-gray-600 text-white'
                                  }`}>
                                    {statusInfo.label}
                                  </span>
                                </h4>
                                {order.customerName && (
                                  <p className="text-sm text-blue-300">
                                    <span className="font-semibold">Customer:</span> {order.customerName}
                                  </p>
                                )}
                                {order.caseNumber && (
                                  <p className="text-xs text-gray-400">
                                    Case #: {order.caseNumber}
                                  </p>
                                )}
                              </div>
                              <div className="text-right">
                                <span className="inline-block bg-red-600 text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                                  VIN: {order.vin}
                                </span>
                                {order.licensePlate && (
                                  <div className="mt-1">
                                    <span className="inline-block bg-yellow-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                                      Plate: {order.licensePlate}
                                    </span>
                                  </div>
                                )}
                                {/* Check-in counters */}
                                <div className="mt-2 space-y-1">
                                  {getActionCounter(order, 'not_present') > 0 && (
                                    <div className="text-orange-400 text-xs font-semibold">
                                      ⏰ Not Present: {getActionCounter(order, 'not_present')}
                                    </div>
                                  )}
                                  {getActionCounter(order, 'blocked') > 0 && (
                                    <div className="text-red-400 text-xs font-semibold">
                                      🚧 Blocked: {getActionCounter(order, 'blocked')}
                                    </div>
                                  )}
                                  {getActionCounter(order, 'located') > 0 && (
                                    <div className="text-green-400 text-xs font-semibold">
                                      ✅ Located: {getActionCounter(order, 'located')}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>

                            {/* Camera Car Actions - Only show for camera car drivers */}
                            {isCameraCarDriver && (
                              <div className="mb-4 grid grid-cols-1 sm:grid-cols-3 gap-2">
                                <button
                                  onClick={() => handleCameraAction('located', order)}
                                  className="bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-lg text-sm font-semibold transition-all flex items-center justify-center relative"
                                >
                                  <span className="mr-1">✅</span>
                                  Vehicle Located
                                  {getActionCounter(order, 'located') > 0 && (
                                    <span className="absolute -top-2 -right-2 bg-green-800 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center font-bold border border-white">
                                      {getActionCounter(order, 'located')}
                                    </span>
                                  )}
                                </button>

                                <button
                                  onClick={() => handleCameraAction('not_present', order)}
                                  className="bg-orange-600 hover:bg-orange-700 text-white py-2 px-3 rounded-lg text-sm font-semibold transition-all flex items-center justify-center relative"
                                >
                                  <span className="mr-1">⏰</span>
                                  Not Present
                                  {getActionCounter(order, 'not_present') > 0 && (
                                    <span className="absolute -top-2 -right-2 bg-orange-800 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center font-bold border border-white">
                                      {getActionCounter(order, 'not_present')}
                                    </span>
                                  )}
                                </button>

                                <button
                                  onClick={() => handleCameraAction('blocked', order)}
                                  className="bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded-lg text-sm font-semibold transition-all flex items-center justify-center relative"
                                >
                                  <span className="mr-1">🚧</span>
                                  Blocked In
                                  {getActionCounter(order, 'blocked') > 0 && (
                                    <span className="absolute -top-2 -right-2 bg-red-800 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center font-bold border border-white">
                                      {getActionCounter(order, 'blocked')}
                                    </span>
                                  )}
                                </button>
                              </div>
                            )}

                            {/* Order Details Grid */}
                            <div className="grid grid-cols-2 gap-3 mb-3 text-sm">
                              {order.color && (
                                <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                                  <p className="text-gray-400 text-xs">Color</p>
                                  <p className="text-pink-400 font-semibold">{order.color}</p>
                                </div>
                              )}
                              {order.make && (
                                <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                                  <p className="text-gray-400 text-xs">Make</p>
                                  <p className="text-blue-400 font-semibold">{order.make}</p>
                                </div>
                              )}
                              {order.model && (
                                <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                                  <p className="text-gray-400 text-xs">Model</p>
                                  <p className="text-green-400 font-semibold">{order.model}</p>
                                </div>
                              )}
                              {order.year && (
                                <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                                  <p className="text-gray-400 text-xs">Year</p>
                                  <p className="text-purple-400 font-semibold">{order.year}</p>
                                </div>
                              )}
                            </div>

                            {/* Addresses Section with numbered badges */}
                            {order.addresses && order.addresses.length > 0 && (
                              <div className="mb-3 bg-blue-900 bg-opacity-40 rounded-lg p-3 border border-blue-600">
                                <p className="text-blue-300 text-xs font-semibold mb-2 flex items-center">
                                  <span className="mr-1">📍</span>
                                  {order.addresses.length > 1 ? `Multiple Addresses (${order.addresses.length})` : 'Address'}
                                </p>
                                {order.addresses.map((address, index) => (
                                  <div 
                                    key={index} 
                                    className="mb-2 last:mb-0 p-2 bg-gray-800 rounded-lg cursor-pointer hover:bg-gray-700 transition-all"
                                    onClick={() => {
                                      const addressText = formatOrderAddress(address);
                                      if (addressText !== 'N/A') {
                                        navigateToAddress(addressText);
                                      }
                                    }}
                                  >
                                    <div className="flex items-start gap-2">
                                      {order.addresses.length > 1 && (
                                        <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                                          {index + 1}
                                        </div>
                                      )}
                                      <div className="flex-1">
                                        <p className="text-white font-semibold text-sm">
                                          {formatOrderAddress(address)}
                                        </p>
                                        {address.position && address.position.lat && address.position.lng && (
                                          <p className="text-green-400 text-xs mt-1">
                                            📍 GPS: {address.position.lat.toFixed(6)}, {address.position.lng.toFixed(6)}
                                          </p>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                                {order.addresses.length > 1 && (
                                  <div className="mt-2 p-2 bg-yellow-900 bg-opacity-30 rounded border border-yellow-600">
                                    <p className="text-yellow-300 text-xs">
                                      💡 Multiple locations - each has a numbered marker on the map
                                    </p>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Check-in History */}
                            {checkIns.length > 0 && (
                              <div className="mb-3 bg-gray-700 bg-opacity-50 rounded-lg p-3">
                                <p className="text-gray-400 text-xs font-semibold mb-2 flex items-center">
                                  <span className="mr-1">📋</span>
                                  Check-in History ({checkIns.length})
                                </p>
                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                  {checkIns.map(checkIn => (
                                    <div key={checkIn.id} className="bg-gray-800 rounded p-2 border-l-2 border-blue-500">
                                      <div className="flex justify-between items-start mb-1">
                                        <span className={`text-xs font-semibold ${
                                          checkIn.actionType === 'located' ? 'text-green-400' :
                                          checkIn.actionType === 'not_present' ? 'text-orange-400' :
                                          checkIn.actionType === 'blocked' ? 'text-red-400' :
                                          'text-gray-400'
                                        }`}>
                                          {checkIn.actionType === 'located' && '✅ Vehicle Located'}
                                          {checkIn.actionType === 'not_present' && '⏰ Not Currently Present'}
                                          {checkIn.actionType === 'blocked' && '🚧 Blocked In'}
                                        </span>
                                        <span className="text-gray-500 text-xs">
                                          {formatTimestamp(checkIn.timestamp)}
                                        </span>
                                      </div>
                                      <p className="text-gray-300 text-xs">
                                        By: {checkIn.userName}
                                      </p>
                                      {checkIn.notes && (
                                        <p className="text-gray-200 text-xs mt-1">
                                          {checkIn.notes}
                                        </p>
                                      )}
                                      {checkIn.vinVerified && (
                                        <p className="text-green-400 text-xs mt-1">
                                          ✅ VIN Verified
                                        </p>
                                      )}
                                      {checkIn.photos && checkIn.photos.length > 0 && (
                                        <p className="text-blue-400 text-xs mt-1">
                                          📸 {checkIn.photos.length} photo(s)
                                        </p>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Order Details/Notes */}
                            {(order.details || order.notes) && (
                              <div className="bg-gray-700 bg-opacity-50 rounded-lg p-3 mb-3">
                                <p className="text-gray-400 text-xs font-semibold mb-1">📝 Details</p>
                                <p className="text-gray-200 text-sm">{order.details || order.notes}</p>
                              </div>
                            )}

                            {/* Tow Truck Driver Info */}
                            {!isCameraCarDriver && (
                              <div className="bg-gray-700 rounded-lg p-3 text-center">
                                <p className="text-gray-300 text-sm">
                                  🚛 Tow truck drivers: Wait for camera car to locate vehicle, then it will appear in your Pending Pickup list
                                </p>
                              </div>
                            )}

                            {/* Camera Car Driver Info */}
                            {isCameraCarDriver && (
                              <div className="bg-blue-700 bg-opacity-30 rounded-lg p-3 text-center">
                                <p className="text-blue-200 text-sm">
                                  📱 Camera car driver: Use the buttons above to check in on this vehicle and help tow truck drivers
                                </p>
                              </div>
                            )}
                          </div>
                        );
                      })()}
                    </div>
                  )}
                </div>
              )}
            </>
          )}

          <div className="mt-4 text-center">
            <button 
              onClick={loadTeamVehicles}
              className="text-purple-300 hover:text-purple-100 text-sm font-semibold flex items-center mx-auto"
              disabled={loadingTeamVehicles}
            >
              <span className="mr-2">🔄</span>
              Refresh Team Data
            </button>
          </div>
        </div>
      </div>

      {/* Camera Car Action Modal */}
      {showCameraAction && selectedOrder && (
        <CameraCarActionModal
          order={selectedOrder}
          actionType={cameraActionType}
          onClose={() => {
            setShowCameraAction(false);
            setCameraActionType(null);
            setSelectedOrder(null);
          }}
          onSubmit={submitCameraAction}
          currentUser={user}
          db={db}
          team={team}
        />
      )}

      {/* Recovery Modal */}
      {showRecoveryModal && recoveryVehicle && (
        <Recovery
          db={db}
          user={user}
          selectedWeek={selectedWeek}
          isModal={true}
          vehicleData={recoveryVehicle}
          onComplete={handleRecoveryComplete}
          onCancel={() => {
            setShowRecoveryModal(false);
            setRecoveryVehicle(null);
          }}
        />
      )}

      {/* Can't Secure Reason Modal */}
      {showCantSecureModal && cantSecureVehicle && (
        <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900 rounded-xl p-6 max-w-md w-full border border-red-600 shadow-2xl">
            <h3 className="text-xl font-bold text-red-400 mb-4">Can't Secure Vehicle</h3>

            <div className="mb-4">
              <p className="text-white font-bold">{cantSecureVehicle.vehicle}</p>
              <p className="text-gray-300 text-sm">VIN: {cantSecureVehicle.vin}</p>
              <p className="text-gray-400 text-xs">From: {cantSecureVehicle.teamMemberName}</p>
            </div>

            <div className="mb-4">
              <label className="block text-gray-300 text-sm font-semibold mb-2">
                Please enter the reason why this vehicle cannot be secured:
              </label>
              <textarea
                value={cantSecureReason}
                onChange={(e) => setCantSecureReason(e.target.value)}
                className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-red-500 focus:outline-none"
                rows="4"
                placeholder="Enter reason here..."
                autoFocus
              />
            </div>

            <div className="bg-red-900 bg-opacity-30 rounded-lg p-3 mb-4 border border-red-600">
              <p className="text-red-300 text-xs">
                ⚠️ This will permanently move the vehicle to the "Never Secured" list and remove it from active tracking.
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={processCantSecure}
                disabled={!cantSecureReason.trim()}
                className="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-2 rounded-lg font-semibold shadow-lg transition-all"
              >
                Confirm Can't Secure
              </button>
              <button
                onClick={() => {
                  setShowCantSecureModal(false);
                  setCantSecureVehicle(null);
                  setCantSecureReason('');
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg font-semibold shadow-lg transition-all"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Vehicle Confirmation Modal */}
      {showDeleteModal && vehicleToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900 rounded-xl p-6 max-w-md w-full border border-red-600 shadow-2xl">
            <h3 className="text-xl font-bold text-red-400 mb-4">🗑️ Delete Vehicle from Team Tracker</h3>

            <div className="mb-4">
              <p className="text-white font-bold">{vehicleToDelete.vehicle}</p>
              <p className="text-gray-300 text-sm">VIN: {vehicleToDelete.vin}</p>
              <p className="text-gray-400 text-xs">From: {vehicleToDelete.teamMemberName}</p>
            </div>

            <div className="mb-4">
              <label className="block text-gray-300 text-sm font-semibold mb-2">
                Please enter the reason for removing this vehicle from the team tracker:
              </label>
              <textarea
                value={deleteReason}
                onChange={(e) => setDeleteReason(e.target.value)}
                className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-red-500 focus:outline-none"
                rows="4"
                placeholder="e.g., Vehicle not on anyone's list anymore, duplicate entry, etc."
                autoFocus
              />
            </div>

            <div className="bg-yellow-900 bg-opacity-30 rounded-lg p-3 mb-4 border border-yellow-600">
              <p className="text-yellow-300 text-xs">
                ⚠️ This will remove the vehicle from the team tracker and move it to the "Never Secured" list with status "MANUALLY REMOVED". This action helps clean up stuck vehicles that aren't on anyone's personal lists anymore.
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={processVehicleDelete}
                disabled={!deleteReason.trim()}
                className="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-2 rounded-lg font-semibold shadow-lg transition-all"
              >
                Confirm Delete
              </button>
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setVehicleToDelete(null);
                  setDeleteReason('');
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg font-semibold shadow-lg transition-all"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default TeamVehicleTracker;