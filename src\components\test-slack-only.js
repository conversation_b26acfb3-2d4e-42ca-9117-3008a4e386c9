# Create the test file
cat > test-slack-only.js << 'EOF'
require('dotenv').config();
const { App } = require('@slack/bolt');

console.log('Testing Slack connection...');

const app = new App({
  token: process.env.SLACK_BOT_TOKEN,
  signingSecret: process.env.SLACK_SIGNING_SECRET,
  socketMode: true,
  appToken: process.env.SLACK_APP_TOKEN,
});

app.command('/vehicle', async ({ command, ack, say }) => {
  await ack();
  await say(`🚗 Bot is working! You typed: ${command.text || 'nothing'}`);
});

(async () => {
  try {
    await app.start();
    console.log('⚡️ Bot is running!');
    console.log('Go to Slack and type: /vehicle test');
  } catch (error) {
    console.error('Error:', error.message);
  }
})();
EOF

# Run it
node test-slack-only.js