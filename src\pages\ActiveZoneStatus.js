import React, { useState, useEffect } from 'react';
import { 
  collection,
  query,
  where,
  onSnapshot,
  getFirestore 
} from 'firebase/firestore';

function ActiveZoneStatus({ 
  teamId, 
  currentUser, 
  currentLocation,
  isClockedIn
}) {
  const db = getFirestore();
  const [zones, setZones] = useState([]);
  const [activeZones, setActiveZones] = useState([]);
  const [expanded, setExpanded] = useState(false);
  
  // Load zones for the team
  useEffect(() => {
    if (!db || !teamId || !currentUser) return;
    
    // Get zones assigned to this user
    const zonesRef = collection(db, 'teams', teamId, 'zones');
    const unsubscribe = onSnapshot(zonesRef, (snapshot) => {
      const zonesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      // Filter only zones relevant to this user (either unassigned or assigned to this user)
      const userZones = zonesData.filter(zone => 
        !zone.assignedUsers || 
        zone.assignedUsers.length === 0 || 
        zone.assignedUsers.includes(currentUser.uid)
      );
      
      setZones(userZones);
    }, (error) => {
      console.error("Error loading zones:", error);
    });
    
    return () => unsubscribe();
  }, [db, teamId, currentUser]);
  
  // Check if user is in a zone when location changes
  useEffect(() => {
    if (!currentLocation || !zones.length || !isClockedIn) return;
    
    const userZones = zones.filter(zone => {
      if (!zone.bounds || !zone.bounds.northEast || !zone.bounds.southWest) return false;
      
      // Check if the user's location is within the zone boundaries
      return currentLocation.lat >= zone.bounds.southWest.lat && 
             currentLocation.lat <= zone.bounds.northEast.lat &&
             currentLocation.lng >= zone.bounds.southWest.lng &&
             currentLocation.lng <= zone.bounds.northEast.lng;
    });
    
    setActiveZones(userZones);
  }, [currentLocation, zones, isClockedIn]);
  
  // If not in any zone or not clocked in, render nothing or a minimal indicator
  if (!isClockedIn || activeZones.length === 0) {
    return (
      <div className="text-xs text-gray-400 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
          <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5z" />
        </svg>
        No active zone
      </div>
    );
  }
  
  return (
    <div 
      className="relative"
      onMouseEnter={() => setExpanded(true)}
      onMouseLeave={() => setExpanded(false)}
    >
      <div className="flex items-center text-xs cursor-pointer">
        <div className="flex items-center bg-blue-900 bg-opacity-50 px-2 py-1 rounded-md border border-blue-700">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5z" />
          </svg>
          {activeZones.length > 1 ? (
            <span className="text-blue-300">{activeZones.length} Active Zones</span>
          ) : (
            <span className="text-blue-300">{activeZones[0].name}</span>
          )}
        </div>
      </div>
      
      {expanded && activeZones.length > 0 && (
        <div className="absolute top-full left-0 mt-1 bg-gray-800 rounded-md shadow-lg border border-gray-700 p-2 z-10 w-48">
          <h4 className="text-xs font-medium text-gray-400 mb-1">Active Zones:</h4>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {activeZones.map(zone => (
              <div 
                key={zone.id}
                className="flex items-center text-xs py-1 px-2 rounded hover:bg-gray-700"
              >
                <span 
                  className="w-2 h-2 rounded-full mr-2" 
                  style={{ backgroundColor: zone.color || '#3b82f6' }}
                ></span>
                <span className="text-white">{zone.name}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default ActiveZoneStatus;