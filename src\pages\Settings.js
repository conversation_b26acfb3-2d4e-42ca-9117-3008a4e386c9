import React, { useState, useEffect } from 'react';
import { getFirestore, doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext.js';

const Settings = () => {
  const navigate = useNavigate();
  const { currentUser, isAdmin } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [viewportHeight, setViewportHeight] = useState(window.innerHeight);
  const [orientation, setOrientation] = useState(window.innerWidth > window.innerHeight ? 'landscape' : 'portrait');
  const [isIPadDevice, setIsIPadDevice] = useState(false);
  
  // Pay calculation settings (updated with new toggles)
  const [settings, setSettings] = useState({
    // Feature toggles
    enablePayCalculations: true,
    enableBasicPayCalculations: true, // New toggle for basic pay (hourly rate & recovery bonus)
    enableScanBonusCalculations: true, // New toggle for scan bonus
    showTaxEstimates: true,
    
    // Basic pay settings
    hourlyRate: 15,
    bonusPerCarRecovered: 80,
    
    // Scan bonus settings
    scanBonusPerTeamMember: 150,
    scanBonusTier1ThresholdPerPerson: 65000,
    scanBonusTier2ThresholdPerPerson: 80000,
    scanBonusTier3ThresholdPerPerson: 100000,
    scanBonusTier4ThresholdPerPerson: 150000,
    scanBonusTier5ThresholdPerPerson: 250000,
    
    // Tax settings
    stateTaxRate: 4.95,
    federalTaxBrackets: [
      { rate: 10, threshold: 0 },
      { rate: 12, threshold: 11000 },
      { rate: 22, threshold: 44725 },
      { rate: 24, threshold: 95375 },
      { rate: 32, threshold: 182100 },
      { rate: 35, threshold: 231250 },
      { rate: 37, threshold: 578125 }
    ],
    socialSecurityTaxRate: 6.2,
    medicareTaxRate: 1.45,
    
    // Example pay calculation settings
    exampleCarsPerWeek: 20,
    exampleHoursPerWeek: 40,
    exampleTeamSize: 4,
    exampleScansPerWeek: 70000,
    
    // Daily Pay settings
    enableDailyPay: true,
    dailyPayFee: 5.99,
    dailyPayMaxPercentage: 60
  });
  
  // Enhanced responsive handling
  useEffect(() => {
    // Detect iPad
    const detectIPad = () => {
      const isIPad = (
        /iPad/.test(navigator.userAgent) || 
        (/Macintosh/.test(navigator.userAgent) && 'ontouchend' in document)
      ) && window.innerWidth >= 768 && window.innerWidth <= 1180;
      
      setIsIPadDevice(isIPad);
      if (isIPad) {
        document.body.classList.add('ipad');
      } else {
        document.body.classList.remove('ipad');
      }
      return isIPad;
    };
    
    // Update viewport variables
    const updateViewport = () => {
      // Calculate true viewport height for mobile browsers
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
      setViewportHeight(window.innerHeight);
      
      // Determine orientation
      const currentOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
      setOrientation(currentOrientation);
      document.body.setAttribute('data-orientation', currentOrientation);
    };
    
    // Combined handler for orientation and resize
    const handleViewportChange = () => {
      // Wait for browser UI to settle (especially on iOS)
      setTimeout(() => {
        updateViewport();
        detectIPad();
      }, 150);
    };
    
    // Initial setup
    detectIPad();
    updateViewport();
    
    // Event listeners
    window.addEventListener('orientationchange', handleViewportChange);
    window.addEventListener('resize', handleViewportChange);
    
    return () => {
      window.removeEventListener('orientationchange', handleViewportChange);
      window.removeEventListener('resize', handleViewportChange);
    };
  }, []);
  
  // Check if user is admin and load settings
  useEffect(() => {
    const checkAccessAndLoadSettings = async () => {
      try {
        setLoading(true);
        
        if (!currentUser) {
          setError("You must be logged in to access this page");
          navigate('/login');
          return;
        }
        
        if (!isAdmin) {
          setError("You don't have permission to access this page");
          setTimeout(() => navigate('/'), 3000);
          return;
        }
        
        await loadSettings();
      } catch (err) {
        console.error("Error checking access or loading settings:", err);
        setError("An error occurred while loading settings");
      } finally {
        setLoading(false);
      }
    };
    
    checkAccessAndLoadSettings();
  }, [currentUser, isAdmin, navigate]);
  
  // Load settings from Firestore
  const loadSettings = async () => {
    try {
      const db = getFirestore();
      const settingsDoc = await getDoc(doc(db, 'settings', 'payCalculation'));
      
      if (settingsDoc.exists()) {
        const savedSettings = settingsDoc.data();
        setSettings(prevSettings => ({
          ...prevSettings,
          ...savedSettings
        }));
      } else {
        await setDoc(doc(db, 'settings', 'payCalculation'), settings);
      }
      
      // Check for daily pay settings
      const dailyPayDoc = await getDoc(doc(db, 'settings', 'dailyPay'));
      if (dailyPayDoc.exists()) {
        const dailyPaySettings = dailyPayDoc.data();
        setSettings(prevSettings => ({
          ...prevSettings,
          enableDailyPay: dailyPaySettings.enabled,
          dailyPayFee: dailyPaySettings.fee,
          dailyPayMaxPercentage: dailyPaySettings.maxPercentage
        }));
      }
    } catch (err) {
      console.error("Error loading settings:", err);
      setError("Failed to load settings");
    }
  };
  
  // Save settings to Firestore
  const saveSettings = async () => {
    try {
      setSaving(true);
      const db = getFirestore();
      await updateDoc(doc(db, 'settings', 'payCalculation'), settings);
      
      // Save daily pay settings
      await setDoc(doc(db, 'settings', 'dailyPay'), {
        enabled: settings.enableDailyPay,
        fee: settings.dailyPayFee,
        maxPercentage: settings.dailyPayMaxPercentage
      });
      
      setSuccessMessage("Settings saved successfully");
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err) {
      console.error("Error saving settings:", err);
      setError("Failed to save settings");
      setTimeout(() => setError(null), 3000);
    } finally {
      setSaving(false);
    }
  };
  
  // Handle input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    const newValue = type === 'checkbox' ? checked : 
                     type === 'number' ? parseFloat(value) : 
                     value;
    
    // Special handling for main pay toggle - disable sub-toggles when main is off
    if (name === "enablePayCalculations" && !checked) {
      setSettings(prevSettings => ({
        ...prevSettings,
        [name]: newValue,
        enableBasicPayCalculations: false,
        enableScanBonusCalculations: false,
        showTaxEstimates: false
      }));
    } else {
      setSettings(prevSettings => ({
        ...prevSettings,
        [name]: newValue
      }));
    }
  };
  
  // Reset settings to defaults
  const resetSettings = () => {
    if (window.confirm("Are you sure you want to reset all settings to default values?")) {
      setSettings({
        enablePayCalculations: true,
        enableBasicPayCalculations: true,
        enableScanBonusCalculations: true,
        showTaxEstimates: true,
        hourlyRate: 15,
        bonusPerCarRecovered: 80,
        scanBonusPerTeamMember: 150,
        scanBonusTier1ThresholdPerPerson: 65000,
        scanBonusTier2ThresholdPerPerson: 80000,
        scanBonusTier3ThresholdPerPerson: 100000,
        scanBonusTier4ThresholdPerPerson: 150000,
        scanBonusTier5ThresholdPerPerson: 250000,
        stateTaxRate: 4.95,
        federalTaxBrackets: [
          { rate: 10, threshold: 0 },
          { rate: 12, threshold: 11000 },
          { rate: 22, threshold: 44725 },
          { rate: 24, threshold: 95375 },
          { rate: 32, threshold: 182100 },
          { rate: 35, threshold: 231250 },
          { rate: 37, threshold: 578125 }
        ],
        socialSecurityTaxRate: 6.2,
        medicareTaxRate: 1.45,
        exampleCarsPerWeek: 20,
        exampleHoursPerWeek: 40,
        exampleTeamSize: 4,
        exampleScansPerWeek: 70000,
        enableDailyPay: true,
        dailyPayFee: 5.99,
        dailyPayMaxPercentage: 60
      });
    }
  };
  
  // Calculate example weekly pay
  const calculateExamplePayWithTaxes = () => {
    if (!settings.enablePayCalculations || !settings.showTaxEstimates) {
      return null;
    }
    
    // Skip calculations if basic pay or scan bonus are disabled
    if (!settings.enableBasicPayCalculations && !settings.enableScanBonusCalculations) {
      return null;
    }
    
    const { 
      hourlyRate, 
      bonusPerCarRecovered, 
      exampleCarsPerWeek,
      exampleHoursPerWeek,
      exampleTeamSize,
      exampleScansPerWeek,
      scanBonusPerTeamMember,
      scanBonusTier1ThresholdPerPerson,
      scanBonusTier2ThresholdPerPerson,
      scanBonusTier3ThresholdPerPerson,
      scanBonusTier4ThresholdPerPerson,
      scanBonusTier5ThresholdPerPerson,
      stateTaxRate,
      socialSecurityTaxRate,
      medicareTaxRate,
      federalTaxBrackets
    } = settings;
    
    // Basic pay calculations (only if enabled)
    const hourlyPayAmount = settings.enableBasicPayCalculations ? hourlyRate * exampleHoursPerWeek : 0;
    const recoveryBonusPool = settings.enableBasicPayCalculations ? exampleCarsPerWeek * bonusPerCarRecovered : 0;
    const recoveryBonusPerPerson = recoveryBonusPool / exampleTeamSize;
    
    // Scan bonus calculations (only if enabled)
    let scanBonus = 0;
    if (settings.enableScanBonusCalculations) {
      // Calculate scan bonus thresholds for the team
      const tier1Threshold = scanBonusTier1ThresholdPerPerson * exampleTeamSize;
      const tier2Threshold = scanBonusTier2ThresholdPerPerson * exampleTeamSize;
      const tier3Threshold = scanBonusTier3ThresholdPerPerson * exampleTeamSize;
      const tier4Threshold = scanBonusTier4ThresholdPerPerson * exampleTeamSize;
      const tier5Threshold = scanBonusTier5ThresholdPerPerson * exampleTeamSize;
      
      if (exampleScansPerWeek >= tier5Threshold) {
        scanBonus = scanBonusPerTeamMember * 5 * exampleTeamSize;
      } else if (exampleScansPerWeek >= tier4Threshold) {
        scanBonus = scanBonusPerTeamMember * 4 * exampleTeamSize;
      } else if (exampleScansPerWeek >= tier3Threshold) {
        scanBonus = scanBonusPerTeamMember * 3 * exampleTeamSize;
      } else if (exampleScansPerWeek >= tier2Threshold) {
        scanBonus = scanBonusPerTeamMember * 2 * exampleTeamSize;
      } else if (exampleScansPerWeek >= tier1Threshold) {
        scanBonus = scanBonusPerTeamMember * exampleTeamSize;
      }
    }
    
    // Per person calculations
    const scanBonusPerPerson = scanBonus / exampleTeamSize;
    
    // Calculate gross pay
    const grossWeeklyPay = hourlyPayAmount + recoveryBonusPerPerson + scanBonusPerPerson;
    
    // Annualize for tax calculations
    let annualizedPay = grossWeeklyPay * 52;
    
    // Calculate taxes
    let federalTax = 0;
    const sortedBrackets = [...federalTaxBrackets].sort((a, b) => a.threshold - b.threshold);
    
    for (let i = 0; i < sortedBrackets.length; i++) {
      const currentBracket = sortedBrackets[i];
      const nextBracket = sortedBrackets[i + 1];
      
      if (!nextBracket || annualizedPay <= nextBracket.threshold) {
        federalTax += (annualizedPay - currentBracket.threshold) * (currentBracket.rate / 100);
        break;
      } else {
        federalTax += (nextBracket.threshold - currentBracket.threshold) * (currentBracket.rate / 100);
      }
    }
    
    const weeklyFederalTax = federalTax / 52;
    
    // State tax
    const weeklyStateTax = grossWeeklyPay * (stateTaxRate / 100);
    
    // FICA taxes
    const weeklySocialSecurityTax = Math.min(grossWeeklyPay * (socialSecurityTaxRate / 100), 
                                         (147000 / 52) * (socialSecurityTaxRate / 100));
    const weeklyMedicareTax = grossWeeklyPay * (medicareTaxRate / 100);
    
    // Total tax and net pay
    const totalWeeklyTax = weeklyFederalTax + weeklyStateTax + weeklySocialSecurityTax + weeklyMedicareTax;
    const netWeeklyPay = grossWeeklyPay - totalWeeklyTax;
    
    return {
      grossWeeklyPay,
      hourlyPay: hourlyPayAmount,
      recoveryBonusPerPerson,
      scanBonusPerPerson,
      weeklyFederalTax,
      weeklyStateTax,
      weeklySocialSecurityTax,
      weeklyMedicareTax,
      totalWeeklyTax,
      netWeeklyPay
    };
  };
  
  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount || 0);
  };
  
  // Example pay calculation
  const examplePay = calculateExamplePayWithTaxes();
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="p-6 bg-gray-800 rounded-lg shadow-lg text-center border border-gray-700">
          <div className="animate-spin w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-300">Loading settings...</p>
        </div>
      </div>
    );
  }
  
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="p-6 bg-gray-800 rounded-lg shadow-lg text-center max-w-md border border-gray-700">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-400 mb-4">Access Denied</h1>
          <p className="text-gray-300 mb-4">{error || "You don't have permission to access this page."}</p>
          <p className="text-gray-400">Redirecting to homepage...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="settings-wrapper bg-gray-900 text-gray-200">
      <div className="settings-container max-w-6xl mx-auto py-4 sm:py-6 px-4 sm:px-6">
        <div className="bg-gray-800 rounded-lg shadow-lg p-4 sm:p-6 mb-6 border border-gray-700">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-6 gap-4">
            <h1 className="text-xl sm:text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">Pay Calculation Settings</h1>
            <div className="flex flex-wrap gap-2">
              <button 
                onClick={resetSettings}
                className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors text-sm sm:text-base"
              >
                Reset to Default
              </button>
              <button 
                onClick={saveSettings}
                disabled={saving}
                className={`${saving ? 'bg-blue-700' : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600'} text-white py-2 px-4 rounded-lg transition-colors flex items-center text-sm sm:text-base`}
              >
                {saving ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </>
                ) : "Save Settings"}
              </button>
            </div>
          </div>
          
          {error && (
            <div className="bg-red-900 bg-opacity-50 border border-red-700 text-red-200 px-4 py-3 rounded relative mb-4">
              <span className="block sm:inline">{error}</span>
            </div>
          )}
          
          {successMessage && (
            <div className="bg-green-900 bg-opacity-50 border border-green-700 text-green-200 px-4 py-3 rounded relative mb-4">
              <span className="block sm:inline">{successMessage}</span>
            </div>
          )}
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            {/* Feature Toggles - UPDATED with new toggles */}
            <div className="bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700">
              <h2 className="text-lg font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">Feature Toggles</h2>
              
              <div className="space-y-4">
                {/* Main pay calculations toggle */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center cursor-pointer">
                    <div className="text-gray-300 font-medium text-sm sm:text-base">Enable Pay Calculations</div>
                    <div className="ml-2 text-xs text-gray-500">(Master toggle)</div>
                  </label>
                  <label className="inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      className="sr-only peer"
                      name="enablePayCalculations"
                      checked={settings.enablePayCalculations}
                      onChange={handleChange}
                    />
                    <div className="relative w-10 h-5 sm:w-11 sm:h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-gray-300 after:border-gray-600 after:border after:rounded-full after:h-4 after:w-4 sm:after:h-5 sm:after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                
                {/* Basic pay calculations toggle (new) */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center cursor-pointer">
                    <div className="text-gray-300 font-medium text-sm sm:text-base">Enable Basic Pay</div>
                    <div className="ml-2 text-xs text-gray-500">(Hourly & recovery bonuses)</div>
                  </label>
                  <label className="inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      className="sr-only peer"
                      name="enableBasicPayCalculations"
                      checked={settings.enableBasicPayCalculations}
                      onChange={handleChange}
                      disabled={!settings.enablePayCalculations}
                    />
                    <div className={`relative w-10 h-5 sm:w-11 sm:h-6 ${settings.enablePayCalculations ? 'bg-gray-700' : 'bg-gray-800 opacity-50'} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-gray-300 after:border-gray-600 after:border after:rounded-full after:h-4 after:w-4 sm:after:h-5 sm:after:w-5 after:transition-all peer-checked:bg-blue-600`}></div>
                  </label>
                </div>
                
                {/* Scan bonus calculations toggle (new) */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center cursor-pointer">
                    <div className="text-gray-300 font-medium text-sm sm:text-base">Enable Scan Bonus</div>
                    <div className="ml-2 text-xs text-gray-500">(Tiered scan volume bonuses)</div>
                  </label>
                  <label className="inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      className="sr-only peer"
                      name="enableScanBonusCalculations"
                      checked={settings.enableScanBonusCalculations}
                      onChange={handleChange}
                      disabled={!settings.enablePayCalculations}
                    />
                    <div className={`relative w-10 h-5 sm:w-11 sm:h-6 ${settings.enablePayCalculations ? 'bg-gray-700' : 'bg-gray-800 opacity-50'} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-gray-300 after:border-gray-600 after:border after:rounded-full after:h-4 after:w-4 sm:after:h-5 sm:after:w-5 after:transition-all peer-checked:bg-blue-600`}></div>
                  </label>
                </div>
                
                {/* Tax estimates toggle */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center cursor-pointer">
                    <div className="text-gray-300 font-medium text-sm sm:text-base">Show Tax Estimates</div>
                    <div className="ml-2 text-xs text-gray-500">(Illinois taxes)</div>
                  </label>
                  <label className="inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      className="sr-only peer"
                      name="showTaxEstimates"
                      checked={settings.showTaxEstimates}
                      onChange={handleChange}
                      disabled={!settings.enablePayCalculations}
                    />
                    <div className={`relative w-10 h-5 sm:w-11 sm:h-6 ${settings.enablePayCalculations ? 'bg-gray-700' : 'bg-gray-800 opacity-50'} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-gray-300 after:border-gray-600 after:border after:rounded-full after:h-4 after:w-4 sm:after:h-5 sm:after:w-5 after:transition-all peer-checked:bg-blue-600`}></div>
                  </label>
                </div>
              </div>
              
              {/* Information note about visibility */}
              <div className="mt-4 text-xs text-gray-400 p-2 bg-gray-800 rounded-lg">
                <p className="font-semibold text-blue-400 mb-1">How these settings affect the app:</p>
                <ul className="list-disc pl-4 space-y-1">
                  <li>When <span className="text-blue-300">Pay Calculations</span> is disabled, no pay information will be shown anywhere in the app.</li>
                  <li>When <span className="text-blue-300">Basic Pay</span> is disabled, hourly rates and recovery bonuses will not be shown or calculated.</li>
                  <li>When <span className="text-blue-300">Scan Bonus</span> is disabled, scan bonus features will not be shown or calculated.</li>
                </ul>
              </div>
            </div>
            
            {/* Basic Pay Settings - Disabled if basic pay toggle is off */}
            <div className={`bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700 ${!settings.enablePayCalculations || !settings.enableBasicPayCalculations ? 'opacity-50' : ''}`}>
              <h2 className="text-lg font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">
                Basic Pay Settings
                {(!settings.enablePayCalculations || !settings.enableBasicPayCalculations) && 
                  <span className="ml-2 text-xs text-red-400">(Currently Disabled)</span>
                }
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Hourly Rate
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-400 sm:text-sm">$</span>
                    </div>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      name="hourlyRate"
                      value={settings.hourlyRate}
                      onChange={handleChange}
                      disabled={!settings.enablePayCalculations || !settings.enableBasicPayCalculations}
                      className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                      placeholder="0.00"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-400 sm:text-sm">/hour</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Bonus Per Car Recovered
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-400 sm:text-sm">$</span>
                    </div>
                    <input
                      type="number"
                      min="0"
                      step="1"
                      name="bonusPerCarRecovered"
                      value={settings.bonusPerCarRecovered}
                      onChange={handleChange}
                      disabled={!settings.enablePayCalculations || !settings.enableBasicPayCalculations}
                      className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                      placeholder="0"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-400 sm:text-sm">/car</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Scan Bonus Settings - Disabled if scan bonus toggle is off */}
          <div className={`mt-6 sm:mt-8 bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700 ${!settings.enablePayCalculations || !settings.enableScanBonusCalculations ? 'opacity-50' : ''}`}>
            <h2 className="text-lg font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">
              Scan Bonus Settings
              {(!settings.enablePayCalculations || !settings.enableScanBonusCalculations) && 
                <span className="ml-2 text-xs text-red-400">(Currently Disabled)</span>
              }
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Scan Bonus Per Team Member (Base Amount)
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-400 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    min="0"
                    step="1"
                    name="scanBonusPerTeamMember"
                    value={settings.scanBonusPerTeamMember}
                    onChange={handleChange}
                    disabled={!settings.enablePayCalculations || !settings.enableScanBonusCalculations}
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                    placeholder="0"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-400">This amount is multiplied by team size and tier level</p>
              </div>
              
              <div className="bg-indigo-900 bg-opacity-30 p-4 rounded-lg border border-indigo-800">
                <h3 className="text-md font-medium text-blue-300 mb-2">How Scan Bonuses Work</h3>
                <p className="text-xs sm:text-sm text-blue-200">
                  Scan bonuses are calculated based on team size. The formula is:<br />
                  <span className="font-semibold">Bonus = Base Amount × Tier Level × Team Size</span><br /><br />
                  For example, with ${settings.scanBonusPerTeamMember} base amount and a team of 4:<br />
                  - Tier 1: ${settings.scanBonusPerTeamMember} × 1 × 4 = ${settings.scanBonusPerTeamMember * 4}<br />
                  - Tier 2: ${settings.scanBonusPerTeamMember} × 2 × 4 = ${settings.scanBonusPerTeamMember * 2 * 4}
                </p>
              </div>
            </div>
            
            <div className="mt-6">
              <h3 className="text-md font-medium text-gray-300 mb-3">Scan Volume Thresholds (Per Person)</h3>
              <p className="text-xs sm:text-sm text-gray-400 mb-4">These thresholds will be multiplied by team size to determine the actual threshold for each team.</p>
              
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-1">
                    Tier 1 Threshold
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="1000"
                    name="scanBonusTier1ThresholdPerPerson"
                    value={settings.scanBonusTier1ThresholdPerPerson}
                    onChange={handleChange}
                    disabled={!settings.enablePayCalculations || !settings.enableScanBonusCalculations}
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full text-xs sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                    placeholder="0"
                  />
                  <p className="mt-1 text-xs text-gray-400">Team of 4: {(settings.scanBonusTier1ThresholdPerPerson * 4).toLocaleString()}</p>
                </div>
                
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-1">
                    Tier 2 Threshold
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="1000"
                    name="scanBonusTier2ThresholdPerPerson"
                    value={settings.scanBonusTier2ThresholdPerPerson}
                    onChange={handleChange}
                    disabled={!settings.enablePayCalculations || !settings.enableScanBonusCalculations}
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full text-xs sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                    placeholder="0"
                  />
                  <p className="mt-1 text-xs text-gray-400">Team of 4: {(settings.scanBonusTier2ThresholdPerPerson * 4).toLocaleString()}</p>
                </div>
                
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-1">
                    Tier 3 Threshold
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="1000"
                    name="scanBonusTier3ThresholdPerPerson"
                    value={settings.scanBonusTier3ThresholdPerPerson}
                    onChange={handleChange}
                    disabled={!settings.enablePayCalculations || !settings.enableScanBonusCalculations}
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full text-xs sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                    placeholder="0"
                  />
                  <p className="mt-1 text-xs text-gray-400">Team of 4: {(settings.scanBonusTier3ThresholdPerPerson * 4).toLocaleString()}</p>
                </div>
                
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-1">
                    Tier 4 Threshold
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="1000"
                    name="scanBonusTier4ThresholdPerPerson"
                    value={settings.scanBonusTier4ThresholdPerPerson}
                    onChange={handleChange}
                    disabled={!settings.enablePayCalculations || !settings.enableScanBonusCalculations}
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full text-xs sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                    placeholder="0"
                  />
                  <p className="mt-1 text-xs text-gray-400">Team of 4: {(settings.scanBonusTier4ThresholdPerPerson * 4).toLocaleString()}</p>
                </div>
                
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-1">
                    Tier 5 Threshold
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="1000"
                    name="scanBonusTier5ThresholdPerPerson"
                    value={settings.scanBonusTier5ThresholdPerPerson}
                    onChange={handleChange}
                    disabled={!settings.enablePayCalculations || !settings.enableScanBonusCalculations}
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full text-xs sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                    placeholder="0"
                  />
                  <p className="mt-1 text-xs text-gray-400">Team of 4: {(settings.scanBonusTier5ThresholdPerPerson * 4).toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Daily Pay Settings */}
          <div className="mt-6 sm:mt-8 bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700">
            <h2 className="text-lg font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">
              Daily Pay Settings
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              <div>
                <div className="flex items-center justify-between mb-4">
                  <label className="flex items-center cursor-pointer">
                    <div className="text-sm font-medium text-gray-300">Enable Daily Pay</div>
                    <div className="ml-2 text-xs text-gray-500">(Allow users to access daily pay)</div>
                  </label>
                  <label className="inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      className="sr-only peer"
                      name="enableDailyPay"
                      checked={settings.enableDailyPay}
                      onChange={handleChange}
                    />
                    <div className="relative w-10 h-5 sm:w-11 sm:h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-gray-300 after:border-gray-600 after:border after:rounded-full after:h-4 after:w-4 sm:after:h-5 sm:after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                
                <div className={`bg-indigo-900 bg-opacity-30 p-4 rounded-lg border border-indigo-800 ${!settings.enableDailyPay ? 'opacity-50' : ''}`}>
                  <h3 className="text-md font-medium text-blue-300 mb-2">How Daily Pay Works</h3>
                  <p className="text-xs sm:text-sm text-blue-200">
                    Daily Pay allows team members to access a portion of their daily pay before the regular payday.<br /><br />
                    Users can withdraw up to <span className="font-semibold">{settings.dailyPayMaxPercentage || 60}%</span> of their daily hourly pay (not including bonuses) for a small fee of <span className="font-semibold">{formatCurrency(settings.dailyPayFee || 5.99)}</span> per transaction.<br /><br />
                    Daily Pay is limited to once per day per user, and the amount plus fee is deducted from their next direct deposit.
                  </p>
                </div>
              </div>
              
              <div>
                <div className={`space-y-4 ${!settings.enableDailyPay ? 'opacity-50' : ''}`}>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Daily Pay Fee
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-400 sm:text-sm">$</span>
                      </div>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        name="dailyPayFee"
                        value={settings.dailyPayFee || 5.99}
                        onChange={handleChange}
                        disabled={!settings.enableDailyPay}
                        className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                        placeholder="5.99"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-400">This fee is charged for each daily pay transaction</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Maximum Percentage Available
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <input
                        type="number"
                        min="1"
                        max="100"
                        name="dailyPayMaxPercentage"
                        value={settings.dailyPayMaxPercentage || 60}
                        onChange={handleChange}
                        disabled={!settings.enableDailyPay}
                        className="focus:ring-blue-500 focus:border-blue-500 block w-full pr-12 sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                        placeholder="60"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-400 sm:text-sm">%</span>
                      </div>
                    </div>
                    <p className="mt-1 text-xs text-gray-400">Maximum percentage of daily hourly pay users can withdraw</p>
                  </div>
                  
                  <div className="bg-green-900 bg-opacity-30 p-3 rounded-lg border border-green-800">
                    <h4 className="text-sm font-semibold text-green-300 mb-1">Example Calculation</h4>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="text-gray-300">Hours worked:</div>
                      <div className="text-white">8 hours</div>
                      
                      <div className="text-gray-300">Hourly rate:</div>
                      <div className="text-white">{formatCurrency(settings.hourlyRate || 15)}/hr</div>
                      
                      <div className="text-gray-300">Daily pay:</div>
                      <div className="text-white">{formatCurrency((settings.hourlyRate || 15) * 8)}</div>
                      
                      <div className="text-gray-300">Available amount ({settings.dailyPayMaxPercentage || 60}%):</div>
                      <div className="text-green-300">{formatCurrency(((settings.hourlyRate || 15) * 8) * ((settings.dailyPayMaxPercentage || 60) / 100))}</div>
                      
                      <div className="text-gray-300">Fee:</div>
                      <div className="text-red-300">- {formatCurrency(settings.dailyPayFee || 5.99)}</div>
                      
                      <div className="text-gray-300 font-semibold">Net amount:</div>
                      <div className="text-green-300 font-semibold">
                        {formatCurrency((((settings.hourlyRate || 15) * 8) * ((settings.dailyPayMaxPercentage || 60) / 100)) - (settings.dailyPayFee || 5.99))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-4 bg-gray-800 p-4 rounded-lg">
              <h3 className="text-md font-semibold text-yellow-300 mb-2">Important Notes</h3>
              <ul className="list-disc pl-5 text-xs text-gray-300 space-y-1">
                <li>Daily Pay is designed to help team members with short-term cash needs without waiting for the weekly paycheck.</li>
                <li>The fee structure is designed to cover processing costs and should be kept affordable.</li>
                <li>When disabled, all users will see a message that Daily Pay is not currently available.</li>
                <li>Limiting to 60% ensures team members still receive a meaningful amount in their regular paycheck.</li>
                <li>All Daily Pay transactions are tracked and reported for accounting and transparency.</li>
              </ul>
            </div>
          </div>
          
          {/* Tax Settings - Only visible if tax estimates are enabled */}
          <div className={`mt-6 sm:mt-8 bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700 ${!settings.enablePayCalculations || !settings.showTaxEstimates ? 'opacity-50' : ''}`}>
            <h2 className="text-lg font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">
              Tax Calculation Settings (Illinois)
              {(!settings.enablePayCalculations || !settings.showTaxEstimates) && 
                <span className="ml-2 text-xs text-red-400">(Currently Disabled)</span>
              }
            </h2>
            
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Illinois State Tax Rate (%)
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <input
                    type="number"
                    min="0"
                    max="15"
                    step="0.01"
                    name="stateTaxRate"
                    value={settings.stateTaxRate}
                    onChange={handleChange}
                    disabled={!settings.enablePayCalculations || !settings.showTaxEstimates}
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full pr-12 sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                    placeholder="0.00"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <span className="text-gray-400 sm:text-sm">%</span>
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Social Security Tax Rate (%)
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <input
                    type="number"
                    min="0"
                    max="15"
                    step="0.01"
                    name="socialSecurityTaxRate"
                    value={settings.socialSecurityTaxRate}
                    onChange={handleChange}
                    disabled={!settings.enablePayCalculations || !settings.showTaxEstimates}
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full pr-12 sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                    placeholder="0.00"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <span className="text-gray-400 sm:text-sm">%</span>
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Medicare Tax Rate (%)
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <input
                    type="number"
                    min="0"
                    max="5"
                    step="0.01"
                    name="medicareTaxRate"
                    value={settings.medicareTaxRate}
                    onChange={handleChange}
                    disabled={!settings.enablePayCalculations || !settings.showTaxEstimates}
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full pr-12 sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                    placeholder="0.00"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <span className="text-gray-400 sm:text-sm">%</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-4">
              <p className="text-xs sm:text-sm text-gray-400">
                Note: Federal tax brackets are automatically applied based on 2023 tax tables for a single filer.
                For more complex tax situations, employees should consult a tax professional.
              </p>
            </div>
          </div>
          
          {/* Example Pay Calculator - Only visible if pay calculations are enabled */}
          <div className={`mt-6 sm:mt-8 bg-gray-900 bg-opacity-60 p-4 rounded-lg border border-gray-700 ${!settings.enablePayCalculations ? 'opacity-50' : ''}`}>
            <h2 className="text-lg font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-4">
              Example Pay Calculator
              {!settings.enablePayCalculations && 
                <span className="ml-2 text-xs text-red-400">(Currently Disabled)</span>
              }
            </h2>
            <p className="text-xs sm:text-sm text-gray-400 mb-4">
              Use this calculator to estimate weekly pay after taxes. These values will be used in the Stats display 
              to show team members what their weekly check might look like.
            </p>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-1">
                  Example Cars Per Week
                </label>
                <input
                  type="number"
                  min="0"
                  step="1"
                  name="exampleCarsPerWeek"
                  value={settings.exampleCarsPerWeek}
                  onChange={handleChange}
                  disabled={!settings.enablePayCalculations}
                  className="focus:ring-blue-500 focus:border-blue-500 block w-full text-xs sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                />
              </div>
              
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-1">
                  Example Hours Per Week
                </label>
                <input
                  type="number"
                  min="0"
                  max="168"
                  step="1"
                  name="exampleHoursPerWeek"
                  value={settings.exampleHoursPerWeek}
                  onChange={handleChange}
                  disabled={!settings.enablePayCalculations}
                  className="focus:ring-blue-500 focus:border-blue-500 block w-full text-xs sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                />
              </div>
              
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-1">
                  Example Team Size
                </label>
                <input
                  type="number"
                  min="1"
                  step="1"
                  name="exampleTeamSize"
                  value={settings.exampleTeamSize}
                  onChange={handleChange}
                  disabled={!settings.enablePayCalculations}
                  className="focus:ring-blue-500 focus:border-blue-500 block w-full text-xs sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                />
              </div>
              
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-300 mb-1">
                  Example Team Scans Per Week
                </label>
                <input
                  type="number"
                  min="0"
                  step="1000"
                  name="exampleScansPerWeek"
                  value={settings.exampleScansPerWeek}
                  onChange={handleChange}
                  disabled={!settings.enablePayCalculations || !settings.enableScanBonusCalculations}
                  className="focus:ring-blue-500 focus:border-blue-500 block w-full text-xs sm:text-sm bg-gray-700 border-gray-600 rounded-md text-white"
                />
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
              <h3 className="text-md font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400 mb-3">Weekly Pay Estimate (After Illinois Taxes)</h3>
              
              {examplePay ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-300 mb-2">Income Breakdown</h4>
                    <ul className="space-y-2">
                      {settings.enableBasicPayCalculations && (
                        <>
                          <li className="flex justify-between text-xs sm:text-sm">
                            <span className="text-gray-400">Hourly Pay:</span>
                            <span className="font-medium text-white">{formatCurrency(examplePay.hourlyPay)}</span>
                          </li>
                          <li className="flex justify-between text-xs sm:text-sm">
                            <span className="text-gray-400">Car Recovery Bonus:</span>
                            <span className="font-medium text-white">{formatCurrency(examplePay.recoveryBonusPerPerson)}</span>
                          </li>
                        </>
                      )}
                      
                      {settings.enableScanBonusCalculations && (
                        <li className="flex justify-between text-xs sm:text-sm">
                          <span className="text-gray-400">Scan Bonus:</span>
                          <span className="font-medium text-white">{formatCurrency(examplePay.scanBonusPerPerson)}</span>
                        </li>
                      )}
                      
                      <li className="flex justify-between text-xs sm:text-sm font-semibold">
                        <span className="text-blue-300">Gross Weekly Pay:</span>
                        <span className="text-blue-300">{formatCurrency(examplePay.grossWeeklyPay)}</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-300 mb-2">Tax Deductions</h4>
                    <ul className="space-y-2">
                      <li className="flex justify-between text-xs sm:text-sm">
                        <span className="text-gray-400">Federal Income Tax:</span>
                        <span className="font-medium text-white">-{formatCurrency(examplePay.weeklyFederalTax)}</span>
                      </li>
                      <li className="flex justify-between text-xs sm:text-sm">
                        <span className="text-gray-400">Illinois State Tax:</span>
                        <span className="font-medium text-white">-{formatCurrency(examplePay.weeklyStateTax)}</span>
                      </li>
                      <li className="flex justify-between text-xs sm:text-sm">
                        <span className="text-gray-400">Social Security:</span>
                        <span className="font-medium text-white">-{formatCurrency(examplePay.weeklySocialSecurityTax)}</span>
                      </li>
                      <li className="flex justify-between text-xs sm:text-sm">
                        <span className="text-gray-400">Medicare:</span>
                        <span className="font-medium text-white">-{formatCurrency(examplePay.weeklyMedicareTax)}</span>
                      </li>
                      <li className="flex justify-between text-xs sm:text-sm font-semibold text-red-400">
                        <span>Total Tax:</span>
                        <span>-{formatCurrency(examplePay.totalWeeklyTax)}</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div className="md:col-span-2 mt-4 pt-4 border-t border-gray-700">
                    <div className="flex justify-between items-center">
                      <span className="text-md sm:text-lg font-bold text-gray-200">Weekly Take-Home Pay:</span>
                      <span className="text-xl sm:text-2xl font-bold text-green-400">{formatCurrency(examplePay.netWeeklyPay)}</span>
                    </div>
                    <p className="mt-2 text-xs sm:text-sm text-gray-400">
                      This is an estimated weekly paycheck based on {settings.exampleCarsPerWeek} cars recovered, 
                      {settings.exampleHoursPerWeek} hours worked, and a team of {settings.exampleTeamSize} with 
                      {settings.exampleScansPerWeek.toLocaleString()} total scans.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center p-4">
                  <p className="text-gray-400 text-sm">
                    {!settings.enablePayCalculations ? 
                      "Pay calculations are currently disabled." : 
                      !settings.showTaxEstimates ? 
                        "Tax estimates are currently disabled." : 
                        "No payment components are enabled. Enable Basic Pay or Scan Bonus to see calculations."}
                  </p>
                </div>
              )}
            </div>
          </div>
          
          <div className="mt-6 sm:mt-8 flex justify-end space-x-2">
            <button 
              onClick={() => navigate(-1)}
              className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors text-sm sm:text-base"
            >
              Cancel
            </button>
            <button 
              onClick={saveSettings}
              disabled={saving}
              className={`${saving ? 'bg-blue-700' : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600'} text-white py-2 px-4 rounded-lg transition-colors flex items-center text-sm sm:text-base`}
            >
              {saving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : "Save Settings"}
            </button>
          </div>
        </div>
      </div>
      
      {/* Responsive and Device-Specific Styles */}
      <style jsx="true">{`
        /* Base layout and responsive styles */
        .settings-wrapper {
          min-height: 100vh;
          width: 100%;
          -webkit-overflow-scrolling: touch;
          overflow-y: auto;
          overflow-x: hidden;
        }
        
        /* Fix iOS & iPad height issues with viewport units */
        @supports (-webkit-touch-callout: none) {
          .settings-wrapper {
            min-height: -webkit-fill-available;
          }
        }
        
        /* iPad adjustments */
        .ipad .settings-container {
          padding-bottom: 2rem;
        }
        
        /* Landscape mode optimizations */
        @media screen and (orientation: landscape) and (max-height: 768px) {
          .settings-container {
            padding-top: 0.5rem;
            padding-bottom: 1rem;
          }
          
          .settings-wrapper {
            overscroll-behavior: contain;
          }
          
          .grid {
            grid-gap: 0.75rem;
          }
          
          .mt-6, .mt-8 {
            margin-top: 0.75rem;
          }
          
          .p-6, .p-4 {
            padding: 0.75rem;
          }
          
          .space-y-4 > * + * {
            margin-top: 0.5rem;
          }
        }
        
        /* Portrait iPad specific */
        @media screen and (orientation: portrait) and (min-width: 768px) and (max-width: 1024px) {
          .settings-container {
            max-width: 90vw;
          }
        }
        
        /* Better scrolling on iOS */
        * {
          -webkit-overflow-scrolling: touch;
        }
        
        /* Prevent zoom on inputs */
        input, select, textarea {
          font-size: 16px;
          max-height: 38px;
        }
        
        /* Better touch targets on mobile */
        button, input[type="checkbox"] {
          touch-action: manipulation;
        }
        
        /* Improve scrollbar on desktop */
        ::-webkit-scrollbar {
          width: 8px;
        }
        
        ::-webkit-scrollbar-track {
          background: #1f2937;
        }
        
        ::-webkit-scrollbar-thumb {
          background: #4b5563;
          border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: #6b7280;
        }
        
        /* Fix for common browser rendering issues */
        * {
          box-sizing: border-box;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
      `}</style>
    </div>
  );
};

export default Settings;