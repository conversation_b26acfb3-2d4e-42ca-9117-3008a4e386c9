import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  doc, 
  collection, 
  getDocs, 
  getDoc, 
  setDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  onSnapshot, 
  serverTimestamp 
} from 'firebase/firestore';

/**
 * Custom hook for Firestore operations
 * 
 * @param {Object} db - Firestore database instance
 * @param {Object} currentUser - Current authenticated user
 * @returns {Object} Firestore operations and state
 */
const useFirestore = (db, currentUser) => {
  // Collections state
  const [locations, setLocations] = useState([]);
  const [userLocations, setUserLocations] = useState([]);
  const [userTraces, setUserTraces] = useState({});
  const [chatMessages, setChatMessages] = useState([]);
  
  // UI state
  const [loading, setLoading] = useState({
    locations: false,
    users: false,
    messages: false
  });
  const [error, setError] = useState(null);
  
  // Listener cleanup references
  const listenersRef = useRef({
    locations: null,
    userLocations: null,
    userTraces: null,
    chatMessages: null
  });
  
  /**
   * Clear all error messages
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  /**
   * Set up a real-time listener for locations collection
   */
  const setupLocationsListener = useCallback(() => {
    if (!db) return null;
    
    setLoading(prev => ({ ...prev, locations: true }));
    
    try {
      // Clean up existing listener if any
      if (listenersRef.current.locations) {
        listenersRef.current.locations();
      }
      
      // Set up new listener
      const unsubscribe = onSnapshot(
        collection(db, 'locations'),
        (snapshot) => {
          const fetchedLocations = [];
          
          snapshot.forEach((doc) => {
            const data = doc.data();
            const location = {
              id: doc.id,
              name: data.name,
              position: {
                lat: data.position.lat,
                lng: data.position.lng
              },
              isAdminOnly: data.isAdminOnly || false,
              isPriority: data.isPriority || false,
              details: data.details || '',
              images: data.images || [],
              parkingSide: data.parkingSide || null,
              createdBy: data.createdBy || null,
              createdAt: data.createdAt || null,
              address: data.address || '',
              intersection: data.intersection || '',
              status: data.status || 'pending',
              pickedUpBy: data.pickedUpBy || null,
              pickedUpAt: data.pickedUpAt || null,
              // Vehicle-specific fields
              plateNumber: data.plateNumber || '',
              vin: data.vin || '',
              driveType: data.driveType || '',
              make: data.make || '',
              model: data.model || ''
            };
            
            fetchedLocations.push(location);
          });
          
          setLocations(fetchedLocations);
          setLoading(prev => ({ ...prev, locations: false }));
        },
        (err) => {
          console.error("Error in locations listener:", err);
          setError(`Failed to load locations: ${err.message}`);
          setLoading(prev => ({ ...prev, locations: false }));
        }
      );
      
      // Store unsubscribe function
      listenersRef.current.locations = unsubscribe;
      return unsubscribe;
    } catch (err) {
      console.error("Error setting up locations listener:", err);
      setError(`Failed to set up locations listener: ${err.message}`);
      setLoading(prev => ({ ...prev, locations: false }));
      return null;
    }
  }, [db]);
  
  /**
   * Set up a real-time listener for user locations
   */
  const setupUserLocationsListener = useCallback((maxDistance = 100) => {
    if (!db) return null;
    
    setLoading(prev => ({ ...prev, users: true }));
    
    try {
      // Clean up existing listener if any
      if (listenersRef.current.userLocations) {
        listenersRef.current.userLocations();
      }
      
      // Set up new listener
      const unsubscribe = onSnapshot(
        collection(db, 'userLocations'),
        (snapshot) => {
          const users = [];
          
          snapshot.forEach(doc => {
            const userData = doc.data();
            
            // Don't include current user in the list
            if (currentUser && userData.uid === currentUser.uid) return;
            
            users.push({
              ...userData,
              id: doc.id
            });
          });
          
          setUserLocations(users);
          setLoading(prev => ({ ...prev, users: false }));
        },
        (err) => {
          console.error("Error in user locations listener:", err);
          setError(`Failed to load user locations: ${err.message}`);
          setLoading(prev => ({ ...prev, users: false }));
        }
      );
      
      // Store unsubscribe function
      listenersRef.current.userLocations = unsubscribe;
      return unsubscribe;
    } catch (err) {
      console.error("Error setting up user locations listener:", err);
      setError(`Failed to set up user locations listener: ${err.message}`);
      setLoading(prev => ({ ...prev, users: false }));
      return null;
    }
  }, [db, currentUser]);
  
  /**
   * Set up a real-time listener for user traces
   */
  const setupUserTracesListener = useCallback(() => {
    if (!db) return null;
    
    try {
      // Clean up existing listener if any
      if (listenersRef.current.userTraces) {
        listenersRef.current.userTraces();
      }
      
      // Set up new listener
      const unsubscribe = onSnapshot(
        collection(db, 'userTraces'),
        (snapshot) => {
          const traces = {};
          
          snapshot.forEach(doc => {
            const traceData = doc.data();
            
            // Include all user traces
            traces[traceData.uid] = traceData.trace || [];
          });
          
          setUserTraces(traces);
        },
        (err) => {
          console.error("Error in user traces listener:", err);
          setError(`Failed to load user traces: ${err.message}`);
        }
      );
      
      // Store unsubscribe function
      listenersRef.current.userTraces = unsubscribe;
      return unsubscribe;
    } catch (err) {
      console.error("Error setting up user traces listener:", err);
      setError(`Failed to set up user traces listener: ${err.message}`);
      return null;
    }
  }, [db]);
  
  /**
   * Set up a real-time listener for chat messages
   */
  const setupChatMessagesListener = useCallback(() => {
    if (!db) return null;
    
    setLoading(prev => ({ ...prev, messages: true }));
    
    try {
      // Clean up existing listener if any
      if (listenersRef.current.chatMessages) {
        listenersRef.current.chatMessages();
      }
      
      // Set up new listener
      const unsubscribe = onSnapshot(
        query(collection(db, 'chatMessages')),
        (snapshot) => {
          const messages = [];
          
          snapshot.forEach((doc) => {
            const data = doc.data();
            
            // Convert Firestore timestamp to JS Date
            const timestamp = data.timestamp ? new Date(data.timestamp.toDate()) : new Date();
            
            messages.push({
              id: doc.id,
              text: data.text || '',
              sender: {
                uid: data.sender?.uid || 'unknown',
                name: data.sender?.name || 'Unknown User',
                photo: data.sender?.photo || null
              },
              timestamp: timestamp
            });
          });
          
          // Sort by timestamp
          messages.sort((a, b) => a.timestamp - b.timestamp);
          
          setChatMessages(messages);
          setLoading(prev => ({ ...prev, messages: false }));
        },
        (err) => {
          console.error("Error in chat messages listener:", err);
          setError(`Failed to load chat messages: ${err.message}`);
          setLoading(prev => ({ ...prev, messages: false }));
        }
      );
      
      // Store unsubscribe function
      listenersRef.current.chatMessages = unsubscribe;
      return unsubscribe;
    } catch (err) {
      console.error("Error setting up chat messages listener:", err);
      setError(`Failed to set up chat messages listener: ${err.message}`);
      setLoading(prev => ({ ...prev, messages: false }));
      return null;
    }
  }, [db]);
  
  /**
   * Add a new location to Firestore
   */
  const addLocation = useCallback(async (locationData) => {
    if (!db || !currentUser) {
      setError("You must be logged in to add locations.");
      return null;
    }
    
    try {
      // Add createdBy and timestamp
      const newLocationData = {
        ...locationData,
        createdBy: currentUser.uid,
        createdAt: serverTimestamp()
      };
      
      // Add to Firestore
      const docRef = await addDoc(collection(db, 'locations'), newLocationData);
      return docRef.id;
    } catch (err) {
      console.error("Error adding location:", err);
      setError(`Failed to add location: ${err.message}`);
      return null;
    }
  }, [db, currentUser]);
  
  /**
   * Update an existing location in Firestore
   */
  const updateLocation = useCallback(async (locationId, locationData) => {
    if (!db || !currentUser) {
      setError("You must be logged in to update locations.");
      return false;
    }
    
    try {
      // Get current location data
      const locationRef = doc(db, 'locations', locationId);
      const locationDoc = await getDoc(locationRef);
      
      if (!locationDoc.exists()) {
        setError("Location does not exist.");
        return false;
      }
      
      const currentData = locationDoc.data();
      
      // Check if user has permission to update
      const isAdmin = await checkIfUserIsAdmin(currentUser.uid);
      const isOwner = currentData.createdBy === currentUser.uid;
      
      if (!isAdmin && (!isOwner || currentData.isAdminOnly)) {
        setError("You don't have permission to update this location.");
        return false;
      }
      
      // Update in Firestore (keep original createdBy and createdAt)
      await updateDoc(locationRef, {
        ...locationData,
        updatedAt: serverTimestamp()
      });
      
      return true;
    } catch (err) {
      console.error("Error updating location:", err);
      setError(`Failed to update location: ${err.message}`);
      return false;
    }
  }, [db, currentUser]);
  
  /**
   * Delete a location from Firestore
   */
  const deleteLocation = useCallback(async (locationId) => {
    if (!db || !currentUser) {
      setError("You must be logged in to delete locations.");
      return false;
    }
    
    try {
      // Get current location data
      const locationRef = doc(db, 'locations', locationId);
      const locationDoc = await getDoc(locationRef);
      
      if (!locationDoc.exists()) {
        setError("Location does not exist.");
        return false;
      }
      
      const locationData = locationDoc.data();
      
      // Check if user has permission to delete
      const isAdmin = await checkIfUserIsAdmin(currentUser.uid);
      const isOwner = locationData.createdBy === currentUser.uid;
      
      if (!isAdmin && (!isOwner || locationData.isAdminOnly)) {
        setError("You don't have permission to delete this location.");
        return false;
      }
      
      // Delete from Firestore
      await deleteDoc(locationRef);
      return true;
    } catch (err) {
      console.error("Error deleting location:", err);
      setError(`Failed to delete location: ${err.message}`);
      return false;
    }
  }, [db, currentUser]);
  
  /**
   * Mark a location as picked up (for tow truck users)
   */
  const markLocationAsPickedUp = useCallback(async (locationId, pickupData = {}) => {
    if (!db || !currentUser) {
      setError("You must be logged in to mark locations as picked up.");
      return false;
    }
    
    try {
      // Get current location data
      const locationRef = doc(db, 'locations', locationId);
      const locationDoc = await getDoc(locationRef);
      
      if (!locationDoc.exists()) {
        setError("Location does not exist.");
        return false;
      }
      
      const locationData = locationDoc.data();
      
      // Check if location is already picked up
      if (locationData.status === 'picked-up') {
        setError("This location has already been picked up.");
        return false;
      }
      
      // Update with pickup details
      await updateDoc(locationRef, {
        status: 'picked-up',
        pickedUpBy: currentUser.uid,
        pickedUpAt: serverTimestamp(),
        // Add any additional pickup data
        ...pickupData
      });
      
      return true;
    } catch (err) {
      console.error("Error marking location as picked up:", err);
      setError(`Failed to mark location as picked up: ${err.message}`);
      return false;
    }
  }, [db, currentUser]);
  
  /**
   * Update user location in Firestore
   */
  const updateUserLocation = useCallback(async (position) => {
    if (!db || !currentUser) return false;
    
    try {
      const userDocRef = doc(db, 'userLocations', currentUser.uid);
      
      // Check if document exists
      const userDoc = await getDoc(userDocRef);
      
      const userData = {
        uid: currentUser.uid,
        email: currentUser.email || '',
        displayName: currentUser.displayName || '',
        position: {
          lat: position.lat,
          lng: position.lng
        },
        lastUpdated: serverTimestamp(),
        online: true
      };
      
      if (userDoc.exists()) {
        // Update existing document
        await updateDoc(userDocRef, userData);
      } else {
        // Create new document with specific ID
        await setDoc(userDocRef, userData);
      }
      
      return true;
    } catch (err) {
      console.error("Error updating user location:", err);
      setError(`Failed to update your location: ${err.message}`);
      return false;
    }
  }, [db, currentUser]);
  
  /**
   * Update user online status
   */
  const updateUserOnlineStatus = useCallback(async (isOnline) => {
    if (!db || !currentUser) return false;
    
    try {
      const userDocRef = doc(db, 'userLocations', currentUser.uid);
      
      // Check if document exists
      const userDoc = await getDoc(userDocRef);
      
      if (userDoc.exists()) {
        // Just update the online status and timestamp
        await updateDoc(userDocRef, {
          online: isOnline,
          lastUpdated: serverTimestamp()
        });
      } else if (isOnline) {
        // Only create document if setting to online
        const userData = {
          uid: currentUser.uid,
          email: currentUser.email || '',
          displayName: currentUser.displayName || '',
          position: null, // Will be set by location updates
          lastUpdated: serverTimestamp(),
          online: isOnline
        };
        
        await setDoc(userDocRef, userData);
      }
      
      return true;
    } catch (err) {
      console.error("Error updating user online status:", err);
      return false;
    }
  }, [db, currentUser]);
  
  /**
   * Update user trace (breadcrumb) in Firestore
   */
  const updateUserTrace = useCallback(async (position) => {
    if (!db || !currentUser) return false;
    
    try {
      const userTraceRef = doc(db, 'userTraces', currentUser.uid);
      
      // Check if document exists
      const userTraceDoc = await getDoc(userTraceRef);
      
      if (userTraceDoc.exists()) {
        // Update existing trace
        const existingTrace = userTraceDoc.data().trace || [];
        
        // Only add if we've moved enough from the last point
        if (existingTrace.length > 0) {
          const lastPoint = existingTrace[existingTrace.length - 1];
          const lastPosition = {
            lat: lastPoint.lat,
            lng: lastPoint.lng
          };
          
          // Calculate distance (requires the calculateDistance function)
          const distance = calculateDistance(lastPosition, position);
          
          if (distance > 0.006) { // ~30 feet (0.006 miles)
            await updateDoc(userTraceRef, {
              trace: [...existingTrace, {
                lat: position.lat,
                lng: position.lng,
                timestamp: serverTimestamp()
              }]
            });
          }
        } else {
          // First point in trace
          await updateDoc(userTraceRef, {
            trace: [{
              lat: position.lat,
              lng: position.lng,
              timestamp: serverTimestamp()
            }]
          });
        }
      } else {
        // Create new trace
        await setDoc(userTraceRef, {
          uid: currentUser.uid,
          trace: [{
            lat: position.lat,
            lng: position.lng,
            timestamp: serverTimestamp()
          }]
        });
      }
      
      return true;
    } catch (err) {
      console.error("Error updating user trace:", err);
      return false;
    }
  }, [db, currentUser]);
  
  /**
   * Clear user trace (breadcrumb) from Firestore
   */
  const clearUserTrace = useCallback(async (userId = null) => {
    if (!db) return false;
    
    try {
      // Use provided userId or current user's id
      const targetUserId = userId || (currentUser ? currentUser.uid : null);
      
      if (!targetUserId) {
        setError("No user specified for clearing trace.");
        return false;
      }
      
      // Check if current user is admin when clearing other user's trace
      if (userId && userId !== currentUser?.uid) {
        const isAdmin = await checkIfUserIsAdmin(currentUser?.uid);
        if (!isAdmin) {
          setError("Only admins can clear other users' traces.");
          return false;
        }
      }
      
      // Clear trace
      const userTraceRef = doc(db, 'userTraces', targetUserId);
      await updateDoc(userTraceRef, {
        trace: []
      });
      
      return true;
    } catch (err) {
      console.error("Error clearing user trace:", err);
      setError(`Failed to clear user trace: ${err.message}`);
      return false;
    }
  }, [db, currentUser]);
  
  /**
   * Check if user is an admin
   */
  const checkIfUserIsAdmin = useCallback(async (userId) => {
    if (!db || !userId) return false;
    
    try {
      const userDoc = await getDoc(doc(db, "userProfiles", userId));
      
      if (userDoc.exists() && userDoc.data().isAdmin) {
        return true;
      }
      
      return false;
    } catch (err) {
      console.error("Error checking admin status:", err);
      return false;
    }
  }, [db]);
  
  /**
   * Check if user has a specific tag
   */
  const checkUserHasTag = useCallback(async (userId, tagName) => {
    if (!db || !userId) return false;
    
    try {
      const profileDoc = await getDoc(doc(db, "userProfiles", userId));
      
      if (profileDoc.exists() && profileDoc.data().tags) {
        const tags = profileDoc.data().tags || [];
        return tags.includes(tagName);
      }
      
      return false;
    } catch (err) {
      console.error("Error checking user tag:", err);
      return false;
    }
  }, [db]);
  
  /**
   * Get user profile picture
   */
  const getUserProfilePicture = useCallback(async (userId) => {
    if (!db || !userId) return null;
    
    try {
      const profileDoc = await getDoc(doc(db, "userProfiles", userId));
      
      if (profileDoc.exists() && profileDoc.data().photoBase64) {
        return profileDoc.data().photoBase64;
      }
      
      return null;
    } catch (err) {
      console.error("Error fetching user profile picture:", err);
      return null;
    }
  }, [db]);
  
  /**
   * Get user display name
   */
  const getUserDisplayName = useCallback(async (userId) => {
    if (!db || !userId) return null;
    
    try {
      const profileDoc = await getDoc(doc(db, "userProfiles", userId));
      
      if (profileDoc.exists() && profileDoc.data().displayName) {
        return profileDoc.data().displayName;
      }
      
      return null;
    } catch (err) {
      console.error("Error fetching user display name:", err);
      return null;
    }
  }, [db]);
  
  /**
   * Send a chat message
   */
  const sendChatMessage = useCallback(async (messageText) => {
    if (!db || !currentUser || !messageText.trim()) return false;
    
    try {
      // Get display name and photo
      let displayName = currentUser.displayName || currentUser.email || '';
      let userPhoto = null;
      
      try {
        const profileDoc = await getDoc(doc(db, "userProfiles", currentUser.uid));
        if (profileDoc.exists()) {
          displayName = profileDoc.data().displayName || displayName;
          userPhoto = profileDoc.data().photoBase64 || null;
        }
      } catch (err) {
        console.warn("Error fetching user profile for chat:", err);
      }
      
      // Create message data
      const messageData = {
        text: messageText,
        sender: {
          uid: currentUser.uid,
          name: displayName,
          photo: userPhoto
        },
        timestamp: serverTimestamp()
      };
      
      // Add to Firestore
      await addDoc(collection(db, 'chatMessages'), messageData);
      return true;
    } catch (err) {
      console.error("Error sending chat message:", err);
      setError(`Failed to send message: ${err.message}`);
      return false;
    }
  }, [db, currentUser]);
  
  /**
   * Save a map screenshot
   */
  const saveMapScreenshot = useCallback(async (screenshotData) => {
    if (!db || !currentUser) {
      setError("You must be logged in to save screenshots.");
      return false;
    }
    
    try {
      // Verify if user is admin
      const isAdmin = await checkIfUserIsAdmin(currentUser.uid);
      if (!isAdmin) {
        setError("Only admins can save map screenshots.");
        return false;
      }
      
      // Add metadata
      const screenshotWithMeta = {
        ...screenshotData,
        createdBy: currentUser.uid,
        createdAt: serverTimestamp()
      };
      
      // Save to Firestore
      await addDoc(collection(db, 'mapScreenshots'), screenshotWithMeta);
      return true;
    } catch (err) {
      console.error("Error saving map screenshot:", err);
      setError(`Failed to save screenshot: ${err.message}`);
      return false;
    }
  }, [db, currentUser, checkIfUserIsAdmin]);
  
  /**
   * Update user clock status
   */
  const updateClockStatus = useCallback(async (isClockedIn) => {
    if (!db || !currentUser) return false;
    
    try {
      const userStatusRef = doc(db, 'userStatus', currentUser.uid);
      
      await setDoc(userStatusRef, {
        clockedIn: isClockedIn,
        [isClockedIn ? 'clockInTime' : 'clockOutTime']: serverTimestamp(),
        uid: currentUser.uid
      }, { merge: true });
      
      return true;
    } catch (err) {
      console.error(`Error updating clock ${isClockedIn ? 'in' : 'out'} status:`, err);
      setError(`Failed to update clock status: ${err.message}`);
      return false;
    }
  }, [db, currentUser]);
  
  /**
   * Clean up all listeners
   */
  const cleanupListeners = useCallback(() => {
    // Clean up each listener
    Object.entries(listenersRef.current).forEach(([key, unsubscribe]) => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
        listenersRef.current[key] = null;
      }
    });
  }, []);
  
  // Clean up listeners on unmount
  useEffect(() => {
    return () => {
      cleanupListeners();
    };
  }, [cleanupListeners]);
  
  // Utility function for distance calculation between two points
  const calculateDistance = (point1, point2) => {
    const R = 3958.8; // Earth's radius in MILES
    const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
    const φ2 = point2.lat * Math.PI/180;
    const Δφ = (point2.lat-point1.lat) * Math.PI/180;
    const Δλ = (point2.lng-point1.lng) * Math.PI/180;
  
    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  
    const d = R * c; // in MILES
    return d;
  };
  
  return {
    // Data collections
    locations,
    userLocations,
    userTraces,
    chatMessages,
    
    // Status
    loading,
    error,
    clearError,
    
    // Collection listeners
    setupLocationsListener,
    setupUserLocationsListener,
    setupUserTracesListener,
    setupChatMessagesListener,
    cleanupListeners,
    
    // Location operations
    addLocation,
    updateLocation,
    deleteLocation,
    markLocationAsPickedUp,
    
    // User operations
    updateUserLocation,
    updateUserOnlineStatus,
    updateUserTrace,
    clearUserTrace,
    checkIfUserIsAdmin,
    checkUserHasTag,
    getUserProfilePicture,
    getUserDisplayName,
    
    // Chat operations
    sendChatMessage,
    
    // Other operations
    saveMapScreenshot,
    updateClockStatus
  };
};

export default useFirestore;