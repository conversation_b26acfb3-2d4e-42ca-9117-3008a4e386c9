import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet.markercluster/dist/leaflet.markercluster.js';
import 'leaflet.markercluster/dist/MarkerCluster.css';
import 'leaflet.markercluster/dist/MarkerCluster.Default.css';
import 'leaflet-routing-machine';
import 'leaflet-routing-machine/dist/leaflet-routing-machine.css';
import './MapDisplay.css';
import TeamTrailSystem from './TeamTrailSystem';
import './teamZones.css';
import './TeamTrailStyles.css';

import { db } from './firebase';

// ENHANCED: Trail color constants and utilities
const USER_TRAIL_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', 
  '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA', 
  '#F1948A', '#85C0C7', '#D7BDE2', '#A9CCE3', '#F9E79F', '#A3E4D7', 
  '#FADBD8', '#D5DBDB'
];

const getUserTrailColor = (userId) => {
  if (!userId) return USER_TRAIL_COLORS[0];
  
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    const char = userId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  
  const colorIndex = Math.abs(hash) % USER_TRAIL_COLORS.length;
  return USER_TRAIL_COLORS[colorIndex];
};

// Route request cache and rate limiting
const ROUTE_REQUEST_CACHE = new Map();
const PENDING_REQUESTS = new Set();
const LAST_REQUEST_TIME = { time: 0 };
let MIN_REQUEST_INTERVAL = 1000;
const MAX_CACHE_SIZE = 50;
const MAX_RETRIES = 3;

// Enhanced Leaflet Path patch
const applyLeafletPathPatch = () => {
  if (typeof L === 'undefined' || !L.Path || !L.Path.prototype) return;

  if (!window._leafletPathRegistry) {
    window._leafletPathRegistry = new WeakMap();
  }

  const originalInitPath = L.Path.prototype._initPath;
  L.Path.prototype._initPath = function () {
    try {
      originalInitPath.call(this);
      if (this._path) {
        window._leafletPathRegistry.set(this, {
          hasPath: true,
          inDOM: !!this._path.parentNode
        });
      }
    } catch (e) {
      console.warn('Error in patched _initPath:', e);
      originalInitPath.call(this);
    }
  };

  const originalRemovePath = L.Path.prototype._removePath;
  L.Path.prototype._removePath = function () {
    try {
      const pathInfo = window._leafletPathRegistry.get(this);

      if (pathInfo && !pathInfo.hasPath) {
        console.debug('Path already marked as removed, skipping _removePath');
        return;
      }

      if (!this._path) {
        console.debug('No _path property, skipping _removePath');
        if (pathInfo) {
          window._leafletPathRegistry.set(this, { hasPath: false, inDOM: false });
        }
        return;
      }

      if (!this._path.parentNode) {
        console.debug('Path not in DOM, skipping _removePath');
        if (pathInfo) {
          window._leafletPathRegistry.set(this, { hasPath: true, inDOM: false });
        }
        return;
      }

      originalRemovePath.call(this);
      window._leafletPathRegistry.set(this, { hasPath: false, inDOM: false });
    } catch (e) {
      console.warn('Error in patched _removePath caught and handled:', e);

      if (this._path) {
        try {
          if (this._path.parentNode) {
            this._path.parentNode.removeChild(this._path);
          }
          this._path = null;
        } catch (cleanupError) {
          console.warn('Error during path cleanup:', cleanupError);
        }
      }

      window._leafletPathRegistry.set(this, { hasPath: false, inDOM: false });
    }
  };

  const originalRemoveLayer = L.LayerGroup.prototype.removeLayer;
  L.LayerGroup.prototype.removeLayer = function (layer) {
    if (!layer) {
      console.debug('Attempted to remove undefined layer, skipping');
      return this;
    }

    try {
      return originalRemoveLayer.call(this, layer);
    } catch (e) {
      console.warn('Error in LayerGroup.removeLayer caught and handled:', e);

      if (this._layers) {
        if (layer in this._layers) {
          delete this._layers[layer];
        } else if (layer._leaflet_id && this._layers[layer._leaflet_id]) {
          delete this._layers[layer._leaflet_id];
        }
      }

      return this;
    }
  };

  console.log("Enhanced Leaflet Path and LayerGroup patches applied");
};

// ENHANCED: ZIP Code Boundaries Manager with multi-state support
const ZipCodeBoundaryManager = {
  isLoaded: false,
  isLoading: false,
  boundariesVisible: false,
  stateGeoJsonPaths: {
    'il': '/data/il-zip-codes.geojson',
    'in': '/data/in-zip-codes.geojson',
    'wi': '/data/wi-zip-codes.geojson'
  },
  fallbackPaths: {
    'il': 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/il_illinois_zip_codes_geo.min.json',
    'in': 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/in_indiana_zip_codes_geo.min.json',
    'wi': 'https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/wi_wisconsin_zip_codes_geo.min.json'
  },
  stateLabels: {
    'il': 'Illinois',
    'in': 'Indiana',
    'wi': 'Wisconsin'
  },
  stateFeatureCollections: {
    'il': null,
    'in': null,
    'wi': null
  },
  layerGroups: { zoomedOut: null, zoomedIn: null },
  selectedZipCodes: [],
  loadAttempts: { 'il': 0, 'in': 0, 'wi': 0 },
  maxAttempts: 2,

  loadGeoJson: async function (mapRefs) {
    if (this.isLoading) return false;
    this.isLoading = true;

    try {
      console.log("Loading ZIP code boundaries for multiple states", this.stateGeoJsonPaths);
      
      if (!this.stateGeoJsonPaths || Object.keys(this.stateGeoJsonPaths).length === 0) {
        console.error("No valid file paths configured for ZIP code boundaries");
        this.isLoading = false;
        return false;
      }
      
      let loadedStates = [];
      const statePromises = [];
      
      for (const stateCode of ['il', 'in', 'wi']) {
        if (this.stateFeatureCollections[stateCode]) {
          loadedStates.push(stateCode);
          continue;
        }
        
        this.loadAttempts[stateCode] = (this.loadAttempts[stateCode] || 0) + 1;
        
        const statePromise = this.loadStateGeoJson(stateCode)
          .then(data => {
            if (data) {
              console.log(`Loaded ${data.features.length} ZIP boundaries for ${this.stateLabels[stateCode]}`);
              this.stateFeatureCollections[stateCode] = data;
              loadedStates.push(stateCode);
              return true;
            }
            return false;
          })
          .catch(error => {
            console.error(`Error loading ${stateCode.toUpperCase()} ZIP boundaries:`, error);
            return false;
          });
          
        statePromises.push(statePromise);
      }
      
      await Promise.allSettled(statePromises);

      this.isLoaded = loadedStates.length > 0;
      
      console.log(`Successfully loaded ${loadedStates.length} states: ${loadedStates.join(', ')}`);
      
      if (this.boundariesVisible && mapRefs.zoomedOutMapRef.current && mapRefs.zoomedInMapRef.current) {
        this.displayBoundaries(mapRefs.zoomedOutMapRef.current, mapRefs.zoomedInMapRef.current);
      }

      this.isLoading = false;
      return this.isLoaded;
    } catch (error) {
      console.error("Error loading ZIP code boundaries:", error);
      this.isLoaded = false;
      this.isLoading = false;
      return false;
    }
  },

  loadStateGeoJson: async function(stateCode) {
    if (!stateCode || !this.stateGeoJsonPaths[stateCode]) {
      console.error(`Invalid state code or missing path: ${stateCode}`);
      return null;
    }
    
    const primaryPath = this.stateGeoJsonPaths[stateCode];
    const fallbackPath = this.fallbackPaths[stateCode];
    const stateName = this.stateLabels[stateCode] || stateCode.toUpperCase();
    
    try {
      console.log(`Loading ${stateName} ZIP boundaries from: ${primaryPath}`);
      
      const response = await fetch(primaryPath, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        cache: 'no-store'
      });
      
      if (!response.ok) {
        console.warn(`Server returned ${response.status} for ${primaryPath}, trying fallback...`);
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.warn(`Non-JSON content type (${contentType}) received, checking content...`);
      }
      
      const text = await response.text();
      
      if (!text.trim().startsWith('{') && !text.trim().startsWith('[')) {
        console.warn(`Response is not valid JSON, appears to be HTML or other format`);
        throw new Error('Response is not valid JSON');
      }
      
      try {
        const data = JSON.parse(text);
        
        if (!data.type || data.type !== 'FeatureCollection' || !Array.isArray(data.features)) {
          console.warn(`Response is not a valid GeoJSON FeatureCollection`);
          throw new Error('Not a valid GeoJSON FeatureCollection');
        }
        
        console.log(`Successfully loaded ${stateName} GeoJSON from primary path`);
        return data;
      } catch (parseError) {
        console.error(`JSON parse error:`, parseError);
        throw new Error('Failed to parse JSON response');
      }
    } catch (primaryError) {
      if (fallbackPath) {
        try {
          console.log(`Trying fallback path for ${stateName}: ${fallbackPath}`);
          
          const fallbackResponse = await fetch(fallbackPath, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          });
          
          if (!fallbackResponse.ok) {
            throw new Error(`Fallback HTTP error ${fallbackResponse.status}`);
          }
          
          const fallbackData = await fallbackResponse.json();
          
          if (!fallbackData.type || fallbackData.type !== 'FeatureCollection' || !Array.isArray(fallbackData.features)) {
            throw new Error('Fallback is not a valid GeoJSON FeatureCollection');
          }
          
          console.log(`Successfully loaded ${stateName} GeoJSON from fallback path`);
          return fallbackData;
        } catch (fallbackError) {
          console.error(`Fallback path for ${stateName} also failed:`, fallbackError);
          return null;
        }
      } else {
        console.error(`No fallback path available for ${stateName}`);
        return null;
      }
    }
  },

  getFeatures: function() {
    const allFeatures = [];
    let featureCounts = {};
    
    Object.entries(this.stateFeatureCollections).forEach(([stateCode, collection]) => {
      if (collection && collection.features) {
        featureCounts[stateCode] = collection.features.length;
        
        const stateFeatures = collection.features.map(feature => ({
          ...feature,
          properties: {
            ...feature.properties,
            stateCode: stateCode
          }
        }));
        allFeatures.push(...stateFeatures);
      } else {
        featureCounts[stateCode] = 0;
      }
    });
    
    console.log(`Found features by state:`, featureCounts);
    console.log(`Total features to display: ${allFeatures.length}`);
    
    return allFeatures;
  },

  displayBoundaries: function (zoomedOutMap, zoomedInMap) {
    if (!this.isLoaded) {
      console.log("No GeoJSON data loaded");
      return false;
    }

    const features = this.getFeatures();
    if (!features || features.length === 0) {
      console.log("No features to display");
      return false;
    }

    this.clearBoundaries(zoomedOutMap, zoomedInMap);

    this.layerGroups.zoomedOut = L.layerGroup().addTo(zoomedOutMap);
    this.layerGroups.zoomedIn = L.layerGroup().addTo(zoomedInMap);

    const bounds = zoomedOutMap.getBounds();
    const zoom = zoomedOutMap.getZoom();

    let displayLimit = 100;
    let skipFactor = 1;

    if (zoom < 10) {
      displayLimit = 50;
      skipFactor = 10;
    } else if (zoom < 12) {
      displayLimit = 75;
      skipFactor = 5;
    }

    let featuresInView = features
      .filter((feature, index) => {
        if (index % skipFactor !== 0) return false;

        if (!feature.properties || !feature.geometry) return false;

        const zipCode = feature.properties.ZCTA5CE10 ||
          feature.properties.ZIP ||
          feature.properties.zipCode ||
          feature.properties.zip ||
          feature.properties.postalCode;

        if (!zipCode) return false;

        return true;
      })
      .slice(0, displayLimit);

    console.log(`Displaying ${featuresInView.length} ZIP boundaries`);

    const processBatch = (features, startIndex) => {
      const batchSize = 10;
      const endIndex = Math.min(startIndex + batchSize, features.length);

      for (let i = startIndex; i < endIndex; i++) {
        const feature = features[i];

        try {
          const zipCode = feature.properties.ZCTA5CE10 ||
            feature.properties.ZIP ||
            feature.properties.zipCode ||
            feature.properties.zip ||
            feature.properties.postalCode;

          if (!feature.geometry || !zipCode) continue;

          const isSelected = this.selectedZipCodes.includes(zipCode);
          
          const stateCode = feature.properties.stateCode || 'unknown';
          
          let stateColor;
          switch(stateCode) {
            case 'il': stateColor = '#3b82f6'; break;
            case 'in': stateColor = '#8b5cf6'; break;
            case 'wi': stateColor = '#f97316'; break;
            default: stateColor = '#3b82f6';
          }

          const polygonOptions = {
            color: isSelected ? '#10b981' : stateColor,
            weight: isSelected ? 2 : 1,
            opacity: 0.8,
            fillColor: isSelected ? '#10b981' : stateColor,
            fillOpacity: isSelected ? 0.4 : 0.2,
            className: `zip-boundary ${stateCode}`,
            interactive: true
          };

          const layer = L.geoJSON(feature, {
            style: polygonOptions
          });

          const tooltipContent = `${zipCode} (${stateCode.toUpperCase()})`;
            
          layer.bindTooltip(tooltipContent, {
            permanent: false,
            direction: 'center',
            className: 'zip-tooltip'
          });

          layer.on('click', (e) => {
            if (e && e.originalEvent) {
              e.originalEvent.stopPropagation();
            }

            if (this.selectedZipCodes.includes(zipCode)) {
              this.selectedZipCodes = this.selectedZipCodes.filter(code => code !== zipCode);
              layer.setStyle({
                fillColor: stateColor,
                color: stateColor,
                fillOpacity: 0.2,
                weight: 1
              });
            } else {
              this.selectedZipCodes.push(zipCode);
              layer.setStyle({
                fillColor: '#10b981',
                color: '#10b981',
                fillOpacity: 0.4,
                weight: 2
              });
            }
          });

          if (this.layerGroups.zoomedOut) {
            this.layerGroups.zoomedOut.addLayer(layer);
          }

          if (this.layerGroups.zoomedIn) {
            const layerClone = L.geoJSON(feature, {
              style: polygonOptions
            });

            layerClone.bindTooltip(tooltipContent, {
              permanent: false,
              direction: 'center',
              className: 'zip-tooltip'
            });

            layerClone.on('click', (e) => {
              if (e && e.originalEvent) {
                e.originalEvent.stopPropagation();
              }

              if (this.selectedZipCodes.includes(zipCode)) {
                this.selectedZipCodes = this.selectedZipCodes.filter(code => code !== zipCode);
                layerClone.setStyle({
                  fillColor: stateColor,
                  color: stateColor,
                  fillOpacity: 0.2,
                  weight: 1
                });
              } else {
                this.selectedZipCodes.push(zipCode);
                layerClone.setStyle({
                  fillColor: '#10b981',
                  color: '#10b981',
                  fillOpacity: 0.4,
                  weight: 2
                });
              }
            });

            this.layerGroups.zoomedIn.addLayer(layerClone);
          }
        } catch (error) {
          console.warn("Error processing boundary:", error);
        }
      }

      if (endIndex < features.length) {
        setTimeout(() => {
          processBatch(features, endIndex);
        }, 0);
      }
    };

    processBatch(featuresInView, 0);

    this.boundariesVisible = true;
    return true;
  },

  clearBoundaries: function (zoomedOutMap, zoomedInMap) {
    if (this.layerGroups.zoomedOut && zoomedOutMap) {
      zoomedOutMap.removeLayer(this.layerGroups.zoomedOut);
      this.layerGroups.zoomedOut = null;
    }

    if (this.layerGroups.zoomedIn && zoomedInMap) {
      zoomedInMap.removeLayer(this.layerGroups.zoomedIn);
      this.layerGroups.zoomedIn = null;
    }

    this.boundariesVisible = false;
  },

  toggleBoundaries: function (zoomedOutMap, zoomedInMap) {
    if (this.boundariesVisible) {
      this.clearBoundaries(zoomedOutMap, zoomedInMap);
    } else {
      if (this.isLoaded) {
        this.displayBoundaries(zoomedOutMap, zoomedInMap);
      } else if (!this.isLoading) {
        this.loadGeoJson({
          zoomedOutMapRef: { current: zoomedOutMap },
          zoomedInMapRef: { current: zoomedInMap }
        });
      }
    }

    return this.boundariesVisible;
  }
};

// Add CSS styles for enhanced visuals and vehicle markers
const addMapStyles = () => {
  if (!document.getElementById('map-display-styles')) {
    const styleEl = document.createElement('style');
    styleEl.id = 'map-display-styles';
    styleEl.textContent = `
      .zip-boundary {
        transition: all 0.2s ease;
      }
      
      .zip-boundary:hover {
        fillOpacity: 0.3 !important;
        weight: 2 !important;
        cursor: pointer;
      }
      
      .zip-tooltip {
        background: rgba(0, 0, 0, 0.7) !important;
        border: none !important;
        color: white !important;
        font-weight: bold;
        padding: 2px 6px !important;
      }
      
      .zip-boundary.il:hover {
        fill-opacity: 0.4 !important;
      }
      
      .zip-boundary.in:hover {
        fill-opacity: 0.4 !important;
      }
      
      .zip-boundary.wi:hover {
        fill-opacity: 0.4 !important;
      }

      .current-location-marker {
        background: transparent !important;
        border: none !important;
      }

      .current-marker-inner {
        width: 20px;
        height: 20px;
        background-color: #3B82F6;
        border: 3px solid #FFFFFF;
        border-radius: 50%;
        position: relative;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
      }

      .current-marker-inner.pulse {
        animation: pulse-blue 2s infinite;
      }

      @keyframes pulse-blue {
        0% {
          box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
        }
        70% {
          box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
        }
      }

      /* ENHANCED: Vehicle marker styles */
      .vehicle-marker {
        background: transparent !important;
        border: none !important;
        z-index: 1000 !important;
      }

      .vehicle-marker-container {
        cursor: pointer !important;
        transition: transform 0.2s ease;
        pointer-events: auto !important;
      }

      .vehicle-marker-container:hover {
        transform: scale(1.1);
      }

      .vehicle-marker-bg {
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        transition: box-shadow 0.2s ease;
      }

      .vehicle-marker-container:hover .vehicle-marker-bg {
        box-shadow: 0 4px 12px rgba(0,0,0,0.4);
      }

      /* Pulse animation for vehicle markers */
      @keyframes vehicle-pulse {
        0% { 
          transform: scale(1); 
          opacity: 0.3; 
        }
        50% { 
          transform: scale(1.2); 
          opacity: 0.1; 
        }
        100% { 
          transform: scale(1); 
          opacity: 0.3; 
        }
      }

      .vehicle-marker-pulse {
        animation: vehicle-pulse 2s infinite;
      }

      /* Priority marker animation */
      .vehicle-marker.priority .vehicle-marker-pulse {
        animation: vehicle-pulse 1s infinite;
      }

      /* Team vehicle marker styles */
      .team-vehicle-marker {
        background: transparent !important;
        border: none !important;
        z-index: 2000 !important;
      }
      
      .team-vehicle-marker .leaflet-marker-icon {
        z-index: 2000 !important;
      }

      .team-vehicle-marker-bg {
        border: 3px solid #9333EA;
        box-shadow: 0 0 0 2px rgba(147, 51, 234, 0.3);
      }

      .team-vehicle-marker.in-route .team-vehicle-marker-bg {
        border-color: #3B82F6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
      }

      .team-vehicle-marker.arrived .team-vehicle-marker-bg {
        border-color: #10B981;
        box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.5);
      }

      .team-vehicle-marker.bottom-status .team-vehicle-marker-bg {
        border-color: #EF4444;
        box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
      }

      .heading-indicator {
        position: absolute;
        top: -8px;
        left: 50%;
        transform-origin: center;
        margin-left: -8px;
        z-index: 1000;
      }

      .heading-indicator svg {
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
      }

      .team-member-marker {
        background: transparent !important;
        border: none !important;
        z-index: 1000 !important;
      }

      .team-member-marker .pulse-ring {
        animation: pulse-ring 2s infinite;
      }

      @keyframes pulse-ring {
        0% {
          transform: scale(0.8);
          opacity: 1;
        }
        100% {
          transform: scale(2.4);
          opacity: 0;
        }
      }

      /* ENHANCED: Trail line styles for colored trails */
      .user-trail-line {
        stroke-width: 3;
        stroke-opacity: 0.8;
        fill: none;
        stroke-linecap: round;
        stroke-linejoin: round;
        transition: stroke-width 0.2s ease;
      }
      
      .user-trail-line.current-user {
        stroke-width: 4;
        stroke-opacity: 1.0;
      }
      
      .user-trail-line:hover {
        stroke-width: 5;
        stroke-opacity: 1.0;
      }
      
      .user-trail-arrow {
        fill-opacity: 0.8;
      }

      /* Enhanced team member marker styles */
      .team-marker-container {
        position: relative;
      }

      .team-marker-bg {
        border-radius: 50%;
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
      }

      .team-marker-bg:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(0,0,0,0.4);
      }

      .online-indicator {
        animation: online-pulse 2s infinite;
      }

      @keyframes online-pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }

      /* Leaflet marker icon fixes */
      .leaflet-marker-icon {
        background: transparent !important;
        border: none !important;
      }

      .leaflet-div-icon {
        background: transparent !important;
        border: none !important;
      }

      /* Order popup styles */
      .order-popup {
        min-width: 200px;
      }

      .order-popup button {
        transition: all 0.2s ease;
      }

      .order-popup button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      }

      /* Manual location markers */
      .manual-location-marker {
        background: transparent !important;
        border: none !important;
      }

      .location-marker-container {
        cursor: pointer !important;
        transition: transform 0.2s ease;
      }

      .location-marker-container:hover {
        transform: scale(1.1);
      }
      
      /* Make vehicle popups distinctive */
      .team-vehicle-popup-wrapper .leaflet-popup-content-wrapper {
        padding: 0 !important;
        overflow: hidden;
        border-radius: 8px;
      }
      
      .team-vehicle-popup-wrapper .leaflet-popup-content {
        margin: 0 !important;
        padding: 20px !important;
      }
      
      .team-vehicle-popup-wrapper .leaflet-popup-tip {
        background: #7C3AED;
      }
    `;
    document.head.appendChild(styleEl);
  }
};

const MapDisplay = ({
  mapRef,
  mapContainerRef,
  currentMarkerRef,
  userLabelRef,
  markerClusterRef,
  userMarkerClusterRef,
  routingControlRef,
  navigationElementsRef,
  breadcrumbPathsRef,
  currentLocation,
  handleMapClick,
  isAddingMarker,
  isAddingAdminMarker,
  isSettingLocation,
  newMarkerPosition,
  setNewMarkerPosition,
  lastKnownPositionRef,
  error,
  isLoading: parentIsLoading,
  setIsLoading,
  isMapRefreshing,
  findMyLocation,
  refreshMap,
  clearTrail,
  isAdmin,
  isClockedIn,
  hasMovedSinceClockIn,
  showUserActionPopup,
  userForAction,
  userActionPopupPosition,
  setShowUserActionPopup,
  handleStartDM,
  userDisplayNames,
  userProfilePictures,
  showUserInDetailsPanel,
  confirmDeleteTrail,
  deleteUserTrail,
  userToDeleteTrail,
  setShowDeleteConfirmation,
  showDeleteConfirmation,
  screenConfig,
  teamId,
  setNavigationDirection = () => { },
  setDistanceToDestination = () => { },
  setEstimatedTime = () => { },
  setArrivalTime = () => { },
  setDestinationAddress = () => { },
  onNavigationStatusChange = () => { },
  // ENHANCED: Team members with location and trail color data
  teamMembers = [],
  onlineUsers = [],
  currentUser,
  // REMOVED: Old locations prop - now using processedMarkers
  handleSelectLocation = () => { },
  showZipBoundaries = true,
  zipCodeGeoJsonPath = '/data/il_illinois_zip_codes_geo.min.json',
  // ENHANCED: Optional callback for updating user location data with live tracking
  updateUserLocationInDatabase = null,
  // ENHANCED: Optional callback for requesting team member updates
  requestTeamMemberUpdate = null,
  // ENHANCED: Optional callback for when location data changes
  onLocationUpdate = null,
  // NEW: Processed markers from MarkerManager
  processedMarkers = null,
  isProcessingMarkers = false,
  // NEW: Team vehicles from TeamVehicleTracker
  teamVehicles = [],
  onTeamVehicleClick = null,
  onTeamVehicleNavigate = null
}) => {
  // Local loading state and refs
  const [internalLoading, setInternalLoading] = useState(true);
  const initAttemptedRef = useRef(false);
  const loadingTimeoutRef = useRef(null);
  const orientationChangeTimeoutRef = useRef(null);
  const resizeObserverRef = useRef(null);
  const boundsChangeTimeoutRef = useRef(null);

  // Refs for both maps
  const zoomedOutMapRef = useRef(null);
  const zoomedInMapRef = useRef(null);
  const zoomedOutContainerRef = useRef(null);
  const zoomedInContainerRef = useRef(null);
  const zoomedInMapWrapperRef = useRef(null);

  // Track if we are dragging map to prevent auto-centering
  const userHasDraggedMap = useRef(false);

  // Refs for marker clusters in both maps
  const zoomedOutMarkerClusterRef = useRef(null);
  const zoomedInMarkerClusterRef = useRef(null);
  const zoomedOutUserMarkerClusterRef = useRef(null);
  const zoomedInUserMarkerClusterRef = useRef(null);

  // Refs for breadcrumb paths in both maps
  const zoomedOutBreadcrumbPathsRef = useRef({});
  const zoomedInBreadcrumbPathsRef = useRef({});

  // NEW: Refs for markers created from processed data
  const processedMarkersRef = useRef({
    zoomedOut: new Map(),
    zoomedIn: new Map()
  });

  // ENHANCED: Team Member Marker Management with trail colors
  const teamMemberMarkersRef = useRef({
    zoomedOut: new Map(),
    zoomedIn: new Map()
  });

  // NEW: Team Vehicle Marker Management
  const teamVehicleMarkersRef = useRef({
    zoomedOut: new Map(),
    zoomedIn: new Map()
  });

  // Routing controls references for both maps
  const zoomedOutRoutingControlRef = useRef(null);
  const zoomedInRoutingControlRef = useRef(null);

  // ENHANCED: Location tracking with more frequent updates
  const locationWatchIdRef = useRef(null);
  const initialLocationRequestedRef = useRef(false);

  // ENHANCED: State for location tracking status
  const [locationTrackingEnabled, setLocationTrackingEnabled] = useState(true);

  // ENHANCED: More frequent location updates
  const lastLocationUpdateRef = useRef(Date.now());
  const locationUpdateIntervalRef = useRef(1000); // Update server every 1 second

  // Route information
  const [routeInfo, setRouteInfo] = useState(null);
  const [nextManeuver, setNextManeuver] = useState(null);

  // Destination and rotation state
  const destinationRef = useRef(null);
  const [mapRotation, setMapRotation] = useState(0);
  const [mapScale, setMapScale] = useState(1.5);
  const [isRotationEnabled, setIsRotationEnabled] = useState(true);

  // Track markers created
  const currentLocationMarkerRef = useRef(null);
  const destinationMarkerRef = useRef(null);

  // Track route lines
  const routeLineRef = useRef(null);

  // Track update cycles to prevent race conditions
  const updateInProgressRef = useRef(false);

  // ZIP code boundaries state
  const [showZipCodes, setShowZipCodes] = useState(showZipBoundaries);

  // Debug state
  const [debugInfo, setDebugInfo] = useState({
    currentLocationReceived: false,
    currentMarkerCreated: false,
    destinationSet: false
  });

  const lastScreenSizeRef = useRef({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0
  });

  // Constants for zoom levels
  const ZOOMED_OUT_LEVEL = 15;
  const ZOOMED_IN_LEVEL = 19;

  // Default routing options with improved stability
  const DEFAULT_ROUTING_OPTIONS = {
    show: false,
    collapsible: true,
    showAlternatives: false,
    routeWhileDragging: false,
    fitSelectedRoutes: false,
    draggableWaypoints: false,
    autoRoute: true,
    addWaypoints: false,
    useZoomParameter: false,
    lineOptions: {
      styles: [
        { color: '#4a89f3', opacity: 0.8, weight: 5 },
        { color: '#2a69d3', opacity: 0.9, weight: 3, className: 'routing-line-inner' }
      ],
      extendToWaypoints: true,
      missingRouteTolerance: 10,
      className: 'custom-route-path'
    },
    router: L.Routing.osrmv1({
      serviceUrl: 'https://router.project-osrm.org/route/v1',
      profile: 'driving',
      suppressDemoServerWarning: true,
      timeout: 30 * 1000,
      geometryOnly: false
    }),
    createMarker: function () { return null; }
  };

  // ENHANCED: Apply Leaflet patches and add styles
  useEffect(() => {
    applyLeafletPathPatch();
    addMapStyles();
  }, []);

  // NEW: Create Leaflet marker from processed marker data
  const createMarkerFromProcessedData = useCallback((markerData, isZoomedIn = false) => {
    if (!markerData || !markerData.position) {
      console.warn("Invalid marker data:", markerData);
      return null;
    }

    let icon;
    const size = isZoomedIn ? (markerData.size || 28) + 4 : markerData.size || 28;

    // Create icon based on marker type
    switch (markerData.iconType) {
      case 'vehicle':
        icon = createVehicleIcon(markerData, size);
        break;
      case 'user-avatar':
        icon = createUserAvatarIcon(markerData, size);
        break;
      case 'location-pin':
        icon = createLocationPinIcon(markerData, size);
        break;
      default:
        icon = createGenericIcon(markerData, size);
    }

    const marker = L.marker([markerData.position.lat, markerData.position.lng], {
      icon: icon,
      title: markerData.name || 'Marker',
      zIndexOffset: markerData.zIndex || 100,
      riseOnHover: true
    });

    // Add popup if marker has popup content
    if (markerData.showPopup && markerData.popupContent) {
      marker.bindPopup(markerData.popupContent, { maxWidth: 250 });
    }

    // Add click handler
    if (markerData.clickable && markerData.onClick) {
      marker.on('click', () => {
        console.log(`Marker clicked: ${markerData.id} (${markerData.type})`);
        markerData.onClick();
      });
    }

    // Add popup event handlers for dynamic content
    marker.on('popupopen', () => {
      setTimeout(() => {
        // Handle order details button
        const detailsBtn = document.getElementById(`order-details-btn-${markerData.sourceOrder || markerData.id}`);
        if (detailsBtn && markerData.onClick) {
          detailsBtn.addEventListener('click', () => {
            markerData.onClick();
            marker.closePopup();
          });
        }

        // Handle navigate button
        const navigateBtn = document.getElementById(`order-navigate-btn-${markerData.sourceOrder || markerData.id}`);
        if (navigateBtn && markerData.onNavigate) {
          navigateBtn.addEventListener('click', () => {
            markerData.onNavigate();
            marker.closePopup();
          });
        }

        // Handle location details button
        const locationDetailsBtn = document.getElementById(`location-details-btn-${markerData.id}`);
        if (locationDetailsBtn && markerData.onClick) {
          locationDetailsBtn.addEventListener('click', () => {
            markerData.onClick();
            marker.closePopup();
          });
        }

        // Handle location navigate button
        const locationNavigateBtn = document.getElementById(`location-navigate-btn-${markerData.id}`);
        if (locationNavigateBtn && markerData.onNavigate) {
          locationNavigateBtn.addEventListener('click', () => {
            markerData.onNavigate();
            marker.closePopup();
          });
        }
      }, 10);
    });

    return marker;
  }, []);

  // Create vehicle icon from marker data
  const createVehicleIcon = useCallback((markerData, size) => {
    const bgColor = markerData.markerColor || '#2563EB';
    const iconPath = markerData.iconPath || "M9,11H11V9H9M9,15H11V13H9M9,7H11V5H9M9,3H11V1H9M13,5H15V3H13M17,11V5H15V7H13V9H15V11M17,15H19V13H17V11H15V13H13V15H15V17H17M1,17H5V15H3V13H1V17M5,11V9H3V11M1,9H3V7H5V5H1V9M5,3V1H1V5H5Z";
    
    return L.divIcon({
      className: `vehicle-marker ${markerData.priority ? 'priority' : ''} status-${markerData.status || 'unknown'}`,
      html: `
        <div class="vehicle-marker-container" style="width:${size}px; height:${size}px; position: relative;">
          <div class="vehicle-marker-bg" style="
            background-color: ${bgColor}; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            position: relative; 
            border: 2px solid #FFFFFF;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
          ">
            <svg viewBox="0 0 24 24" style="
              width: ${size * 0.65}px; 
              height: ${size * 0.65}px;
              fill: #FFFFFF;
            ">
              <path d="${iconPath}" />
            </svg>
          </div>
          <div class="vehicle-marker-pulse" style="
            position: absolute; 
            top: 0; 
            left: 0; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            background-color: ${bgColor}; 
            opacity: 0.3; 
            z-index: -1;
            animation: vehicle-pulse 2s infinite;
          "></div>
        </div>
      `,
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2]
    });
  }, []);

  // NEW: Create team vehicle icon - FIXED VERSION
  const createTeamVehicleIcon = useCallback((vehicleData, size = 36) => {
    let bgColor = '#9333EA'; // Purple for team vehicles
    let statusClass = 'team-vehicle-marker';
    let pulseAnimation = '';
    
    // Status-based colors
    if (vehicleData.arrivedDriverId) {
      bgColor = '#10B981'; // Green for arrived
      statusClass += ' arrived';
    } else if (vehicleData.inRouteDriverId) {
      bgColor = '#3B82F6'; // Blue for in route
      statusClass += ' in-route';
      pulseAnimation = 'animation: vehicle-pulse 1.5s infinite;';
    } else if (vehicleData.bottomStatus) {
      bgColor = '#EF4444'; // Red for bottom status
      statusClass += ' bottom-status';
    }
    
    // Use a car icon that's clearly different from user icons
    const carIconPath = "M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 15c-.83 0-1.5-.67-1.5-1.5S5.67 12 6.5 12s1.5.67 1.5 1.5S7.33 15 6.5 15zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 10l1.5-4.5h11L19 10H5z";
    
    return L.divIcon({
      className: statusClass,
      html: `
        <div class="vehicle-marker-container" style="width:${size}px; height:${size}px; position: relative;">
          <!-- Outer ring to make it more visible -->
          <div style="
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border: 3px solid ${bgColor};
            border-radius: 50%;
            opacity: 0.3;
          "></div>
          
          <div class="team-vehicle-marker-bg vehicle-marker-bg" style="
            background-color: ${bgColor}; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            position: relative; 
            border: 3px solid #FFFFFF;
            box-shadow: 0 4px 8px rgba(0,0,0,0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
          ">
            <svg viewBox="0 0 24 24" style="
              width: ${size * 0.65}px; 
              height: ${size * 0.65}px;
              fill: #FFFFFF;
            ">
              <path d="${carIconPath}" />
            </svg>
          </div>
          ${pulseAnimation ? `
            <div class="vehicle-marker-pulse" style="
              position: absolute; 
              top: 0; 
              left: 0; 
              width: ${size}px; 
              height: ${size}px; 
              border-radius: 50%; 
              background-color: ${bgColor}; 
              opacity: 0.3; 
              z-index: -1;
              ${pulseAnimation}
            "></div>
          ` : ''}
        </div>
      `,
      iconSize: [size + 8, size + 8], // Make icon size slightly larger to account for outer ring
      iconAnchor: [(size + 8) / 2, (size + 8) / 2]
    });
  }, []);

  // Create user avatar icon from marker data
  const createUserAvatarIcon = useCallback((markerData, size) => {
    const isOnline = markerData.online || false;
    const userColor = markerData.markerColor || getUserTrailColor(markerData.userId);
    const profilePicture = markerData.profilePicture || '';
    const displayName = markerData.name || `User ${markerData.userId}`;
    
    let headingIndicator = '';
    if (markerData.heading !== undefined && markerData.heading !== null) {
      headingIndicator = `
        <div class="heading-indicator" style="transform: rotate(${markerData.heading}deg); position: absolute; top: -8px; left: 50%; transform-origin: center;">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="12" height="12">
            <path fill="#FFFFFF" d="M12,2L4,13h16L12,2z"/>
          </svg>
        </div>
      `;
    }

    return L.divIcon({
      className: `team-member-marker ${isOnline ? 'online' : 'offline'}`,
      html: `
        <div class="team-marker-container" style="width:${size}px; height:${size}px; position: relative;">
          <div class="team-marker-bg" style="
            background-color: ${userColor}; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            border: 3px solid white;
            position: relative;
            ${isOnline ? 'box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);' : ''}
          ">
            ${profilePicture ? 
              `<img src="${profilePicture}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;" />` :
              `<div style="
                width: 100%; 
                height: 100%; 
                display: flex; 
                align-items: center; 
                justify-content: center; 
                color: white; 
                font-weight: bold; 
                font-size: ${size * 0.4}px;
              ">${displayName.charAt(0).toUpperCase()}</div>`
            }
            ${isOnline ? `<div class="online-indicator" style="
              position: absolute; 
              bottom: 2px; 
              right: 2px; 
              width: 8px; 
              height: 8px; 
              background-color: #10B981; 
              border-radius: 50%; 
              border: 2px solid white;
            "></div>` : ''}
          </div>
          ${headingIndicator}
          ${isOnline ? `<div class="pulse-ring" style="
            position: absolute; 
            top: 0; 
            left: 0; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            background-color: ${userColor}; 
            opacity: 0.3; 
            animation: pulse 2s infinite;
          "></div>` : ''}
        </div>
      `,
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2]
    });
  }, []);

  // Create location pin icon from marker data
  const createLocationPinIcon = useCallback((markerData, size) => {
    const bgColor = markerData.markerColor || '#6B7280';
    const iconPath = markerData.iconPath || "M12,2C8.13,2 5,5.13 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9C19,5.13 15.87,2 12,2M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5Z";
    
    return L.divIcon({
      className: 'manual-location-marker',
      html: `
        <div class="location-marker-container" style="width:${size}px; height:${size}px;">
          <div style="
            background-color: ${bgColor}; 
            width: ${size}px; 
            height: ${size}px; 
            border-radius: 50%; 
            position: relative; 
            border: 2px solid #FFFFFF;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
          ">
            <svg viewBox="0 0 24 24" style="
              width: ${size * 0.7}px; 
              height: ${size * 0.7}px;
              fill: #FFFFFF;
            ">
              <path d="${iconPath}" />
            </svg>
          </div>
        </div>
      `,
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2]
    });
  }, []);

  // Create generic icon from marker data
  const createGenericIcon = useCallback((markerData, size) => {
    const bgColor = markerData.markerColor || '#6B7280';
    
    return L.divIcon({
      className: 'generic-marker',
      html: `
        <div style="
          background-color: ${bgColor}; 
          width: ${size}px; 
          height: ${size}px; 
          border-radius: 50%; 
          border: 2px solid #FFFFFF;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: ${size * 0.3}px;
        ">
          ?
        </div>
      `,
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2]
    });
  }, []);

  // Set destination with improved error handling - MOVED BEFORE CALLBACKS
  const setDestination = (lat, lng) => {
    if (updateInProgressRef.current) return false;
    updateInProgressRef.current = true;

    console.log(`Setting destination to: ${lat}, ${lng}`);

    try {
      destinationRef.current = { lat, lng };

      setDebugInfo(prev => ({
        ...prev,
        destinationSet: true
      }));

      if (currentLocation && currentLocation.lat && currentLocation.lng) {
        setRoutingWaypoints(currentLocation, { lat, lng });
      }

      if (zoomedOutMapRef.current && zoomedInMapRef.current) {
        if (destinationMarkerRef.current) {
          if (destinationMarkerRef.current.zoomed_out) {
            if (zoomedOutMarkerClusterRef.current) {
              safeRemoveFromCluster(zoomedOutMarkerClusterRef.current, destinationMarkerRef.current.zoomed_out);
            } else {
              safeRemoveLayer(zoomedOutMapRef.current, destinationMarkerRef.current.zoomed_out);
            }
          }

          if (destinationMarkerRef.current.zoomed_in) {
            if (zoomedInMarkerClusterRef.current) {
              safeRemoveFromCluster(zoomedInMarkerClusterRef.current, destinationMarkerRef.current.zoomed_in);
            } else {
              safeRemoveLayer(zoomedInMapRef.current, destinationMarkerRef.current.zoomed_in);
            }
          }
        }

        const destinationIcon = L.divIcon({
          className: 'destination-marker',
          html: '<div class="destination-marker-inner"></div>',
          iconSize: [32, 32],
          iconAnchor: [16, 16]
        });

        const destinationMarker = L.marker([lat, lng], { icon: destinationIcon });
        const destinationMarkerClone = L.marker([lat, lng], { icon: destinationIcon });

        const popupContent = `
          <div class="text-center">
            <strong>Navigation Destination</strong><br>
            <span class="text-xs">${lat.toFixed(6)}, ${lng.toFixed(6)}</span><br>
            <button id="center-maps-btn" class="bg-blue-600 text-white px-2 py-1 rounded text-xs mt-1">
              Center Maps Here
            </button>
          </div>
        `;

        destinationMarker.bindPopup(popupContent);
        destinationMarkerClone.bindPopup(popupContent);

        destinationMarker.on('popupopen', () => {
          setTimeout(() => {
            const centerBtn = document.getElementById('center-maps-btn');
            if (centerBtn) {
              centerBtn.addEventListener('click', () => {
                zoomedOutMapRef.current.setView([lat, lng], ZOOMED_OUT_LEVEL, { animate: true });
                zoomedInMapRef.current.setView([lat, lng], ZOOMED_IN_LEVEL, { animate: true });
              });
            }
          }, 10);
        });

        let zoomedOutAdded = false;
        let zoomedInAdded = false;

        try {
          if (zoomedOutMarkerClusterRef.current) {
            try {
              zoomedOutMarkerClusterRef.current.addLayer(destinationMarkerClone);
              zoomedOutAdded = true;
            } catch (e) {
              console.warn("Error adding to zoomed out cluster:", e);
              try {
                zoomedOutMapRef.current.addLayer(destinationMarkerClone);
                zoomedOutAdded = true;
              } catch (mapError) {
                console.warn("Error adding to zoomed out map:", mapError);
              }
            }
          } else if (zoomedOutMapRef.current) {
            try {
              zoomedOutMapRef.current.addLayer(destinationMarkerClone);
              zoomedOutAdded = true;
            } catch (mapError) {
              console.warn("Error adding to zoomed out map:", mapError);
            }
          }
        } catch (outError) {
          console.warn("Error handling zoomed out marker:", outError);
        }

        try {
          if (zoomedInMarkerClusterRef.current) {
            try {
              zoomedInMarkerClusterRef.current.addLayer(destinationMarker);
              zoomedInAdded = true;
            } catch (e) {
              console.warn("Error adding to zoomed in cluster:", e);
              try {
                zoomedInMapRef.current.addLayer(destinationMarker);
                zoomedInAdded = true;
              } catch (mapError) {
                console.warn("Error adding to zoomed in map:", mapError);
              }
            }
          } else if (zoomedInMapRef.current) {
            try {
              zoomedInMapRef.current.addLayer(destinationMarker);
              zoomedInAdded = true;
            } catch (mapError) {
              console.warn("Error adding to zoomed in map:", mapError);
            }
          }
        } catch (inError) {
          console.warn("Error handling zoomed in marker:", inError);
        }

        if (zoomedOutAdded && zoomedInAdded) {
          destinationMarkerRef.current = {
            zoomed_out: destinationMarkerClone,
            zoomed_in: destinationMarker
          };
        }

        if (!isRotationEnabled) {
          setIsRotationEnabled(true);
        }

        updateMapRotation();

        try {
          zoomedOutMapRef.current.setView([lat, lng], ZOOMED_OUT_LEVEL, { animate: true });
        } catch (error) {
          console.warn("Error centering zoomed out map:", error);
        }

        try {
          zoomedInMapRef.current.setView([lat, lng], ZOOMED_IN_LEVEL, { animate: true });
        } catch (error) {
          console.warn("Error centering zoomed in map:", error);
        }
      }

      updateInProgressRef.current = false;
      return true;
    } catch (error) {
      console.error("Error setting destination:", error);
      updateInProgressRef.current = false;
      return false;
    }
  };

  // NEW: Add team vehicle markers to maps - UPDATED VERSION
  const addTeamVehicleMarkers = useCallback(() => {
    if (!teamVehicles || teamVehicles.length === 0) {
      console.log("No team vehicles to display");
      clearTeamVehicleMarkers();
      return;
    }

    if (!zoomedOutMapRef.current || !zoomedInMapRef.current) {
      console.log("Maps not ready for team vehicle markers");
      return;
    }

    console.log(`Adding ${teamVehicles.length} team vehicle markers to maps`);
    console.log("Team vehicles data:", teamVehicles);

    // Clear any existing team vehicle markers first
    clearTeamVehicleMarkers();

    let markersAdded = 0;
    
    teamVehicles.forEach(vehicle => {
      // Validate this is actually a vehicle, not a user
      if (!vehicle.vin && !vehicle.vehicle) {
        console.warn("Skipping item - not a valid vehicle:", vehicle);
        return;
      }
      
      // Extract coordinates with multiple fallbacks
      let lat, lng;
      
      if (vehicle.position && typeof vehicle.position.lat === 'number' && typeof vehicle.position.lng === 'number') {
        lat = vehicle.position.lat;
        lng = vehicle.position.lng;
      } else if (typeof vehicle.lat === 'number' && typeof vehicle.lng === 'number') {
        lat = vehicle.lat;
        lng = vehicle.lng;
      } else {
        console.warn(`Team vehicle ${vehicle.vehicle || 'unknown'} has no valid coordinates`);
        return;
      }
      
      // Validate coordinates
      if (isNaN(lat) || isNaN(lng) || Math.abs(lat) > 90 || Math.abs(lng) > 180) {
        console.warn(`Invalid coordinates for vehicle ${vehicle.vehicle}:`, { lat, lng });
        return;
      }

      try {
        // Create a distinct vehicle icon
        const icon = createTeamVehicleIcon(vehicle, 40); // Larger size for vehicles

        // Create detailed vehicle popup (NOT user popup)
        const popupContent = `
          <div class="team-vehicle-popup" style="min-width: 300px; max-width: 400px;">
            <div style="background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%); color: white; padding: 12px; margin: -20px -20px 12px -20px; border-radius: 8px 8px 0 0;">
              <h3 style="margin: 0; font-size: 18px; font-weight: bold; display: flex; align-items: center;">
                <span style="font-size: 24px; margin-right: 8px;">🚗</span>
                TEAM VEHICLE
              </h3>
            </div>
            
            <div style="background: #F3F4F6; padding: 12px; border-radius: 8px; margin-bottom: 12px;">
              <h4 style="font-weight: bold; margin: 0 0 8px 0; font-size: 20px; color: #1F2937;">
                ${vehicle.vehicle || vehicle.name || "Unknown Vehicle"}
              </h4>
              
              <div style="display: grid; grid-template-columns: auto 1fr; gap: 8px; font-size: 14px; color: #4B5563;">
                <strong>VIN:</strong> <span style="font-family: monospace;">${vehicle.vin || 'N/A'}</span>
                <strong>Reported By:</strong> <span>${vehicle.teamMemberName || "Unknown"}</span>
                ${vehicle.plateNumber ? `<strong>Plate:</strong> <span>${vehicle.plateNumber}</span>` : ''}
                ${vehicle.financier ? `<strong>Financier:</strong> <span>${vehicle.financier}</span>` : ''}
                ${vehicle.accountNumber ? `<strong>Account:</strong> <span>${vehicle.accountNumber}</span>` : ''}
              </div>
            </div>
            
            ${vehicle.bottomStatus ? `
              <div style="background: #FEF2F2; border: 1px solid #FCA5A5; color: #DC2626; padding: 10px; border-radius: 6px; margin-bottom: 12px;">
                <strong>⚠️ Bottom Status Alert</strong><br/>
                ${vehicle.bottomStatus} (Strike ${vehicle.bottomStatusCount || 1} of 3)
              </div>
            ` : ''}
            
            ${vehicle.arrivedDriverId ? `
              <div style="background: #D1FAE5; border: 1px solid #6EE7B7; color: #059669; padding: 10px; border-radius: 6px; margin-bottom: 12px;">
                <strong>✅ Driver Status: ARRIVED</strong><br/>
                Driver: ${vehicle.arrivedDriverName || 'Unknown Driver'}
                ${vehicle.arrivedTimestamp ? `<br/>Time: ${new Date(vehicle.arrivedTimestamp.seconds * 1000).toLocaleTimeString()}` : ''}
              </div>
            ` : vehicle.inRouteDriverId ? `
              <div style="background: #DBEAFE; border: 1px solid #93C5FD; color: #2563EB; padding: 10px; border-radius: 6px; margin-bottom: 12px;">
                <strong>🚚 Driver Status: IN ROUTE</strong><br/>
                Driver: ${vehicle.inRouteDriverName || 'Unknown Driver'}
                ${vehicle.inRouteTimestamp ? `<br/>Time: ${new Date(vehicle.inRouteTimestamp.seconds * 1000).toLocaleTimeString()}` : ''}
              </div>
            ` : `
              <div style="background: #F3F4F6; border: 1px solid #D1D5DB; color: #6B7280; padding: 10px; border-radius: 6px; margin-bottom: 12px;">
                <strong>📍 Status: AWAITING PICKUP</strong><br/>
                No driver assigned yet
              </div>
            `}
            
            <div style="background: #F9FAFB; padding: 10px; border-radius: 6px; margin-bottom: 12px; border: 1px solid #E5E7EB;">
              <strong style="color: #374151;">📍 Current Location:</strong><br/>
              <span style="color: #6B7280; font-size: 13px;">
                ${vehicle.address || `GPS Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}`}
              </span>
            </div>
            
            ${vehicle.notes ? `
              <div style="background: #FFFBEB; padding: 10px; border-radius: 6px; margin-bottom: 12px; border: 1px solid #FCD34D;">
                <strong style="color: #92400E;">📝 Notes:</strong><br/>
                <span style="color: #B45309; font-size: 13px;">${vehicle.notes}</span>
              </div>
            ` : ''}
            
            <div style="display: flex; gap: 10px; margin-top: 16px;">
              <button id="team-vehicle-details-btn-${vehicle.id || vehicle.uniqueKey}" style="
                flex: 1;
                background: linear-gradient(135deg, #6366F1 0%, #4F46E5 100%);
                color: white;
                border: none;
                padding: 12px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 600;
                font-size: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                transition: all 0.2s;
              " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.15)';" 
                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)';">
                View Full Details
              </button>
              <button id="team-vehicle-navigate-btn-${vehicle.id || vehicle.uniqueKey}" style="
                flex: 1;
                background: linear-gradient(135deg, #10B981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 12px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 600;
                font-size: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                transition: all 0.2s;
              " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.15)';" 
                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)';">
                Navigate to Vehicle
              </button>
            </div>
          </div>
        `;

        // Create markers with very high z-index to ensure they appear on top
        const zoomedOutMarker = L.marker([lat, lng], {
          icon: icon,
          title: `🚗 VEHICLE: ${vehicle.vehicle || "Unknown"} - VIN: ${vehicle.vin}`,
          zIndexOffset: 2000, // Much higher than user markers
          riseOnHover: true,
          keyboard: false,
          interactive: true
        });

        const zoomedInMarker = L.marker([lat, lng], {
          icon: icon,
          title: `🚗 VEHICLE: ${vehicle.vehicle || "Unknown"} - VIN: ${vehicle.vin}`,
          zIndexOffset: 2000, // Much higher than user markers
          riseOnHover: true,
          keyboard: false,
          interactive: true
        });

        // Bind popups
        zoomedOutMarker.bindPopup(popupContent, { 
          maxWidth: 400,
          className: 'team-vehicle-popup-wrapper',
          offset: [0, -20]
        });
        
        zoomedInMarker.bindPopup(popupContent, { 
          maxWidth: 400,
          className: 'team-vehicle-popup-wrapper',
          offset: [0, -20]
        });

        // Add popup event handlers
        const setupPopupHandlers = (marker) => {
          marker.on('popupopen', () => {
            setTimeout(() => {
              // Details button
              const detailsBtn = document.getElementById(`team-vehicle-details-btn-${vehicle.id || vehicle.uniqueKey}`);
              if (detailsBtn) {
                detailsBtn.addEventListener('click', () => {
                  console.log("Opening details for vehicle:", vehicle);
                  if (onTeamVehicleClick) {
                    onTeamVehicleClick(vehicle);
                  }
                  marker.closePopup();
                });
              }

              // Navigate button
              const navigateBtn = document.getElementById(`team-vehicle-navigate-btn-${vehicle.id || vehicle.uniqueKey}`);
              if (navigateBtn) {
                navigateBtn.addEventListener('click', () => {
                  console.log("Navigating to vehicle:", vehicle);
                  if (onTeamVehicleNavigate) {
                    onTeamVehicleNavigate(vehicle);
                  } else {
                    setDestination(lat, lng);
                  }
                  marker.closePopup();
                });
              }
            }, 10);
          });
        };

        setupPopupHandlers(zoomedOutMarker);
        setupPopupHandlers(zoomedInMarker);

        // Add to maps - directly, not to clusters to ensure visibility
        try {
          zoomedOutMapRef.current.addLayer(zoomedOutMarker);
          zoomedInMapRef.current.addLayer(zoomedInMarker);
          
          // Store references
          const markerId = vehicle.uniqueKey || vehicle.id || `vehicle-${Date.now()}-${Math.random()}`;
          teamVehicleMarkersRef.current.zoomedOut.set(markerId, zoomedOutMarker);
          teamVehicleMarkersRef.current.zoomedIn.set(markerId, zoomedInMarker);
          
          markersAdded++;
          console.log(`Added vehicle marker: ${vehicle.vehicle} at ${lat}, ${lng}`);
        } catch (err) {
          console.error(`Failed to add vehicle marker for ${vehicle.vehicle}:`, err);
        }

      } catch (error) {
        console.error(`Error adding team vehicle marker ${vehicle.vehicle}:`, error);
      }
    });

    console.log(`✅ Successfully added ${markersAdded}/${teamVehicles.length} team vehicle markers`);
    
    // Force map refresh
    setTimeout(() => {
      if (zoomedOutMapRef.current) {
        zoomedOutMapRef.current.invalidateSize();
      }
      if (zoomedInMapRef.current) {
        zoomedInMapRef.current.invalidateSize();
      }
    }, 100);
    
    return markersAdded;
  }, [teamVehicles, onTeamVehicleClick, onTeamVehicleNavigate, createTeamVehicleIcon, setDestination]);

  // NEW: Clear team vehicle markers
  const clearTeamVehicleMarkers = useCallback(() => {
    console.log("Clearing team vehicle markers");

    // Clear zoomed out markers
    teamVehicleMarkersRef.current.zoomedOut.forEach((marker, id) => {
      try {
        if (zoomedOutMarkerClusterRef.current && zoomedOutMarkerClusterRef.current.hasLayer(marker)) {
          zoomedOutMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedOutMapRef.current && zoomedOutMapRef.current.hasLayer(marker)) {
          zoomedOutMapRef.current.removeLayer(marker);
        }
      } catch (error) {
        console.warn(`Error removing zoomed out team vehicle marker ${id}:`, error);
      }
    });

    // Clear zoomed in markers
    teamVehicleMarkersRef.current.zoomedIn.forEach((marker, id) => {
      try {
        if (zoomedInMarkerClusterRef.current && zoomedInMarkerClusterRef.current.hasLayer(marker)) {
          zoomedInMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedInMapRef.current && zoomedInMapRef.current.hasLayer(marker)) {
          zoomedInMapRef.current.removeLayer(marker);
        }
      } catch (error) {
        console.warn(`Error removing zoomed in team vehicle marker ${id}:`, error);
      }
    });

    // Clear the references
    teamVehicleMarkersRef.current.zoomedOut.clear();
    teamVehicleMarkersRef.current.zoomedIn.clear();
  }, []);

  // NEW: Effect to handle team vehicle changes
  useEffect(() => {
    if (!teamVehicles || teamVehicles.length === 0) {
      clearTeamVehicleMarkers();
      return;
    }

    if (!zoomedOutMapRef.current || !zoomedInMapRef.current) {
      console.log("Maps not ready for team vehicle markers");
      return;
    }

    console.log("Team vehicles changed, updating markers");
    addTeamVehicleMarkers();

    return () => {
      clearTeamVehicleMarkers();
    };
  }, [teamVehicles, addTeamVehicleMarkers, clearTeamVehicleMarkers]);

  // NEW: Add processed markers to maps
  const addProcessedMarkers = useCallback(() => {
    if (!processedMarkers || !zoomedOutMapRef.current || !zoomedInMapRef.current) {
      console.log("⏳ Cannot add markers - missing processed data or maps not ready");
      return;
    }

    console.log("🔄 Adding processed markers to maps", {
      orderMarkers: processedMarkers.orderMarkers?.length || 0,
      manualMarkers: processedMarkers.manualMarkers?.length || 0,
      teamMarkers: processedMarkers.teamMarkers?.length || 0
    });

    // Clear existing processed markers
    clearProcessedMarkers();

    // Process all marker categories
    const allMarkers = [
      ...(processedMarkers.orderMarkers || []),
      ...(processedMarkers.manualMarkers || []),
      ...(processedMarkers.teamMarkers || [])
    ];

    let addedCount = 0;

    allMarkers.forEach(markerData => {
      try {
        // Create markers for both maps
        const zoomedOutMarker = createMarkerFromProcessedData(markerData, false);
        const zoomedInMarker = createMarkerFromProcessedData(markerData, true);

        if (zoomedOutMarker && zoomedInMarker) {
          // Add to appropriate clusters or directly to maps
          let zoomedOutAdded = false;
          let zoomedInAdded = false;

          // Determine which cluster to use based on marker category
          let targetZoomedOutCluster, targetZoomedInCluster;

          if (markerData.category === 'user') {
            targetZoomedOutCluster = zoomedOutUserMarkerClusterRef.current;
            targetZoomedInCluster = zoomedInUserMarkerClusterRef.current;
          } else {
            targetZoomedOutCluster = zoomedOutMarkerClusterRef.current;
            targetZoomedInCluster = zoomedInMarkerClusterRef.current;
          }

          // Add to zoomed out map
          try {
            if (targetZoomedOutCluster) {
              targetZoomedOutCluster.addLayer(zoomedOutMarker);
              zoomedOutAdded = true;
            } else if (zoomedOutMapRef.current) {
              zoomedOutMapRef.current.addLayer(zoomedOutMarker);
              zoomedOutAdded = true;
            }
          } catch (e) {
            console.warn(`Error adding ${markerData.id} to zoomed out map:`, e);
            if (zoomedOutMapRef.current) {
              try {
                zoomedOutMapRef.current.addLayer(zoomedOutMarker);
                zoomedOutAdded = true;
              } catch (e2) {
                console.error(`Failed to add ${markerData.id} to zoomed out map:`, e2);
              }
            }
          }

          // Add to zoomed in map
          try {
            if (targetZoomedInCluster) {
              targetZoomedInCluster.addLayer(zoomedInMarker);
              zoomedInAdded = true;
            } else if (zoomedInMapRef.current) {
              zoomedInMapRef.current.addLayer(zoomedInMarker);
              zoomedInAdded = true;
            }
          } catch (e) {
            console.warn(`Error adding ${markerData.id} to zoomed in map:`, e);
            if (zoomedInMapRef.current) {
              try {
                zoomedInMapRef.current.addLayer(zoomedInMarker);
                zoomedInAdded = true;
              } catch (e2) {
                console.error(`Failed to add ${markerData.id} to zoomed in map:`, e2);
              }
            }
          }

          if (zoomedOutAdded && zoomedInAdded) {
            // Store markers for later cleanup
            processedMarkersRef.current.zoomedOut.set(markerData.id, zoomedOutMarker);
            processedMarkersRef.current.zoomedIn.set(markerData.id, zoomedInMarker);
            addedCount++;
          }
        }
      } catch (error) {
        console.error(`Error processing marker ${markerData.id}:`, error);
      }
    });

    console.log(`✅ Successfully added ${addedCount}/${allMarkers.length} processed markers to maps`);

    // Force map refresh
    setTimeout(() => {
      if (zoomedOutMapRef.current) {
        zoomedOutMapRef.current.invalidateSize();
      }
      if (zoomedInMapRef.current) {
        zoomedInMapRef.current.invalidateSize();
      }
    }, 100);
  }, [processedMarkers, createMarkerFromProcessedData]);

  // NEW: Clear processed markers
  const clearProcessedMarkers = useCallback(() => {
    console.log("🗑️ Clearing processed markers");

    // Clear zoomed out markers
    processedMarkersRef.current.zoomedOut.forEach((marker, id) => {
      try {
        if (zoomedOutMarkerClusterRef.current && zoomedOutMarkerClusterRef.current.hasLayer(marker)) {
          zoomedOutMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedOutUserMarkerClusterRef.current && zoomedOutUserMarkerClusterRef.current.hasLayer(marker)) {
          zoomedOutUserMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedOutMapRef.current && zoomedOutMapRef.current.hasLayer(marker)) {
          zoomedOutMapRef.current.removeLayer(marker);
        }
      } catch (error) {
        console.warn(`Error removing zoomed out marker ${id}:`, error);
      }
    });

    // Clear zoomed in markers
    processedMarkersRef.current.zoomedIn.forEach((marker, id) => {
      try {
        if (zoomedInMarkerClusterRef.current && zoomedInMarkerClusterRef.current.hasLayer(marker)) {
          zoomedInMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedInUserMarkerClusterRef.current && zoomedInUserMarkerClusterRef.current.hasLayer(marker)) {
          zoomedInUserMarkerClusterRef.current.removeLayer(marker);
        } else if (zoomedInMapRef.current && zoomedInMapRef.current.hasLayer(marker)) {
          zoomedInMapRef.current.removeLayer(marker);
        }
      } catch (error) {
        console.warn(`Error removing zoomed in marker ${id}:`, error);
      }
    });

    // Clear the references
    processedMarkersRef.current.zoomedOut.clear();
    processedMarkersRef.current.zoomedIn.clear();
  }, []);

  // NEW: Effect to handle processed markers changes
  useEffect(() => {
    if (!processedMarkers) {
      console.log("📭 No processed markers available");
      clearProcessedMarkers();
      return;
    }

    if (isProcessingMarkers) {
      console.log("⏳ Markers are still being processed...");
      return;
    }

    if (!zoomedOutMapRef.current || !zoomedInMapRef.current) {
      console.log("🗺️ Maps not ready yet for marker placement");
      return;
    }

    console.log("🔄 Processed markers changed, updating map display", processedMarkers.summary);
    addProcessedMarkers();

    // Cleanup on unmount or change
    return () => {
      clearProcessedMarkers();
    };
  }, [processedMarkers, isProcessingMarkers, addProcessedMarkers, clearProcessedMarkers]);

  // ENHANCED: Cancel navigation and clean up everything
  const cancelNavigation = () => {
    console.log("Canceling navigation...");
    
    try {
      destinationRef.current = null;
      
      if (destinationMarkerRef.current) {
        if (destinationMarkerRef.current.zoomed_out) {
          if (zoomedOutMarkerClusterRef.current) {
            safeRemoveFromCluster(zoomedOutMarkerClusterRef.current, destinationMarkerRef.current.zoomed_out);
          } else if (zoomedOutMapRef.current) {
            safeRemoveLayer(zoomedOutMapRef.current, destinationMarkerRef.current.zoomed_out);
          }
        }
        
        if (destinationMarkerRef.current.zoomed_in) {
          if (zoomedInMarkerClusterRef.current) {
            safeRemoveFromCluster(zoomedInMarkerClusterRef.current, destinationMarkerRef.current.zoomed_in);
          } else if (zoomedInMapRef.current) {
            safeRemoveLayer(zoomedInMapRef.current, destinationMarkerRef.current.zoomed_in);
          }
        }
        
        destinationMarkerRef.current = null;
      }
      
      const clearPromises = [];
      
      if (zoomedOutRoutingControlRef.current) {
        clearPromises.push(safeClearRouteLines(zoomedOutRoutingControlRef.current));
      }
      
      if (zoomedInRoutingControlRef.current) {
        clearPromises.push(safeClearRouteLines(zoomedInRoutingControlRef.current));
      }
      
      if (routingControlRef && routingControlRef.current) {
        clearPromises.push(safeClearRouteLines(routingControlRef.current));
      }
      
      Promise.all(clearPromises).then(() => {
        console.log("All routes cleared");
      });
      
      ROUTE_REQUEST_CACHE.clear();
      PENDING_REQUESTS.clear();
      
      setRouteInfo(null);
      setNextManeuver(null);
      setNavigationDirection('straight');
      setDistanceToDestination(0);
      setEstimatedTime(0);
      setArrivalTime(null);
      setDestinationAddress('');
      
      if (isRotationEnabled) {
        setMapRotation(0);
        setMapScale(1.5);
      }
      
      onNavigationStatusChange({
        isNavigating: false,
        nextInstruction: null,
        routeSummary: null
      });
      
      console.log("Navigation canceled successfully");
      return true;
      
    } catch (error) {
      console.error("Error canceling navigation:", error);
      return false;
    }
  };

  // CRITICAL FIX: Force recreate user marker
  const forceRecreateUserMarker = () => {
    console.log("🔄 Force recreating user marker...");
    
    if (currentLocation && currentLocation.lat && currentLocation.lng) {
      // Clear existing marker reference
      currentLocationMarkerRef.current = null;
      
      // Recreate marker
      createNewUserMarkers(currentLocation);
      
      console.log("✅ User marker force recreated");
      
      return true;
    } else {
      console.log("❌ No valid location for marker recreation");
      return false;
    }
  };

  // ENHANCED: Force create user marker with better error handling
  const forceCreateUserMarker = () => {
    if (currentLocation && currentLocation.lat && currentLocation.lng) {
      console.log("Force creating user marker...");
      
      if (currentLocationMarkerRef.current) {
        if (currentLocationMarkerRef.current.zoomed_out) {
          if (zoomedOutUserMarkerClusterRef.current) {
            safeRemoveFromCluster(zoomedOutUserMarkerClusterRef.current, currentLocationMarkerRef.current.zoomed_out);
          } else if (zoomedOutMapRef.current) {
            safeRemoveLayer(zoomedOutMapRef.current, currentLocationMarkerRef.current.zoomed_out);
          }
        }
        
        if (currentLocationMarkerRef.current.zoomed_in) {
          if (zoomedInUserMarkerClusterRef.current) {
            safeRemoveFromCluster(zoomedInUserMarkerClusterRef.current, currentLocationMarkerRef.current.zoomed_in);
          } else if (zoomedInMapRef.current) {
            safeRemoveLayer(zoomedInMapRef.current, currentLocationMarkerRef.current.zoomed_in);
          }
        }
        
        currentLocationMarkerRef.current = null;
      }
      
      createNewUserMarkers(currentLocation);
      
      if (zoomedOutMapRef.current) {
        zoomedOutMapRef.current.setView([currentLocation.lat, currentLocation.lng], ZOOMED_OUT_LEVEL);
      }
      if (zoomedInMapRef.current) {
        zoomedInMapRef.current.setView([currentLocation.lat, currentLocation.lng], ZOOMED_IN_LEVEL);
      }
    }
  };

  // CRITICAL FIX: Enhanced user location update with better error handling
  const updateUserLocationSmooth = (location) => {
    if (!location || !location.lat || !location.lng) return false;
    
    console.log("🔄 Smoothly updating user location:", location);

    try {
      // CRITICAL FIX: Always update map view first
      if (zoomedOutMapRef.current && !userHasDraggedMap.current) {
        zoomedOutMapRef.current.panTo([location.lat, location.lng], { 
          animate: true, 
          duration: 0.25,
          noMoveStart: true 
        });
      }
      if (zoomedInMapRef.current && !userHasDraggedMap.current) {
        zoomedInMapRef.current.panTo([location.lat, location.lng], { 
          animate: true, 
          duration: 0.25,
          noMoveStart: true 
        });
      }
    } catch (error) {
      console.warn("Error updating map views:", error);
    }

    // CRITICAL FIX: Enhanced marker update with better error handling
    if (currentLocationMarkerRef.current) {
      try {
        let updateSuccessful = true;
        
        // Update zoomed out marker
        if (currentLocationMarkerRef.current.zoomed_out) {
          try {
            currentLocationMarkerRef.current.zoomed_out.setLatLng([location.lat, location.lng]);
            
            if (location.heading !== undefined) {
              updateMarkerHeading(currentLocationMarkerRef.current.zoomed_out, location.heading);
            }
            
            console.log("✅ Updated zoomed out user marker");
          } catch (error) {
            console.warn("❌ Error updating zoomed out marker:", error);
            updateSuccessful = false;
          }
        } else {
          updateSuccessful = false;
        }
        
        // Update zoomed in marker
        if (currentLocationMarkerRef.current.zoomed_in) {
          try {
            currentLocationMarkerRef.current.zoomed_in.setLatLng([location.lat, location.lng]);
            
            if (location.heading !== undefined) {
              updateMarkerHeading(currentLocationMarkerRef.current.zoomed_in, location.heading);
            }
            
            console.log("✅ Updated zoomed in user marker");
          } catch (error) {
            console.warn("❌ Error updating zoomed in marker:", error);
            updateSuccessful = false;
          }
        } else {
          updateSuccessful = false;
        }
        
        // CRITICAL FIX: If update failed, recreate markers
        if (!updateSuccessful) {
          console.log("🔄 Marker update failed, recreating user markers");
          createNewUserMarkers(location);
        }
        
      } catch (error) {
        console.warn("❌ Error in marker update, recreating:", error);
        createNewUserMarkers(location);
      }
    } else {
      // CRITICAL FIX: No marker exists, create new ones
      console.log("🆕 No existing marker, creating new user markers");
      createNewUserMarkers(location);
    }

    return true;
  };

  // CRITICAL FIX: Enhanced create new user markers with better cleanup
  const createNewUserMarkers = (location) => {
    if (!zoomedOutMapRef.current || !zoomedInMapRef.current) {
      console.warn("❌ Maps not ready for marker creation");
      return;
    }

    console.log("🆕 Creating new user markers at:", location);

    // CRITICAL FIX: Clean up existing markers first
    if (currentLocationMarkerRef.current) {
      try {
        if (currentLocationMarkerRef.current.zoomed_out) {
          if (zoomedOutUserMarkerClusterRef.current) {
            safeRemoveFromCluster(zoomedOutUserMarkerClusterRef.current, currentLocationMarkerRef.current.zoomed_out);
          } else if (zoomedOutMapRef.current) {
            safeRemoveLayer(zoomedOutMapRef.current, currentLocationMarkerRef.current.zoomed_out);
          }
        }
        
        if (currentLocationMarkerRef.current.zoomed_in) {
          if (zoomedInUserMarkerClusterRef.current) {
            safeRemoveFromCluster(zoomedInUserMarkerClusterRef.current, currentLocationMarkerRef.current.zoomed_in);
          } else if (zoomedInMapRef.current) {
            safeRemoveLayer(zoomedInMapRef.current, currentLocationMarkerRef.current.zoomed_in);
          }
        }
        
        currentLocationMarkerRef.current = null;
        console.log("🗑️ Cleaned up existing user markers");
      } catch (error) {
        console.warn("⚠️ Error cleaning up existing markers:", error);
      }
    }

    let headingIndicator = '';
    if (location.heading !== undefined && location.heading !== null) {
      headingIndicator = `
        <div class="heading-indicator" style="transform: rotate(${location.heading}deg)">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
            <path fill="#FFFFFF" d="M12,2L4,13h16L12,2z"/>
          </svg>
        </div>
      `;
    }

    const userIcon = L.divIcon({
      className: 'current-location-marker',
      html: `
        <div class="current-marker-inner pulse"></div>
        ${headingIndicator}
      `,
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });

    // CRITICAL FIX: Create markers with unique IDs to prevent conflicts
    const userMarkerZoomedOut = L.marker([location.lat, location.lng], {
      icon: userIcon,
      zIndexOffset: 1000,
      title: "Your Location"
    });

    const userMarkerZoomedIn = L.marker([location.lat, location.lng], {
      icon: userIcon,
      zIndexOffset: 1000,
      title: "Your Location"
    });

    try {
      // CRITICAL FIX: Add directly to map first, then to cluster if needed
      zoomedOutMapRef.current.addLayer(userMarkerZoomedOut);
      zoomedInMapRef.current.addLayer(userMarkerZoomedIn);
      
      console.log("✅ Added user markers to maps directly");

      currentLocationMarkerRef.current = {
        zoomed_out: userMarkerZoomedOut,  
        zoomed_in: userMarkerZoomedIn
      };
      
      currentMarkerRef.current = userMarkerZoomedOut;
      
      console.log("✅ User markers created and stored successfully");
      
    } catch (error) {
      console.warn("❌ Error adding new user markers:", error);
    }
  };

  // Update marker heading function
  const updateMarkerHeading = (marker, heading) => {
    try {
      const element = marker.getElement();
      if (element) {
        const headingIndicator = element.querySelector('.heading-indicator');
        if (headingIndicator) {
          headingIndicator.style.transform = `rotate(${heading}deg)`;
        }
      }
    } catch (error) {
      console.warn('Error updating marker heading:', error);
    }
  };

  // ENHANCED: Start continuous location tracking with immediate updates
  const startContinuousTracking = () => {
    if (locationWatchIdRef.current) return;

    console.log("Starting enhanced continuous location tracking");
    
    if (navigator.geolocation) {
      try {
        const watchId = navigator.geolocation.watchPosition(
          (position) => {
            const { latitude, longitude, accuracy, heading, speed } = position.coords;
            
            const location = { 
              lat: latitude, 
              lng: longitude,
              accuracy: accuracy,
              heading: heading || 0,
              speed: speed || 0,
              timestamp: Date.now() // Use current timestamp
            };
            
            console.log("Live location update received:", location);
            
            // CRITICAL: Update UI immediately
            if (zoomedOutMapRef.current && zoomedInMapRef.current) {
              updateUserLocationSmooth(location);
            }
            
            // CRITICAL: Always update the parent component state
            if (lastKnownPositionRef.current) {
              lastKnownPositionRef.current = location;
            }
            
            // CRITICAL: Call parent update function
            if (typeof updateUserLocationInDatabase === 'function') {
              updateUserLocationInDatabase(location);
            }
            
            // ENHANCED: Force trigger any parent location update callbacks
            if (typeof onLocationUpdate === 'function') {
              onLocationUpdate(location);
            }
          },
          (error) => {
            console.error("Enhanced location tracking error:", error);
          },
          {
            enableHighAccuracy: true,
            timeout: 5000, // Shorter timeout for more frequent updates
            maximumAge: 0, // Always get fresh location, never use cached
            desiredAccuracy: 10, // Request 10 meter accuracy
          }
        );
        
        locationWatchIdRef.current = watchId;
        setLocationTrackingEnabled(true);
        
        return watchId;
      } catch (error) {
        console.error("Error setting up enhanced location tracking:", error);
        setLocationTrackingEnabled(false);
        return null;
      }
    }
  };
  
  // Stop continuous location tracking
  const stopContinuousTracking = () => {
    if (locationWatchIdRef.current !== null) {
      console.log("Stopping continuous location tracking");
      navigator.geolocation.clearWatch(locationWatchIdRef.current);
      locationWatchIdRef.current = null;
      setLocationTrackingEnabled(false);
    }
  };
  
  // Toggle location tracking
  const toggleLocationTracking = () => {
    if (locationTrackingEnabled) {
      stopContinuousTracking();
    } else {
      startContinuousTracking();
    }
  };

  // Process route information and update navigation HUD
  const processRouteInformation = (route) => {
    if (!route || !route.instructions || route.instructions.length === 0) {
      console.log("No valid route instructions received");
      return;
    }

    try {
      console.log("Processing route information:", route);

      const nextInstruction = route.instructions.find(instr => instr.distance > 0);

      if (nextInstruction) {
        console.log("Next navigation instruction:", nextInstruction);

        setNextManeuver(nextInstruction);

        const mapDirectionToSimple = (direction) => {
          if (!direction) return 'straight';

          direction = direction.toLowerCase();

          if (direction.includes('left') || direction.includes('west')) {
            return 'left';
          } else if (direction.includes('right') || direction.includes('east')) {
            return 'right';
          } else if (direction.includes('straight') || direction.includes('north')) {
            return 'straight';
          } else if (direction.includes('uturn') || direction.includes('south')) {
            return 'south';
          } else {
            return 'straight';
          }
        };

        const simpleDirection = mapDirectionToSimple(nextInstruction.type);
        setNavigationDirection(simpleDirection);

        if (route.summary && typeof route.summary.totalDistance === 'number') {
          const distanceInMiles = route.summary.totalDistance / 1609.34;
          setDistanceToDestination(distanceInMiles);

          const timeInSeconds = (distanceInMiles / 30) * 3600;
          setEstimatedTime(timeInSeconds);

          const now = new Date();
          const arrivalTimeMs = now.getTime() + (timeInSeconds * 1000);
          setArrivalTime(new Date(arrivalTimeMs));
        }

        if (route.name) {
          setDestinationAddress(route.name);
        }

        onNavigationStatusChange({
          isNavigating: true,
          nextInstruction: nextInstruction,
          routeSummary: route.summary
        });
      }
    } catch (error) {
      console.error("Error processing route information:", error);
    }
  };

  // ENHANCED: Safe utility functions for route management
  const safeClearRouteLines = (routingControl) => {
    if (!routingControl) return false;

    try {
      if (!routingControl._plan ||
        !routingControl._routes ||
        !Array.isArray(routingControl._routes) ||
        routingControl._routes.length === 0) {
        return false;
      }

      if (typeof routingControl._clearLines !== 'function') {
        return false;
      }

      const hasValidPaths = routingControl._routes.some(route => {
        try {
          return route &&
            route.line &&
            route.line._path &&
            route.line._path.parentNode;
        } catch (e) {
          return false;
        }
      });

      if (hasValidPaths) {
        return new Promise((resolve) => {
          const timeout = setTimeout(() => {
            console.warn("Route clearing timed out");
            resolve(false);
          }, 300);

          try {
            routingControl._routes.forEach(route => {
              try {
                if (route && route.line) {
                  if (typeof route.line.eachLayer === 'function') {
                    const layers = [];
                    route.line.eachLayer(layer => layers.push(layer));

                    layers.forEach(layer => {
                      try {
                        route.line.removeLayer(layer);
                      } catch (e) {
                        console.warn("Error removing layer from line:", e);
                      }
                    });
                  }

                  if (routingControl._container && routingControl._map) {
                    try {
                      if (route.line._leaflet_id && routingControl._map._layers &&
                        routingControl._map._layers[route.line._leaflet_id]) {
                        routingControl._map.removeLayer(route.line);
                      }
                    } catch (e) {
                      console.warn("Error removing line from map:", e);
                    }
                  }
                }
              } catch (e) {
                console.warn("Error in route cleanup:", e);
              }
            });

            routingControl._routes = [];

            clearTimeout(timeout);
            resolve(true);
          } catch (error) {
            console.warn("Error in route clearing:", error);
            clearTimeout(timeout);
            resolve(false);
          }
        });
      } else {
        routingControl._routes = [];
        return true;
      }
    } catch (error) {
      console.warn("Error in safeClearRouteLines:", error);
      return false;
    }
  };

  // Safe layer removal helper functions
  const safeRemoveLayer = (map, layer) => {
    if (!map || !layer) return;

    try {
      if (map.hasLayer(layer)) {
        map.removeLayer(layer);
      }
    } catch (error) {
      console.warn("Error safely removing layer:", error);
    }
  };

  const safeRemoveFromCluster = (cluster, layer) => {
    if (!cluster || !layer) return;

    try {
      if (cluster.hasLayer(layer)) {
        cluster.removeLayer(layer);
      }
    } catch (error) {
      console.warn("Error safely removing layer from cluster:", error);
    }
  };

  // ENHANCED: Set waypoints for routing with better caching and rate limiting
  const setRoutingWaypoints = async (start, end) => {
    if (!start || !end || (!start.lat && !start.lng) || (!end.lat && !end.lng)) {
      console.error("Invalid waypoints for routing:", { start, end });
      return false;
    }

    try {
      const roundCoord = coord => parseFloat(coord.toFixed(5));
      const cacheKey = `${roundCoord(start.lat)},${roundCoord(start.lng)};${roundCoord(end.lat)},${roundCoord(end.lng)}`;

      if (ROUTE_REQUEST_CACHE.has(cacheKey)) {
        console.log("Using cached route:", cacheKey);
        const cachedRoute = ROUTE_REQUEST_CACHE.get(cacheKey);

        if (cachedRoute && cachedRoute.waypoints) {
          setTimeout(() => {
            updateRouteDisplayFromCache(cachedRoute);
          }, 10);
          return true;
        }
      }

      if (PENDING_REQUESTS.has(cacheKey)) {
        console.log("Request already pending:", cacheKey);
        return true;
      }

      const waypoints = [
        L.latLng(start.lat, start.lng),
        L.latLng(end.lat, end.lng)
      ];

      const now = Date.now();
      const timeSinceLastRequest = now - LAST_REQUEST_TIME.time;

      if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
        console.log(`Rate limiting in effect. Delaying route request for ${MIN_REQUEST_INTERVAL - timeSinceLastRequest}ms`);

        PENDING_REQUESTS.add(cacheKey);

        setTimeout(() => {
          PENDING_REQUESTS.delete(cacheKey);
          setRoutingWaypoints(start, end);
        }, MIN_REQUEST_INTERVAL - timeSinceLastRequest + Math.random() * 500);

        return true;
      }

      if (zoomedOutRoutingControlRef.current) {
        LAST_REQUEST_TIME.time = now;
        PENDING_REQUESTS.add(cacheKey);

        const waypointsCopy = waypoints.map(wp => L.latLng(wp.lat, wp.lng));

        await safeClearRouteLines(zoomedOutRoutingControlRef.current);

        const handleRouteFound = (e) => {
          try {
            if (e.routes && e.routes.length > 0) {
              const routeData = JSON.parse(JSON.stringify(e.routes[0]));

              ROUTE_REQUEST_CACHE.set(cacheKey, {
                waypoints: waypointsCopy,
                route: routeData,
                timestamp: Date.now()
              });

              if (ROUTE_REQUEST_CACHE.size > MAX_CACHE_SIZE) {
                const oldestKey = [...ROUTE_REQUEST_CACHE.entries()]
                  .sort((a, b) => a[1].timestamp - b[1].timestamp)[0][0];
                ROUTE_REQUEST_CACHE.delete(oldestKey);
              }

              updateAllMapsWithRouteData(waypointsCopy, routeData);
              PENDING_REQUESTS.delete(cacheKey);
              zoomedOutRoutingControlRef.current.off('routesfound', handleRouteFound);
            }
          } catch (error) {
            console.warn("Error handling route found event:", error);
            PENDING_REQUESTS.delete(cacheKey);
          }
        };

        const handleRouteError = (e) => {
          console.warn("Routing error:", e);
          PENDING_REQUESTS.delete(cacheKey);

          zoomedOutRoutingControlRef.current.off('routingerror', handleRouteError);
          zoomedOutRoutingControlRef.current.off('routesfound', handleRouteFound);

          if (e.error && e.error.status === 429) {
            MIN_REQUEST_INTERVAL = Math.min(MIN_REQUEST_INTERVAL * 2, 10000);

            console.warn(`Rate limit exceeded. Increasing delay to ${MIN_REQUEST_INTERVAL}ms`);

            setTimeout(() => {
              MIN_REQUEST_INTERVAL = 1000;
            }, 30000);
          }
        };

        zoomedOutRoutingControlRef.current.once('routesfound', handleRouteFound);
        zoomedOutRoutingControlRef.current.once('routingerror', handleRouteError);

        setTimeout(() => {
          try {
            zoomedOutRoutingControlRef.current.setWaypoints(waypointsCopy);
          } catch (error) {
            console.warn("Error setting primary waypoints:", error);
            PENDING_REQUESTS.delete(cacheKey);
            zoomedOutRoutingControlRef.current.off('routesfound', handleRouteFound);
            zoomedOutRoutingControlRef.current.off('routingerror', handleRouteError);
          }
        }, 50);

        return true;
      }

      return false;
    } catch (error) {
      console.error("Error in rate-limited setRoutingWaypoints:", error);
      return false;
    }
  };

  // Helper functions for route management
  const updateRouteDisplayFromCache = (cachedData) => {
    if (!cachedData || !cachedData.route) return;

    try {
      processRouteInformation(cachedData.route);

      Promise.resolve().then(() => {
        return updateAllMapsWithRouteData(cachedData.waypoints, cachedData.route);
      }).catch(error => {
        console.warn("Error processing cached route:", error);
      });
    } catch (error) {
      console.warn("Error updating from cached route:", error);
    }
  };

  const updateAllMapsWithRouteData = async (waypoints, routeData) => {
    try {
      const updatePromises = [];

      if (zoomedInRoutingControlRef.current) {
        updatePromises.push(
          updateRouteDisplayManually(zoomedInRoutingControlRef.current, waypoints, routeData)
        );
      }

      if (routingControlRef && routingControlRef.current &&
        routingControlRef.current !== zoomedOutRoutingControlRef.current) {
        updatePromises.push(
          updateRouteDisplayManually(routingControlRef.current, waypoints, routeData)
        );
      }

      await Promise.all(updatePromises);
      return true;
    } catch (error) {
      console.warn("Error updating multiple maps with route data:", error);
      return false;
    }
  };

const updateRouteDisplayManually = async (routingControl, waypoints, routeData) => {
    if (!routingControl) return false;

    try {
      await safeClearRouteLines(routingControl);

      if (!routingControl._container) {
        console.warn("Routing control has no container, skipping manual update");
        return false;
      }

      const freshRouteData = {
        ...routeData,
        name: routeData.name,
        coordinates: [...(routeData.coordinates || [])],
        instructions: [...(routeData.instructions || [])],
        summary: { ...routeData.summary },
        inputWaypoints: waypoints.map(wp => ({ ...wp })),
        waypoints: waypoints.map(wp => ({ ...wp }))
      };

      try {
        routingControl._routes = [freshRouteData];

        if (typeof routingControl._updateLineCallback === 'function') {
          try {
            routingControl._updateLineCallback(freshRouteData);
          } catch (renderError) {
            console.warn("Error in manual route rendering callback:", renderError);
          }
        }
      } catch (controlError) {
        console.warn("Error updating routing control state:", controlError);
        return false;
      }

      return true;
    } catch (error) {
      console.warn("Error manually updating route display:", error);
      return false;
    }
  };

  // Create routing control with better error handling
  const createRoutingControl = (map, options = {}) => {
    if (!map) return null;

    try {
      let existingControl = null;
      map.eachLayer((layer) => {
        if (layer && layer._plan && layer._routes) {
          existingControl = layer;
        }
      });

      if (existingControl) {
        safelyDisposeRoutingControl(existingControl);
      }

      const routingOptions = {
        ...DEFAULT_ROUTING_OPTIONS,
        ...options
      };

      const routingControl = L.Routing.control(routingOptions);

      routingControl.on('routingerror', (e) => {
        console.warn('Routing error encountered:', e.error);

        if (e.error && e.error.status === 429) {
          console.warn('Rate limit exceeded (429), implementing exponential backoff');
          MIN_REQUEST_INTERVAL = Math.min(MIN_REQUEST_INTERVAL * 2, 10000);

          setTimeout(() => {
            MIN_REQUEST_INTERVAL = 1000;
          }, 30000);
        }

        if (routingControl._routes) {
          try {
            safeClearRouteLines(routingControl);
          } catch (clearError) {
            console.warn('Error clearing routes after routing error:', clearError);
          }
        }
      });

      routingControl.on('routesfound', (e) => {
        const routes = e.routes;
        if (routes && routes.length > 0) {
          const primaryRoute = routes[0];
          setRouteInfo(primaryRoute);
          processRouteInformation(primaryRoute);
        }
      });

      setTimeout(() => {
        try {
          routingControl.addTo(map);
        } catch (error) {
          console.warn("Error adding routing control to map:", error);
        }
      }, 500);

      return routingControl;
    } catch (error) {
      console.error("Error creating routing control:", error);
      return {
        setWaypoints: () => { },
        on: () => { },
        off: () => { },
        once: () => { },
        removeFrom: () => { },
        remove: () => { },
        _routes: [],
        _clearLines: () => { }
      };
    }
  };

  const safelyDisposeRoutingControl = (routingControl) => {
    if (!routingControl) return Promise.resolve();

    return new Promise((resolve) => {
      setTimeout(async () => {
        try {
          await safeClearRouteLines(routingControl);

          try {
            if (routingControl._map) {
              routingControl.removeFrom(routingControl._map);
            }
          } catch (e) {
            console.warn("Error removing routing control from map:", e);
          }

          try {
            routingControl._map = null;
            routingControl._container = null;
            routingControl._routes = [];
          } catch (e) {
            console.warn("Error clearing routing control references:", e);
          }

          resolve();
        } catch (e) {
          console.warn("Error disposing routing control:", e);
          resolve();
        }
      }, 100);
    });
  };

// Calculate scale factor and bearing for map rotation
const calculateScaleFactor = (angle) => {
  const rad = angle * Math.PI / 180;
  const scale = Math.max(
    Math.abs(Math.cos(rad)) + Math.abs(Math.sin(rad)),
    Math.abs(Math.sin(rad)) + Math.abs(Math.cos(rad))
  );
  return Math.max(scale * 1.2, 1.5);
};

const calculateBearing = (startLat, startLng, endLat, endLng) => {
  const startLatRad = startLat * Math.PI / 180;
  const startLngRad = startLng * Math.PI / 180;
  const endLatRad = endLat * Math.PI / 180;
  const endLngRad = endLng * Math.PI / 180;

  const y = Math.sin(endLngRad - startLngRad) * Math.cos(endLatRad);
  const x = Math.cos(startLatRad) * Math.sin(endLatRad) -
    Math.sin(startLatRad) * Math.cos(endLatRad) * Math.cos(endLngRad - startLngRad);

  let bearing = Math.atan2(y, x) * 180 / Math.PI;
  bearing = (bearing + 360) % 360;

  return bearing;
};

const updateMapRotation = () => {
  if (!currentLocation || !destinationRef.current || !isRotationEnabled) return;

  const { lat: startLat, lng: startLng } = currentLocation;
  const { lat: endLat, lng: endLng } = destinationRef.current;

  const bearing = calculateBearing(startLat, startLng, endLat, endLng);
  const scaleFactor = calculateScaleFactor(bearing);

  setMapRotation(bearing);
  setMapScale(scaleFactor);

  console.log("MapDisplay: Updated map rotation, bearing:", bearing, "scale:", scaleFactor);

  setTimeout(() => {
    if (zoomedInMapRef.current) {
      zoomedInMapRef.current.invalidateSize();
    }
  }, 300);
};

// Create fallback maps if initialization fails
const createFallbackMaps = () => {
  try {
    console.log("MapDisplay: Creating fallback maps");

    if (zoomedOutContainerRef.current && !zoomedOutMapRef.current) {
      ensureContainerDimensions(zoomedOutContainerRef.current, 'zoomedOut');

      const zoomedOutMap = L.map(zoomedOutContainerRef.current, {
        zoomControl: false,
        dragging: true,
        touchZoom: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        boxZoom: true,
        keyboard: true,
        preferCanvas: false
      }).setView([
        currentLocation?.lat || 37.7749,
        currentLocation?.lng || -122.4194
      ], ZOOMED_OUT_LEVEL);

      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
      }).addTo(zoomedOutMap);

      zoomedOutMapRef.current = zoomedOutMap;
      zoomedOutMarkerClusterRef.current = L.markerClusterGroup({
        animate: false,
        animateAddingMarkers: false
      }).addTo(zoomedOutMap);

      zoomedOutUserMarkerClusterRef.current = L.markerClusterGroup({
        animate: false,
        animateAddingMarkers: false
      }).addTo(zoomedOutMap);

      setTimeout(() => {
        zoomedOutRoutingControlRef.current = createRoutingControl(zoomedOutMap);
      }, 1000);
    }

    if (zoomedInContainerRef.current && !zoomedInMapRef.current) {
      ensureContainerDimensions(zoomedInContainerRef.current, 'zoomedIn');

      const zoomedInMap = L.map(zoomedInContainerRef.current, {
        zoomControl: false,
        dragging: true,
        touchZoom: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        boxZoom: true,
        keyboard: true,
        preferCanvas: false
      }).setView([
        currentLocation?.lat || 37.7749,
        currentLocation?.lng || -122.4194
      ], ZOOMED_IN_LEVEL);

      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
      }).addTo(zoomedInMap);

      zoomedInMapRef.current = zoomedInMap;
      zoomedInMarkerClusterRef.current = L.markerClusterGroup({
        animate: false,
        animateAddingMarkers: false
      }).addTo(zoomedInMap);

      zoomedInUserMarkerClusterRef.current = L.markerClusterGroup({
        animate: false,
        animateAddingMarkers: false
      }).addTo(zoomedInMap);

      setTimeout(() => {
        zoomedInRoutingControlRef.current = createRoutingControl(zoomedInMap);
      }, 1500);
    }

    if (typeof window !== 'undefined') {
      window.zoomedOutMapInstance = zoomedOutMapRef.current;
      window.zoomedInMapInstance = zoomedInMapRef.current;
    }

    zoomedOutBreadcrumbPathsRef.current = {};
    zoomedInBreadcrumbPathsRef.current = {};

    mapRef.current = zoomedOutMapRef.current;

    if (zoomedOutMapRef.current && zoomedInMapRef.current) {
      zoomedOutMapRef.current.on('click', (e) => {
        if (typeof handleMapClick === 'function') {
          handleMapClick(e);
        }

        const newCenter = e.latlng;
        zoomedOutMapRef.current.setView(newCenter, ZOOMED_OUT_LEVEL, { animate: true });
        zoomedInMapRef.current.setView(newCenter, ZOOMED_IN_LEVEL, { animate: true });

        if (isRotationEnabled) {
          setDestination(newCenter.lat, newCenter.lng);
        }
      });
    }
    setTimeout(() => {
      if (zoomedOutMapRef.current) {
        zoomedOutMapRef.current.invalidateSize({
          animate: false,
          pan: false
        });
      }
      if (zoomedInMapRef.current) {
        zoomedInMapRef.current.invalidateSize({
          animate: false,
          pan: false
        });
      }
    }, 100);

    // ENHANCED: Immediately show the user's location if available
    if (currentLocation && currentLocation.lat && currentLocation.lng) {
      console.log("Showing initial user location from init:", currentLocation);
      setTimeout(() => {
        updateUserLocationSmooth(currentLocation);
      }, 300);
    }

    // Start continuous location tracking if not already started
    if (!locationWatchIdRef.current && !initialLocationRequestedRef.current) {
      initialLocationRequestedRef.current = true;
      startContinuousTracking();
    }

    // Load ZIP code boundaries after maps are created
    setTimeout(() => {
      if (showZipCodes) {
        console.log("Loading ZIP code boundaries for all supported states");
        ZipCodeBoundaryManager.loadGeoJson({
          zoomedOutMapRef,
          zoomedInMapRef
        });
      }
    }, 1000);
  } catch (err) {
    console.error("Error creating fallback maps:", err);
  }
};

// Calculate appropriate container dimensions based on screen size
const calculateContainerDimensions = (containerType) => {
  if (!screenConfig) return { minHeight: "250px" };

  const { width, height, isSmallScreen, isMediumScreen } = screenConfig;
  const isLandscape = width > height;
  const isPad = width >= 768 && width <= 1024;

  const baseStyles = {
    minHeight: "250px",
    width: "100%",
    flex: "1",
    display: "flex",
    flexDirection: "column"
  };

  if (!isSmallScreen && !isMediumScreen) {
    return {
      ...baseStyles,
      height: "50%",
      minHeight: "300px",
    };
  }

  if (isPad && isLandscape) {
    return {
      ...baseStyles,
      height: "50%",
      minHeight: "250px"
    };
  }

  if (isPad && !isLandscape) {
    return {
      ...baseStyles,
      height: "50%",
      minHeight: "250px"
    };
  }

  if (isSmallScreen) {
    if (isLandscape) {
      return {
        ...baseStyles,
        height: "50%",
        minHeight: "150px"
      };
    } else {
      return {
        ...baseStyles,
        height: "50%",
        minHeight: "175px"
      };
    }
  }

  return {
    ...baseStyles,
    height: "50%",
    minHeight: "250px"
  };
};

// Get container layout - always column with detailed on top
const getContainerLayout = () => {
  return {
    flexDirection: "column",
    gap: "16px"
  };
};

// Ensure container has proper dimensions
const ensureContainerDimensions = (container, type) => {
  if (!container) return;

  const dimensions = calculateContainerDimensions(type);

  Object.entries(dimensions).forEach(([key, value]) => {
    container.style[key] = value;
  });

  console.log(`Map container dimensions (${type}):`, {
    offsetWidth: container.offsetWidth,
    offsetHeight: container.offsetHeight,
    appliedStyles: dimensions
  });
};

// Toggle showing ZIP code boundaries
const toggleZipBoundaries = () => {
  setShowZipCodes(prevState => {
    const newState = !prevState;

    if (newState) {
      if (ZipCodeBoundaryManager.isLoaded) {
        ZipCodeBoundaryManager.displayBoundaries(
          zoomedOutMapRef.current,
          zoomedInMapRef.current
        );
      } else {
        console.log("Loading ZIP code boundaries...");
        ZipCodeBoundaryManager.stateFeatureCollections = {
          'il': null,
          'in': null,
          'wi': null
        };
        
        ZipCodeBoundaryManager.loadGeoJson({
          zoomedOutMapRef,
          zoomedInMapRef
        }).then(success => {
          if (success) {
            console.log("Successfully loaded all ZIP code boundaries");
            ZipCodeBoundaryManager.displayBoundaries(
              zoomedOutMapRef.current,
              zoomedInMapRef.current
            );
          } else {
            console.error("Failed to load ZIP code boundaries");
          }
        });
      }
    } else {
      ZipCodeBoundaryManager.clearBoundaries(
        zoomedOutMapRef.current,
        zoomedInMapRef.current
      );
    }

    return newState;
  });
};

// Initialize maps when containers are available - CONTINUED
useEffect(() => {
  if (initAttemptedRef.current || !zoomedOutContainerRef.current || !zoomedInContainerRef.current) {
    return;
  }

  if (zoomedOutMapRef.current && zoomedInMapRef.current) {
    console.log("MapDisplay: Maps already exist, turning off loading");
    setInternalLoading(false);
    if (typeof setIsLoading === 'function') {
      setIsLoading(false);
    }
    return;
  }

  console.log("MapDisplay: Starting initialization", {
    hasZoomedOutContainer: !!zoomedOutContainerRef.current,
    hasZoomedInContainer: !!zoomedInContainerRef.current,
    zoomedOutHeight: zoomedOutContainerRef.current?.offsetHeight,
    zoomedOutWidth: zoomedOutContainerRef.current?.offsetWidth,
    zoomedInHeight: zoomedInContainerRef.current?.offsetHeight,
    zoomedInWidth: zoomedInContainerRef.current?.offsetWidth
  });

  initAttemptedRef.current = true;

  try {
    console.log("MapDisplay: Creating map instances");

    ensureContainerDimensions(zoomedOutContainerRef.current, 'zoomedOut');
    ensureContainerDimensions(zoomedInContainerRef.current, 'zoomedIn');

    const zoomedOutMap = L.map(zoomedOutContainerRef.current, {
      zoomControl: false,
      dragging: true,
      touchZoom: true,
      scrollWheelZoom: true,
      doubleClickZoom: true,
      boxZoom: true,
      keyboard: true,
      attributionControl: true,
      preferCanvas: false,
      zIndex: 10
    }).setView([
      currentLocation?.lat || 37.7749,
      currentLocation?.lng || -122.4194
    ], ZOOMED_OUT_LEVEL);

    const zoomedInMap = L.map(zoomedInContainerRef.current, {
      zoomControl: false,
      dragging: true,
      touchZoom: true,
      scrollWheelZoom: true,
      doubleClickZoom: true,
      boxZoom: true,
      keyboard: true,
      attributionControl: true,
      preferCanvas: false,
      zIndex: 10
    }).setView([
      currentLocation?.lat || 37.7749,
      currentLocation?.lng || -122.4194
    ], ZOOMED_IN_LEVEL);

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: 19
    }).addTo(zoomedOutMap);

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: 19
    }).addTo(zoomedInMap);

    zoomedOutMapRef.current = zoomedOutMap;
    zoomedInMapRef.current = zoomedInMap;

    if (typeof window !== 'undefined') {
      window.zoomedOutMapInstance = zoomedOutMap;
      window.zoomedInMapInstance = zoomedInMap;
    }

    mapRef.current = zoomedOutMap;

    console.log("MapDisplay: Maps created successfully!");

    zoomedOutMarkerClusterRef.current = L.markerClusterGroup({
      animate: false,
      animateAddingMarkers: false
    }).addTo(zoomedOutMap);

    zoomedInMarkerClusterRef.current = L.markerClusterGroup({
      animate: false,
      animateAddingMarkers: false
    }).addTo(zoomedInMap);

    zoomedOutUserMarkerClusterRef.current = L.markerClusterGroup({
      animate: false,
      animateAddingMarkers: false
    }).addTo(zoomedOutMap);

    zoomedInUserMarkerClusterRef.current = L.markerClusterGroup({
      animate: false,
      animateAddingMarkers: false
    }).addTo(zoomedInMap);

    markerClusterRef.current = zoomedOutMarkerClusterRef.current;
    userMarkerClusterRef.current = zoomedOutUserMarkerClusterRef.current;

    zoomedOutBreadcrumbPathsRef.current = {};
    zoomedInBreadcrumbPathsRef.current = {};
    breadcrumbPathsRef.current = zoomedOutBreadcrumbPathsRef.current;

    zoomedOutMap.on('dragstart', () => {
      userHasDraggedMap.current = true;
    });

    zoomedInMap.on('dragstart', () => {
      userHasDraggedMap.current = true;
    });

    zoomedOutMap.on('dragend', () => {
      setTimeout(() => {
        userHasDraggedMap.current = false;
      }, 5000);
    });

    zoomedInMap.on('dragend', () => {
      setTimeout(() => {
        userHasDraggedMap.current = false;
      }, 5000);
    });

    // ENHANCED: Add routing controls with increased delay
    setTimeout(() => {
      console.log("Creating zoomed out routing control with delay");
      try {
        zoomedOutRoutingControlRef.current = createRoutingControl(zoomedOutMap);
      } catch (error) {
        console.warn("Failed to create zoomed out routing control:", error);
      }

      setTimeout(() => {
        console.log("Creating zoomed in routing control with delay");
        try {
          zoomedInRoutingControlRef.current = createRoutingControl(zoomedInMap);
        } catch (error) {
          console.warn("Failed to create zoomed in routing control:", error);
        }

        if (routingControlRef) {
          setTimeout(() => {
            try {
              routingControlRef.current = zoomedOutRoutingControlRef.current;
            } catch (error) {
              console.warn("Failed to update parent routing control:", error);
            }
          }, 150);
        }
      }, 250);
    }, 500);

    if (typeof handleMapClick === 'function') {
      zoomedOutMap.on('click', (e) => {
        handleMapClick(e);

        const newCenter = e.latlng;
        zoomedOutMap.setView(newCenter, ZOOMED_OUT_LEVEL, { animate: true });
        zoomedInMap.setView(newCenter, ZOOMED_IN_LEVEL, { animate: true });

        if (isRotationEnabled) {
          setDestination(newCenter.lat, newCenter.lng);
        }
      });

      zoomedInMap.on('click', (e) => {
        handleMapClick(e);

        const newCenter = e.latlng;
        zoomedOutMap.setView(newCenter, ZOOMED_OUT_LEVEL, { animate: true });
        zoomedInMap.setView(newCenter, ZOOMED_IN_LEVEL, { animate: true });

        if (isRotationEnabled) {
          setDestination(newCenter.lat, newCenter.lng);
        }
      });
    }

    // Add context menu for zoomed out map
    zoomedOutMap.on('contextmenu', (e) => {
      const contextMenu = L.popup({
        closeButton: false,
        className: 'context-menu-popup',
        offset: [0, 0]
      })
        .setLatLng(e.latlng)
        .setContent(`
        <div class="context-menu">
          <button class="context-menu-item" id="center-map-btn">Center maps here</button>
          <button class="context-menu-item" id="set-location-btn">Set my location here</button>
          <button class="context-menu-item" id="add-marker-btn">Add marker here</button>
          <button class="context-menu-item" id="navigate-here-btn">Navigate to this point</button>
        </div>
      `)
        .openOn(zoomedOutMap);

      setTimeout(() => {
        document.getElementById('center-map-btn')?.addEventListener('click', () => {
          zoomedOutMap.setView(e.latlng, ZOOMED_OUT_LEVEL, { animate: true });
          zoomedInMap.setView(e.latlng, ZOOMED_IN_LEVEL, { animate: true });
          zoomedOutMap.closePopup();
        });

        document.getElementById('set-location-btn')?.addEventListener('click', () => {
          const newLocation = {
            lat: e.latlng.lat,
            lng: e.latlng.lng,
            timestamp: Date.now()
          };
          
          updateUserLocationSmooth(newLocation);
          
          // ENHANCED: Update parent component state immediately
          if (lastKnownPositionRef) {
            lastKnownPositionRef.current = newLocation;
          }
          
          // ENHANCED: Update database and trigger callbacks
          if (typeof updateUserLocationInDatabase === 'function') {
            updateUserLocationInDatabase(newLocation);
          }
          
          if (typeof onLocationUpdate === 'function') {
            onLocationUpdate(newLocation);
          }
          
          zoomedOutMap.closePopup();
        });

        document.getElementById('add-marker-btn')?.addEventListener('click', () => {
          if (isAddingMarker || isAddingAdminMarker) {
            const newPosition = {
              lat: e.latlng.lat,
              lng: e.latlng.lng
            };
            if (typeof setNewMarkerPosition === 'function') {
              setNewMarkerPosition(newPosition);
            }
          }
          zoomedOutMap.closePopup();
        });

        document.getElementById('navigate-here-btn')?.addEventListener('click', () => {
          setDestination(e.latlng.lat, e.latlng.lng);
          zoomedOutMap.closePopup();
        });
      }, 10);
    });

    // Add context menu for zoomed in map
    zoomedInMap.on('contextmenu', (e) => {
      const contextMenu = L.popup({
        closeButton: false,
        className: 'context-menu-popup',
        offset: [0, 0]
      })
        .setLatLng(e.latlng)
        .setContent(`
        <div class="context-menu">
          <button class="context-menu-item" id="center-maps-btn">Center maps here</button>
          <button class="context-menu-item" id="set-location-btn">Set my location here</button>
          <button class="context-menu-item" id="add-marker-btn">Add marker here</button>
          <button class="context-menu-item" id="navigate-here-btn">Navigate to this point</button>
        </div>
      `)
        .openOn(zoomedInMap);

      setTimeout(() => {
        document.getElementById('center-maps-btn')?.addEventListener('click', () => {
          zoomedOutMap.setView(e.latlng, ZOOMED_OUT_LEVEL, { animate: true });
          zoomedInMap.setView(e.latlng, ZOOMED_IN_LEVEL, { animate: true });
          zoomedInMap.closePopup();
        });

        document.getElementById('set-location-btn')?.addEventListener('click', () => {
          const newLocation = {
            lat: e.latlng.lat,
            lng: e.latlng.lng,
            timestamp: Date.now()
          };
          
          updateUserLocationSmooth(newLocation);
          
          // ENHANCED: Update parent component state immediately
          if (lastKnownPositionRef) {
            lastKnownPositionRef.current = newLocation;
          }
          
          // ENHANCED: Update database and trigger callbacks
          if (typeof updateUserLocationInDatabase === 'function') {
            updateUserLocationInDatabase(newLocation);
          }
          
          if (typeof onLocationUpdate === 'function') {
            onLocationUpdate(newLocation);
          }
          
          zoomedInMap.closePopup();
        });

        document.getElementById('add-marker-btn')?.addEventListener('click', () => {
          if (isAddingMarker || isAddingAdminMarker) {
            const newPosition = {
              lat: e.latlng.lat,
              lng: e.latlng.lng
            };
            if (typeof setNewMarkerPosition === 'function') {
              setNewMarkerPosition(newPosition);
            }
          }
          zoomedInMap.closePopup();
        });

        document.getElementById('navigate-here-btn')?.addEventListener('click', () => {
          setDestination(e.latlng.lat, e.latlng.lng);
          zoomedInMap.closePopup();
        });
      }, 10);
    });

    setTimeout(() => {
      if (zoomedOutMapRef.current) {
        zoomedOutMapRef.current.invalidateSize({
          animate: false,
          pan: false
        });
      }
      if (zoomedInMapRef.current) {
        zoomedInMapRef.current.invalidateSize({
          animate: false,
          pan: false
        });
      }
    }, 200);

    // ENHANCED: Immediately show the user's location if available
    if (currentLocation && currentLocation.lat && currentLocation.lng) {
      console.log("Showing initial user location from init:", currentLocation);
      setTimeout(() => {
        updateUserLocationSmooth(currentLocation);
      }, 300);
    }

    // ENHANCED: Start continuous location tracking with immediate updates
    if (!locationWatchIdRef.current && !initialLocationRequestedRef.current) {
      initialLocationRequestedRef.current = true;
      console.log("Starting enhanced continuous location tracking from map initialization...");
      startContinuousTracking();
    }

    // Load ZIP code boundaries for all states if enabled
    if (showZipCodes) {
      setTimeout(() => {
        console.log("Loading ZIP code boundaries for all supported states");
        
        ZipCodeBoundaryManager.stateFeatureCollections = {
          'il': null,
          'in': null,
          'wi': null
        };
        
        ZipCodeBoundaryManager.loadGeoJson({
          zoomedOutMapRef,
          zoomedInMapRef
        }).then(success => {
          if (success) {
            console.log("Successfully loaded all ZIP code boundaries");
            
            if (zoomedOutMapRef.current && zoomedInMapRef.current) {
              ZipCodeBoundaryManager.displayBoundaries(
                zoomedOutMapRef.current,
                zoomedInMapRef.current
              );
            }
          } else {
            console.error("Failed to load ZIP code boundaries");
          }
        });
      }, 1000);
    }

    setInternalLoading(false);
    if (typeof setIsLoading === 'function') {
      setIsLoading(false);
    }

    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    return () => {
      if (locationWatchIdRef.current !== null) {
        console.log("Cleaning up location tracking...");
        navigator.geolocation.clearWatch(locationWatchIdRef.current);
        locationWatchIdRef.current = null;
      }
      
      if (zoomedOutMap) {
        try {
          if (zoomedOutRoutingControlRef.current) {
            try {
              safeClearRouteLines(zoomedOutRoutingControlRef.current);
              zoomedOutRoutingControlRef.current.remove();
              zoomedOutRoutingControlRef.current = null;
            } catch (error) {
              console.warn("Error cleaning up zoomed out routing control:", error);
            }
          }

          zoomedOutMap.off();
          zoomedOutMap.remove();
        } catch (err) {
          console.warn("Error cleaning up zoomed out map:", err);
        }
      }

      if (zoomedInMap) {
        try {
          if (zoomedInRoutingControlRef.current) {
            try {
              safeClearRouteLines(zoomedInRoutingControlRef.current);
              zoomedInRoutingControlRef.current.remove();
              zoomedInRoutingControlRef.current = null;
            } catch (error) {
              console.warn("Error cleaning up zoomed in routing control:", error);
            }
          }

          zoomedInMap.off();
          zoomedInMap.remove();
        } catch (err) {
          console.warn("Error cleaning up zoomed in map:", err);
        }
      }

      if (routingControlRef && routingControlRef.current) {
        try {
          routingControlRef.current = null;
        } catch (err) {
          console.warn("Error cleaning up parent routing control reference:", err);
        }
      }

      ZipCodeBoundaryManager.clearBoundaries(zoomedOutMap, zoomedInMap);

      if (typeof window !== 'undefined') {
        window.zoomedOutMapInstance = null;
        window.zoomedInMapInstance = null;
      }

      zoomedOutMapRef.current = null;
      zoomedInMapRef.current = null;
      mapRef.current = null;
    };
  } catch (err) {
    console.error("Error during maps initialization:", err);

    setInternalLoading(false);
    if (typeof setIsLoading === 'function') {
      setIsLoading(false);
    }

    createFallbackMaps();
  }
}, [createFallbackMaps, currentLocation, handleMapClick, isAddingMarker, isAddingAdminMarker, setIsLoading, setNewMarkerPosition, isRotationEnabled, showZipCodes, updateUserLocationInDatabase, startContinuousTracking, onLocationUpdate]);

 // ENHANCED: Start continuous location tracking when component mounts
useEffect(() => {
  if (!locationWatchIdRef.current && !initialLocationRequestedRef.current) {
    console.log("Starting enhanced continuous location tracking from mount effect");
    initialLocationRequestedRef.current = true;
    const watchId = startContinuousTracking();
    
    locationWatchIdRef.current = watchId;
  }
  
  return () => {
    if (locationWatchIdRef.current !== null) {
      console.log("Stopping continuous location tracking on unmount");
      navigator.geolocation.clearWatch(locationWatchIdRef.current);
      locationWatchIdRef.current = null;
    }
  };
}, [startContinuousTracking]);

// Handle screen config changes
useEffect(() => {
  if (!zoomedOutContainerRef.current || !zoomedInContainerRef.current) return;

  ensureContainerDimensions(zoomedInContainerRef.current, 'zoomedIn');

  if (zoomedOutMapRef.current) {
    console.log("MapDisplay: Screen config changed, invalidating zoomed out map size");
    setTimeout(() => {
      zoomedOutMapRef.current?.invalidateSize({
        animate: false,
        pan: false,
        debounceMoveend: true
      });
    }, 100);
  }

  if (zoomedInMapRef.current) {
    console.log("MapDisplay: Screen config changed, invalidating zoomed in map size");
    setTimeout(() => {
      zoomedInMapRef.current?.invalidateSize({
        animate: false,
        pan: false,
        debounceMoveend: true
      });
    }, 100);
  }
}, [screenConfig]);

// Update rotation when current location changes
useEffect(() => {
  if (currentLocation && destinationRef.current && isRotationEnabled) {
    updateMapRotation();
  }
}, [currentLocation, isRotationEnabled]);

// CRITICAL FIX: Enhanced effect to handle current location changes
useEffect(() => {
  if (!currentLocation || (!currentLocation.lat && !currentLocation.lng)) {
    console.log("❌ Invalid current location:", currentLocation);
    return;
  }

  console.log("📍 Current location updated:", currentLocation);
  setDebugInfo(prev => ({
    ...prev,
    currentLocationReceived: true
  }));

  // CRITICAL FIX: Always update lastKnownPositionRef
  if (lastKnownPositionRef.current) {
    lastKnownPositionRef.current = currentLocation;
  }

  // CRITICAL FIX: Update user marker if maps are initialized
  if (zoomedOutMapRef.current && zoomedInMapRef.current) {
    const updateSuccess = updateUserLocationSmooth(currentLocation);
    
    if (!updateSuccess) {
      console.log("⚠️ User location update failed, retrying in 100ms");
      setTimeout(() => {
        updateUserLocationSmooth(currentLocation);
      }, 100);
    }
  } else {
    console.log("⚠️ Maps not ready yet, will update when available");
  }

  // ENHANCED: Trigger parent callbacks for location updates
  if (typeof onLocationUpdate === 'function') {
    onLocationUpdate(currentLocation);
  }
}, [currentLocation, onLocationUpdate]);

// Create a single resize observer for both containers
useEffect(() => {
  if (typeof ResizeObserver === 'undefined' || 
      !zoomedOutContainerRef.current || 
      !zoomedInContainerRef.current) {
    return;
  }
  
  if (resizeObserverRef.current) {
    resizeObserverRef.current.disconnect();
  }
  
  let resizeTimeout = null;
  const debouncedInvalidate = () => {
    if (resizeTimeout) {
      clearTimeout(resizeTimeout);
    }
    
    resizeTimeout = setTimeout(() => {
      try {
        if (zoomedOutMapRef.current) {
          zoomedOutMapRef.current.invalidateSize({
            animate: false,
            pan: false
          });
        }
      } catch (error) {
        console.warn("Error invalidating zoomed out map:", error);
      }
      
      try {
        if (zoomedInMapRef.current) {
          zoomedInMapRef.current.invalidateSize({
            animate: false,
            pan: false
          });
        }
      } catch (error) {
        console.warn("Error invalidating zoomed in map:", error);
      }
    }, 100);
  };
  
  const resizeObserver = new ResizeObserver(() => {
    console.log("Container size changed, will invalidate maps");
    debouncedInvalidate();
  });
  
  resizeObserver.observe(zoomedOutContainerRef.current);
  resizeObserver.observe(zoomedInContainerRef.current);
  
  resizeObserverRef.current = resizeObserver;
  
  return () => {
    if (resizeTimeout) {
      clearTimeout(resizeTimeout);
    }
    
    if (resizeObserverRef.current) {
      resizeObserverRef.current.disconnect();
      resizeObserverRef.current = null;
    }
  };
}, []);

// Emergency fix if maps are missing
useEffect(() => {
  if (!internalLoading && !parentIsLoading && zoomedOutContainerRef.current && zoomedInContainerRef.current 
      && (!zoomedOutMapRef.current || !zoomedInMapRef.current)) {
    console.log("EMERGENCY FIX: Creating maps as loading is off but maps don't exist");
    createFallbackMaps();
  }
}, [internalLoading, parentIsLoading, createFallbackMaps]);

// Effect for handling ZIP boundaries refreshes when map view changes
useEffect(() => {
  if (!showZipCodes || !zoomedOutMapRef.current || !zoomedInMapRef.current) return;
  
  const handleMapChange = () => {
    if (ZipCodeBoundaryManager.isLoaded && ZipCodeBoundaryManager.boundariesVisible) {
      clearTimeout(boundsChangeTimeoutRef.current);
      boundsChangeTimeoutRef.current = setTimeout(() => {
        if (zoomedOutMapRef.current && zoomedInMapRef.current) {
          ZipCodeBoundaryManager.displayBoundaries(
            zoomedOutMapRef.current,
            zoomedInMapRef.current
          );
        }
      }, 300);
    }
  };
  
  zoomedOutMapRef.current.on('moveend', handleMapChange);
  zoomedOutMapRef.current.on('zoomend', handleMapChange);
  
  return () => {
    if (zoomedOutMapRef.current) {
      zoomedOutMapRef.current.off('moveend', handleMapChange);
      zoomedOutMapRef.current.off('zoomend', handleMapChange);
    }
    clearTimeout(boundsChangeTimeoutRef.current);
  };
}, [showZipCodes]);

// ENHANCED: Find my location functionality
const handleFindMyLocation = () => {
  if (locationTrackingEnabled && lastKnownPositionRef.current) {
    console.log("Using last known location since tracking is already enabled");
    const location = lastKnownPositionRef.current;
    
    if (zoomedOutMapRef.current) {
      zoomedOutMapRef.current.setView([location.lat, location.lng], ZOOMED_OUT_LEVEL);
    }
    
    if (zoomedInMapRef.current) {
      zoomedInMapRef.current.setView([location.lat, location.lng], ZOOMED_IN_LEVEL);
    }
    
    return;
  }
  
  if (typeof findMyLocation === 'function') {
    console.log("Using provided findMyLocation function");
    findMyLocation();
  } else {
    console.log("Using enhanced geolocation tracking");
    startContinuousTracking();
  }
};

// ENHANCED: Refresh map functionality with better team zones refresh
const handleRefreshMap = () => {
  if (typeof refreshMap === 'function') {
    refreshMap();
  } else {
    if (zoomedOutMapRef.current && zoomedInMapRef.current) {
      zoomedOutMapRef.current.invalidateSize();
      zoomedInMapRef.current.invalidateSize();
      
      if (currentLocation) {
        updateUserLocationSmooth(currentLocation);
      }
    }
  }
  
  // ENHANCED: Always trigger team zones refresh with proper delay
  setTimeout(() => {
    if (typeof window !== 'undefined' && window.refreshTeamZones) {
      console.log("Map refreshed, refreshing team zones");
      window.refreshTeamZones();
    }
    
    if (showZipCodes && zoomedOutMapRef.current && zoomedInMapRef.current) {
      ZipCodeBoundaryManager.displayBoundaries(
        zoomedOutMapRef.current,
        zoomedInMapRef.current
      );
    }
  }, 300);
};

// Toggle rotation on/off
const toggleRotation = () => {
  setIsRotationEnabled(!isRotationEnabled);
  
  if (!isRotationEnabled && destinationRef.current) {
    setTimeout(updateMapRotation, 0);
  }
  
  if (isRotationEnabled) {
    setMapRotation(0);
    setMapScale(1.5);
  }
};

// Reset rotation to 0
const resetRotation = () => {
  setMapRotation(0);
  setMapScale(1.5);
};

// ENHANCED: Expose functions to parent component with new debugging capabilities
useEffect(() => {
  if (typeof window !== 'undefined') {
    window.setMapDestination = setDestination;
    window.toggleMapRotation = toggleRotation;
    window.resetMapRotation = resetRotation;
    window.updateUserLocation = updateUserLocationSmooth;
    window.toggleZipBoundaries = toggleZipBoundaries;
    window.toggleLocationTracking = toggleLocationTracking;
    
    // ENHANCED: Add enhanced navigation and debugging functions
    window.cancelNavigation = cancelNavigation;
    window.forceCreateUserMarker = forceCreateUserMarker;
    window.showMyLocation = () => {
      if (currentLocation) {
        forceCreateUserMarker();
      }
    };
    
    // CRITICAL FIX: Add debugging functions
    window.forceRecreateUserMarker = forceRecreateUserMarker;
    window.debugUserMarker = () => {
      console.log("🔍 User Marker Debug:", {
        hasCurrentLocation: !!(currentLocation?.lat && currentLocation?.lng),
        currentLocation,
        hasMarkerRef: !!currentLocationMarkerRef.current,
        hasZoomedOutMarker: !!(currentLocationMarkerRef.current?.zoomed_out),
        hasZoomedInMarker: !!(currentLocationMarkerRef.current?.zoomed_in),
        mapsReady: !!(zoomedOutMapRef.current && zoomedInMapRef.current),
        lastKnownPosition: lastKnownPositionRef.current
      });
    };
    
    // Test function to manually update marker position
    window.testUpdateUserMarker = (lat, lng) => {
      console.log(`🧪 Testing user marker update to ${lat}, ${lng}`);
      const testLocation = { lat, lng, timestamp: Date.now() };
      updateUserLocationSmooth(testLocation);
    };
    
    // ENHANCED: Advanced debugging functions
    window.getLocationTrackingStatus = () => {
      return {
        enabled: locationTrackingEnabled,
        watchId: locationWatchIdRef.current !== null,
        lastUpdate: lastLocationUpdateRef.current,
        updateInterval: locationUpdateIntervalRef.current,
        currentLocation: currentLocation,
        lastKnownPosition: lastKnownPositionRef.current
      };
    };
    
    window.testMapRotation = (degrees) => {
      console.log(`Setting map rotation to ${degrees} degrees`);
      setMapRotation(degrees);
      setMapScale(calculateScaleFactor(degrees));
      return true;
    };
    
    // ENHANCED: Comprehensive debug state function
    window.debugMapState = () => {
      return {
        zoomedOutMap: !!zoomedOutMapRef.current,
        zoomedInMap: !!zoomedInMapRef.current,
        currentLocation,
        destinationSet: !!destinationRef.current,
        rotationEnabled: isRotationEnabled,
        userMarker: !!currentLocationMarkerRef.current,
        destinationMarker: !!destinationMarkerRef.current,
        routeLine: !!routeLineRef.current,
        updateInProgress: updateInProgressRef.current,
        routeInfo: routeInfo,
        nextManeuver: nextManeuver,
        locationTracking: {
          enabled: locationTrackingEnabled,
          watchId: locationWatchIdRef.current !== null,
          lastUpdate: lastLocationUpdateRef.current,
          updateInterval: locationUpdateIntervalRef.current
        },
        routeCache: {
          size: ROUTE_REQUEST_CACHE.size,
          pendingRequests: PENDING_REQUESTS.size,
          minInterval: MIN_REQUEST_INTERVAL
        },
        zipCodeBoundaries: {
          visible: showZipCodes,
          loaded: ZipCodeBoundaryManager.isLoaded,
          selectedCount: ZipCodeBoundaryManager.selectedZipCodes.length
        },
        teamMembers: {
          count: teamMembers.length,
          markersZoomedOut: teamMemberMarkersRef.current.zoomedOut.size,
          markersZoomedIn: teamMemberMarkersRef.current.zoomedIn.size,
          trailColors: teamMembers.reduce((acc, member) => {
            if (member.trailColor) {
              acc[member.id] = member.trailColor;
            }
            return acc;
          }, {})
        },
        processedMarkers: {
          hasData: !!processedMarkers,
          isProcessing: isProcessingMarkers,
          summary: processedMarkers?.summary || null,
          markersZoomedOut: processedMarkersRef.current.zoomedOut.size,
          markersZoomedIn: processedMarkersRef.current.zoomedIn.size
        },
        teamVehicles: {
          count: teamVehicles.length,
          markersZoomedOut: teamVehicleMarkersRef.current.zoomedOut.size,
          markersZoomedIn: teamVehicleMarkersRef.current.zoomedIn.size
        },
        enhanced: {
          liveUpdatesEnabled: true,
          updateFrequency: '1-2 seconds',
          coloredTrails: teamMembers.filter(m => m.trailColor).length,
          totalUsers: teamMembers.length
        }
      };
    };
    
    // NEW: Debug processed markers
    window.debugProcessedMarkers = () => {
      console.log("🔍 Processed Markers Debug:", {
        hasProcessedMarkers: !!processedMarkers,
        isProcessing: isProcessingMarkers,
        summary: processedMarkers?.summary,
        orderMarkers: processedMarkers?.orderMarkers?.length || 0,
        manualMarkers: processedMarkers?.manualMarkers?.length || 0,
        teamMarkers: processedMarkers?.teamMarkers?.length || 0,
        leafletMarkersZoomedOut: processedMarkersRef.current.zoomedOut.size,
        leafletMarkersZoomedIn: processedMarkersRef.current.zoomedIn.size
      });
    };
    
    // NEW: Debug team vehicles
    window.debugTeamVehicles = () => {
      console.log("🔍 Team Vehicles Debug:", {
        teamVehiclesCount: teamVehicles.length,
        markersZoomedOut: teamVehicleMarkersRef.current.zoomedOut.size,
        markersZoomedIn: teamVehicleMarkersRef.current.zoomedIn.size,
        vehicles: teamVehicles.map(v => ({
          id: v.id,
          vehicle: v.vehicle,
          vin: v.vin,
          hasLocation: !!(v.lat && v.lng),
          status: v.arrivedDriverId ? 'ARRIVED' : v.inRouteDriverId ? 'IN_ROUTE' : v.bottomStatus || 'AVAILABLE'
        }))
      });
    };
    
    // ENHANCED: Clear all routes with better cleanup
    window.clearAllRoutes = async () => {
      try {
        let clearedAny = false;
        
        if (zoomedOutRoutingControlRef.current) {
          clearedAny = await safeClearRouteLines(zoomedOutRoutingControlRef.current) || clearedAny;
        }
        
        if (zoomedInRoutingControlRef.current) {
          clearedAny = await safeClearRouteLines(zoomedInRoutingControlRef.current) || clearedAny;
        }
        
        if (routingControlRef && routingControlRef.current) {
          clearedAny = await safeClearRouteLines(routingControlRef.current) || clearedAny;
        }
        
        ROUTE_REQUEST_CACHE.clear();
        PENDING_REQUESTS.clear();
        
        return clearedAny;
      } catch (error) {
        console.warn("Error clearing routes:", error);
        return false;
      }
    };
    
    console.log("Enhanced map navigation functions available globally with live tracking, team vehicles, and marker debugging");
  }
  
  return () => {
    if (typeof window !== 'undefined') {
      delete window.setMapDestination;
      delete window.toggleMapRotation;
      delete window.resetMapRotation;
      delete window.updateUserLocation;
      delete window.testMapRotation;
      delete window.debugMapState;
      delete window.clearAllRoutes;
      delete window.toggleZipBoundaries;
      delete window.toggleLocationTracking;
      delete window.cancelNavigation;
      delete window.forceCreateUserMarker;
      delete window.showMyLocation;
      delete window.getLocationTrackingStatus;
      delete window.forceRecreateUserMarker;
      delete window.debugUserMarker;
      delete window.testUpdateUserMarker;
      delete window.debugProcessedMarkers;
      delete window.debugTeamVehicles;
    }
  };
}, [isRotationEnabled, currentLocation, showZipCodes, locationTrackingEnabled, teamMembers, cancelNavigation, forceCreateUserMarker, forceRecreateUserMarker, processedMarkers, isProcessingMarkers, teamVehicles]);

// Get dynamic container styles
const containerWrapperStyles = getContainerLayout();
const zoomedOutContainerStyles = calculateContainerDimensions('zoomedOut');
const zoomedInContainerStyles = calculateContainerDimensions('zoomedIn');

return (
  <div className="map-container-wrapper relative flex" style={{ 
    width: "100%", 
    height: "100%", 
    zIndex: 10,
    ...containerWrapperStyles
  }}>
    {/* Detailed Map (zoomed in) - ALWAYS ON TOP - WITH ROTATION */}
    <div className="zoomed-in-map-wrapper relative flex flex-col" ref={zoomedInMapWrapperRef} style={{ flex: 1 }}>
      <div className="text-center text-white text-sm bg-gray-800 py-1 mb-1 rounded-t-lg flex justify-between items-center px-2">
        <span>Detailed Map</span>
        <div className="flex items-center">
          <button 
            className={`text-xs px-2 py-1 rounded ${isRotationEnabled ? 'bg-green-600' : 'bg-gray-600'}`}
            onClick={toggleRotation}
            title={isRotationEnabled ? "Disable auto-rotation" : "Enable auto-rotation"}
          >
            {isRotationEnabled ? "Auto-Rotate: ON" : "Auto-Rotate: OFF"}
          </button>
        </div>
      </div>
      <div 
        className="zoomed-in-map-container relative border border-gray-700 rounded-lg overflow-hidden"
        style={{ 
          flex: 1,
          position: "relative"
        }}
      >
        <div 
          className="absolute inset-0 transform-wrapper"
          style={{
            transform: isRotationEnabled ? `rotate(${mapRotation}deg) scale(${mapScale})` : "none",
            transition: "transform 0.3s ease-out",
            transformOrigin: "center center",
            willChange: "transform"
          }}
        >
          <div 
            ref={zoomedInContainerRef} 
            className="absolute inset-0 leaflet-container"
          />
        </div>
        
        {isRotationEnabled && destinationRef.current && (
          <div className="absolute top-4 left-4 rotate-indicator z-50">
            <div className="bg-black bg-opacity-50 px-2 py-1 rounded text-white text-xs flex items-center">
              <svg className="w-4 h-4 mr-1 text-green-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
              </svg>
              Rotating
            </div>
          </div>
        )}
      </div>
    </div>
    
    {/* Overview Map (zoomed out) - ALWAYS AT BOTTOM */}
    <div className="zoomed-out-map-wrapper relative flex flex-col" style={{ flex: 1 }}>
      <div className="text-center text-white text-sm bg-gray-800 py-1 mb-1 rounded-t-lg">
        Overview Map
      </div>
      <div 
        ref={zoomedOutContainerRef} 
        className="w-full rounded-lg border border-gray-700 leaflet-container flex-grow"
        style={{ 
          ...zoomedOutContainerStyles,
          position: "relative",
          overflow: "hidden",
          zIndex: 10
        }} 
      />
    </div>
    
    {/* ENHANCED: Add TeamTrailSystem to both maps with trail color support */}
    {zoomedOutMapRef.current && (
      <TeamTrailSystem
        map={zoomedOutMapRef.current}
        teamMembers={teamMembers}
        onlineUsers={onlineUsers}
        currentUser={currentUser}
        isAdmin={isAdmin}
        userDisplayNames={userDisplayNames}
        userProfilePictures={userProfilePictures}
        confirmDeleteTrail={confirmDeleteTrail}
        clearTrail={clearTrail}
      />
    )}
    
    {zoomedInMapRef.current && (
      <TeamTrailSystem
        map={zoomedInMapRef.current}
        teamMembers={teamMembers}
        onlineUsers={onlineUsers}
        currentUser={currentUser}
        isAdmin={isAdmin}
        userDisplayNames={userDisplayNames}
        userProfilePictures={userProfilePictures}
        confirmDeleteTrail={confirmDeleteTrail}
        clearTrail={clearTrail}
      />
    )}
    
    {/* Loading indicator */}
    {internalLoading && (
      <div className="absolute inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50">
        <div className="text-center p-4 bg-gray-800 rounded-lg">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-200 text-lg">Loading enhanced maps...</p>
          <p className="text-gray-400 text-sm mt-2">
            Setting up live location tracking and processing markers...
          </p>
          <button 
            className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            onClick={() => {
              setInternalLoading(false);
              if (typeof setIsLoading === 'function') {
                setIsLoading(false);
              }
              window.location.reload();
            }}
          >
            Refresh Page
          </button>
        </div>
      </div>
    )}
    
    {/* Parent loading indicator */}
    {!internalLoading && parentIsLoading && (
      <div className="absolute inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
        <div className="text-center bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p>Loading map data...</p>
          <button 
            className="mt-3 bg-blue-500 text-white px-3 py-1 rounded text-sm"
            onClick={() => {
              if (typeof setIsLoading === 'function') {
                setIsLoading(false);
              }
            }}
          >
            Cancel Loading
          </button>
        </div>
      </div>
    )}
    
    {/* Map refreshing indicator */}
    {isMapRefreshing && (
      <div className="absolute inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
        <div className="text-center bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p>Refreshing enhanced maps...</p>
        </div>
      </div>
    )}
    
    {/* Processing markers indicator */}
    {isProcessingMarkers && (
      <div className="absolute top-4 left-4 bg-blue-800 bg-opacity-90 border border-blue-600 rounded-lg p-2 shadow-lg z-40 text-xs text-white">
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400 mr-2"></div>
          <span>Processing markers...</span>
        </div>
      </div>
    )}
    
    {/* Adding marker indicator */}
    {isAddingMarker && !newMarkerPosition && (
      <div className="absolute top-4 left-0 right-0 mx-auto w-64 bg-blue-900 text-blue-100 px-4 py-2 rounded-lg text-center shadow-lg z-50 text-sm">
        Click on map to add location
      </div>
    )}

    {/* ENHANCED: Map control buttons with location tracking status */}
    <div className="absolute bottom-4 right-4 flex flex-col space-y-2 z-20">
      {/* ENHANCED: Location tracking status indicator with more info */}
      <div 
        className={`${locationTrackingEnabled ? 'bg-green-600' : 'bg-red-600'} text-white p-2 rounded-full shadow flex items-center justify-center cursor-pointer relative group`}
        onClick={toggleLocationTracking}
        title={locationTrackingEnabled ? "Live tracking enabled - click to disable" : "Live tracking disabled - click to enable"}
      >
        {locationTrackingEnabled ? (
          <svg className="h-6 w-6 animate-pulse" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 3l-6 6m0 0V4m0 5h5M5 3a2 2 0 00-2 2v1c0 8.284 6.716 15 15 15h1a2 2 0 002-2v-3.28a1 1 0 00-.684-.948l-4.493-1.498a1 1 0 00-1.21.502l-1.13 2.257a11.042 11.042 0 01-5.516-5.517l2.257-1.128a1 1 0 00.502-1.21L9.228 3.683A1 1 0 008.279 3H5z" />
          </svg>
        ) : (
          <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
          </svg>
        )}
        
        {/* Enhanced tooltip */}
        <div className="absolute right-full mr-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap pointer-events-none">
          {locationTrackingEnabled ? "Live Tracking: ON" : "Live Tracking: OFF"}
        </div>
      </div>
      
      {/* Find location button */}
      <div 
        className="bg-blue-600 hover:bg-blue-500 text-white p-2 rounded-full shadow cursor-pointer"
        onClick={handleFindMyLocation}
        title="Find my location"
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-6 w-6" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" 
          />
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" 
          />
        </svg>
      </div>
      
      {/* Refresh map button */}
      <div 
        className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-full shadow cursor-pointer"
        onClick={handleRefreshMap}
        title="Refresh maps and team zones"
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-6 w-6" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
          />
        </svg>
      </div>
      
      {/* Toggle zip code boundaries button */}
      <div 
        className={`${showZipCodes ? 'bg-blue-600 hover:bg-blue-500' : 'bg-gray-600 hover:bg-gray-500'} text-white p-2 rounded-full shadow cursor-pointer`}
        onClick={toggleZipBoundaries}
        title={showZipCodes ? "Hide ZIP code boundaries" : "Show ZIP code boundaries"}
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-6 w-6" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" 
            />
          </svg>
        </div>
        
        {/* Toggle rotation button */}
        <div 
          className={`${isRotationEnabled ? 'bg-green-600 hover:bg-green-500' : 'bg-gray-600 hover:bg-gray-500'} text-white p-2 rounded-full shadow cursor-pointer`}
          onClick={toggleRotation}
          title={isRotationEnabled ? "Disable auto-rotation" : "Enable auto-rotation"}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 4v1m6 11h2m-6 0h-2v4m0-11v-4m6 6h2m-10 0H2" 
            />
          </svg>
        </div>
        
        {/* Reset rotation button (only shown when rotation is enabled) */}
        {isRotationEnabled && (
          <div 
            className="bg-purple-600 hover:bg-purple-500 text-white p-2 rounded-full shadow cursor-pointer"
            onClick={resetRotation}
            title="Reset map rotation"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M10 19l-7-7m0 0l7-7m-7 7h18" 
              />
            </svg>
          </div>
        )}
        
        {/* Cancel Navigation button (only shown when destination is set) */}
        {destinationRef.current && (
          <div 
            className="bg-red-600 hover:bg-red-500 text-white p-2 rounded-full shadow cursor-pointer"
            onClick={cancelNavigation}
            title="Cancel navigation"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
          </div>
        )}

        {/* Show My Location button for debugging */}
        <div 
          className="bg-purple-600 hover:bg-purple-500 text-white p-2 rounded-full shadow cursor-pointer"
          onClick={forceCreateUserMarker}
          title="Show my location marker"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" 
            />
          </svg>
        </div>
        
        {/* Clear trail button (only shown when clocked in and has moved) */}
        {isClockedIn && hasMovedSinceClockIn && (
          <div 
            className="bg-red-600 hover:bg-red-500 text-white p-2 rounded-full shadow cursor-pointer"
            onClick={clearTrail}
            title="Clear trail"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" 
              />
            </svg>
          </div>
        )}
      </div>
      
      {/* Delete trail confirmation */}
      {showDeleteConfirmation && (
        <div className="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-[2000]">
          <div className="bg-gray-800 rounded-lg p-4 max-w-md mx-4">
            <h3 className="text-white text-lg font-bold mb-2">Confirm Delete Trail</h3>
            <p className="text-gray-300 mb-4">
              Are you sure you want to delete this user's trail? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600"
                onClick={() => setShowDeleteConfirmation(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-500"
                onClick={() => {
                  deleteUserTrail(userToDeleteTrail);
                  setShowDeleteConfirmation(false);
                }}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* ZIP code info panel (shown when ZIP code boundaries are active) */}
      {showZipCodes && ZipCodeBoundaryManager.selectedZipCodes.length > 0 && (
        <div className="absolute top-4 left-4 bg-gray-800 bg-opacity-90 border border-gray-700 rounded-lg p-2 shadow-lg z-50 max-w-xs">
          <div className="flex flex-col">
            <h3 className="text-white text-sm font-medium mb-1">Selected ZIP Codes</h3>
            <div className="flex flex-wrap gap-1 max-h-32 overflow-y-auto">
              {ZipCodeBoundaryManager.selectedZipCodes.map(zipCode => (
                <span 
                  key={zipCode}
                  className="bg-green-700 text-white text-xs px-2 py-1 rounded-md flex items-center"
                >
                  {zipCode}
                  <button
                    onClick={() => {
                      ZipCodeBoundaryManager.selectedZipCodes = ZipCodeBoundaryManager.selectedZipCodes.filter(z => z !== zipCode);
                      if (zoomedOutMapRef.current && zoomedInMapRef.current) {
                        ZipCodeBoundaryManager.displayBoundaries(
                          zoomedOutMapRef.current,
                          zoomedInMapRef.current
                        );
                      }
                    }}
                    className="ml-1 text-green-300 hover:text-white"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
            <button
              onClick={() => {
                ZipCodeBoundaryManager.selectedZipCodes = [];
                if (zoomedOutMapRef.current && zoomedInMapRef.current) {
                  ZipCodeBoundaryManager.displayBoundaries(
                    zoomedOutMapRef.current,
                    zoomedInMapRef.current
                  );
                }
              }}
              className="mt-2 bg-red-600 hover:bg-red-500 text-white text-xs px-2 py-1 rounded"
            >
              Clear All Selected ZIP Codes
            </button>
          </div>
        </div>
      )}
      
      {/* ENHANCED: Real-time GPS tracking indicator with more details */}
      <div className="absolute bottom-4 left-4 z-20">
        {locationTrackingEnabled && (
          <div className="bg-black bg-opacity-50 px-3 py-1 rounded-lg text-white text-xs flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            <span>Live GPS Tracking</span>
            {teamMembers.length > 0 && (
              <span className="ml-2 text-green-300">
                ({teamMembers.filter(m => m.trailColor).length} colored trails)
              </span>
            )}
          </div>
        )}
        
        {/* Enhanced marker status info */}
        {processedMarkers && (
          <div className="bg-blue-800 bg-opacity-50 px-3 py-1 rounded-lg text-white text-xs mt-2">
            <span>📍 {processedMarkers.summary?.totalMarkers || 0} markers loaded</span>
            <span className="ml-2">🚗 {processedMarkersRef.current?.zoomedOut?.size || 0} on map</span>
          </div>
        )}
        
        {/* Team vehicles indicator */}
        {teamVehicles.length > 0 && (
          <div className="bg-purple-800 bg-opacity-50 px-3 py-1 rounded-lg text-white text-xs mt-2">
            <span>🚗 {teamVehicles.length} team vehicles</span>
            <span className="ml-2">
              {teamVehicles.filter(v => v.inRouteDriverId || v.arrivedDriverId).length} assigned
            </span>
          </div>
        )}
      </div>
      
      {/* Enhanced marker status info (top right) */}
      {processedMarkers && processedMarkers.summary && (
        <div className="absolute top-4 right-4 bg-gray-800 bg-opacity-90 border border-gray-700 rounded-lg p-2 shadow-lg z-40 text-xs text-white">
          <div className="flex flex-col space-y-1">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-600 rounded-full mr-2"></div>
              <span>Orders: {processedMarkers.summary.orderAddresses || 0}</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-orange-600 rounded-full mr-2"></div>
              <span>Locations: {processedMarkers.summary.manualLocations || 0}</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-600 rounded-full mr-2"></div>
              <span>Team: {processedMarkers.summary.teamMembers || 0}</span>
            </div>
            {teamVehicles.length > 0 && (
              <div className="flex items-center border-t border-gray-600 pt-1 mt-1">
                <div className="w-3 h-3 bg-purple-600 rounded-full mr-2"></div>
                <span>Vehicles: {teamVehicles.length}</span>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* User Action Popup */}
      {showUserActionPopup && userForAction && (
        <div 
          className="absolute bg-gray-800 border border-gray-600 rounded-lg shadow-lg p-3 z-50"
          style={{
            left: userActionPopupPosition.x,
            top: userActionPopupPosition.y,
            transform: 'translate(-50%, -100%)',
            marginTop: '-10px'
          }}
        >
          <div className="text-white text-sm">
            <div className="font-medium mb-2">
              {userDisplayNames[userForAction] || `User ${userForAction}`}
            </div>
            <div className="flex flex-col space-y-2">
              <button
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs"
                onClick={() => {
                  showUserInDetailsPanel(userForAction);
                  setShowUserActionPopup(false);
                }}
              >
                View Details
              </button>
              <button
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs"
                onClick={() => {
                  handleStartDM(userForAction);
                  setShowUserActionPopup(false);
                }}
              >
                Send Message
              </button>
              {isAdmin && (
                <button
                  className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs"
                  onClick={() => {
                    confirmDeleteTrail(userForAction);
                    setShowUserActionPopup(false);
                  }}
                >
                  Delete Trail
                </button>
              )}
              <button
                className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs"
                onClick={() => setShowUserActionPopup(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Error display */}
      {error && (
        <div className="absolute bottom-20 left-4 right-4 bg-red-800 bg-opacity-90 border border-red-600 rounded-lg p-3 shadow-lg z-50">
          <div className="text-white text-sm">
            <div className="flex items-center mb-2">
              <svg className="w-4 h-4 mr-2 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">Map Error</span>
            </div>
            <p className="text-red-100 text-xs">{error}</p>
            <button
              className="mt-2 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs"
              onClick={() => {
                if (typeof setIsLoading === 'function') {
                  setIsLoading(false);
                }
                window.location.reload();
              }}
            >
              Reload Page
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MapDisplay;