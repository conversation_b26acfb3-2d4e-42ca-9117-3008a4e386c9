import React, { useContext } from 'react';
// In all components
import { MapContext } from '../MapContext';

const UserInfoBar = () => {
  const {
    currentUser,
    isAdmin,
    isTowTruckUser,
    userDisplayNames,
    allUsers,
    locations
  } = useContext(MapContext);

  return (
    <div className="bg-gray-800 text-gray-200 px-2 py-1 text-xs flex justify-between items-center flex-wrap">
      <div className="truncate">
        {currentUser ? (
          <span className="truncate">
            Logged in: {userDisplayNames[currentUser.uid] || currentUser.email || currentUser.uid.substring(0, 8)} 
            {isAdmin && 
              <span className="bg-blue-900 text-blue-200 px-1 py-0.5 rounded text-xs ml-1">Admin</span>
            }
            {isTowTruckUser && 
              <span className="bg-green-900 text-green-200 px-1 py-0.5 rounded text-xs ml-1">Tow Truck</span>
            }
          </span>
        ) : (
          <span className="text-red-400">Not logged in</span>
        )}
      </div>
      <div className="text-xs text-gray-400 flex-shrink-0">
        {locations.length} locations | {allUsers.length} users online
      </div>
    </div>
  );
};

export default UserInfoBar;