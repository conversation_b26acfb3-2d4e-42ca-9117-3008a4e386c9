// server.js - Standalone backend server for Slack bot OAuth
const express = require('express');
const bodyParser = require('body-parser');
const axios = require('axios');
const admin = require('firebase-admin');
require('dotenv').config();

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID || process.env.REACT_APP_PROD_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    })
  });
}

const db = admin.firestore();
const app = express();

// Middleware
app.use(bodyParser.json());
app.use((req, res, next) => {
  // Allow requests from your React app domain
  const allowedOrigins = [
    'http://localhost:3000',
    'https://recoveriqs.net',
    'https://www.recoveriqs.net'
  ];
  
  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  }
  
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  
  next();
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({ 
    message: 'Vehicle Tracker Slack Bot Backend',
    endpoints: {
      health: '/health',
      oauth: '/api/slack-oauth',
      webhook: '/api/slack/vehicle-update'
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'slack-bot-backend'
  });
});

// OAuth endpoint for the React app /bot page
app.post('/api/slack-oauth', async (req, res) => {
  console.log('[OAUTH] Received OAuth exchange request');
  
  const { code, redirectUri } = req.body;

  if (!code) {
    return res.status(400).json({ error: 'Missing OAuth code' });
  }

  try {
    // Exchange code for access token
    const response = await axios.post('https://slack.com/api/oauth.v2.access', null, {
      params: {
        client_id: process.env.SLACK_CLIENT_ID,
        client_secret: process.env.SLACK_CLIENT_SECRET,
        code: code,
        redirect_uri: redirectUri || 'https://recoveriqs.net/bot'
      }
    });

    const { ok, access_token, team, authed_user, bot_user_id, app_id, error } = response.data;

    if (!ok) {
      console.error('[OAUTH] Slack OAuth error:', response.data);
      return res.status(400).json({ 
        error: 'OAuth failed', 
        details: error || response.data.error 
      });
    }

    console.log(`[OAUTH] Successfully authenticated for team: ${team.name}`);

    // Store installation data
    await db.collection('slack_installations').doc(team.id).set({
      teamId: team.id,
      teamName: team.name,
      accessToken: access_token,
      botUserId: bot_user_id,
      appId: app_id,
      installedBy: authed_user.id,
      installedAt: admin.firestore.FieldValue.serverTimestamp(),
      isActive: true
    });

    // Check/create team
    const teamQuery = await db.collection('teams')
      .where('slackTeamId', '==', team.id)
      .limit(1)
      .get();

    if (teamQuery.empty) {
      await db.collection('teams').add({
        name: team.name,
        slackTeamId: team.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log(`[OAUTH] Created new team: ${team.name}`);
    } else {
      // Update existing team
      await teamQuery.docs[0].ref.update({
        slackInstallation: {
          installed: true,
          installedAt: admin.firestore.FieldValue.serverTimestamp(),
          botUserId: bot_user_id
        }
      });
      console.log(`[OAUTH] Updated existing team: ${team.name}`);
    }

    // Send welcome message using the new access token
    try {
      const { WebClient } = require('@slack/web-api');
      const slackClient = new WebClient(access_token);
      
      await slackClient.chat.postMessage({
        channel: authed_user.id,
        text: 'Welcome to Vehicle Tracker Bot! 🚗',
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `👋 *Welcome to Vehicle Tracker Bot!*\n\nSuccessfully installed in *${team.name}*!`
            }
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '*Getting Started:*\n1. Add me to a channel: `/invite @Vehicle Tracker`\n2. Link teams: `/vehicle setup link`\n3. View vehicles: `/vehicle list`\n4. Get help: `/vehicle help`'
            }
          },
          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: 'Need help? Contact <EMAIL>'
              }
            ]
          }
        ]
      });
      console.log('[OAUTH] Welcome message sent');
    } catch (msgError) {
      console.error('[OAUTH] Failed to send welcome message:', msgError);
      // Don't fail the installation if message fails
    }

    res.json({
      success: true,
      team: {
        id: team.id,
        name: team.name
      },
      message: 'Bot successfully installed!'
    });

  } catch (error) {
    console.error('[OAUTH] Error:', error.response?.data || error.message);
    res.status(500).json({ 
      error: 'Failed to complete installation',
      details: error.response?.data || error.message 
    });
  }
});

// Vehicle update webhook endpoint
app.post('/api/slack/vehicle-update', async (req, res) => {
  console.log('[WEBHOOK] Received vehicle update');
  
  try {
    const { teamId, vehicle, updateType } = req.body;
    
    if (!teamId || !vehicle) {
      return res.status(400).json({ error: 'Missing required data' });
    }
    
    // For the standalone server, we'll just acknowledge the webhook
    // The full Slack bot would handle posting to Slack here
    console.log(`[WEBHOOK] Vehicle update: ${updateType} for ${vehicle.vehicle}`);
    
    res.json({ 
      success: true, 
      message: 'Update received',
      note: 'To post to Slack, run the full bot: node src/components/slack-vehicle-bot.js'
    });
    
  } catch (error) {
    console.error('[WEBHOOK] Error processing update:', error);
    res.status(500).json({ error: 'Failed to process update', details: error.message });
  }
});

// Start server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`🚀 Slack Bot Backend Server running on port ${PORT}`);
  console.log(`   Root: http://localhost:${PORT}/`);
  console.log(`   Health check: http://localhost:${PORT}/health`);
  console.log(`   OAuth endpoint: http://localhost:${PORT}/api/slack-oauth`);
  console.log(`   Webhook endpoint: http://localhost:${PORT}/api/slack/vehicle-update`);
});