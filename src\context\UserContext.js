import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { 
  getAuth, 
  onAuthStateChanged, 
  signInWithEmailAndPassword, 
  signOut, 
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  updateProfile as updateAuthProfile,
  reauthenticateWithCredential,
  EmailAuthProvider
} from 'firebase/auth';
import { 
  getFirestore, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  serverTimestamp,
  collection,
  query,
  where,
  getDocs
} from 'firebase/firestore';

// Create the context
const UserContext = createContext(null);

/**
 * User context provider component
 */
export const UserProvider = ({ children }) => {
  const auth = getAuth();
  const db = getFirestore();
  
  // User authentication state
  const [currentUser, setCurrentUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // User permissions and roles
  const [isAdmin, setIsAdmin] = useState(false);
  const [userRoles, setUserRoles] = useState([]);
  
  // User profile data
  const [profileData, setProfileData] = useState(null);
  const [profilePicture, setProfilePicture] = useState(null);
  const [displayName, setDisplayName] = useState('');
  
  // User preferences
  const [preferences, setPreferences] = useState({
    darkMode: true,
    notifications: true,
    defaultMapView: 'hybrid'
  });
  
  /**
   * Handle authentication state changes
   */
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setIsLoading(true);
      
      if (user) {
        setCurrentUser(user);
        
        // Load user profile data
        await loadUserProfile(user.uid);
        
        // Check user roles and permissions
        await checkUserPermissions(user.uid);
        
        // Load user preferences
        await loadUserPreferences(user.uid);
      } else {
        // Reset user state when logged out
        setCurrentUser(null);
        setProfileData(null);
        setProfilePicture(null);
        setDisplayName('');
        setIsAdmin(false);
        setUserRoles([]);
        
        // Reset preferences to defaults
        setPreferences({
          darkMode: true,
          notifications: true,
          defaultMapView: 'hybrid'
        });
      }
      
      setIsLoading(false);
    });
    
    return () => unsubscribe();
  }, [auth, db]);
  
  /**
   * Load user profile data from Firestore
   */
  const loadUserProfile = async (userId) => {
    try {
      const profileRef = doc(db, "userProfiles", userId);
      const profileDoc = await getDoc(profileRef);
      
      if (profileDoc.exists()) {
        const data = profileDoc.data();
        setProfileData(data);
        setProfilePicture(data.photoBase64 || null);
        setDisplayName(data.displayName || currentUser?.displayName || '');
      } else {
        // Create default profile if it doesn't exist
        const defaultProfile = {
          uid: userId,
          displayName: currentUser?.displayName || '',
          email: currentUser?.email || '',
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          tags: [],
          photoBase64: null
        };
        
        await setDoc(profileRef, defaultProfile);
        setProfileData(defaultProfile);
      }
    } catch (error) {
      console.error("Error loading user profile:", error);
      setError("Failed to load user profile data.");
    }
  };
  
  /**
   * Check user permissions and roles
   */
  const checkUserPermissions = async (userId) => {
    try {
      const profileRef = doc(db, "userProfiles", userId);
      const profileDoc = await getDoc(profileRef);
      
      if (profileDoc.exists()) {
        // Check admin status
        setIsAdmin(profileDoc.data().isAdmin === true);
        
        // Get user roles/tags
        const tags = profileDoc.data().tags || [];
        setUserRoles(tags);
      } else {
        setIsAdmin(false);
        setUserRoles([]);
      }
    } catch (error) {
      console.error("Error checking user permissions:", error);
      setIsAdmin(false);
      setUserRoles([]);
    }
  };
  
  /**
   * Load user preferences
   */
  const loadUserPreferences = async (userId) => {
    try {
      const preferencesRef = doc(db, "userPreferences", userId);
      const preferencesDoc = await getDoc(preferencesRef);
      
      if (preferencesDoc.exists()) {
        setPreferences(preferencesDoc.data());
      } else {
        // Set default preferences
        const defaultPreferences = {
          darkMode: true,
          notifications: true,
          defaultMapView: 'hybrid'
        };
        
        await setDoc(preferencesRef, defaultPreferences);
        setPreferences(defaultPreferences);
      }
    } catch (error) {
      console.error("Error loading user preferences:", error);
      // Keep default preferences if fails
    }
  };
  
  /**
   * Sign in with email and password
   */
  const signIn = async (email, password) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } catch (error) {
      console.error("Error signing in:", error);
      
      // Provide user-friendly error messages
      let errorMessage;
      switch (error.code) {
        case 'auth/invalid-email':
          errorMessage = "Invalid email address format.";
          break;
        case 'auth/user-disabled':
          errorMessage = "This account has been disabled.";
          break;
        case 'auth/user-not-found':
          errorMessage = "No account found with this email.";
          break;
        case 'auth/wrong-password':
          errorMessage = "Incorrect password.";
          break;
        default:
          errorMessage = "Failed to sign in. Please try again.";
      }
      
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Register a new account
   */
  const register = async (email, password, name) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Create user account
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Update display name in Auth profile
      await updateAuthProfile(user, {
        displayName: name
      });
      
      // Create user profile in Firestore
      const profileData = {
        uid: user.uid,
        email: email,
        displayName: name,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        tags: [],
        isAdmin: false,
        photoBase64: null
      };
      
      await setDoc(doc(db, "userProfiles", user.uid), profileData);
      
      // Create default preferences
      const defaultPreferences = {
        darkMode: true,
        notifications: true,
        defaultMapView: 'hybrid'
      };
      
      await setDoc(doc(db, "userPreferences", user.uid), defaultPreferences);
      
      return user;
    } catch (error) {
      console.error("Error registering:", error);
      
      // Provide user-friendly error messages
      let errorMessage;
      switch (error.code) {
        case 'auth/email-already-in-use':
          errorMessage = "This email is already in use.";
          break;
        case 'auth/invalid-email':
          errorMessage = "Invalid email address format.";
          break;
        case 'auth/weak-password':
          errorMessage = "Password is too weak. Use at least 6 characters.";
          break;
        default:
          errorMessage = "Failed to register. Please try again.";
      }
      
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Sign out the current user
   */
  const logOut = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Update user status to offline in Firestore
      if (currentUser) {
        try {
          const userDocRef = doc(db, 'userLocations', currentUser.uid);
          const userDoc = await getDoc(userDocRef);
          
          if (userDoc.exists()) {
            await updateDoc(userDocRef, {
              online: false,
              lastUpdated: serverTimestamp()
            });
          }
        } catch (err) {
          console.warn("Error updating offline status:", err);
        }
      }
      
      // Sign out from Firebase
      await signOut(auth);
      return true;
    } catch (error) {
      console.error("Error signing out:", error);
      setError("Failed to sign out. Please try again.");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Reset password via email
   */
  const resetPassword = async (email) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await sendPasswordResetEmail(auth, email);
      return true;
    } catch (error) {
      console.error("Error resetting password:", error);
      
      // Provide user-friendly error messages
      let errorMessage;
      switch (error.code) {
        case 'auth/invalid-email':
          errorMessage = "Invalid email address format.";
          break;
        case 'auth/user-not-found':
          errorMessage = "No account found with this email.";
          break;
        default:
          errorMessage = "Failed to send password reset email. Please try again.";
      }
      
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Update user profile
   */
  const updateProfile = async (profileUpdates) => {
    if (!currentUser) {
      setError("You must be logged in to update your profile.");
      throw new Error("User not authenticated");
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { displayName, photoBase64 } = profileUpdates;
      
      // Update Firebase Auth profile if display name is provided
      if (displayName) {
        await updateAuthProfile(currentUser, { displayName });
      }
      
      // Update Firestore profile
      const profileRef = doc(db, "userProfiles", currentUser.uid);
      const updates = {
        updatedAt: serverTimestamp()
      };
      
      if (displayName) updates.displayName = displayName;
      if (photoBase64) updates.photoBase64 = photoBase64;
      
      await updateDoc(profileRef, updates);
      
      // Update local state
      if (displayName) setDisplayName(displayName);
      if (photoBase64) setProfilePicture(photoBase64);
      
      // Also update in userLocations for faster access
      try {
        const userLocationRef = doc(db, 'userLocations', currentUser.uid);
        const locationDoc = await getDoc(userLocationRef);
        
        if (locationDoc.exists()) {
          const locationUpdates = {};
          if (displayName) locationUpdates.displayName = displayName;
          
          await updateDoc(userLocationRef, locationUpdates);
        }
      } catch (err) {
        console.warn("Error updating display name in userLocations:", err);
      }
      
      return true;
    } catch (error) {
      console.error("Error updating profile:", error);
      setError("Failed to update profile. Please try again.");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Update user preferences
   */
  const updatePreferences = async (newPreferences) => {
    if (!currentUser) {
      setError("You must be logged in to update preferences.");
      throw new Error("User not authenticated");
    }
    
    try {
      // Merge with existing preferences
      const updatedPreferences = {
        ...preferences,
        ...newPreferences
      };
      
      // Update in Firestore
      await updateDoc(doc(db, "userPreferences", currentUser.uid), updatedPreferences);
      
      // Update local state
      setPreferences(updatedPreferences);
      
      return true;
    } catch (error) {
      console.error("Error updating preferences:", error);
      setError("Failed to update preferences. Please try again.");
      throw error;
    }
  };
  
  /**
   * Check if user has a specific role
   */
  const hasRole = useCallback((role) => {
    return userRoles.includes(role);
  }, [userRoles]);
  
  /**
   * Check if user can perform a specific action
   */
  const canPerformAction = useCallback((action) => {
    // Define action permissions
    const actionPermissions = {
      'add-location': true, // Anyone can add locations
      'delete-location': (userId, locationOwnerId) => isAdmin || userId === locationOwnerId,
      'edit-location': (userId, locationOwnerId) => isAdmin || userId === locationOwnerId,
      'add-admin-location': () => isAdmin,
      'view-admin-location': () => isAdmin,
      'mark-location-picked-up': () => hasRole('Tow Truck'),
      'delete-user-trail': (userId, trailOwnerId) => isAdmin || userId === trailOwnerId,
      'take-screenshot': () => isAdmin
    };
    
    // Check if action exists and user has permission
    if (action in actionPermissions) {
      if (typeof actionPermissions[action] === 'function') {
        // For function-based permissions, return the function for later evaluation
        return actionPermissions[action];
      } else {
        // For boolean permissions, return directly
        return actionPermissions[action];
      }
    }
    
    // Default deny permission for unknown actions
    return false;
  }, [isAdmin, hasRole]);
  
  // Context value
  const contextValue = {
    // User data
    currentUser,
    isLoading,
    error,
    isAdmin,
    userRoles,
    profileData,
    profilePicture,
    displayName,
    preferences,
    
    // Authentication methods
    signIn,
    register,
    logOut,
    resetPassword,
    
    // Profile methods
    updateProfile,
    updatePreferences,
    
    // Permission methods
    hasRole,
    canPerformAction,
    
    // Utility
    clearError: () => setError(null)
  };
  
  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

/**
 * Custom hook to use the user context
 */
export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};

export default UserContext;