import React, { useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App.js'; // Changed back to App from AppWithBackgroundService

// ============================================================================
// 🚨 EMERGENCY LOCALSTORAGE FIX - PREVENTS JSON PARSE ERRORS
// ============================================================================

// Emergency cleanup function to remove corrupted localStorage values
function emergencyLocalStorageCleanup() {
  console.log('🚨 EMERGENCY: Cleaning up corrupted localStorage...');
  
  const keysToCheck = [
    'currentUser',
    'isAdmin', 
    'isClockedIn',
    'userTeams',
    'preserveTimeCard',
    'hasCompletedInspection',
    'clockInTime',
    'wasNavigating'
  ];
  
  keysToCheck.forEach(key => {
    try {
      const value = localStorage.getItem(key);
      
      // Check for problematic values
      if (value === 'undefined' || value === 'null' || value === '' || value === null) {
        console.log(`🧹 Removing corrupted key: ${key} (value: ${value})`);
        localStorage.removeItem(key);
      } else if (value && (key === 'currentUser' || key === 'userTeams')) {
        // Try to parse JSON values
        try {
          JSON.parse(value);
        } catch (e) {
          console.log(`🧹 Removing unparseable JSON for key: ${key}`);
          localStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.error(`Error checking localStorage key ${key}:`, error);
      localStorage.removeItem(key);
    }
  });
  
  console.log('✅ Emergency cleanup completed');
}

// Setup localStorage monitoring to prevent future corruption
function setupLocalStorageMonitor() {
  const originalSetItem = localStorage.setItem;
  
  // Override setItem to prevent storing undefined values
  localStorage.setItem = function(key, value) {
    if (value === undefined || value === 'undefined' || value === null || value === 'null') {
      console.warn(`🚨 BLOCKED: Attempted to store invalid value in localStorage key: ${key}, value: ${value}`);
      console.trace('Stack trace for invalid storage attempt:');
      return;
    }
    
    return originalSetItem.apply(this, arguments);
  };
  
  console.log('📊 localStorage monitor activated');
}

// Global error handler to catch JSON parse errors
function setupGlobalErrorHandler() {
  // Handle unhandled promise rejections (like JSON parse errors)
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message && 
        (event.reason.message.includes('not valid JSON') || 
         event.reason.message.includes('corrupted localStorage'))) {
      console.error('🚨 Caught JSON parse error, cleaning localStorage:', event.reason);
      
      // Emergency cleanup
      emergencyLocalStorageCleanup();
      
      // Prevent the error from showing in console repeatedly
      event.preventDefault();
      
      // Optional: Reload page after cleanup (uncomment if needed)
      // setTimeout(() => window.location.reload(), 100);
    }
  });
  
  // Handle regular JavaScript errors
  window.addEventListener('error', (event) => {
    if (event.error && event.error.message && 
        (event.error.message.includes('not valid JSON') || 
         event.error.message.includes('corrupted localStorage'))) {
      console.error('🚨 Caught error event with JSON parse issue:', event.error);
      emergencyLocalStorageCleanup();
      event.preventDefault();
    }
  });
  
  console.log('🛡️ Global error handlers activated');
}

// Override JSON.parse to gracefully handle undefined parsing attempts
function setupJSONParseProtection() {
  const originalJSONParse = JSON.parse;
  
  JSON.parse = function(text, reviver) {
    // Handle problematic values gracefully instead of throwing errors
    if (text === 'undefined' || text === undefined) {
      console.warn('🚨 HANDLED: Attempted to parse undefined as JSON, returning null');
      return null;
    }
    
    if (text === 'null' || text === null) {
      console.warn('🚨 HANDLED: Attempted to parse null as JSON, returning null');
      return null;
    }
    
    if (text === '') {
      console.warn('🚨 HANDLED: Attempted to parse empty string as JSON, returning null');
      return null;
    }
    
    // Handle string values that might be corrupted
    if (typeof text === 'string') {
      const trimmed = text.trim();
      if (trimmed === 'undefined' || trimmed === 'null' || trimmed === '') {
        console.warn('🚨 HANDLED: Attempted to parse corrupted string as JSON, returning null');
        return null;
      }
    }
    
    try {
      return originalJSONParse.call(this, text, reviver);
    } catch (error) {
      console.error('❌ JSON.parse failed gracefully:', { 
        text: text, 
        textType: typeof text,
        textLength: text ? text.length : 'N/A',
        error: error.message 
      });
      
      // Instead of throwing, return null and trigger cleanup
      emergencyLocalStorageCleanup();
      return null;
    }
  };
  
  console.log('🔒 JSON.parse protection activated (graceful mode)');
}

// Additional protection for common localStorage patterns
function setupLocalStorageGetItemProtection() {
  const originalGetItem = localStorage.getItem;
  
  localStorage.getItem = function(key) {
    try {
      const value = originalGetItem.call(this, key);
      
      // Check for problematic values and auto-clean them
      if (value === 'undefined' || value === 'null' || value === '') {
        console.warn(`🧹 Auto-cleaning corrupted localStorage key: ${key}`);
        localStorage.removeItem(key);
        return null;
      }
      
      return value;
    } catch (error) {
      console.error(`Error reading localStorage key ${key}:`, error);
      return null;
    }
  };
  
  console.log('🔒 localStorage.getItem protection activated');
}

// Safe JSON parse utility function
function safeJSONParse(jsonString, defaultValue = null) {
  try {
    if (!jsonString || 
        jsonString === 'undefined' || 
        jsonString === 'null' || 
        jsonString === '') {
      return defaultValue;
    }
    
    const parsed = JSON.parse(jsonString);
    return parsed;
  } catch (error) {
    console.warn('Safe JSON parse failed:', { jsonString, error: error.message });
    return defaultValue;
  }
}

// Make safeJSONParse available globally
if (typeof window !== 'undefined') {
  window.safeJSONParse = safeJSONParse;
}

// ============================================================================
// RUN ALL FIXES IMMEDIATELY
// ============================================================================

// Apply all fixes immediately when the script loads
if (typeof window !== 'undefined') {
  console.log('🚀 Applying localStorage fixes...');
  
  // 1. Clean up any existing corrupted data
  emergencyLocalStorageCleanup();
  
  // 2. Setup monitoring to prevent future issues
  setupLocalStorageMonitor();
  
  // 3. Setup global error handlers
  setupGlobalErrorHandler();
  
  // 4. Protect JSON.parse (graceful mode)
  setupJSONParseProtection();
  
  // 5. Protect localStorage.getItem
  setupLocalStorageGetItemProtection();
  
  console.log('✅ All localStorage fixes applied (graceful mode)');
}

// ============================================================================
// EXISTING CODE (UNMODIFIED)
// ============================================================================

// UNREGISTER ALL SERVICE WORKERS
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(let registration of registrations) {
      console.log('Unregistering service worker:', registration);
      registration.unregister();
    }
  });
}

// Fix for Leaflet MarkerCluster _unspiderfy error
function fixLeafletMarkerClusterUnspiderfyError() {
  if (typeof window === 'undefined' || !window.L) {
    // In server-side rendering or if Leaflet is not loaded yet
    if (typeof window !== 'undefined') {
      // If in browser, try again after a delay to catch when Leaflet loads
      setTimeout(fixLeafletMarkerClusterUnspiderfyError, 500);
    }
    return;
  }

  const L = window.L;

  // Check if MarkerClusterGroup exists and hasn't been patched yet
  if (L.MarkerClusterGroup && !L.MarkerClusterGroup._unspiderfyFixed) {
    console.log("Applying fix for MarkerCluster _unspiderfyWrapper error");

    // 1. Directly fix the _unspiderfyWrapper function that's causing the error
    if (L.MarkerClusterGroup.prototype._unspiderfyWrapper) {
      const originalUnspiderfyWrapper = L.MarkerClusterGroup.prototype._unspiderfyWrapper;
      
      L.MarkerClusterGroup.prototype._unspiderfyWrapper = function() {
        try {
          // Guard against undefined this
          if (!this || !this._unspiderfy) {
            console.warn("Prevented _unspiderfyWrapper error: this or _unspiderfy is undefined");
            return;
          }
          
          // Safely call _unspiderfy directly (skipping the wrapper logic that's causing issues)
          this._unspiderfy();
        } catch (e) {
          console.warn("Error in _unspiderfyWrapper caught and handled:", e);
        }
      };
    }

    // 2. Ensure _unspiderfy method itself is safe
    if (L.MarkerClusterGroup.prototype._unspiderfy) {
      const originalUnspiderfy = L.MarkerClusterGroup.prototype._unspiderfy;
      
      L.MarkerClusterGroup.prototype._unspiderfy = function(zoomDetails) {
        try {
          // Guard against missing _spiderfied
          if (!this._spiderfied) {
            return;
          }
          return originalUnspiderfy.call(this, zoomDetails);
        } catch (e) {
          console.warn("Error in _unspiderfy caught and handled:", e);
        }
      };
    }

    // 3. Fix event binding issues by properly binding event handlers
    const originalOn = L.MarkerClusterGroup.prototype.on;
    if (originalOn) {
      L.MarkerClusterGroup.prototype.on = function(type, fn, context) {
        if (type === 'zoomanim' && fn === this._unspiderfyZoomAnim) {
          // Specifically fix the binding of _unspiderfyZoomAnim 
          // This addresses the exact issue in the error
          return originalOn.call(this, type, fn.bind(this), context);
        }
        return originalOn.call(this, type, fn, context);
      };
    }

    // 4. Fix the Spiderfier._animate which can cause issues
    if (L.MarkerCluster && L.MarkerCluster.Spiderfier && 
        L.MarkerCluster.Spiderfier.prototype._animate) {
      const originalAnimate = L.MarkerCluster.Spiderfier.prototype._animate;
      
      L.MarkerCluster.Spiderfier.prototype._animate = function(...args) {
        try {
          // Only proceed if this is valid
          if (!this || !this._group) {
            console.warn("Prevented _animate error: invalid context");
            return;
          }
          return originalAnimate.apply(this, args);
        } catch (e) {
          console.warn("Error in Spiderfier._animate caught:", e);
        }
      };
    }

    // 5. Fix the specific method mentioned in the error (line 535)
    if (L.MarkerClusterGroup.prototype._unspiderfyZoomAnim) {
      const originalZoomAnim = L.MarkerClusterGroup.prototype._unspiderfyZoomAnim;
      
      L.MarkerClusterGroup.prototype._unspiderfyZoomAnim = function(...args) {
        try {
          // First ensure this context is valid
          if (!this || typeof this._unspiderfyWrapper !== 'function') {
            console.warn("Prevented _unspiderfyZoomAnim error: invalid context");
            return;
          }
          
          // Use try-catch when calling the problematic function
          try {
            return originalZoomAnim.apply(this, args);
          } catch (e) {
            console.warn("Error in _unspiderfyZoomAnim caught:", e);
            
            // If error occurs, try to directly unspiderfy without the wrapper
            if (typeof this._unspiderfy === 'function') {
              this._unspiderfy();
            }
          }
        } catch (outerError) {
          console.warn("Critical error in _unspiderfyZoomAnim handler:", outerError);
        }
      };
    }

    // Mark as fixed
    L.MarkerClusterGroup._unspiderfyFixed = true;
    console.log("MarkerCluster _unspiderfyWrapper fix applied successfully");
  } else if (!L.MarkerClusterGroup) {
    // If MarkerClusterGroup isn't loaded yet, try again after a delay
    setTimeout(fixLeafletMarkerClusterUnspiderfyError, 1000);
  }
}

// Run the Leaflet fix immediately
if (typeof window !== 'undefined') {
  fixLeafletMarkerClusterUnspiderfyError();
  // Also run it after a delay to catch if Leaflet loads later
  setTimeout(fixLeafletMarkerClusterUnspiderfyError, 2000);
}

// Viewport Meta Tag Fix Component
function ViewportFix() {
  useEffect(() => {
    // Force the viewport to use the device width without allowing user scaling
    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
    
    // Remove any existing viewport meta tags
    const existingMeta = document.querySelector('meta[name="viewport"]');
    if (existingMeta) {
      existingMeta.parentNode.removeChild(existingMeta);
    }
    
    document.head.appendChild(meta);
    
    // Also set CSS variables for app height
    const setAppHeight = () => {
      document.documentElement.style.setProperty('--app-height', `${window.innerHeight}px`);
    };
    
    // Set initial height
    setAppHeight();
    
    // Update on resize
    window.addEventListener('resize', setAppHeight);
    
    // Also run the Leaflet fix when DOM is loaded
    fixLeafletMarkerClusterUnspiderfyError();
    
    return () => {
      if (meta.parentNode) meta.parentNode.removeChild(meta);
      window.removeEventListener('resize', setAppHeight);
    };
  }, []);
  
  return null;
}

// LeafletPatchComponent to ensure the fix is applied at component mount time
function LeafletPatchComponent() {
  useEffect(() => {
    // Try to apply the fix when this component mounts
    fixLeafletMarkerClusterUnspiderfyError();
    
    // Also set up a MutationObserver to detect when Leaflet script is added
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes) {
          for (let i = 0; i < mutation.addedNodes.length; i++) {
            const node = mutation.addedNodes[i];
            if (node.tagName === 'SCRIPT' && 
                node.src && 
                (node.src.includes('leaflet') || node.src.includes('markercluster'))) {
              // Leaflet script detected, apply fix after a short delay
              setTimeout(fixLeafletMarkerClusterUnspiderfyError, 500);
            }
          }
        }
      });
    });
    
    // Start observing
    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });
    
    return () => {
      observer.disconnect();
    };
  }, []);
  
  return null;
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <ViewportFix />
    <LeafletPatchComponent />
    <App /> {/* Changed back to App from AppWithBackgroundService */}
  </React.StrictMode>
);