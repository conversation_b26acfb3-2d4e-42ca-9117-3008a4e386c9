import React, { useState, useCallback } from 'react';
import { 
  collection, 
  addDoc, 
  getDocs,
  query,
  where,
  serverTimestamp,
  getFirestore 
} from 'firebase/firestore';

function SimpleZipZoneCreator({ teamId, currentUser, onZoneCreated }) {
  const [zipCode, setZipCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const db = getFirestore();
  
  // Function to check if a zip code zone already exists
  const checkZoneExists = async (zipCode) => {
    const zonesRef = collection(db, 'teams', teamId, 'zones');
    const q = query(zonesRef, where("zipCode", "==", zipCode));
    const snapshot = await getDocs(q);
    return !snapshot.empty;
  };
  
  // Create a zone from zip code
  const createZone = useCallback(async (e) => {
    e.preventDefault();
    
    if (!zipCode || zipCode.length !== 5 || !/^\d+$/.test(zipCode)) {
      setError('Please enter a valid 5-digit zip code');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    setSuccess(false);
    
    try {
      // Check if zone already exists
      const exists = await checkZoneExists(zipCode);
      if (exists) {
        setError(`A zone for zip code ${zipCode} already exists`);
        setIsLoading(false);
        return;
      }
      
      // Use Nominatim (free, no API key required) to get basic info about the zip code
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?postalcode=${zipCode}&country=USA&format=json&limit=1`
      );
      
      if (!response.ok) {
        throw new Error('Failed to get information for this zip code');
      }
      
      const data = await response.json();
      
      if (!data || data.length === 0) {
        throw new Error(`Zip code ${zipCode} not found`);
      }
      
      const result = data[0];
      
      if (!result.lat || !result.lon || !result.boundingbox) {
        throw new Error('Incomplete location data for this zip code');
      }
      
      // Create a bounding box
      const bounds = {
        southWest: {
          lat: parseFloat(result.boundingbox[0]),
          lng: parseFloat(result.boundingbox[2])
        },
        northEast: {
          lat: parseFloat(result.boundingbox[1]),
          lng: parseFloat(result.boundingbox[3])
        }
      };
      
      // Prepare location name
      const locationName = result.display_name || `Zip Code ${zipCode}`;
      const zoneName = `${zipCode} - ${locationName.split(',')[0]}`;
      
      // Create zone with random color
      const colors = ['#3b82f6', '#8b5cf6', '#ef4444', '#10b981', '#f59e0b', '#ec4899'];
      const randomColor = colors[Math.floor(Math.random() * colors.length)];
      
      const newZone = {
        name: zoneName,
        color: randomColor,
        description: `Automatically created zone for ${zipCode}: ${locationName}`,
        bounds,
        zipCode,
        center: { lat: parseFloat(result.lat), lng: parseFloat(result.lon) },
        formattedAddress: locationName,
        assignedUsers: [],
        createdAt: serverTimestamp(),
        createdBy: currentUser.uid,
        teamId
      };
      
      // Add to Firestore
      const zoneRef = await addDoc(collection(db, 'teams', teamId, 'zones'), newZone);
      
      // Show success message
      setSuccess(true);
      setZipCode('');
      
      // Notify parent component
      if (onZoneCreated) {
        onZoneCreated({
          id: zoneRef.id,
          ...newZone
        });
      }
      
      // Reset success after a delay
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error creating zone:', error);
      setError(error.message || 'Failed to create zone');
    } finally {
      setIsLoading(false);
    }
  }, [zipCode, teamId, currentUser, db, onZoneCreated]);
  
  return (
    <div className="auto-create-zone">
      {error && (
        <div className="bg-red-900 bg-opacity-25 border border-red-800 text-red-100 px-4 py-2 rounded-md mb-3 text-sm">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-900 bg-opacity-25 border border-green-800 text-green-100 px-4 py-2 rounded-md mb-3 text-sm">
          Zone for {zipCode} created successfully!
        </div>
      )}
      
      <form onSubmit={createZone} className="flex">
        <input
          type="text"
          value={zipCode}
          onChange={(e) => setZipCode(e.target.value.trim())}
          className="px-3 py-2 rounded-l-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-24"
          placeholder="Zip Code"
          pattern="[0-9]{5}"
          maxLength={5}
          required
          disabled={isLoading}
        />
        <button
          type="submit"
          disabled={isLoading || !zipCode || zipCode.length !== 5 || !/^\d+$/.test(zipCode)}
          className={`px-3 py-2 rounded-r-md font-medium ${
            isLoading || !zipCode || zipCode.length !== 5 || !/^\d+$/.test(zipCode)
              ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isLoading ? (
            <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            'Create Zone'
          )}
        </button>
      </form>
    </div>
  );
}

export default SimpleZipZoneCreator;