import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Detect environment
const isLocalhost = typeof window !== 'undefined' && 
  (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

console.log(`Running in ${isLocalhost ? 'development (localhost)' : 'production'} environment`);

// Firebase configuration - separate configs for dev and prod
const firebaseConfig = isLocalhost 
  ? {
      // Development config - for localhost
      apiKey: "AIzaSyDefault-DEV",
      authDomain: "your-dev-app.firebaseapp.com",
      projectId: "your-dev-project-id",
      storageBucket: "your-dev-app.appspot.com",
      messagingSenderId: "123456789",
      appId: "1:123456789:web:abcdef-dev"
    }
  : {
      // Production config - for deployed site
      apiKey: "AIzaSyDefault",
      authDomain: "your-app.firebaseapp.com",
      projectId: "your-project-id",
      storageBucket: "your-app.appspot.com",
      messagingSenderId: "123456789",
      appId: "1:123456789:web:abcdef"
    };

// Initialize Firebase - ensure this happens only once!
let app;
try {
  // Check if Firebase has already been initialized
  if (!app) {
    app = initializeApp(firebaseConfig);
    console.log("Firebase app initialized successfully");
  }
} catch (error) {
  console.error("Error initializing Firebase:", error);
}

// Initialize services - always use the same instance
const db = getFirestore(app);
const auth = getAuth(app);

// Make Firebase available globally for debugging
if (typeof window !== 'undefined') {
  window.firebaseApp = app;
  window.firebaseDB = db;
  window.firebaseAuth = auth;
}

export { app, db, auth };