import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { doc, getDoc, updateDoc, setDoc } from 'firebase/firestore';
import { db } from '../pages/firebase.js';
import { useAuth } from '../contexts/AuthContext.js';

function Profile() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [profileData, setProfileData] = useState({
    displayName: '',
    jobTitle: '',
    vehicle : '',
    location: '',
    notes: '',
    phoneNumber: '',
    todo: [],
    tags: []
  });
  const [availableTags, setAvailableTags] = useState([]);
  const [newTask, setNewTask] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [completedTasks, setCompletedTasks] = useState([]);
  const [isTagDropdownOpen, setIsTagDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  
  // Determine if viewing own profile or someone else's
  const userId = params.userId || currentUser?.uid;
  const isOwnProfile = currentUser?.uid === userId;
  const canEditAll = isAdmin; // Only admins can edit all fields
  const canEditPhoto = isOwnProfile; // User can edit their own photo

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsTagDropdownOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch user profile and available tags
  useEffect(() => {
    async function fetchProfileAndTags() {
      if (!userId) return;
      
      try {
        // Fetch user profile
        const profileRef = doc(db, "userProfiles", userId);
        const profileDoc = await getDoc(profileRef);
        
        if (profileDoc.exists()) {
          const data = profileDoc.data();
          setProfileData(data);
          if (data.photoBase64) {
            setImagePreview(data.photoBase64);
          }
          
          // Initialize completed tasks array if it exists
          if (data.completedTasks) {
            setCompletedTasks(data.completedTasks);
          }
        } else {
          // Create a default profile document if it doesn't exist
          const defaultProfile = {
            displayName: '',
            jobTitle: '',
            vehicle: '',
            location: '',
            notes: '',
            phoneNumber: '',
            todo: [],
            completedTasks: [],
            tags: [],
            createdAt: new Date().toISOString()
          };
          
          await setDoc(profileRef, defaultProfile);
          setProfileData(defaultProfile);
        }
        
        // Fetch available system tags
        if (isAdmin) {
          const tagsRef = doc(db, "settings", "systemTags");
          const tagsDoc = await getDoc(tagsRef);
          
          if (tagsDoc.exists() && tagsDoc.data().tags) {
            setAvailableTags(tagsDoc.data().tags);
          }
        }
      } catch (error) {
        console.error("Error fetching profile:", error);
        setError("Failed to load profile data.");
      } finally {
        setLoading(false);
      }
    }
    
    fetchProfileAndTags();
  }, [userId, currentUser, isAdmin]);

  // Compress and convert image to base64
  const compressImage = (file, maxWidth = 400, maxHeight = 400) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (event) => {
        const img = new Image();
        img.src = event.target.result;
        img.onload = () => {
          const canvas = document.createElement('canvas');
          let width = img.width;
          let height = img.height;
          
          // Calculate new dimensions to maintain aspect ratio
          if (width > height) {
            if (width > maxWidth) {
              height = Math.round((height * maxWidth) / width);
              width = maxWidth;
            }
          } else {
            if (height > maxHeight) {
              width = Math.round((width * maxHeight) / height);
              height = maxHeight;
            }
          }
          
          canvas.width = width;
          canvas.height = height;
          
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);
          
          // Get the base64 string
          const base64 = canvas.toDataURL('image/jpeg', 0.8); // 0.8 quality for JPEG format
          resolve(base64);
        };
        img.onerror = error => reject(error);
      };
      reader.onerror = error => reject(error);
    });
  };

  // Handle image selection
  const handleImageChange = async (e) => {
    if (e.target.files[0]) {
      const file = e.target.files[0];
      try {
        // Show preview immediately
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);
        
        // Compress image in background
        const compressedBase64 = await compressImage(file);
        setProfileImage(compressedBase64);
      } catch (error) {
        console.error("Error processing image:", error);
        setError("Failed to process image. Please try a different file.");
      }
    }
  };

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setProfileData({
      ...profileData,
      [name]: value
    });
  };

  // Handle tag selection
  const handleTagSelect = (tag) => {
    // Check if tag is already assigned
    const tagExists = profileData.tags && profileData.tags.some(t => 
      t.name === tag.name && t.color === tag.color
    );
    
    if (!tagExists) {
      // Add the tag to the user's tags
      const updatedTags = [...(profileData.tags || []), tag];
      
      // Update profile data
      setProfileData({
        ...profileData,
        tags: updatedTags
      });
      
      // Save to database
      saveTagsToDatabase(updatedTags);
    }
    
    // Close dropdown
    setIsTagDropdownOpen(false);
  };

  // Handle tag removal
  const handleRemoveTag = (tagToRemove) => {
    const updatedTags = profileData.tags.filter(tag => 
      !(tag.name === tagToRemove.name && tag.color === tagToRemove.color)
    );
    
    // Update profile data
    setProfileData({
      ...profileData,
      tags: updatedTags
    });
    
    // Save to database
    saveTagsToDatabase(updatedTags);
  };

  // Save tags to database
  const saveTagsToDatabase = async (tags) => {
    if (!isAdmin) return;
    
    try {
      setSaving(true);
      
      // Update only the tags in Firestore
      const profileRef = doc(db, "userProfiles", userId);
      await updateDoc(profileRef, {
        tags: tags,
        updatedAt: new Date().toISOString()
      });
      
      setSuccess("Tags updated successfully!");
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (error) {
      console.error("Error updating tags:", error);
      setError("Failed to update tags. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Add task
  const handleAddTask = async () => {
    if (newTask.trim() && !profileData.todo.includes(newTask.trim())) {
      const updatedTodoList = [...(profileData.todo || []), newTask.trim()];
      
      // Update local state
      setProfileData({
        ...profileData,
        todo: updatedTodoList
      });
      setNewTask('');
      
      try {
        setSaving(true);
        
        // Update in Firestore
        const profileRef = doc(db, "userProfiles", userId);
        await updateDoc(profileRef, {
          todo: updatedTodoList,
          updatedAt: new Date().toISOString()
        });
        
        setSuccess("Task added successfully!");
        
        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess('');
        }, 3000);
      } catch (error) {
        console.error("Error adding task:", error);
        setError("Failed to add task. Please try again.");
      } finally {
        setSaving(false);
      }
    }
  };

  // Remove task
  const handleRemoveTask = async (taskToRemove) => {
    const updatedTodoList = (profileData.todo || []).filter(task => task !== taskToRemove);
    
    // Update local state
    setProfileData({
      ...profileData,
      todo: updatedTodoList
    });
    
    try {
      setSaving(true);
      
      // Update in Firestore
      const profileRef = doc(db, "userProfiles", userId);
      await updateDoc(profileRef, {
        todo: updatedTodoList,
        updatedAt: new Date().toISOString()
      });
      
      setSuccess("Task removed successfully!");
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (error) {
      console.error("Error removing task:", error);
      setError("Failed to remove task. Please try again.");
    } finally {
      setSaving(false);
    }
  };
  
  // Toggle task completion
  const handleToggleTaskCompletion = async (task) => {
    // Check if task is already in completed tasks
    const isCompleted = completedTasks.includes(task);
    
    // Create updated array
    const updatedCompletedTasks = isCompleted
      ? completedTasks.filter(t => t !== task)
      : [...completedTasks, task];
    
    // Update local state
    setCompletedTasks(updatedCompletedTasks);
    
    try {
      setSaving(true);
      
      // Update in Firestore
      const profileRef = doc(db, "userProfiles", userId);
      await updateDoc(profileRef, {
        completedTasks: updatedCompletedTasks,
        updatedAt: new Date().toISOString()
      });
      
      const statusMessage = isCompleted ? "Task marked as incomplete" : "Task marked as complete";
      setSuccess(statusMessage);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (error) {
      console.error("Error updating task completion status:", error);
      setError("Failed to update task status. Please try again.");
      // Revert the local state change if the database update fails
      setCompletedTasks(isCompleted ? [...completedTasks] : completedTasks.filter(t => t !== task));
    } finally {
      setSaving(false);
    }
  };
  
  // Mark task as complete and remove from todo list
  const handleCompleteTask = async (task) => {
    try {
      setSaving(true);
      
      // Remove the task from the todo list
      const updatedTodoList = (profileData.todo || []).filter(t => t !== task);
      
      // Update profile data state
      setProfileData({
        ...profileData,
        todo: updatedTodoList
      });
      
      // Update in Firestore
      const profileRef = doc(db, "userProfiles", userId);
      await updateDoc(profileRef, {
        todo: updatedTodoList,
        updatedAt: new Date().toISOString()
      });
      
      setSuccess(`Task "${task}" marked as complete and removed!`);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (error) {
      console.error("Error completing task:", error);
      setError("Failed to complete task. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Save profile picture only
  const handleSaveProfilePicture = async () => {
    if (!profileImage) return;
    
    setError('');
    setSuccess('');
    setSaving(true);
    
    try {
      // Update only the photoBase64 in Firestore
      const profileRef = doc(db, "userProfiles", userId);
      
      // First check if the document exists
      const docSnap = await getDoc(profileRef);
      
      if (docSnap.exists()) {
        // Update existing document
        await updateDoc(profileRef, {
          photoBase64: profileImage,
          updatedAt: new Date().toISOString()
        });
      } else {
        // Create new document with photoBase64
        await setDoc(profileRef, {
          photoBase64: profileImage,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          displayName: '',
          jobTitle: '',
          vehicle: '',
          location: '',
          notes: '',
          phoneNumber: '',
          todo: [],
          completedTasks: [],
          tags: []
        });
      }
      
      setSuccess("Profile picture updated successfully!");
      // Update local state with the saved photo
      setProfileData({
        ...profileData,
        photoBase64: profileImage
      });
      setProfileImage(null); // Reset the file input
    } catch (error) {
      console.error("Error updating profile picture:", error);
      setError("Failed to update profile picture. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Save all profile data (admin only)
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setSaving(true);
    
    try {
      let updatedProfile = { ...profileData };
      
      // Ensure all required fields exist to prevent undefined errors
      const requiredFields = ['displayName', 'jobTitle', 'vehicle', 'location', 'notes', 'phoneNumber', 'todo', 'tags'];
      requiredFields.forEach(field => {
        if (updatedProfile[field] === undefined) {
          if (field === 'todo' || field === 'tags') {
            updatedProfile[field] = [];
          } else {
            updatedProfile[field] = '';
          }
        }
      });
      
      // Include completed tasks
      updatedProfile.completedTasks = completedTasks;
      
      // Include compressed image if available
      if (profileImage) {
        updatedProfile.photoBase64 = profileImage;
      }
      
      // Add timestamp
      updatedProfile.updatedAt = new Date().toISOString();
      
      // Update profile in Firestore
      const profileRef = doc(db, "userProfiles", userId);
      
      // First check if the document exists
      const docSnap = await getDoc(profileRef);
      
      if (docSnap.exists()) {
        await updateDoc(profileRef, updatedProfile);
      } else {
        updatedProfile.createdAt = new Date().toISOString();
        await setDoc(profileRef, updatedProfile);
      }
      
      setSuccess("Profile updated successfully!");
      // Update local state
      setProfileData(updatedProfile);
      setProfileImage(null); // Reset the file input
    } catch (error) {
      console.error("Error updating profile:", error);
      setError("Failed to update profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Function to determine text color based on background color
  const getTextColor = (hexColor) => {
    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    
    // Calculate brightness (YIQ formula)
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    
    return yiq >= 150 ? '#000000' : '#FFFFFF';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex justify-center items-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-3 text-gray-300">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Top Navigation Bar */}
      <nav className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 shadow-md border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">NWRepo</h1>
              </div>
              <div className="ml-6 flex space-x-4">
                <button onClick={() => navigate('/dashboard')} className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                  Dashboard
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>
      
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">
              {isOwnProfile ? "My Profile" : `${profileData.displayName || "User"}'s Profile`}
            </h1>
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-gray-800 hover:bg-gray-700 text-gray-300 font-medium py-2 px-4 rounded-md flex items-center border border-gray-700"
            >
              <span>← Back to Dashboard</span>
            </button>
          </div>
          
          {error && (
            <div className="mt-4 bg-red-900 bg-opacity-75 text-red-100 px-4 py-3 rounded-md">
              <span>{error}</span>
              <button 
                className="float-right text-red-200 hover:text-white"
                onClick={() => setError(null)}
              >
                &times;
              </button>
            </div>
          )}
          
          {success && (
            <div className="mt-4 bg-green-900 bg-opacity-75 text-green-100 px-4 py-3 rounded-md">
              <span>{success}</span>
              <button 
                className="float-right text-green-200 hover:text-white"
                onClick={() => setSuccess(null)}
              >
                &times;
              </button>
            </div>
          )}
          
          <div className="bg-gray-800 shadow-md rounded-lg border border-gray-700 overflow-hidden mb-6">
            {/* Profile Header with Picture and Tags */}
            <div className="px-4 py-5 sm:p-6 border-b border-gray-700">
              <div className="flex flex-col md:flex-row md:items-center">
                {/* Profile Picture */}
                <div className="flex flex-col items-center md:mr-6">
                  <div className="w-32 h-32 rounded-full overflow-hidden bg-gray-700 mb-4 border border-gray-600">
                    {imagePreview ? (
                      <img 
                        src={imagePreview} 
                        alt="Profile" 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    )}
                  </div>
                  
                  {canEditPhoto && (
                    <div className="flex flex-col items-center">
                      <label className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-4 py-2 rounded-md cursor-pointer mb-3 shadow-md">
                        <span>Change Picture</span>
                        <input 
                          type="file" 
                          accept="image/*" 
                          className="hidden" 
                          onChange={handleImageChange}
                        />
                      </label>
                      
                      {profileImage && (
                        <button
                          type="button"
                          onClick={handleSaveProfilePicture}
                          disabled={saving}
                          className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          {saving ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Saving...
                            </>
                          ) : 'Save Picture'}
                        </button>
                      )}
                    </div>
                  )}
                </div>
                
                {/* User Info and Tags */}
                <div className="flex-grow mt-6 md:mt-0">
                  <h2 className="text-xl font-semibold mb-2 text-gray-200">{profileData.displayName || "User"}</h2>
                  
                  <div className="flex flex-wrap mb-3">
                    {profileData.jobTitle && (
                      <div className="mr-4 mb-2">
                        <span className="font-medium text-gray-300">Job Title:</span> <span className="text-gray-200">{profileData.jobTitle}</span>
                      </div>
                    )}
                    {profileData.location && (
                      <div className="mr-4 mb-2">
                        <span className="font-medium text-gray-300">Location:</span> <span className="text-gray-200">{profileData.location}</span>
                      </div>
                    )}
                    {profileData.vehicle && (
                      <div className="mb-2">
                        <span className="font-medium text-gray-300">Vehicle #:</span> <span className="text-gray-200">{profileData.vehicle}</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Tags Section */}
                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <h3 className="text-sm font-medium text-gray-300 mr-2">Tags:</h3>
                      
                      {/* Admin Tag Selection Dropdown */}
                      {isAdmin && (
                        <div className="relative" ref={dropdownRef}>
                          <button 
                            type="button"
                            onClick={() => setIsTagDropdownOpen(!isTagDropdownOpen)}
                            className="inline-flex items-center px-3 py-1 border border-gray-600 rounded-md text-sm bg-gray-700 hover:bg-gray-600 text-gray-200"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                            </svg>
                            Add Tag
                          </button>
                          
                          {isTagDropdownOpen && (
                            <div className="absolute z-10 mt-1 w-56 bg-gray-800 rounded-md shadow-lg border border-gray-700">
                              <div className="max-h-60 overflow-y-auto p-2">
                                <div className="text-xs font-medium text-gray-400 mb-2 px-2">Available Tags</div>
                                {availableTags && availableTags.length > 0 ? (
                                  availableTags.map((tag, index) => {
                                    // Check if tag is already assigned
                                    const isAssigned = profileData.tags && profileData.tags.some(t => 
                                      t.name === tag.name && t.color === tag.color
                                    );
                                    
                                    return (
                                      <button
                                        key={index}
                                        type="button"
                                        onClick={() => !isAssigned && handleTagSelect(tag)}
                                        className={`w-full text-left px-2 py-2 rounded-md flex items-center ${
                                          isAssigned ? 'opacity-50 cursor-not-allowed bg-gray-700' : 'hover:bg-gray-700 text-gray-200'
                                        }`}
                                        disabled={isAssigned}
                                      >
                                        <span 
                                          className="w-4 h-4 rounded-full mr-2"
                                          style={{ backgroundColor: tag.color }}
                                        ></span>
                                        <span>{tag.name}</span>
                                        {isAssigned && (
                                          <span className="ml-auto text-xs text-gray-500">Already added</span>
                                        )}
                                      </button>
                                    );
                                  })
                                ) : (
                                  <div className="px-2 py-2 text-sm text-gray-400">
                                    No tags available. Create tags in Tag Management.
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {/* Display assigned tags */}
                    <div className="flex flex-wrap gap-2">
                      {profileData.tags && profileData.tags.length > 0 ? (
                        profileData.tags.map((tag, index) => (
                          <div
                            key={index}
                            className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium"
                            style={{
                              backgroundColor: tag.color,
                              color: getTextColor(tag.color)
                            }}
                          >
                            <span>{tag.name}</span>
                            {isAdmin && (
                              <button
                                type="button"
                                onClick={() => handleRemoveTag(tag)}
                                className="ml-1 focus:outline-none"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            )}
                          </div>
                        ))
                      ) : (
                        <span className="text-gray-500 text-sm italic">No tags assigned</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="px-4 py-5 sm:p-6">
                <div className="grid grid-cols-1 gap-6">
                  {/* Basic Info */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-200 mb-3">Basic Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="displayName" className="block text-sm font-medium text-gray-300 mb-1">
                          Full Name
                        </label>
                        {canEditAll ? (
                          <input
                            type="text"
                            name="displayName"
                            id="displayName"
                            value={profileData.displayName || ''}
                            onChange={handleChange}
                            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                          />
                        ) : (
<p className="text-gray-200 py-2">{profileData.displayName || "Not specified"}</p>
                        )}
                      </div>
                      
                      <div>
                        <label htmlFor="jobTitle" className="block text-sm font-medium text-gray-300 mb-1">
                          Job Title
                        </label>
                        {canEditAll ? (
                          <input
                            type="text"
                            name="jobTitle"
                            id="jobTitle"
                            value={profileData.jobTitle || ''}
                            onChange={handleChange}
                            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                          />
                        ) : (
                          <p className="text-gray-200 py-2">{profileData.jobTitle || "Not specified"}</p>
                        )}
                      </div>
                      
                      <div>
                        <label htmlFor="vehicle" className="block text-sm font-medium text-gray-300 mb-1">
                          Vehicle #
                        </label>
                        {canEditAll ? (
                          <input
                            type="text"
                            name="vehicle"
                            id="vehicle"
                            value={profileData.vehicle || ''}
                            onChange={handleChange}
                            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                          />
                        ) : (
                          <p className="text-gray-200 py-2">{profileData.vehicle || "Not specified"}</p>
                        )}
                      </div>
                      
                      <div>
                        <label htmlFor="location" className="block text-sm font-medium text-gray-300 mb-1">
                          Location
                        </label>
                        {canEditAll ? (
                          <input
                            type="text"
                            name="location"
                            id="location"
                            value={profileData.location || ''}
                            onChange={handleChange}
                            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                          />
                        ) : (
                          <p className="text-gray-200 py-2">{profileData.location || "Not specified"}</p>
                        )}
                      </div>
                      
                      <div className="md:col-span-2">
                        <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-300 mb-1">
                          Phone Number
                        </label>
                        {canEditAll ? (
                          <input
                            type="tel"
                            name="phoneNumber"
                            id="phoneNumber"
                            value={profileData.phoneNumber || ''}
                            onChange={handleChange}
                            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                          />
                        ) : (
                          <p className="text-gray-200 py-2">{profileData.phoneNumber || "Not specified"}</p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Bio */}
                  <div>
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-300 mb-1">
                      Notes
                    </label>
                    {canEditAll ? (
                      <textarea
                        name="notes"
                        id="notes"
                        rows="4"
                        value={profileData.notes || ''}
                        onChange={handleChange}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="Add general notes about this contact here..."
                      ></textarea>
                    ) : (
                      <p className="text-gray-200 py-2 whitespace-pre-line">{profileData.notes || "No notes provided."}</p>
                    )}
                  </div>
                  
                  {/* Tasks */}
                  <div>
                    <label className="block text-lg font-medium text-gray-200 mb-3">
                      To Do List
                    </label>
                    
                    {/* Task List */}
                    <div className="mb-4">
                      {profileData.todo && profileData.todo.length > 0 ? (
                        <div className="space-y-3">
                          {profileData.todo.map((task, index) => (
                            <div 
                              key={index} 
                              className="flex items-center p-3 border border-gray-700 rounded-md shadow-sm bg-gray-750"
                            >
                              <input
                                type="checkbox"
                                id={`task-${index}`}
                                checked={completedTasks.includes(task)}
                                onChange={() => handleToggleTaskCompletion(task)}
                                className="h-5 w-5 text-red-600 focus:ring-red-500 bg-gray-700 border-gray-600 rounded"
                              />
                              <label 
                                htmlFor={`task-${index}`}
                                className={`ml-3 text-xl font-medium flex-grow ${
                                  completedTasks.includes(task) ? 'text-red-400 line-through' : 'text-red-500'
                                }`}
                              >
                                {task}
                              </label>
                              
                              {canEditAll && (
                                <div className="flex space-x-2">
                                  {completedTasks.includes(task) && (
                                    <button 
                                      type="button"
                                      onClick={() => handleCompleteTask(task)}
                                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                    >
                                      Complete
                                    </button>
                                  )}
                                  <button 
                                    type="button"
                                    onClick={() => handleRemoveTask(task)}
                                    className="text-gray-400 hover:text-red-400"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                  </button>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">No tasks added yet.</p>
                      )}
                    </div>
                    
                    {/* Add Task Input */}
                    {canEditAll && (
                      <div className="flex">
                        <input
                          type="text"
                          value={newTask}
                          onChange={(e) => setNewTask(e.target.value)}
                          className="flex-grow px-3 py-2 bg-gray-700 border border-gray-600 rounded-l-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-white"
                          placeholder="Add a new task"
                          onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTask())}
                        />
                        <button
                          type="button"
                          onClick={handleAddTask}
                          className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-4 py-2 rounded-r-md"
                        >
                          Add Task
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              {canEditAll && (
                <div className="px-4 py-3 bg-gray-750 border-t border-gray-700 text-right sm:px-6">
                  <button
                    type="submit"
                    disabled={saving}
                    className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {saving ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Saving...
                      </>
                    ) : 'Save Full Profile'}
                  </button>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Profile;