import React, { useState, useEffect } from 'react';
import { 
  collection, 
  getDocs, 
  addDoc, 
  setDoc,
  updateDoc, 
  getDoc,
  deleteDoc,
  doc, 
  query, 
  where, 
  orderBy, 
  serverTimestamp,
  onSnapshot // ⭐ Added for real-time updates
} from 'firebase/firestore';
import { db } from './firebase';
import { useAuth } from '../contexts/AuthContext';

// Import utility functions and constants
import { 
  STATUS_MAPPING, 
  formatUserDisplayName, 
  geocodeAddress, 
  generateMultipleCarViews,
  prepareOrderData
} from './utility-functions';

// Import UI components
import { 
  OrderStats, 
  Toolbar, 
  Alert, 
  LoadingSpinner, 
  NoOrdersMessage 
} from './ui-components';

// Import specialized components
import OrderForm from './OrderForm';
import OrderCard from './OrderCard';

function Orders() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [successMessage, setSuccessMessage] = useState(null);
  const [isGeocodingInProgress, setIsGeocodingInProgress] = useState(false);
  const [currentTeamId, setCurrentTeamId] = useState('');
  
  // Use the auth context to get the current user
  const { currentUser } = useAuth();
  
  // Get current team ID from localStorage or user preferences
  useEffect(() => {
    const storedTeamId = localStorage.getItem('lastUsedTeamId');
    if (storedTeamId) {
      setCurrentTeamId(storedTeamId);
    }
  }, []);
  
  // ⭐ UPDATED: Real-time orders listener instead of fetchOrders function
  useEffect(() => {
    if (!db) {
      setOrders([]);
      return;
    }

    console.log('🔥 Setting up real-time orders listener...');
    setLoading(true);

    try {
      // Build query based on selection
      let ordersQuery;
      const ordersRef = collection(db, 'orders');
      
      if (selectedFilter === 'all') {
        ordersQuery = query(ordersRef, orderBy('createdAt', 'desc'));
      } else {
        ordersQuery = query(
          ordersRef, 
          where('status', '==', selectedFilter),
          orderBy('createdAt', 'desc')
        );
      }

      // ⭐ Use onSnapshot for real-time updates instead of getDocs
      const unsubscribe = onSnapshot(ordersQuery, 
        (querySnapshot) => {
          const ordersData = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          
          console.log(`📦 Real-time update: ${ordersData.length} orders loaded`);
          setOrders(ordersData);
          setLoading(false);
          setError(null);
        },
        (err) => {
          console.error("❌ Error in real-time orders listener:", err);
          setError("Failed to load orders. Please try again.");
          setLoading(false);
        }
      );

      // Return cleanup function
      return () => {
        console.log('🧹 Cleaning up orders listener');
        unsubscribe();
      };

    } catch (err) {
      console.error("❌ Error setting up orders listener:", err);
      setError("Failed to load orders. Please try again.");
      setLoading(false);
    }
  }, [selectedFilter, db]); // ⭐ Added db as dependency
  
  // Add CSS for animations to document
  useEffect(() => {
    // Add global styles for animations
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      @keyframes pulse {
        0% { box-shadow: 0 0 8px rgba(59, 130, 246, 0.3); }
        50% { box-shadow: 0 0 15px rgba(59, 130, 246, 0.5); }
        100% { box-shadow: 0 0 8px rgba(59, 130, 246, 0.3); }
      }
      
      @keyframes fadeIn {
        0% { opacity: 0; transform: translateY(10px); }
        100% { opacity: 1; transform: translateY(0); }
      }
      
      .animate-fadeIn {
        animation: fadeIn 0.5s ease-in-out;
      }
    `;
    document.head.appendChild(styleElement);
    
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  
  // Handle order creation
  const handleCreateOrder = async (orderData) => {
    try {
      setLoading(true);
      
      // First, save to orders collection
      const orderRef = await addDoc(collection(db, 'orders'), {
        ...orderData,
        teamId: currentTeamId // Add teamId to order document
      });
      console.log("Created order with ID:", orderRef.id);
      
      // Make sure we have a valid name for LocationsPanel
      const orderName = orderData.name || `${orderData.year} ${orderData.make} ${orderData.model} - ${orderData.licensePlate}`;
      
      // Also save to locations collection with appropriate format for LocationsPanel
      const locationData = {
        name: orderName,
        position: orderData.position || null, // Only use position from addresses
        status: STATUS_MAPPING[orderData.status] || 'pending',
        make: orderData.make,
        model: orderData.model,
        plateNumber: orderData.licensePlate,
        year: orderData.year,
        createdAt: orderData.createdAt,
        teamId: currentTeamId, // Add teamId to location document
        // Additional fields that might be useful for LocationsPanel
        vin: orderData.vin,
        sourceType: 'order',
        orderReference: orderRef.id
      };
      
      // Use the same ID for the locations document as the order document
      await setDoc(doc(db, 'locations', orderRef.id), locationData);
      console.log("Saved to locations collection with ID:", orderRef.id);
      
      // Success! Hide form and show message
      setShowOrderForm(false);
      setSuccessMessage("Order created successfully and added to the locations panel!");
      
      // Clear success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);
      
      // ⭐ REMOVED: fetchOrders() - real-time listener will update automatically
      
    } catch (err) {
      console.error("Error creating order:", err);
      setError(`Failed to create order: ${err.message}. Please try again.`);
      setLoading(false);
      
      // Clear error message after 5 seconds
      setTimeout(() => {
        setError(null);
      }, 5000);
    }
  };
  
  // Handle deletion of order and associated location
  const handleDeleteOrder = async (orderId) => {
    try {
      setLoading(true);
      
      // Delete from orders collection
      const orderRef = doc(db, 'orders', orderId);
      await deleteDoc(orderRef);
      console.log(`Deleted order ${orderId} from orders collection`);
      
      // Delete from locations collection
      const locationRef = doc(db, 'locations', orderId);
      try {
        // Check if document exists first
        const locationDoc = await getDoc(locationRef);
        if (locationDoc.exists()) {
          await deleteDoc(locationRef);
          console.log(`Deleted location ${orderId} from locations collection`);
        } else {
          console.log(`No location document found for ID ${orderId}, skipping deletion`);
        }
      } catch (locationError) {
        console.error(`Error checking/deleting location document: ${locationError.message}`);
        // Continue with UI update even if location deletion fails
      }
      
      // Update local state to reflect deletion
      setOrders(prevOrders => prevOrders.filter(order => order.id !== orderId));
      
      setLoading(false);
      setSuccessMessage("Order deleted successfully!");
      
      // Clear success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);
      
      // ⭐ REMOVED: fetchOrders() - real-time listener will update automatically
      
    } catch (err) {
      console.error("Error deleting order:", err);
      setError(`Failed to delete order: ${err.message}. Please try again.`);
      setLoading(false);
      
      // Clear error message after 5 seconds
      setTimeout(() => {
        setError(null);
      }, 5000);
    }
  };
  
  // Handle general updates to an order
  const handleUpdateOrder = async (orderId, data) => {
    try {
      setLoading(true);
      const orderRef = doc(db, 'orders', orderId);
      
      // Get current order data
      const orderDoc = await getDoc(orderRef);
      if (!orderDoc.exists()) {
        throw new Error(`Order with ID ${orderId} not found`);
      }
      
      const currentOrderData = orderDoc.data();
      
      // Add updatedAt timestamp and ensure teamId is included
      await updateDoc(orderRef, {
        ...data,
        teamId: currentTeamId, // Ensure teamId is updated
        updatedAt: serverTimestamp()
      });
      
      // If order name or status components change, update the locations document
      if (data.make || data.model || data.year || data.licensePlate || data.status || data.position) {
        const locationRef = doc(db, 'locations', orderId);
        const locationDoc = await getDoc(locationRef);
        
        if (locationDoc.exists()) {
          const updateData = {};
          
          // Only update fields that have changed
          if (data.make || data.model || data.year || data.licensePlate) {
            const make = data.make || currentOrderData.make;
            const model = data.model || currentOrderData.model;
            const year = data.year || currentOrderData.year;
            const licensePlate = data.licensePlate || currentOrderData.licensePlate;
            
            updateData.name = `${year} ${make} ${model} - ${licensePlate}`;
            updateData.make = make;
            updateData.model = model;
            updateData.year = year;
            updateData.plateNumber = licensePlate;
          }
          
          if (data.status) {
            updateData.status = STATUS_MAPPING[data.status] || 'pending';
          }
          
          // Update position if provided in the data
          if (data.position && data.position.lat && data.position.lng) {
            updateData.position = data.position;
          }
          
          // Always ensure teamId is included
          updateData.teamId = currentTeamId;
          
          if (Object.keys(updateData).length > 0) {
            updateData.updatedAt = serverTimestamp();
            await updateDoc(locationRef, updateData);
            console.log(`Updated location document ${orderId} with new data:`, updateData);
          }
        } else {
          // Location document doesn't exist, create it
          console.log(`Location document for order ${orderId} doesn't exist, creating it...`);
          
          const make = data.make || currentOrderData.make;
          const model = data.model || currentOrderData.model;
          const year = data.year || currentOrderData.year;
          const licensePlate = data.licensePlate || currentOrderData.licensePlate;
          const status = data.status || currentOrderData.status;
          
          // Only use position from addresses, never default values
          const position = (data.position && data.position.lat && data.position.lng) 
            ? data.position 
            : null;
            
          const locationData = {
            name: `${year} ${make} ${model} - ${licensePlate}`,
            position: position,
            status: STATUS_MAPPING[status] || 'pending',
            make: make,
            model: model,
            year: year,
            plateNumber: licensePlate,
            vin: currentOrderData.vin,
            sourceType: 'order',
            orderReference: orderId,
            teamId: currentTeamId, // Ensure teamId is included
            createdAt: currentOrderData.createdAt || serverTimestamp(),
            updatedAt: serverTimestamp()
          };
          
          await setDoc(doc(db, 'locations', orderId), locationData);
          console.log(`Created location document for order ${orderId}`);
        }
      }
      
      // Update local state to reflect changes
      setOrders(prevOrders => 
        prevOrders.map(order => {
          if (order.id === orderId) {
            return { 
              ...order, 
              ...data,
              updatedAt: new Date() // For immediate UI update
            };
          }
          return order;
        })
      );
      
      setLoading(false);
      setSuccessMessage("Order updated successfully!");
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
      
    } catch (err) {
      console.error("Error updating order:", err);
      setError(`Failed to update order: ${err.message}. Please try again.`);
      setLoading(false);
      
      // Clear error message after 5 seconds
      setTimeout(() => {
        setError(null);
      }, 5000);
    }
  };
  
  // Handle status change
  const handleStatusChange = async (orderId, isSecure) => {
    try {
      setLoading(true);
      const newStatus = isSecure ? 'secure' : 'open';
      const locationStatus = STATUS_MAPPING[newStatus] || 'pending';
      
      console.log(`Updating order ${orderId} to ${newStatus} (${locationStatus} in locations)`);
      
      // Additional data for status change
      let additionalData = {};
      
      if (isSecure) {
        // Get current timestamp
        const timestamp = new Date();
        
        // Format the user's display name using the utility function
        const userDisplayName = formatUserDisplayName(currentUser);
        
        additionalData = {
          secureTimestamp: serverTimestamp(),
          securedBy: userDisplayName
        };
        
        console.log("Secured by user:", userDisplayName);
      } else {
        // When unsecuring, we need to remove the secure data fields
        additionalData = {
          secureTimestamp: null,
          securedBy: null
        };
        
        console.log("Removing secure details as item is being marked unsecure");
      }
      
      // Update order in orders collection
      const orderRef = doc(db, 'orders', orderId);
      await updateDoc(orderRef, {
        secure: isSecure,
        status: newStatus,
        teamId: currentTeamId, // Ensure teamId is included
        updatedAt: serverTimestamp(),
        ...additionalData
      });
      
      // Check if the document exists in locations collection before updating
      const locationRef = doc(db, 'locations', orderId);
      const locationDoc = await getDoc(locationRef);
      
      if (locationDoc.exists()) {
        // Update existing location document
        await updateDoc(locationRef, {
          status: locationStatus,
          teamId: currentTeamId, // Ensure teamId is included
          updatedAt: serverTimestamp()
        });
        console.log(`Updated existing location document ${orderId} with status ${locationStatus}`);
      } else {
        // Create location document if it doesn't exist
        const orderDoc = await getDoc(orderRef);
        
        if (orderDoc.exists()) {
          const orderData = orderDoc.data();
          
          // Create a new location document with the same ID
          const locationData = {
            name: orderData.name || `${orderData.year} ${orderData.make} ${orderData.model} - ${orderData.licensePlate}`,
            position: orderData.position || null, // Only use address position
            status: locationStatus,
            make: orderData.make,
            model: orderData.model,
            plateNumber: orderData.licensePlate,
            year: orderData.year,
            teamId: currentTeamId, // Ensure teamId is included
            createdAt: orderData.createdAt || serverTimestamp(),
            updatedAt: serverTimestamp(),
            sourceType: 'order',
            orderReference: orderId
          };
          
          await setDoc(locationRef, locationData);
          console.log(`Created new location document ${orderId} with status ${locationStatus}`);
        }
      }
      
      // Create a current timestamp for immediate UI update
      const clientTimestamp = new Date();
      
      // Update local state immediately to reflect changes without refresh
      setOrders(prevOrders => 
        prevOrders.map(order => {
          if (order.id === orderId) {
            if (isSecure) {
              // Mark as secure
              return { 
                ...order, 
                secure: true, 
                status: newStatus, 
                updatedAt: clientTimestamp,
                secureTimestamp: clientTimestamp,
                securedBy: additionalData.securedBy
              };
            } else {
              // Mark as unsecure - remove secure data
              const updatedOrder = { 
                ...order, 
                secure: false, 
                status: newStatus, 
                updatedAt: clientTimestamp
              };
              
              // Remove secure fields
              delete updatedOrder.secureTimestamp;
              delete updatedOrder.securedBy;
              
              return updatedOrder;
            }
          }
          return order;
        })
      );
      
      setLoading(false);
      // Show success message
      setSuccessMessage(
        isSecure 
          ? "Vehicle marked as secure!" 
          : "Vehicle marked as not secure and returned to open orders."
      );
      
      // Clear success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);
      
    } catch (err) {
      console.error("Error updating order:", err);
      setError(`Failed to update order status: ${err.message}. Please try again.`);
      setLoading(false);
      
      // Clear error message after 5 seconds
      setTimeout(() => {
        setError(null);
      }, 5000);
    }
  };
  
  // Function to geocode all addresses for all orders
  const geocodeAllAddresses = async () => {
    if (orders.length === 0) return;
    
    setIsGeocodingInProgress(true);
    setSuccessMessage("Geocoding all addresses in background. This may take some time...");
    
    try {
      let updatedCount = 0;
      
      // Process each order sequentially to avoid rate limits
      for (const order of orders) {
        // Skip orders that already have all addresses geocoded
        if (!order.addresses?.some(addr => addr.street && (!addr.position || !addr.position.lat || !addr.position.lng))) {
          continue;
        }
        
        // Process addresses for this order
        if (order.addresses && order.addresses.length > 0) {
          const addresses = Array.isArray(order.addresses) ? order.addresses : [];
          
          // Geocode each address that doesn't have coordinates
          const geocodedAddresses = await Promise.all(
            addresses.map(async (addr) => {
              if (addr.street && (!addr.position || !addr.position.lat || !addr.position.lng)) {
                // Add a small delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 1000));
                return await geocodeAddress(addr);
              }
              return addr;
            })
          );
          
          // Determine the main order position from the first valid address
          const firstValidAddress = geocodedAddresses.find(addr => addr.position?.lat && addr.position?.lng);
          let position = null; // Don't use a default position
          
          // Only set position if we have a valid address with coordinates
          if (firstValidAddress?.position) {
            position = firstValidAddress.position;
          }
          
          // Update the order
          if (firstValidAddress || geocodedAddresses.some(addr => addr.position?.lat && addr.position?.lng)) {
            await updateDoc(doc(db, 'orders', order.id), { 
              addresses: geocodedAddresses,
              position,
              teamId: currentTeamId, // Ensure teamId is included
              updatedAt: serverTimestamp()
            });
            
            // Also update the locations collection
            const locationRef = doc(db, 'locations', order.id);
            const locationDoc = await getDoc(locationRef);
            
            if (locationDoc.exists() && position) {
              await updateDoc(locationRef, {
                position: position,
                teamId: currentTeamId, // Ensure teamId is included
                updatedAt: serverTimestamp()
              });
            }
            
            updatedCount++;
          }
        }
      }
      
      // ⭐ REMOVED: await fetchOrders() - real-time listener will update automatically
      
      setSuccessMessage(`Geocoding completed! Updated ${updatedCount} orders.`);
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (error) {
      console.error("Error geocoding all addresses:", error);
      setError("Error geocoding addresses: " + error.message);
      setTimeout(() => setError(null), 5000);
    } finally {
      setIsGeocodingInProgress(false);
    }
  };
  
  // Function to regenerate 3D images for all orders
  const regenerateAll3DImages = async () => {
    if (orders.length === 0) return;
    
    setLoading(true);
    setSuccessMessage("Regenerating 3D renders for all vehicles. This may take some time...");
    
    try {
      let updatedCount = 0;
      
      // Process each order sequentially
      for (const order of orders) {
        // Skip orders that don't have required fields
        if (!order.make || !order.model) {
          continue;
        }
        
        // Generate multiple views for the vehicle
        const generatedViews = await generateMultipleCarViews(
          order.make, 
          order.model, 
          order.year, 
          order.color
        );
        
        // Only update if we generated views
        if (generatedViews.length > 0) {
          await updateDoc(doc(db, 'orders', order.id), { 
            vehicleImage: generatedViews[0].url,
            vehicleRenderViews: generatedViews,
            teamId: currentTeamId, // Ensure teamId is included
            updatedAt: serverTimestamp()
          });
          
          updatedCount++;
        }
        
        // Add a delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // ⭐ REMOVED: await fetchOrders() - real-time listener will update automatically
      
      setSuccessMessage(`3D rendering completed! Updated ${updatedCount} vehicles.`);
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (error) {
      console.error("Error regenerating 3D images:", error);
      setError("Error generating 3D images: " + error.message);
      setTimeout(() => setError(null), 5000);
    } finally {
      setLoading(false);
    }
  };
  
  // Filter orders based on search term
  const filteredOrders = orders.filter(order => {
    const searchTermLower = searchTerm.toLowerCase();
    
    const checkAddresses = (addresses) => {
      if (!addresses) return false;
      
      return addresses.some(addr => {
        if (typeof addr === 'string') {
          return addr.toLowerCase().includes(searchTermLower);
        }
        // For structured address objects
        return (
          (addr.street && addr.street.toLowerCase().includes(searchTermLower)) ||
          (addr.city && addr.city.toLowerCase().includes(searchTermLower)) ||
          (addr.state && addr.state.toLowerCase().includes(searchTermLower)) ||
          (addr.zip && addr.zip.toLowerCase().includes(searchTermLower))
        );
      });
    };
    
    return (
      order.make?.toLowerCase().includes(searchTermLower) ||
      order.model?.toLowerCase().includes(searchTermLower) ||
      order.year?.toLowerCase().includes(searchTermLower) ||
      order.vin?.toLowerCase().includes(searchTermLower) ||
      order.licensePlate?.toLowerCase().includes(searchTermLower) ||
      order.caseNumber?.toLowerCase().includes(searchTermLower) ||
      order.customerName?.toLowerCase().includes(searchTermLower) || 
      order.color?.toLowerCase().includes(searchTermLower) ||
      checkAddresses(order.addresses)
    );
  });
  
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 p-4 shadow-lg border-b border-gray-700">
        <div className="container mx-auto">
          <Toolbar 
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            selectedFilter={selectedFilter}
            onFilterChange={setSelectedFilter}
            onAddOrder={() => setShowOrderForm(true)}
            onGeocodeAll={geocodeAllAddresses}
            onRegenerate3D={regenerateAll3DImages}
            isGeocodingInProgress={isGeocodingInProgress}
            isLoading={loading}
          />
          
          {/* Team ID display */}
          {currentTeamId && (
            <div className="mt-2 text-xs text-blue-400">
              Current Team: {currentTeamId}
            </div>
          )}
          
          {/* Statistics dashboard */}
          <OrderStats orders={orders} />
        </div>
      </div>
      
      {/* Main Content */}
      <div className="container mx-auto p-4">
        {/* Current User Info (debugging purposes) */}
        {currentUser && (
          <div className="mb-4 bg-gray-800 rounded-lg p-3 text-xs border border-gray-700">
            <span className="text-blue-400">Logged in as:</span> {formatUserDisplayName(currentUser)}
          </div>
        )}
        
        {/* Success message */}
        <Alert 
          type="success" 
          message={successMessage} 
          onClose={() => setSuccessMessage(null)} 
        />
        
        {/* Form for adding a new order */}
        {showOrderForm && (
          <div className="mb-6 animate-fadeIn">
            <OrderForm
              onSubmit={handleCreateOrder}
              onCancel={() => setShowOrderForm(false)}
            />
          </div>
        )}
        
        {/* Error message */}
        <Alert 
          type="error" 
          message={error} 
          onClose={() => setError(null)} 
        />
        
        {/* Loading indicator */}
        {loading && <LoadingSpinner />}
        
        {/* No orders message */}
        {!loading && filteredOrders.length === 0 && !error && (
          <NoOrdersMessage 
            searchTerm={searchTerm} 
            onAddOrder={() => setShowOrderForm(true)} 
          />
        )}
        
        {/* Orders List */}
        {!loading && filteredOrders.length > 0 && (
          <div className="space-y-4">
          <div className="text-sm text-gray-400 mb-2">
              Showing {filteredOrders.length} of {orders.length} orders
            </div>
            
            {filteredOrders.map(order => (
              <OrderCard
                key={order.id}
                order={order}
                onStatusChange={handleStatusChange}
                onDeleteOrder={handleDeleteOrder}
                onUpdateOrder={handleUpdateOrder}
                currentUser={currentUser}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default Orders;