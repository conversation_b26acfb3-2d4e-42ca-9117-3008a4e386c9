{"name": "nwr", "version": "1.0.0", "description": "Vehicle Tracker with Slack Bot Integration", "main": "postcss.config.js", "repository": {"type": "git", "url": "git+https://github.com/MonkeyzSkinz/NWR.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/MonkeyzSkinz/NWR/issues"}, "homepage": ".", "dependencies": {"@react-google-maps/api": "^2.20.6", "@slack/bolt": "^4.4.0", "@slack/web-api": "^7.9.3", "@tailwindcss/postcss": "^4.0.12", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "axios": "^1.10.0", "body-parser": "^2.2.0", "chart.js": "^4.4.9", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.6.0", "express": "^5.1.0", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.25", "leaflet": "^1.9.4", "leaflet-contextmenu": "^1.4.0", "leaflet-rotate": "^0.2.8", "leaflet-routing-machine": "^3.2.12", "leaflet.markercluster": "^1.5.3", "mapbox-gl": "^3.10.0", "node-cron": "^4.1.1", "nosleep.js": "^0.12.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.3", "recharts": "^2.15.1", "tailwindcss": "^3.3.5"}, "devDependencies": {"autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "html-webpack-plugin": "^5.6.3", "nodemon": "^3.0.1", "postcss": "^8.5.3", "react-scripts": "^5.0.1"}, "optionalDependencies": {"canvas": "^2.11.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "bot": "node src/components/slack-vehicle-bot.js", "bot:dev": "nodemon src/components/slack-vehicle-bot.js", "server": "node server.js", "server:dev": "nodemon server.js", "dev": "concurrently \"npm start\" \"npm run bot:dev\"", "install:canvas": "npm install canvas --build-from-source"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.0.0"}}