import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext.js';
import { getFirestore, collection, addDoc, updateDoc, doc, getDoc, serverTimestamp, query, where, getDocs } from 'firebase/firestore';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

function Recovery({ 
  // Modal mode props
  isModal = false,
  vehicleData = null,
  onComplete = null,
  onCancel = null,
  // Standard props (for modal mode these come from parent)
  db: modalDb = null,
  user: modalUser = null,
  selectedWeek: modalSelectedWeek = null
}) {
  const { currentUser, userRole } = useAuth();
  const navigate = useNavigate();
  const dbInstance = modalDb || getFirestore();
  const storage = getStorage();
  const fileInputRef = useRef();

  // Use modal props if in modal mode, otherwise use auth context
  const effectiveUser = isModal ? modalUser : currentUser;
  const effectiveDb = isModal ? modalDb : dbInstance;

  // Generate a unique storage key for this recovery session
  const storageKey = `recovery_form_${effectiveUser?.uid || effectiveUser?.id || 'temp'}_${vehicleData?.id || 'manual'}`;

  // State for user profile and recovery information
  const [loading, setLoading] = useState(!isModal); // Don't show loading in modal mode
  const [saving, setSaving] = useState(false);
  const [hasTowTruckTag, setHasTowTruckTag] = useState(isModal ? true : false); // Assume true in modal mode
  const [recoveryCaseId, setRecoveryCaseId] = useState('');
  const [recoveryLocation, setRecoveryLocation] = useState('');
  const [vehicleInfo, setVehicleInfo] = useState({
    make: '',
    model: '',
    year: '',
    color: '',
    licensePlate: '',
    vin: '',
    assignedVin: '' // For verification
  });
  const [recoveryChecklist, setRecoveryChecklist] = useState({
    confirmOnHook: false,
    verifyVinMatch: false,
    conditionReportComplete: false,
    personalPropertyInventory: false,
    vehicleSecured: false,
    photosTaken: false,
    formsCompleted: false,
    systemUpdated: false,
    partiesNotified: false
  });
  const [vehicleCondition, setVehicleCondition] = useState({
    exterior: 'Good',
    interior: 'Good', 
    engine: 'Unknown',
    transmission: 'Unknown',
    tires: 'Good',
    damage: '',
    mileage: '',
    keys: false,
    personalItems: '',
    notes: ''
  });
  const [photos, setPhotos] = useState({
    frontView: null,
    rearView: null,
    driverSide: null,
    passengerSide: null,
    interiorFront: null,
    interiorRear: null,
    dashboard: null,
    damage: [],
    vin: null,
    licensePlate: null
  });
  const [photoURLs, setPhotoURLs] = useState({
    frontView: '',
    rearView: '',
    driverSide: '',
    passengerSide: '',
    interiorFront: '',
    interiorRear: '',
    dashboard: '',
    damage: [],
    vin: '',
    licensePlate: ''
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [currentSection, setCurrentSection] = useState(isModal ? 'vehicle-info' : 'assignments');
  const [showVinMismatchWarning, setShowVinMismatchWarning] = useState(false);
  const [repoAssignments, setRepoAssignments] = useState([]);
  const [selectedAssignment, setSelectedAssignment] = useState(null);

  // Enhanced vehicle data parsing function
  const parseVehicleData = (vehicleData) => {
    if (!vehicleData) return {};

    let make = vehicleData.make || '';
    let model = vehicleData.model || '';
    let year = vehicleData.year || '';

    // If individual fields are missing but we have a combined vehicle string, try to parse it
    if ((!make || !model || !year) && vehicleData.vehicle) {
      const vehicleParts = vehicleData.vehicle.trim().split(' ');
      
      // Try to extract year (usually 4 digits)
      const yearMatch = vehicleParts.find(part => /^\d{4}$/.test(part));
      if (yearMatch && !year) {
        year = yearMatch;
      }
      
      // If we found a year, remove it from parts for make/model parsing
      if (yearMatch) {
        const filteredParts = vehicleParts.filter(part => part !== yearMatch);
        
        // First part is usually make, rest is model
        if (filteredParts.length >= 2) {
          if (!make) make = filteredParts[0];
          if (!model) model = filteredParts.slice(1).join(' ');
        } else if (filteredParts.length === 1) {
          if (!make) make = filteredParts[0];
        }
      } else {
        // No year found, try to split into make and model
        if (vehicleParts.length >= 2) {
          if (!make) make = vehicleParts[0];
          if (!model) model = vehicleParts.slice(1).join(' ');
        } else if (vehicleParts.length === 1) {
          if (!make) make = vehicleParts[0];
        }
      }
    }

    return {
      make,
      model,
      year,
      color: vehicleData.color || '',
      licensePlate: vehicleData.plateNumber || vehicleData.licensePlate || '',
      vin: vehicleData.vin || '',
      assignedVin: vehicleData.vin || '',
      caseId: vehicleData.accountNumber || vehicleData.caseNumber || vehicleData.id || '',
      location: vehicleData.fullAddress || 
                vehicleData.address || 
                [vehicleData.city, vehicleData.state, vehicleData.zipCode].filter(Boolean).join(', ') || '',
      notes: vehicleData.notes || ''
    };
  };

  // Save form data to localStorage
  const saveFormData = () => {
    if (!isModal) return; // Only save in modal mode to prevent accidental loss

    const formData = {
      recoveryCaseId,
      recoveryLocation,
      vehicleInfo,
      recoveryChecklist,
      vehicleCondition,
      photoURLs, // Save preview URLs, not actual files
      currentSection,
      timestamp: new Date().toISOString()
    };

    try {
      localStorage.setItem(storageKey, JSON.stringify(formData));
      console.log('Form data saved to localStorage');
    } catch (error) {
      console.warn('Failed to save form data:', error);
    }
  };

  // Load form data from localStorage
  const loadFormData = () => {
    if (!isModal) return false; // Only load in modal mode

    try {
      const savedData = localStorage.getItem(storageKey);
      if (!savedData) return false;

      const formData = JSON.parse(savedData);
      
      // Check if saved data is recent (within 24 hours)
      const savedTime = new Date(formData.timestamp);
      const now = new Date();
      const hoursDiff = (now - savedTime) / (1000 * 60 * 60);
      
      if (hoursDiff > 24) {
        // Data is too old, remove it
        localStorage.removeItem(storageKey);
        return false;
      }

      // Restore form data
      setRecoveryCaseId(formData.recoveryCaseId || '');
      setRecoveryLocation(formData.recoveryLocation || '');
      setVehicleInfo(formData.vehicleInfo || vehicleInfo);
      setRecoveryChecklist(formData.recoveryChecklist || recoveryChecklist);
      setVehicleCondition(formData.vehicleCondition || vehicleCondition);
      setPhotoURLs(formData.photoURLs || photoURLs);
      setCurrentSection(formData.currentSection || 'vehicle-info');

      return true;
    } catch (error) {
      console.warn('Failed to load saved form data:', error);
      return false;
    }
  };

  // Clear saved form data
  const clearSavedFormData = () => {
    try {
      localStorage.removeItem(storageKey);
      console.log('Saved form data cleared');
    } catch (error) {
      console.warn('Failed to clear saved form data:', error);
    }
  };

  // FIXED: Enhanced VIN matching function - more flexible
  const checkVinMatch = (enteredVin, assignedVin) => {
    if (!assignedVin || !enteredVin) return true; // Allow empty VINs to pass validation
    
    const normalizedEnteredVin = enteredVin.replace(/[\s-]/g, '').toUpperCase();
    const normalizedAssignedVin = assignedVin.replace(/[\s-]/g, '').toUpperCase();
    
    // If either is empty after normalization, consider it a match
    if (!normalizedEnteredVin || !normalizedAssignedVin) return true;
    
    // Exact match
    if (normalizedEnteredVin === normalizedAssignedVin) return true;
    
    // If assigned VIN is shorter, check if entered VIN ends with it
    if (normalizedAssignedVin.length < normalizedEnteredVin.length) {
      return normalizedEnteredVin.endsWith(normalizedAssignedVin);
    }
    
    // If entered VIN is shorter, check if assigned VIN ends with it
    if (normalizedEnteredVin.length < normalizedAssignedVin.length) {
      return normalizedAssignedVin.endsWith(normalizedEnteredVin);
    }
    
    // As a last resort, check if they contain each other
    return normalizedEnteredVin.includes(normalizedAssignedVin) || 
           normalizedAssignedVin.includes(normalizedEnteredVin);
  };

  // Enhanced pre-fill form with vehicle data in modal mode
  useEffect(() => {
    if (isModal && vehicleData) {
      // First try to load saved form data
      const loadedSavedData = loadFormData();
      
      if (!loadedSavedData) {
        // No saved data, use vehicle data to pre-fill
        const parsedData = parseVehicleData(vehicleData);
        
        console.log('Original vehicle data:', vehicleData);
        console.log('Parsed vehicle data:', parsedData);

        // Pre-fill vehicle information
        setVehicleInfo({
          make: parsedData.make,
          model: parsedData.model,
          year: parsedData.year,
          color: parsedData.color,
          licensePlate: parsedData.licensePlate,
          vin: parsedData.vin,
          assignedVin: parsedData.assignedVin
        });

        // Set case ID and location
        setRecoveryCaseId(parsedData.caseId);
        setRecoveryLocation(parsedData.location);

        // Auto-confirm some checklist items that would be assumed true
        setRecoveryChecklist(prev => ({
          ...prev,
          confirmOnHook: true,
          verifyVinMatch: true // Always set to true initially since we're pre-filling
        }));

        // Pre-fill any existing notes
        if (parsedData.notes) {
          setVehicleCondition(prev => ({
            ...prev,
            notes: parsedData.notes
          }));
        }
      } else {
        console.log('Loaded saved form data from localStorage');
        setSuccessMessage('Previously entered form data has been restored.');
        setTimeout(() => setSuccessMessage(''), 5000);
      }
    }
  }, [isModal, vehicleData]);

  // Auto-save form data periodically
  useEffect(() => {
    if (!isModal) return;

    const interval = setInterval(saveFormData, 30000); // Save every 30 seconds
    
    return () => clearInterval(interval);
  }, [isModal, recoveryCaseId, recoveryLocation, vehicleInfo, recoveryChecklist, vehicleCondition, photoURLs, currentSection]);

  // Save form data when any critical field changes
  useEffect(() => {
    if (isModal && (recoveryCaseId || vehicleInfo.vin || recoveryLocation)) {
      const timeoutId = setTimeout(saveFormData, 2000); // Save after 2 seconds of inactivity
      return () => clearTimeout(timeoutId);
    }
  }, [isModal, recoveryCaseId, recoveryLocation, vehicleInfo, vehicleCondition]);

  // Check if user has the Tow Truck tag (only in full page mode)
  useEffect(() => {
    if (isModal) return; // Skip this check in modal mode

    const checkTowTruckTag = async () => {
      if (!effectiveUser) {
        setLoading(false);
        return;
      }

      try {
        const userProfileRef = doc(effectiveDb, "userProfiles", effectiveUser.uid);
        const userProfileSnap = await getDoc(userProfileRef);

        if (userProfileSnap.exists()) {
          const userData = userProfileSnap.data();
          
          // Check if user has Tow Truck tag
          const hasTowTag = userData.tags && 
            userData.tags.some(tag => 
              tag.name.toLowerCase() === "tow truck" || 
              tag.name.toLowerCase() === "recovery" ||
              tag.name.toLowerCase() === "repo");
          
          setHasTowTruckTag(hasTowTag);
          
          // If not a tow truck operator and not admin, redirect
          if (!hasTowTag && !userRole?.includes('admin')) {
            navigate('/dashboard');
          }
        }
        
        // Fetch active repo assignments
        const assignmentsQuery = query(
          collection(effectiveDb, "repoAssignments"),
          where("status", "==", "assigned"),
          where("assignedTo", "==", effectiveUser.uid)
        );
        
        const assignmentSnap = await getDocs(assignmentsQuery);
        const assignments = [];
        
        assignmentSnap.forEach((doc) => {
          assignments.push({
            id: doc.id,
            ...doc.data()
          });
        });
        
        setRepoAssignments(assignments);
      } catch (error) {
        console.error("Error checking user tags:", error);
        setErrorMessage("Failed to load user profile data.");
      } finally {
        setLoading(false);
      }
    };

    checkTowTruckTag();
  }, [effectiveUser, effectiveDb, navigate, userRole, isModal]);

  // Handle selecting an assignment
  const handleSelectAssignment = (assignment) => {
    setSelectedAssignment(assignment);
    
    // Populate form with assignment data
    setRecoveryCaseId(assignment.caseNumber || '');
    setVehicleInfo({
      ...vehicleInfo,
      make: assignment.vehicleMake || '',
      model: assignment.vehicleModel || '',
      year: assignment.vehicleYear || '',
      color: assignment.vehicleColor || '',
      licensePlate: assignment.licensePlate || '',
      assignedVin: assignment.vin || ''
    });
  };

  // FIXED: Handle VIN change & verification - more lenient validation
  const handleVinChange = (e) => {
    const enteredVin = e.target.value;
    setVehicleInfo({
      ...vehicleInfo,
      vin: enteredVin
    });
    
    // Clear any existing errors
    setFormErrors(prev => ({
      ...prev,
      vin: undefined,
      vinMismatch: undefined
    }));
    
    if (vehicleInfo.assignedVin && enteredVin) {
      // Use enhanced VIN matching function
      const vinsMatch = checkVinMatch(enteredVin, vehicleInfo.assignedVin);
      
      setShowVinMismatchWarning(!vinsMatch);
      
      // Update checklist item for VIN verification
      setRecoveryChecklist({
        ...recoveryChecklist,
        verifyVinMatch: true // Always allow to proceed, just show warning
      });
    } else {
      // If no assigned VIN or no entered VIN, no warning needed
      setShowVinMismatchWarning(false);
      setRecoveryChecklist({
        ...recoveryChecklist,
        verifyVinMatch: true
      });
    }
  };

  // Handle checklist changes - FIXED VERSION
  const handleChecklistChange = (e) => {
    const { name, checked } = e.target;
    setRecoveryChecklist(prevState => ({
      ...prevState,
      [name]: checked
    }));
  };

  // Handle form changes
  const handleVehicleInfoChange = (e) => {
    setVehicleInfo({
      ...vehicleInfo,
      [e.target.name]: e.target.value
    });
  };

  const handleConditionChange = (e) => {
    setVehicleCondition({
      ...vehicleCondition,
      [e.target.name]: e.target.value
    });
  };

  // Handle key toggle
  const handleKeyToggle = () => {
    setVehicleCondition({
      ...vehicleCondition,
      keys: !vehicleCondition.keys
    });
  };

  // Remove a single photo - NEW FUNCTION
  const removePhoto = (photoType) => {
    setPhotos({
      ...photos,
      [photoType]: null
    });
    
    setPhotoURLs({
      ...photoURLs,
      [photoType]: ''
    });
  };

  // Handle photo upload
  const handlePhotoUpload = async (e, photoType) => {
    const file = e.target.files[0];
    if (!file) return;

    // Simple validation for image files
    if (!file.type.startsWith('image/')) {
      setErrorMessage("Please upload only image files.");
      return;
    }

    // Update the photos state
    if (photoType === 'damage') {
      setPhotos({
        ...photos,
        damage: [...photos.damage, file]
      });
    } else {
      setPhotos({
        ...photos,
        [photoType]: file
      });
    }

    // Create a preview for the user
    const reader = new FileReader();
    reader.onload = (event) => {
      if (photoType === 'damage') {
        setPhotoURLs({
          ...photoURLs,
          damage: [...photoURLs.damage, event.target.result]
        });
      } else {
        setPhotoURLs({
          ...photoURLs,
          [photoType]: event.target.result
        });
      }
    };
    reader.readAsDataURL(file);

    // Check if all required photos are uploaded and update checklist
    setTimeout(() => {
      const requiredPhotoTypes = ['frontView', 'rearView', 'vin'];
      const hasAllRequiredPhotos = requiredPhotoTypes.every(type => {
        return type === 'damage' 
          ? photoURLs.damage.length > 0 
          : photoURLs[type] !== '';
      });

      setRecoveryChecklist({
        ...recoveryChecklist,
        photosTaken: hasAllRequiredPhotos
      });
    }, 100);
  };

  // Remove a damage photo
  const removeDamagePhoto = (index) => {
    const updatedDamagePhotos = [...photos.damage];
    const updatedDamageURLs = [...photoURLs.damage];
    
    updatedDamagePhotos.splice(index, 1);
    updatedDamageURLs.splice(index, 1);
    
    setPhotos({
      ...photos,
      damage: updatedDamagePhotos
    });
    
    setPhotoURLs({
      ...photoURLs,
      damage: updatedDamageURLs
    });
  };

  // Upload all photos to Firebase Storage
  const uploadPhotos = async () => {
    const photoUploadPromises = [];
    const uploadedPhotoURLs = { ...photoURLs };
    
    // Helper function to upload a single photo
    const uploadPhoto = async (file, path) => {
      if (!file) return null;
      
      const storageRef = ref(storage, path);
      await uploadBytes(storageRef, file);
      return getDownloadURL(storageRef);
    };
    
    // Upload each photo type
    for (const [photoType, photoFile] of Object.entries(photos)) {
      if (photoType === 'damage') {
        // Handle damage photos array
        const damageUrls = [];
        for (let i = 0; i < photoFile.length; i++) {
          if (photoFile[i]) {
            const path = `recoveries/${effectiveUser.uid}/${recoveryCaseId}/damage-${Date.now()}-${i}.jpg`;
            const url = await uploadPhoto(photoFile[i], path);
            if (url) damageUrls.push(url);
          }
        }
        uploadedPhotoURLs.damage = damageUrls;
      } else if (photoFile) {
        // Handle single photos
        const path = `recoveries/${effectiveUser.uid}/${recoveryCaseId}/${photoType}.jpg`;
        const url = await uploadPhoto(photoFile, path);
        if (url) uploadedPhotoURLs[photoType] = url;
      }
    }
    
    return uploadedPhotoURLs;
  };

  // FIXED: Form validation - more lenient VIN checking
  const validateForm = () => {
    const errors = {};
    
    // Required fields validation
    if (!recoveryCaseId) errors.recoveryCaseId = "Case ID is required";
    if (!recoveryLocation) errors.recoveryLocation = "Recovery location is required";
    if (!vehicleInfo.make) errors.make = "Make is required";
    if (!vehicleInfo.model) errors.model = "Model is required";
    if (!vehicleInfo.vin) errors.vin = "VIN is required";
    
    // VIN validation (usually 17 characters) - but be more flexible
    if (vehicleInfo.vin && vehicleInfo.vin.replace(/\s/g, '').length < 3) {
      errors.vin = "Please enter a valid VIN (at least 3 characters)";
    }
    
    // RELAXED VIN matching validation - only warn, don't block
    if (vehicleInfo.assignedVin && vehicleInfo.vin) {
      const vinsMatch = checkVinMatch(vehicleInfo.vin, vehicleInfo.assignedVin);
      if (!vinsMatch) {
        // Don't block submission, just set a warning
        console.warn("VIN mismatch detected but allowing submission");
        setShowVinMismatchWarning(true);
      }
    }
    
    // Required photos validation - FIXED
    const requiredPhotoTypes = ['frontView', 'rearView', 'vin'];
    requiredPhotoTypes.forEach(type => {
      if (!photoURLs[type] || photoURLs[type] === '') {
        errors[`photo_${type}`] = `${type.replace(/([A-Z])/g, ' $1').trim()} photo is required`;
      }
    });
    
    // Checklist validation - required items
    const requiredChecks = ['confirmOnHook', 'conditionReportComplete', 'personalPropertyInventory', 'vehicleSecured'];
    const missingChecks = requiredChecks.filter(check => !recoveryChecklist[check]);
    if (missingChecks.length > 0) {
      errors.checklist = "Please complete all required checklist items";
      errors.checklistItems = missingChecks;
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Submit the recovery form
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Reset messages
    setErrorMessage('');
    setSuccessMessage('');
    
    // Form validation
    if (!validateForm()) {
      setErrorMessage("Please correct the errors in the form");
      // Scroll to top to see error message
      if (!isModal) {
        window.scrollTo(0, 0);
      }
      return;
    }
    
    setSaving(true);
    
    try {
      // Upload all photos first
      const uploadedPhotoURLs = await uploadPhotos();
      
      // Create recovery data object
      const recoveryData = {
        userId: effectiveUser.uid || effectiveUser.id,
        caseId: recoveryCaseId,
        recoveryLocation,
        vehicleInfo,
        vehicleCondition,
        photoURLs: uploadedPhotoURLs,
        checklistCompleted: recoveryChecklist,
        timestamp: serverTimestamp(),
        status: 'completed',
        assignmentId: selectedAssignment?.id || null,
        // Additional data for modal mode
        originalVehicleData: isModal ? vehicleData : null,
        fromTeamVehicleTracker: isModal,
        personalPropertyHandled: recoveryChecklist.personalPropertyInventory,
        personalPropertyNotes: vehicleCondition.personalItems,
        checklist: recoveryChecklist,
        notes: vehicleCondition.notes,
        photos: uploadedPhotoURLs,
        vinMatchWarning: showVinMismatchWarning // Include VIN mismatch warning for reference
      };
      
      if (isModal && onComplete) {
        // In modal mode, pass data back to parent component
        // Clear saved form data on successful completion
        clearSavedFormData();
        onComplete(recoveryData);
      } else {
        // In full page mode, save to Firestore
        const recoveryDocRef = await addDoc(collection(effectiveDb, "recoveries"), recoveryData);
        
        // If this was from an assignment, update the assignment status
        if (selectedAssignment) {
          await updateDoc(doc(effectiveDb, "repoAssignments", selectedAssignment.id), {
            status: "recovered",
            recoveryTimestamp: serverTimestamp(),
            recoveryDocId: recoveryDocRef.id
          });
        }
        
        // Clear saved form data on successful completion
        clearSavedFormData();
        
        // Show success message
        setSuccessMessage("Recovery successfully documented!");
        setFormSubmitted(true);
        
        // Reset form
        setTimeout(() => {
          setVehicleInfo({
            make: '',
            model: '',
            year: '',
            color: '',
            licensePlate: '',
            vin: '',
            assignedVin: ''
          });
          setVehicleCondition({
            exterior: 'Good',
            interior: 'Good',
            engine: 'Unknown',
            transmission: 'Unknown',
            tires: 'Good',
            damage: '',
            mileage: '',
            keys: false,
            personalItems: '',
            notes: ''
          });
          setPhotos({
            frontView: null,
            rearView: null,
            driverSide: null,
            passengerSide: null,
            interiorFront: null,
            interiorRear: null,
            dashboard: null,
            damage: [],
            vin: null,
            licensePlate: null
          });
          setPhotoURLs({
            frontView: '',
            rearView: '',
            driverSide: '',
            passengerSide: '',
            interiorFront: '',
            interiorRear: '',
            dashboard: '',
            damage: [],
            vin: '',
            licensePlate: ''
          });
          setRecoveryChecklist({
            confirmOnHook: false,
            verifyVinMatch: false,
            conditionReportComplete: false,
            personalPropertyInventory: false,
            vehicleSecured: false,
            photosTaken: false,
            formsCompleted: false,
            systemUpdated: false,
            partiesNotified: false
          });
          setRecoveryCaseId('');
          setRecoveryLocation('');
          setSelectedAssignment(null);
          setFormSubmitted(false);
          setCurrentSection('assignments');
        }, 5000);
        
        // Scroll to top to see confirmation
        window.scrollTo(0, 0);
      }
      
    } catch (error) {
      console.error("Error saving recovery:", error);
      setErrorMessage("Failed to save recovery. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Enhanced cancel handler for modal mode
  const handleCancel = () => {
    if (isModal) {
      // Check if there's any form data to save
      const hasFormData = recoveryCaseId || vehicleInfo.vin || recoveryLocation || 
                         Object.values(photoURLs).some(url => url && url !== '');
      
      if (hasFormData) {
        if (confirm("You have unsaved changes. Do you want to save them before closing? (You can resume later)")) {
          saveFormData();
          setSuccessMessage("Form data saved. You can resume later by opening this vehicle again.");
        }
      }
      
      if (onCancel) {
        onCancel();
      }
    }
  };

  // Handle navigation between form sections
  const handleSectionChange = (section) => {
    // When moving to condition-report, update the checklist automatically
    if (section === 'condition-report') {
      setRecoveryChecklist({
        ...recoveryChecklist,
        conditionReportComplete: true
      });
    }
    
    // When moving to personal-property, update the checklist
    if (section === 'personal-property') {
      setRecoveryChecklist({
        ...recoveryChecklist,
        personalPropertyInventory: true
      });
    }
    
    // When moving to final-checklist, update the forms completed item
    if (section === 'final-checklist') {
      setRecoveryChecklist({
        ...recoveryChecklist,
        formsCompleted: true
      });
    }
    
    setCurrentSection(section);
    if (!isModal) {
      window.scrollTo(0, 0);
    }
  };

  // Helper function to render photo upload component - NEW COMPONENT
  const renderPhotoUpload = (photoType, title, isRequired = false) => {
    return (
      <div className="bg-gray-750 rounded-lg p-4 border border-gray-700">
        <h3 className="font-medium mb-2 text-white">
          {title} {isRequired && '*'}
        </h3>
        <div className="aspect-w-16 aspect-h-9 bg-gray-800 rounded-md overflow-hidden mb-2 h-32 relative">
          {photoURLs[photoType] ? (
            <>
              <img src={photoURLs[photoType]} alt={title} className="object-cover w-full h-full" />
              <button
                type="button"
                onClick={() => removePhoto(photoType)}
                className="absolute top-1 right-1 bg-red-600 rounded-full p-1 text-white hover:bg-red-700 transition-colors"
                title="Remove photo"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </>
          ) : (
            <div className="flex items-center justify-center h-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
          )}
        </div>
        <div className="flex justify-center">
          <label className={`cursor-pointer ${isModal ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'} text-white text-sm font-medium py-2 px-4 rounded flex items-center justify-center w-full transition duration-200`}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            {photoURLs[photoType] ? 'Change Photo' : 'Upload Photo'}
            <input 
              type="file" 
              accept="image/*"
              onChange={(e) => handlePhotoUpload(e, photoType)}
              className="hidden" 
            />
          </label>
        </div>
        {formErrors[`photo_${photoType}`] && <p className="text-red-500 text-xs mt-1">{formErrors[`photo_${photoType}`]}</p>}
      </div>
    );
  };

  // Loading state (only in full page mode)
  if (loading && !isModal) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Not authorized (only in full page mode)
  if (!hasTowTruckTag && !userRole?.includes('admin') && !isModal) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-6">
        <h1 className="text-xl font-bold mb-4">Not Authorized</h1>
        <p>You do not have permission to access recovery forms.</p>
        <button 
          onClick={() => navigate('/dashboard')}
          className="mt-4 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded"
        >
          Return to Dashboard
        </button>
      </div>
    );
  }

  // Modal render
  if (isModal) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-80 z-[70] flex items-center justify-center p-4" onClick={handleCancel}>
        <div className="bg-gray-900 rounded-xl w-full max-w-6xl max-h-[95vh] overflow-hidden border-2 border-green-600 shadow-2xl" onClick={(e) => e.stopPropagation()}>
          {/* Modal Header */}
          <div className="p-4 border-b border-gray-700 bg-gradient-to-r from-gray-900 to-gray-800 rounded-t-xl">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl font-bold text-green-400 flex items-center">
                  <span className="mr-2">🔒</span>
                  Vehicle Recovery Documentation
                </h2>
                {vehicleData && (
                  <p className="text-gray-300 text-sm mt-1">
                    {vehicleData.vehicle || `${vehicleData.year} ${vehicleData.make} ${vehicleData.model}`} (VIN: {vehicleData.vin}) from {vehicleData.teamMemberName}
                  </p>
                )}
                {/* Show auto-save indicator */}
                <p className="text-green-400 text-xs mt-1">
                  ✓ Form data is auto-saved every 30 seconds
                </p>
              </div>
              <button onClick={handleCancel} className="text-gray-400 hover:text-white text-2xl">
                ×
              </button>
            </div>
          </div>

          {/* Modal Content */}
          <div className="overflow-y-auto max-h-[calc(95vh-80px)]">
            {/* Form Navigation Tabs */}
            <div className="bg-gray-900 px-4 sm:px-6 border-b border-gray-700 sticky top-0 z-10">
              <nav className="flex space-x-4 overflow-x-auto py-4 hide-scrollbar">
                <button
                  onClick={() => handleSectionChange('vehicle-info')}
                  className={`px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap ${
                    currentSection === 'vehicle-info' 
                      ? 'bg-green-600 text-white' 
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  1. Vehicle Info
                </button>
                <button
                  onClick={() => handleSectionChange('photos')}
                  className={`px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap ${
                    currentSection === 'photos' 
                      ? 'bg-green-600 text-white' 
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  2. Photos
                </button>
                <button
                  onClick={() => handleSectionChange('condition-report')}
                  className={`px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap ${
                    currentSection === 'condition-report' 
                      ? 'bg-green-600 text-white' 
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  3. Condition
                </button>
                <button
                  onClick={() => handleSectionChange('personal-property')}
                  className={`px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap ${
                    currentSection === 'personal-property' 
                      ? 'bg-green-600 text-white' 
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  4. Personal Property
                </button>
                <button
                  onClick={() => handleSectionChange('final-checklist')}
                  className={`px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap ${
                    currentSection === 'final-checklist' 
                      ? 'bg-green-600 text-white' 
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  5. Verification
                </button>
              </nav>
            </div>

            {/* Messages */}
            {errorMessage && (
              <div className="mx-6 mt-4 bg-red-900 border border-red-800 text-white px-4 py-3 rounded-md flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <div>
                  <p className="font-medium">{errorMessage}</p>
                  {formErrors.checklistItems && (
                    <ul className="mt-2 list-disc list-inside text-sm">
                      {formErrors.checklistItems.map((item, index) => (
                        <li key={index}>{item.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            )}

            {successMessage && (
              <div className="mx-6 mt-4 bg-green-900 border border-green-800 text-white px-4 py-3 rounded-md flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <p className="font-medium">{successMessage}</p>
              </div>
            )}

            {/* Form Content */}
            <form onSubmit={handleSubmit} className="p-6">
              {/* Vehicle Info Section */}
              {currentSection === 'vehicle-info' && (
                <div>
                  <h2 className="text-xl font-bold mb-4 text-white">Vehicle Information</h2>
                  <p className="mb-4 text-gray-300">
                    Verify and complete the vehicle information. Fields marked with * are required.
                  </p>

                  {/* Debug info for development */}
                  {vehicleData && process.env.NODE_ENV === 'development' && (
                    <div className="mb-4 p-3 bg-blue-900 bg-opacity-30 rounded border border-blue-600">
                      <p className="text-blue-300 text-xs font-semibold mb-1">Debug Info (Dev Only):</p>
                      <p className="text-blue-200 text-xs">
                        Original: {JSON.stringify({
                          vehicle: vehicleData.vehicle,
                          make: vehicleData.make,
                          model: vehicleData.model,
                          year: vehicleData.year,
                          vin: vehicleData.vin
                        }, null, 2)}
                      </p>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Case/Recovery ID *
                        <input 
                          type="text" 
                          value={recoveryCaseId}
                          onChange={(e) => setRecoveryCaseId(e.target.value)}
                          className={`mt-1 block w-full bg-gray-700 border ${formErrors.recoveryCaseId ? 'border-red-500' : 'border-gray-600'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white`}
                          placeholder="Enter case number"
                        />
                        {formErrors.recoveryCaseId && <p className="text-red-500 text-sm mt-1">{formErrors.recoveryCaseId}</p>}
                      </label>
                    </div>
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Recovery Location *
                        <input 
                          type="text" 
                          value={recoveryLocation}
                          onChange={(e) => setRecoveryLocation(e.target.value)}
                          className={`mt-1 block w-full bg-gray-700 border ${formErrors.recoveryLocation ? 'border-red-500' : 'border-gray-600'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white`}
                          placeholder="Address or location description"
                        />
                        {formErrors.recoveryLocation && <p className="text-red-500 text-sm mt-1">{formErrors.recoveryLocation}</p>}
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Make *
                        <input 
                          type="text" 
                          name="make"
                          value={vehicleInfo.make}
                          onChange={handleVehicleInfoChange}
                          className={`mt-1 block w-full bg-gray-700 border ${formErrors.make ? 'border-red-500' : 'border-gray-600'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white`}
                          placeholder="e.g. Ford, Toyota"
                        />
                        {formErrors.make && <p className="text-red-500 text-sm mt-1">{formErrors.make}</p>}
                      </label>
                    </div>
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Model *
                        <input 
                          type="text" 
                          name="model"
                          value={vehicleInfo.model}
                          onChange={handleVehicleInfoChange}
                          className={`mt-1 block w-full bg-gray-700 border ${formErrors.model ? 'border-red-500' : 'border-gray-600'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white`}
                          placeholder="e.g. F-150, Camry"
                        />
                        {formErrors.model && <p className="text-red-500 text-sm mt-1">{formErrors.model}</p>}
                      </label>
                    </div>
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Year
                        <input 
                          type="text" 
                          name="year"
                          value={vehicleInfo.year}
                          onChange={handleVehicleInfoChange}
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                          placeholder="e.g. 2018"
                        />
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Color
                        <input 
                          type="text" 
                          name="color"
                          value={vehicleInfo.color}
                          onChange={handleVehicleInfoChange}
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                          placeholder="e.g. Red, Silver"
                        />
                      </label>
                    </div>
                    <div>
                      <label className="block text-gray-300 mb-2">
                        License Plate
                        <input 
                          type="text" 
                          name="licensePlate"
                          value={vehicleInfo.licensePlate}
                          onChange={handleVehicleInfoChange}
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                          placeholder="Enter license plate number"
                        />
                      </label>
                    </div>
                  </div>

                  <div className="mb-6">
                    <label className="block text-gray-300 mb-2">
                      VIN (Vehicle Identification Number) *
                      <input 
                        type="text" 
                        name="vin"
                        value={vehicleInfo.vin}
                        onChange={handleVinChange}
                        className={`mt-1 block w-full bg-gray-700 border ${
                          formErrors.vin ? 'border-red-500' : 'border-gray-600'
                        } rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white`}
                        placeholder="Enter vehicle VIN"
                      />
                      {formErrors.vin && <p className="text-red-500 text-sm mt-1">{formErrors.vin}</p>}
                      {showVinMismatchWarning && !formErrors.vin && (
                        <p className="text-yellow-500 text-sm mt-1">
                          ⚠️ Warning: VIN may not match exactly. Please verify this is the correct vehicle.
                        </p>
                      )}
                      {vehicleInfo.assignedVin && (
                        <p className="text-gray-400 text-sm mt-1">
                          Expected VIN: {vehicleInfo.assignedVin}
                        </p>
                      )}
                    </label>
                  </div>

                  <div className="flex justify-between mt-8">
                    <button
                      type="button"
                      onClick={handleCancel}
                      className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        // Do a simple validation before proceeding
                        if (!recoveryCaseId || !vehicleInfo.make || !vehicleInfo.model || !vehicleInfo.vin) {
                          setErrorMessage("Please fill in all required fields before proceeding.");
                          return;
                        }
                        
                        // Auto-set the On-Hook confirmation when moving to photos
                        setRecoveryChecklist({
                          ...recoveryChecklist,
                          confirmOnHook: true
                        });
                        
                        handleSectionChange('photos');
                      }}
                      className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded transition duration-200"
                    >
                      Continue to Photos
                    </button>
                  </div>
                </div>
              )}

              {/* Photos Section */}
              {currentSection === 'photos' && (
                <div>
                  <h2 className="text-xl font-bold mb-4 text-white">Vehicle Photos</h2>
                  <p className="mb-4 text-gray-300">
                    Upload clear photos of the vehicle from different angles. Photos marked with * are required.
                  </p>

                  <div className="bg-gray-750 rounded-lg p-4 mb-6 border border-gray-700">
                    <h3 className="font-medium mb-2 flex items-center text-white">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Photo Guidelines
                    </h3>
                    <ul className="list-disc list-inside text-sm text-gray-300 space-y-1">
                      <li>Take photos in good lighting conditions</li>
                      <li>Ensure all photos are clear and in focus</li>
                      <li>Capture the entire vehicle in exterior shots</li>
                      <li>Take close-ups of any existing damage</li>
                      <li>Ensure VIN photo is clearly legible</li>
                    </ul>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    {/* Required Photos */}
                    {renderPhotoUpload('frontView', 'Front View', true)}
                    {renderPhotoUpload('rearView', 'Rear View', true)}
                    {renderPhotoUpload('vin', 'VIN Photo', true)}
                    
                    {/* Optional Photos */}
                    {renderPhotoUpload('driverSide', 'Driver Side')}
                    {renderPhotoUpload('passengerSide', 'Passenger Side')}
                    {renderPhotoUpload('interiorFront', 'Interior Front')}
                    {renderPhotoUpload('interiorRear', 'Interior Rear')}
                    {renderPhotoUpload('dashboard', 'Dashboard/Odometer')}
                    {renderPhotoUpload('licensePlate', 'License Plate')}
                  </div>

                  {/* Damage Photos */}
                  <div className="mb-6">
                    <h3 className="font-medium mb-2 text-white">Damage Photos</h3>
                    <p className="text-sm text-gray-300 mb-4">
                      Upload clear photos of any damage found on the vehicle.
                    </p>
                    
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4">
                      {photoURLs.damage.map((url, index) => (
                        <div key={index} className="bg-gray-750 rounded-lg p-2 border border-gray-700 relative">
                          <div className="aspect-w-1 aspect-h-1 bg-gray-800 rounded-md overflow-hidden h-24">
                            <img src={url} alt={`Damage ${index + 1}`} className="object-cover w-full h-full" />
                          </div>
                          <button
                            type="button"
                            onClick={() => removeDamagePhoto(index)}
                            className="absolute top-1 right-1 bg-red-600 rounded-full p-1 text-white hover:bg-red-700 transition-colors"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                      ))}
                      
                      {/* Upload damage photo button */}
                      <div className="bg-gray-750 rounded-lg p-2 border border-gray-700 border-dashed">
                        <div className="aspect-w-1 aspect-h-1 bg-gray-800 rounded-md overflow-hidden flex items-center justify-center h-24">
                          <label className="cursor-pointer flex flex-col items-center justify-center w-full h-full">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            <span className="text-xs text-gray-400 mt-2">Add Photo</span>
                            <input 
                              type="file" 
                              accept="image/*"
                              onChange={(e) => handlePhotoUpload(e, 'damage')}
                              className="hidden" 
                            />
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <button
                      type="button"
                      onClick={() => handleSectionChange('vehicle-info')}
                      className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                    >
                      Back
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        // Check if required photos are uploaded - FIXED VALIDATION
                        const requiredPhotoTypes = ['frontView', 'rearView', 'vin'];
                        const missingPhotos = requiredPhotoTypes.filter(type => !photoURLs[type] || photoURLs[type] === '');
                        
                        if (missingPhotos.length > 0) {
                          setErrorMessage("Please upload all required photos before proceeding.");
                          // Set specific errors for missing photos
                          const photoErrors = {};
                          missingPhotos.forEach(type => {
                            photoErrors[`photo_${type}`] = `${type.replace(/([A-Z])/g, ' $1').trim()} photo is required`;
                          });
                          setFormErrors({...formErrors, ...photoErrors});
                          return;
                        }
                        
                        // Auto-update the photo checklist item
                        setRecoveryChecklist({
                          ...recoveryChecklist,
                          photosTaken: true
                        });
                        
                        handleSectionChange('condition-report');
                      }}
                      className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded transition duration-200"
                    >
                      Continue to Condition Report
                    </button>
                  </div>
                </div>
              )}

              {/* Condition Report Section */}
              {currentSection === 'condition-report' && (
                <div>
                  <h2 className="text-xl font-bold mb-4 text-white">Vehicle Condition Report</h2>
                  <p className="mb-4 text-gray-300">
                    Document the current condition of the vehicle. Be specific about any damage or issues.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Exterior Condition
                        <select
                          name="exterior"
                          value={vehicleCondition.exterior}
                          onChange={handleConditionChange}
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                        >
                          <option value="Excellent">Excellent</option>
                          <option value="Good">Good</option>
                          <option value="Fair">Fair</option>
                          <option value="Poor">Poor</option>
                          <option value="Very Poor">Very Poor</option>
                        </select>
                      </label>
                    </div>
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Interior Condition
                        <select
                          name="interior"
                          value={vehicleCondition.interior}
                          onChange={handleConditionChange}
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                        >
                          <option value="Excellent">Excellent</option>
                          <option value="Good">Good</option>
                          <option value="Fair">Fair</option>
                          <option value="Poor">Poor</option>
                          <option value="Very Poor">Very Poor</option>
                        </select>
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Engine Condition
                        <select
                          name="engine"
                          value={vehicleCondition.engine}
                          onChange={handleConditionChange}
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                        >
                          <option value="Unknown">Unknown</option>
                          <option value="Running">Running</option>
                          <option value="Not Running">Not Running</option>
                          <option value="Damaged">Damaged</option>
                        </select>
                      </label>
                    </div>
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Transmission Condition
                        <select
                          name="transmission"
                          value={vehicleCondition.transmission}
                          onChange={handleConditionChange}
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                        >
                          <option value="Unknown">Unknown</option>
                          <option value="Working">Working</option>
                          <option value="Issues">Has Issues</option>
                          <option value="Not Working">Not Working</option>
                        </select>
                      </label>
                    </div>
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Tire Condition
                        <select
                          name="tires"
                          value={vehicleCondition.tires}
                          onChange={handleConditionChange}
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                        >
                          <option value="Excellent">Excellent</option>
                          <option value="Good">Good</option>
                          <option value="Fair">Fair</option>
                          <option value="Poor">Poor</option>
                          <option value="Flat/Damaged">Flat/Damaged</option>
                        </select>
                      </label>
                    </div>
                  </div>

                  <div className="mb-6">
                    <label className="block text-gray-300 mb-2">
                      Detailed Damage Description (if applicable)
                      <textarea
                        name="damage"
                        value={vehicleCondition.damage}
                        onChange={handleConditionChange}
                        rows="3"
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                        placeholder="Describe any damage in detail (dents, scratches, broken parts, etc.)"
                      ></textarea>
                    </label>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                      <label className="block text-gray-300 mb-2">
                        Odometer Reading (if visible)
                        <input
                          type="text"
                          name="mileage"
                          value={vehicleCondition.mileage}
                          onChange={handleConditionChange}
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                          placeholder="Enter current mileage"
                        />
                      </label>
                    </div>
                    <div className="flex items-center">
                      <label className="flex items-center cursor-pointer mt-8">
                        <div className="relative">
                          <input 
                            type="checkbox" 
                            className="sr-only" 
                            checked={vehicleCondition.keys} 
                            onChange={handleKeyToggle}
                          />
                          <div className={`block ${vehicleCondition.keys ? 'bg-green-600' : 'bg-gray-600'} w-14 h-8 rounded-full transition-colors duration-300`}></div>
                          <div className={`dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition-transform duration-300 ${vehicleCondition.keys ? 'transform translate-x-6' : ''}`}></div>
                        </div>
                        <div className="ml-3 text-gray-300 font-medium">
                          Keys Present
                        </div>
                      </label>
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <button
                      type="button"
                      onClick={() => handleSectionChange('photos')}
                      className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                    >
                      Back
                    </button>
                    <button
                      type="button"
                      onClick={() => handleSectionChange('personal-property')}
                      className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded transition duration-200"
                    >
                      Continue to Personal Property
                    </button>
                  </div>
                </div>
              )}

              {/* Personal Property Section */}
              {currentSection === 'personal-property' && (
                <div>
                  <h2 className="text-xl font-bold mb-4 text-white">Personal Property Inventory</h2>
                  <p className="mb-4 text-gray-300">
                    Document any personal items found in the vehicle. This is a critical legal requirement.
                  </p>

                  <div className="bg-gray-750 rounded-lg p-4 mb-6 border border-gray-700">
                    <h3 className="font-medium mb-2 flex items-center text-yellow-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Important Legal Notice
                    </h3>
                    <p className="text-sm text-gray-300">
                      Personal property in the vehicle must be documented and safeguarded. Failing to properly 
                      inventory and secure personal property can result in legal liability. Be thorough and 
                      specific in your documentation.
                    </p>
                  </div>

                  <div className="mb-6">
                    <label className="block text-gray-300 mb-2">
                      Personal Items Inventory
                      <textarea
                        name="personalItems"
                        value={vehicleCondition.personalItems}
                        onChange={handleConditionChange}
                        rows="5"
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                        placeholder="List all personal items found in the vehicle (e.g., clothing, electronics, documents, tools, etc.). Write 'No personal items found' if the vehicle is empty."
                      ></textarea>
                    </label>
                  </div>

                  <div className="mb-6">
                    <label className="block text-gray-300 mb-2">
                      Additional Notes
                      <textarea
                        name="notes"
                        value={vehicleCondition.notes}
                        onChange={handleConditionChange}
                        rows="3"
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500 text-white"
                        placeholder="Add any additional notes or observations about the vehicle or recovery process"
                      ></textarea>
                    </label>
                  </div>

                  {/* Personal Property Checklist - FIXED VERSION */}
                  <div className="bg-gray-750 rounded-lg p-4 mb-6 border border-gray-700">
                    <h3 className="font-medium mb-3 text-white">Personal Property Confirmation</h3>
                    <label className="flex items-start cursor-pointer group mb-3">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="personalPropertyInventory"
                          checked={recoveryChecklist.personalPropertyInventory}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          I have thoroughly checked the vehicle for personal property and documented all items found
                        </span>
                      </div>
                    </label>
                    <label className="flex items-start cursor-pointer group">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="vehicleSecured"
                          checked={recoveryChecklist.vehicleSecured}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          I have secured the vehicle and any personal property
                        </span>
                      </div>
                    </label>
                  </div>

                  <div className="flex justify-between mt-8">
                    <button
                      type="button"
                      onClick={() => handleSectionChange('condition-report')}
                      className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                    >
                      Back
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        // Check if property inventory is checked
                        if (!recoveryChecklist.personalPropertyInventory) {
                          setErrorMessage("Please confirm you've inventoried all personal property before proceeding.");
                          return;
                        }
                        
                        // Set all system-related checklist items to true when proceeding to final verification
                        setRecoveryChecklist({
                          ...recoveryChecklist,
                          formsCompleted: true,
                          systemUpdated: true,
                          partiesNotified: true
                        });
                        
                        handleSectionChange('final-checklist');
                      }}
                      className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded transition duration-200"
                    >
                      Continue to Final Verification
                    </button>
                  </div>
                </div>
              )}

              {/* Final Checklist Section - FIXED VERSION */}
              {currentSection === 'final-checklist' && (
                <div>
                  <h2 className="text-xl font-bold mb-4 text-white">Final Verification Checklist</h2>
                  <p className="mb-4 text-gray-300">
                    Review and confirm all required steps have been completed before submitting the recovery report.
                  </p>

                  {/* VIN Mismatch Warning at top of final checklist */}
                  {showVinMismatchWarning && (
                    <div className="bg-yellow-900 bg-opacity-40 rounded-lg p-4 mb-6 border border-yellow-600">
                      <div className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-500 mt-1 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <div>
                          <h3 className="font-medium mb-2 text-yellow-400">VIN Verification Notice</h3>
                          <p className="text-sm text-yellow-200">
                            The entered VIN may not match exactly with the expected VIN. Please verify that this is the correct vehicle before proceeding. 
                            You can still complete the recovery, but this will be noted in the report.
                          </p>
                          <div className="mt-2 text-xs text-yellow-300">
                            <div>Entered VIN: {vehicleInfo.vin}</div>
                            <div>Expected VIN: {vehicleInfo.assignedVin}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="bg-gray-800 rounded-lg p-6 mb-6 border border-gray-700">
                    <h3 className="font-medium mb-4 text-white">Recovery Procedures Checklist</h3>
                    
                    <div className="space-y-4">
                      <label className="flex items-start cursor-pointer group">
                        <div className="flex items-center h-5">
                          <input 
                            type="checkbox" 
                            name="confirmOnHook"
                            checked={recoveryChecklist.confirmOnHook}
                            onChange={handleChecklistChange}
                            className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <span className="text-gray-300 group-hover:text-white transition-colors">
                            Vehicle is properly and safely secured on tow truck
                          </span>
                        </div>
                      </label>
                      
                      <label className="flex items-start cursor-pointer group">
                        <div className="flex items-center h-5">
                          <input 
                            type="checkbox" 
                            name="verifyVinMatch"
                            checked={recoveryChecklist.verifyVinMatch}
                            onChange={handleChecklistChange}
                            className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <span className="text-gray-300 group-hover:text-white transition-colors">
                            VIN has been verified (or discrepancy noted)
                          </span>
                        </div>
                      </label>
                      
                      <label className="flex items-start cursor-pointer group">
                        <div className="flex items-center h-5">
                          <input 
                            type="checkbox" 
                            name="conditionReportComplete"
                            checked={recoveryChecklist.conditionReportComplete}
                            onChange={handleChecklistChange}
                            className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <span className="text-gray-300 group-hover:text-white transition-colors">
                            Vehicle condition report completed accurately
                          </span>
                        </div>
                      </label>
                      
                      <label className="flex items-start cursor-pointer group">
                        <div className="flex items-center h-5">
                          <input 
                            type="checkbox" 
                            name="photosTaken"
                            checked={recoveryChecklist.photosTaken}
                            onChange={handleChecklistChange}
                            className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <span className="text-gray-300 group-hover:text-white transition-colors">
                            All required photos have been taken
                          </span>
                        </div>
                      </label>
                      
                      <label className="flex items-start cursor-pointer group">
                        <div className="flex items-center h-5">
                          <input 
                            type="checkbox" 
                            name="personalPropertyInventory"
                            checked={recoveryChecklist.personalPropertyInventory}
                            onChange={handleChecklistChange}
                            className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <span className="text-gray-300 group-hover:text-white transition-colors">
                            Personal property inventory completed
                          </span>
                        </div>
                      </label>
                      
                      <label className="flex items-start cursor-pointer group">
                        <div className="flex items-center h-5">
                          <input 
                            type="checkbox" 
                            name="vehicleSecured"
                            checked={recoveryChecklist.vehicleSecured}
                            onChange={handleChecklistChange}
                            className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <span className="text-gray-300 group-hover:text-white transition-colors">
                            Vehicle and contents have been properly secured
                          </span>
                        </div>
                      </label>
                    </div>

                    <h3 className="font-medium mt-6 mb-4 text-white">Post-Recovery Documentation</h3>
                    
                    <div className="space-y-4">
                      <label className="flex items-start cursor-pointer group">
                        <div className="flex items-center h-5">
                          <input 
                            type="checkbox" 
                            name="formsCompleted"
                            checked={recoveryChecklist.formsCompleted}
                            onChange={handleChecklistChange}
                            className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <span className="text-gray-300 group-hover:text-white transition-colors">
                            All required recovery forms completed
                          </span>
                        </div>
                      </label>
                      
                      <label className="flex items-start cursor-pointer group">
                        <div className="flex items-center h-5">
                          <input 
                            type="checkbox" 
                            name="systemUpdated"
                            checked={recoveryChecklist.systemUpdated}
                            onChange={handleChecklistChange}
                            className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <span className="text-gray-300 group-hover:text-white transition-colors">
                            System has been updated with recovery details
                          </span>
                        </div>
                      </label>
                      
                      <label className="flex items-start cursor-pointer group">
                        <div className="flex items-center h-5">
                          <input 
                            type="checkbox" 
                            name="partiesNotified"
                            checked={recoveryChecklist.partiesNotified}
                            onChange={handleChecklistChange}
                            className="w-5 h-5 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500 focus:ring-2 cursor-pointer"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <span className="text-gray-300 group-hover:text-white transition-colors">
                            Appropriate parties notified of successful recovery
                          </span>
                        </div>
                      </label>
                    </div>
                  </div>

                  <div className="bg-gray-800 rounded-lg p-6 mb-6 border border-yellow-600">
                    <div className="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-500 mt-1 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      <div>
                        <h3 className="font-medium mb-2 text-yellow-400">Legal Compliance Statement</h3>
                        <p className="text-sm text-gray-300">
                          By submitting this report, I certify that all information provided is accurate and complete. I have followed all legal protocols 
                          for vehicle recovery including proper documentation of the vehicle condition and personal property. I understand that providing 
                          false information may result in legal consequences.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <button
                      type="button"
                      onClick={() => handleSectionChange('personal-property')}
                      className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                    >
                      Back
                    </button>
                    <button
                      type="submit"
                      disabled={saving}
                      className={`${
                        saving 
                          ? 'bg-gray-600 cursor-not-allowed' 
                          : 'bg-green-600 hover:bg-green-700'
                      } text-white font-medium py-2 px-6 rounded transition duration-200 flex items-center`}
                    >
                      {saving ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Completing Recovery...
                        </>
                      ) : (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          Complete Vehicle Recovery
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </form>
          </div>

          {/* Add style for hiding scrollbars */}
          <style>
            {`
              .hide-scrollbar::-webkit-scrollbar {
                display: none;
              }
              .hide-scrollbar {
                -ms-overflow-style: none;
                scrollbar-width: none;
              }
            `}
          </style>
        </div>
      </div>
    );
  }

  // Full page render (original functionality)
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="bg-gray-800 shadow-lg border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-white">Vehicle Recovery</h1>
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md text-white transition duration-300 ease-in-out flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Dashboard
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Messages */}
        {errorMessage && (
          <div className="mb-6 bg-red-900 border border-red-800 text-white px-4 py-3 rounded-md flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <div>
              <p className="font-medium">{errorMessage}</p>
              {formErrors.checklistItems && (
                <ul className="mt-2 list-disc list-inside text-sm">
                  {formErrors.checklistItems.map((item, index) => (
                    <li key={index}>{item.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        )}

        {successMessage && (
          <div className="mb-6 bg-green-900 border border-green-800 text-white px-4 py-3 rounded-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <p className="font-medium">{successMessage}</p>
          </div>
        )}

        {/* Recovery Form */}
        <div className="bg-gray-800 rounded-lg shadow-lg border border-gray-700 overflow-hidden">
          {/* Form Navigation Tabs */}
          <div className="bg-gray-900 px-4 sm:px-6 border-b border-gray-700">
            <nav className="flex space-x-4 overflow-x-auto py-4 hide-scrollbar">
              <button
                onClick={() => handleSectionChange('assignments')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentSection === 'assignments' 
                    ? 'bg-blue-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                1. Assignments
              </button>
              <button
                onClick={() => handleSectionChange('vehicle-info')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentSection === 'vehicle-info' 
                    ? 'bg-blue-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                2. Vehicle Info
              </button>
              <button
                onClick={() => handleSectionChange('photos')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentSection === 'photos' 
                    ? 'bg-blue-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                3. Photos
              </button>
              <button
                onClick={() => handleSectionChange('condition-report')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentSection === 'condition-report' 
                    ? 'bg-blue-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                4. Condition Report
              </button>
              <button
                onClick={() => handleSectionChange('personal-property')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentSection === 'personal-property' 
                    ? 'bg-blue-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                5. Personal Property
              </button>
              <button
                onClick={() => handleSectionChange('final-checklist')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentSection === 'final-checklist' 
                    ? 'bg-blue-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                6. Verification
              </button>
            </nav>
          </div>

          {/* Form Content */}
          <form onSubmit={handleSubmit} className="p-6">
            {/* Assignments Section */}
            {currentSection === 'assignments' && (
              <div>
                <h2 className="text-xl font-bold mb-4">Current Assignments</h2>
                <p className="mb-4 text-gray-300">
                  Select from your current recovery assignments or create a new recovery record manually.
                </p>

                {repoAssignments.length > 0 ? (
                  <div className="space-y-4 mb-6">
                    {repoAssignments.map((assignment) => (
                      <div 
                        key={assignment.id}
                        onClick={() => handleSelectAssignment(assignment)}
                        className={`bg-gray-750 border ${
                          selectedAssignment?.id === assignment.id 
                            ? 'border-blue-500 bg-blue-900 bg-opacity-20' 
                            : 'border-gray-700 hover:border-gray-500'
                        } rounded-lg p-4 cursor-pointer transition-all duration-200`}
                      >
                        <div className="flex justify-between">
                          <h3 className="font-medium">Case #{assignment.caseNumber}</h3>
                          <span className="px-2 py-1 bg-yellow-900 text-yellow-300 rounded-full text-xs">
                            Assigned
                          </span>
                        </div>
                        <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-gray-400">Vehicle:</span> {assignment.vehicleYear} {assignment.vehicleMake} {assignment.vehicleModel}
                          </div>
                          <div>
                            <span className="text-gray-400">Color:</span> {assignment.vehicleColor}
                          </div>
                          <div>
                            <span className="text-gray-400">License:</span> {assignment.licensePlate}
                          </div>
                          <div>
                            <span className="text-gray-400">VIN:</span> {assignment.vin}
                          </div>
                        </div>
                        {assignment.notes && (
                          <div className="mt-2 text-sm">
                            <span className="text-gray-400">Notes:</span> {assignment.notes}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="bg-gray-750 border border-gray-700 rounded-lg p-6 mb-6 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <p className="text-gray-400">No active assignments found</p>
                  </div>
                )}

                <div className="mt-8 flex justify-between">
                  <button
                    type="button"
                    onClick={() => selectedAssignment ? handleSectionChange('vehicle-info') : setErrorMessage("Please select an assignment or click 'Create New Recovery' to continue")}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Continue with Selected Assignment
                  </button>
                  <button
                    type="button"
                    onClick={() => handleSectionChange('vehicle-info')}
                    className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Create New Recovery
                  </button>
                </div>
              </div>
            )}

            {/* Vehicle Info Section for full page mode */}
            {currentSection === 'vehicle-info' && (
              <div>
                <h2 className="text-xl font-bold mb-4">Vehicle Information</h2>
                <p className="mb-4 text-gray-300">
                  Enter basic information about the recovered vehicle. Fields marked with * are required.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Case/Recovery ID *
                      <input 
                        type="text" 
                        value={recoveryCaseId}
                        onChange={(e) => setRecoveryCaseId(e.target.value)}
                        className={`mt-1 block w-full bg-gray-700 border ${formErrors.recoveryCaseId ? 'border-red-500' : 'border-gray-600'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white`}
                        placeholder="Enter case number"
                      />
                      {formErrors.recoveryCaseId && <p className="text-red-500 text-sm mt-1">{formErrors.recoveryCaseId}</p>}
                    </label>
                  </div>
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Recovery Location *
                      <input 
                        type="text" 
                        value={recoveryLocation}
                        onChange={(e) => setRecoveryLocation(e.target.value)}
                        className={`mt-1 block w-full bg-gray-700 border ${formErrors.recoveryLocation ? 'border-red-500' : 'border-gray-600'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white`}
                        placeholder="Address or location description"
                      />
                      {formErrors.recoveryLocation && <p className="text-red-500 text-sm mt-1">{formErrors.recoveryLocation}</p>}
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Make *
                      <input 
                        type="text" 
                        name="make"
                        value={vehicleInfo.make}
                        onChange={handleVehicleInfoChange}
                        className={`mt-1 block w-full bg-gray-700 border ${formErrors.make ? 'border-red-500' : 'border-gray-600'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white`}
                        placeholder="e.g. Ford, Toyota"
                      />
                      {formErrors.make && <p className="text-red-500 text-sm mt-1">{formErrors.make}</p>}
                    </label>
                  </div>
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Model *
                      <input 
                        type="text" 
                        name="model"
                        value={vehicleInfo.model}
                        onChange={handleVehicleInfoChange}
                        className={`mt-1 block w-full bg-gray-700 border ${formErrors.model ? 'border-red-500' : 'border-gray-600'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white`}
                        placeholder="e.g. F-150, Camry"
                      />
                      {formErrors.model && <p className="text-red-500 text-sm mt-1">{formErrors.model}</p>}
                    </label>
                  </div>
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Year
                      <input 
                        type="text" 
                        name="year"
                        value={vehicleInfo.year}
                        onChange={handleVehicleInfoChange}
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="e.g. 2018"
                      />
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Color
                      <input 
                        type="text" 
                        name="color"
                        value={vehicleInfo.color}
                        onChange={handleVehicleInfoChange}
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="e.g. Red, Silver"
                      />
                    </label>
                  </div>
                  <div>
                    <label className="block text-gray-300 mb-2">
                      License Plate
                      <input 
                        type="text" 
                        name="licensePlate"
                        value={vehicleInfo.licensePlate}
                        onChange={handleVehicleInfoChange}
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="Enter license plate number"
                      />
                    </label>
                  </div>
                </div>

                <div className="mb-6">
                  <label className="block text-gray-300 mb-2">
                    VIN (Vehicle Identification Number) *
                    <input 
                      type="text" 
                      name="vin"
                      value={vehicleInfo.vin}
                      onChange={handleVinChange}
                      className={`mt-1 block w-full bg-gray-700 border ${
                        formErrors.vin || showVinMismatchWarning 
                          ? 'border-red-500' 
                          : 'border-gray-600'
                      } rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white`}
                      placeholder="Enter vehicle VIN"
                    />
                    {formErrors.vin && <p className="text-red-500 text-sm mt-1">{formErrors.vin}</p>}
                    {showVinMismatchWarning && !formErrors.vin && (
                      <p className="text-yellow-500 text-sm mt-1">
                        ⚠️ Warning: VIN may not match exactly. Please verify this is the correct vehicle.
                      </p>
                    )}
                    {vehicleInfo.assignedVin && (
                      <p className="text-gray-400 text-sm mt-1">
                        Expected VIN: {vehicleInfo.assignedVin}
                      </p>
                    )}
                  </label>
                </div>

                <div className="flex justify-between mt-8">
                  <button
                    type="button"
                    onClick={() => handleSectionChange('assignments')}
                    className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Back
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      // Do a simple validation before proceeding
                      if (!recoveryCaseId || !vehicleInfo.make || !vehicleInfo.model || !vehicleInfo.vin) {
                        setErrorMessage("Please fill in all required fields before proceeding.");
                        return;
                      }
                      
                      // Auto-set the On-Hook confirmation when moving to photos
                      setRecoveryChecklist({
                        ...recoveryChecklist,
                        confirmOnHook: true
                      });
                      
                      handleSectionChange('photos');
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Continue to Photos
                  </button>
                </div>
              </div>
            )}

            {/* Photos Section for full page mode */}
            {currentSection === 'photos' && (
              <div>
                <h2 className="text-xl font-bold mb-4">Vehicle Photos</h2>
                <p className="mb-4 text-gray-300">
                  Upload clear photos of the vehicle from different angles. Photos marked with * are required.
                </p>

                <div className="bg-gray-750 rounded-lg p-4 mb-6 border border-gray-700">
                  <h3 className="font-medium mb-2 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Photo Guidelines
                  </h3>
                  <ul className="list-disc list-inside text-sm text-gray-300 space-y-1">
                    <li>Take photos in good lighting conditions</li>
                    <li>Ensure all photos are clear and in focus</li>
                    <li>Capture the entire vehicle in exterior shots</li>
                    <li>Take close-ups of any existing damage</li>
                    <li>Ensure VIN photo is clearly legible</li>
                  </ul>
                </div>

                {/* Photo upload grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                  {/* Required Photos */}
                  {renderPhotoUpload('frontView', 'Front View', true)}
                  {renderPhotoUpload('rearView', 'Rear View', true)}
                  {renderPhotoUpload('vin', 'VIN Photo', true)}
                  
                  {/* Optional Photos */}
                  {renderPhotoUpload('driverSide', 'Driver Side')}
                  {renderPhotoUpload('passengerSide', 'Passenger Side')}
                  {renderPhotoUpload('interiorFront', 'Interior Front')}
                  {renderPhotoUpload('interiorRear', 'Interior Rear')}
                  {renderPhotoUpload('dashboard', 'Dashboard/Odometer')}
                  {renderPhotoUpload('licensePlate', 'License Plate')}
                </div>

                {/* Damage Photos */}
                <div className="mb-6">
                  <h3 className="font-medium mb-2 text-white">Damage Photos</h3>
                  <p className="text-sm text-gray-300 mb-4">
                    Upload clear photos of any damage found on the vehicle.
                  </p>
                  
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4">
                    {photoURLs.damage.map((url, index) => (
                      <div key={index} className="bg-gray-750 rounded-lg p-2 border border-gray-700 relative">
                        <div className="aspect-w-1 aspect-h-1 bg-gray-800 rounded-md overflow-hidden h-24">
                          <img src={url} alt={`Damage ${index + 1}`} className="object-cover w-full h-full" />
                        </div>
                        <button
                          type="button"
                          onClick={() => removeDamagePhoto(index)}
                          className="absolute top-1 right-1 bg-red-600 rounded-full p-1 text-white hover:bg-red-700 transition-colors"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ))}
                    
                    {/* Upload damage photo button */}
                    <div className="bg-gray-750 rounded-lg p-2 border border-gray-700 border-dashed">
                      <div className="aspect-w-1 aspect-h-1 bg-gray-800 rounded-md overflow-hidden flex items-center justify-center h-24">
                        <label className="cursor-pointer flex flex-col items-center justify-center w-full h-full">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          <span className="text-xs text-gray-400 mt-2">Add Photo</span>
                          <input 
                            type="file" 
                            accept="image/*"
                            onChange={(e) => handlePhotoUpload(e, 'damage')}
                            className="hidden" 
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between mt-8">
                  <button
                    type="button"
                    onClick={() => handleSectionChange('vehicle-info')}
                    className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Back
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      // Check if required photos are uploaded - FIXED VALIDATION
                      const requiredPhotoTypes = ['frontView', 'rearView', 'vin'];
                      const missingPhotos = requiredPhotoTypes.filter(type => !photoURLs[type] || photoURLs[type] === '');
                      
                      if (missingPhotos.length > 0) {
                        setErrorMessage("Please upload all required photos before proceeding.");
                        // Set specific errors for missing photos
                        const photoErrors = {};
                        missingPhotos.forEach(type => {
                          photoErrors[`photo_${type}`] = `${type.replace(/([A-Z])/g, ' $1').trim()} photo is required`;
                        });
                        setFormErrors({...formErrors, ...photoErrors});
                        return;
                      }
                      
                      // Auto-update the photo checklist item
                      setRecoveryChecklist({
                        ...recoveryChecklist,
                        photosTaken: true
                      });
                      
                      handleSectionChange('condition-report');
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Continue to Condition Report
                  </button>
                </div>
              </div>
            )}

            {/* Condition Report Section for full page mode */}
            {currentSection === 'condition-report' && (
              <div>
                <h2 className="text-xl font-bold mb-4">Vehicle Condition Report</h2>
                <p className="mb-4 text-gray-300">
                  Document the current condition of the vehicle. Be specific about any damage or issues.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Exterior Condition
                      <select
                        name="exterior"
                        value={vehicleCondition.exterior}
                        onChange={handleConditionChange}
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                      >
                        <option value="Excellent">Excellent</option>
                        <option value="Good">Good</option>
                        <option value="Fair">Fair</option>
                        <option value="Poor">Poor</option>
                        <option value="Very Poor">Very Poor</option>
                      </select>
                    </label>
                  </div>
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Interior Condition
                      <select
                        name="interior"
                        value={vehicleCondition.interior}
                        onChange={handleConditionChange}
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                      >
                        <option value="Excellent">Excellent</option>
                        <option value="Good">Good</option>
                        <option value="Fair">Fair</option>
                        <option value="Poor">Poor</option>
                        <option value="Very Poor">Very Poor</option>
                      </select>
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Engine Condition
                      <select
                        name="engine"
                        value={vehicleCondition.engine}
                        onChange={handleConditionChange}
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                      >
                        <option value="Unknown">Unknown</option>
                        <option value="Running">Running</option>
                        <option value="Not Running">Not Running</option>
                        <option value="Damaged">Damaged</option>
                      </select>
                    </label>
                  </div>
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Transmission Condition
                      <select
                        name="transmission"
                        value={vehicleCondition.transmission}
                        onChange={handleConditionChange}
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                      >
                        <option value="Unknown">Unknown</option>
                        <option value="Working">Working</option>
                        <option value="Issues">Has Issues</option>
                        <option value="Not Working">Not Working</option>
                      </select>
                    </label>
                  </div>
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Tire Condition
                      <select
                        name="tires"
                        value={vehicleCondition.tires}
                        onChange={handleConditionChange}
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                      >
                        <option value="Excellent">Excellent</option>
                        <option value="Good">Good</option>
                        <option value="Fair">Fair</option>
                        <option value="Poor">Poor</option>
                        <option value="Flat/Damaged">Flat/Damaged</option>
                      </select>
                    </label>
                  </div>
                </div>

                <div className="mb-6">
                  <label className="block text-gray-300 mb-2">
                    Detailed Damage Description (if applicable)
                    <textarea
                      name="damage"
                      value={vehicleCondition.damage}
                      onChange={handleConditionChange}
                      rows="3"
                      className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                      placeholder="Describe any damage in detail (dents, scratches, broken parts, etc.)"
                    ></textarea>
                  </label>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="block text-gray-300 mb-2">
                      Odometer Reading (if visible)
                      <input
                        type="text"
                        name="mileage"
                        value={vehicleCondition.mileage}
                        onChange={handleConditionChange}
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="Enter current mileage"
                      />
                    </label>
                  </div>
                  <div className="flex items-center">
                    <label className="flex items-center cursor-pointer mt-8">
                      <div className="relative">
                        <input 
                          type="checkbox" 
                          className="sr-only" 
                          checked={vehicleCondition.keys} 
                          onChange={handleKeyToggle}
                        />
                        <div className={`block ${vehicleCondition.keys ? 'bg-blue-600' : 'bg-gray-600'} w-14 h-8 rounded-full transition-colors duration-300`}></div>
                        <div className={`dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition-transform duration-300 ${vehicleCondition.keys ? 'transform translate-x-6' : ''}`}></div>
                      </div>
                      <div className="ml-3 text-gray-300 font-medium">
                        Keys Present
                      </div>
                    </label>
                  </div>
                </div>

                <div className="flex justify-between mt-8">
                  <button
                    type="button"
                    onClick={() => handleSectionChange('photos')}
                    className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Back
                  </button>
                  <button
                    type="button"
                    onClick={() => handleSectionChange('personal-property')}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Continue to Personal Property
                  </button>
                </div>
              </div>
            )}

            {/* Personal Property Section for full page mode */}
            {currentSection === 'personal-property' && (
              <div>
                <h2 className="text-xl font-bold mb-4">Personal Property Inventory</h2>
                <p className="mb-4 text-gray-300">
                  Document any personal items found in the vehicle. This is a critical legal requirement.
                </p>

                <div className="bg-gray-750 rounded-lg p-4 mb-6 border border-gray-700">
                  <h3 className="font-medium mb-2 flex items-center text-yellow-400">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Important Legal Notice
                  </h3>
                  <p className="text-sm text-gray-300">
                    Personal property in the vehicle must be documented and safeguarded. Failing to properly 
                    inventory and secure personal property can result in legal liability. Be thorough and 
                    specific in your documentation.
                  </p>
                </div>

                <div className="mb-6">
                  <label className="block text-gray-300 mb-2">
                    Personal Items Inventory
                    <textarea
                      name="personalItems"
                      value={vehicleCondition.personalItems}
                      onChange={handleConditionChange}
                      rows="5"
                      className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                      placeholder="List all personal items found in the vehicle (e.g., clothing, electronics, documents, tools, etc.). Write 'No personal items found' if the vehicle is empty."
                    ></textarea>
                  </label>
                </div>

                <div className="mb-6">
                  <label className="block text-gray-300 mb-2">
                    Additional Notes
                    <textarea
                      name="notes"
                      value={vehicleCondition.notes}
                      onChange={handleConditionChange}
                      rows="3"
                      className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-white"
                      placeholder="Add any additional notes or observations about the vehicle or recovery process"
                    ></textarea>
                  </label>
                </div>

                {/* Personal Property Checklist - FIXED VERSION for full page */}
                <div className="bg-gray-750 rounded-lg p-4 mb-6 border border-gray-700">
                <h3 className="font-medium mb-3">Personal Property Confirmation</h3>
                  <label className="flex items-start cursor-pointer group mb-3">
                    <div className="flex items-center h-5">
                      <input 
                        type="checkbox" 
                        name="personalPropertyInventory"
                        checked={recoveryChecklist.personalPropertyInventory}
                        onChange={handleChecklistChange}
                        className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <span className="text-gray-300 group-hover:text-white transition-colors">
                        I have thoroughly checked the vehicle for personal property and documented all items found
                      </span>
                    </div>
                  </label>
                  <label className="flex items-start cursor-pointer group">
                    <div className="flex items-center h-5">
                      <input 
                        type="checkbox" 
                        name="vehicleSecured"
                        checked={recoveryChecklist.vehicleSecured}
                        onChange={handleChecklistChange}
                        className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <span className="text-gray-300 group-hover:text-white transition-colors">
                        I have secured the vehicle and any personal property
                      </span>
                    </div>
                  </label>
                </div>

                <div className="flex justify-between mt-8">
                  <button
                    type="button"
                    onClick={() => handleSectionChange('condition-report')}
                    className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Back
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      // Check if property inventory is checked
                      if (!recoveryChecklist.personalPropertyInventory) {
                        setErrorMessage("Please confirm you've inventoried all personal property before proceeding.");
                        return;
                      }
                      
                      // Set all system-related checklist items to true when proceeding to final verification
                      setRecoveryChecklist({
                        ...recoveryChecklist,
                        formsCompleted: true,
                        systemUpdated: true,
                        partiesNotified: true
                      });
                      
                      handleSectionChange('final-checklist');
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Continue to Final Verification
                  </button>
                </div>
              </div>
            )}

            {/* Final Checklist Section for full page mode */}
            {currentSection === 'final-checklist' && (
              <div>
                <h2 className="text-xl font-bold mb-4">Final Verification Checklist</h2>
                <p className="mb-4 text-gray-300">
                  Review and confirm all required steps have been completed before submitting the recovery report.
                </p>

                {/* VIN Mismatch Warning at top of final checklist */}
                {showVinMismatchWarning && (
                  <div className="bg-yellow-900 bg-opacity-40 rounded-lg p-4 mb-6 border border-yellow-600">
                    <div className="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-500 mt-1 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      <div>
                        <h3 className="font-medium mb-2 text-yellow-400">VIN Verification Notice</h3>
                        <p className="text-sm text-yellow-200">
                          The entered VIN may not match exactly with the expected VIN. Please verify that this is the correct vehicle before proceeding. 
                          You can still complete the recovery, but this will be noted in the report.
                        </p>
                        <div className="mt-2 text-xs text-yellow-300">
                          <div>Entered VIN: {vehicleInfo.vin}</div>
                          <div>Expected VIN: {vehicleInfo.assignedVin}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="bg-gray-800 rounded-lg p-6 mb-6 border border-gray-700">
                  <h3 className="font-medium mb-4 text-white">Recovery Procedures Checklist</h3>
                  
                  <div className="space-y-4">
                    <label className="flex items-start cursor-pointer group">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="confirmOnHook"
                          checked={recoveryChecklist.confirmOnHook}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          Vehicle is properly and safely secured on tow truck
                        </span>
                      </div>
                    </label>
                    
                    <label className="flex items-start cursor-pointer group">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="verifyVinMatch"
                          checked={recoveryChecklist.verifyVinMatch}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          VIN has been verified (or discrepancy noted)
                        </span>
                      </div>
                    </label>
                    
                    <label className="flex items-start cursor-pointer group">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="conditionReportComplete"
                          checked={recoveryChecklist.conditionReportComplete}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          Vehicle condition report completed accurately
                        </span>
                      </div>
                    </label>
                    
                    <label className="flex items-start cursor-pointer group">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="photosTaken"
                          checked={recoveryChecklist.photosTaken}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          All required photos have been taken
                        </span>
                      </div>
                    </label>
                    
                    <label className="flex items-start cursor-pointer group">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="personalPropertyInventory"
                          checked={recoveryChecklist.personalPropertyInventory}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          Personal property inventory completed
                        </span>
                      </div>
                    </label>
                    
                    <label className="flex items-start cursor-pointer group">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="vehicleSecured"
                          checked={recoveryChecklist.vehicleSecured}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          Vehicle and contents have been properly secured
                        </span>
                      </div>
                    </label>
                  </div>

                  <h3 className="font-medium mt-6 mb-4 text-white">Post-Recovery Documentation</h3>
                  
                  <div className="space-y-4">
                    <label className="flex items-start cursor-pointer group">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="formsCompleted"
                          checked={recoveryChecklist.formsCompleted}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          All required recovery forms completed
                        </span>
                      </div>
                    </label>
                    
                    <label className="flex items-start cursor-pointer group">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="systemUpdated"
                          checked={recoveryChecklist.systemUpdated}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          System has been updated with recovery details
                        </span>
                      </div>
                    </label>
                    
                    <label className="flex items-start cursor-pointer group">
                      <div className="flex items-center h-5">
                        <input 
                          type="checkbox" 
                          name="partiesNotified"
                          checked={recoveryChecklist.partiesNotified}
                          onChange={handleChecklistChange}
                          className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <span className="text-gray-300 group-hover:text-white transition-colors">
                          Appropriate parties notified of successful recovery
                        </span>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-6 mb-6 border border-yellow-600">
                  <div className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-500 mt-1 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <div>
                      <h3 className="font-medium mb-2 text-yellow-400">Legal Compliance Statement</h3>
                      <p className="text-sm text-gray-300">
                        By submitting this report, I certify that all information provided is accurate and complete. I have followed all legal protocols 
                        for vehicle recovery including proper documentation of the vehicle condition and personal property. I understand that providing 
                        false information may result in legal consequences.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between mt-8">
                  <button
                    type="button"
                    onClick={() => handleSectionChange('personal-property')}
                    className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded transition duration-200"
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    disabled={saving}
                    className={`${
                      saving 
                        ? 'bg-gray-600 cursor-not-allowed' 
                        : 'bg-blue-600 hover:bg-blue-700'
                    } text-white font-medium py-2 px-6 rounded transition duration-200 flex items-center`}
                  >
                    {saving ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Completing Recovery...
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Complete Vehicle Recovery
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}
          </form>n mbjhvgc 
        </div>
      </main>

      {/* Add style for hiding scrollbars */}
      <style>
        {`
          .hide-scrollbar::-webkit-scrollbar {
            display: none;
          }
          .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
          }
        `}
      </style>
    </div>
  );
}

export default Recovery;