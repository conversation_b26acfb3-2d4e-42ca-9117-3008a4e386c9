import React, { useState, useEffect, useRef, useContext } from 'react';
import { collection, addDoc, query, onSnapshot, serverTimestamp } from 'firebase/firestore';
import { MapContext } from '../MapContext';
import useMap from '../../hooks/useMap'; // Import the useMap hook

const ChatPanel = () => {
  // Use the map context hook
  const { 
    currentUser, 
    firestoreRef, 
    userProfilePictures, 
    setUserProfilePictures, 
    userDisplayNames,
    setUserDisplayNames,
    setError,
    isLargeScreen,
    detailsVisible,
    chatMessages,
    sendChatMessage
  } = useMap();

  // Component state
  const [newChatMessage, setNewChatMessage] = useState('');
  const [mediaToUpload, setMediaToUpload] = useState(null);
  const [chatImageUpload, setChatImageUpload] = useState(false);
  const [chatVideoUpload, setChatVideoUpload] = useState(false);
  const [expandedImageIndex, setExpandedImageIndex] = useState(null);
  
  // Refs
  const chatMessagesRef = useRef(null);

  // Scroll to the bottom of the chat when messages change
  useEffect(() => {
    if (chatMessagesRef.current) {
      chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // Handle chat message submission
  const handleChatSubmit = (e) => {
    e.preventDefault();
    if (newChatMessage.trim() || mediaToUpload) {
      sendChatMessage(newChatMessage, mediaToUpload);
      setNewChatMessage('');
      setMediaToUpload(null);
      setChatImageUpload(false);
      setChatVideoUpload(false);
    }
  };

  // Handle media upload for chat
  const handleChatMediaUpload = (mediaType) => {
    try {
      const input = document.createElement('input');
      input.type = 'file';
      
      if (mediaType === 'image') {
        input.accept = 'image/*';
        setChatImageUpload(true);
        setChatVideoUpload(false);
      } else if (mediaType === 'video') {
        input.accept = 'video/*';
        setChatImageUpload(false);
        setChatVideoUpload(true);
      }
      
      input.onchange = async (e) => {
        if (e.target.files && e.target.files[0]) {
          try {
            const file = e.target.files[0];
            
            // Check file size
            if (file.size > 5 * 1024 * 1024) { // 5MB limit
              setError("File is too large. Please upload a file smaller than 5MB.");
              return;
            }
            
            // Compress image if it's an image
            if (mediaType === 'image') {
              // Compress the image
              const compressImage = async (file) => {
                return new Promise((resolve, reject) => {
                  const reader = new FileReader();
                  reader.readAsDataURL(file);
                  reader.onload = (event) => {
                    const img = new Image();
                    img.src = event.target.result;
                    img.onload = () => {
                      const canvas = document.createElement('canvas');
                      const ctx = canvas.getContext('2d');
                      
                      // Calculate new dimensions
                      const MAX_WIDTH = 800;
                      const MAX_HEIGHT = 800;
                      let width = img.width;
                      let height = img.height;
                      
                      if (width > height) {
                        if (width > MAX_WIDTH) {
                          height *= MAX_WIDTH / width;
                          width = MAX_WIDTH;
                        }
                      } else {
                        if (height > MAX_HEIGHT) {
                          width *= MAX_HEIGHT / height;
                          height = MAX_HEIGHT;
                        }
                      }
                      
                      // Resize the image
                      canvas.width = width;
                      canvas.height = height;
                      ctx.drawImage(img, 0, 0, width, height);
                      
                      // Get the data URL
                      const dataUrl = canvas.toDataURL('image/jpeg', 0.7);
                      resolve(dataUrl);
                    };
                    img.onerror = reject;
                  };
                  reader.onerror = reject;
                });
              };
              
              const base64 = await compressImage(file);
              setMediaToUpload({
                type: 'image',
                data: base64
              });
            } else {
              // For video, we'll use an object URL for preview
              const objectURL = URL.createObjectURL(file);
              setMediaToUpload({
                type: 'video',
                data: objectURL,
                file: file
              });
            }
          } catch (err) {
            console.error("Error processing chat media:", err);
            setError("Failed to process media. Please try again with a smaller file.");
          }
        }
      };
      
      input.click();
    } catch (err) {
      console.error("Error handling chat media upload:", err);
      setError("Could not access media. Please check permissions.");
    }
  };

  // Cancel media upload for chat
  const cancelMediaUpload = () => {
    setMediaToUpload(null);
    setChatImageUpload(false);
    setChatVideoUpload(false);
  };

  // Open image viewer
  const openImageViewer = (images, initialIndex) => {
    try {
      setExpandedImageIndex(initialIndex);
      
      // Create image viewer overlay
      const viewer = document.createElement('div');
      viewer.className = 'image-viewer';
      viewer.innerHTML = `
        <div class="image-viewer-content">
          <button class="image-viewer-close">×</button>
          <img src="${images[initialIndex]}" alt="Enlarged image">
        </div>
      `;
      
      document.body.appendChild(viewer);
      
      // Add close event
      viewer.querySelector('.image-viewer-close').addEventListener('click', () => {
        try {
          document.body.removeChild(viewer);
        } catch (err) {
          console.warn("Error removing image viewer:", err);
        }
        setExpandedImageIndex(null);
      });
      
      // Close on background click
      viewer.addEventListener('click', (e) => {
        if (e.target === viewer) {
          try {
            document.body.removeChild(viewer);
          } catch (err) {
            console.warn("Error removing image viewer:", err);
          }
          setExpandedImageIndex(null);
        }
      });
    } catch (err) {
      console.error("Error opening image viewer:", err);
    }
  };

  // Render chat message with profile picture and media
  const renderChatMessage = (message) => {
    const isCurrentUser = currentUser && message.sender && message.sender.uid === currentUser.uid;
    
    // Try to get profile picture from multiple sources
    let profilePic = null;
    
    // First check if message already has a photo
    if (message.sender.photo) {
      profilePic = message.sender.photo;
    } 
    // Then check our cached profile pictures
    else if (userProfilePictures[message.sender.uid]) {
      profilePic = userProfilePictures[message.sender.uid];
    }
    
    return (
      <div 
        key={message.id} 
        className={`chat-message ${isCurrentUser ? 'sent' : 'received'}`}
      >
        <div className="chat-message-header">
          <div className="chat-profile-pic">
            {profilePic ? (
              <img src={profilePic} alt={message.sender.name} />
            ) : (
              <div className="profile-placeholder">
                {message.sender.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          <div className="chat-message-sender">
            {message.sender.name}
          </div>
        </div>
        <div className="chat-message-content">
          {message.text}
        </div>
        
        {/* Render media if present */}
        {message.mediaUrl && message.mediaType === 'image' && (
          <div className="chat-media">
            <img 
              src={message.mediaUrl} 
              alt="Shared" 
              onClick={() => openImageViewer([message.mediaUrl], 0)}
            />
          </div>
        )}
        
        {message.mediaUrl && message.mediaType === 'video' && (
          <div className="chat-media">
            <video 
              src={message.mediaUrl} 
              controls
              width="100%"
            />
          </div>
        )}
        
        <div className="chat-message-time">
          {message.timestamp.toLocaleTimeString()}
        </div>
      </div>
    );
  };

  return (
    <div className="chat-container" style={{ maxWidth: isLargeScreen && detailsVisible ? '75%' : '100%' }}>
      <div className="chat-header">
        <span>Team Chat</span>
        <span>{chatMessages.length} messages</span>
      </div>
      
      <div className="chat-messages" ref={chatMessagesRef}>
        {chatMessages.map(message => renderChatMessage(message))}
      </div>
      
      <form className="chat-input-container" onSubmit={handleChatSubmit}>
        {mediaToUpload && (
          <div className="relative mr-2 bg-gray-700 rounded overflow-hidden" style={{ width: '40px', height: '40px' }}>
            {mediaToUpload.type === 'image' && (
              <img 
                src={mediaToUpload.data} 
                alt="Upload" 
                className="w-full h-full object-cover"
              />
            )}
            {mediaToUpload.type === 'video' && (
              <video 
                src={mediaToUpload.data} 
                className="w-full h-full object-cover"
              />
            )}
            <button
              type="button"
              className="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
              onClick={cancelMediaUpload}
            >
              ×
            </button>
          </div>
        )}
        
        <input
          type="text"
          className="chat-input"
          placeholder="Type your message..."
          value={newChatMessage}
          onChange={(e) => setNewChatMessage(e.target.value)}
          disabled={!currentUser}
        />
        
        <div className="chat-media-buttons">
          <button 
            type="button" 
            className="chat-media-btn image"
            onClick={() => handleChatMediaUpload('image')}
            disabled={!currentUser}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M6.002 5.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
              <path d="M2.002 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2h-12zm12 1a1 1 0 0 1 1 1v6.5l-3.777-1.947a.5.5 0 0 0-.577.093l-3.71 3.71-2.66-1.772a.5.5 0 0 0-.63.062L1.002 12V3a1 1 0 0 1 1-1h12z"/>
            </svg>
          </button>
          <button 
            type="button" 
            className="chat-media-btn video"
            onClick={() => handleChatMediaUpload('video')}
            disabled={!currentUser}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M0 5a2 2 0 0 1 2-2h7.5a2 2 0 0 1 1.983 1.738l3.11-1.382A1 1 0 0 1 16 4.269v7.462a1 1 0 0 1-1.406.913l-3.111-1.382A2 2 0 0 1 9.5 13H2a2 2 0 0 1-2-2V5zm11.5 5.175 3.5 1.556V4.269l-3.5 1.556v4.35zM2 4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h7.5a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1H2z"/>
            </svg>
          </button>
        </div>
        
        <button 
          type="submit" 
          className="send-chat-btn"
          disabled={(!newChatMessage.trim() && !mediaToUpload) || !currentUser}
        >
          Send
        </button>
      </form>
    </div>
  );
};

export default ChatPanel;