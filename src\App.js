import React, { useEffect, useState, Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext.js';
import { getFirestore, doc, setDoc, updateDoc, serverTimestamp, collection, query, getDocs, where, Timestamp, getDoc } from 'firebase/firestore';

// Core pages (always loaded)
import SplashScreen from './pages/SplashScreen.js';
import Login from './pages/Login.js';
import Dashboard from './pages/Dashboard.js';
import Profile from './pages/Profile.js';

// Standalone components (no auth required) - loaded immediately for better UX
import StandaloneVehicleTracker from './components/StandaloneVehicleTracker.js';
import VehicleTrackerURLGenerator from './components/VehicleTrackerURLGenerator.js';
import StandaloneMapDisplay from './components/StandaloneMapDisplay.js';

// Lazy load heavy components for better performance
const AdminPanel = lazy(() => import('./pages/AdminPanel.js'));
const TagsManagement = lazy(() => import('./pages/TagsManagement.js'));
const Map = lazy(() => import('./pages/Map.js'));
const Team = lazy(() => import('./pages/Team.js'));
const TeamMeeting = lazy(() => import('./pages/TeamMeeting.js'));
const Analytics = lazy(() => import('./pages/Analytics.js'));
const Orders = lazy(() => import('./pages/Orders.js'));
const Settings = lazy(() => import('./pages/Settings.js'));
const SuggestionsUpdates = lazy(() => import('./pages/SuggestionsUpdates.js'));
const PayInfo = lazy(() => import('./pages/PayInfo.js'));
const Inspection = lazy(() => import('./pages/Inspection.js'));
const Awards = lazy(() => import('./pages/Awards.js'));
const TimeCard = lazy(() => import('./pages/TimeCard.js'));
const CameraSys = lazy(() => import('./pages/CameraSys.js'));
const News = lazy(() => import('./pages/News.js'));
const EmployeeDirectory = lazy(() => import('./pages/EmployeeDirectory.js'));
const Fleet = lazy(() => import('./pages/Fleet.js'));
const Recovery = lazy(() => import('./pages/Recovery.js'));
const CameraStreamingManager = lazy(() => import('./components/CameraStreamingManager.js'));
const Bot = lazy(() => import('./pages/Bot.js'));

// Background service component that keeps cameras running
const BackgroundCameraService = () => {
  const { currentUser } = useAuth();
  const [serviceId, setServiceId] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  
  useEffect(() => {
    if (!currentUser) return;
    
    // Generate a unique ID for this service instance
    const uniqueId = `bg-service-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    setServiceId(uniqueId);
    
    // Check if we have a persisted server session
    const persistedServer = localStorage.getItem('cameraServerSessionId');
    
    // Register the service in Firestore
    const registerService = async () => {
      try {
        const db = getFirestore();
        const serviceRef = doc(db, 'backgroundServices', uniqueId);
        await setDoc(serviceRef, {
          userId: currentUser.uid,
          type: 'cameraService',
          status: 'active',
          startedAt: serverTimestamp(),
          lastHeartbeat: serverTimestamp(),
          persistedServer: persistedServer || null,
        });
        
        console.log('Background camera service registered:', uniqueId);
        setIsRunning(true);
        
        // Set up heartbeat to keep service active
        const heartbeatInterval = setInterval(async () => {
          try {
            await updateDoc(serviceRef, {
              lastHeartbeat: serverTimestamp(),
              status: 'active'
            });
          } catch (error) {
            console.error('Heartbeat error:', error);
          }
        }, 30000);
        
        // Create a ping channel to coordinate with any opened camera tabs
        try {
          const broadcastChannel = new BroadcastChannel('camera-system-channel');
          
          // Send periodic pings to keep any camera pages alive
          const pingInterval = setInterval(() => {
            broadcastChannel.postMessage({
              type: 'PING_REQUEST',
              serviceId: uniqueId,
              timestamp: Date.now()
            });
          }, 60000); // Every minute
          
          // Listen for responses
          broadcastChannel.onmessage = (event) => {
            if (event.data && event.data.type === 'PING_RESPONSE') {
              console.log('Received ping response from camera page:', event.data.clientId);
            } else if (event.data && event.data.type === 'SESSION_REGISTERED') {
              console.log('New camera session registered:', event.data.clientId);
            }
          };
          
          return () => {
            clearInterval(heartbeatInterval);
            clearInterval(pingInterval);
            broadcastChannel.close();
          };
        } catch (e) {
          console.log('Broadcast Channel not supported, using fallback:', e);
          
          // Fallback: just the interval
          return () => {
            clearInterval(heartbeatInterval);
          };
        }
      } catch (error) {
        console.error('Error registering background service:', error);
      }
    };
    
    registerService();
    
    // Register with service worker if available
    if ('serviceWorker' in navigator) {
      try {
        navigator.serviceWorker.register('/camera-service-worker.js')
          .then(registration => {
            console.log('Camera service worker registered by background service:', registration);
            
            // We're deliberately not using periodic sync to avoid permission errors
            console.log('Using alternative background mechanisms instead of periodic sync');
            
            // Set up listener for service worker messages if needed
            navigator.serviceWorker.addEventListener('message', (event) => {
              if (event.data && event.data.type === 'PING_FROM_SERVICE_WORKER') {
                console.log('Received ping from service worker');
              }
            });
          })
          .catch(error => {
            console.log('Service worker registration failed - this is not critical:', error.message);
          });
      } catch (error) {
        console.log('Service worker not available - this is not critical:', error.message);
      }
    } else {
      console.log('Service workers not supported by this browser');
    }
    
    // This component never unmounts, but if it does, ensure cleanup
    return () => {
      console.log('Background service component unmounting - but service continues');
    };
  }, [currentUser]);
  
  // The service doesn't render anything visible
  return null;
};

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('App Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
          <div className="text-center max-w-md p-6">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h1 className="text-2xl font-bold mb-2">Something went wrong</h1>
            <p className="text-gray-300 mb-4">
              An unexpected error occurred. Please refresh the page or contact support.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Loading Component
const LoadingSpinner = ({ message = "Loading..." }) => (
  <div className="min-h-screen bg-gray-900 flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
      <p className="text-gray-300">{message}</p>
    </div>
  </div>
);

// Basic Protected route component
function PrivateRoute({ children }) {
  const { currentUser } = useAuth();
  
  if (!currentUser) {
    return <Navigate to="/splash" state={{ redirectTo: "/login" }} />;
  }
  
  return children;
}

// Admin route component
function AdminRoute({ children }) {
  const { isAdmin } = useAuth();
  
  if (!isAdmin) {
    return <Navigate to="/dashboard" />;
  }
  
  return children;
}

// Enhanced route component that requires clock-in
function ClockInRequiredRoute({ children }) {
  const { currentUser, isAdmin } = useAuth();
  const [isClockedIn, setIsClockedIn] = useState(false);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Skip check for admins
    if (isAdmin) {
      setIsClockedIn(true);
      setLoading(false);
      return;
    }
    
    const checkClockStatus = async () => {
      if (!currentUser) return;
      
      try {
        const db = getFirestore();
        const timeCardRef = doc(db, 'timeCards', currentUser.uid);
        const timeCardDoc = await getDoc(timeCardRef);
        
        if (timeCardDoc.exists()) {
          const timeCardData = timeCardDoc.data();
          const today = new Date().toLocaleDateString();
          
          setIsClockedIn(
            timeCardData.currentDay === today && 
            timeCardData.clockedIn && 
            !timeCardData.clockedOut
          );
        } else {
          setIsClockedIn(false);
        }
      } catch (error) {
        console.error("Error checking clock status:", error);
        setIsClockedIn(false);
      } finally {
        setLoading(false);
      }
    };
    
    checkClockStatus();
  }, [currentUser, isAdmin]);
  
  if (!currentUser) {
    return <Navigate to="/splash" state={{ redirectTo: "/login" }} />;
  }
  
  if (loading) {
    return <LoadingSpinner message="Checking clock-in status..." />;
  }
  
  if (!isClockedIn && !isAdmin) {
    return <Navigate to="/timecard" state={{ requiredFor: "inspection" }} />;
  }
  
  return children;
}

// Enhanced route component that requires inspection
function InspectionRequiredRoute({ children }) {
  const { currentUser, isAdmin } = useAuth();
  const [hasCompletedInspection, setHasCompletedInspection] = useState(false);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Skip check for admins
    if (isAdmin) {
      setHasCompletedInspection(true);
      setLoading(false);
      return;
    }
    
    const checkInspectionStatus = async () => {
      if (!currentUser) return;
      
      try {
        // First check cache for better performance
        const cachedValue = localStorage.getItem('hasCompletedInspection');
        if (cachedValue === 'true') {
          setHasCompletedInspection(true);
          setLoading(false);
          return;
        }
        
        const db = getFirestore();
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        const startTimestamp = Timestamp.fromDate(today);
        const endTimestamp = Timestamp.fromDate(tomorrow);
        
        const q = query(
          collection(db, 'inspections'),
          where('userId', '==', currentUser.uid),
          where('timestamp', '>=', startTimestamp),
          where('timestamp', '<', endTimestamp)
        );
        
        const querySnapshot = await getDocs(q);
        const completed = !querySnapshot.empty;
        
        setHasCompletedInspection(completed);
        localStorage.setItem('hasCompletedInspection', completed ? 'true' : 'false');
      } catch (error) {
        console.error("Error checking inspection status:", error);
        setHasCompletedInspection(false);
      } finally {
        setLoading(false);
      }
    };
    
    checkInspectionStatus();
  }, [currentUser, isAdmin]);
  
  if (!currentUser) {
    return <Navigate to="/splash" state={{ redirectTo: "/login" }} />;
  }
  
  if (loading) {
    return <LoadingSpinner message="Checking inspection status..." />;
  }
  
  if (!hasCompletedInspection && !isAdmin) {
    return <Navigate to="/inspection" state={{ requiredFor: "map" }} />;
  }
  
  return children;
}

// Main App Component
function App() {
  return (
    <ErrorBoundary>
      <Router>
        <AuthProvider>
          {/* Background Camera Service runs for authenticated users */}
          <BackgroundCameraService />
          
          <Suspense fallback={<LoadingSpinner />}>
            <Routes>
              {/* ================ STANDALONE ROUTES (NO AUTH REQUIRED) ================ */}
              
              {/* MAP DISPLAY - NO AUTH REQUIRED - USING REAL STANDALONE MAP COMPONENT */}
              <Route path="/map-display" element={<StandaloneMapDisplay />} />
              
              {/* ✅ BOT ROUTE - NO AUTH REQUIRED FOR OAUTH FLOW */}
              <Route path="/bot/*" element={
                <Suspense fallback={<LoadingSpinner message="Loading bot..." />}>
                  <Bot />
                </Suspense>
              } />
              
              {/* ✅ FIXED: STANDALONE VEHICLE TRACKER ROUTES - NO AUTH REQUIRED */}
              {/* Catch-all for vehicles routes to handle dynamic parameters */}
              <Route path="/vehicles/*" element={<StandaloneVehicleTracker />} />
              <Route path="/vehicles" element={<StandaloneVehicleTracker />} />
              
              {/* ================ AUTHENTICATED ROUTES ================ */}
              
              {/* Splash screen route */}
              <Route path="/splash" element={<SplashScreen />} />
              
              {/* Login route now redirects through splash */}
              <Route path="/login" element={<Navigate to="/splash" state={{ redirectTo: "/login-direct" }} />} />
              
              {/* Direct login route that's only accessible after splash */}
              <Route path="/login-direct" element={<Login />} />
              
              <Route 
                path="/dashboard" 
                element={
                  <PrivateRoute>
                    <Dashboard />
                  </PrivateRoute>
                } 
              />
              
              <Route 
                path="/admin" 
                element={
                  <AdminRoute>
                    <AdminPanel />
                  </AdminRoute>
                } 
              />
              
              {/* Vehicle Tracker URL Generator - Admin Only */}
              <Route 
                path="/admin/vehicle-urls" 
                element={
                  <AdminRoute>
                    <VehicleTrackerURLGenerator />
                  </AdminRoute>
                } 
              />
              
              {/* Fleet Management Route - Admin Only */}
              <Route 
                path="/fleet" 
                element={
                  <AdminRoute>
                    <Fleet />
                  </AdminRoute>
                } 
              />
              
              {/* Profile routes - no longer lazy loaded */}
              <Route 
                path="/profile" 
                element={
                  <PrivateRoute>
                    <Profile />
                  </PrivateRoute>
                } 
              />
              
              <Route 
                path="/profile/:userId" 
                element={
                  <PrivateRoute>
                    <Profile />
                  </PrivateRoute>
                } 
              />
              
              <Route 
                path="/tags-management" 
                element={
                  <AdminRoute>
                    <TagsManagement />
                  </AdminRoute>
                } 
              />
              
              <Route 
                path="/settings" 
                element={
                  <PrivateRoute>
                    <Settings />
                  </PrivateRoute>
                } 
              />
              
              {/* Map now requires completed inspection */}
              <Route 
                path="/map" 
                element={
                  <InspectionRequiredRoute>
                    <Map />
                  </InspectionRequiredRoute>
                } 
              />
              
              {/* Camera System route - Updated with CameraStreamingManager */}
              <Route 
                path="/camera" 
                element={
                  <InspectionRequiredRoute>
                    <Suspense fallback={<LoadingSpinner message="Loading camera system..." />}>
                      <CameraStreamingManager>
                        <CameraSys />
                      </CameraStreamingManager>
                    </Suspense>
                  </InspectionRequiredRoute>
                } 
              />
              
              <Route 
                path="/team" 
                element={
                  <PrivateRoute>
                    <Team />
                  </PrivateRoute>
                } 
              />

              {/* Team Meeting route - adding both routes for with and without meetingId */}
              <Route 
                path="/team-meeting" 
                element={
                  <PrivateRoute>
                    <TeamMeeting />
                  </PrivateRoute>
                } 
              />
              
              {/* Add the parameterized route for meeting ID */}
              <Route 
                path="/team-meeting/:meetingId" 
                element={
                  <PrivateRoute>
                    <TeamMeeting />
                  </PrivateRoute>
                } 
              />
              
              {/* Employee Directory route */}
              <Route 
                path="/employee-directory" 
                element={
                  <PrivateRoute>
                    <EmployeeDirectory />
                  </PrivateRoute>
                } 
              />
              
              <Route 
                path="/analytics" 
                element={
                  <AdminRoute>
                    <Analytics />
                  </AdminRoute>
                } 
              />
              
              <Route 
                path="/orders" 
                element={
                  <PrivateRoute>
                    <Orders />
                  </PrivateRoute>
                } 
              />
              
              <Route 
                path="/suggestions-updates" 
                element={
                  <PrivateRoute>
                    <SuggestionsUpdates />
                  </PrivateRoute>
                } 
              />
              
              <Route 
                path="/pay-info" 
                element={
                  <PrivateRoute>
                    <PayInfo />
                  </PrivateRoute>
                } 
              />
              
              {/* Inspection now requires clock-in */}
              <Route 
                path="/inspection" 
                element={
                  <ClockInRequiredRoute>
                    <Inspection />
                  </ClockInRequiredRoute>
                } 
              />
              
              <Route 
                path="/awards" 
                element={
                  <PrivateRoute>
                    <Awards />
                  </PrivateRoute>
                } 
              />
              
              {/* Add TimeCard route */}
              <Route 
                path="/timecard" 
                element={
                  <PrivateRoute>
                    <TimeCard />
                  </PrivateRoute>
                } 
              />
              
              {/* Add News route */}
              <Route 
                path="/news" 
                element={
                  <PrivateRoute>
                    <News />
                  </PrivateRoute>
                } 
              />
              
              {/* Add Recovery route */}
              <Route 
                path="/recovery" 
                element={
                  <PrivateRoute>
                    <Recovery />
                  </PrivateRoute>
                } 
              />
              
              {/* Redirect root to splash page */}
              <Route path="/" element={<Navigate to="/splash" state={{ redirectTo: "/login-direct" }} />} />
              
              {/* Catch-all route for 404s */}
              <Route path="*" element={
                <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-yellow-500 text-6xl mb-4">🔍</div>
                    <h1 className="text-2xl font-bold mb-2">Page Not Found</h1>
                    <p className="text-gray-300 mb-4">The page you're looking for doesn't exist.</p>
                    <button
                      onClick={() => window.history.back()}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded mr-2"
                    >
                      Go Back
                    </button>
                    <button
                      onClick={() => window.location.href = '/'}
                      className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
                    >
                      Home
                    </button>
                  </div>
                </div>
              } />
            </Routes>
          </Suspense>
        </AuthProvider>
      </Router>
    </ErrorBoundary>
  );
}

export default App;