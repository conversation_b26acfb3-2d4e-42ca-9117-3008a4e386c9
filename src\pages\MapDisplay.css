/* Global Leaflet fixes */
.leaflet-container {
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #343332 !important;
}

.leaflet-control-container {
    z-index: 800;
    position: relative;
}

/* Custom animation for rotation indicator */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 2s linear infinite;
}

/* Ensure tiles properly display */
.leaflet-tile-container img {
    width: 256px !important;
    height: 256px !important;
}

/* Force map visibility */
.leaflet-layer,
.leaflet-control,
.leaflet-pane {
    z-index: 1 !important;
}

/* Map containers need position and overflow settings */
.zoomed-in-map-container,
.zoomed-out-map-container {
    position: relative;
    overflow: hidden;
}

/* IMPROVED: Fix for current location marker styling */
.current-location-marker {
    background: transparent !important;
    z-index: 1000 !important;
}

.current-marker-inner {
    width: 24px;
    height: 24px;
    background: rgba(0, 123, 255, 0.3);
    border: 2px solid #007bff;
    border-radius: 50%;
    position: relative;
    box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.2);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

.current-marker-inner:after {
    content: '';
    width: 10px;
    height: 10px;
    background: #007bff;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Style for destination marker */
.destination-marker {
    background: transparent !important;
    z-index: 900 !important;
}

.destination-marker-inner {
    width: 32px;
    height: 32px;
    background: rgba(220, 38, 38, 0.3);
    border: 2px solid #dc2626;
    border-radius: 50%;
    position: relative;
    box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.2);
}

.destination-marker-inner:after {
    content: '';
    width: 12px;
    height: 12px;
    background: #dc2626;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Vehicle Marker Styling */
.vehicle-marker {
    background: transparent !important;
    z-index: 700 !important;
}

.vehicle-marker-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vehicle-marker-bg {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    z-index: 10;
}

.vehicle-marker-pulse {
    animation: vehicle-pulse 2s infinite;
}

.vehicle-marker.priority .vehicle-marker-pulse {
    animation: vehicle-priority-pulse 1.5s infinite;
}

@keyframes vehicle-pulse {
    0% {
        transform: scale(0.8);
        opacity: 0.7;
    }

    70% {
        transform: scale(1.5);
        opacity: 0;
    }

    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

@keyframes vehicle-priority-pulse {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
    }

    50% {
        transform: scale(1.7);
        opacity: 0.2;
    }

    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

/* Routing styles */

/* Hide Leaflet Routing Machine default control panel */
.leaflet-routing-container {
    display: none !important;
}

/* Style for route lines - main line */
.leaflet-routing-line {
    stroke: #4a89f3;
    stroke-width: 6;
    stroke-opacity: 0.8;
    stroke-dasharray: none;
    stroke-linecap: round;
    fill: none;
}

/* Style for route lines - inner line for better visibility */
.routing-line-inner {
    stroke: #2a69d3;
    stroke-width: 3;
    stroke-opacity: 0.9;
    stroke-dasharray: none;
    fill: none;
}

/* Style for turn-by-turn direction icons */
.turn-icon {
    width: 30px;
    height: 30px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Context menu styling */
.context-menu-popup .leaflet-popup-content-wrapper {
    background-color: #1F2937;
    color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.context-menu-popup .leaflet-popup-tip {
    background-color: #1F2937;
}

.context-menu {
    padding: 0.5rem;
}

.context-menu-item {
    display: block;
    width: 100%;
    text-align: left;
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.25rem;
    color: white;
    background-color: #374151;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.context-menu-item:hover {
    background-color: #4B5563;
}

.context-menu-item:last-child {
    margin-bottom: 0;
}

/* Fix for marker clusters to ensure they're visible */
.marker-cluster {
    background-clip: padding-box;
    border-radius: 20px;
    z-index: 650 !important;
}

.marker-cluster div {
    width: 30px;
    height: 30px;
    margin-left: 5px;
    margin-top: 5px;
    text-align: center;
    border-radius: 15px;
    font-size: 12px;
    z-index: 651 !important;
}

/* Vehicle-specific marker clusters */
.marker-cluster-small {
    background-color: rgba(59, 130, 246, 0.6);
}

.marker-cluster-small div {
    background-color: rgba(59, 130, 246, 0.8);
    color: white;
}

.marker-cluster-medium {
    background-color: rgba(139, 92, 246, 0.6);
}

.marker-cluster-medium div {
    background-color: rgba(139, 92, 246, 0.8);
    color: white;
}

.marker-cluster-large {
    background-color: rgba(239, 68, 68, 0.6);
}

.marker-cluster-large div {
    background-color: rgba(239, 68, 68, 0.8);
    color: white;
}

/* Fix for vehicle order makers */
.leaflet-marker-icon {
    z-index: 600 !important;
}

/* Fix for zoomed-in map vehicle markers */
.zoomed-in-map-container .vehicle-marker,
.zoomed-out-map-container .vehicle-marker {
    background-color: transparent !important;
}

/* Fix for cluster marker visibility */
.leaflet-marker-pane {
    z-index: 600 !important;
}

.leaflet-overlay-pane {
    z-index: 400 !important;
}

/* Fix for SVG paths inside vehicle markers */
.vehicle-marker-bg svg path {
    fill: inherit;
}

/* Open order marker styles */
.open-order-marker-inner {
    width: 16px;
    height: 16px;
    background-color: #1E40AF;
    /* Blue color for open orders */
    border: 3px solid white;
    border-radius: 50%;
    box-shadow: 0 0 0 2px rgba(30, 64, 175, 0.4);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

.open-order-marker-inner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background-color: rgba(30, 64, 175, 0.3);
    border-radius: 50%;
    animation: pulse-open-order 2s infinite;
}

@keyframes pulse-open-order {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.8;
    }

    70% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }

    100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
}

/* Custom popup styling */
.order-popup {
    min-width: 150px;
}

/* Team-related paths and trails */
.user-trail {
    stroke: #3b82f6;
    stroke-width: 3;
    stroke-opacity: 0.7;
    fill: none;
    stroke-dasharray: 5, 5;
    animation: dashdraw 15s linear infinite;
}

@keyframes dashdraw {
    to {
        stroke-dashoffset: 200;
    }
}

.user-trail-admin {
    stroke: #8b5cf6;
    stroke-width: 3;
}

.user-trail-highlight {
    stroke: #f59e0b;
    stroke-width: 4;
    stroke-opacity: 0.9;
}

/* User marker styles */
.user-marker {
    background: transparent !important;
    z-index: 900 !important;
}

.user-marker-inner {
    width: 30px;
    height: 30px;
    background: #ffffff;
    border: 2px solid #3b82f6;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4);
}