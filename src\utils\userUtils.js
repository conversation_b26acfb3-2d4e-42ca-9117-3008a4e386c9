/**
 * Utility functions for user management, authentication, and profile operations
 */
import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  serverTimestamp, 
  collection, 
  query, 
  where, 
  getDocs 
} from 'firebase/firestore';

/**
 * Check if a user has admin permissions
 * 
 * @param {string} userId - User ID to check
 * @param {Object} db - Firestore database reference
 * @returns {Promise<boolean>} Promise resolving to admin status
 */
export const checkIfUserIsAdmin = async (userId, db) => {
  if (!userId || !db) return false;
  
  try {
    const userDoc = await getDoc(doc(db, "userProfiles", userId));
    
    if (userDoc.exists() && userDoc.data().isAdmin) {
      return true;
    }
    
    return false;
  } catch (err) {
    console.error("Error checking admin status:", err);
    return false;
  }
};

/**
 * Check if user has a specific tag/role
 * 
 * @param {string} userId - User ID to check
 * @param {string} tagName - Tag name to check for
 * @param {Object} db - Firestore database reference
 * @returns {Promise<boolean>} Promise resolving to tag status
 */
export const checkUserHasTag = async (userId, tagName, db) => {
  if (!userId || !tagName || !db) return false;
  
  try {
    const profileRef = doc(db, "userProfiles", userId);
    const profileDoc = await getDoc(profileRef);
    
    if (profileDoc.exists() && profileDoc.data().tags) {
      const tags = profileDoc.data().tags || [];
      return tags.includes(tagName);
    }
    
    return false;
  } catch (error) {
    console.error("Error checking user tag:", error);
    return false;
  }
};

/**
 * Get user profile picture
 * 
 * @param {string} userId - User ID
 * @param {Object} db - Firestore database reference
 * @returns {Promise<string|null>} Promise resolving to profile picture URL/base64
 */
export const getUserProfilePicture = async (userId, db) => {
  if (!userId || !db) return null;
  
  try {
    const profileRef = doc(db, "userProfiles", userId);
    const profileDoc = await getDoc(profileRef);
    
    if (profileDoc.exists() && profileDoc.data().photoBase64) {
      return profileDoc.data().photoBase64;
    }
    
    return null;
  } catch (error) {
    console.error("Error fetching user profile picture:", error);
    return null;
  }
};

/**
 * Get user display name
 * 
 * @param {string} userId - User ID
 * @param {Object} db - Firestore database reference
 * @returns {Promise<string|null>} Promise resolving to display name
 */
export const getUserDisplayName = async (userId, db) => {
  if (!userId || !db) return null;
  
  try {
    const profileRef = doc(db, "userProfiles", userId);
    const profileDoc = await getDoc(profileRef);
    
    if (profileDoc.exists() && profileDoc.data().displayName) {
      return profileDoc.data().displayName;
    }
    
    return null;
  } catch (error) {
    console.error("Error fetching user display name:", error);
    return null;
  }
};

/**
 * Update user's online status
 * 
 * @param {Object} user - User object
 * @param {boolean} isOnline - Whether user is online
 * @param {Object} db - Firestore database reference
 * @returns {Promise<boolean>} Promise resolving to success status
 */
export const updateUserOnlineStatus = async (user, isOnline, db) => {
  if (!user || !db) return false;
  
  try {
    const userDocRef = doc(db, 'userLocations', user.uid);
    
    // Check if document exists
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      // Just update the online status and timestamp
      await updateDoc(userDocRef, {
        online: isOnline,
        lastUpdated: serverTimestamp()
      });
    } else if (isOnline) {
      // Only create document if setting to online
      const userData = {
        uid: user.uid,
        email: user.email || '',
        displayName: user.displayName || '',
        position: null, // Will be set by location updates
        lastUpdated: serverTimestamp(),
        online: isOnline
      };
      
      await setDoc(userDocRef, userData);
    }
    
    return true;
  } catch (err) {
    console.error("Error updating user online status:", err);
    return false;
  }
};

/**
 * Update user location
 * 
 * @param {Object} user - User object
 * @param {Object} position - Position with lat/lng
 * @param {Object} db - Firestore database reference
 * @returns {Promise<boolean>} Promise resolving to success status
 */
export const updateUserLocation = async (user, position, db) => {
  if (!user || !position || !db) return false;
  
  try {
    const userDocRef = doc(db, 'userLocations', user.uid);
    
    // Check if document exists
    const userDoc = await getDoc(userDocRef);
    
    const userData = {
      uid: user.uid,
      email: user.email || '',
      displayName: user.displayName || '',
      position: {
        lat: position.lat,
        lng: position.lng
      },
      lastUpdated: serverTimestamp(),
      online: true
    };
    
    if (userDoc.exists()) {
      // Update existing document
      await updateDoc(userDocRef, userData);
    } else {
      // Create new document with specific ID
      await setDoc(userDocRef, userData);
    }
    
    return true;
  } catch (err) {
    console.error("Error updating user location:", err);
    return false;
  }
};

/**
 * Update user's clock in/out status
 * 
 * @param {Object} user - User object
 * @param {boolean} isClockedIn - Whether user is clocked in
 * @param {Object} db - Firestore database reference
 * @returns {Promise<boolean>} Promise resolving to success status
 */
export const updateUserClockStatus = async (user, isClockedIn, db) => {
  if (!user || !db) return false;
  
  try {
    const userStatusRef = doc(db, 'userStatus', user.uid);
    
    await setDoc(userStatusRef, {
      clockedIn: isClockedIn,
      [isClockedIn ? 'clockInTime' : 'clockOutTime']: serverTimestamp(),
      uid: user.uid
    }, { merge: true });
    
    return true;
  } catch (err) {
    console.error(`Error updating clock ${isClockedIn ? 'in' : 'out'} status:`, err);
    return false;
  }
};

/**
 * Get all active users
 * 
 * @param {Object} db - Firestore database reference
 * @param {Object} options - Query options 
 * @returns {Promise<Array>} Promise resolving to array of users
 */
export const getActiveUsers = async (db, options = {}) => {
  if (!db) return [];
  
  const {
    excludeCurrentUser = true,
    currentUser = null,
    onlineOnly = true,
    maxUsers = 50
  } = options;
  
  try {
    let userQuery = collection(db, 'userLocations');
    
    // Filter for online users only if specified
    if (onlineOnly) {
      userQuery = query(userQuery, where('online', '==', true));
    }
    
    const snapshot = await getDocs(userQuery);
    const users = [];
    
    snapshot.forEach((doc) => {
      const data = doc.data();
      
      // Skip current user if excludeCurrentUser is true
      if (excludeCurrentUser && currentUser && data.uid === currentUser.uid) {
        return;
      }
      
      users.push({
        ...data,
        id: doc.id
      });
    });
    
    // Limit to maxUsers
    return users.slice(0, maxUsers);
  } catch (err) {
    console.error("Error getting active users:", err);
    return [];
  }
};

/**
 * Format user name for display
 * 
 * @param {Object} user - User data
 * @param {Object} options - Formatting options
 * @returns {string} Formatted user name
 */
export const formatUserName = (user, options = {}) => {
  if (!user) return 'Unknown User';
  
  const {
    preferDisplayName = true,
    fallbackToEmail = true,
    showIdIfNoName = false,
    abbreviateEmail = false,
    maxLength = 0
  } = options;
  
  let name = '';
  
  if (preferDisplayName && user.displayName) {
    name = user.displayName;
  } else if (fallbackToEmail && user.email) {
    name = abbreviateEmail ? user.email.split('@')[0] : user.email;
  } else if (showIdIfNoName && user.uid) {
    name = user.uid.substring(0, 8) + '...';
  } else {
    name = 'Unknown User';
  }
  
  // Truncate if needed
  if (maxLength > 0 && name.length > maxLength) {
    name = name.substring(0, maxLength - 3) + '...';
  }
  
  return name;
};

/**
 * Generate a consistent color based on user ID
 * 
 * @param {string} userId - User identifier
 * @returns {string} Color as hex code
 */
export const getUserColor = (userId) => {
  // List of distinct colors for different users
  const colors = [
    '#FF5555', // Red (default color)
    '#4CAF50', // Green
    '#2196F3', // Blue
    '#FF9800', // Orange
    '#9C27B0', // Purple
    '#00BCD4', // Cyan
    '#FFEB3B', // Yellow
    '#795548', // Brown
    '#009688', // Teal
    '#E91E63', // Pink
    '#673AB7', // Deep Purple
    '#FFC107', // Amber
    '#8BC34A', // Light Green
    '#03A9F4', // Light Blue
    '#FF5722', // Deep Orange
    '#607D8B'  // Blue Grey
  ];
  
  if (!userId) return colors[0]; // Default color
  
  // Create a simple hash from the user ID
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // Use the hash to pick a color
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

/**
 * Generate user initials from name
 * 
 * @param {string} name - User name
 * @param {number} maxChars - Maximum number of characters
 * @returns {string} User initials
 */
export const getUserInitials = (name, maxChars = 2) => {
  if (!name) return '?';
  
  // Handle email addresses
  if (name.includes('@')) {
    name = name.split('@')[0];
  }
  
  // Split by spaces and get first letter of each part
  const parts = name.trim().split(/\s+/);
  let initials = '';
  
  if (parts.length === 1) {
    // For single name, take first letter or first two letters
    initials = parts[0].substring(0, maxChars).toUpperCase();
  } else {
    // For multiple names, take first letter of first and last names
    initials = (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
  }
  
  return initials;
};

/**
 * Format time since last update
 * 
 * @param {Object} timestamp - Firestore timestamp or Date
 * @returns {string} Formatted time string
 */
export const formatTimeSince = (timestamp) => {
  if (!timestamp) return 'Never';
  
  try {
    // Convert Firestore timestamp to JS Date if needed
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const seconds = Math.floor((now - date) / 1000);
    
    if (seconds < 60) {
      return 'Just now';
    }
    
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) {
      return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
    }
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) {
      return `${hours} hour${hours === 1 ? '' : 's'} ago`;
    }
    
    const days = Math.floor(hours / 24);
    if (days < 7) {
      return `${days} day${days === 1 ? '' : 's'} ago`;
    }
    
    // For older dates, show the actual date
    return date.toLocaleDateString();
  } catch (err) {
    console.error("Error formatting timestamp:", err);
    return 'Unknown time';
  }
};

/**
 * Clear user's breadcrumb trail
 * 
 * @param {string} userId - User ID
 * @param {Object} db - Firestore database reference
 * @param {boolean} requireAdmin - Whether admin permissions are required
 * @param {string} currentUserId - Current user ID for permission check
 * @returns {Promise<boolean>} Promise resolving to success status
 */
export const clearUserTrail = async (userId, db, requireAdmin = true, currentUserId = null) => {
  if (!userId || !db) return false;
  
  try {
    // Check permissions
    if (requireAdmin && userId !== currentUserId) {
      const isAdmin = await checkIfUserIsAdmin(currentUserId, db);
      if (!isAdmin) {
        console.error("Only admins can clear other users' traces");
        return false;
      }
    }
    
    // Clear trail
    const userTraceRef = doc(db, 'userTraces', userId);
    const traceDoc = await getDoc(userTraceRef);
    
    if (traceDoc.exists()) {
      await updateDoc(userTraceRef, {
        trace: []
      });
    } else {
      // Create empty trace if it doesn't exist
      await setDoc(userTraceRef, {
        uid: userId,
        trace: []
      });
    }
    
    return true;
  } catch (err) {
    console.error("Error clearing user trail:", err);
    return false;
  }
};

/**
 * Update user profile picture
 * 
 * @param {string} userId - User ID
 * @param {string} photoBase64 - Base64 encoded photo
 * @param {Object} db - Firestore database reference
 * @returns {Promise<boolean>} Promise resolving to success status
 */
export const updateUserProfilePicture = async (userId, photoBase64, db) => {
  if (!userId || !photoBase64 || !db) return false;
  
  try {
    const profileRef = doc(db, 'userProfiles', userId);
    
    // Check if document exists
    const profileDoc = await getDoc(profileRef);
    
    if (profileDoc.exists()) {
      // Update existing document
      await updateDoc(profileRef, {
        photoBase64: photoBase64,
        updatedAt: serverTimestamp()
      });
    } else {
      // Create new document
      await setDoc(profileRef, {
        uid: userId,
        photoBase64: photoBase64,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    
    return true;
  } catch (err) {
    console.error("Error updating user profile picture:", err);
    return false;
  }
};

/**
 * Update user display name
 * 
 * @param {string} userId - User ID
 * @param {string} displayName - New display name
 * @param {Object} db - Firestore database reference
 * @returns {Promise<boolean>} Promise resolving to success status
 */
export const updateUserDisplayName = async (userId, displayName, db) => {
  if (!userId || !displayName || !db) return false;
  
  try {
    const profileRef = doc(db, 'userProfiles', userId);
    
    // Check if document exists
    const profileDoc = await getDoc(profileRef);
    
    if (profileDoc.exists()) {
      // Update existing document
      await updateDoc(profileRef, {
        displayName: displayName,
        updatedAt: serverTimestamp()
      });
    } else {
      // Create new document
      await setDoc(profileRef, {
        uid: userId,
        displayName: displayName,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    
    // Also update in userLocations for faster access
    const userLocationRef = doc(db, 'userLocations', userId);
    const locationDoc = await getDoc(userLocationRef);
    
    if (locationDoc.exists()) {
      await updateDoc(userLocationRef, {
        displayName: displayName
      });
    }
    
    return true;
  } catch (err) {
    console.error("Error updating user display name:", err);
    return false;
  }
};

/**
 * Get users near a location
 * 
 * @param {Object} position - Position with lat/lng
 * @param {Object} db - Firestore database reference
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Promise resolving to array of nearby users
 */
export const getUsersNearLocation = async (position, db, options = {}) => {
  if (!position || !db) return [];
  
  const {
    maxDistance = 5, // miles
    excludeCurrentUser = true,
    currentUser = null,
    onlineOnly = true,
    maxUsers = 20
  } = options;
  
  try {
    // Get all active users first (Firestore doesn't support geospatial queries directly)
    const allUsers = await getActiveUsers(db, {
      excludeCurrentUser,
      currentUser,
      onlineOnly
    });
    
    // Calculate distance for each user and filter
    const nearbyUsers = allUsers
      .filter(user => user.position) // Only users with position
      .map(user => {
        // Calculate distance
        const distance = calculateDistance(position, user.position);
        return { ...user, distance };
      })
      .filter(user => user.distance <= maxDistance) // Only users within maxDistance
      .sort((a, b) => a.distance - b.distance) // Sort by distance
      .slice(0, maxUsers); // Limit result count
    
    return nearbyUsers;
  } catch (err) {
    console.error("Error getting users near location:", err);
    return [];
  }
};

/**
 * Calculate distance between two points (in MILES)
 * 
 * @param {Object} point1 - First point with lat/lng properties
 * @param {Object} point2 - Second point with lat/lng properties
 * @returns {number} Distance in miles
 */
export const calculateDistance = (point1, point2) => {
  if (!point1 || !point2) return Infinity;
  
  const R = 3958.8; // Earth's radius in MILES
  const φ1 = point1.lat * Math.PI/180; // φ, λ in radians
  const φ2 = point2.lat * Math.PI/180;
  const Δφ = (point2.lat-point1.lat) * Math.PI/180;
  const Δλ = (point2.lng-point1.lng) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  const d = R * c; // in MILES
  return d;
};