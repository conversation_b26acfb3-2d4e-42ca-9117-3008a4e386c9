// src/pages/TagsPage.js
import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '../pages/firebase.js';
import { useAuth } from '../contexts/AuthContext.js';
import Tags from '../components/Tags.js';

function TagsPage() {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [userTags, setUserTags] = useState([]);
  const [availableTags, setAvailableTags] = useState([]);
  const [userData, setUserData] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // Get userId from params or use current user's ID
  const userId = params.userId || currentUser?.uid;
  
  // Security check - only allow admins to view others' tags
  const canViewTags = isAdmin || currentUser?.uid === userId;
  
  // Fetch user data and available tags
  useEffect(() => {
    async function fetchData() {
      if (!userId || !canViewTags) {
        setLoading(false);
        return;
      }
      
      try {
        // Fetch user profile to get their tags
        const userRef = doc(db, "userProfiles", userId);
        const userDoc = await getDoc(userRef);
        
        if (userDoc.exists()) {
          const userData = userDoc.data();
          setUserData(userData);
          if (userData.tags) {
            setUserTags(userData.tags);
          }
        }
        
        // Fetch available system tags
        const tagsRef = doc(db, "settings", "systemTags");
        const tagsDoc = await getDoc(tagsRef);
        
        if (tagsDoc.exists() && tagsDoc.data().tags) {
          setAvailableTags(tagsDoc.data().tags);
        }
      } catch (error) {
        console.error("Error fetching tags data:", error);
        setError("Failed to load tags data.");
      } finally {
        setLoading(false);
      }
    }
    
    fetchData();
  }, [userId, currentUser, isAdmin, canViewTags]);
  
  // Handle tags change
  const handleTagsChange = async (updatedTags) => {
    if (!canViewTags) return;
    
    setError('');
    setSuccess('');
    setSaving(true);
    
    try {
      // Update user profile with new tags
      const userRef = doc(db, "userProfiles", userId);
      await updateDoc(userRef, {
        tags: updatedTags,
        updatedAt: new Date().toISOString()
      });
      
      // Update local state
      setUserTags(updatedTags);
      setSuccess("Tags updated successfully!");
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (error) {
      console.error("Error updating user tags:", error);
      setError("Failed to update tags. Please try again.");
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex justify-center items-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-3">Loading tags...</p>
        </div>
      </div>
    );
  }
  
  if (!canViewTags) {
    return (
      <div className="min-h-screen bg-gray-100 flex justify-center items-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h2 className="text-xl font-bold text-red-600 mb-4">Access Denied</h2>
          <p className="mb-4">You don't have permission to view these tags.</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold">NWRepo</h1>
              </div>
            </div>
            <div className="flex items-center">
              <button
                onClick={() => navigate('/dashboard')}
                className="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md mr-4"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </nav>
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">
              {userId === currentUser?.uid ? "My Tags" : `${userData?.displayName || "User"}'s Tags`}
            </h1>
          </div>
          
          {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">{error}</div>}
          {success && <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">{success}</div>}
          
          <div className="bg-white shadow overflow-hidden sm:rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Manage Tags</h2>
            <p className="text-gray-600 mb-4">
              Tags help categorize users and can be used for filtering in reports and dashboards.
              {isAdmin ? ' As an admin, you can create new tags or use existing ones.' : ''}
            </p>
            
            {saving ? (
              <div className="flex items-center text-gray-500 mb-4">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-2"></div>
                <span>Saving changes...</span>
              </div>
            ) : null}
            
            <Tags
              initialTags={userTags}
              onTagsChange={handleTagsChange}
              availableTags={availableTags}
              allowCreate={isAdmin}
              allowEdit={isAdmin}
              allowDelete={true}
              className="mb-4"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default TagsPage;